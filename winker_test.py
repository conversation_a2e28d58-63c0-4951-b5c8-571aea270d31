# -*- coding: utf-8 -*-
# author:liuyang

import os
import sys

### 共有函数，可以被外部调用
def oscmd(cmd):
    """
    执行命令，并输出返回
    :param cmd: 执行的命令
    :return: 命令执行完成的返回码
    """
    code = os.system(cmd)
    return code

currentdir = os.getcwd()
generatorScriptPath = os.path.join(currentdir, "scripts/generate_garbage_code/generator.py")  # 测试环境网络允许请求
generatedCodeDir = os.path.join(currentdir, "modules/biz/lib/garbage/")  # 测试环境网络允许请求
if __name__ == '__main__':
    try:
        oscmd(f"python3 {generatorScriptPath} -o {generatedCodeDir}")
        full_cmd = f"fvm flutter build apk --flavor baseGp --dart-define=build.env=test --dart-define=build.from=server"
        oscmd(full_cmd)
    except Exception as e:
        raise e
