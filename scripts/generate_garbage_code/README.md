# Dart垃圾代码生成器

## 项目简介

本项目是一个用Python编写的自动化工具，能够批量生成符合Dart语法规范但无实际业务意义的Dart类文件。每个文件均为独立的工具类，包含完整的类定义、字段和方法，严格遵循Dart编码规范，适用于提升代码多样性、规避应用商店重复代码检测等场景。

## 主要特性
- 生成大量语法正确但无实际功能的Dart代码文件
- 文件间完全独立，无依赖关系
- 仅依赖Flutter SDK标准库，无第三方依赖
- 通过丰富模板和词库，保证生成内容的多样性和随机性
- 支持自定义模板、词库和生成参数

## 目录结构

```
generate_garbage_code/
├── class_templates/       # Dart类结构模板
│   ├── standard_class.txt
│   ├── singleton_class.txt
│   └── static_class.txt
├── function_templates/    # Dart方法模板
│   ├── async_method.txt
│   ├── callback_method.txt
│   ├── chained_method.txt
│   ├── conditional_method.txt
│   ├── default_value_method.txt
│   ├── factory_method.txt
│   ├── from_json_method.txt
│   ├── getter_method.txt
│   ├── iterator_method.txt
│   ├── simple_method.txt
│   ├── static_method.txt
│   ├── stream_method.txt
│   └── try_catch_method.txt
├── field_templates/       # Dart字段模板
│   ├── final_field.txt
│   ├── simple_field.txt
│   ├── static_const_field.txt
│   └── static_field.txt
├── word_banks/            # 词汇资源库
│   ├── adjectives.txt
│   ├── nouns.txt
│   └── verbs.txt
├── config.py              # 配置参数和常量
├── utils.py               # 工具函数
├── generator.py           # 主生成脚本
└── README.md              # 项目说明文档
```

## 主要文件说明
- **class_templates/**: 各类Dart类结构模板（标准类、单例类、静态类等）
- **function_templates/**: 各类Dart方法模板（异步、工厂、getter、try-catch等）
- **field_templates/**: 各类字段声明模板（普通、final、static等）
- **word_banks/**: 词汇库，包含名词、动词、形容词，用于生成类名、方法名、字段名等
- **config.py**: 生成参数配置，包括文件数量、输出路径、命名规则等
- **utils.py**: 工具函数，负责模板加载、随机词汇选择、名称生成等
- **generator.py**: 主生成脚本，负责整体流程控制、模板渲染和文件输出

## 环境要求
- Python 3.6及以上
- 支持Windows、macOS、Linux
- 无需第三方依赖，仅用Python标准库

## 快速开始
1. 克隆本项目：
```bash
<NAME_EMAIL>:social-infra/flutter/packages/generate_garbage_code.git
cd generate_garbage_code
```
2. 运行主脚本，生成10个Dart文件（默认参数）：
```bash
python generator.py
```
3. 可用参数：
- `-n, --num`  生成文件数量（默认10）
- `-o, --output`  指定输出目录（默认./output）

示例：
```bash
python generator.py -n 5
python generator.py -n 20 -o ./my_dart_files
```

## 自定义与扩展
- **添加类/方法/字段模板**：在对应模板目录下添加txt文件，使用占位符如`{{CLASS_NAME}}`、`{{METHOD_NAME}}`等
- **扩展词库**：在word_banks目录下添加或编辑txt文件，每行一个词
- **修改生成参数**：编辑config.py，调整生成数量、命名规则、数据类型等

## 常见问题
- **生成代码有语法错误**：检查模板文件是否符合Dart语法，特别注意async/await、返回类型、命名规范等
- **如何提升多样性**：增加模板和词库内容，调整生成参数
- **类名/方法名非法**：检查utils.py中的名称生成逻辑，确保过滤非法字符

## 示例输出
```dart
// Example Dart class generated by this tool
import 'dart:core';

/// Example class
class ExampleClass {
  final int id;
  static const String tag = 'example';

  ExampleClass(this.id);

  String get description => 'This is an example.';

  static Future<void> doSomething() async {
    await Future.delayed(Duration(seconds: 1));
  }
}
```
