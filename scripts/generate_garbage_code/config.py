#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模块 - 存储可配置参数和常量
"""

import os

# 基本配置
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output')
TEMPLATES_DIR = {
    'class': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'class_templates'),
    'function': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'function_templates'),
    'field': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'field_templates')
}
WORD_BANKS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'word_banks')

# 生成配置
DEFAULT_FILE_COUNT = 10  # 默认生成文件数量
MIN_METHODS_PER_CLASS = 3  # 每个类最少方法数
MAX_METHODS_PER_CLASS = 8  # 每个类最多方法数
MIN_FIELDS_PER_CLASS = 2  # 每个类最少字段数
MAX_FIELDS_PER_CLASS = 6  # 每个类最多字段数

# 占位符标记
PLACEHOLDERS = {
    'CLASS_NAME': '{{CLASS_NAME}}',
    'CLASS_DESCRIPTION': '{{CLASS_DESCRIPTION}}',
    'FIELD_DECLARATIONS': '{{FIELD_DECLARATIONS}}',
    'METHOD_IMPLEMENTATIONS': '{{METHOD_IMPLEMENTATIONS}}',
    'METHOD_NAME': '{{METHOD_NAME}}',
    'METHOD_DESCRIPTION': '{{METHOD_DESCRIPTION}}',
    'PARAMETER_LIST': '{{PARAMETER_LIST}}',
    'RETURN_TYPE': '{{RETURN_TYPE}}',
    'METHOD_BODY': '{{METHOD_BODY}}',
    'FIELD_NAME': '{{FIELD_NAME}}',
    'FIELD_TYPE': '{{FIELD_TYPE}}',
    'FIELD_DESCRIPTION': '{{FIELD_DESCRIPTION}}',
    'FIELD_DEFAULT_VALUE': '{{FIELD_DEFAULT_VALUE}}',
    'IMPORT_STATEMENTS': '{{IMPORT_STATEMENTS}}',
    'FILE_DESCRIPTION': '{{FILE_DESCRIPTION}}',
    'GENERATION_DATE': '{{GENERATION_DATE}}',
}

# Dart导入语句白名单
ALLOWED_IMPORTS = [
    "import 'dart:core';",
    "import 'dart:async';",
    "import 'dart:collection';",
    "import 'dart:convert';",
    "import 'dart:math';",
    "import 'dart:typed_data';",
    "import 'dart:io';",
]

# Dart基本数据类型
DART_TYPES = [
    'String',
    'int',
    'double',
    'bool',
    'List<String>',
    'List<int>',
    'List<double>',
    'List<bool>',
    'Map<String, dynamic>',
    'Map<String, String>',
    'Map<String, int>',
    'Set<String>',
    'Set<int>',
    'Future<void>',
    'Future<String>',
    'Future<bool>',
    'Future<int>',
    'Future<List<int>>',
    'Future<List<double>>',
    'Future<List<String>>',
    'Future<Map<String, String>>',
    'Stream<String>',
    'void',
]

# 不能用作字段类型的类型
INVALID_FIELD_TYPES = [
    'void',
]

# 文件命名规则
FILE_NAME_PREFIX = ['util', 'helper', 'manager', 'service', 'handler', 'processor', 'converter', 'factory', 'builder', 'generator']
