  /// {{METHOD_DESCRIPTION}}
  /// Implementation varies based on conditions.
  {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {
    final bool isEvenSecond = DateTime.now().second % 2 == 0;

    {{#if_eq_string RETURN_TYPE "String"}}
    if (isEvenSecond) {
      return 'Value for even second';
    } else {
      return 'Value for odd second';
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "int"}}
    return isEvenSecond ? 42 : 7;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "double"}}
    return isEvenSecond ? 3.14 : 2.71;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "bool"}}
    return isEvenSecond;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "void"}}
    if (isEvenSecond) {
      print('Executing on even second');
    } else {
      print('Executing on odd second');
    }
    return;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<String>"}}
    if (isEvenSecond) {
      return ['even1', 'even2', 'even3'];
    } else {
      return ['odd1', 'odd2'];
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<int>"}}
    return isEvenSecond ? [2, 4, 6, 8] : [1, 3, 5, 7, 9];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<double>"}}
    return isEvenSecond ? [2.0, 4.0, 6.0] : [1.5, 3.5, 5.5];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<bool>"}}
    return isEvenSecond ? [true, false, true] : [false, true];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Set<String>"}}
    if (isEvenSecond) {
      return {'even1', 'even2'};
    } else {
      return {'odd1', 'odd2', 'odd3'};
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Set<int>"}}
    return isEvenSecond ? {2, 4, 6} : {1, 3, 5};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, String>"}}
    if (isEvenSecond) {
      return {'key1': 'even1', 'key2': 'even2'};
    } else {
      return {'key1': 'odd1', 'key2': 'odd2'};
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, int>"}}
    return isEvenSecond
        ? {'even1': 2, 'even2': 4}
        : {'odd1': 1, 'odd2': 3};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, dynamic>"}}
    if (isEvenSecond) {
      return {'status': 'even', 'value': 2, 'isValid': true};
    } else {
      return {'status': 'odd', 'value': 1, 'isValid': false};
    }
    {{/if_eq_string}}

    throw UnimplementedError('Unsupported return type: {{RETURN_TYPE}}');
  }