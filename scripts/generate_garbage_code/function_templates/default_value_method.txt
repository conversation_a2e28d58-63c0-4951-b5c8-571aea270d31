  /// {{METHOD_DESCRIPTION}}
  {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {
    return _getReturnValueByType{{METHOD_NAME}}('{{RETURN_TYPE}}');
  }

  dynamic _getReturnValueByType{{METHOD_NAME}}(String typeName) {
    if (typeName == 'String') {
      return 'defaultValue';
    } else if (typeName == 'int') {
      return 42;
    } else if (typeName == 'double') {
      return 3.14;
    } else if (typeName == 'bool') {
      return true;
    } else if (typeName == 'List<String>') {
      return ['item1', 'item2'];
    } else if (typeName == 'List<int>') {
      return [1, 2, 3];
    } else if (typeName == 'List<double>') {
      return [1.1, 2.2];
    } else if (typeName == 'List<bool>') {
      return [true, false];
    } else if (typeName == 'Map<String, String>') {
      return {'key1': 'value1', 'key2': 'value2'};
    } else if (typeName == 'Map<String, int>') {
      return {'key1': 1, 'key2': 2};
    } else if (typeName == 'Map<String, dynamic>') {
      return {'key1': 'value1', 'key2': 2};
    } else if (typeName == 'Set<String>') {
      return {'item1', 'item2'};
    } else if (typeName == 'Set<int>') {
      return {1, 2, 3};
    } else if (typeName.startsWith('Future<void>')) {
      return null; // Future<void>不需要返回值
    } else if (typeName.startsWith('Future<')) {
      String innerType = typeName.substring(7, typeName.length - 1);
      if (innerType == 'String') {
        return 'futureResult';
      } else if (innerType == 'int') {
        return 42;
      } else if (innerType == 'double') {
        return 3.14;
      } else if (innerType == 'bool') {
        return true;
      } else if (innerType.startsWith('List<')) {
        if (innerType.contains('String')) {
          return ['item1', 'item2'];
        } else if (innerType.contains('int')) {
          return [1, 2, 3];
        } else {
          return [];
        }
      } else {
        return null;
      }
    } else if (typeName.startsWith('Stream<')) {
      return null;
    } else if (typeName == 'void') {
      return null; 
    } else {
      return null;
    }
  }
