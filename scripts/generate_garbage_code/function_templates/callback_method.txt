  /// {{METHOD_DESCRIPTION}}
  /// Takes a callback function as parameter.
  {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {
    {{#if_eq_string RETURN_TYPE "void"}}
    final callback = () {
      print('Callback executed');
    };

    callback();
    return;
    {{/if_eq_string}}

    {{#if_not_eq_string RETURN_TYPE "void"}}
    final callback = ({{RETURN_TYPE}} value) {
      print('Callback called with: $value');
    };

    {{RETURN_TYPE}} result;

    {{#if_eq_string RETURN_TYPE "String"}}
    result = 'callbackResult';
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "int"}}
    result = 42;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "double"}}
    result = 3.14;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "bool"}}
    result = true;
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<String>"}}
    result = <String>['item1', 'item2'];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<int>"}}
    result = <int>[1, 2, 3];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<double>"}}
    result = <double>[1.1, 2.2, 3.3];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "List<bool>"}}
    result = <bool>[true, false];
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, String>"}}
    result = <String, String>{'key': 'value'};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, int>"}}
    result = <String, int>{'key': 1};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Map<String, dynamic>"}}
    result = <String, dynamic>{'key': 'value', 'num': 1};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Set<String>"}}
    result = <String>{'item1', 'item2'};
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "Set<int>"}}
    result = <int>{1, 2, 3};
    {{/if_eq_string}}

    callback(result);
    return result;
    {{/if_not_eq_string}}
  }
