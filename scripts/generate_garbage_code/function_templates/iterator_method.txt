  /// {{METHOD_DESCRIPTION}}
  /// Makes this class iterable by implementing the Iterable interface.
  Iterable<{{RETURN_TYPE}}> get {{METHOD_NAME}}Iterable sync* {
    {{#if_eq_string RETURN_TYPE "String"}}
    final String sampleText = 'Hello';
    for (int i = 0; i < sampleText.length; i++) {
      yield sampleText[i];
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "int"}}
    int a = 0, b = 1;
    yield a;
    yield b;
    for (int i = 2; i < 8; i++) {
      int next = a + b;
      yield next;
      a = b;
      b = next;
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "double"}}
    final double pi = 3.14159;
    for (int i = 1; i <= 5; i++) {
      yield pi * i;
    }
    {{/if_eq_string}}
    {{#if_eq_string RETURN_TYPE "bool"}}
    for (int i = 0; i < 5; i++) {
      yield i % 2 == 0;
    }
    {{/if_eq_string}}
  }

  /// Returns an iterator for this object.
  Iterator<{{RETURN_TYPE}}> get {{METHOD_NAME}}Iterator {
    return {{METHOD_NAME}}Iterable.iterator;
  }
