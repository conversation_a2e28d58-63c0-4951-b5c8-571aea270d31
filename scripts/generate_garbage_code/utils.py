#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块 - 提供模板处理、随机生成等功能
"""

import os
import random
import string
import datetime
from typing import List, Dict

import config

def load_templates(template_type: str) -> Dict[str, str]:
    """
    加载指定类型的所有模板文件

    Args:
        template_type: 模板类型 ('class', 'function', 'field')

    Returns:
        包含模板名称和内容的字典
    """
    templates = {}
    template_dir = config.TEMPLATES_DIR.get(template_type)

    if not template_dir or not os.path.exists(template_dir):
        return templates

    for filename in os.listdir(template_dir):
        if filename.endswith('.dart') or filename.endswith('.txt'):
            file_path = os.path.join(template_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                template_name = os.path.splitext(filename)[0]
                templates[template_name] = f.read()

    return templates

def load_word_bank(bank_name: str) -> List[str]:
    """
    加载指定的词汇库

    Args:
        bank_name: 词汇库名称 (不含扩展名)

    Returns:
        词汇列表
    """
    word_bank_path = os.path.join(config.WORD_BANKS_DIR, f"{bank_name}.txt")

    if not os.path.exists(word_bank_path):
        return []

    with open(word_bank_path, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f.readlines() if line.strip()]

def get_random_word(bank_name: str) -> str:
    """
    从指定词汇库中随机选择一个词

    Args:
        bank_name: 词汇库名称

    Returns:
        随机选择的词
    """
    words = load_word_bank(bank_name)
    if not words:
        # 如果词汇库为空，返回一个随机字符串
        return ''.join(random.choice(string.ascii_lowercase) for _ in range(5))
    return random.choice(words)

def generate_class_name() -> str:
    """
    生成一个符合Dart命名规范的类名

    Returns:
        类名字符串
    """
    noun = get_random_word('nouns').capitalize()
    adjective = get_random_word('adjectives').capitalize()
    suffix = random.choice(['Util', 'Helper', 'Manager', 'Service', 'Handler', 'Processor', 'Converter'])

    # 确保单词不包含连字符或其他非法字符
    noun = ''.join(c for c in noun if c.isalnum())
    adjective = ''.join(c for c in adjective if c.isalnum())

    # 50%的概率使用形容词+名词+后缀，50%的概率只使用名词+后缀
    if random.random() > 0.5:
        return f"{adjective}{noun}{suffix}"
    else:
        return f"{noun}{suffix}"

def generate_method_name() -> str:
    """
    生成一个符合Dart命名规范的方法名

    Returns:
        方法名字符串
    """
    verb = get_random_word('verbs').lower()
    noun = get_random_word('nouns').capitalize()

    # 确保单词不包含连字符或其他非法字符
    verb = ''.join(c for c in verb if c.isalnum())
    noun = ''.join(c for c in noun if c.isalnum())

    # 有时添加前缀
    prefixes = ['get', 'set', 'is', 'has', 'can', 'should', 'will', 'parse', 'convert', 'format', 'validate']
    if random.random() > 0.7:
        return f"{random.choice(prefixes)}{noun}"

    return f"{verb}{noun}"

def generate_field_name() -> str:
    """
    生成一个符合Dart命名规范的字段名

    Returns:
        字段名字符串
    """
    noun = get_random_word('nouns').lower()
    adjective = get_random_word('adjectives').lower()

    # 确保单词不包含连字符或其他非法字符
    noun = ''.join(c for c in noun if c.isalnum())
    adjective = ''.join(c for c in adjective if c.isalnum())

    # 有时使用形容词修饰
    if random.random() > 0.5:
        return f"{adjective}{noun.capitalize()}"

    return noun

def generate_parameter_list(count: int = None) -> str:
    """
    生成方法参数列表

    Args:
        count: 参数数量，如果为None则随机生成1-3个参数

    Returns:
        参数列表字符串
    """
    if count is None:
        count = random.randint(0, 3)

    params = []
    used_param_names = set()  # 跟踪已使用的参数名

    for _ in range(count):
        param_type = random.choice(config.DART_TYPES)

        # 确保参数名不重复
        while True:
            param_name = generate_field_name()
            if param_name not in used_param_names:
                used_param_names.add(param_name)
                break

        # 不再添加required关键字
        params.append(f"{param_type} {param_name}")

    return ", ".join(params)

def generate_file_name() -> str:
    """
    生成符合Dart命名规范的文件名

    Returns:
        文件名字符串
    """
    prefix = random.choice(config.FILE_NAME_PREFIX)
    noun = get_random_word('nouns').lower()
    return f"{prefix}_{noun}.dart"

def replace_placeholders(template: str, replacements: Dict[str, str]) -> str:
    """
    替换模板中的占位符，支持条件语法

    Args:
        template: 模板字符串
        replacements: 包含占位符和替换内容的字典

    Returns:
        替换后的字符串
    """
    result = template

    # 首先处理条件语法
    for placeholder, value in replacements.items():
        # 处理 {{#if_eq_string PLACEHOLDER "VALUE"}} 条件语法
        if_pattern = f"{{{{#if_eq_string {placeholder} "
        if if_pattern in result:
            lines = result.split('\n')
            processed_lines = []
            i = 0
            while i < len(lines):
                line = lines[i]
                if if_pattern in line:
                    # 提取条件值
                    start_idx = line.find(if_pattern) + len(if_pattern)
                    end_idx = line.find("}}", start_idx)
                    if end_idx > start_idx:
                        condition_value = line[start_idx:end_idx].strip('"')

                        # 查找条件块结束
                        end_tag = f"{{{{/if_eq_string}}}}"
                        j = i + 1
                        condition_block = []
                        while j < len(lines) and end_tag not in lines[j]:
                            condition_block.append(lines[j])
                            j += 1

                        # 如果条件匹配，保留条件块内容
                        if str(value) == condition_value:
                            processed_lines.extend(condition_block)

                        # 跳过条件块和结束标签
                        i = j + 1
                        continue
                processed_lines.append(line)
                i += 1

            result = '\n'.join(processed_lines)

        # 处理 {{#if_not_eq_string PLACEHOLDER "VALUE"}} 条件语法
        if_not_pattern = f"{{{{#if_not_eq_string {placeholder} "
        if if_not_pattern in result:
            lines = result.split('\n')
            processed_lines = []
            i = 0
            while i < len(lines):
                line = lines[i]
                if if_not_pattern in line:
                    # 提取条件值
                    start_idx = line.find(if_not_pattern) + len(if_not_pattern)
                    end_idx = line.find("}}", start_idx)
                    if end_idx > start_idx:
                        condition_value = line[start_idx:end_idx].strip('"')

                        # 查找条件块结束
                        end_tag = f"{{{{/if_not_eq_string}}}}"
                        j = i + 1
                        condition_block = []
                        while j < len(lines) and end_tag not in lines[j]:
                            condition_block.append(lines[j])
                            j += 1

                        # 如果条件不匹配，保留条件块内容
                        if str(value) != condition_value:
                            processed_lines.extend(condition_block)

                        # 跳过条件块和结束标签
                        i = j + 1
                        continue
                processed_lines.append(line)
                i += 1

            result = '\n'.join(processed_lines)

    # 然后替换普通占位符
    import re
    for placeholder, value in replacements.items():
        # 使用正则表达式进行精确替换，避免花括号被错误解释
        pattern = r'\{\{' + re.escape(placeholder) + r'\}\}'
        result = re.sub(pattern, str(value), result)

    return result

def get_random_imports() -> str:
    """
    生成随机的导入语句

    Returns:
        导入语句字符串
    """
    # 始终包含dart:core
    imports = ["import 'dart:core';"]

    # 随机添加其他导入
    other_imports = [imp for imp in config.ALLOWED_IMPORTS if imp != "import 'dart:core';"]
    num_imports = random.randint(1, min(4, len(other_imports)))
    imports.extend(random.sample(other_imports, num_imports))

    return "\n".join(imports)

def get_current_date() -> str:
    """
    获取当前日期字符串

    Returns:
        日期字符串 (YYYY-MM-DD)
    """
    return datetime.datetime.now().strftime("%Y-%m-%d")

def generate_description(subject: str) -> str:
    """
    生成随机描述

    Args:
        subject: 描述主题 ('class', 'method', 'field', 'file')

    Returns:
        描述字符串
    """
    if subject == 'class':
        templates = [
            "A utility class that provides methods for {purpose}.",
            "Helper class for {purpose} operations.",
            "Manages {purpose} and provides utility methods.",
            "Service class responsible for {purpose}.",
            "Handles {purpose} and related operations."
        ]
    elif subject == 'method':
        templates = [
            "Performs {purpose} operation and returns the result.",
            "Converts input to {purpose} format.",
            "Validates and processes {purpose} data.",
            "Checks if the input meets {purpose} criteria.",
            "Transforms data according to {purpose} rules."
        ]
    elif subject == 'field':
        templates = [
            "Stores {purpose} information.",
            "Contains {purpose} data.",
            "Represents {purpose} value.",
            "Holds {purpose} state.",
            "Keeps track of {purpose}."
        ]
    elif subject == 'file':
        templates = [
            "This file contains utility functions for {purpose}.",
            "A collection of helper methods for {purpose}.",
            "Provides functionality for {purpose} operations.",
            "Implements various utilities for {purpose}.",
            "Contains tools and utilities for {purpose}."
        ]
    else:
        return "No description available."

    template = random.choice(templates)
    purpose = f"{get_random_word('adjectives')} {get_random_word('nouns')}"

    return template.replace("{purpose}", purpose)
