// {{FILE_DESCRIPTION}}
// Generated on {{GENERATION_DATE}}

{{IMPORT_STATEMENTS}}

/// {{CLASS_DESCRIPTION}}
class {{CLASS_NAME}} {
  // Singleton instance
  static final {{CLASS_NAME}} _instance = {{CLASS_NAME}}._internal();

  // Factory constructor
  factory {{CLASS_NAME}}() {
    return _instance;
  }

  // Private constructor
  {{CLASS_NAME}}._internal();

  // Fields
{{FIELD_DECLARATIONS}}

  // Methods
{{METHOD_IMPLEMENTATIONS}}
}
