string
text
number
integer
float
date
time
timestamp
file
path
directory
list
array
map
dictionary
set
collection
queue
stack
tree
graph
node
edge
vertex
point
line
rectangle
circle
triangle
shape
color
image
audio
video
media
document
message
notification
event
action
transaction
process
thread
task
job
service
request
response
result
status
state
configuration
setting
preference
option
parameter
argument
value
key
token
credential
user
account
profile
contact
address
location
position
coordinate
route
path
direction
distance
duration
speed
velocity
acceleration
force
energy
power
temperature
pressure
volume
weight
size
dimension
width
height
length
depth
area
perimeter
radius
diameter
angle
degree
radian
byte
bit
pixel
resolution
frequency
amplitude
wavelength
period
cycle
iteration
loop
condition
expression
statement
function
method
procedure
routine
algorithm
formula
equation
variable
constant
object
instance
interface
module
package
library
framework
system
platform
device
sensor
network
protocol
format
encoding
compression
encryption
decryption
hash
checksum
validation
verification
authentication
authorization
permission
access
security
privacy
data
information
knowledge
wisdom
logic
reason
inference
deduction
induction
analysis
synthesis
evaluation
comparison
sorting
filtering
mapping
reduction
transformation
conversion
calculation
computation
operation
manipulation
modification
generation
creation
deletion
insertion
update
retrieval
query
search
index
cache
buffer
stream
channel
pipe
socket
connection
session
context
environment
scope
namespace
domain
range
boundary
limit
threshold
tolerance
precision
accuracy
deviation
variance
distribution
pattern
template
model
schema
structure
hierarchy
taxonomy
ontology
category
classification
type
kind
genre
style
mode
format
layout
design
pattern
template
theme
skin
appearance
behavior
functionality
feature
capability
requirement
specification
implementation
deployment
maintenance
support
feedback
suggestion
recommendation
decision
choice
selection
priority
importance
urgency
severity
impact
risk
issue
problem
challenge
opportunity
solution
resolution
fix
patch
update
upgrade
version
release
build
milestone
deadline
schedule
timeline
history
log
record
archive
backup
restore
recovery
migration
integration
interoperability
compatibility
portability
scalability
reliability
availability
performance
efficiency
optimization
resource
memory
storage
database
repository
registry
inventory
catalog
directory
index
reference
pointer
handle
identifier
name
label
tag
marker
indicator
signal
trigger
hook
callback
listener
observer
publisher
subscriber
producer
consumer
client
server
host
guest
peer
neighbor
parent
child
sibling
ancestor
descendant
root
leaf
branch
trunk
core
shell
wrapper
adapter
decorator
proxy
facade
bridge
composite
flyweight
singleton
factory
builder
prototype
visitor
strategy
command
interpreter
iterator
mediator
memento
observer
state
template
chain
