get
set
is
has
can
should
will
parse
convert
format
validate
check
verify
test
analyze
process
handle
manage
control
monitor
track
log
record
store
save
load
read
write
create
generate
build
construct
make
produce
develop
design
implement
code
program
compile
execute
run
perform
do
complete
finish
start
begin
initialize
setup
configure
prepare
arrange
organize
sort
filter
search
find
locate
identify
recognize
detect
discover
explore
investigate
examine
inspect
review
evaluate
assess
measure
calculate
compute
count
sum
add
subtract
multiply
divide
increase
decrease
grow
shrink
expand
contract
extend
reduce
limit
restrict
constrain
allow
permit
enable
disable
activate
deactivate
turn
switch
toggle
change
modify
alter
adjust
adapt
transform
convert
translate
map
bind
link
connect
join
merge
combine
split
separate
divide
isolate
extract
remove
delete
clear
clean
reset
restore
recover
repair
fix
solve
resolve
debug
trace
track
follow
lead
guide
direct
navigate
move
shift
transfer
transmit
send
receive
accept
reject
approve
deny
grant
revoke
assign
allocate
distribute
share
exchange
swap
replace
substitute
update
refresh
reload
renew
upgrade
downgrade
install
uninstall
deploy
publish
release
launch
deliver
supply
provide
offer
present
show
display
render
draw
paint
print
output
input
enter
exit
return
yield
throw
catch
try
attempt
succeed
fail
pass
block
wait
delay
pause
resume
continue
stop
halt
terminate
abort
cancel
skip
jump
branch
loop
iterate
repeat
cycle
rotate
flip
reverse
invert
negate
complement
match
compare
differ
vary
deviate
conform
adhere
follow
obey
violate
break
encrypt
decrypt
hash
sign
verify
authenticate
authorize
register
login
logout
subscribe
unsubscribe
notify
alert
warn
inform
report
announce
declare
state
assert
claim
argue
debate
discuss
explain
describe
define
specify
detail
outline
summarize
conclude
infer
deduce
derive
induce
abstract
generalize
specialize
customize
personalize
tailor
adapt
fit
suit
satisfy
fulfill
meet
exceed
fall
rise
climb
descend
float
sink
swim
fly
crawl
walk
run
jump
skip
hop
dance
sing
play
work
rest
sleep
wake
dream
think
ponder
consider
contemplate
meditate
focus
concentrate
attend
notice
observe
perceive
sense
feel
touch
taste
smell
hear
see
look
watch
view
witness
experience
undergo
suffer
enjoy
appreciate
value
prize
treasure
cherish
love
hate
like
dislike
prefer
favor
support
oppose
resist
fight
defend
attack
strike
hit
punch
kick
throw
catch
grab
hold
release
free
liberate
capture
trap
ensnare
entangle
wrap
fold
unfold
bend
straighten
twist
turn
spin
revolve
orbit
circle
curve
arc
zigzag
meander
wander
roam
explore
discover
find
lose
miss
seek
search
hunt
chase
pursue
follow
lead
guide
direct
steer
navigate
pilot
drive
ride
fly
sail
swim
float
sink
dive
climb
crawl
walk
run
sprint
dash
race
compete
win
lose
tie
draw
bet
gamble
risk
venture
invest
spend
save
earn
gain
profit
benefit
advantage
disadvantage
harm
hurt
damage
destroy
ruin
wreck
break
crack
split
tear
rip
cut
slice
dice
chop
mince
grind
crush
smash
shatter
explode
implode
collapse
fall
rise
lift
raise
lower
drop
throw
toss
pitch
hurl
fling
cast
shoot
fire
launch
deploy
release
free
capture
catch
grab
snatch
seize
take
give
offer
provide
supply
deliver
send
receive
accept
reject
refuse
deny
grant
allow
permit
forbid
prohibit
ban
restrict
limit
constrain
confine
contain
hold
keep
maintain
sustain
support
bear
carry
transport
move
shift
transfer
relocate
displace
replace
substitute
swap
exchange
trade
barter
buy
sell
purchase
acquire
obtain
get
gather
collect
accumulate
amass
pile
stack
heap
arrange
organize
sort
classify
categorize
group
cluster
gather
scatter
disperse
spread
distribute
allocate
assign
delegate
appoint
elect
choose
select
pick
opt
decide
determine
resolve
settle
fix
establish
institute
found
create
generate
produce
make
build
construct
fabricate
manufacture
assemble
disassemble
dismantle
take
break
destroy
