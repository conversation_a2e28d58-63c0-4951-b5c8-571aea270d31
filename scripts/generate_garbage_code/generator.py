#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主生成脚本 - 生成符合Dart语法规范的独立工具类代码文件
"""

import os
import random
import argparse
import string
from typing import Tuple

import config
import utils

def generate_simple_method_body(return_type: str) -> str:
    """
    生成简单的方法体，避免使用复杂的条件替换

    Args:
        return_type: 返回类型

    Returns:
        方法体字符串
    """
    lines = []

    if return_type == 'void':
        # void方法不需要返回值
        lines.append(f"    print('Processing simple operation');")
        lines.append(f"    return;")
    elif return_type.startswith('Future<'):
        # 异步方法，添加async关键字
        inner_type = return_type[7:-1]  # 提取Future<T>中的T
        lines.insert(0, f"  async {{\n")  # 在方法体开头添加async关键字
        lines.append(f"    await Future.delayed(Duration(milliseconds: 100));")

        if inner_type == 'void':
            lines.append(f"    print('Async operation completed');")
            lines.append(f"    return;")
        elif inner_type == 'String':
            lines.append(f"    return 'async result';")
        elif inner_type == 'int':
            lines.append(f"    return 42;")
        elif inner_type == 'double':
            lines.append(f"    return 3.14;")
        elif inner_type == 'bool':
            lines.append(f"    return true;")
        elif inner_type.startswith('Future<'):
            # 处理嵌套Future类型，如Future<Future<T>>
            nested_type = inner_type[7:-1]  # 提取Future<T>中的T
            if nested_type == 'void':
                lines.append(f"    return Future<void>.value();")
            elif nested_type == 'String':
                lines.append(f"    return Future<String>.value('nested async result');")
            elif nested_type == 'int':
                lines.append(f"    return Future<int>.value(42);")
            elif nested_type == 'double':
                lines.append(f"    return Future<double>.value(3.14);")
            elif nested_type == 'bool':
                lines.append(f"    return Future<bool>.value(true);")
            elif nested_type.startswith('List<'):
                element_type = nested_type[5:-1]
                if element_type == 'String':
                    lines.append(f"    return Future<List<String>>.value(['item1', 'item2']);")
                elif element_type == 'int':
                    lines.append(f"    return Future<List<int>>.value([1, 2, 3]);")
                elif element_type == 'double':
                    lines.append(f"    return Future<List<double>>.value([1.1, 2.2, 3.3]);")
                elif element_type == 'bool':
                    lines.append(f"    return Future<List<bool>>.value([true, false]);")
                else:
                    lines.append(f"    return Future<List>.value([]);")
            elif nested_type.startswith('Map<'):
                lines.append(f"    return Future<Map>.value({{}});")
            elif nested_type.startswith('Set<'):
                lines.append(f"    return Future<Set>.value({{}});")
            else:
                lines.append(f"    return Future.value(null);")
        elif inner_type.startswith('List<'):
            element_type = inner_type[5:-1]
            if element_type == 'String':
                lines.append(f"    return ['item1', 'item2'];")
            elif element_type == 'int':
                lines.append(f"    return [1, 2, 3];")
            elif element_type == 'double':
                lines.append(f"    return [1.1, 2.2, 3.3];")
            elif element_type == 'bool':
                lines.append(f"    return [true, false];")
            else:
                lines.append(f"    return [];")
        elif inner_type.startswith('Map<'):
            key_type = inner_type[4:].split(',')[0].strip()
            value_type = inner_type.split(',')[1].strip()[:-1]
            if key_type == 'String':
                if value_type == 'String':
                    lines.append(f"    return {{'key': 'value'}};")
                elif value_type == 'int':
                    lines.append(f"    return {{'key': 1}};")
                elif value_type == 'dynamic':
                    lines.append(f"    return {{'key': 'value'}};")
                else:
                    lines.append(f"    return {{}};")
            else:
                lines.append(f"    return {{}};")
        elif inner_type.startswith('Set<'):
            element_type = inner_type[4:-1]
            if element_type == 'String':
                lines.append(f"    return {{'item1', 'item2'}};")
            elif element_type == 'int':
                lines.append(f"    return {{1, 2, 3}};")
            else:
                lines.append(f"    return {{}};")
        elif inner_type.startswith('Stream<'):
            element_type = inner_type[7:-1]
            if element_type == 'String':
                lines.append(f"    return Stream<String>.fromIterable(['item1', 'item2']);")
            elif element_type == 'int':
                lines.append(f"    return Stream<int>.fromIterable([1, 2, 3]);")
            else:
                lines.append(f"    return Stream.empty();")
        else:
            lines.append(f"    return null;")
        lines.append(f"  }}")  # 在方法体结尾添加闭合括号
    elif return_type.startswith('Stream<'):
        # Stream方法，添加async*关键字
        element_type = return_type[7:-1]  # 提取Stream<T>中的T
        lines.insert(0, f"  async* {{\n")  # 在方法体开头添加async*关键字
        if element_type == 'String':
            lines.append(f"    yield 'item1';")
            lines.append(f"    yield 'item2';")
        elif element_type == 'int':
            lines.append(f"    yield 1;")
            lines.append(f"    yield 2;")
        elif element_type == 'double':
            lines.append(f"    yield 1.1;")
            lines.append(f"    yield 2.2;")
        elif element_type == 'bool':
            lines.append(f"    yield true;")
            lines.append(f"    yield false;")
        else:
            lines.append(f"    if (false) yield null;")  # 这样可以避免实际产生任何元素
        lines.append(f"  }}")  # 在方法体结尾添加闭合括号
    else:

        if return_type == 'String':
            lines.append(f"    return 'simple result';")
        elif return_type == 'int':
            lines.append(f"    return 42;")
        elif return_type == 'double':
            lines.append(f"    return 3.14;")
        elif return_type == 'bool':
            lines.append(f"    return true;")
        elif return_type.startswith('List<'):
            element_type = return_type[5:-1]
            if element_type == 'String':
                lines.append(f"    return ['item1', 'item2'];")
            elif element_type == 'int':
                lines.append(f"    return [1, 2, 3];")
            elif element_type == 'double':
                lines.append(f"    return [1.1, 2.2, 3.3];")
            elif element_type == 'bool':
                lines.append(f"    return [true, false];")
            else:
                lines.append(f"    return [];")
        elif return_type.startswith('Map<'):
            key_type = return_type[4:].split(',')[0].strip()
            value_type = return_type.split(',')[1].strip()[:-1]
            if key_type == 'String':
                if value_type == 'String':
                    lines.append(f"    return {{'key': 'value'}};")
                elif value_type == 'int':
                    lines.append(f"    return {{'key': 1}};")
                elif value_type == 'double':
                    lines.append(f"    return {{'key': 1.1}};")
                elif value_type == 'bool':
                    lines.append(f"    return {{'key': true}};")
                elif value_type == 'dynamic':
                    lines.append(f"    return {{'key': 'value'}};")
                else:
                    lines.append(f"    return {{}};")
            else:
                lines.append(f"    return {{}};")
        elif return_type.startswith('Set<'):
            element_type = return_type[4:-1]
            if element_type == 'String':
                lines.append(f"    return {{'item1', 'item2'}};")
            elif element_type == 'int':
                lines.append(f"    return {{1, 2, 3}};")
            else:
                lines.append(f"    return {{}};")
        elif return_type.endswith('Helper') or return_type.endswith('Service') or return_type.endswith('Manager') or return_type.endswith('Util') or return_type.endswith('Handler') or return_type.endswith('Processor') or return_type.endswith('Converter'):
            # 对于自定义类型，返回null并添加注释
            lines.append(f"    return {return_type}._();")
        else:
            lines.append(f"    return null;")

    return "\n".join(lines)


def get_default_value_for_type(return_type: str) -> str:
    """
    根据返回类型生成默认返回值

    Args:
        return_type: 返回类型

    Returns:
        默认返回值字符串
    """
    if return_type == 'void':
        return ''  # void类型不需要返回值
    elif return_type == 'String':
        return "'result'"
    elif return_type == 'int':
        return '42'
    elif return_type == 'double':
        return '3.14'
    elif return_type == 'bool':
        return 'true'
    elif return_type.startswith('List<'):
        if 'String' in return_type:
            return "['item1', 'item2']"
        elif 'int' in return_type:
            return '[1, 2, 3]'
        elif 'double' in return_type:
            return '[1.1, 2.2, 3.3]'
        elif 'bool' in return_type:
            return '[true, false]'
        else:
            return '[]'
    elif return_type.startswith('Map<'):
        if 'String, String' in return_type:
            return "{'key': 'value'}"
        elif 'String, int' in return_type:
            return "{'key': 1}"
        elif 'String, dynamic' in return_type or 'dynamic' in return_type:
            return "{'key': 'value'}"
        else:
            return '{}'
    elif return_type.startswith('Set<'):
        if 'String' in return_type:
            return "<String>{'item1', 'item2'}"
        elif 'int' in return_type:
            return "<int>{1, 2, 3}"
        else:
            return "<dynamic>{}"
    elif return_type.startswith('Future<'):
        inner_type = return_type[7:-1]  # 提取Future<T>中的T
        if inner_type == 'void':
            return 'Future<void>.value()'
        elif inner_type == 'String':
            return "Future<String>.value('async result')"
        elif inner_type == 'int':
            return 'Future<int>.value(42)'
        elif inner_type == 'double':
            return 'Future<double>.value(3.14)'
        elif inner_type == 'bool':
            return 'Future<bool>.value(true)'
        elif inner_type.startswith('List<'):
            if 'String' in inner_type:
                return "Future<List<String>>.value(['item1', 'item2'])"
            elif 'int' in inner_type:
                return 'Future<List<int>>.value([1, 2, 3])'
            elif 'double' in inner_type:
                return 'Future<List<double>>.value([1.1, 2.2, 3.3])'
            elif 'bool' in inner_type:
                return 'Future<List<bool>>.value([true, false])'
            else:
                return 'Future<List>.value([])'
        elif inner_type.startswith('Map<'):
            if 'String, String' in inner_type:
                return "Future<Map<String, String>>.value({'key': 'value'})"
            elif 'String, int' in inner_type:
                return "Future<Map<String, int>>.value({'key': 1})"
            elif 'String, dynamic' in inner_type or 'dynamic' in inner_type:
                return "Future<Map<String, dynamic>>.value({'key': 'value'})"
            else:
                return 'Future<Map>.value({})'
        else:
            return 'Future.value(null)'
    elif return_type.startswith('Stream<'):
        inner_type = return_type[7:-1]  # 提取Stream<T>中的T
        if inner_type == 'String':
            return "Stream<String>.fromIterable(['item1', 'item2'])"
        elif inner_type == 'int':
            return 'Stream<int>.fromIterable([1, 2, 3])'
        elif inner_type == 'double':
            return 'Stream<double>.fromIterable([1.1, 2.2, 3.3])'
        elif inner_type == 'bool':
            return 'Stream<bool>.fromIterable([true, false])'
        else:
            return 'Stream.empty()'
    else:
        # 对于自定义类型，返回null
        return 'null'

def generate_method_body(return_type: str) -> str:
    """
    根据返回类型生成方法体

    Args:
        return_type: 方法返回类型

    Returns:
        方法体字符串
    """
    # 使用简单的方法体，避免复杂的条件替换
    return generate_simple_method_body(return_type)

def get_random_value_by_type(type_name: str) -> str:
    """
    根据类型生成随机值

    Args:
        type_name: 类型名称

    Returns:
        随机值字符串
    """
    if type_name == 'String':
        return f"'{utils.get_random_word('nouns')}'"
    elif type_name == 'int':
        return str(random.randint(0, 1000))
    elif type_name == 'double':
        return f"{random.uniform(0, 100):.2f}"
    elif type_name == 'bool':
        return random.choice(['true', 'false'])
    elif type_name.startswith('List<'):
        inner_type = type_name[5:-1]  # 提取List<T>中的T
        items = [get_random_value_by_type(inner_type) for _ in range(random.randint(1, 4))]
        return f"[{', '.join(items)}]"
    elif type_name.startswith('Map<'):
        # 处理不同类型的Map
        if type_name.startswith('Map<String, String>'):
            keys = [f"'{utils.get_random_word('nouns')}'" for _ in range(random.randint(1, 3))]
            values = [f"'{utils.get_random_word('nouns')}'" for _ in range(len(keys))]
            items = [f"{k}: {v}" for k, v in zip(keys, values)]
            return f"{{{', '.join(items)}}}"
        elif type_name.startswith('Map<String, int>'):
            keys = [f"'{utils.get_random_word('nouns')}'" for _ in range(random.randint(1, 3))]
            values = [str(random.randint(0, 1000)) for _ in range(len(keys))]
            items = [f"{k}: {v}" for k, v in zip(keys, values)]
            return f"{{{', '.join(items)}}}"
        elif type_name.startswith('Map<String, bool>'):
            keys = [f"'{utils.get_random_word('nouns')}'" for _ in range(random.randint(1, 3))]
            values = [random.choice(['true', 'false']) for _ in range(len(keys))]
            items = [f"{k}: {v}" for k, v in zip(keys, values)]
            return f"{{{', '.join(items)}}}"
        else:
            # 默认处理为Map<String, dynamic>
            keys = [f"'{utils.get_random_word('nouns')}'" for _ in range(random.randint(1, 3))]
            values = [get_random_value_by_type('String') for _ in range(len(keys))]
            items = [f"{k}: {v}" for k, v in zip(keys, values)]
            return f"{{{', '.join(items)}}}"
    elif type_name.startswith('Set<'):
        inner_type = type_name[4:-1]  # 提取Set<T>中的T
        items = [get_random_value_by_type(inner_type) for _ in range(random.randint(1, 4))]
        return f"{{{', '.join(items)}}}"
    elif type_name.startswith('Future<'):
        # 对于Future类型，返回一个Future.value()
        inner_type = type_name[7:-1]  # 提取Future<T>中的T

        # 处理嵌套Future类型
        if inner_type.startswith('Future<'):
            # 对于Future<Future<T>>类型，直接返回内部Future<T>的值
            inner_inner_type = inner_type[7:-1]  # 提取Future<T>中的T
            if inner_inner_type == 'void':
                return 'Future.value()'
            else:
                inner_value = get_random_value_by_type(inner_inner_type)
                return f"Future.value({inner_value})"
        elif inner_type == 'void':
            return 'Future.value()'
        else:
            inner_value = get_random_value_by_type(inner_type)
            return f"Future.value({inner_value})"
    elif type_name.startswith('Stream<'):
        # 对于Stream类型，返回一个Stream.empty()
        return 'Stream.empty()'
    elif type_name == 'void':
        # void类型不能作为值
        return 'null'
    else:
        # 默认返回null
        return 'null'

def generate_field_declarations(count: int = None) -> str:
    """
    生成字段声明

    Args:
        count: 字段数量，如果为None则随机生成

    Returns:
        字段声明字符串
    """
    if count is None:
        count = random.randint(config.MIN_FIELDS_PER_CLASS, config.MAX_FIELDS_PER_CLASS)

    field_templates = utils.load_templates('field')
    if not field_templates:
        return "  // No fields"

    fields = []
    # 跟踪已使用的字段名，避免重复
    used_field_names = set()

    for _ in range(count):
        # 随机选择一个字段模板
        template_name = random.choice(list(field_templates.keys()))
        template = field_templates[template_name]

        # 生成字段名，确保不重复
        field_name = utils.generate_field_name()
        while field_name in used_field_names:
            field_name = utils.generate_field_name()

        # 添加到已使用的字段名集合
        used_field_names.add(field_name)

        # 确保字段类型不是无效类型（如void）
        valid_types = [t for t in config.DART_TYPES if t not in config.INVALID_FIELD_TYPES]
        field_type = random.choice(valid_types)

        # 生成字段描述
        field_description = utils.generate_description('field')

        # 生成默认值
        default_value = get_random_value_by_type(field_type)

        # 如果是const字段，确保默认值是编译时常量
        if template_name in ['static_const_field', 'const_field']:
            # 对于Future类型，不使用const初始化
            if field_type.startswith('Future<'):
                template = field_templates['static_field'] if template_name == 'static_const_field' else field_templates['final_field']
            # 对于其他非编译时常量类型，也不使用const
            elif field_type.startswith('Map<') or field_type.startswith('List<') or field_type.startswith('Set<'):
                if random.random() > 0.5:  # 50%的概率使用非const模板
                    template = field_templates['static_field'] if template_name == 'static_const_field' else field_templates['final_field']

        # 替换占位符
        field_declaration = utils.replace_placeholders(template, {
            'FIELD_NAME': field_name,
            'FIELD_TYPE': field_type,
            'FIELD_DESCRIPTION': field_description,
            'FIELD_DEFAULT_VALUE': default_value
        })

        fields.append(field_declaration)

    return "\n".join(fields)

def generate_method_implementations(class_name: str = None) -> str:
    """
    生成方法实现

    Args:
        class_name: 类名，用于生成特定的方法体

    Returns:
        方法实现字符串
    """
    # 加载方法模板
    function_templates = utils.load_templates('function')
    if not function_templates:
        raise ValueError("No function templates found")

    # 随机选择3-8个方法
    num_methods = random.randint(3, 8)

    # 跟踪已使用的方法名，避免重复
    used_method_names = set()

    methods = []

    for _ in range(num_methods):
        # 随机选择返回类型
        return_type = random.choice(config.DART_TYPES)

        # 根据返回类型确定可用的模板类型
        available_templates = []

        # 对于Future类型，只使用async_method模板
        if return_type.startswith('Future<'):
            if 'async_method' in function_templates:
                available_templates.append('async_method')

        # 对于Stream类型，只使用stream_method模板
        elif return_type.startswith('Stream<'):
            if 'stream_method' in function_templates:
                available_templates.append('stream_method')
        else:
            # 随机决定是否使用静态方法（只对非Future和非Stream类型）
            use_static = random.random() < 0.3  # 30%的概率使用静态方法
            if use_static and 'static_method' in function_templates:
                available_templates.append('static_method')

            # 随机决定是否使用默认值方法
            use_default_value = random.random() < 0.3  # 30%的概率使用默认值方法
            if use_default_value and 'default_value_method' in function_templates:
                available_templates.append('default_value_method')

            # 随机决定是否使用工厂方法（需要类名）
            use_factory = random.random() < 0.2  # 20%的概率使用工厂方法
            if use_factory and 'factory_method' in function_templates and class_name:
                available_templates.append('factory_method')

            # 随机决定是否使用fromJson方法（需要类名）
            use_from_json = random.random() < 0.2  # 20%的概率使用fromJson方法
            if use_from_json and 'from_json_method' in function_templates and class_name:
                available_templates.append('from_json_method')

            # 随机决定是否使用try-catch方法
            use_try_catch = random.random() < 0.3  # 30%的概率使用try-catch方法
            if use_try_catch and 'try_catch_method' in function_templates:
                available_templates.append('try_catch_method')

            # 随机决定是否使用callback方法
            use_callback = random.random() < 0.25  # 25%的概率使用callback方法
            if use_callback and 'callback_method' in function_templates:
                available_templates.append('callback_method')

            # 随机决定是否使用链式方法（需要类名）
            use_chained = random.random() < 0.25  # 25%的概率使用链式方法
            if use_chained and 'chained_method' in function_templates and class_name:
                # 链式方法必须返回类名类型
                available_templates.append('chained_method')

            # 随机决定是否使用getter方法
            use_getter = random.random() < 0.3  # 30%的概率使用getter方法
            if use_getter and 'getter_method' in function_templates:
                available_templates.append('getter_method')

            # 随机决定是否使用条件方法（仅适用于特定返回类型）
            use_conditional = random.random() < 0.3  # 30%的概率使用条件方法
            supported_types = [
                'String', 'int', 'double', 'bool', 'void',
                'List<String>', 'List<int>', 'List<double>', 'List<bool>',
                'Set<String>', 'Set<int>',
                'Map<String, String>', 'Map<String, int>', 'Map<String, dynamic>'
            ]
            if use_conditional and 'conditional_method' in function_templates and return_type in supported_types:
                available_templates.append('conditional_method')

            # 暂时禁用可选参数方法，因为它可能导致语法错误
            # 随机决定是否使用可选参数方法
            # use_optional_params = random.random() < 0.3  # 30%的概率使用可选参数方法
            # if use_optional_params and 'optional_params_method' in function_templates:
            #     available_templates.append('optional_params_method')

            # 随机决定是否使用迭代器方法（仅适用于基本类型）
            use_iterator = random.random() < 0.2  # 20%的概率使用迭代器方法
            # 迭代器方法只能用于基本类型，且不能用于void
            if use_iterator and 'iterator_method' in function_templates and return_type in ['String', 'int', 'double', 'bool']:
                available_templates.append('iterator_method')

            # 对于普通类型，添加simple_method作为备选
            if 'simple_method' in function_templates:
                available_templates.append('simple_method')

        # 如果没有找到合适的模板，使用安全的备选模板
        if not available_templates:
            # 根据返回类型选择安全的备选模板
            if return_type.startswith('Future<'):
                # 对于Future类型，使用async_method或创建一个临时的async模板
                if 'async_method' in function_templates:
                    available_templates.append('async_method')
                elif 'simple_method' in function_templates:
                    # 使用simple_method，但稍后会添加async关键字
                    available_templates.append('simple_method')
            elif return_type.startswith('Stream<'):
                # 对于Stream类型，使用stream_method或创建一个临时的stream模板
                if 'stream_method' in function_templates:
                    available_templates.append('stream_method')
                elif 'simple_method' in function_templates:
                    # 使用simple_method，但稍后会添加async*关键字
                    available_templates.append('simple_method')
            else:
                # 对于其他类型，使用simple_method
                if 'simple_method' in function_templates:
                    available_templates.append('simple_method')
                else:
                    # 如果没有simple_method，使用任何可用的模板
                    available_templates = list(function_templates.keys())

        # 随机选择一个模板
        template_name = random.choice(available_templates)
        template = function_templates[template_name]

        # 生成方法名
        method_name = utils.generate_method_name()

        # 确保方法名不重复
        while method_name in used_method_names:
            method_name = utils.generate_method_name()

        used_method_names.add(method_name)

        # 生成方法体，确保返回值与返回类型匹配
        method_body = generate_method_body(return_type)

        # 使用简单的参数列表，避免复杂的模板处理
        param_type = random.choice(['String', 'int', 'double', 'bool'])
        param_name = utils.generate_field_name()

        # 使用命名参数（花括号）并添加默认值
        if param_type == 'String':
            parameter_list = f"{{{param_type} {param_name} = 'default'}}"
        elif param_type == 'int':
            parameter_list = f"{{{param_type} {param_name} = 0}}"
        elif param_type == 'double':
            parameter_list = f"{{{param_type} {param_name} = 0.0}}"
        elif param_type == 'bool':
            parameter_list = f"{{{param_type} {param_name} = false}}"
        else:
            parameter_list = f"{{{param_type} {param_name}}}"

        # 生成方法描述
        method_description = utils.generate_description('method')

        # 生成默认返回值
        default_value = get_default_value_for_type(return_type)

        # 替换占位符
        replacements = {
            'METHOD_NAME': method_name,
            'RETURN_TYPE': return_type,
            'PARAMETER_LIST': parameter_list,
            'METHOD_DESCRIPTION': method_description,
            'METHOD_BODY': method_body,
            'DEFAULT_VALUE': default_value
        }

        # 如果有类名，添加到替换字典中
        if class_name:
            replacements['CLASS_NAME'] = class_name

        # 如果是链式方法，需要特殊处理返回类型
        if template_name == 'chained_method' and class_name:
            # 链式方法需要返回类名类型，覆盖之前的返回类型
            replacements['RETURN_TYPE'] = class_name

        # 根据返回类型和模板类型调整模板
        if template_name == 'static_method':
            # 对于静态方法，需要在应用模板前修改模板本身
            if return_type.startswith('Future<'):
                # 修改静态方法模板，添加async关键字
                modified_template = template.replace('static {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {',
                                                   'static {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) async {')
                method_implementation = utils.replace_placeholders(modified_template, replacements)
            elif return_type.startswith('Stream<'):
                # 修改静态方法模板，添加async*关键字
                modified_template = template.replace('static {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {',
                                                   'static {{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) async* {')
                method_implementation = utils.replace_placeholders(modified_template, replacements)
            else:
                # 普通静态方法，使用原始模板
                method_implementation = utils.replace_placeholders(template, replacements)
        elif template_name in ['factory_method', 'from_json_method']:
            # 工厂方法和fromJson方法不需要特殊处理，直接应用模板
            method_implementation = utils.replace_placeholders(template, replacements)
        elif template_name == 'try_catch_method':
            # try_catch方法需要特殊处理，确保异步方法正确处理
            if return_type.startswith('Future<'):
                # 对于Future类型，添加async关键字
                modified_template = template.replace('{{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {',
                                                   '{{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) async {')
                method_implementation = utils.replace_placeholders(modified_template, replacements)
            elif return_type.startswith('Stream<'):
                # 对于Stream类型，添加async*关键字
                modified_template = template.replace('{{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) {',
                                                   '{{RETURN_TYPE}} {{METHOD_NAME}}({{PARAMETER_LIST}}) async* {')
                method_implementation = utils.replace_placeholders(modified_template, replacements)
            else:
                # 普通方法，直接应用模板
                method_implementation = utils.replace_placeholders(template, replacements)
        elif template_name in ['callback_method', 'optional_params_method', 'conditional_method']:
            # 这些方法不需要特殊处理，直接应用模板
            method_implementation = utils.replace_placeholders(template, replacements)
        elif template_name == 'getter_method':
            # getter方法需要特殊处理，确保方法体正确
            method_implementation = utils.replace_placeholders(template, replacements)
        elif template_name == 'iterator_method':
            # 迭代器方法需要特殊处理，确保返回类型正确
            # 创建一个新的替换字典，确保RETURN_TYPE是正确的
            iterator_replacements = replacements.copy()
            # 迭代器方法的返回类型是固定的，但内部类型需要匹配原始返回类型
            method_implementation = utils.replace_placeholders(template, iterator_replacements)
        elif template_name == 'default_value_method':
            # 默认值方法需要特殊处理，确保返回类型正确
            # 首先修改模板，将'{{RETURN_TYPE}}'替换为实际的返回类型
            if return_type.startswith('Stream<'):
                # 对于Stream类型，需要使用yield语句
                element_type = return_type[7:-1]  # 提取Stream<T>中的T
                stream_body = ""
                if element_type == 'String':
                    stream_body = "    yield 'item1';\n    yield 'item2';"
                elif element_type == 'int':
                    stream_body = "    yield 1;\n    yield 2;"
                elif element_type == 'double':
                    stream_body = "    yield 1.1;\n    yield 2.2;"
                elif element_type == 'bool':
                    stream_body = "    yield true;\n    yield false;"
                else:
                    stream_body = "    // 生成空流"

                # 创建一个新的模板，不使用原始模板
                modified_template = f"""  /// {{{{METHOD_DESCRIPTION}}}}
  {return_type} {method_name}({{{{PARAMETER_LIST}}}}) async* {{
    // 生成流数据
{stream_body}
  }}"""
            elif return_type.startswith('Future<'):
                # 对于Future类型，添加async关键字
                # 先替换方法名
                modified_template = template.replace("{{METHOD_NAME}}", method_name)
                # 然后替换返回类型
                modified_template = modified_template.replace("'{{RETURN_TYPE}}'", f"'{return_type}'")
                # 最后添加async关键字
                modified_template = modified_template.replace(f"{return_type} {method_name}({{PARAMETER_LIST}}) {{",
                                                           f"{return_type} {method_name}({{PARAMETER_LIST}}) async {{")
            else:
                # 对于普通类型，只替换方法名和返回类型
                # 先替换方法名
                modified_template = template.replace("{{METHOD_NAME}}", method_name)
                # 然后替换返回类型
                modified_template = modified_template.replace("'{{RETURN_TYPE}}'", f"'{return_type}'")

            method_implementation = utils.replace_placeholders(modified_template, replacements)
        else:
            # 对于非静态方法，先应用模板
            method_implementation = utils.replace_placeholders(template, replacements)

            # 然后根据需要添加async或async*关键字
            if return_type.startswith('Future<') and template_name == 'simple_method':
                # 简单方法模板需要添加async关键字
                method_implementation = method_implementation.replace(') {', ') async {')
            elif return_type.startswith('Stream<') and template_name == 'simple_method':
                # 简单方法模板需要添加async*关键字
                method_implementation = method_implementation.replace(') {', ') async* {')

        methods.append(method_implementation)

    return "\n".join(methods)

def generate_dart_file() -> Tuple[str, str]:
    """
    生成一个完整的Dart文件

    Returns:
        (文件名, 文件内容)的元组
    """
    # 随机选择一个类模板
    class_templates = utils.load_templates('class')
    if not class_templates:
        raise ValueError("No class templates found")

    template_name = random.choice(list(class_templates.keys()))
    template = class_templates[template_name]

    # 生成类名
    class_name = utils.generate_class_name()

    # 生成文件名
    file_name = utils.generate_file_name()

    # 生成类描述
    class_description = utils.generate_description('class')

    # 生成文件描述
    file_description = utils.generate_description('file')

    # 生成导入语句
    import_statements = utils.get_random_imports()

    # 生成字段声明
    field_declarations = generate_field_declarations()

    # 生成方法实现，传递类名
    method_implementations = generate_method_implementations(class_name=class_name)

    # 获取当前日期
    generation_date = utils.get_current_date()

    # 替换占位符
    file_content = utils.replace_placeholders(template, {
        'CLASS_NAME': class_name,
        'CLASS_DESCRIPTION': class_description,
        'FIELD_DECLARATIONS': field_declarations,
        'METHOD_IMPLEMENTATIONS': method_implementations,
        'IMPORT_STATEMENTS': import_statements,
        'FILE_DESCRIPTION': file_description,
        'GENERATION_DATE': generation_date
    })

    return (file_name, file_content)

def analyze_dart_files(output_dir):
    """
    分析生成的Dart文件，检查语法错误

    Args:
        output_dir: 输出目录路径

    Returns:
        错误信息列表
    """
    import subprocess
    import re

    print("开始分析Dart文件语法...")

    try:
        # 执行Dart分析命令，不过滤结果，以便查看完整输出
        print("执行命令: fvm dart analyze " + output_dir)
        result = subprocess.run(
            f"fvm dart analyze {output_dir}",
            shell=True,
            capture_output=True,
            text=True
        )

        # 打印完整输出以便调试
        print("\n完整分析输出:")
        print(result.stdout)

        # 再次运行，但只过滤错误
        result_errors = subprocess.run(
            f"fvm dart analyze {output_dir} | grep -i 'error'",
            shell=True,
            capture_output=True,
            text=True
        )

        # 解析错误信息
        errors = []
        if result_errors.stdout:
            error_lines = result_errors.stdout.strip().split('\n')
            for line in error_lines:
                if line.strip():
                    errors.append(line)

            # 按文件名分组错误
            error_by_file = {}
            for error in errors:
                # 尝试提取文件名和错误信息
                match = re.search(r'([^:]+):(\d+):(\d+):(.*)', error)
                if match:
                    file_path, line_num, col_num, message = match.groups()
                    file_name = os.path.basename(file_path)

                    if file_name not in error_by_file:
                        error_by_file[file_name] = []

                    error_by_file[file_name].append({
                        'line': int(line_num),
                        'column': int(col_num),
                        'message': message.strip(),
                        'full_error': error
                    })

            # 打印分组后的错误
            print(f"发现 {len(errors)} 个错误:")
            for file_name, file_errors in error_by_file.items():
                print(f"\n文件 {file_name} 中有 {len(file_errors)} 个错误:")
                for err in file_errors:
                    print(f"  行 {err['line']}, 列 {err['column']}: {err['message']}")
        else:
            print("没有发现语法错误！")

        return errors

    except Exception as e:
        print(f"执行Dart分析时出错: {e}")
        return []

def generate_references_file(output_dir: str, generated_files: list) -> str:
    """
    生成一个references.dart文件，导入所有生成的Dart文件

    Args:
        output_dir: 输出目录路径
        generated_files: 生成的Dart文件名列表

    Returns:
        references.dart文件的路径
    """
    if not generated_files:
        print("没有生成的文件，跳过创建references.dart")
        return None

    references_path = os.path.join(output_dir, "references.dart")

    # 生成文件头部注释
    content = [
        "// 此文件由generator.py自动生成",
        "// 用于引用所有生成的Dart垃圾代码文件，防止Flutter的Tree shaking机制移除这些文件",
        "// 生成日期: " + utils.get_current_date(),
        ""
    ]

    # 添加导入语句
    for file_name in generated_files:
        # 在Dart中，导入路径需要包含文件扩展名
        content.append(f"import './{file_name}';")

    # 添加一个空类，确保文件有实际内容
    content.extend([
        "",
        "/// 引用所有生成的垃圾代码文件的类",
        "/// 防止Flutter的Tree shaking机制移除这些文件",
        "class GarbageCodeReferences {",
        "  /// 私有构造函数，防止实例化",
        "  GarbageCodeReferences._();",
        "",
        "  /// 单例实例",
        "  static final GarbageCodeReferences _instance = GarbageCodeReferences._();",
        "",
        "  /// 获取单例实例",
        "  static GarbageCodeReferences get instance => _instance;",
        "",
        "  /// 初始化方法，确保所有引用的文件都被加载",
        "  void initialize() {",
        "    print('初始化垃圾代码引用');",
        "    // 此方法仅用于确保所有引用的文件都被加载",
        "    // 实际上不需要执行任何操作",
        "  }",
        "}",
    ])

    # 写入文件
    with open(references_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(content))

    return references_path

def insert_import_to_biz_dart():
    """
    将references.dart的import语句插入到modules/biz/lib/biz.dart中（如未存在则插入，已存在则跳过）
    """
    import os
    biz_dart_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'modules', 'biz', 'lib', 'biz.dart')
    import_line = "import 'package:biz/garbage/references.dart';\n"
    try:
        with open(biz_dart_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # 检查是否已存在import
        if any(import_line.strip() == line.strip() for line in lines):
            print('biz.dart已包含references.dart的import，无需重复插入')
            return
        # 找到最后一个import/export语句的位置
        last_import_idx = -1
        for idx, line in enumerate(lines):
            if line.strip().startswith('import') or line.strip().startswith('export'):
                last_import_idx = idx
        # 插入import语句
        insert_idx = last_import_idx + 1
        lines.insert(insert_idx, import_line)
        with open(biz_dart_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print('已将references.dart的import插入到biz.dart')
    except Exception as e:
        print(f'插入import到biz.dart时出错: {e}')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成符合Dart语法规范的独立工具类代码文件')
    parser.add_argument('-n', '--num', type=int, default=config.DEFAULT_FILE_COUNT,
                        help=f'生成文件数量 (默认: {config.DEFAULT_FILE_COUNT})')
    parser.add_argument('-o', '--output', type=str, default=config.OUTPUT_DIR,
                        help=f'输出目录 (默认: {config.OUTPUT_DIR})')
    parser.add_argument('--analyze', action='store_true',
                        help='生成后分析Dart文件语法')
    args = parser.parse_args()

    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)

    print(f"开始生成 {args.num} 个Dart文件...")

    # 用于收集生成的文件名
    generated_files = []

    for i in range(args.num):
        try:
            file_name, file_content = generate_dart_file()
            file_path = os.path.join(args.output, file_name)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)

            # 添加到生成的文件列表
            generated_files.append(file_name)

            print(f"[{i+1}/{args.num}] 已生成: {file_path}")
        except Exception as e:
            print(f"生成文件时出错: {e}")

    print("生成完成!")

    # 生成references.dart文件
    if generated_files:
        references_path = generate_references_file(args.output, generated_files)
        if references_path:
            print(f"已生成引用文件: {references_path}")
            insert_import_to_biz_dart()

    # 如果指定了分析选项，则分析生成的Dart文件
    if args.analyze:
        errors = analyze_dart_files(args.output)
        if not errors:
            print("所有文件通过语法检查！")
        else:
            print(f"发现 {len(errors)} 个语法错误，请检查并修复模板文件。")

if __name__ == '__main__':
    main()
