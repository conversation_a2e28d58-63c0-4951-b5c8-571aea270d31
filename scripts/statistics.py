import os
import json
import time
import jinja2
import shutil
import sys


def generate_template_code(config_path: str, save_path: str):
    """
    根据json配置文件，自动生成打点代码文件
    :param config_path: json配置文件路径地址
    :param save_path: 生成的代码文件保存目录
    :return:
    """
    data = []
    if not os.path.exists(config_path):
        raise Exception("文件不存在，请检查文件路径")
    try:
        content = json.load(open(config_path, "r", encoding="utf-8"))
    except:
        raise Exception("文件不是一个json格式，请检查格式是否正确")
    if os.path.exists(save_path):
        shutil.rmtree(save_path)
    os.mkdir(save_path)
    for element in content:
        element = dict(element)
        temp_json = {}
        action = element.get("action", "")
        if action:
            temp_json["action"] = action
            name_list = action.split("_")
            name_list_cap = list(map(lambda x: str(x).capitalize(), name_list))
            temp_json["func_name"] = "report" + "".join(name_list_cap).replace("-", "")  # 函数名字
        else:
            raise Exception("action为空")

        temp_json["annotation_header"] = element.get("timing", "")
        temp_json["annotation_foot"] = "highPriority 是否高优先级"
        if len(element["params"]) == 0:
            temp_json["params"] = []
        else:
            element_list = list(element["params"])
            for i in range(len(element_list)):
                name = element_list[i]["name"]
                element_list[i]["origin"] = name
                name_split = str(name).split("_")
                if len(name_split) > 1:
                    ret_list = list(map(lambda x: str(x).capitalize(), name_split[1:]))
                    ret_str = name_split[0] + "".join(ret_list)
                    element_list[i]["name"] = ret_str
            temp_json["params"] = element_list
            param_str = ""
            param_str_swift = ""
            for param in temp_json["params"]:
                param_str += f"{param['origin']}:${param['name']},"
                param_str_swift += f"{param['origin']}:\({param['name']}),"
            temp_json["kt_log"] = f"Log.d(\"StatisticUtil\", \"action:{action},{param_str[:-1]}\")"
        data.append(temp_json)

    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    kt_content = """package sing.lagufun.social.live.utils

import com.flat.feature_common_base.statistics.FlatStatisticsProxy

/**
 * Created by Ti on {{current_time}}
 */
object StatisticUtil {
{% for content in data %}
    /**
     * {{content.annotation_header}}
    {% if content.params %}
        {% for param in content.params %}
        * @param {{ param.name }} {{ param.comment }}
        {% endfor %}
    {% endif %}
     * @param highPriority 是否高优先级
     */
    public fun {{content.func_name}}(
    {% if content.params %}
        {% for param in content.params %}
        {{param.name}}: String = "",
        {% endfor %}
    {% endif %}
        highPriority: Boolean = false
    ) {
        {{content.kt_log}}
        val priority: Int = when (highPriority) {
            true -> 1
            else -> 0
        }
        StatisticsProxy.getReporter("{{ content.action }}")
    {% if content.params %}
        {% for param in content.params %}
            .put("{{ param.origin }}", {{ param.name }})
        {% endfor %}
    {% endif %}
            .setPriority(priority)
            .report()
    }
{% endfor %}
}
"""
    swift_content = """//  Created by Ti on {{current_time}}.

import UIKit
import FLStatistic

@objc public class StatisticUtil: NSObject {    
{% for content in data %}
    /// {{ content.annotation_header }}
    /// - Parameters:
    {% if content.params  %}
        {% for param in content.params %}
    ///   - {{ param.name }}: {{ param.comment  }}
        {% endfor %}
    {% endif %}
    ///   - poriority: 优先级
    @objc public static func {{ content.func_name }}(
    {% if content.params %}
        {% for param in content.params %}
        {{ param.name }}: Any?, 
        {% endfor %}
    {% endif %}
        poriority: FLStatistcPriority = .Normal) {
        var params: [String: Any] = [:]
    {% if content.params %}
        {% for param in content.params %}
        params["{{ param.origin }}"] = {{ param.name }}
        {% endfor %}
    {% endif %}
        {{ content.swift_log }}
        FLStatistic.report(event: "{{ content.action }}", params: params, priority: poriority);
    }
{% endfor %}
}
"""
    kt_template = jinja2.Template(kt_content, trim_blocks=True)
    _text = kt_template.render(data=data, current_time=current_time)
    swift_template = jinja2.Template(swift_content, trim_blocks=True)
    swift_text = swift_template.render(data=data, current_time=current_time)
    # todo: 需要安装ktlint：https://ktlint.github.io/
    # todo: 需要安装swiftlint：https://github.com/realm/SwiftLint/blob/master/README_CN.md
    if os.path.isdir(save_path):
        save_path_full = os.path.join(save_path, "StatisticUtil.kt")
        swift_path_full = os.path.join(save_path, "StatisticUtil.swift")
        with open(save_path_full, "w") as pf:
            pf.write(_text)
        with open(swift_path_full, "w") as pf:
            pf.write(swift_text)
        os.system("swiftlint lint --format --autocorrect " + swift_path_full)
        current_path = os.getcwd()
        os.chdir(save_path)
        os.system("ktlint -F \"*.kt\"")
        os.chdir(current_path)


if __name__ == '__main__':
    path = sys.argv[1]
    generate_template_code(path, os.path.join(os.getcwd(), "result"))
