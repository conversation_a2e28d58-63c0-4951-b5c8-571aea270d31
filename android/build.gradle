buildscript {
    ext {
        kotlin_version = '1.9.23'
        resguard_version = "0.1.10"
        booster_version = "4.16.3"
        bytex_version = "0.3.0"
    }
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://maven.scijava.org/content/repositories/public/' }
        maven {
            url "http://**************:8081/repository/maven-public/"
            allowInsecureProtocol = true
        }
        maven {
            url "http://maven-social.flatincbr.com:8083/repository/maven-public/"
            allowInsecureProtocol = true
        }
        maven { url "https://artifact.bytedance.com/repository/byteX/" }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://artifact.bytedance.com/repository/byteX/" }
        maven { url 'https://raw.githubusercontent.com/martinloren/AabResGuard/mvn-repo' }
    }


    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.entertain.sns:app-joint-plugin:2.0.3'
        classpath 'com.google.gms:google-services:4.3.10'
        classpath 'com.entertain.sns:re-pack:2.0.1'
        classpath "com.bytedance.android:aabresguard-plugin:$resguard_version"
        classpath "com.bytedance.android.byteX:base-plugin:$bytex_version"
        classpath "com.bytedance.android.byteX:shrink-r-plugin:$bytex_version"
        classpath "com.bytedance.android.byteX:const-inline-plugin:$bytex_version"
        classpath "com.bytedance.android.byteX:field-assign-opt-plugin:$bytex_version"
//        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
    }
}



allprojects {
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://maven.scijava.org/content/repositories/public/' }
        maven {
            url "http://**************:8081/repository/maven-public/"
            allowInsecureProtocol = true
        }
        maven {
            url "http://maven-social.flatincbr.com:8083/repository/maven-public/"
            allowInsecureProtocol = true
        }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://maven.rongcloud.cn/repository/maven-releases/" }
    }
}

ext {
    // get flutter build param --dart-define key-value map
    def dartEnvVars = new HashMap()

    if (project.hasProperty('dart-defines')) {
        dartEnvVars = project.property('dart-defines')
                .split(',')
                .collectEntries { entry ->
                    def byteArray = Base64.getDecoder().decode(entry)
                    def decodedString = new String(byteArray)
                    def pair = decodedString.split('=')
                    [(pair.first()): pair.last()]
                }
    }

    def buildEnv = dartEnvVars['build.env']
    if (buildEnv == null) {
        buildEnv = 'test'
    }
    isDebugEnv = buildEnv == 'test'
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
