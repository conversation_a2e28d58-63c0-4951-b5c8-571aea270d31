package com.winker.app

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.net.Uri
import android.os.Looper
import android.view.ViewTreeObserver
import com.entertain.sns.cloudconfig.publish.CloudConfig
import com.entertain.sns.common.utils.GsonUtils
import com.entertain.sns.common.utils.ISPService
import com.entertain.sns.nettier.publish.response.ResponseEntry
import com.flat.feature_common_base.utils.PLog
import com.flat.library_service.channel.ChannelManager
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.gyf.immersionbar.ktx.immersionBar
import com.winker.app.biz.pay.PayHandler
import com.winker.app.biz.capture.CaptureObserver
import com.winker.app.biz.channel.FlutterChannelHelper
import com.winker.app.biz.gp_upgrade.GpInAppUpgrade
import com.winker.app.biz.lan.AppLanHandler
import com.winker.app.flutter.EventChannelHelper
import com.winker.module.api.PushActionOper
import com.winker.module.api.ReportApi
import com.winker.module.common.K_START_PARAMS
import com.winker.module.common.pref.SharedPreferencesUtils
import com.winker.module.common.statistic.StatisticUtil
import com.winker.module.net.getApi
import com.winker.module.push.model.PushModel
import com.winker.module.service.eventchannel.ISPEventChannel
import com.winker.upgrade.UpdateVersionPlugin
import com.tencent.bugly.crashreport.BuglyLog
import com.winker.app.game.new.NewGameViewFactory
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * Created by Sven on 16/07/2021
 */

enum class StartType(val value: String) {
    DESK("desk"),
    PUSH("push"),
    SHARE("share"),
}

class BoostActivity : FlutterActivity() {

    companion object {
        var startType = StartType.DESK.value
        const val NEW_GAME_VIEW_TYPE = "plugins.room/webgame"
        const val TAG = "BoostActivity"
    }

    private var eventChannelHelper: EventChannelHelper? = null
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        flutterEngine.platformViewsController
                .registry
                .registerViewFactory(NEW_GAME_VIEW_TYPE, NewGameViewFactory())

        eventChannelHelper = EventChannelHelper(flutterEngine.dartExecutor.binaryMessenger)
        ISPService.getService(ISPEventChannel::class.java)
            .setCallback(eventChannelHelper)
    }

    override fun detachFromFlutterEngine() {
        if (mHadDetach) {
            return
        }
        mHadDetach = true
        super.detachFromFlutterEngine()
        eventChannelHelper?.detach()
        PLog.i(TAG, "detachFromFlutterEngine")
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(AppLanHandler.replaceContextLocale(newBase))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        AppInitHelper.initFirstActivity(application, this)

        Looper.myQueue().addIdleHandler {
            AppInitHelper.initLowOnMain()
            false
        }

        AppInitHelper.initLowPriorityTask()

        immersionBar { statusBarColor(android.R.color.transparent) }
        CaptureObserver.init(contentResolver)
        flutterEngine?.plugins?.add(UpdateVersionPlugin())
        AppInitHelper.runAfterFlutter(this)

        setPushData(intent)
        handleDynamicLink(intent)

        window.decorView.viewTreeObserver.addOnGlobalLayoutListener(mGlobalLayoutListener)
    }

    private var mHadDetach = false
    override fun onDestroy() {
        PLog.i(TAG, "onDestroy")
        BuglyLog.i(TAG, "onDestroy start")
        CaptureObserver.unregister()
        detachFromFlutterEngine()

        destroyEngine()
        super.onDestroy()

        window.decorView.viewTreeObserver.removeOnGlobalLayoutListener(mGlobalLayoutListener)
        AppInitHelper.exitApp()
        BuglyLog.i(TAG, "onDestroy end")
    }

    private fun destroyEngine() {
        val destroyEngine = CloudConfig.getFunction("dev", "flutter").getString("destroy_engine")
        if (destroyEngine != "1") {
            PLog.i(TAG, "destroyEngine")
            flutterEngine?.platformViewsController?.onDetachedFromJNI()
            flutterEngine?.destroy()
        }
    }

    override fun onStart() {
        super.onStart()
        if (!hadBegin && lunchEnTime <= 0L) {
            hadBegin = true
            lunchEnTime = System.currentTimeMillis()
            val duration = lunchEnTime - lunchBeginTime
            if (duration in 1..10000) {
                PLog.i(TAG, "launch duration: $duration")
                BuglyLog.i(TAG, "launch duration: $duration")
                StatisticUtil.reportAppStartup(duration = "$duration")
                com.entertain.sns.perfreport.PerfReport.withTech().reportFtAppStartup(duration)
            }
        }

        CaptureObserver.register()
    }

    override fun onResume() {
        super.onResume()
        FlutterChannelHelper.setFlutterActivity(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        GpInAppUpgrade.checkCancel(requestCode, resultCode)
        PayHandler.onActivityResult(requestCode, resultCode, data)
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onStop() {
        PLog.i(TAG, "onStop")
        super.onStop()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setPushData(intent)
        handleDynamicLink(intent)
    }

    private fun handleDynamicLink(intent: Intent) {
        intent.data?.let {
            PLog.i("DeepLink", "handleDeepLink uri-> $it")
            val jump = it.getQueryParameter("jump")
            PLog.i("DeepLink", "jump-> $jump")
            if (!jump.isNullOrEmpty()) {
                setDynamicLink(jump)
            }
        }

//        FirebaseDynamicLinks.getInstance()
//            .getDynamicLink(intent)
//            .addOnSuccessListener(
//                this
//            ) { pendingDynamicLinkData ->
//                pendingDynamicLinkData?.link?.let {
//                    PLog.i("DeepLink", "uri-> $it")
//                    val jump = it.getQueryParameter("jump")
//                    if (!jump.isNullOrEmpty()) {
//                        setDynamicLink(jump)
//                    }
//                }
//            }
    }

    private fun setDynamicLink(jump: String) {
        startType = StartType.SHARE.value
        PLog.i("Push", "jump")
        addStartParams(mapOf(Pair("jump", jump)))
    }

    private fun setPushData(pushIntent: Intent?) {
        pushIntent?.let {
            val pushData = it.getStringExtra("Signaling")

            PLog.i("Push", "pushData-> $pushData")
            if (pushData?.isNotEmpty() == true) {
                PLog.i("Push", "pushData")
                addStartParams(mapOf(Pair("pushData", pushData)))
                startType = StartType.PUSH.value
                return
            }

            var messageId = it.getStringExtra(PushModel.MESSAGE_ID)
                ?: it.getStringExtra(PushModel.MI_MESSAGE_ID)

            // 兼容小米推送参数解析
            if (it.hasExtra(PushModel.MI_PUSH_MSG) && messageId?.isEmpty() == true) {
                messageId = it.getStringExtra(PushModel.MI_MESSAGE_ID)
            }

            if (messageId?.isNotEmpty() == true) {
                var model: PushModel? = null

                val title = it.getStringExtra(PushModel.TITLE) ?: model?.title
                val body = it.getStringExtra(PushModel.BODY) ?: model?.body
                val deeplink = it.getStringExtra(PushModel.DEEPLINK) ?: model?.deeplink
                val serType = it.getStringExtra(PushModel.SER_TYPE) ?: model?.serType
                val logExt = it.getStringExtra(PushModel.LOG_EXT) ?: model?.logExt
                val notifyBegin = it.getStringExtra(PushModel.NOTIFY_BEGIN) ?: model?.notifyBegin
                val notifyEnd = it.getStringExtra(PushModel.NOTIFY_END) ?: model?.notifyEnd
                val sendType = it.getStringExtra(PushModel.SEND_TYPE) ?: model?.sendType
                val itemId = it.getStringExtra(PushModel.ITEM_ID) ?: model?.itemId

                val params = mapOf(
                    Pair(PushModel.TITLE, title ?: ""),
                    Pair(PushModel.BODY, body ?: ""),
                    Pair(PushModel.DEEPLINK, deeplink ?: ""),
                    Pair(PushModel.SER_TYPE, serType ?: ""),
                    Pair(PushModel.LOG_EXT, logExt ?: ""),
                    Pair(PushModel.MESSAGE_ID, messageId ?: ""),
                    Pair(PushModel.NOTIFY_BEGIN, notifyBegin ?: ""),
                    Pair(PushModel.NOTIFY_END, notifyEnd ?: ""),
                    Pair(PushModel.SEND_TYPE, sendType ?: ""),
                    Pair(PushModel.ITEM_ID, itemId ?: "")
                )

                startType = StartType.PUSH.value

                addStartParams(params)

                StatisticUtil.reportFleetsPushClick(
                    sendType = sendType ?: "",
                    serType = serType ?: "",
                    logExt = logExt ?: "",
                    messageId = messageId,
                    title = title ?: "",
                    deeplink = deeplink ?: "",
                    itemId = itemId ?: "",
                    notifyBegin = notifyBegin ?: "",
                    notifyEnd = notifyEnd ?: ""
                )

                getApi(ReportApi::class.java).pushReport(messageId, PushActionOper.CLICK.value)
                    .enqueue(object : Callback<ResponseEntry<Any>> {
                        override fun onResponse(
                            call: Call<ResponseEntry<Any>>,
                            response: Response<ResponseEntry<Any>>
                        ) {
                            PLog.i("pushReport", "success")
                        }

                        override fun onFailure(call: Call<ResponseEntry<Any>>, t: Throwable) {
                            PLog.e("pushReport", "failure", t)
                        }
                    })
                return
            }

            intent.extras?.keySet()?.let { keys ->
                val args = mutableMapOf<String, Any>()
                for (key in keys) {
                    intent.extras?.get(key)?.let { value ->
                        if (value is String) {
                            args[key] = value
                        }
                    }
                }

                PLog.i("Push", "other")
                addStartParams(args)
                return
            }
        }
    }

    var lastStartParams: Map<String, Any>? = null
    private fun addStartParams(params: Map<String, Any>) {
        if (lastStartParams == params) {
            return
        }
        lastStartParams = params
        PLog.i("Push", "addStartParams-> $params")
        ChannelManager.instance.invokeMethod("launch.onArgsUpdate", params, null)
        val json = GsonUtils.toJson(params)
        SharedPreferencesUtils.put(K_START_PARAMS, json)
    }

    private var mWindowHeight = 0
    private val mGlobalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener =
        ViewTreeObserver.OnGlobalLayoutListener {
            val r = Rect()
            // 获取当前窗口实际的可见区域
            window.decorView.getWindowVisibleDisplayFrame(r)
            val height: Int = r.height()
            if (mWindowHeight == 0) {
                // 一般情况下，这是原始的窗口高度
                mWindowHeight = height
            } else {
                if (mWindowHeight != height) {
                    // / 显示键盘
                    // 两次窗口高度相减，就是软键盘高度
//                    if (mNavBarHeight == null) mNavBarHeight = getNavigationBarHeight(context)
                    val softKeyboardHeight =
                        mWindowHeight - height
                    EventChannelHelper.updateSoftKeyboard(true, softKeyboardHeight)
                } else {
                    // / 隐藏键盘
                    EventChannelHelper.updateSoftKeyboard(false, 0)
                }
            }
        }
}
