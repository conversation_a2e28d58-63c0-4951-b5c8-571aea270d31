package com.winker.app

import com.entertain.sns.appjoint.core.ServiceProvider
import com.winker.module.common.pref.SharedPreferencesUtils
import com.winker.module.common.K_ENV_CONFIG
import com.winker.module.config.ISPConfig

@ServiceProvider
class ConfigImpl : ISPConfig {

    override fun getConfigType(): String = BuildConfig.BUILD_ENV

    override fun isTestConfig(): <PERSON><PERSON>an {
        if (BuildConfig.TEST_ENV != true) {
            return false
        }
        var config = SharedPreferencesUtils.getInt(K_ENV_CONFIG, -1)
        if (config == 1) {
            return true
        }
        if (config == 2) {
            return false
        }
        return BuildConfig.TEST_ENV
    }

    override fun isPing(): <PERSON><PERSON>an {
        return BuildConfig.BUILD_PING
    }

    override fun setTestConfig(config: Int): Unit {
        SharedPreferencesUtils.put(K_ENV_CONFIG, config)
    }

    override fun getTestConfig(): Int {
        return SharedPreferencesUtils.getInt(K_ENV_CONFIG, -1)
    }
}