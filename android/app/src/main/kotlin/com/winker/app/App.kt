package com.winker.app

import android.content.Context
import android.os.Build
import androidx.multidex.MultiDex
import com.entertain.sns.appjoint.core.AppSpec
import com.entertain.sns.common.utils.AndroidUtils
import com.entertain.sns.nettier.NetworkManager
import com.flat.feature_common_base.utils.PLog
import com.knightboot.spwaitkiller.SpWaitKiller
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.bugly.crashreport.CrashReport.CrashHandleCallback
import com.winker.app.biz.channel.FlutterChannelHelper
import com.winker.module.common.K_APP_CRASH
import com.winker.module.common.pref.SharedPreferencesUtils
import com.winker.module.common.util.DeviceUtil
import io.flutter.app.FlutterApplication
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 * Created by <PERSON> on 22/06/2021
 */
@AppSpec
class App : FlutterApplication() {

    companion object {
        private const val TAG = "App"
    }

    override fun onCreate() {
        appContext = this
        super.onCreate()
        val isMain = isMainProcess()
        if (isMain) {
            lunchBeginTime = System.currentTimeMillis()
        }

        /// SP ANR解决方案
        SpWaitKiller.builder(this).build().work()

        SharedPreferencesUtils.init(this)
        initCrashReport()

        FlutterChannelHelper.setupChannelHandler(isMain)
        AppInitHelper.init(this, isMain)
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    private fun isMainProcess(): Boolean {
        return applicationContext.packageName == AndroidUtils.getCurrentProcessName(applicationContext)
    }

    private fun initCrashReport() {
        val strategy = CrashReport.UserStrategy(this)
        strategy.setCrashHandleCallback(crashHandleCallback)
        strategy.isUploadProcess = isMainProcess()
        strategy.deviceModel = Build.MODEL
        CrashReport.initCrashReport(applicationContext, strategy)
    }

    private val crashHandleCallback = object : CrashHandleCallback() {
        override fun onCrashHandleStart(
            p0: Int,
            p1: String?,
            p2: String?,
            p3: String?
        ): MutableMap<String, String> {

            PLog.e("BuglyCrash", "type -> $p0, msg -> $p2")
            PLog.e("BuglyCrash", "errorStack -> $p3")
            DeviceUtil.memoryInfo();

            val now = Date()
            val df = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            val mod = NetworkManager.getPublicParams()["mod"] ?: ""
            val did = NetworkManager.getPublicParams()["did"] ?: ""
            SharedPreferencesUtils.put(K_APP_CRASH, "1_${df.format(now)}_${mod}_crashType:${p0}_$did")
            val map: LinkedHashMap<String, String> = LinkedHashMap<String, String>()
            map["time"] = "${df.format(now)}"
            map["crashType"] = "$p0"
            map["errorType"] = p1 ?: ""
            map["errorMessage"] = p2 ?: ""
            map["errorStack"] = p3 ?: ""
            return map
        }
    }
}

/**
 * 全局applicationContext
 */
lateinit var appContext: Context

// 启动结束时间
var lunchEnTime: Long = 0

// 启动开始时间
var lunchBeginTime: Long = 0

var hadBegin = false
