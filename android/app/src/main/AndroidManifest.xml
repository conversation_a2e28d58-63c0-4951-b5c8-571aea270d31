<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.winker.app"
    >

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="MediaStore.Images.Media.INTERNAL_CONTENT_URI" />
    <uses-permission android:name="MediaStore.Images.Media.EXTERNAL_CONTENT_URI" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />


    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-permission android:name="com.android.vending.BILLING" />

    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />

    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
        <!--        <intent>-->
        <!--            <action android:name="android.intent.action.DIAL" />-->
        <!--            <data android:scheme="tel" />-->
        <!--        </intent>-->
        <!--        <intent>-->
        <!--            <action android:name="android.intent.action.SENDTO" />-->
        <!--            <data android:scheme="smsto" />-->
        <!--        </intent>-->
        <!--        <intent>-->
        <!--            <action android:name="android.intent.action.SEND" />-->
        <!--            <data android:mimeType="*/*" />-->
        <!--        </intent>-->
    </queries>

    <application
        android:name=".App"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        tools:node="merge"
        tools:replace="android:label">
        <activity
            android:name=".BoostActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme"
            android:taskAffinity = "winker.chat.task"
            android:windowSoftInputMode="adjustResize">

            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/LaunchTheme" />

            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />

            <!--  华为appid -->
<!--            <meta-data-->
<!--                android:name="com.huawei.hms.client.appid"-->
<!--                android:value="@string/huawei_app_id" />-->

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/conversation/"
                    android:scheme="rong" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/conversationlist"
                    android:scheme="rong" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/push_message"
                    android:scheme="rong" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="winker.chat"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="*.winker.chat"
                    android:scheme="http" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="winker.chat"
                    android:scheme="winker" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="@string/firebase_dynamic" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- fcm通知栏icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/notification_icon" />

        <!-- bugly -->
        <meta-data
            android:name="BUGLY_APPID"
            android:value="@string/bugly_appid" />
        <meta-data
            android:name="BUGLY_APP_VERSION"
            android:value="${buglyAppVersion}" />
        <meta-data
            android:name="BUGLY_ENABLE_DEBUG"
            android:value="false" />

        <!-- FB配置 -->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter><action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="@string/facebook_share_provider"
            android:exported="true" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider" />
        </provider>

        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="true" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="true" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="false" />

        <service
            android:name=".biz.push.MateFCMService"
            android:stopWithTask="false"
            android:directBootAware="false"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

<!--        <service-->
<!--            android:name=".biz.push.MateHMSService"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        <service-->
<!--            android:name="com.ishumei.smantifraud.IServiceImp"-->
<!--            android:isolatedProcess="true" />-->

        <!-- 本地通知 -->
        <receiver
            android:name=".biz.local_push.LocalPushReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.winker.local_push_action" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".biz.local_push.PullPushReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.winker.pull_push_action" />
            </intent-filter>
        </receiver>


        <receiver
            android:name=".biz.push.ThirdPushRegReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="io.rong.push.intent.THIRD_PARTY_PUSH_STATE" />
            </intent-filter>
        </receiver>

    </application>
</manifest>
