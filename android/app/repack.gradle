apply plugin: 'com.bytedance.repack'
repackConfigs {
    logEnable true //开启会增加编译时长
    enableInDebug true
    // 需要扫描的库名
    prefix = ["com.heflash", "com.flat", "com.flatfish", "com.nemo.library", "com.nemo.feature", "com.entertain.sns", "com.paymentgateway"]
    renamePath = [
            "com.flatfish"      : "com.winker",
            "com.heflash"       : "com.winker",
            "com.flat"          : "com.winker",
            "com.entertain.sns" : "com.winker",
            "com.paymentgateway": "com.winker",
//            "com.entertain.sns.common": "com.hala.common",
//            "com.entertain.sns.nettier": "com.hala.nettier",
//            "com.entertain.sns.statistics": "com.hala.statistics",
//            "com.entertain.sns.activation": "com.hala.activation",
//            "com.entertain.sns.channel": "com.hala.channel",
//            "com.entertain.sns.channel_gp": "com.hala.channel_gp",
//            "com.entertain.sns.channel_kochava": "com.hala.channel_kochava",
//            "com.entertain.sns.channel_walle": "com.hala.channel_walle",
//            "com.entertain.sns.channel_dsp": "com.hala.channel_dsp",
    ]
//    classNameMap = []
}