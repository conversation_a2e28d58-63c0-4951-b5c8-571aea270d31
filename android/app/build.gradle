apply from: 'tools.gradle'

def localProperties = loadProperties('local.properties')

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.gms.google-services'
apply from: 'common.gradle'
apply from: 'bytex.gradle'
apply from: 'appjoint.gradle'
apply from: 'repack.gradle'
//apply plugin: 'com.huawei.agconnect'

android {

    namespace "com.winker.app"

    compileSdk 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    // get flutter build param --dart-define key-value map
    def dartEnvVars = new HashMap()

    if (project.hasProperty('dart-defines')) {
        dartEnvVars = project.property('dart-defines')
                .split(',')
                .collectEntries { entry ->
                    def byteArray = Base64.getDecoder().decode(entry)
                    def decodedString = new String(byteArray)
                    def pair = decodedString.split('=')
                    [(pair.first()): pair.last()]
                }
    }

    def buildEnv = dartEnvVars['build.env']
    if (buildEnv == null) {
        buildEnv = 'test'
    }
    def buildFrom = dartEnvVars['build.from']
    if (buildFrom == null) {
        buildFrom = 'local'
    }

    // 是否主播包
    def buildPing = dartEnvVars['build.ping']
    if (buildPing == null) {
        buildPing = '0'
    }


    defaultConfig {

        def appVerCode = flutterVersionCode.toInteger()
        def appVerName = flutterVersionName

        minSdkVersion 23
        targetSdk 34
        versionCode appVerCode
        versionName appVerName

        resConfigs configs.languages
        flavorDimensions "PACKAGE_TYPE", "PUBLISH_TYPE"

        manifestPlaceholders =
                [buglyAppVersion: "$appVerName.${String.format("%03d", appVerCode % 1000)}"]

        println("Building environment: $buildEnv")
        buildConfigField "String", "BUILD_ENV", "\"$buildEnv\""
        buildConfigField "Boolean", "BUILD_PING", "${buildPing == '1'}"
        buildConfigField "Boolean", "TEST_ENV", "${buildEnv != 'product'}"


//        resValue "String", "facebook_app_id", fbAppid
//        resValue "String", "fb_login_protocol_scheme", fbScheme

        multiDexEnabled true
    }

    signingConfigs {
        final keyPath = "../"
        debug {
            println("signing debug")
            def keyProperties = loadProperties('key-debug.properties')

            Boolean isPing = false
            gradle.startParameter.getTaskNames().each { task ->
                println("task $task")
                if (task.toLowerCase().contains("ping")) {
                    println("signing debug ping")
                    keyProperties = loadProperties('key-ping.properties')
                }
            }

            storeFile file(keyPath + keyProperties.getProperty('ks_name'))
            storePassword keyProperties.getProperty('ks_pass')
            keyAlias keyProperties.getProperty('ks_key_alias')
            keyPassword keyProperties.getProperty('key_pass')
            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            Boolean isPing = false
            gradle.startParameter.getTaskNames().each { task ->
                println("task $task")
                if (task.toLowerCase().contains("ping")) {
                    println("signing pingpingpingping")
                    isPing = true
                }
            }

            println("signing release $isPing")
            // 打包机上的签名路径
            def certProperties = loadProperties('cert-path.properties')
            def keyRootDir = new File(certProperties['path'])
            def keyPropertiesFile = new File(keyRootDir, 'key-release.properties')
            def keyProperties
            def keyStoreFile
            def storeNameKey = 'ks_name'
            if (isPing) {
                /// 线下包
                println("Warning: build release with ping KeyStore")
                keyProperties = loadProperties('key-ping.properties')
                keyStoreFile = file(keyPath + keyProperties[storeNameKey])
            } else if (keyPropertiesFile.exists()) {
                // 如果是打包机环境，读取打包机的配置
                keyProperties = loadProperties(keyPropertiesFile.absolutePath, true)
                keyStoreFile = new File(keyRootDir, keyProperties[storeNameKey])
            } else {
                // 本地打包使用debug的签名
                println("Warning: build release with debug KeyStore")
                keyProperties = loadProperties('key-debug.properties')
                keyStoreFile = file(keyPath + keyProperties[storeNameKey])
            }
            storeFile keyStoreFile
            storePassword keyProperties['ks_pass']
            keyAlias keyProperties['ks_key_alias']
            keyPassword keyProperties['key_pass']
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }

            multiDexKeepFile file('multidex-config.txt')
            multiDexKeepProguard file('multidex-config.pro')

        }
        profile {

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }

            multiDexKeepFile file('multidex-config.txt')
            multiDexKeepProguard file('multidex-config.pro')

        }
        release {
            signingConfig signingConfigs.release

            minifyEnabled true
            shrinkResources false

            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            multiDexKeepFile file('multidex-config.txt')
            multiDexKeepProguard file('multidex-config.pro')

        }
    }

    productFlavors {
        base {
            dimension "PACKAGE_TYPE"
            applicationId "com.winkertest.chat.android"
        }

        pingBase {
            dimension "PACKAGE_TYPE"
            applicationId "com.ping.global.test"
        }

        product {
            dimension "PACKAGE_TYPE"
            applicationId "com.winker.chat.android"
        }

        pingProduct {
            dimension "PACKAGE_TYPE"
            applicationId "com.ping.global"
        }

        apk {
            dimension "PUBLISH_TYPE"
            buildConfigField "Boolean", "GP_VERSION", "false"

            ndk {
                abiFilters "armeabi-v7a"
            }
        }
        gp {
            dimension "PUBLISH_TYPE"
            buildConfigField "Boolean", "GP_VERSION", "true"

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }

        }
        hw {
            dimension "PUBLISH_TYPE"
            buildConfigField "Boolean", "GP_VERSION", "false"

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }

        }
        strm {
            dimension "PUBLISH_TYPE"
            buildConfigField "Boolean", "GP_VERSION", "false"

            ndk {
                abiFilters "armeabi-v7a"
            }
        }
    }

    // 本地不展示product类型的任务避免错误执行
    if (buildFrom != "server") {
        println("Build from local, disable product flavor")
        variantFilter { variant ->
            def target = variant.flavors*.name.find { name ->
                return name.toLowerCase().startsWith("product")
            }
            if (target != null) {
                setIgnore(true)
            } else {
                setIgnore(false)
            }
        }
    }

    aaptOptions {
        // 去掉声网多余的头文件
        ignoreAssetsPattern "!*.h"
    }

    packagingOptions {
        // 去掉无用的proto文件
        exclude 'google/protobuf/**'
        exclude 'kotlin/**/*.kotlin_metadata'
        exclude 'META-INF/*.kotlin_module'
        exclude 'META-INF/*.version'

        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'

        jniLibs {
            useLegacyPackaging = true
        }

    }

    compileOptions {
        // Flag to enable support for the new language APIs
        // coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    dependenciesInfo {
        // Disables dependency metadata when building APKs.
        includeInApk = false
        // Disables dependency metadata when building Android App Bundles.
        includeInBundle = false
    }

}

allprojects {
    configurations.all {
        resolutionStrategy.force 'com.entertain.sns:offline-webview:1.0.29'
        resolutionStrategy.force 'com.entertain.sns:offline-resource:1.0.32'
        resolutionStrategy.force 'com.entertain.sns:activation:1.1.8'
        resolutionStrategy.force 'com.entertain.sns:channel:1.2.1'
        resolutionStrategy.force 'com.entertain.sns:channel-kochava:1.1.8'
        resolutionStrategy.force 'com.entertain.sns:channel-gp:1.1.0'
        resolutionStrategy.force 'com.entertain.sns:channel-walle:1.1.0'
        resolutionStrategy.force 'com.entertain.sns:common:2.0.9.1'
        resolutionStrategy.force 'com.entertain.sns:app-joint-core:1.0.5'
        resolutionStrategy.force 'com.entertain.sns:nettier:1.3.7'
        resolutionStrategy.force 'com.entertain.sns:stat:1.2.3'
        resolutionStrategy.force 'com.tencent:mmkv-static:1.3.2'
        resolutionStrategy.force 'com.tencent:mmkv:1.3.2'
        resolutionStrategy.force 'com.kochava.tracker:tracker:5.4.0'
    }
}

flutter {
    source '../..'
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation project(":module")

    // For AGP 7.4+
//    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    implementation 'com.github.bumptech.glide:glide:4.14.2'
    kapt 'com.github.bumptech.glide:compiler:4.12.0'

    // 沉浸式状态栏
    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'
    implementation 'com.gyf.immersionbar:immersionbar-ktx:3.0.0'

    implementation 'com.tencent:mmkv-static:1.2.10'
    modules {
        module("com.tencent:mmkv") {
            replacedBy("com.tencent:mmkv-static", "Using mmkv-static for flutter")
        }
    }

    // gms
    implementation("com.google.android.gms:play-services-gcm:17.0.0") {
        force = true
    }

    // upgrade
    apkImplementation 'com.github.jenly1314.AppUpdater:app-updater:1.2.0'
    // upgrade
    hwImplementation 'com.github.jenly1314.AppUpdater:app-updater:1.2.0'

//    implementation 'com.google.android.play:core-ktx:1.8.1'

    // 分包
    implementation 'androidx.multidex:multidex:2.0.1'

    // 华为push
//    implementation ('com.huawei.hms:push:6.9.0.300') {
//        exclude module: "availableupdate"
//    }

    // 支付sdk
    implementation 'com.social.paysdk:gp:1.2.1'
    api "com.android.billingclient:billing:6.0.1"
    api "com.android.billingclient:billing-ktx:6.0.1"


//    implementation 'com.github.canardoux:flutter_sound_core:9.2.13'
//    implementation 'com.dooboolab.fluttersound:flutter_sound:9.2.13'
//    implementation project(':flutter_sound')

    // 内存泄露检测工具
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'

    /// SP ANR解决方案
    implementation 'io.github.knight-zxw:spwaitkiller:0.0.8'
}

import com.android.build.gradle.internal.tasks.FinalizeBundleTask

//打包命名处理
android.applicationVariants.all { variant ->
    variant.outputs.all { output ->
        //正式版还是测试版
        String typeName = buildType.name
        //渠道名称
        String flavorsName = variant.flavorName
        //应用名称
        String appName = "app"

        //AAB文件只处理正式版和baseGp
        if (typeName == "release" && flavorsName == "baseGp") {
            String bundleFinalizeTaskName = "signBaseGpReleaseBundle"
            tasks.named(bundleFinalizeTaskName, FinalizeBundleTask.class) {
                File bundleFile = finalBundleFile.asFile.get()
                doLast {
                    String aabFileName = "${appName}-${flavorsName}-${typeName}.aab"

                    println "bundle编译结束=$aabFileName"
                    project.copy {
                        from("${bundleFile}")
                        rename("${bundleFile.name}", "${aabFileName}")
                        into("${bundleFile.getParent()}")
                    }
                }
            }
        }
    }
}