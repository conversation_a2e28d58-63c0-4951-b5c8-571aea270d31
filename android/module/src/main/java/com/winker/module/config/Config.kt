package com.winker.module.config

import com.entertain.sns.common.utils.ISPService
import com.flat.feature_common_base.utils.PLog

val DEBUG by lazy {
    ISPService.getService(ISPConfig::class.java).isTestConfig()
}

/**
 * Created by <PERSON> on 14/07/2021
 */
class Config private constructor(
    val anm: String,
    val netHost: String,
    val enableDataEncode: Boolean,
    val domains: Set<String>,
    val activationHost: String,
    val getDidPath: String,
    val installPath: String,
    val signinPath: String,
    val statisticHost: String,
    val statisticPath: String,
    val configHost: String,
    val configPath: String,
    val configFetchInterval: Long, // 拉取间隔 秒
    val agoraAppId: String, // 声网appid
    val kochavaGuId: String,
    val netReportPath: String,
    val payCallbackPath: String, // 支付回调
    val payNetHost: String,
    val payStatisticHost: String, // 支付打点
    val payLogPath: String, // 支付日志
) {

    companion object {
        const val TAG = "Config"
        val instance by lazy(LazyThreadSafetyMode.PUBLICATION) {

            if (DEBUG) {
                PLog.i(TAG, "debug config")
                debugConfig()
            } else {
                PLog.i(TAG, "release config")
                releaseConfig()
            }
        }

        private fun debugConfig(): Config {
            return Config(
                anm = "nova",
                netHost = "http://api-dev.winker.chat/",
                enableDataEncode = true,
                domains = setOf(""),
                activationHost = "http://*************:8001/",
                getDidPath = "api/dev/device/get_did2",
                installPath = "api/dev/device/install2",
                signinPath = "api/dev/device/signin2",
                statisticHost = "http://*************:8009/",
                statisticPath = "api/log/addlogs",
                configHost = "http://*************:8001/",
                configPath = "api/appconfig/config/get",
                configFetchInterval = 30 * 60,
                agoraAppId = "********************************",
                kochavaGuId = "",
                netReportPath = "api/parser/tick/front/6456",
                payCallbackPath = "",
                payNetHost = "http://*************:8001/",
                payStatisticHost = "http://*************:8009/",
                payLogPath = "api/log/log"
            )
        }

        private fun releaseConfig(): Config {
            return Config(
                anm = "nova",
                netHost = "https://api-nova.winker.chat/",
                enableDataEncode = true,
                domains = setOf(""),
                activationHost = "https://api.winker.chat/",
                getDidPath = "api/dev/device/get_did2",
                installPath = "api/dev/device/install2",
                signinPath = "api/dev/device/signin2",
                statisticHost = "https://l.winker.chat/",
                statisticPath = "api/log/addlogs",
                configHost = "https://api.winker.chat/",
                configPath = "api/appconfig/config/get",
                configFetchInterval = 30 * 60,
                agoraAppId = "********************************",
                kochavaGuId = "kowinker-ndroid-fxp8c7w",
                netReportPath = "api/parser/tick/front/6456",
                payCallbackPath = "",
                payNetHost = "https://api.winker.chat/",
                payStatisticHost = "https://l.winker.chat/",
                payLogPath = "api/log/log"
            )
        }
    }
}