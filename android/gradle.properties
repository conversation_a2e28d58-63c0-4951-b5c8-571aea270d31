org.gradle.jvmargs=-Xmx4096m -XX:+HeapDumpOnOutOfMemoryError \
  --add-exports=java.base/sun.nio.ch=ALL-UNNAMED \
  --add-opens=java.base/java.lang=ALL-UNNAMED \
  --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
  --add-opens=java.base/java.io=ALL-UNNAMED \
  --add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED

android.useAndroidX=true
android.enableJetifier=true
#android.enableR8=true

#解压so，方便检查Native崩溃
#android.bundle.enableUncompressedNativeLibs=false

android.experimental.legacyTransform.forceNonIncremental=true

android.disableAutomaticComponentCreation=true

kotlin.setJvmTargetFromAndroidCompileOptions=true

android.precompileDependenciesResources=false

supportAppJoint=com.heflash,com.library

#bytex相关开关
bytex.forbidUseLenientMutationDuringGetArtifact=true
bytex.enableDuplicateClassCheck=false

https.protocols=TLSv1.2,TLSv1.1,SSLv3
systemProp.https.protocols=TLSv1.2,TLSv1.1,SSLv3

java.version=11

android.warningsAsErrors=false