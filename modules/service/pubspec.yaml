name: service
description: 混编服务层
publish_to: 'none'

environment:
  sdk: ">=3.4.0 <4.0.0"
  flutter: ">=3.0.0"

dependency_overrides:
#  analyzer: ^1.7.2
#  scrollable_positioned_list: 0.3.8
#  url_launcher_android: ^6.3.3
  uuid: ^4.4.0
  http: 1.1.0
  protobuf:
    path: ../../packages/protobuf-dart
  intl: 0.18.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get: ^4.6.6
  # 第三方
  protobuf: 3.1.0
  path: ^1.9.0
  path_provider: ^2.1.3
  svgaplayer_flutter: ^2.2.0
  flutter_svga_player:
    hosted:
      name: flutter_svga_player
      url: http://pub.flatincbr.com:4000
    version: ^0.0.4
  lottie: ^3.1.2
  flutter_share: ^2.0.0

  mmkv: ^1.3.9

  flutter_cache_manager: ^3.1.2

  rxdart: ^0.27.7
  pedantic: ^1.11.1
  flutter_image_compress: ^2.3.0
  # < jdk17 version
  connectivity_plus: ^5.0.2
  permission_handler: ^11.3.1
  tuple: ^2.0.2
  azlistview_plus: 3.0.0
  url_launcher: ^6.3.0
  url_launcher_ios: ^6.3.2
  flutter_archive: ^6.0.3
  image: ^4.2.0
  flutter_keyboard_visibility: ^6.0.0
  cached_network_image: ^3.3.1
  # nil
  nil: ^1.1.1

  # sql
  floor: ^1.5.0

  image_picker:
    hosted:
      name: image_picker
      url: http://pub.flatincbr.com:4000
    version: ^1.1.2

  video_player_web: ^2.3.1

  equatable: ^2.0.5
  dio: ^5.3.2

  webview_flutter: ^3.0.4

  location:
    path: ../../packages/flutter_location/location

  # 融云
  rongcloud_im_wrapper_plugin:
    hosted:
      name: rongcloud_im_wrapper_plugin
      url: http://pub.flatincbr.com:4000
    version: 5.12.1

    # 分享
  flutter_social_content_share:
    hosted:
      name: flutter_social_content_share
      url: http://pub.flatincbr.com:4000
    version: ^0.0.5

  # 画板
  flutter_drawing_board:
    hosted:
      name: flutter_drawing_board
      url: http://pub.flatincbr.com:4000
    version: ^0.0.2

  flutter_vap:
    hosted:
      name: flutter_vap
      url: http://pub.flatincbr.com:4000
    version: ^0.1.4

  # 公司库
  feature_widgets_nullsafety:
    hosted:
      name: feature_widgets_nullsafety
      url: http://pub.flatincbr.com:4000
    version: ^2.0.5

  library_base_nullsafety:
    hosted:
      name: library_base_nullsafety
      url: http://pub.flatincbr.com:4000
    version: ^2.0.8

  feature_flat_base:
    hosted:
      name: feature_flat_base
      url: http://pub.flatincbr.com:4000
    version: ^5.2.5

  library_service:
    hosted:
      name: library_service
      url: http://pub.flatincbr.com:4000
    version: 2.2.1

  library_router:
    hosted:
      name: library_router
      url: http://pub.flatincbr.com:4000
    version: ^1.0.6

  feature_auth:
    hosted:
      name: feature_auth
      url: http://pub.flatincbr.com:4000
    version: ^1.0.3+1

  feature_auth_facebook:
    hosted:
      name: feature_auth_facebook
      url: http://pub.flatincbr.com:4000
    version: ^1.0.4

  feature_auth_google:
    hosted:
      name: feature_auth_google
      url: http://pub.flatincbr.com:4000
    version: ^1.0.3

  feature_auth_apple:
    hosted:
      name: feature_auth_apple
      url: http://pub.flatincbr.com:4000
    version: ^1.0.3

  bugly_crashreport:
    hosted:
      name: bugly_crashreport
      url: http://pub.flatincbr.com:4000
    version: ^1.0.7

  audio_player:
    hosted:
      name: audio_player
      url: http://pub.flatincbr.com:4000
    version: 0.20.3

  device_info_plus_platform_interface: ^7.0.0

  wechat_assets_picker: ^9.5.0

  manage_calendar_events:
    hosted:
      name: manage_calendar_events
      url: http://pub.flatincbr.com:4000
    version: ^0.0.4

  golden_eye:
    hosted:
      name: golden_eye
      url: http://pub.flatincbr.com:4000
    version: ^0.1.11

  flutter_svg: ^2.0.10+1

dev_dependencies:
  flutter_lints: any
  build_runner: any
  lint: 2.0.0
  file: ^6.1.4
  floor_generator: ^1.1.0
  effective_dart: ^1.3.2
  json_serializable: any

flutter:

flutter_intl:
  enabled: true