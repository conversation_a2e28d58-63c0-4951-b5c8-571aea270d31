import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:service/service.dart';

double softKeyboardHeight(BuildContext context) {
  return SoftKeyboardObserver.instance.getKeyBoardHeight(context);
}

class SoftKeyboardObserver {
  static final SoftKeyboardObserver instance = SoftKeyboardObserver._();
  double _keyboardHeight = 0;
  SoftKeyboardObserver._() {
    if (Platform.isAndroid) {
      rxUtil.observer<int>(SoftKeyBoardEvent.change).listen(_onKeyBoardChange);
    }
  }
  void _onKeyBoardChange(int height) {
    _keyboardHeight = height.dp;
  }

  double getKeyBoardHeight(BuildContext context) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    return bottom != 0 ? bottom : _keyboardHeight;
  }
}

class SoftKeyboardVisibilityBuilder extends StatelessWidget {
  const SoftKeyboardVisibilityBuilder({Key? key, required this.builder})
      : super(key: key);

  final Widget Function(BuildContext, bool isKeyboardVisible) builder;

  @override
  Widget build(BuildContext context) {
    final bottom = softKeyboardHeight(context);
    if (Platform.isAndroid) {
      return StreamBuilder<bool>(
        stream: rxUtil
            .observer<int>(SoftKeyBoardEvent.change)
            .map((event) => event > 0),
        initialData: bottom > 0,
        builder: (context, snapshot) {
          return builder(context, snapshot.data ?? false);
        },
      );
    } else {
      return KeyboardVisibilityBuilder(builder: builder);
    }
  }
}

class SoftKeyboardWrapper extends StatefulWidget {
  final Widget? child;

  /// 检查是否需要响应键盘变化
  final bool Function()? checkNeedChange;

  final Widget Function(double keyboardHeight)? widgetBuilder;

  SoftKeyboardWrapper({this.child, this.checkNeedChange, this.widgetBuilder});

  @override
  State<StatefulWidget> createState() {
    return _SoftKeyboardWrapperState();
  }
}

class _SoftKeyboardWrapperState extends State<SoftKeyboardWrapper> {
  double _keyboardHeight = 0;

  StreamSubscription? _subscription;

  @override
  void initState() {
    super.initState();
    if (Platform.isAndroid) {
      _subscription = rxUtil
          .observer<int>(SoftKeyBoardEvent.change)
          .listen(_onKeyBoardChange);
    }
  }

  void _onKeyBoardChange(int height) {
    if (mounted) {
      /// 高度大于0的情况下，键盘顶起时检查是否需要顶起
      if (height > 0 &&
          widget.checkNeedChange != null &&
          !widget.checkNeedChange!.call()) {
        Log.d("widget.checkNeedChange", "false");
        return;
      }
      Log.d("widget.checkNeedChange", "true");
      setState(() {
        _keyboardHeight = height.dp;
      });
    }
  }

  @override
  void dispose() {
    if (Platform.isAndroid) {
      _subscription?.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.viewInsets;
    Log.d("SoftKeyboard", "bottom -> ${padding.bottom}");
    Log.d("SoftKeyboard", "height -> $_keyboardHeight");
    double bottom = padding.bottom != 0 ? padding.bottom : _keyboardHeight;
    if (widget.checkNeedChange != null && !widget.checkNeedChange!.call()) {
      Log.d("SoftKeyboardWrapper checkNeedChange", "false");
      bottom = 0;
    }
    if (widget.child != null) {
      return Padding(
        child: widget.child,
        padding: EdgeInsets.only(bottom: bottom),
      );
    } else if (widget.widgetBuilder != null) {
      return widget.widgetBuilder!.call(bottom);
    }

    return SizedBox();
  }
}

/// 点击空白处可以触发键盘收起
class SoftKeyBoardBlank extends StatelessWidget {
  final Widget child;

  SoftKeyBoardBlank({required this.child});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: child,
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
    );
  }
}