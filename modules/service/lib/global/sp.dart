import 'package:library_base_nullsafety/library_base.dart';

/// SharedPreferences模块名
const String spEasterEgg = 'easter_egg';
const String spLocale = 'locale';
const String spNotify = 'notify';
const String spUser = 'user';
const String spMoment = 'moment';
const String spEmoji = 'emoji';
const String spDisturb = 'disturb';
const String spGift = 'gift';
const String spBottle = 'bottle';
const String spChatMode = 'chat_mode';
const String spVoiceVerify = 'voice_verify';
const String spLive = 'live';
const String spFinance = 'finance';
const String spChat = 'chat';
const String spTask = 'task';
const String spRemoteConfig = 'remote_config';
const String spIntimacy = 'intimacy';
const String spWeb = 'web';
const String spSearch = 'search';

/// 网络缓存模块，存储信息的大小控制在1MB以内
const String spNetCache = 'net_cache';

/// 针对会话的sp
String spChatTargetId(String targetId) {
  return 'chat_$targetId';
}

String spChatGiftGuideTargetId(String targetId) {
  return 'chat_gift_shake_$targetId';
}

/// 是否展示聊天付费提示
String spChatDepletionAlertTargetId(String targetId) {
  return 'chat_depletion_alert_$targetId';
}

/// 是否展示打招呼信息
String spChatShowGreetingTargetId(String targetId) {
  return 'chat_greeting_$targetId';
}

/// 是否展示聊天引导
const String spShowChatGuide = 'show_chat_guide';

final globalSp = Preferences.newInstance('global');

/// ---------------------- [globalSp] 的key ------------------------
/// apns 点击key
const spKeyApnsMsg = 'K_APNS_MSG';

/// dynamicLink 动态链接
const spKeyDynamicLink = 'K_DYNAMIC_LINK';

/// 是否展示过社区规范
const spKeyHasShownCommunityRules = 'K_HAS_SHOWN_COMMUNITY_RULES';

/// 是否展示过阅后即焚提示
const spKeyHasShownDestructHint = 'K_HAS_SHOWN_DESTRUCT_HINT';

/// 忽略版本更新
const spKeyIgnoreUpgradeDate = 'K_IGNORE_UPGRADE_TIME_';

/// 是否点击过评分卡片的评星
const spKeyHasClickEvaluateStar = 'K_HAS_CLICK_EVALUATE_STAR';

/// 评分卡片被关闭次数
const spKeyEvaluateCancelTime = 'K_EVALUATE_CANCEL_TIME';

/// 进入聊天界面是否已经申请过外部存储权限
const spKeyHasRequestStorageInChat = 'K_HAS_REQUESTED_STORAGE_IN_CHAT';

/// 是否展示过截图警告
const spKeyHasShownScreenshotAlert = 'K_HAS_SHOWN_SCREENSHOT_ALERT';

/// 是否弹过放弃匹配确认弹窗
const spKeyHasConfirmedLeavingMatch = 'K_HAS_CONFIRMED_LEAVING_MATCH';

/// 哪种登录成功
const spKeyLoginSuccessType = 'K_LOGIN_SUCCESS_TYPE';

/// 当天第一次登陆时间
const spKeyTodayFirstTime = 'K_TODAY_FIRSTTIME';

/// 是否已经启动过
const spKeyHadStartApp = 'K_HAD_START_APP';
/// 第一次启动app时间
const spKeyStartAppFirstTime = 'K_START_APP_FIRST_TIME';

/// 是否获取过相册
const spKeyHadRequestPhoto = 'K_HAD_REQUEST_PHOTO';

/// 启动参数
const spKeyStartParams = 'K_START_PARAMS';

/// 崩溃
const spKeyAppCrash = 'K_APP_CRASH';

/// 骚扰限制
const spKeyDisturb = 'K_DISTURB_';

/// 进入聊天界面是否已经触摸屏幕
const spKeyHasActionInChat = 'K_HAS_ACTION_IN_CHAT';

/// 是否检查过渠道
const spKeyHasCheckChannelTask = 'K_HAS_CHECK_CHANNEL_TASK';

/// 上报过2d时间
const spKeyHadReportLaunch2D = 'K_HAD_REPORT_LAUNCH_2D';

/// 漂流瓶设置页引导
const spKeyWishBottleRule = 'K_WISH_BOTTLE_RULE';

/// 漂流瓶扔瓶子引导
const spKeyWishBottleThrow = 'K_WISH_BOTTLE_THROW';

/// 漂流瓶捞瓶子引导
const spKeyWishBottleSalvage = 'K_WISH_BOTTLE_SALVAGE';

/// 漂流瓶捞设置引导
const spKeyWishBottleSetting = 'K_WISH_BOTTLE_SETTING';

/// 上报过fb post [bool]
const spKeyFbPostSuccess = 'K_FB_POST_SUCCESS';

/// 上报fb sendMsg [bool]
const spKeyFbSendMsgSuccess = 'K_FB_SEND_MSG_SUCCESS';
/// 上报 [int]
const spKeyFbSendMsgDailySuccess = 'K_FB_SEND_MSG_DAILY_SUCCESS';

/// 上报fb 匹配成功 [bool]
const spKeyFbMatchSuccess = 'K_FB_MATCH_SUCCESS';
/// 上报 [int]
const spKeyFbMatchDailySuccess = 'K_FB_MATCH_DAILY_SUCCESS';

/// 上报fb 被匹配弹出chat [bool]
const spKeyFbMatchReceiveChat = 'K_FB_MATCH_RECEIVE_CHAT';
/// 上报 [int]
const spKeyFbMatchReceiveDailyChat = 'K_FB_MATCH_RECEIVE_DAILY_CHAT';

/// 上报fb 关注成功 [bool]
const spKeyFbFollowUserSuccess = 'K_FB_FOLLOW_USER_SUCCESS';

/// 上报fb 声音认证提交
const spKeyVerifyRecordSubmit = 'K_FB_VERIFY_RECORD_SUBMIT';

/// 上报firebase 性别注册 [bool]
const spKeyFirebaseHadRegister = 'K_HAD_FIREBASE_REGISTER';

/// 上报firebase female 发送信息 [bool]
const spKeyFirebaseFemaleHadSendMsg = 'K_HAD_FIREBASE_FEMALE_HAD_SEND_MSG';
/// 上报firebase 发送信息 daily [int]
const spKeyFirebaseFemaleDailySendMsg = 'K_HAD_FIREBASE_FEMALE_DAILY_SEND_MSG';
/// 上报firebase female 匹配 [bool]
const spKeyFirebaseFemaleHadMatchPage = 'K_HAD_FIREBASE_FEMALE_HAD_MATCH_PAGE';

/// 设置页展示头像new
const spDisplayInSetting = 'new_avatar_to_photo_in_setting';

/// bottomSheet展示头像new
const spDisplayInAction = 'new_avatar_to_photo_in_sheet';

/// 是否已经设置过啊语
const spHadSetLan = 'K_HAD_SET_LAN';

/// 直播间音乐的音量
const spLiveRoomMusicVolume = 'K_LIVE_ROOM_MUSIC_VOLUME';
/// 直播间播放选中的音乐id
const spLiveRoomPlayingMusicId = 'K_LIVE_ROOM_PLAYING_MUSIC_ID';

const spNewHandRewardLiveRoomSpKey = "newHandRewardLiveRoomSpKey";

/// 当天转盘隐藏
const spDateHideTurntable = '_K_LIVE_DATE_HIDE_TURNTABLE';

/// 当天是否已经抽过奖
const spDateHasUseTurntable = '_K_LIVE_DATE_USE_TURNTABLE';

/// 管理的房间是有有follower新增红点
const spRoomFollowerRedPoint = '_K_ROOM_FOLLOWER_RED_POINT';

/// 自己的房间是否有bonus红点
const spRoomBonusRedPoint = '_K_ROOM_BONUS_RED_POINT';

/// 房间麦位模式红点提示
const spRoomMicModeRedPoint = '_K_ROOM_MIC_MODE_RED_POINT';

/// 今天是否调用过bonus接口，一个账号一天只调用一次
const spRoomBonusExecuteInterface = '_K_ROOM_BONUS_EXECUTE_INTERFACE';

/// 房间信息请求栏是否有红点，以上红点存在并不影响其消失，所以需要另存sp
const spRoomRedPoint = '_K_ROOM_RED_POINT';

/// 用户总人数
const spTotalUserCount = '_K_TOTAL_USER_COUNT';

/// 分享弹窗评论下标
const spShareCommentIndex = '_K_SHARE_COMMENT_INDEX';

/// 加入幸运转盘是否提示扣费
const spRemindJoinLuckyWheel = '_K_REMIND_JOIN_LUCKY_WHEEL';

/// 已充值过的skuId
const spSkuIdListHasRecharge = 'K_SKU_ID_LIST_HAS_RECHARGE';

/// 选择的支付方式
const spRechargePayType = 'K_RECHARGE_PAY_TYPE';

/// 选择支付方式的引导
const spRechargePayGuidShow = 'K_RECHARGE_PAY_GUID_SHOW';

/// 已展示过求婚信函svga
const spProposeHasShowSvg = 'K_PROPOSE_HAS_SHOW_SVG';

/// 视频音量大小
const spRoomVideoVolume = 'K_ROOM_VIDEO_VOLUME';

/// 是否显示im消息弹窗
const spImMsgNotificationShow = 'K_IM_MSG_NOTIFICATION_SHOW';

/// 不再弹出红包弹窗 [bool]
const spKeyHideAutoLuckyBag = 'K_HIDE_AUTO_LUCKY_BAG';

/// 是否已展示过cp房主持人规则弹窗 [bool]
const spKeyHasShowHostAnnounceRule = 'K_HAS_SHOW_HOST_ANNOUNCE_RULE';

/// 是否已展示过特殊活动banner引导
const spKeyHasShowGuideEventBanner = 'K_HAS_SHOW_GUIDE_EVENT_BANNER';

/// 是否已展示过特殊活动订阅引导
const spKeyHasShowGuideEventSubscribe = 'K_HAS_SHOW_GUIDE_EVENT_SUBSCRIBE';

/// 群聊送礼上一次选中uid
const spKeyGroupChatGiftLastUid = 'K_GROUP_CHAT_GIFT_LAST_UID_';

/// 是否展示过房间任务引导
const spKeyHasShowRoomTaskGuideBubble = '_K_HAS_SHOW_ROOM_TASK_GUIDE_BUBBLE';

/// 红包状态
const spKeyStatusLuckyBag = 'K_STATUS_LUCKY_BAG_';

/// 启动流程推荐关注引导展示次数
const spKeyTimesShowRecommend = '_K_TIMES_SHOW_RECOMMEND_';

/// 直播间耳返
const spLiveRoomEarEnable = 'K_LIVE_ROOM_EAR_ENABLE';

/// 直播间耳返音量
const spLiveRoomEarVolume = 'K_LIVE_ROOM_EAR_VOLUME';

/// 混响类型
const spLiveRoomReverbTag = 'K_LIVE_ROOM_REVERB_TAG';

/// 房主首次开播过
const spAdminFirstStartRoom = 'K_ADMIN_FIRST_START_ROOM_';

/// 跨房pk设置引导
const spCrossPkSettingGuide = 'K_LIVE_ROOM_CROSS_PK_SETTING_GUIDE';

/// 你画我猜画板操作记录
const spKeyDrawGuessAction = "KEY_DRAW_GUESS_ACTION";
const spKeyDrawGuessStrokeWidth = "KEY_DRAW_GUESS_STROKE_WIDTH";
const spKeyDrawGuessColor = "KEY_DRAW_GUESS_COLOR";
const spKeyDrawGuessEraserWidth = "KEY_DRAW_GUESS_ERASER_WIDTH";

/// 任务中心的版本号
const spKeyTaskListVersion = "KEY_TASK_LIST_VERSION";

/// 位置定位用户选择的状态
const spKeyLocationPermissionStatus = "KEY_LOCATION_PERMISSION_STATUS";

/// 位置服务弹出提醒
const spKeyLocationServiceTip = "KEY_LOCATION_POP_SERVICE_TIP";

/// 位置权限弹窗提醒
const spKeyLocationPermissionTip = "KEY_LOCATION_POP_PERMISSION_TIP";

String spKeySignInClose(String uid) {
  return 'K_SIGN_IN_CLOSE_$uid';
}

String spKeyNewbieDialog(String uid) {
  return 'K_NEWBIE_DIALOG_$uid';
}

/// 蒙面玩法用户生命周期，揭面提示 , 1显示过
const spKeyMaskedUnmarkTip = 'KEY_MASKED_UNMARK_TIP';

/// 私聊猜拳底部开关状态，默认打开
/// 1.打开
/// 2.关闭
const String spKeyChatPunchSwitch = 'K_CHAT_PUNCH_SWITCH';

/// 用户发起通知权限获取后的返回结果
String spKeyNotifyPermissionStatus = 'KEY_NOTIFY_PERMISSION_STATUS';

/// 用户选择不再提示通知栏权限
const String spKeyNotifyNoRemind = 'K_NOTIFY_NO_REMIND';

const String spD1Retention = 'K_D1_RETENTION';

const String spAppOpenTime = 'K_APP_OPEN_TIME';

const String spKeyIntimacyConfig = 'KEY_INTIMACY_CONFIG';

/// cocos game 游戏初始化完成的版本号
const String spKeyCocosInitedVersion = 'K_COCOS_INITED_VERSION';
/// cocos game 游戏版本号
const String spKeyCocosGameVersion = 'K_COCOS_GAME_VERSION';

/// 活动弹窗缓存 map  id:time
const spKeyActivityDialogMap = 'K_LAST_ACTIVITY_DIALOG_MAP';

String spKeyCountryCities(String countryCode) {
  return 'K_COUNTRY_CITIES_$countryCode';
}

const String spKeyMicrophonePermissionKey = "KEY_MICROPHONE_PERMISSION_KEY";

String spKeyLiveBubbleTip(String uid) {
  return 'KEY_LIVE_BUBBLE_TIP_$uid';
}

/// 粉丝团抽奖次数引导小红点
// [roomFansRedDotTopDot]    1   顶部小红点
// [roomFansRedDotButtonDot] 2   按钮小红点
const String spKeyRoomFansDrawTimesRedDot = 'KEY_ROOM_FANS_DRAW_TIMES_RED_DOT';

String spKeyRoomJoinFansTipDialog({required String? uid, required String? rid}) {
  return 'KEY_ROOM_JOIN_FANS_TIP_DIALOG_${uid}_$rid';
}

String spKeyOpenFruitGame(String? uid) {
  return 'KEY_OPEN_FRUIT_GAME_$uid';
}

/// IM 聊天底部游戏入口
/// 1. 关闭过
String spKeyChatGameEntry(String? uid) {
  return 'KEY_CHAT_GAME_ENTRY_$uid';
}

/// 选中自动开始游戏
const spCheckAutoStartGame = 'K_CHECK_AUTO_START_GAME';

/// 今天踢人不再弹出
const spNoRemindMeAboutKickOutToday = 'K_NO_REMIND_ME_ABOUT_KICK_OUT_TODAY';

/// 搜索历史
const spKeySearchHistory = 'K_SEARCH_HISTORY';

/// 直播间音乐的播放模式
const spLiveRoomMusicPlayMode = 'K_LIVE_ROOM_MUSIC_PLAY_MODE';

/// 当前用户 是否有bc权限
const spUserHadBc = 'K_USER_HAD_BC';

/// 今天上报过每日闹铃，一个账号一天只调用一次
const spReportDayAlarm = 'K_REPORT_DAY_ALARM';

/// 是否点开 badge
const spMineClickedBadge = 'K_MINE_CLICKED_BADGE';

/// 是否点开 家族
const spMineClickedFamily = 'K_MINE_CLICKED_FAMILY';

/// 家族搜索历史
const spKeyFamilySearchHistory = 'K_FAMILY_SEARCH_HISTORY';

/// 显示过房间关注引导
const spKeyShowedRoomFollowGuide = 'K_showed_room_follow_guide';