import 'package:service/common/assets.g.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/modules/finance/model/recharge_model.dart';
import 'package:service/modules/language/language_support.dart';

/// 素材图片路径
final String materialsUrl = 'https://res.winker.chat/nova';

final String urlHome = AppConfig.instance.webBaseUrl;

final String urlHomeNew = AppConfig.instance.webBaseUrlNew;
// final String urlPrivacyPolicy = '$urlHome{lan}/privacy-policy';
// final String urlTermOfUse = '$urlHome{lan}/terms-of-use';

String get _baseUrl => debugEnv ? 'http://test-h5.winker.chat/' : urlHome;

String get _baseUrlNew => debugEnv ? 'http://test-h5.winker.chat/' : urlHomeNew;

List<String> _privacyHtml = [
  Assets.htmlPrivacyId,
  Assets.htmlPrivacyEn,
];

List<String> _termsHtml = [
  Assets.htmlTermsId,
  Assets.htmlTermsEn,
];

String get urlPrivacyPolicy {
  return _findLanListItem(_privacyHtml) ?? Assets.htmlPrivacyEn;
}

String get urlTermOfUse {
  return _findLanListItem(_termsHtml) ?? Assets.htmlTermsEn;
}

/// 主播认证页面
final String urlAnchorReview = '${urlHome}app/anchor-review';

final String urlFaqTest = 'http://test-h5.winker.chat/app/faq';
final String urlFaq = 'https://app.winker.chat/faq';

final String urlLiveCpRules = '$urlHome{lan}/blind-date-room-rule';

final String roomShareUrl = '${urlHome}room-share';

/// 真心话大冒险规则页
final String urlTruthDareRules = '$urlHome{lan}/truth-or-dare-rule';

/// 开宝箱faq
final String urlMagicBoxFaq = '$urlHome{lan}/magic-box-faq';

/// 魅力值
final String charmRuleUrls = '${_baseUrl}app/wealth-charm-intro?type=charm';

/// 财富值
final String wealthRuleUrls = '${_baseUrl}app/wealth-charm-intro?type=wealth';

const String facebookShareLink = 'https://www.facebook.com/profile.php?id=';

/// 帝国冲突游戏的介绍url
final String cocosGameHelp = '${_baseUrl}h5/activity/clash-empire-ill';

final String giftWallHelp = '${_baseUrl}app/exhibition-hall-rule';

final String familyLuckyBagHelp = '${_baseUrlNew}family-bag-rule';

String _removeExtension(String fileName) {
  int dotIndex = fileName.lastIndexOf('.');
  if (dotIndex != -1) {
    return fileName.substring(0, dotIndex);
  }
  return fileName;
}

String? _findLanListItem(List<String> list) {
  var lan = LanguageSupport.getLan();
  for (var item in list) {
    if (_removeExtension(item).endsWith(lan)) {
      return item;
    }
  }
  return null;
}

String thirdPaymentUrl(RechargeProduct product, String? from) {
  String url =
      '${_baseUrl}app/web-play?&good_id=${product.id}&sku=${product.skuId}&price_amount_micros=${product.localePriceAmountMicros}&price_currency_code=${product.localePriceCurrencyCode}';
  if (from?.isNotEmpty == true) {
    url = '$url&from=$from';
  }
  if (debugEnv) {
    url = '$url&debug=1';
  }
  url = Uri.encodeFull(url);
  return url;
}

/// 房主激励（原来存在,加入热度红包说明table）
String roomHostRewardUrl({required String? roomId}) {
  return '${_baseUrl}app/room-reward-detail?room_id=$roomId';
}

/// 热度红包说明
String roomRedPackUrl({required String? roomId}) {
  return '${_baseUrl}app/room-reward-detail?type=normal&room_id=$roomId';
}

String applyBlindDateRoomUrl() {
  return '${_baseUrl}h5/activity/matchmaker-recruitment-plan';
}

String get blindDateIntroUrl {
  return '${_baseUrl}app/blind-date-intro';
}

String get roomRankIntroUrl {
  return '${_baseUrl}app/room-rank';
}

String get roomFansClubIntoUrl {
  return '${_baseUrl}app/fan-club-intro';
}

String get roomFansClubRewardUrl {
  return '${_baseUrl}app/fan-club-reward';
}

String roomBlindBoxUrl({required String? roomId}) {
  return '${_baseUrl}h5/activity/gift-blind-box?room_id=$roomId';
}

/// 房间等级
final String urlRoomLevel = '${_baseUrlNew}voice-room-level';

/// 家族等级页规则
final String urlFamilyLevel = '${_baseUrlNew}family-level-rule';

/// 家族faq页面
final String urlFamilyFaq = '${_baseUrlNew}family-faq';
