import 'package:library_service/service/service_center.dart';
import 'package:service/modules/channel_msg/abs_channel_msg_service.dart';
import 'package:service/pb/local/pb_local.pb.dart';
import 'package:service/pb/local/pb_local_req.pb.dart';
import 'package:service/pb/local/pb_local_resp.pb.dart';

import 'c.dart';

const ENV_PRODUCT = "product";
const ENV_TEST = "test";

// index作为环境设置写入kv
enum EnvConfig {
  none,
  test, // 1
  prod, // 2
}

/// 构建的编译环境：测试/生产
const buildEnv = String.fromEnvironment("build.env", defaultValue: ENV_TEST);
const buildGray = String.fromEnvironment("build.gray", defaultValue: "0");
/// 是否主播包
const buildPing = String.fromEnvironment("build.ping", defaultValue: "0");

int? _cachedEnvTestConfig;
bool? _cachedDebugEnv;

/// 预发布
const bool grayEnv = buildGray == '1';

/// 线下包
const bool isPing = buildPing == '1';

/// 是否测试环境
bool get debugEnv {
  if (_cachedDebugEnv != null) {
    return _cachedDebugEnv!;
  }
  bool retVal;
  if (_cachedEnvTestConfig == EnvConfig.test.index) {
    retVal = true;
  } else if (_cachedEnvTestConfig == EnvConfig.prod.index) {
    retVal = false;
  } else {
    retVal = buildEnv != ENV_PRODUCT;
  }
  print('env config=$_cachedEnvTestConfig,buildEnv=$buildEnv,debugEnv=$retVal');
  _cachedDebugEnv = retVal;
  return retVal;
}

bool get isBuildTestEnv {
  return buildEnv == ENV_TEST;
}

const APP_NAME = 'Winker';

/// 隐藏游戏
final bool hideGame = true;

Future<void> initEnvConfig() async {
  if (buildEnv != ENV_PRODUCT) {
    _cachedEnvTestConfig = await getTestConfig();
  } else {
    _cachedEnvTestConfig = EnvConfig.prod.index;
  }
}

int getCachedTestConfig() {
  return _cachedEnvTestConfig!;
}

Future<int> getTestConfig() async {
  var req = PbLocalEnvConfigReq();
  req.type = 1;
  PbLocalEnvConfigResp? resp = await getService<AbsChannelMsgService>()?.send(
      PbLocalMsgType.PbLocalMsgType_ENV_CONFIG,
      req: req,
      resp: PbLocalEnvConfigResp.create());
  return resp?.config ?? -1;
}

Future<int> setTestConfig(EnvConfig config) async {
  var req = PbLocalEnvConfigReq();
  req.type = 2;
  req.config = config.index;
  PbLocalEnvConfigResp? resp = await getService<AbsChannelMsgService>()
      ?.send(PbLocalMsgType.PbLocalMsgType_ENV_CONFIG, req: req);
  return resp?.code ?? -1;
}

class AppConfig {
  /// 融云 appid
  final String rongCloudAppId;

  /// 融云海外数据中心配置
  final String rongServerUrl;

  /// http 请求base url
  final String baseUrl;

  /// 中台的baseUrl
  final String centerUrl;

  /// 前端baseUrl
  final String webBaseUrl;

  /// oss baseUrl
  final String ossBaseUrl;

  /// xiaomi appId
  final String? miAppId;

  /// xiaomi appKey
  final String? miAppKey;

  final String videoListUrl;

  /// 前端新地址 后续端内h5都替换
  final String webBaseUrlNew;

  AppConfig._({
    required this.baseUrl,
    required this.centerUrl,
    required this.rongCloudAppId,
    required this.rongServerUrl,
    required this.webBaseUrl,
    required this.webBaseUrlNew,
    required this.ossBaseUrl,
    this.miAppId,
    this.miAppKey,
    required this.videoListUrl,
  });

  static final instance = debugEnv ? createDebug() : isPing ? createPing() : createRelease();

  static AppConfig createDebug() {
    return AppConfig._(
        baseUrl: 'http://api-dev.winker.chat/api/nova-api/',
        centerUrl: 'http://47.74.180.115:8001/',
        rongCloudAppId: cryRongCloudAppId(true),
        rongServerUrl: 'nav.sg-light-edge.com',
        webBaseUrl: 'https://www.winker.chat/',
        webBaseUrlNew: 'https://app.winker.chat/',
        ossBaseUrl: '',

        /// push相关
        miAppId: "",
        miAppKey: "",
        videoListUrl: '');
  }

  static AppConfig createRelease() {
    return AppConfig._(
      baseUrl: 'https://api.winker.chat/${grayEnv ? 'api/nova-api-gray/' : 'api/nova-api/'}',
      centerUrl: 'https://api.winker.chat/',
      rongCloudAppId: cryRongCloudAppId(false),
      rongServerUrl: 'nav.sg-b-light-edge.com',
      webBaseUrl: 'https://www.winker.chat/',
      webBaseUrlNew: 'https://app.winker.chat/',
      ossBaseUrl: '',

      /// push相关
      miAppId: "",
      miAppKey: "",
      videoListUrl: '',
    );
  }

  static AppConfig createPing() {
    return AppConfig._(
      // baseUrl: 'https://api.winker.chat/${grayEnv ? 'api/nova-api-gray/' : 'api/nova-api/'}',
      baseUrl: 'https://api.ping-chat.com/api/nova-api/',
      centerUrl: 'https://api.ping-chat.com/',
      rongCloudAppId: cryRongCloudAppId(false),
      rongServerUrl: 'nav.sg-b-light-edge.com',
      webBaseUrl: 'https://www.ping-chat.com/',
      webBaseUrlNew: 'https://app.ping-chat.com/',
      ossBaseUrl: '',

      /// push相关
      miAppId: "",
      miAppKey: "",
      videoListUrl: '',
    );
  }
}
