import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';

Size boundingTextSize(
  String text,
  TextStyle style, {
  int maxLines = 2 ^ 31,
  double maxWidth = double.infinity,
  TextDirection textDirection = TextDirection.rtl,
}) {
  if (text.isEmpty) {
    return Size.zero;
  }
  try {
    final TextPainter textPainter =
        TextPainter(text: TextSpan(text: text, style: style), maxLines: maxLines, textDirection: textDirection)
          ..layout(maxWidth: maxWidth);
    return textPainter.size;
  } on Exception catch (e) {
    print('Exception -> boundingTextSize text err=$e');
    return Size(text.length * (style.fontSize ?? 0) / 2, 0);
  }
}

/// 解决啊语英语组合乱序
String formatArString(String string) {
  return '\u200e$string';
}

/// 判断含有url
bool checkUrl(String text) {
  var strRegex = r"^(https|http)?://[^\s]*";
  RegExp checkUrl = RegExp(strRegex, caseSensitive: false);
  return checkUrl.hasMatch(text);
}

/// 检查字符串是否包含特殊字符
/// 返回true表示字符串合法（不包含特殊字符），false表示包含特殊字符
bool isValidString(String input) {
  final specialChars = RegExp(r'''[`~!@#$%^&*()_+=|;:{}"'[\]<>,.?/\\-]''');
  return !specialChars.hasMatch(input);
}

/// 移除字符串中的所有特殊字符
/// 返回处理后的字符串
String removeSpecialChars(String input) {
  final specialChars = RegExp(r'''[`~!@#$%^&*()_+=|;:{}"'[\]<>,.?/\\-]''');
  return input.replaceAll(specialChars, '');
}

extension NumExtension on num {
  double get sp => toSp(this);

  ///字体大小
  double toSp(num sp) {
    return sp * 1.0 + (Platform.isIOS ? 1 : 0);
  }
}
