// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// ServiceWriterGenerator
// **************************************************************************

import 'package:library_service/service/abs_service_mapping_creator.dart';
import 'package:library_service/service/service.dart';
import 'package:service/remote_config/service/abs_nova_remote_config_service.dart';
import 'package:service/modules/hashtag/abs_hashtag_service.dart';
import 'package:service/modules/home/<USER>';
import 'package:service/modules/friend/abs_friend_service.dart';
import 'package:service/modules/upgrade/abs_upgrade_service.dart';
import 'package:service/modules/ranking/abs_ranking_service.dart';
import 'package:service/modules/moments/abs_moments_service.dart';
import 'package:service/modules/moments/abs_moments_service.dart';
import 'package:service/modules/moments/abs_post_guide_service.dart';
import 'package:service/modules/channel_msg/abs_channel_msg_service.dart';
import 'package:service/modules/ad/abs_ad_service.dart';
import 'package:service/modules/magic/abs_magic_service.dart';
import 'package:service/modules/voice_verify/abs_voice_verify_service.dart';
import 'package:service/modules/match/abs_match_service.dart';
import 'package:service/modules/notification/abs_notification_service.dart';
import 'package:service/modules/punch_game/abs_punch_game_service.dart';
import 'package:service/modules/location/abs_location_service.dart';
import 'package:service/modules/intimacy/asb_intimacy_service.dart';
import 'package:service/modules/family/abs_family_service.dart';
import 'package:service/modules/quiz/abs_quiz_service.dart';
import 'package:service/modules/easter_egg/abs_easter_egg_service.dart';
import 'package:service/modules/language/abs_language_service.dart';
import 'package:service/modules/user/service/abs_user_service.dart';
import 'package:service/modules/user/service/abs_avatar_service.dart';
import 'package:service/modules/coupon/abs_coupon_service.dart';
import 'package:service/modules/voice_call_match/abs_voice_call_match_service.dart';
import 'package:service/modules/sticker/services/abs_version_service.dart';
import 'package:service/modules/sticker/services/abs_sticker_service.dart';
import 'package:service/modules/sticker/services/abs_sticker_mic_emoji_service.dart';
import 'package:service/modules/notice/abs_notice_service.dart';
import 'package:service/modules/gift/abs_gift_service.dart';
import 'package:service/modules/social/abs_social_service.dart';
import 'package:service/modules/common/abs_common_service.dart';
import 'package:service/modules/evaluate/abs_evaluate_service.dart';
import 'package:service/modules/call/abs_call_service.dart';
import 'package:service/modules/live/room/abs_room_service.dart';
import 'package:service/modules/live/custom_turntable/abs_custom_turntable_service.dart';
import 'package:service/modules/live/chat/abs_room_chat_service.dart';
import 'package:service/modules/live/gift_award/abs_gift_award_service.dart';
import 'package:service/modules/live/room_report/abs_room_report_service.dart';
import 'package:service/modules/live/plugins/turntable_game/abs_turntable_game_service.dart';
import 'package:service/modules/live/mic_seat/abs_mic_seat_service.dart';
import 'package:service/modules/live/game/abs_new_game_service.dart';
import 'package:service/modules/live/game/abs_game_service.dart';
import 'package:service/modules/live/game/player/abs_player_service.dart';
import 'package:service/modules/live/room_guide/abs_room_guide_service.dart';
import 'package:service/modules/live/lucky_bag/abs_lucky_bag_service.dart';
import 'package:service/modules/live/room_gift/abs_room_gift_service.dart';
import 'package:service/modules/live/turntable/abs_turntable_service.dart';
import 'package:service/modules/live/event/abs_event_service.dart';
import 'package:service/modules/live/media/abs_media_service.dart';
import 'package:service/modules/voice_match/abs_voice_match_service.dart';
import 'package:service/modules/task/abs_task_service.dart';
import 'package:service/modules/ornament/abs_ornament_service.dart';
import 'package:service/modules/finance/abs_finance_service.dart';
import 'package:service/modules/im/abs_im_room_service.dart';
import 'package:service/modules/im/abs_safe_mode_service.dart';
import 'package:service/modules/im/abs_im_base_service.dart';
import 'package:service/modules/im/abs_im_function_service.dart';
import 'package:service/modules/im/abs_im_msg_service.dart';
import 'package:service/modules/im/abs_im_cache_service.dart';
import 'package:service/modules/im/abs_im_chat_service.dart';
import 'package:service/modules/mall/abs_mall_service.dart';
import 'package:service/modules/alarm/abs_alarm_service.dart';
import 'package:service/modules/account/abs_account_service.dart';
import 'package:service/modules/voice_dating/abs_voice_dating_service.dart';
import 'package:service/modules/overlay/abs_overlay_service.dart';
import 'package:service/modules/property_report/abs_property_report_service.dart';
import 'package:service/modules/bottle/abs_bottle_service.dart';
import 'package:service/modules/questions/abs_question_service.dart';
import 'package:service/modules/disturb/abs_disturb_service.dart';
import 'package:service/modules/share/abs_share_service.dart';
import 'package:service/modules/badge/abs_badge_service.dart';
import 'package:service/remote_config/service/nova_remote_config_service.dart';
import 'package:service/modules/hashtag/hashtag_service.dart';
import 'package:service/modules/home/<USER>';
import 'package:service/modules/upgrade/upgrade_service.dart';
import 'package:service/modules/friend/friend_service.dart';
import 'package:service/modules/ranking/ranking_service.dart';
import 'package:service/modules/moments/moments_service.dart';
import 'package:service/modules/moments/moments_opration_service.dart';
import 'package:service/modules/moments/post_guide_service.dart';
import 'package:service/modules/channel_msg/channel_msg_service.dart';
import 'package:service/modules/ad/ad_service.dart';
import 'package:service/modules/magic/magic_service.dart';
import 'package:service/modules/voice_verify/voice_verify_service.dart';
import 'package:service/modules/match/match_service.dart';
import 'package:service/modules/notification/notification_service.dart';
import 'package:service/modules/punch_game/punch_game_service.dart';
import 'package:service/modules/location/location_service.dart';
import 'package:service/modules/intimacy/intimacy_service.dart';
import 'package:service/modules/family/family_service.dart';
import 'package:service/modules/quiz/quiz_service.dart';
import 'package:service/modules/easter_egg/easter_egg_service.dart';
import 'package:service/modules/language/language_service.dart';
import 'package:service/modules/user/service/avatar_service.dart';
import 'package:service/modules/user/service/user_service.dart';
import 'package:service/modules/coupon/coupon_service.dart';
import 'package:service/modules/voice_call_match/voice_call_match_service.dart';
import 'package:service/modules/sticker/services/version_service.dart';
import 'package:service/modules/sticker/services/sticker_mic_emoji_service.dart';
import 'package:service/modules/sticker/services/sticker_service.dart';
import 'package:service/modules/notice/notice_service.dart';
import 'package:service/modules/gift/gift_service.dart';
import 'package:service/modules/social/social_service.dart';
import 'package:service/modules/common/common_service.dart';
import 'package:service/modules/evaluate/evaluate_service.dart';
import 'package:service/modules/call/call_service.dart';
import 'package:service/modules/live/room/room_service.dart';
import 'package:service/modules/live/custom_turntable/custom_turntable_service.dart';
import 'package:service/modules/live/chat/room_chat_service.dart';
import 'package:service/modules/live/gift_award/gift_award_service.dart';
import 'package:service/modules/live/room_report/room_report_service.dart';
import 'package:service/modules/live/plugins/turntable_game/turntable_game_service.dart';
import 'package:service/modules/live/mic_seat/mic_seat_service.dart';
import 'package:service/modules/live/game/new_game_service.dart';
import 'package:service/modules/live/game/game_service.dart';
import 'package:service/modules/live/game/player/player_service.dart';
import 'package:service/modules/live/lucky_bag/lucky_bag_service.dart';
import 'package:service/modules/live/room_guide/room_guide_service.dart';
import 'package:service/modules/live/room_gift/room_gift_service.dart';
import 'package:service/modules/live/turntable/turntable_service.dart';
import 'package:service/modules/live/event/event_service.dart';
import 'package:service/modules/live/media/media_service.dart';
import 'package:service/modules/voice_match/voice_match_service.dart';
import 'package:service/modules/task/task_service.dart';
import 'package:service/modules/ornament/ornament_service.dart';
import 'package:service/modules/finance/finance_service.dart';
import 'package:service/modules/im/safe_mode/safe_mode_service.dart';
import 'package:service/modules/im/function/im_function_service.dart';
import 'package:service/modules/im/rong_cloud/im_base_service.dart';
import 'package:service/modules/im/rong_cloud/im_cache_service.dart';
import 'package:service/modules/im/rtm/im_room_service.dart';
import 'package:service/modules/im/rong_cloud/im_chat_service.dart';
import 'package:service/modules/im/rong_cloud/im_msg_service.dart';
import 'package:service/modules/mall/mall_service.dart';
import 'package:service/modules/alarm/alarm_service.dart';
import 'package:service/modules/account/account_service.dart';
import 'package:service/modules/voice_dating/voice_dating_service.dart';
import 'package:service/modules/overlay/overlay_service.dart';
import 'package:service/modules/property_report/property_report_service.dart';
import 'package:service/modules/bottle/battle_service.dart';
import 'package:service/modules/disturb/disturb_service.dart';
import 'package:service/modules/questions/question_service.dart';
import 'package:service/modules/share/share_service.dart';
import 'package:service/modules/badge/badge_service.dart';

class O2ServiceCreator extends AbsServiceMappingCreator {
  final Map<dynamic, dynamic> _innerRouterMap = <dynamic, dynamic>{
    AbsAccountService: AccountService,
    AbsAdService: AdService,
    AbsAlarmService: AlarmService,
    AbsAvatarService: AvatarService,
    AbsBadgeService: BadgeService,
    AbsBottleService: BattleService,
    AbsCallService: CallService,
    AbsChannelMsgService: ChannelMsgService,
    AbsChatSafeModeService: ChatSafeModeService,
    AbsCommonService: CommonService,
    AbsCouponService: CouponService,
    AbsCustomTurntableService: CustomTurntableService,
    AbsDisturbService: DisturbService,
    AbsEasterEggService: EasterEggService,
    AbsEvaluateService: EvaluateService,
    AbsEventService: EventService,
    AbsFamilyService: FamilyService,
    AbsFinanceService: FinanceService,
    AbsFriendService: FriendService,
    AbsGameService: GameService,
    AbsGiftAwardService: GiftAwardService,
    AbsGiftService: GiftService,
    AbsHashtagService: HashtagService,
    AbsHomeService: HomeService,
    AbsIMBaseService: IMBaseService,
    AbsIMCacheService: IMCacheService,
    AbsIMChatService: IMChatService,
    AbsIMFunctionService: ImFunctionService,
    AbsIMMsgService: IMMsgService,
    AbsImRoomService: ImRoomService,
    AbsIntimacyService: IntimacyService,
    AbsLanguageService: LanguageService,
    AbsLocationService: LocationService,
    AbsLuckyBagService: LuckyBagService,
    AbsMagicService: MagicService,
    AbsMallService: MallService,
    AbsMatchService: MatchService,
    AbsMediaService: MediaService,
    AbsMicSeatService: MicSeatService,
    AbsMomentsOperationService: MomentsOperationService,
    AbsMomentsService: MomentsService,
    AbsNewGameService: NewGameService,
    AbsNoticeService: NoticeService,
    AbsNotificationService: NotificationService,
    AbsNovaRemoteConfigService: NovaRemoteConfigService,
    AbsOverlayService: OverlayService,
    AbsPlayerService: PlayerService,
    AbsPostGuideService: PostGuideService,
    AbsPropertyReportService: PropertyReportService,
    AbsPunchGameService: PunchGameService,
    AbsQuestionService: QuestionService,
    AbsQuizService: QuizService,
    AbsRankingService: RankingService,
    AbsRoomChatService: RoomChatService,
    AbsRoomGiftService: RoomGiftService,
    AbsRoomGuideService: RoomGuideService,
    AbsRoomReportService: RoomReportService,
    AbsRoomService: RoomService,
    AbsShareService: ShareService,
    AbsSocialService: SocialService,
    AbsStickerMicEmojiService: StickerMicEmojiService,
    AbsStickerService: StickerService,
    AbsStickerVersionService: StickerVersionService,
    AbsTaskService: TaskService,
    AbsTurntableGameService: TurntableGameService,
    AbsTurntableService: TurntableService,
    AbsUpgradeService: UpgradeService,
    AbsUserOrnamentService: UserOrnamentService,
    AbsUserService: UserService,
    AbsVoiceCallMatchService: VoiceCallMatchService,
    AbsVoiceDatingService: VoiceDatingService,
    AbsVoiceMatchService: VoiceMatchService,
    AbsVoiceVerifyService: VoiceVerifyService,
  };

  @override
  // ignore: type_annotate_public_apis
  T? createService<T extends AbsService>(abs) {
    return _findService<T>(abs);
  }

  @override
  // ignore: type_annotate_public_apis
  bool hadBinding(abs) {
    return _innerRouterMap.containsKey(abs);
  }

  T? _findService<T extends AbsService>(dynamic name) {
    final dynamic serviceClass = findPageClass(name);
    if (serviceClass != null) {
      return instanceFromClazz(serviceClass);
    } else {
      return null;
    }
  }

  T? instanceFromClazz<T extends AbsService>(Type clazz) {
    {
      switch (clazz) {
        case AccountService:
          return AccountService() as T;
        case AdService:
          return AdService() as T;
        case AlarmService:
          return AlarmService() as T;
        case AvatarService:
          return AvatarService() as T;
        case BadgeService:
          return BadgeService() as T;
        case BattleService:
          return BattleService() as T;
        case CallService:
          return CallService() as T;
        case ChannelMsgService:
          return ChannelMsgService() as T;
        case ChatSafeModeService:
          return ChatSafeModeService() as T;
        case CommonService:
          return CommonService() as T;
        case CouponService:
          return CouponService() as T;
        case CustomTurntableService:
          return CustomTurntableService() as T;
        case DisturbService:
          return DisturbService() as T;
        case EasterEggService:
          return EasterEggService() as T;
        case EvaluateService:
          return EvaluateService() as T;
        case EventService:
          return EventService() as T;
        case FamilyService:
          return FamilyService() as T;
        case FinanceService:
          return FinanceService() as T;
        case FriendService:
          return FriendService() as T;
        case GameService:
          return GameService() as T;
        case GiftAwardService:
          return GiftAwardService() as T;
        case GiftService:
          return GiftService() as T;
        case HashtagService:
          return HashtagService() as T;
        case HomeService:
          return HomeService() as T;
        case IMBaseService:
          return IMBaseService() as T;
        case IMCacheService:
          return IMCacheService() as T;
        case IMChatService:
          return IMChatService() as T;
        case ImFunctionService:
          return ImFunctionService() as T;
        case IMMsgService:
          return IMMsgService() as T;
        case ImRoomService:
          return ImRoomService() as T;
        case IntimacyService:
          return IntimacyService() as T;
        case LanguageService:
          return LanguageService() as T;
        case LocationService:
          return LocationService() as T;
        case LuckyBagService:
          return LuckyBagService() as T;
        case MagicService:
          return MagicService() as T;
        case MallService:
          return MallService() as T;
        case MatchService:
          return MatchService() as T;
        case MediaService:
          return MediaService() as T;
        case MicSeatService:
          return MicSeatService() as T;
        case MomentsOperationService:
          return MomentsOperationService() as T;
        case MomentsService:
          return MomentsService() as T;
        case NewGameService:
          return NewGameService() as T;
        case NoticeService:
          return NoticeService() as T;
        case NotificationService:
          return NotificationService() as T;
        case NovaRemoteConfigService:
          return NovaRemoteConfigService() as T;
        case OverlayService:
          return OverlayService() as T;
        case PlayerService:
          return PlayerService() as T;
        case PostGuideService:
          return PostGuideService() as T;
        case PropertyReportService:
          return PropertyReportService() as T;
        case PunchGameService:
          return PunchGameService() as T;
        case QuestionService:
          return QuestionService() as T;
        case QuizService:
          return QuizService() as T;
        case RankingService:
          return RankingService() as T;
        case RoomChatService:
          return RoomChatService() as T;
        case RoomGiftService:
          return RoomGiftService() as T;
        case RoomGuideService:
          return RoomGuideService() as T;
        case RoomReportService:
          return RoomReportService() as T;
        case RoomService:
          return RoomService() as T;
        case ShareService:
          return ShareService() as T;
        case SocialService:
          return SocialService() as T;
        case StickerMicEmojiService:
          return StickerMicEmojiService() as T;
        case StickerService:
          return StickerService() as T;
        case StickerVersionService:
          return StickerVersionService() as T;
        case TaskService:
          return TaskService() as T;
        case TurntableGameService:
          return TurntableGameService() as T;
        case TurntableService:
          return TurntableService() as T;
        case UpgradeService:
          return UpgradeService() as T;
        case UserOrnamentService:
          return UserOrnamentService() as T;
        case UserService:
          return UserService() as T;
        case VoiceCallMatchService:
          return VoiceCallMatchService() as T;
        case VoiceDatingService:
          return VoiceDatingService() as T;
        case VoiceMatchService:
          return VoiceMatchService() as T;
        case VoiceVerifyService:
          return VoiceVerifyService() as T;
        default:
          return null;
      }
    }
  }

  dynamic findPageClass(dynamic url) {
    final dynamic pageClass = _innerRouterMap[url];
    if (null != pageClass) {
      return pageClass;
    }
    return null;
  }
}
