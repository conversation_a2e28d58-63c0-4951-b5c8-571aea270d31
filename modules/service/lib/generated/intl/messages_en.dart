// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(room) =>
      "A family lucky bag was sent at ${room}, please go to the family room to grab it!";

  static String m1(other) => "About ${other} information";

  static String m2(diamond, name) =>
      "Confirm to pay ${diamond} diamonds to activate the ${name} premium?";

  static String m3(name, time) =>
      "You have successfully enabled ${name} premium, valid to ${time}";

  static String m4(name, id) => "· Agency: ${name} (ID:${id})";

  static String m5(count) => "all participants defense buff ${count}";

  static String m6(points) => "all participants ${points} points";

  static String m7(nick) => "${nick} answered the question game.";

  static String m8(days) =>
      "Are you sure you want to disband the family? Once the family is disbanded, all personal and family-related assets will be cleared. After applying for disbandment, there is a ${days}-day cooling-off period, during which you can cancel the disbandment at any time. After the ${days}-day cooling-off period ends, the family will be disbanded.";

  static String m9(num1, num2, where1, where2) =>
      "Are you sure you want to distribute a total of ${num1} diamonds in ${num2} lucky bags at ${where1} ${where2}?";

  static String m10(num) => "At least ${num} diamonds";

  static String m11(name) => "@${name} ";

  static String m12(num) => "Automatic start: ${num}s";

  static String m13(time) => "Awarded on ${time}";

  static String m14(ranking) => "Ranking:${ranking}";

  static String m15(username) => "Follow ${username} to unlock more features!";

  static String m16(num) =>
      "${Intl.plural(num, zero: '${num} day', one: '${num} day', other: '${num} days')} ago";

  static String m17(num) =>
      "${Intl.plural(num, zero: '${num} hour', one: '${num} hour', other: '${num} hours')} ago";

  static String m18(count) => "${count} minutes ago";

  static String m19(num) =>
      "${Intl.plural(num, zero: '${num} month', one: '${num} month', other: '${num} months')} ago";

  static String m20(num) =>
      "${Intl.plural(num, zero: '${num} week', one: '${num} week', other: '${num} weeks')} ago";

  static String m21(num) =>
      "${Intl.plural(num, zero: '${num} year', one: '${num} year', other: '${num} years')} ago";

  static String m22(time) => "Block time ${time}";

  static String m23(star, type) =>
      "Are you sure you want to spend ${star} coins to buy ${type}?";

  static String m24(price) =>
      "Sending gifts requires the gift box ，do you want to spend ${price} stars to purchase the gift box?";

  static String m25(amount) => "${amount} buy";

  static String m26(time) => "Call time: ${time}";

  static String m27(name) => "${name} Change chat mode";

  static String m28(count) => "Change the number of mic to ${count} seats.";

  static String m29(name) =>
      "Congratulations on unlocking the ${name} feature ! Experience it now and bring yourself closer to the other person!";

  static String m30(num) => "Still ${num} Intimacy unlock";

  static String m31(num) => "Chatting can get intimacy （+${num} ）";

  static String m32(name) => "${name} click the [BOOM]!!";

  static String m33(user) => "${user}  disabled global mute.";

  static String m34(count) => "Automatically close after ${count} s";

  static String m35(day, coins) =>
      "${coins} / ${Intl.plural(day, zero: '${day}Day', one: '${day}Day', two: '${day}Days', few: '${day}Days', other: '${day}Days')}";

  static String m36(percent) => "${percent}% complete";

  static String m37(percent) => "Profile complete ${percent}%";

  static String m38(count) =>
      "Spend ${count} diamonds to compulsory divorce. Your marriage will be over and the rings will disappear";

  static String m39(badge) => "Congrats! You have lighted up ${badge} badge";

  static String m40(familyName, position) =>
      "Congratulations on being appointed as ${familyName}’s ${position}!";

  static String m41(familyName) =>
      "Congratulations on joining the ${familyName} family, come check it out now!";

  static String m42(familyName) =>
      "Congratulations on successfully creating the ${familyName} family. You can now recruit more members. Go check it out!";

  static String m43(familyName, level) =>
      "Congratulations to ${familyName} family for upgrading to Level${level}. come check it out now!";

  static String m44(userName, giftName) =>
      "Congratulations to ${userName} on ${giftName}";

  static String m45(n, sex) =>
      "Do you want to spend ${n} diamonds to light up this gift for ${sex}?";

  static String m46(uid) => "Contact CS ${uid}";

  static String m47(content) => "${content} off";

  static String m48(num) => "Convertible Starlight:${num}";

  static String m49(days) => "${days}-day cooling-off";

  static String m50(code) =>
      "Invite Code: ${code}. I am experiencing a new social app called \'Winker\' and inviting you to join us. My invitation code can only be used once and the number of times is limited. Please join as soon as possible~To download, please click👉🏻 https://winkerchat.com/";

  static String m51(count, currency) => "${count} ${currency}";

  static String m52(name) => "The host ${name} start a new round.";

  static String m53(name) =>
      "${name} already open, come and join the mic position to participate the couple Pick.";

  static String m54(num) => "Create(${num} Diamonds)";

  static String m55(amount) => "Create with ${amount}";

  static String m56(num) =>
      "${Intl.plural(num, zero: '${num} Day', one: '${num} Day', two: '${num} Days', few: '${num} Days', other: '${num} Days')}";

  static String m57(content) => "-${content}";

  static String m58(host) => "${host}\'s room";

  static String m59(count) => "defense buff ${count}";

  static String m60(num) => "Delete (${num})";

  static String m61(num) => "${num} diamond receive successful";

  static String m62(num) => "${num} diamonds for renewal";

  static String m63(discount) => "${discount}% off";

  static String m64(num) => "Distance ${num}km";

  static String m65(user) =>
      "Do you want to transfer ${user} as the game captain?";

  static String m66(num) => "Done(${num})";

  static String m67(num) => "Draw ${num} once";

  static String m68(time) => "Duration: ${time}";

  static String m69(num) => "${num} had been entered";

  static String m70(code) => "Error occurred, please reload.(code: ${code})";

  static String m71(num) => "Subscribers(${num})";

  static String m72(num) => "${num} subscribers";

  static String m73(id, type) => "Room ID:${id}   (${type})";

  static String m74(name) => "Subject:${name}";

  static String m75(n) => "Successfully exchange ${n} diamonds.";

  static String m76(num) => "${num} Exp";

  static String m77(num) => "Exp ${num}";

  static String m78(time) => "· Will expire on ${time}";

  static String m79(date) => "Expiry date: ${date}";

  static String m80(content) => "Extra ${content}";

  static String m81(day) =>
      "Family cover s revised once a week，Please edit it after ${day}.";

  static String m82(name, position) => "${name}: ${position}";

  static String m83(num) =>
      "If there are less than ${num} family members, the lucky bag cannot be distributed.";

  static String m84(from, to) =>
      "The family name must be between ${from} and ${to} characters.";

  static String m85(name) =>
      "Come and join Winker family ${name} ! Let\'s chat and have fun!";

  static String m86(from, to) =>
      "The family slogan must be between ${from} and ${to} characters.";

  static String m87(num) => "Congrats! Your family level up to LV.${num}";

  static String m88(num) => "Your fan level has being upgraded to ${num} !";

  static String m89(username, num) =>
      "Congratulations! ${username} The fan club level has been raised to lv.${num}!";

  static String m90(sum) => "Total: ${sum} people";

  static String m91(num) => "${num} points to level up.";

  static String m92(username) => "Follow ${username} to unlock more features!";

  static String m93(num) => "Follower: ${num}";

  static String m94(num) => "${num} free chance";

  static String m95(code) => "Game error, error code:${code}";

  static String m96(name) => "Game over! The winner is ${name}";

  static String m97(gem, diamond) => "${gem} Rubies = ${diamond} diamonds.";

  static String m98(num, type) => "Get ${num} ${type} every day";

  static String m99(amount) => "Gift amount: ${amount}";

  static String m100(percent) => "+${percent}%";

  static String m101(a, b, c) => "${a} gives ${b} a ${c}";

  static String m102(a, b) => "${a} won ${b} from the room club~";

  static String m103(count) => "Maximum ${count}";

  static String m104(name, award, count) =>
      "${name} got ${award} ${count} from Treasure Box";

  static String m105(points) => "grab ${points} points";

  static String m106(count) =>
      "Charisma or Contribution must be greater than ${count}";

  static String m107(num) =>
      "You have draw for ${Intl.plural(num, zero: '${num} day', one: '${num} day', two: '${num} days', few: '${num} days', other: '${num} days')} continuously.";

  static String m108(num) =>
      "You have been together ${Intl.plural(num, zero: '${num} day', one: '${num} day', two: '${num} days', few: '${num} days', other: '${num} days')}, please cherish your relationship..";

  static String m109(sex) => "${sex} winked you";

  static String m110(words) => "Hints: ${words}";

  static String m111(time) => "Updated after ${time}";

  static String m112(time) =>
      "${Intl.plural(time, zero: '${time} hour', one: '${time} hour', two: '${time} hours', few: '${time} hours', other: '${time} hours')} ago";

  static String m113(hour) => "${hour} hours";

  static String m114(num) =>
      "${Intl.plural(num, zero: '${num} Hour', one: '${num} Hour', two: '${num} Hours', few: '${num} Hours', other: '${num} Hours')}";

  static String m115(he, him) =>
      "${he} is newbie and doesn\'t have many friends yet. Hurry up and say hello to ${him}.";

  static String m116(size) => "Pictures cannot be larger than ${size}M";

  static String m117(name) => "In ${name} chat room";

  static String m118(game) => "In ${game}";

  static String m119(content) => "+${content}";

  static String m120(remaining) => "（Still need ${remaining} diamonds to buy）";

  static String m121(score) => "Intimacy: ${score}";

  static String m122(username) =>
      "${username} invites you to join into the performer list";

  static String m123(userName, eventName) =>
      "I invite you to participate in ${userName} \'s ${eventName} party, come and join Winker to play together.";

  static String m124(username) =>
      "${username} invites you to join the game and play.";

  static String m125(name) => "Invite you join ${name}";

  static String m126(who) => "${who} invites you to take the mic";

  static String m127(num) => "Join winker:${num} days";

  static String m128(key, diamond) =>
      "${key} keys and ${diamond} diamonds open this box";

  static String m129(num) => "Keys can be gifted: ${num}";

  static String m130(num) => "Keys gifted by others: ${num}";

  static String m131(key) => "${key} keys open this box";

  static String m132(time) => "Last update time：${time}";

  static String m133(level) => "Level ${level} unlock";

  static String m134(num) => "Lighting ${num} gifts";

  static String m135(name, roomId) =>
      "We have funny conversation in here! Come to Winker to join 「${name}」${roomId}!";

  static String m136(name, roomId, pwd) =>
      "We have funny conversation in here! Come to Winker to join 「${name}」${roomId}! The room password is ${pwd}";

  static String m137(game) => "${game} failed to load please try again";

  static String m138(num) => "Loading: ${num}%";

  static String m139(num, time) =>
      "${num} lucky bag, all claimed in ${time} minutes";

  static String m140(name, count) =>
      "${name} was the lucky winner in the Lucky Wheel event and won ${count} diamonds!";

  static String m141(num) => "Lv.${num}";

  static String m142(num) => "Lv.${num} Unlock";

  static String m143(nick) =>
      "${nick} matched you from the voice test function.";

  static String m144(num) => "You can add up to a maximum of ${num} tags";

  static String m145(name) => "Do you want to use the ${name}？";

  static String m146(num) => "Please add a minimum of ${num} tags";

  static String m147(time) =>
      "${Intl.plural(time, zero: '${time} minute', one: '${time} minute', two: '${time} minutes', few: '${time} minutes', other: '${time} minutes')} ago";

  static String m148(time) =>
      "${Intl.plural(time, zero: '${time} min', one: '${time} min', two: '${time} mins', few: '${time} mins', other: '${time} mins')}";

  static String m149(count) =>
      "A minimum of ${count} participants is required to start the Lucky Wheel. Once it starts, others cannot join.";

  static String m150(sum) => "Short of last place: ${sum}";

  static String m151(count) => "${count} Posts";

  static String m152(word) => "My words: ${word}";

  static String m153(name) =>
      "${name} contains illegal content, please re-edit";

  static String m154(name) => "${name}...entered the room";

  static String m155(amount) => "${amount} new messages";

  static String m156(date) => "Next revision date:${date}";

  static String m157(position) => "No.${position} OUT!";

  static String m158(count) => "${count} days";

  static String m159(num) =>
      "${Intl.plural(num, zero: '${num} diamond', one: '${num} diamond', two: '${num} diamonds', few: '${num} diamonds', other: '${num} diamonds')}";

  static String m160(num) =>
      "${Intl.plural(num, zero: '${num} gold', one: '${num} gold', two: '${num} golds', few: '${num} golds', other: '${num} golds')}";

  static String m161(num, count) =>
      "Spend ${Intl.plural(num, zero: '${num} gold', one: '${num} gold', two: '${num} golds', few: '${num} golds', other: '${num} golds')} to increase ${Intl.plural(count, zero: '${count} time', one: '${count} time', two: '${count} times', few: '${count} times', other: '${count} times')}.";

  static String m162(num) => "Round: ${num}";

  static String m163(stars) => "${stars} stars";

  static String m164(num) => "${num} supporter";

  static String m165(num) => "${num} times";

  static String m166(count) => "Online User : ${count}";

  static String m167(name) => "Only calculated ${name} gift";

  static String m168(user) => "${user} enabled global mute.";

  static String m169(receiveNum, total) => "Opened ${receiveNum}/${total}";

  static String m170(num) => "Option (${num}/8)";

  static String m171(num) => "${num} other viewers";

  static String m172(time) => "Party duration: ${time}";

  static String m173(pw) => "Password:${pw}";

  static String m174(name) =>
      "${name}’s performance is end in advance. It\'s time for the judges to speak";

  static String m175(nick) => "${nick} wants to play question game with you.";

  static String m176(pos) => "Player ${pos}";

  static String m177(game) => "Playing ${game}";

  static String m178(from, to) => "Please enter ${from}-${to} characters";

  static String m179(points) => "${points} points";

  static String m180(num, total) => "Privileges(${num}/${total})";

  static String m181(num) => "You\'ll get it in ${num} more draws.";

  static String m182(she) => "${she} is chatting in the room~";

  static String m183(name) =>
      "Proposal has been send to your love. Please be patient while ${name} considers your request. Pray for you.";

  static String m184(name) => "I purchased ${name}，make a proposal for you";

  static String m185(her) =>
      "Congratulations！you won！you can ask ${her} a question";

  static String m186(user, rankName, order) =>
      "${user} become ${rankName} top ${order}";

  static String m187(num) => "Receiver charm exp +${num}";

  static String m188(num) => "Receiver got ${num} rubies";

  static String m189(second) => "Record at least ${second} seconds";

  static String m190(count) =>
      "You can pass by answering ${count} questions correctly.";

  static String m191(count) =>
      "😃Correct answer, just answer ${count} more questions correctly and you will pass.";

  static String m192(count) =>
      "🤖Coincidentally, there are ${count} Winker friends who share your birthday with you, and perhaps you will meet quietly at some point.";

  static String m193(gender, name) =>
      "${gender}${name}, please fill in your birthday so that we can recommend more suitable content and friends for you.";

  static String m194(name) => "${name}，please choose your gender";

  static String m195(sex) =>
      "Once gender is selected, it cannot be changed. Please confirm that your selection is ${sex} ";

  static String m196(name) => "${name}，I like this name😊.";

  static String m197(uid) =>
      "🎉Congratulations on obtaining the Winker Pass「${uid} 」. Now you can enter the Winker community and hope to make new friends here 👇🏻";

  static String m198(time) => "Remaining\n${time}";

  static String m199(name) => "Remove ${name} out family?";

  static String m200(words) => "Result: ${words}";

  static String m201(num) => "${num}golds receive successful";

  static String m202(num) => "${num} admins";

  static String m203(count) => "${count}/1000";

  static String m204(min, max) => "${min}~${max} letters/numbers";

  static String m205(sum) =>
      "Room club names are allowed to be modified once every ${sum} days";

  static String m206(id) => "ID:${id}";

  static String m207(nick) => "Your followed room ${nick} is opening!";

  static String m208(num) => "Congrats! Room upgrade to LV.${num}";

  static String m209(roomName) => "The room name was changed to:${roomName}";

  static String m210(code) =>
      "[Error Code：${code}]Please try to exit the room and re-enter.";

  static String m211(time) =>
      "Due to inactivity in the room for a long period, the room will automatically close in ${time}s.";

  static String m212(room, user) => "${room}’s ${user}";

  static String m213(round) => "Round ${round} Voting";

  static String m214(sex) => "Wink at ${sex}";

  static String m215(sex) => "You\'re already winking at ${sex}";

  static String m216(sex) =>
      "Sending winker will attract ${sex} attention, and mutual winker can start the conversation.";

  static String m217(num) =>
      "Daily greeting limit reached (${num}/Day). \nUnlock more conversations by sending a gift";

  static String m218(She, her) =>
      "${She} is so popular, give ${her} a gift to express your sincerity.";

  static String m219(sex) => "Talk to ${sex} about this moment";

  static String m220(num) =>
      "${Intl.plural(num, zero: '${num} Second', one: '${num} Second', two: '${num} Seconds', few: '${num} Seconds', other: '${num} Seconds')}";

  static String m221(count, max) => "${count}/${max}";

  static String m222(gift) => "Send a ${gift} gift to unlock the chat.";

  static String m223(name) =>
      "${name} sent a request to change your chat mode.";

  static String m224(user, goods, name) =>
      "${user} send a ${goods} ${name} to you.";

  static String m225(n) => "Send you [${n}]";

  static String m226(nick) => "${nick} sends you a question.";

  static String m227(num) => "Sender wealth exp +${num}";

  static String m228(num) =>
      "You can set this when your room level reaches Lv.${num}";

  static String m229(day) => "Sign-in for ${day} days";

  static String m230(day) =>
      "Sign-in for ${day} consecutive ${Intl.plural(day, zero: 'day', one: 'day', two: 'days', few: 'days', other: 'days')}";

  static String m231(num) => "Congratulate get ${num} golds";

  static String m232(num) => "You have signed in for ${num} days.";

  static String m233(coin) =>
      "Would you spend ${coin} golds to send this gift?";

  static String m234(money, name) =>
      "Spend ${money} diamonds to make a proposal to ${name}";

  static String m235(num) => "${num} starlight receive successful";

  static String m236(nick) => "${nick} started the question game.";

  static String m237(num) => "Stay in room for 30min (${num}/30)";

  static String m238(num) => "Stay in room for ${num}";

  static String m239(num) =>
      "Are you sure you want to spend ${num} on this item?";

  static String m240(diamonds) =>
      "Are you sure spend ${diamonds} diamonds to join the Lucky wheel. The winner will take 90% of the prize pool.";

  static String m241(count) => "the opposite room defense buff ${count}";

  static String m242(points) => "the opposite room ${points} points";

  static String m243(time) => "There are ${time} until the start";

  static String m244(num) => "${num} times left today";

  static String m245(num) =>
      "${Intl.plural(num, zero: '${num} time', one: '${num} time', two: '${num} times', few: '${num} times', other: '${num} times')} free";

  static String m246(num) =>
      "${Intl.plural(num, zero: '${num} time', one: '${num} time', two: '${num} times', few: '${num} times', other: '${num} times')}";

  static String m247(num) =>
      "${Intl.plural(num, zero: '${num}  Day', one: '${num}  Day', two: '${num}  Days', few: '${num}  Days', other: '${num}  Days')}";

  static String m248(num) =>
      "${Intl.plural(num, zero: '${num}\n Day', one: '${num}\n Day', two: '${num}\n Days', few: '${num}\n Days', other: '${num}\n Days')}";

  static String m249(index) => "Top${index}:";

  static String m250(count) => "Total: ${count}";

  static String m251(sum) => "Total：${sum} people";

  static String m252(level) =>
      "Only the room level reach LV.${level} have treasure box";

  static String m253(count) => "${count}/10";

  static String m254(user, question) =>
      "${user} punished in this round, please accept the punishment:\n${question}";

  static String m255(user) =>
      "${user} already exit the room, this round of game is over.";

  static String m256(num) => "Congrats! You won ${num} diamonds in this round.";

  static String m257(content) => "Type your answer${content}";

  static String m258(str) => "Uncover Winker profile from each other?${str}";

  static String m259(time, timeZone, timeOffset) =>
      "Update time: ${time}(${timeZone}${timeOffset})";

  static String m260(num) =>
      "Please upgrade your room to level ${num} to change the settings";

  static String m261(diamond) => "Use ${diamond}";

  static String m262(other) => "About ${other}";

  static String m263(identity, name) => "${identity} ${name} closed the video.";

  static String m264(mainUser, targetUser) =>
      "Congrats! ${mainUser} and ${targetUser} has been successfully matched, finish the couple task right now! ";

  static String m265(user) => "${user} exit the family.";

  static String m266(user, count) => "${user} gets ${count} diamonds in PK.";

  static String m267(nick) => "${nick} is opening a party!";

  static String m268(user) => "${user} is the winner in the PK.";

  static String m269(user) => "${user} join the family.";

  static String m270(user, manager) =>
      "${user} was kicked out of the family by ${manager}.";

  static String m271(identity, name) =>
      "${identity} ${name} paused the video play.";

  static String m272(mainUser, targetUser) =>
      "${mainUser} Pick the ${targetUser} in this round.";

  static String m273(identity, name) => "${identity} ${name} play the video.";

  static String m274(user) => "${user} set as Elders.";

  static String m275(user) => "${user} set as vice-family leader.";

  static String m276(user) => "${user} start a gift PK.";

  static String m277(user) => "${user} start a vote PK.";

  static String m278(username) => "${username} took a screenshot";

  static String m279(username, result) =>
      "${username} turned the turntable. The result is: ${result}";

  static String m280(text) => "Updated status: ${text}";

  static String m281(userName, familyName) =>
      "${userName} invited you to join ${familyName}, do you agree to join?";

  static String m282(userName) => "${userName} refuse your family application.";

  static String m283(userName) =>
      "${userName} rejected your application. Go check out other families. Go to Family Square!";

  static String m284(userName) =>
      "${userName} wants to join your family, please review it!";

  static String m285(date) => "Valid until ${date}";

  static String m286(diamond) => "Value: ${diamond}";

  static String m287(time, total) => "${time}/${total}";

  static String m288(num) =>
      "Contact VIP customer service to send post to promote your room activities ${num} a month";

  static String m289(num) => "VIP ${num}";

  static String m290(seconds) => "${seconds}s";

  static String m291(count) => "${count} times left today";

  static String m292(user) => "${user}, welcome to the family!";

  static String m293(coins) =>
      "You have used up free times for today, wanna spend ${coins} to fish one more time?";

  static String m294(day) => "Day ${day}";

  static String m295(who) => "${who}\'s birthday";

  static String m296(who) => "${who}\'s";

  static String m297(name) => "Winning Fruit: ${name}";

  static String m298(who, multiple, award) =>
      "${who} won ${multiple} reward, get ${award}";

  static String m299(coins) => "${coins} golds";

  static String m300(count) =>
      "You already got ${count} conversations in Winker, please give us 5 stars";

  static String m301(count) =>
      "You got ${count} friends in Winker, please give us 5 stars~";

  static String m302(name, from) => "You got ${name} from ${from}.";

  static String m303(num) =>
      "You received ${Intl.plural(num, zero: '${num} gift', one: '${num} gift', two: '${num} gifts', few: '${num} gifts', other: '${num} gifts')}.";

  static String m304(you) => "${you} had got";

  static String m305(familyName) =>
      "You have been removed from the ${familyName}. Go check out other families. Go to Family Square!";

  static String m306(level) => "Your VIP level is down to level ${level}, ";

  static String m307(followers, friends) =>
      "You have ${followers} followers and ${friends} friends in Winker waiting for you";

  static String m308(level) =>
      "Congrats! You have reached VIP level ${level}, ";

  static String m309(name) =>
      "You reject ${name} divorce apply, please cherish…";

  static String m310(count) => "your room defense buff ${count}";

  static String m311(n) => "You send [${n}]";

  static String m312(time, days) =>
      "You submitted the family disbandment application on ${time}, and it is now in the ${days}-day cooling-off period. You can cancel the disbandment at any time.";

  static String m313(sex) => "You winked ${sex}";

  static String m314(name) =>
      "Your room event ${name} did not fill in the information";

  static String m315(points) => "your room grab ${points} points";

  static String m316(points) => "your room is grabbed ${points} points";

  static String m317(points) => "your room ${points} points";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "GoToPost": MessageLookupByLibrary.simpleMessage("Go to post"),
    "JoinFanGroup": MessageLookupByLibrary.simpleMessage("Join fan group"),
    "NoAvailablePerformer": MessageLookupByLibrary.simpleMessage(
      "no available performer, please add first",
    ),
    "NoPeopleOnMic": MessageLookupByLibrary.simpleMessage("No people on mic"),
    "NoShowNextTime": MessageLookupByLibrary.simpleMessage(
      "Don\'t show next time",
    ),
    "OnlinePeopleAtParty": MessageLookupByLibrary.simpleMessage(
      "Online people at the party",
    ),
    "RebateGiftSend": MessageLookupByLibrary.simpleMessage(
      "Rebate on gift sending",
    ),
    "RebateGiftSendDesc": MessageLookupByLibrary.simpleMessage(
      "Send gifts to others and receive diamond rebate.",
    ),
    "ScreenshotShare": MessageLookupByLibrary.simpleMessage("Screenshot Share"),
    "SelectActivityProgress": MessageLookupByLibrary.simpleMessage(
      "Select activity progress",
    ),
    "Unknown": MessageLookupByLibrary.simpleMessage("Unknown"),
    "VerifyBeforeListening": MessageLookupByLibrary.simpleMessage(
      "Verify your voice before listening!",
    ),
    "VerifyUnlockCreate": MessageLookupByLibrary.simpleMessage(
      "Verify your voice to unlock chat room features!",
    ),
    "VerifyUnlockMatch": MessageLookupByLibrary.simpleMessage(
      "Verify your voice to unlock match features!",
    ),
    "WelcomeToJoinParty": MessageLookupByLibrary.simpleMessage(
      "Welcome to join our party!",
    ),
    "aFamilyLuckyBagWasSentAtRoomPleaseGo": m0,
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "aboutBadgeDesc1": MessageLookupByLibrary.simpleMessage(
      "All the badges can be worn.\nYou can wear up to 3 badges at the same time.\nIf less than 3 badges are currently being worn, badges will be worn automatically when you get them.\nEach achievement badge has four levels from low to high: Bronze - Sliver - Gold",
    ),
    "aboutBadgeDesc2": MessageLookupByLibrary.simpleMessage(
      "The honor badges are award to eligible users on the every sunday and is valid for about one week.",
    ),
    "aboutBadgeTitle1": MessageLookupByLibrary.simpleMessage("1、Wear Badges"),
    "aboutBadgeTitle2": MessageLookupByLibrary.simpleMessage("2、Honor Badges"),
    "aboutBadges": MessageLookupByLibrary.simpleMessage("About Badges"),
    "aboutMe": MessageLookupByLibrary.simpleMessage("About me"),
    "aboutOtherInformation": m1,
    "aboutUs": MessageLookupByLibrary.simpleMessage("About us"),
    "academic": MessageLookupByLibrary.simpleMessage("Academic"),
    "academicTip": MessageLookupByLibrary.simpleMessage(
      "I studied at an Indonesian university with excellent academic performance.",
    ),
    "accept": MessageLookupByLibrary.simpleMessage("Accept"),
    "accepted": MessageLookupByLibrary.simpleMessage("Accepted"),
    "accessAllTip": MessageLookupByLibrary.simpleMessage(
      "App can only access some files on the device. Go to system settings and allow app to access all files on the device.",
    ),
    "accessLimitedAssets": MessageLookupByLibrary.simpleMessage(
      "Continue with limited access",
    ),
    "accessPermission": MessageLookupByLibrary.simpleMessage(
      "Access Permission",
    ),
    "accessiblePathName": MessageLookupByLibrary.simpleMessage(
      "Accessible files",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Account"),
    "accountDataCantBeRecovered": MessageLookupByLibrary.simpleMessage(
      "Account data can not be recovered after deleted.",
    ),
    "accountDeleted": MessageLookupByLibrary.simpleMessage("Account deleted"),
    "accumulated": MessageLookupByLibrary.simpleMessage("Accumulated"),
    "accumulatedStarlight": MessageLookupByLibrary.simpleMessage(
      "Accumulated starlight",
    ),
    "accumulatedSubmit": MessageLookupByLibrary.simpleMessage(
      "Accumulated Submit",
    ),
    "achievementBadge": MessageLookupByLibrary.simpleMessage(
      "Achievement badge",
    ),
    "achievementTime": MessageLookupByLibrary.simpleMessage(
      "Achievement time:",
    ),
    "actNow": MessageLookupByLibrary.simpleMessage("Act now"),
    "activate": MessageLookupByLibrary.simpleMessage("activate"),
    "activatePremium": MessageLookupByLibrary.simpleMessage("Activate Premium"),
    "activatePremiumConfirm": m2,
    "activatedValidUntil": m3,
    "activationBroadcast": MessageLookupByLibrary.simpleMessage(
      "Activation broadcast",
    ),
    "activationBroadcastDesc": MessageLookupByLibrary.simpleMessage(
      "Attract everyone\'s attention with your Premium broadcast",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeLevel": MessageLookupByLibrary.simpleMessage("Active Level"),
    "activity": MessageLookupByLibrary.simpleMessage("Activity"),
    "activityBadge": MessageLookupByLibrary.simpleMessage("Activity Badge"),
    "activityProgress": MessageLookupByLibrary.simpleMessage(
      "Activity progress",
    ),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addCoverPhoto": MessageLookupByLibrary.simpleMessage("Add cover photo"),
    "addFriend": MessageLookupByLibrary.simpleMessage("Add friend"),
    "addMusic": MessageLookupByLibrary.simpleMessage("Add Music"),
    "addNewRingInYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Please add new ring in your backpack",
    ),
    "addPassionTip": MessageLookupByLibrary.simpleMessage(
      "Add your interest tags to attract more like-minded mates",
    ),
    "addScore": MessageLookupByLibrary.simpleMessage("Audition Score"),
    "addToCalendar": MessageLookupByLibrary.simpleMessage("Add to Calendar"),
    "addToFavorite": MessageLookupByLibrary.simpleMessage("Add to favorite"),
    "addToMyCollection": MessageLookupByLibrary.simpleMessage(
      "Add to my collection",
    ),
    "addToYourCalendar": MessageLookupByLibrary.simpleMessage(
      "Add event to Your Calendar",
    ),
    "addTopic": MessageLookupByLibrary.simpleMessage("Add Topic"),
    "addUpcomingEvent": MessageLookupByLibrary.simpleMessage(
      "Add upcoming events that you\'re subscribed in to the calendar",
    ),
    "added": MessageLookupByLibrary.simpleMessage("Added"),
    "addedSuccess": MessageLookupByLibrary.simpleMessage("Added successfully"),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "adminRoom": MessageLookupByLibrary.simpleMessage("Admin room"),
    "administrator": MessageLookupByLibrary.simpleMessage("Number of Admin"),
    "advertising": MessageLookupByLibrary.simpleMessage(
      "the user is advertising",
    ),
    "afterTurningItOnOnlyFamilyMembersCanEnterThe":
        MessageLookupByLibrary.simpleMessage(
          "After turning it on, only family members can enter the room, and non-family members who are already in the room will be kicked out of the room",
        ),
    "afterYouQuitTheFamilyYouWillClearAllThe": MessageLookupByLibrary.simpleMessage(
      "After you quit the family, you will clear all the related assets in the family. Are you sure you want to quit the family?",
    ),
    "agencyContactTitle": MessageLookupByLibrary.simpleMessage(
      "Please add WhatsApp to join the agency",
    ),
    "agencyNameId": m4,
    "agoraReconnect": MessageLookupByLibrary.simpleMessage(
      "Reconnect network, Please wait…",
    ),
    "agree": MessageLookupByLibrary.simpleMessage("Agree"),
    "agreeAlert": MessageLookupByLibrary.simpleMessage(
      "Please agree with Privacy Policy and Terms of use to login",
    ),
    "agreeAndContinue": MessageLookupByLibrary.simpleMessage(
      "Agree and continue",
    ),
    "agreePrivacyContent": MessageLookupByLibrary.simpleMessage(
      "Your info will only be used for create account. We never leak user privacy.",
    ),
    "agreeTo": MessageLookupByLibrary.simpleMessage("Agree to "),
    "agreeToTermAndPolicy": MessageLookupByLibrary.simpleMessage(
      "Agree to Terms of use and Privacy policy",
    ),
    "agreed": MessageLookupByLibrary.simpleMessage("Agreed"),
    "album": MessageLookupByLibrary.simpleMessage("Album"),
    "albumNotEnabledContent": MessageLookupByLibrary.simpleMessage(
      "Winker can\'t access photos. Allow permission for Winker to access photos in device settings.",
    ),
    "albumNotEnabledTitle": MessageLookupByLibrary.simpleMessage(
      "Album access not enabled",
    ),
    "alias": MessageLookupByLibrary.simpleMessage("Alias"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "allInRoom": MessageLookupByLibrary.simpleMessage("All in room"),
    "allMessages": MessageLookupByLibrary.simpleMessage("All Messages"),
    "allNotifications": MessageLookupByLibrary.simpleMessage(
      "All notifications",
    ),
    "allOnMic": MessageLookupByLibrary.simpleMessage("All on Mic"),
    "allParticipantsDefenseBuff": m5,
    "allParticipantsPoints": m6,
    "allRings": MessageLookupByLibrary.simpleMessage("All Rings"),
    "allRoomEvent": MessageLookupByLibrary.simpleMessage("All Room Event"),
    "allStickers": MessageLookupByLibrary.simpleMessage("All stickers"),
    "allUserInYourRoomCanHearVoice": MessageLookupByLibrary.simpleMessage(
      "All user in your room can hear the opposite voice now! Try it!",
    ),
    "allUsers": MessageLookupByLibrary.simpleMessage("All Users"),
    "allUsersCanEnterTheRoom": MessageLookupByLibrary.simpleMessage(
      "All users can enter the room?",
    ),
    "allow": MessageLookupByLibrary.simpleMessage("Allow"),
    "alreadyAdded": MessageLookupByLibrary.simpleMessage("Already added"),
    "alreadyGotLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Already got this lucky bag.",
    ),
    "alreadyInThisRoom": MessageLookupByLibrary.simpleMessage(
      "Already in this room",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("Amount:"),
    "amountToExchange": MessageLookupByLibrary.simpleMessage(
      "Enter the amount to exchange",
    ),
    "and": MessageLookupByLibrary.simpleMessage(" and "),
    "anime": MessageLookupByLibrary.simpleMessage("Anime"),
    "anniversary": MessageLookupByLibrary.simpleMessage("Anniversary"),
    "announce": MessageLookupByLibrary.simpleMessage("Announce"),
    "announceDefault": MessageLookupByLibrary.simpleMessage(
      "Welcome to the family!",
    ),
    "announceHerPick": MessageLookupByLibrary.simpleMessage(
      "Announce her pick",
    ),
    "announceHisPick": MessageLookupByLibrary.simpleMessage(
      "Announce his pick",
    ),
    "announceRule": MessageLookupByLibrary.simpleMessage(
      "1、Now you can choose to end the section and announce the results\n2、If two players pick each other up, that will be a successful match",
    ),
    "announceRuleTitle": MessageLookupByLibrary.simpleMessage("Final result"),
    "announcement": MessageLookupByLibrary.simpleMessage("Announcement"),
    "annulledCouple": MessageLookupByLibrary.simpleMessage(
      "You have annulled the couple relationship",
    ),
    "answerAreRevealed": MessageLookupByLibrary.simpleMessage(
      "Answers are revealed when you both answer",
    ),
    "answerQuestionGame": m7,
    "appLanguage": MessageLookupByLibrary.simpleMessage("Language"),
    "appName": MessageLookupByLibrary.simpleMessage("Winker"),
    "appProblems": MessageLookupByLibrary.simpleMessage("App problems"),
    "applicationFailed": MessageLookupByLibrary.simpleMessage(
      "Application failed",
    ),
    "applicationHasBeenSent": MessageLookupByLibrary.simpleMessage(
      "Application has been sent, please wait for the user process",
    ),
    "applicationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Application successful",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "applyAgencySubmit": MessageLookupByLibrary.simpleMessage(
      "The application has been submitted and is awaiting agency review",
    ),
    "applyCompulsoryDivorce": MessageLookupByLibrary.simpleMessage(
      "apply to compulsory divorce, please make your choice.",
    ),
    "applyCondition": MessageLookupByLibrary.simpleMessage("Apply condition"),
    "applyEventRewards": MessageLookupByLibrary.simpleMessage(
      "Apply event rewards",
    ),
    "applyJoinAgency": MessageLookupByLibrary.simpleMessage(
      "Apply to join the Agency",
    ),
    "applyList": MessageLookupByLibrary.simpleMessage("Apply list"),
    "applyMicList": MessageLookupByLibrary.simpleMessage(
      "Apply for the mic list",
    ),
    "applySuccess": MessageLookupByLibrary.simpleMessage("Apply successfully"),
    "applyTakeMic": MessageLookupByLibrary.simpleMessage(
      "Apply to take the mic",
    ),
    "applyTakeMicSuccess": MessageLookupByLibrary.simpleMessage(
      "apply to take the mic successfully.",
    ),
    "applyTooFrequently": MessageLookupByLibrary.simpleMessage(
      "Apply to take mic too frequently. Please try again later",
    ),
    "applyingJoin": MessageLookupByLibrary.simpleMessage("Applying to join"),
    "approvalRequiredToCreateConversation":
        MessageLookupByLibrary.simpleMessage(
          "Approval is required to create conversation",
        ),
    "approvedMic": MessageLookupByLibrary.simpleMessage(
      "Approved to take the mic",
    ),
    "areYouSureToDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete your account?",
    ),
    "areYouSureYouWantToDisbandTheFamilyOnce": m8,
    "areYouSureYouWantToDistributeATotalOf": m9,
    "areYourSureExit": MessageLookupByLibrary.simpleMessage(
      "Are you sure exit?",
    ),
    "askAQuestion": MessageLookupByLibrary.simpleMessage(
      "Ask a question for you both to answer",
    ),
    "askAnotherQuestion": MessageLookupByLibrary.simpleMessage(
      "Tap to ask another question",
    ),
    "askShareToMoment": MessageLookupByLibrary.simpleMessage(
      "Would you like to share to moment？",
    ),
    "askedForMoney": MessageLookupByLibrary.simpleMessage(
      "this user asked me for money",
    ),
    "askingNumber": MessageLookupByLibrary.simpleMessage(
      "the user is asking my number",
    ),
    "assetData": MessageLookupByLibrary.simpleMessage("Asset data"),
    "assetDataCharisma": MessageLookupByLibrary.simpleMessage(
      "Charisma: You can get charisma points when receiving gifts or items from other users:\n1 gold = 1 point \n1 diamond = 10 points \nIf you send gifts to yourself, you will only get contribution points, not charisma points.",
    ),
    "assetDataTitle": MessageLookupByLibrary.simpleMessage(
      "How to get contribution or charisma?",
    ),
    "assetDataWealth": MessageLookupByLibrary.simpleMessage(
      "Contribution: You can get contribution points by sending gifts or items to others:\n1 gold = 1 point \n1 diamond = 10 points",
    ),
    "atLeastNumDiamonds": m10,
    "atTa": MessageLookupByLibrary.simpleMessage("@"),
    "atUserName": MessageLookupByLibrary.simpleMessage("@User Name"),
    "atWho": m11,
    "attachment": MessageLookupByLibrary.simpleMessage("Attachment"),
    "audioCall": MessageLookupByLibrary.simpleMessage("Audio Call"),
    "auditPass": MessageLookupByLibrary.simpleMessage("Audit pass"),
    "auto": MessageLookupByLibrary.simpleMessage("Auto"),
    "autoRejected": MessageLookupByLibrary.simpleMessage("Auto rejected"),
    "automaticStartNum": m12,
    "automaticStartWhenFull": MessageLookupByLibrary.simpleMessage(
      "Automatic start when full",
    ),
    "automaticallyConnected": MessageLookupByLibrary.simpleMessage(
      "A voice call is automatically connected for you. Please get ready.",
    ),
    "automaticallyPass": MessageLookupByLibrary.simpleMessage(
      "Automatically pass",
    ),
    "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
    "avatarFrame": MessageLookupByLibrary.simpleMessage("Avatar frame"),
    "avatarHistory": MessageLookupByLibrary.simpleMessage("Avatar history"),
    "avatarPreview": MessageLookupByLibrary.simpleMessage("Avatar preview"),
    "awaitingResponse": MessageLookupByLibrary.simpleMessage(
      "Awaiting response...",
    ),
    "awaitingWear": MessageLookupByLibrary.simpleMessage("Awaiting wear"),
    "awardedOn": m13,
    "away": MessageLookupByLibrary.simpleMessage("Away"),
    "baby": MessageLookupByLibrary.simpleMessage("Baby"),
    "background": MessageLookupByLibrary.simpleMessage("Background"),
    "backgroundMusic": MessageLookupByLibrary.simpleMessage("Background music"),
    "backpack": MessageLookupByLibrary.simpleMessage("Backpack"),
    "backpackEmpty": MessageLookupByLibrary.simpleMessage(
      "Your backpack is empty.",
    ),
    "backpackRings": MessageLookupByLibrary.simpleMessage("Backpack rings"),
    "badge": MessageLookupByLibrary.simpleMessage("Badge"),
    "badgeDisplay": MessageLookupByLibrary.simpleMessage("Badge display"),
    "badgeDisplayFollowingPlaces": MessageLookupByLibrary.simpleMessage(
      "Badge will be displayed at the following places",
    ),
    "badgeLeaderboard": MessageLookupByLibrary.simpleMessage(
      "Badge leaderboard",
    ),
    "badgeRankRule": MessageLookupByLibrary.simpleMessage("Badge rank"),
    "badgeRankRule1": MessageLookupByLibrary.simpleMessage(
      "Lighting up badge of different level can get stars:",
    ),
    "badgeRankRule2": MessageLookupByLibrary.simpleMessage(
      "Users will be ranked according to the stars",
    ),
    "badgeRanking": m14,
    "badgeRankingRule": MessageLookupByLibrary.simpleMessage(
      "1. The badge leaderboard is ranked according to the total number of stars obtained by users lighting up badges\n\n2. If two or more users have the same number of stars, they will be ranked according to the time when they got stars",
    ),
    "badgeRule": MessageLookupByLibrary.simpleMessage("Badge rule"),
    "badgeRuleBronze": MessageLookupByLibrary.simpleMessage("Bronze"),
    "badgeRuleDiamond": MessageLookupByLibrary.simpleMessage("Diamond"),
    "badgeRuleGold": MessageLookupByLibrary.simpleMessage("Gold"),
    "badgeRuleSilver": MessageLookupByLibrary.simpleMessage("Silver"),
    "badgeTypeRule": MessageLookupByLibrary.simpleMessage(
      "How to obtain a badge?",
    ),
    "badgeTypeRule1": MessageLookupByLibrary.simpleMessage(
      "Achieve the corresponding conditions to light up the badge",
    ),
    "badgeTypeRule2": MessageLookupByLibrary.simpleMessage(
      "Badge level: bronze-silver-gold-diamond",
    ),
    "badgeWearDesc": MessageLookupByLibrary.simpleMessage(
      "You can wear badges you earned in Winker. The source of the Badge comes from the activities and achievements you finish in the Winker. The badges you are wearing will be displayed in the profile and room profile. You can wear a maximum of three badges.",
    ),
    "badgeWearInstructions": MessageLookupByLibrary.simpleMessage(
      "Badge wear instructions",
    ),
    "bag": MessageLookupByLibrary.simpleMessage("Backpack"),
    "bagExpired": MessageLookupByLibrary.simpleMessage(
      "This bag has expired. ",
    ),
    "balance": MessageLookupByLibrary.simpleMessage("Balance"),
    "baloot": MessageLookupByLibrary.simpleMessage("Baloot"),
    "banner": MessageLookupByLibrary.simpleMessage("banner"),
    "baron": MessageLookupByLibrary.simpleMessage("Baron"),
    "beFollowedGuide": m15,
    "beMyBetterHalf": MessageLookupByLibrary.simpleMessage(
      " be my better half",
    ),
    "bePermanentlyRestricted": MessageLookupByLibrary.simpleMessage(
      "Your account has been blocked permanently for violating community rules",
    ),
    "beRestrictedFor": MessageLookupByLibrary.simpleMessage(
      "You account has been restricted because of violating community rules.",
    ),
    "beauty": MessageLookupByLibrary.simpleMessage("Beauty"),
    "becomeCouple30Days": MessageLookupByLibrary.simpleMessage(
      "Become couple 30 days",
    ),
    "becomeCouple7Days": MessageLookupByLibrary.simpleMessage(
      "Become couple 7 days",
    ),
    "becomeCoupleHalfYear": MessageLookupByLibrary.simpleMessage(
      "Become couple half year",
    ),
    "becomeCoupleOneYear": MessageLookupByLibrary.simpleMessage(
      "Become couple one year",
    ),
    "beforeDaysAgo": m16,
    "beforeHoursAgo": m17,
    "beforeMiuntesAgo": m18,
    "beforeMonthsAgo": m19,
    "beforeWeeksAgo": m20,
    "beforeYearsAgo": m21,
    "benefitsHighLevel": MessageLookupByLibrary.simpleMessage(
      "Benefits of High Level",
    ),
    "benefitsLevelContent1": MessageLookupByLibrary.simpleMessage(
      "1. You will get rewards when you level up to special level.",
    ),
    "benefitsLevelContent2": MessageLookupByLibrary.simpleMessage(
      "2. Higher levels will help you get more attention.",
    ),
    "bestLuck": MessageLookupByLibrary.simpleMessage("Best luck"),
    "betterLuckNextTime": MessageLookupByLibrary.simpleMessage(
      "Better luck next time",
    ),
    "biggestWinners": MessageLookupByLibrary.simpleMessage(
      "Biggest winners of this round",
    ),
    "bioCardContent": MessageLookupByLibrary.simpleMessage(
      "Would you like to share to moment make others know you more",
    ),
    "bioCardTitle": MessageLookupByLibrary.simpleMessage(
      "Congrats！\nYou got Winker ID Card！",
    ),
    "birthday": MessageLookupByLibrary.simpleMessage("Birthday"),
    "blessValue": MessageLookupByLibrary.simpleMessage("Bless value"),
    "blindDate": MessageLookupByLibrary.simpleMessage("TMO"),
    "block": MessageLookupByLibrary.simpleMessage("Block"),
    "blockConfirm": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to block this user?",
    ),
    "blockHint": MessageLookupByLibrary.simpleMessage(
      "Your safety is our priority. We will handle it for you promptly. To avoid further disturbance, you may also block this user.",
    ),
    "blockLost": MessageLookupByLibrary.simpleMessage("Block list"),
    "blockRoomConfirm": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to block this room?",
    ),
    "blockSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Block successfully",
    ),
    "blockUser": MessageLookupByLibrary.simpleMessage("Block user"),
    "blockUserPermanently": MessageLookupByLibrary.simpleMessage(
      "Block this user permanently",
    ),
    "blockUserTitle": MessageLookupByLibrary.simpleMessage("Block User"),
    "blockedByThisUser": MessageLookupByLibrary.simpleMessage(
      "You are blocked by this user",
    ),
    "blockedList": MessageLookupByLibrary.simpleMessage("Blocked list"),
    "blockedThisUser": MessageLookupByLibrary.simpleMessage(
      "You blocked this user",
    ),
    "blockedTime": m22,
    "bloodyViolence": MessageLookupByLibrary.simpleMessage("bloody violence"),
    "bonus": MessageLookupByLibrary.simpleMessage("Bonus"),
    "bonusPackage": MessageLookupByLibrary.simpleMessage("Bonus package"),
    "bothOfYouShowId": MessageLookupByLibrary.simpleMessage(
      "Both of you show identities! You can chat with each other.",
    ),
    "bothSidesShowIdentities": MessageLookupByLibrary.simpleMessage(
      "Both sides show identities, no time limitation",
    ),
    "bottleAvatar": MessageLookupByLibrary.simpleMessage("Bottle avatar"),
    "bottleAvatarLabel": MessageLookupByLibrary.simpleMessage(
      "Set an avatar for this anonymous space.",
    ),
    "bottleFrom": MessageLookupByLibrary.simpleMessage("A bottle from "),
    "bottleNickname": MessageLookupByLibrary.simpleMessage("Bottle nickname"),
    "bottleSettingDesc": MessageLookupByLibrary.simpleMessage(
      "Mystery letter is a complete anonymous space. The avatar and nickname you set can only be displayed here.",
    ),
    "brand": MessageLookupByLibrary.simpleMessage("Brand"),
    "bronzeFamily": MessageLookupByLibrary.simpleMessage("Bronze Family"),
    "bug": MessageLookupByLibrary.simpleMessage("Bug"),
    "bugGoodsDialogTips": m23,
    "buyGiftBoxDesc": m24,
    "buyWithAmount": m25,
    "calculatedByGifts": MessageLookupByLibrary.simpleMessage(
      "Calculated by the gifts received",
    ),
    "calculating": MessageLookupByLibrary.simpleMessage("Calculating"),
    "calculator": MessageLookupByLibrary.simpleMessage("Calculator"),
    "calculatorClosed": MessageLookupByLibrary.simpleMessage(
      "Calculator closed.",
    ),
    "calculatorHint": MessageLookupByLibrary.simpleMessage(
      "Enter activity theme...",
    ),
    "calculatorOpenedBy": MessageLookupByLibrary.simpleMessage(
      "Calculator opened by",
    ),
    "calculatorRanking": MessageLookupByLibrary.simpleMessage(
      "Calculator ranking",
    ),
    "calculatorWillClosed": MessageLookupByLibrary.simpleMessage(
      "Calculator will be closed in 1 minutes.",
    ),
    "callCancelled": MessageLookupByLibrary.simpleMessage(
      "Call cancelled by caller",
    ),
    "callDeclined": MessageLookupByLibrary.simpleMessage("Call declined"),
    "callEnded": MessageLookupByLibrary.simpleMessage("Call ended"),
    "callError": MessageLookupByLibrary.simpleMessage("Call error"),
    "callFailed": MessageLookupByLibrary.simpleMessage("Call failed"),
    "callInRoomError": MessageLookupByLibrary.simpleMessage(
      "Calls are not supported in the room",
    ),
    "callInviteAudio": MessageLookupByLibrary.simpleMessage(
      "invites you to a audio call",
    ),
    "callInviteVideo": MessageLookupByLibrary.simpleMessage(
      "invites you to a video call",
    ),
    "callMatch": MessageLookupByLibrary.simpleMessage("Call match"),
    "callMatchRule": MessageLookupByLibrary.simpleMessage(
      "1. Welcome to Call Match! You can match with real users and feel free to talk with them.\n2. Each match will cost some time. It depends on how many user online now. So please be patient.\n3. For each talk, you will have 4min to talk with hidden identity. If you like to talk longer, you can show your identity and convince the opposite to show as well. Once both sides showing identities, you will get extra talk time.\n4. Also, showing identity can allow the opposite to check your profile and learn you more! Showing each other\'s identities also unlocks the conversation after the call ends\n5. If you get any abused, provoked or impolite behavior, please report to us. Also, you can rate each call (good or bad) and we will get your feedback.",
    ),
    "callMatchSetTips": MessageLookupByLibrary.simpleMessage(
      "Close will not call call match for you.",
    ),
    "callMatchTip1": MessageLookupByLibrary.simpleMessage(
      "We are matching user for you. Please wait patiently",
    ),
    "callMatchTip2": MessageLookupByLibrary.simpleMessage(
      "Share your moments with others",
    ),
    "callMatchTip3": MessageLookupByLibrary.simpleMessage(
      "Abuse and provoke is strictly forbidden",
    ),
    "callMatchTips": MessageLookupByLibrary.simpleMessage(
      "The identities of both sides will be hidden by default during voice call. You can show the identity anytime you like.",
    ),
    "callTime": m26,
    "calling": MessageLookupByLibrary.simpleMessage("Calling"),
    "camera": MessageLookupByLibrary.simpleMessage("Camera"),
    "cameraAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Camera access not enabled",
    ),
    "cameraPermission": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" would like to access your Camera and collect your photo data to enable upload photo, camera functions only when the app is in use.",
    ),
    "canNotEditAvatar": MessageLookupByLibrary.simpleMessage(
      "You can update avatar every 24 hours, please try again tomorrow",
    ),
    "canNotEditName": MessageLookupByLibrary.simpleMessage(
      "You can edit nickname every 24 hours, please try again later",
    ),
    "canNotEnterRoom": MessageLookupByLibrary.simpleMessage(
      "You can\'t enter the chat room",
    ),
    "canWeGetKnow": MessageLookupByLibrary.simpleMessage("Can we get to know?"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancelAll": MessageLookupByLibrary.simpleMessage("Cancel all"),
    "cancelDissolution": MessageLookupByLibrary.simpleMessage(
      "Cancel dissolution",
    ),
    "cancelEvent": MessageLookupByLibrary.simpleMessage("Cancel Event"),
    "cancelEventConfirm": MessageLookupByLibrary.simpleMessage(
      "Cancel the event will clears the subscriber, and you need to recreate the event to gather your friends.",
    ),
    "cancelEventCreateContent": MessageLookupByLibrary.simpleMessage(
      "If you leave now, your event won\'t be created and your progress won\'t be saved.",
    ),
    "cancelEventCreateTitle": MessageLookupByLibrary.simpleMessage(
      "Exit without finishing?",
    ),
    "cancelMaster": MessageLookupByLibrary.simpleMessage("Cancel elders"),
    "cancelPickTopic": MessageLookupByLibrary.simpleMessage(
      "Exit recording will delete what you recorded",
    ),
    "cancelPin": MessageLookupByLibrary.simpleMessage("Cancel pin"),
    "cancelRequest": MessageLookupByLibrary.simpleMessage("Cancel request"),
    "cancelRoomEvent": MessageLookupByLibrary.simpleMessage(
      "Cancel room event",
    ),
    "cancelRoomEventTips": MessageLookupByLibrary.simpleMessage(
      "Do you want to cancel the room event?",
    ),
    "cancelVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Cancel Vice-Family Leader",
    ),
    "canceled": MessageLookupByLibrary.simpleMessage("Canceled"),
    "candy": MessageLookupByLibrary.simpleMessage("Candy"),
    "cannotBeSetDuringTheGame": MessageLookupByLibrary.simpleMessage(
      "Cannot be set during the game",
    ),
    "cannotDownMicCaptain": MessageLookupByLibrary.simpleMessage(
      "Captain in the game cannot down mic",
    ),
    "cannotDownMicInPlaying": MessageLookupByLibrary.simpleMessage(
      "Players in the game cannot down mic",
    ),
    "cannotExitRoomDuringGame": MessageLookupByLibrary.simpleMessage(
      "You cannot close the room during the game, but you can choose to leave the room.",
    ),
    "cannotKickMicInPlaying": MessageLookupByLibrary.simpleMessage(
      "Players in the game cannot be kicked out",
    ),
    "cannotStartLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "You can\'t not start lucky wheel when turntable turn on",
    ),
    "cannotStartTurntable": MessageLookupByLibrary.simpleMessage(
      "You can\'t not start turntable when lucky wheel turn on",
    ),
    "cannotSwitchMode": MessageLookupByLibrary.simpleMessage(
      "You cannot switch modes during the game.",
    ),
    "cannotSwitchRoomModes": MessageLookupByLibrary.simpleMessage(
      "You cannot switch room modes during the game",
    ),
    "cartoon": MessageLookupByLibrary.simpleMessage("Cartoon"),
    "cartoonDesc": MessageLookupByLibrary.simpleMessage(
      "Become cartoon superhero and digital art in 5 seconds!",
    ),
    "cd": MessageLookupByLibrary.simpleMessage("CD"),
    "change": MessageLookupByLibrary.simpleMessage("Change"),
    "changeAccessibleLimitedAssets": MessageLookupByLibrary.simpleMessage(
      "Update limited access files list",
    ),
    "changeAvatar": MessageLookupByLibrary.simpleMessage("Change Avatar"),
    "changeCampFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to join the camp, can not change the camp",
    ),
    "changeChatMode": m27,
    "changeChatModeTips": MessageLookupByLibrary.simpleMessage(
      "change chat mode",
    ),
    "changeCover": MessageLookupByLibrary.simpleMessage("Change cover"),
    "changeFanClubName": MessageLookupByLibrary.simpleMessage(
      "Change fan club name",
    ),
    "changeModeContent": MessageLookupByLibrary.simpleMessage(
      "You need to end the current game to perform this operation. Do you want to end the game?",
    ),
    "changeNumberOfMic": m28,
    "changePremiumSetting": MessageLookupByLibrary.simpleMessage(
      "1.Change your Settings above",
    ),
    "changeQuestion": MessageLookupByLibrary.simpleMessage("Change a question"),
    "changeRoomModeTitle": MessageLookupByLibrary.simpleMessage(
      "Change the Room Type",
    ),
    "changeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Change successfully",
    ),
    "changeTheNumberMicsWillRemoveUser": MessageLookupByLibrary.simpleMessage(
      "Change the number of mics will remove all user on the mic, are you sure change the number of mics?",
    ),
    "changeVideoModeTips": MessageLookupByLibrary.simpleMessage(
      "Are you sure change the room mode? It can cause the video stop playing.",
    ),
    "changeVideoVolume": MessageLookupByLibrary.simpleMessage(
      "Change Video Volume",
    ),
    "changedInviteOnly": MessageLookupByLibrary.simpleMessage(
      "The owner can change mic permission to invitation-only",
    ),
    "characterTitle": MessageLookupByLibrary.simpleMessage(
      "Your personality type",
    ),
    "charisma": MessageLookupByLibrary.simpleMessage("Charisma"),
    "charmLevel": MessageLookupByLibrary.simpleMessage("Charm Level"),
    "charmLevelUp": MessageLookupByLibrary.simpleMessage(
      "Congrats! Your charm level up to",
    ),
    "chat": MessageLookupByLibrary.simpleMessage("Chat"),
    "chatBubble": MessageLookupByLibrary.simpleMessage("Chat Frame"),
    "chatConsumeGoldCoin": MessageLookupByLibrary.simpleMessage(
      "First consume gold coins, and then consume diamonds if gold coins are insufficient",
    ),
    "chatConsumes": MessageLookupByLibrary.simpleMessage(
      "Sending messages consumes",
    ),
    "chatFrame": MessageLookupByLibrary.simpleMessage("Chat frame"),
    "chatGuideTipsFate": MessageLookupByLibrary.simpleMessage(
      "Your matching Fate Ball list,click on the one you are interested in to start chatting!",
    ),
    "chatGuideTipsMsg": MessageLookupByLibrary.simpleMessage(
      "All message will be displayed here,and you can click to chat with him/her.",
    ),
    "chatHistory": MessageLookupByLibrary.simpleMessage("Chats history"),
    "chatHotTip": MessageLookupByLibrary.simpleMessage(
      "She received a lot of this gift. Let’s try another gift",
    ),
    "chatInTheRoom": MessageLookupByLibrary.simpleMessage("Chat in the room"),
    "chatInputFucGuessFistOff": MessageLookupByLibrary.simpleMessage(
      "Turn Off Guess Fist",
    ),
    "chatInputFucGuessFistOn": MessageLookupByLibrary.simpleMessage(
      "Guess Fist invite",
    ),
    "chatInputFucPhoto": MessageLookupByLibrary.simpleMessage("Photo"),
    "chatInputFucQA": MessageLookupByLibrary.simpleMessage("Q&A"),
    "chatIntimacyFeatureDes": MessageLookupByLibrary.simpleMessage(
      "Gifting increases intimacy. Higher intimacy unlocks more interactive features",
    ),
    "chatIntimacyInfoFemale": MessageLookupByLibrary.simpleMessage(
      "As the intimacy value is gained, the starlight value will also be gained",
    ),
    "chatIntimacyUnlockMsgTip": m29,
    "chatIntimacyUnlockNum": m30,
    "chatIssue": MessageLookupByLibrary.simpleMessage("Chat Issue"),
    "chatMode": MessageLookupByLibrary.simpleMessage("Chat mode"),
    "chatNoMoneyTaskTitle": MessageLookupByLibrary.simpleMessage(
      "The diamonds have been used up today. You can obtain it in the following ways.",
    ),
    "chatNow": MessageLookupByLibrary.simpleMessage("Chat now"),
    "chatParty": MessageLookupByLibrary.simpleMessage("Voice Party"),
    "chatPerIntimacyIncrease": m31,
    "chatReplayRewardMsgTip1": MessageLookupByLibrary.simpleMessage(
      "Reply to the message now to get the",
    ),
    "chatReplayRewardMsgTip2": MessageLookupByLibrary.simpleMessage(
      "reward from receiving the gift.",
    ),
    "chatRoom": MessageLookupByLibrary.simpleMessage("Chat room"),
    "chatRoomInvite": MessageLookupByLibrary.simpleMessage(
      "Invite you to the party",
    ),
    "chatRoomUpgrade": MessageLookupByLibrary.simpleMessage(
      "Voice Room Upgrade",
    ),
    "chatSettings": MessageLookupByLibrary.simpleMessage("Chat settings"),
    "chatShouldStayPrivate": MessageLookupByLibrary.simpleMessage(
      "Hey, that\'s not cool.\nChats should stay private",
    ),
    "chatUnlockedSuccess": MessageLookupByLibrary.simpleMessage(
      "Unlocked successfully, start chatting now!",
    ),
    "chats": MessageLookupByLibrary.simpleMessage("Chats"),
    "chattingInRoom": MessageLookupByLibrary.simpleMessage(
      "chatting in the voice party now.",
    ),
    "chattingNow": MessageLookupByLibrary.simpleMessage("Chatting now"),
    "check": MessageLookupByLibrary.simpleMessage("Check"),
    "checkAndWearBadge": MessageLookupByLibrary.simpleMessage(
      "You can check and wear badge on the page \"Mine-Settings-Badge\"",
    ),
    "checkDetailInfo": MessageLookupByLibrary.simpleMessage(
      "Check detail info",
    ),
    "checkDetails": MessageLookupByLibrary.simpleMessage("Check details"),
    "checkInBackpack": MessageLookupByLibrary.simpleMessage(
      "Check in backpack",
    ),
    "checkInDetail": MessageLookupByLibrary.simpleMessage("Check in details"),
    "checkInDetails": MessageLookupByLibrary.simpleMessage(
      "check in details! > ",
    ),
    "checkMewPrivilegeDetails": MessageLookupByLibrary.simpleMessage(
      "please check your new privilege details.",
    ),
    "checkMyProposal": MessageLookupByLibrary.simpleMessage(
      "Check my proposal",
    ),
    "checkNetWork": MessageLookupByLibrary.simpleMessage(
      "Please check your network connection and try again",
    ),
    "checkPrivilegeDetails": MessageLookupByLibrary.simpleMessage(
      "please check your privilege details!",
    ),
    "checkUpdate": MessageLookupByLibrary.simpleMessage("Check for update"),
    "checkingUpgrade": MessageLookupByLibrary.simpleMessage(
      "Checking for latest version, please wait...",
    ),
    "cherishTheChance": MessageLookupByLibrary.simpleMessage(
      "Cherish the chance to choose carefully",
    ),
    "chief": MessageLookupByLibrary.simpleMessage("Emperor"),
    "choose": MessageLookupByLibrary.simpleMessage("Choose"),
    "chooseAnswerTip": MessageLookupByLibrary.simpleMessage(
      "Choose an answer best fits you",
    ),
    "chooseAtLeastTwo": MessageLookupByLibrary.simpleMessage(
      "Choose at least two people",
    ),
    "chooseChatTopic": MessageLookupByLibrary.simpleMessage(
      "Choose chat topic",
    ),
    "chooseRoomLabel": MessageLookupByLibrary.simpleMessage(
      "Choose room label",
    ),
    "chooseWordGuideTip": MessageLookupByLibrary.simpleMessage(
      "Choose the words you want to draw, you can also change the words.",
    ),
    "chooseYourWords": MessageLookupByLibrary.simpleMessage(
      "Choose your words",
    ),
    "choosingWords": MessageLookupByLibrary.simpleMessage("Choosing words"),
    "church": MessageLookupByLibrary.simpleMessage("Church"),
    "civilian": MessageLookupByLibrary.simpleMessage("Civilian"),
    "civilianDefeat": MessageLookupByLibrary.simpleMessage("Civilian defeat"),
    "civilianVictory": MessageLookupByLibrary.simpleMessage("Civilian victory"),
    "claimAKey": MessageLookupByLibrary.simpleMessage("Request a key"),
    "clashEmpire": MessageLookupByLibrary.simpleMessage("Clash Empire"),
    "cleanChat": MessageLookupByLibrary.simpleMessage("Clean chat"),
    "cleanChatNoticeContent": MessageLookupByLibrary.simpleMessage(
      "Are you sure clean the chat?",
    ),
    "clear": MessageLookupByLibrary.simpleMessage("Clear"),
    "clearCache": MessageLookupByLibrary.simpleMessage("Clear cache"),
    "clearChatHistory": MessageLookupByLibrary.simpleMessage(
      "Clear chat history",
    ),
    "clearPoint": MessageLookupByLibrary.simpleMessage("Clear point"),
    "clearPointConfirm": MessageLookupByLibrary.simpleMessage(
      "All performers\' points will be cleared. still clear?",
    ),
    "clearSuccess": MessageLookupByLibrary.simpleMessage("Cache cleared"),
    "cleared": MessageLookupByLibrary.simpleMessage("Cleared"),
    "clickBoom": MessageLookupByLibrary.simpleMessage("click the BOOM!!"),
    "clickChangeBottleAvatar": MessageLookupByLibrary.simpleMessage(
      "Click it to change the avatar and nickname.",
    ),
    "clickTheBoom": m32,
    "clickTheMicPosition": MessageLookupByLibrary.simpleMessage(
      "Click the mic position to chat!",
    ),
    "clickToChangePayMethod": MessageLookupByLibrary.simpleMessage(
      "Click here to change the pay method",
    ),
    "clickToGetTheFree": MessageLookupByLibrary.simpleMessage(
      "Click it to get the free rewards.",
    ),
    "clickToGetTt": MessageLookupByLibrary.simpleMessage("click to get it! >"),
    "clickToGrabTheLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Click to grab the lucky bag",
    ),
    "clickToPlay": MessageLookupByLibrary.simpleMessage("Click to play"),
    "clickToRecord": MessageLookupByLibrary.simpleMessage("Click to record"),
    "clickToRecordVoice": MessageLookupByLibrary.simpleMessage(
      "Click to record your voice",
    ),
    "clientError": MessageLookupByLibrary.simpleMessage("Client-side error"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "closeIn": MessageLookupByLibrary.simpleMessage("Close in"),
    "closeMuteAll": m33,
    "closeSeconds": m34,
    "closeVideoRoomTips": MessageLookupByLibrary.simpleMessage(
      "It can cause the room to close and stop watching videos.",
    ),
    "closeWillNotShow": MessageLookupByLibrary.simpleMessage(
      "Close will not show it",
    ),
    "closed": MessageLookupByLibrary.simpleMessage("Closed"),
    "clubSelectContent": MessageLookupByLibrary.simpleMessage(
      "In this room,you are not part of the room club, you can display your existing fan lights.",
    ),
    "clubSelectSecondTitle": MessageLookupByLibrary.simpleMessage(
      "The room clubs you had joined",
    ),
    "clubTitleSelect": MessageLookupByLibrary.simpleMessage(
      "Club title select",
    ),
    "cocosGameLoadFailed": MessageLookupByLibrary.simpleMessage(
      "Game loading failure!",
    ),
    "coins": MessageLookupByLibrary.simpleMessage("Golds"),
    "coinsAndDay": m35,
    "coinsGift": MessageLookupByLibrary.simpleMessage("Coins gift"),
    "collectionDetail": MessageLookupByLibrary.simpleMessage(
      "Collection details",
    ),
    "collections": MessageLookupByLibrary.simpleMessage("Collections"),
    "coloredNameRoomScreen": MessageLookupByLibrary.simpleMessage(
      "Attract everyone\'s attention with a colored name show on room screen.",
    ),
    "combo": MessageLookupByLibrary.simpleMessage("Combo"),
    "comboBtnTextBottom": MessageLookupByLibrary.simpleMessage("COMBO"),
    "comboBtnTextTop": MessageLookupByLibrary.simpleMessage("Pressing for"),
    "comments": MessageLookupByLibrary.simpleMessage("Comments"),
    "communityRules": MessageLookupByLibrary.simpleMessage("Community rules"),
    "completeAccount": MessageLookupByLibrary.simpleMessage(
      "Complete your account info to attract more like-minded mates",
    ),
    "completePercent": m36,
    "completePercentInUserCard": m37,
    "completeProfile": MessageLookupByLibrary.simpleMessage("Complete profile"),
    "completeProfileBubble": MessageLookupByLibrary.simpleMessage(
      "Complete your account info to attract more like-minded mates",
    ),
    "completeProfileContentGiftChat": MessageLookupByLibrary.simpleMessage(
      "Make other people reply to you easily by completing your profile!",
    ),
    "completeProfileContentMatch": MessageLookupByLibrary.simpleMessage(
      "Get more priority and conversation opportunities by completing your profile.",
    ),
    "completeToFindFriends": MessageLookupByLibrary.simpleMessage(
      "Complete all account info will help you find friends.",
    ),
    "completeYourProfile": MessageLookupByLibrary.simpleMessage(
      "Almost done! Complete your profile to find people most suitable for you!",
    ),
    "completeYourProfileInChat": MessageLookupByLibrary.simpleMessage(
      "Complete your profile get reply easily!",
    ),
    "completedProfile": MessageLookupByLibrary.simpleMessage(
      "You have completed the profile",
    ),
    "compulsoryDivorce": MessageLookupByLibrary.simpleMessage("Compulsory"),
    "compulsoryDivorceTip": m38,
    "condition": MessageLookupByLibrary.simpleMessage("Condition"),
    "conditionForFamilyApplication": MessageLookupByLibrary.simpleMessage(
      "Condition for family application",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmDeleteSong": MessageLookupByLibrary.simpleMessage(
      "Confirm to delete this song?",
    ),
    "confirmToClear": MessageLookupByLibrary.simpleMessage(
      "Confirm to clear all the chat history?",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("Confirmation"),
    "congNameToGift": MessageLookupByLibrary.simpleMessage(" has won"),
    "congOnWinning": MessageLookupByLibrary.simpleMessage(
      "Congratulations on winning",
    ),
    "congrats": MessageLookupByLibrary.simpleMessage("Congrats!"),
    "congratsNoExclamation": MessageLookupByLibrary.simpleMessage("Congrats"),
    "congratsYouGot": MessageLookupByLibrary.simpleMessage("Congrats! You got"),
    "congratsYouHaveLightedUp": m39,
    "congratulations": MessageLookupByLibrary.simpleMessage("Congratulations!"),
    "congratulationsOnBeingAppointedAsFamilynamesPosition": m40,
    "congratulationsOnJoiningTheFamilynameFamilyComeCheckItOut": m41,
    "congratulationsOnReceiving": MessageLookupByLibrary.simpleMessage(
      "Congratulations on receiving",
    ),
    "congratulationsOnSuccessfullyCreatingTheFamilynameFamilyYouCanNow": m42,
    "congratulationsToFamilynameFamilyForUpgradingToLevellevelComeCheck": m43,
    "congratulationsToGotten": m44,
    "connectedWifiTips": MessageLookupByLibrary.simpleMessage(
      "Make sure Phone and PC connected to the same Wi-Fi, then open PC\'s browser and enter the link below.",
    ),
    "connecting": MessageLookupByLibrary.simpleMessage("Connecting..."),
    "consensualDivorce": MessageLookupByLibrary.simpleMessage("Consensual"),
    "consensualDivorceTip": MessageLookupByLibrary.simpleMessage(
      "When other side approved, the rings will disappear, and will be single. Are you sure send the divorce apply?",
    ),
    "consumptionReminder": MessageLookupByLibrary.simpleMessage(
      "Consumption reminder",
    ),
    "consumptionReminderContent": m45,
    "contactCS": m46,
    "contactUs": MessageLookupByLibrary.simpleMessage("Contact us"),
    "contacts": MessageLookupByLibrary.simpleMessage("Contacts"),
    "containInvaildCharacters": MessageLookupByLibrary.simpleMessage(
      "Maybe contains special characters such as “ \' / <>. Please try others...",
    ),
    "containSensitive": MessageLookupByLibrary.simpleMessage(
      "Contains sensitive words",
    ),
    "contentCannotEmpty": MessageLookupByLibrary.simpleMessage(
      "Content cannot be empty",
    ),
    "contentOff": m47,
    "contentVoiceVerifyGuide": MessageLookupByLibrary.simpleMessage(
      "Verify your voice to ensure you are a real person.\n Passing the verification to get priority for the match.",
    ),
    "continueMatch": MessageLookupByLibrary.simpleMessage("Keep matching"),
    "continueText": MessageLookupByLibrary.simpleMessage("Continue"),
    "continueToDelete": MessageLookupByLibrary.simpleMessage(
      "Continue to delete",
    ),
    "contribute": MessageLookupByLibrary.simpleMessage("Contribute"),
    "contribution": MessageLookupByLibrary.simpleMessage("Contribution"),
    "convertStarlight": m48,
    "coolingOff": m49,
    "copy": MessageLookupByLibrary.simpleMessage("Copy"),
    "copyInvitationCode": MessageLookupByLibrary.simpleMessage(
      "Copy Invitation Code",
    ),
    "copyInviteCode": m50,
    "copyNumber": MessageLookupByLibrary.simpleMessage("Copy Number"),
    "copySuccess": MessageLookupByLibrary.simpleMessage("Copied successfully"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "Copyright © 2023 All rights reserved",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Correct"),
    "cost": MessageLookupByLibrary.simpleMessage("Cost:"),
    "count": MessageLookupByLibrary.simpleMessage("Minister"),
    "countAndCurrency": m51,
    "countDetail": MessageLookupByLibrary.simpleMessage("Count detail"),
    "countDownTimeTips": MessageLookupByLibrary.simpleMessage(
      "Your match times have been used up. The match times will be refreshed at the end of the countdown.",
    ),
    "countdown": MessageLookupByLibrary.simpleMessage("Countdown"),
    "countriesRegions": MessageLookupByLibrary.simpleMessage(
      "Countries/Regions",
    ),
    "country": MessageLookupByLibrary.simpleMessage("Country"),
    "coupleCenter": MessageLookupByLibrary.simpleMessage("Couple"),
    "coupleList": MessageLookupByLibrary.simpleMessage("Couple list"),
    "coupon": MessageLookupByLibrary.simpleMessage("Coupon"),
    "cpAnnounceTips": MessageLookupByLibrary.simpleMessage(
      "The host will announce everyone\'s selection, please wait patiently.",
    ),
    "cpNewRound": m52,
    "cpRoomOpen": m53,
    "cpTask": MessageLookupByLibrary.simpleMessage("Cp task"),
    "cpTaskDailyTime": MessageLookupByLibrary.simpleMessage(
      "Daily reset at 0:00 (GMT+3)",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Create"),
    "createByNumDiamonds": m54,
    "createConversationBySendGift": MessageLookupByLibrary.simpleMessage(
      "Create conversation with you by sending gift",
    ),
    "createEvent": MessageLookupByLibrary.simpleMessage("Create event"),
    "createEventForYourRoom": MessageLookupByLibrary.simpleMessage(
      "Create a special event for your room",
    ),
    "createFamily": MessageLookupByLibrary.simpleMessage("Create Family"),
    "createFamilyFailed": MessageLookupByLibrary.simpleMessage(
      "create failed, please contact customer service.",
    ),
    "createFamilySuccessfully": MessageLookupByLibrary.simpleMessage(
      "You have successfully submitted your family information, please wait for review",
    ),
    "createMyEvent": MessageLookupByLibrary.simpleMessage("Create my event"),
    "createMyRoom": MessageLookupByLibrary.simpleMessage("Create my room"),
    "createMyRoomUpCase": MessageLookupByLibrary.simpleMessage(
      "Create My Room",
    ),
    "createRoom": MessageLookupByLibrary.simpleMessage("Create room"),
    "createRoomGuideFirst": MessageLookupByLibrary.simpleMessage(
      "You can change your room name and cover anytime, select your favorite tag!",
    ),
    "createRoomGuideSecond": MessageLookupByLibrary.simpleMessage(
      "Invite your friends to join party!",
    ),
    "createWithAmount": m55,
    "csaeOrCsam": MessageLookupByLibrary.simpleMessage("CSAE/CSAM"),
    "csaeOrCsamDetail": MessageLookupByLibrary.simpleMessage(
      "CSAE/CSAM: Child Sexual Abuse and Exploitation/Child Sexual Abuse Materia",
    ),
    "currentIntimacy": MessageLookupByLibrary.simpleMessage("Current Intimacy"),
    "currentTitle": MessageLookupByLibrary.simpleMessage(
      "Currently-worn titles",
    ),
    "currentVersion": MessageLookupByLibrary.simpleMessage("Current Version:"),
    "currentlyRing": MessageLookupByLibrary.simpleMessage("Currently ring"),
    "currentlyWornBadges": MessageLookupByLibrary.simpleMessage(
      "Currently-worn badges",
    ),
    "customBackgroundCard": MessageLookupByLibrary.simpleMessage(
      "custom background card",
    ),
    "customTurntableFaq": MessageLookupByLibrary.simpleMessage(
      "1. Only the room owner and admin can edit, update content, turn on and close the turntable.\n\n2. The room owner and admin can spin the turntable. They can designate the members on the mic to participate the turntable as well.\n\n3. Once the turntable is turned on, the participants who can spin the turnable this round will not change until it is closed\n\n4. While the turntable is spinning, no one else can spin it until it stops.",
    ),
    "customizedThemeNameExist": MessageLookupByLibrary.simpleMessage(
      "The theme is exist",
    ),
    "customizedThemeSubtitlePlaceholder": MessageLookupByLibrary.simpleMessage(
      "Introduce your new theme description",
    ),
    "customizedThemeTitlePlaceholder": MessageLookupByLibrary.simpleMessage(
      "Introduce your new theme",
    ),
    "customizedThemeToastTitle": MessageLookupByLibrary.simpleMessage(
      "The title is not empty",
    ),
    "customizedThemes": MessageLookupByLibrary.simpleMessage(
      "Customized Themes",
    ),
    "daily": MessageLookupByLibrary.simpleMessage("Daily"),
    "dailyList": MessageLookupByLibrary.simpleMessage("Daily list"),
    "dailyRewards": MessageLookupByLibrary.simpleMessage("Daily rewards"),
    "dailyTasks": MessageLookupByLibrary.simpleMessage("Daily tasks"),
    "dare": MessageLookupByLibrary.simpleMessage("Dare"),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "day": MessageLookupByLibrary.simpleMessage("Day"),
    "dayLowCase": MessageLookupByLibrary.simpleMessage("day"),
    "days": MessageLookupByLibrary.simpleMessage("days"),
    "daysTimeout": m56,
    "dearFemale": MessageLookupByLibrary.simpleMessage("Dear"),
    "dearMale": MessageLookupByLibrary.simpleMessage("Dear"),
    "declined": MessageLookupByLibrary.simpleMessage("Declined"),
    "decorations": MessageLookupByLibrary.simpleMessage("Decorations"),
    "decreaseContent": m57,
    "deeplinkPhotoToAvatar": MessageLookupByLibrary.simpleMessage(
      "Photo to avatar in Winker",
    ),
    "defaultError": MessageLookupByLibrary.simpleMessage(
      "Our server is busy  now, please try later",
    ),
    "defaultMatchError": MessageLookupByLibrary.simpleMessage(
      "Your soulmate is not here yet, but this is not the end. Try again later!",
    ),
    "defaultRoomAnnounce": MessageLookupByLibrary.simpleMessage(
      "Welcome to my room, where you can speak freely！",
    ),
    "defaultRoomName": m58,
    "defeat": MessageLookupByLibrary.simpleMessage("Defeat"),
    "defenseBuff": m59,
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete account"),
    "deleteBottleConfirm": MessageLookupByLibrary.simpleMessage(
      "The other side will not receive your messages once you delete this chat.",
    ),
    "deleteCollectionConfirm": MessageLookupByLibrary.simpleMessage(
      "Stickers can\'t be recovered once deleted. Delete now?",
    ),
    "deleteCommentSure": MessageLookupByLibrary.simpleMessage(
      "Deleted comments cannot be recovered",
    ),
    "deleteHistory": MessageLookupByLibrary.simpleMessage("Delete history？"),
    "deleteNum": m60,
    "deleteSureContent": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete?",
    ),
    "deleteUser": MessageLookupByLibrary.simpleMessage("Delete User"),
    "deleted": MessageLookupByLibrary.simpleMessage("Deleted"),
    "desYour": MessageLookupByLibrary.simpleMessage("Describle yourself"),
    "describeYourWords": MessageLookupByLibrary.simpleMessage("Describe words"),
    "describeYourself": MessageLookupByLibrary.simpleMessage(
      "Describe yourself",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "destructHint": MessageLookupByLibrary.simpleMessage(
      "By selecting this, photo or video can only be viewed once , after which it automatically self-destructs.",
    ),
    "destructMsgHint": MessageLookupByLibrary.simpleMessage(
      "Long hold the screen\nCan only check once",
    ),
    "detail": MessageLookupByLibrary.simpleMessage("Detail"),
    "details": MessageLookupByLibrary.simpleMessage("Details"),
    "detailsForCouple": MessageLookupByLibrary.simpleMessage(
      "Details for couple",
    ),
    "diamond": MessageLookupByLibrary.simpleMessage("Diamond"),
    "diamondBalance": MessageLookupByLibrary.simpleMessage("Diamond Balance"),
    "diamondCanBroadcast": MessageLookupByLibrary.simpleMessage(
      "Only value > 1000 diamonds can broadcast to all rooms",
    ),
    "diamondGifts": MessageLookupByLibrary.simpleMessage("Diamond gifts"),
    "diamondQuantity": MessageLookupByLibrary.simpleMessage(
      "Diamond quantity:",
    ),
    "diamondRank": MessageLookupByLibrary.simpleMessage("Diamonds ranking"),
    "diamondRankExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on the diamond value of gifts you sent in this room.",
    ),
    "diamondRecharger": MessageLookupByLibrary.simpleMessage(
      "Diamond recharger",
    ),
    "diamondRewardNum": m61,
    "diamondTask": MessageLookupByLibrary.simpleMessage("Diamond Task"),
    "diamonds": MessageLookupByLibrary.simpleMessage("Diamonds"),
    "diamondsBalance": MessageLookupByLibrary.simpleMessage("Diamonds Balance"),
    "diamondsForRenewal": m62,
    "diamondsGift": MessageLookupByLibrary.simpleMessage("Diamonds gift"),
    "diamondsLowCase": MessageLookupByLibrary.simpleMessage("diamonds"),
    "digitalArt": MessageLookupByLibrary.simpleMessage("Digital Art"),
    "disbandFamily": MessageLookupByLibrary.simpleMessage(
      "Disbanding the Family",
    ),
    "disbandTheFamily": MessageLookupByLibrary.simpleMessage(
      "Disband the Family",
    ),
    "disbandingTheFamily": MessageLookupByLibrary.simpleMessage(
      "Disbanding the Family",
    ),
    "discardNow": MessageLookupByLibrary.simpleMessage("Discard now?"),
    "discardPost": MessageLookupByLibrary.simpleMessage("Discard Post"),
    "discardPublishEdit": MessageLookupByLibrary.simpleMessage(
      "If you discard now,you\'ll lose this post",
    ),
    "discardSelected": MessageLookupByLibrary.simpleMessage("Discard Selected"),
    "discountDay": MessageLookupByLibrary.simpleMessage("Store discount day"),
    "discountDayDesc": MessageLookupByLibrary.simpleMessage(
      "Every Sunday you can purchase store items with a special discount!",
    ),
    "discountOff": m63,
    "discoverMore": MessageLookupByLibrary.simpleMessage("Discover more"),
    "distanceTag": m64,
    "distanceToTheTopOne": MessageLookupByLibrary.simpleMessage(
      "Distance to the Top 1",
    ),
    "distant": MessageLookupByLibrary.simpleMessage("Distant"),
    "divorce": MessageLookupByLibrary.simpleMessage("Divorce"),
    "divorceCoolingPeriod": MessageLookupByLibrary.simpleMessage(
      "You are still in the cooling period of divorce, please wait 3 days before proposing",
    ),
    "divorcePetitionWait": MessageLookupByLibrary.simpleMessage(
      "Divorce petition has been send to your cp, please wait for the other party to deal with it.",
    ),
    "doNotBeShy": MessageLookupByLibrary.simpleMessage("Don\'t be shy!"),
    "doNotRemindMeNextTime": MessageLookupByLibrary.simpleMessage(
      "Do not remind me next time",
    ),
    "doYouWantToKickThisUserOutOfThe": MessageLookupByLibrary.simpleMessage(
      "Do you want to kick this user out of the game?",
    ),
    "doYouWantToSpend": MessageLookupByLibrary.simpleMessage(
      "Do you want to spend",
    ),
    "doYouWantToTransferUserAsTheGameCaptain": m65,
    "doYouWantToUseTheCustomBackgroundCardsDirectly":
        MessageLookupByLibrary.simpleMessage(
          "Do you want to use the custom background cards directly?",
        ),
    "domino": MessageLookupByLibrary.simpleMessage("Domino"),
    "dominoes": MessageLookupByLibrary.simpleMessage("Dominoes"),
    "done": MessageLookupByLibrary.simpleMessage("Done"),
    "doneNum": m66,
    "dontMind": MessageLookupByLibrary.simpleMessage("If you don’t mind, "),
    "dontMiss": MessageLookupByLibrary.simpleMessage(
      "Don\'t miss a single message!",
    ),
    "dontRemindMeToday": MessageLookupByLibrary.simpleMessage(
      "Don\'t remind me today",
    ),
    "dontReminder": MessageLookupByLibrary.simpleMessage("Don\'t remind"),
    "doubleConfirmation": MessageLookupByLibrary.simpleMessage(
      "Double confirmation",
    ),
    "doubleConfirmationDes": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to do this?",
    ),
    "downMic": MessageLookupByLibrary.simpleMessage("Down mic"),
    "download": MessageLookupByLibrary.simpleMessage("Download"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "Downloaded successfully",
    ),
    "downloadingToLater": MessageLookupByLibrary.simpleMessage(
      "Downloading fail, please try again later",
    ),
    "draw": MessageLookupByLibrary.simpleMessage("Draw"),
    "drawFanNumReward": m67,
    "drawForFree": MessageLookupByLibrary.simpleMessage("Draw\nfor free"),
    "drawGuess": MessageLookupByLibrary.simpleMessage("Draw & Guess"),
    "drawGuessFaq": MessageLookupByLibrary.simpleMessage(
      "1. At least 2 players are required to start the game.\n2. Each player takes turns as a painter, drawing the selected words, while the other players are guessers.\n3. The painter scores points based on the number of guessers who guess correctly.\n4. The guessers receive different scores based on the order of the correct answers.\n5. Finally, the players are ranked according to their final scores.",
    ),
    "drawRewards": MessageLookupByLibrary.simpleMessage(
      "Draw to win big rewards",
    ),
    "drawing": MessageLookupByLibrary.simpleMessage("Drawing"),
    "duration": MessageLookupByLibrary.simpleMessage("Duration:"),
    "durationWith": m68,
    "eachPersonCanVoteOnce": MessageLookupByLibrary.simpleMessage(
      "Each person can vote once a time",
    ),
    "earnGold": MessageLookupByLibrary.simpleMessage("Earn gold >"),
    "earnGolds": MessageLookupByLibrary.simpleMessage("Earn golds"),
    "earphone": MessageLookupByLibrary.simpleMessage("Earphone"),
    "earphoneFeedback": MessageLookupByLibrary.simpleMessage(
      "Earphone feedback",
    ),
    "echo": MessageLookupByLibrary.simpleMessage("Echo"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editAnniversary": MessageLookupByLibrary.simpleMessage("Edit Anniversary"),
    "editAvatar": MessageLookupByLibrary.simpleMessage("Edit Avatar"),
    "editFamilyName": MessageLookupByLibrary.simpleMessage("Edit family name"),
    "editFamilySlogan": MessageLookupByLibrary.simpleMessage(
      "Edit family slogan",
    ),
    "editInformation": MessageLookupByLibrary.simpleMessage("Edit information"),
    "editName": MessageLookupByLibrary.simpleMessage("Edit name"),
    "editNickNameAlert": MessageLookupByLibrary.simpleMessage(
      "You can edit nickname every 24 hours, are you sure you want to change it?",
    ),
    "editPhoto": MessageLookupByLibrary.simpleMessage("Edit photo"),
    "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
    "editProfileLivingDes": MessageLookupByLibrary.simpleMessage(
      "The system will recommend nearby users for you.",
    ),
    "editProfileLivingTitle": MessageLookupByLibrary.simpleMessage(
      "Select your main living area",
    ),
    "editQuestions": MessageLookupByLibrary.simpleMessage("Edit questions"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edit successfully",
    ),
    "editTheme": MessageLookupByLibrary.simpleMessage("Edit theme"),
    "editWithoutPermission": MessageLookupByLibrary.simpleMessage(
      "Edit without permission",
    ),
    "effectPreview": MessageLookupByLibrary.simpleMessage("Effect Preview"),
    "elders": MessageLookupByLibrary.simpleMessage("Elders"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "empirePropBuff": MessageLookupByLibrary.simpleMessage(
      "BUFF: Increase Power",
    ),
    "empirePropCamel": MessageLookupByLibrary.simpleMessage(
      "Summoning Battle Camels",
    ),
    "empirePropDragon": MessageLookupByLibrary.simpleMessage(
      "Summoning the Divine Dragon",
    ),
    "empty": MessageLookupByLibrary.simpleMessage("Empty"),
    "emptyComments": MessageLookupByLibrary.simpleMessage(
      "No one has commented yet",
    ),
    "emptyIntimacy": MessageLookupByLibrary.simpleMessage(
      "Ooops, you do not have soulmate.\nOnly the intimacy over 5000 can proposal",
    ),
    "emptyList": MessageLookupByLibrary.simpleMessage("Empty list"),
    "emptyMoment": MessageLookupByLibrary.simpleMessage(
      "There are no posts yet.",
    ),
    "emptyNotice": MessageLookupByLibrary.simpleMessage("No notification yet"),
    "emptyRing": MessageLookupByLibrary.simpleMessage(
      "Buy a ring to make a proposal, show your heart!",
    ),
    "emptyTheList": MessageLookupByLibrary.simpleMessage("Empty the list"),
    "enable": MessageLookupByLibrary.simpleMessage("Enable"),
    "enableCameraAccess": MessageLookupByLibrary.simpleMessage(
      "Please go to \"Settings\" > \"Winker\" and enable camera access.",
    ),
    "enableForMoment": MessageLookupByLibrary.simpleMessage(
      "Enable notification to receive updates for moments and comments",
    ),
    "enableForMsgFemale": MessageLookupByLibrary.simpleMessage(
      "Enable notification to receive her message",
    ),
    "enableForMsgMale": MessageLookupByLibrary.simpleMessage(
      "Enable notification to receive his message",
    ),
    "enableMicAccess": MessageLookupByLibrary.simpleMessage(
      "Please go to \"Settings\" > \"Winker\" and enable microphone access.",
    ),
    "enablePhotoAccess": MessageLookupByLibrary.simpleMessage(
      "Please allow Winker to access your device\'s photos in \"Settings> Privacy > Photos",
    ),
    "enableStorageAccess": MessageLookupByLibrary.simpleMessage(
      "Please go to \"Settings\" > \"Winker\" and enable storage access.",
    ),
    "enabled": MessageLookupByLibrary.simpleMessage("Enabled"),
    "end": MessageLookupByLibrary.simpleMessage("End"),
    "endDateAndTime": MessageLookupByLibrary.simpleMessage("End date and time"),
    "endGame": MessageLookupByLibrary.simpleMessage("End the game"),
    "endMode": MessageLookupByLibrary.simpleMessage("End mode"),
    "ended": MessageLookupByLibrary.simpleMessage("Ended"),
    "energy": MessageLookupByLibrary.simpleMessage("Energy"),
    "ensuresSafety": MessageLookupByLibrary.simpleMessage(
      "Winker ensures all users are in safety and real people environment.",
    ),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterEffect": MessageLookupByLibrary.simpleMessage("Enter effect"),
    "enterEffectTitle": MessageLookupByLibrary.simpleMessage(
      "Enter effect title",
    ),
    "enterEffectTitleDesc": MessageLookupByLibrary.simpleMessage(
      "Attract everyone\'s attention with a gorgeous enter message",
    ),
    "enterEventDescription": MessageLookupByLibrary.simpleMessage(
      "Enter event description",
    ),
    "enterFamily": MessageLookupByLibrary.simpleMessage("Enter family"),
    "enterMessage": MessageLookupByLibrary.simpleMessage("Enter message"),
    "enterNumLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Please enter the number of Lucky bag",
    ),
    "enterPermission": MessageLookupByLibrary.simpleMessage("Enter permission"),
    "enterSmsCodeSentTo": MessageLookupByLibrary.simpleMessage(
      "Send a verification code to the following number:",
    ),
    "enterTheNumberOfLuckyBags": MessageLookupByLibrary.simpleMessage(
      "Enter the number of lucky bags",
    ),
    "enterThePassword": MessageLookupByLibrary.simpleMessage(
      "Enter the password",
    ),
    "enterTheRoom": MessageLookupByLibrary.simpleMessage("Enter the room"),
    "enterTheTotalDiamondAmountForTheLuckyBag":
        MessageLookupByLibrary.simpleMessage(
          "Enter the total diamond amount for the lucky bag",
        ),
    "enteredDays": m69,
    "enteredTheRoom": MessageLookupByLibrary.simpleMessage("Entered the room"),
    "entranceMessage": MessageLookupByLibrary.simpleMessage("Entrance Message"),
    "entryFee": MessageLookupByLibrary.simpleMessage("Entry Fee:"),
    "entryFees": MessageLookupByLibrary.simpleMessage("Entry fees:"),
    "errorOccurred": m70,
    "estimatedRefund": MessageLookupByLibrary.simpleMessage(
      "Estimated refund: ",
    ),
    "evaluateContent": MessageLookupByLibrary.simpleMessage(
      "If you like Winker, please give us a 5 star, thanks~",
    ),
    "evenDetailMedal": MessageLookupByLibrary.simpleMessage("Medal"),
    "evenDetailRoomId": MessageLookupByLibrary.simpleMessage("Room ID"),
    "evenDetailSubject": MessageLookupByLibrary.simpleMessage("Subject"),
    "evenDetailSubscribers": m71,
    "evenDetailTime": MessageLookupByLibrary.simpleMessage("Time"),
    "evenShareSubscribers": m72,
    "event": MessageLookupByLibrary.simpleMessage("Event"),
    "eventBannerLimit": MessageLookupByLibrary.simpleMessage(
      "* The banner size is 300*600 and it can not be more than 5MB",
    ),
    "eventCreateNoRoom": MessageLookupByLibrary.simpleMessage(
      "You need to create your own voice room before you create room event",
    ),
    "eventCreateToast": MessageLookupByLibrary.simpleMessage(
      "You haven\'t fill in all the information",
    ),
    "eventCreatedShare": MessageLookupByLibrary.simpleMessage(
      "You have created your room event successfully. Share it to Moment now!",
    ),
    "eventDescTips": MessageLookupByLibrary.simpleMessage(
      "More information, make guest know your event details.",
    ),
    "eventDescription": MessageLookupByLibrary.simpleMessage(
      "Event description",
    ),
    "eventDetails": MessageLookupByLibrary.simpleMessage("Event details"),
    "eventGiftRankTips": MessageLookupByLibrary.simpleMessage(
      "During the event, sending diamond gifts and gold coin gifts will earn gift points.",
    ),
    "eventInReview": MessageLookupByLibrary.simpleMessage("Event in review"),
    "eventMineEmpty": MessageLookupByLibrary.simpleMessage(
      "You haven\'t created your room event~",
    ),
    "eventName": MessageLookupByLibrary.simpleMessage("Event name"),
    "eventRoom": MessageLookupByLibrary.simpleMessage("Event Room"),
    "eventRoomIDType": m73,
    "eventRule1": MessageLookupByLibrary.simpleMessage(
      "1. Users need to create their own voice room first and then they can create room event",
    ),
    "eventRule2": MessageLookupByLibrary.simpleMessage(
      "2. Users can create room event within 7 days",
    ),
    "eventRule3": MessageLookupByLibrary.simpleMessage(
      "3. When creating a room event, the event details need to be reviewed. During the review process, the details can be modified. Once approved, only the event time can be changed. Each event can only modify its details once.",
    ),
    "eventRuleTitle": MessageLookupByLibrary.simpleMessage("Room Event Rules"),
    "eventSelectPeriod": MessageLookupByLibrary.simpleMessage(
      "Selection period...",
    ),
    "eventShareHost": MessageLookupByLibrary.simpleMessage("Host:"),
    "eventShareTitle": MessageLookupByLibrary.simpleMessage(
      "Subscribe it and participate in the party with me~",
    ),
    "eventShareToMoment": MessageLookupByLibrary.simpleMessage(
      "Share it to Moment",
    ),
    "eventSubject": m74,
    "eventSubjectPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Enter event theme...",
    ),
    "eventTag": MessageLookupByLibrary.simpleMessage("Event tag"),
    "eventTime": MessageLookupByLibrary.simpleMessage("Event time"),
    "eventUploadBanner": MessageLookupByLibrary.simpleMessage("Upload Banner"),
    "eventWithEmoji": MessageLookupByLibrary.simpleMessage("🎉 Event"),
    "events": MessageLookupByLibrary.simpleMessage("Events"),
    "everyone": MessageLookupByLibrary.simpleMessage("Everyone"),
    "exchange": MessageLookupByLibrary.simpleMessage("Exchange"),
    "exchangeAll": MessageLookupByLibrary.simpleMessage("Exchange all"),
    "exchangeDiamonds": MessageLookupByLibrary.simpleMessage(
      "Exchange Diamonds",
    ),
    "exchangeGemToDiamond": MessageLookupByLibrary.simpleMessage(
      "Exchange ruby to diamond",
    ),
    "exchangeHint": MessageLookupByLibrary.simpleMessage("Number of Diamonds"),
    "exchangeKeysToUnlockBox": MessageLookupByLibrary.simpleMessage(
      "Exchange keys to unlock the bonus box",
    ),
    "exchangeNumDiamond": m75,
    "exchangeStarlight": MessageLookupByLibrary.simpleMessage(
      "Exchange with Starlight",
    ),
    "exchangeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Exchange successfully",
    ),
    "exchangeToDiamond": MessageLookupByLibrary.simpleMessage(
      "Exchange to Diamond",
    ),
    "exclusivePremiumAvatar": MessageLookupByLibrary.simpleMessage(
      "Exclusive Premium avatar frame",
    ),
    "exclusivePremiumChat": MessageLookupByLibrary.simpleMessage(
      "Exclusive Premium chat frame",
    ),
    "exclusivePremiumChatDesc": MessageLookupByLibrary.simpleMessage(
      "Your message with gorgeous frame show your friends",
    ),
    "exclusivePrivileges": MessageLookupByLibrary.simpleMessage(
      "Exclusive Privileges",
    ),
    "exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "exitFamily": MessageLookupByLibrary.simpleMessage("Quit the family"),
    "exitFansGroupTipsConfirm": MessageLookupByLibrary.simpleMessage(
      "I confirm",
    ),
    "exitFansGroupTipsContent": MessageLookupByLibrary.simpleMessage(
      "After logging out, the accumulated popularity points and privileges will be reset to zero, Are you sure you want to perform this operation?",
    ),
    "exitFansGroupTipsThinkAgain": MessageLookupByLibrary.simpleMessage(
      "Think again",
    ),
    "exitFansGroupTitle": MessageLookupByLibrary.simpleMessage("Exit reminder"),
    "exitNoSaveInfo": MessageLookupByLibrary.simpleMessage(
      "Exit will not save existing information",
    ),
    "exitRecordingTips": MessageLookupByLibrary.simpleMessage(
      "Discard now? Your audio note is not saved.",
    ),
    "exitRoomContent": MessageLookupByLibrary.simpleMessage(
      "It can cause the room to close.",
    ),
    "exitRoomTitle": MessageLookupByLibrary.simpleMessage(
      "Are you sure exit the room?",
    ),
    "exitRoomWillCauseGameFailed": MessageLookupByLibrary.simpleMessage(
      "Exiting the room will cause the game failed",
    ),
    "exitRoomWillLeaveGame": MessageLookupByLibrary.simpleMessage(
      "Exiting the room will leave the game",
    ),
    "exitTheRoom": MessageLookupByLibrary.simpleMessage("Exit the room"),
    "exitWillDelete": MessageLookupByLibrary.simpleMessage(
      "Exit will delete the records what you selected",
    ),
    "exp": m76,
    "expNum": m77,
    "expStr": MessageLookupByLibrary.simpleMessage("Exp"),
    "expText": MessageLookupByLibrary.simpleMessage("Exp"),
    "expireTime": m78,
    "expired": MessageLookupByLibrary.simpleMessage("expired"),
    "expiredCapital": MessageLookupByLibrary.simpleMessage("Expired"),
    "expiredDate": m79,
    "explore": MessageLookupByLibrary.simpleMessage("Explore"),
    "extraContent": m80,
    "faceNotDetected": MessageLookupByLibrary.simpleMessage(
      "Face are not detected. Please try again.",
    ),
    "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
    "failed": MessageLookupByLibrary.simpleMessage("Failed"),
    "failedToSend": MessageLookupByLibrary.simpleMessage("Failed to send"),
    "failedToSubmit": MessageLookupByLibrary.simpleMessage(
      "Failed to submit, please try again",
    ),
    "failureMatching": MessageLookupByLibrary.simpleMessage(
      "Failure of Matching",
    ),
    "fakeMsgFemale": MessageLookupByLibrary.simpleMessage("Hello"),
    "fakeMsgMale": MessageLookupByLibrary.simpleMessage("Hello"),
    "fakeUserFemale": MessageLookupByLibrary.simpleMessage("Nancy Ramy"),
    "fakeUserMale": MessageLookupByLibrary.simpleMessage("Taim Anas"),
    "family": MessageLookupByLibrary.simpleMessage("family"),
    "familyAnnouncement": MessageLookupByLibrary.simpleMessage(
      "Family announcement",
    ),
    "familyCapital": MessageLookupByLibrary.simpleMessage("Family"),
    "familyCover": MessageLookupByLibrary.simpleMessage("Family cover"),
    "familyCoverEditAfterDays": m81,
    "familyCreateSuccess": MessageLookupByLibrary.simpleMessage(
      "Family create successfully!",
    ),
    "familyData": MessageLookupByLibrary.simpleMessage("Family data"),
    "familyDisbanded": MessageLookupByLibrary.simpleMessage(
      "Your family has been disbanded.",
    ),
    "familyGroup": MessageLookupByLibrary.simpleMessage("Family group"),
    "familyHonorRanking": m82,
    "familyInfoEdit": MessageLookupByLibrary.simpleMessage("Family info Edit"),
    "familyInfoRejected": MessageLookupByLibrary.simpleMessage(
      "The family information you submitted has been rejected due to violation of Winker community rules. Please go to the family creation page to view details!",
    ),
    "familyLevel": MessageLookupByLibrary.simpleMessage("Family level"),
    "familyLevelExp": MessageLookupByLibrary.simpleMessage("Family level exp"),
    "familyList": MessageLookupByLibrary.simpleMessage("Family list"),
    "familyLuckyBag": MessageLookupByLibrary.simpleMessage("Family Lucky Bag"),
    "familyLuckyBagCannotBeDistributed": m83,
    "familyManage": MessageLookupByLibrary.simpleMessage("Family manage"),
    "familyManagement": MessageLookupByLibrary.simpleMessage(
      "Family management",
    ),
    "familyMemberOnly": MessageLookupByLibrary.simpleMessage(
      "Family member only",
    ),
    "familyName": MessageLookupByLibrary.simpleMessage("Family name"),
    "familyNameCountInvalid": m84,
    "familyNameEditOnce": MessageLookupByLibrary.simpleMessage(
      "Family name is revised once a week",
    ),
    "familyPrivilege": MessageLookupByLibrary.simpleMessage("Family privilege"),
    "familyRankExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on the diamond value of gifts family member received",
    ),
    "familyRankRule": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on family member contribution",
    ),
    "familyRepresentative": MessageLookupByLibrary.simpleMessage(
      "Family members",
    ),
    "familyRepresentativeRule": MessageLookupByLibrary.simpleMessage(
      "Family Leader, Vice-Family Leader, Elders",
    ),
    "familyRequest": MessageLookupByLibrary.simpleMessage("Family request"),
    "familyRoom": MessageLookupByLibrary.simpleMessage("Family room"),
    "familyRoomCoverCannotBeModified": MessageLookupByLibrary.simpleMessage(
      "Family room cover cannot be modified",
    ),
    "familyRoomNameCannotBeModified": MessageLookupByLibrary.simpleMessage(
      "Family room name cannot be modified",
    ),
    "familyRoomRule": MessageLookupByLibrary.simpleMessage(
      "Every family can run a room. Family room belong to the family leader.",
    ),
    "familySettings": MessageLookupByLibrary.simpleMessage("Family Settings"),
    "familyShareContent": m85,
    "familySlogan": MessageLookupByLibrary.simpleMessage("Family Slogan"),
    "familySloganCountInvalid": m86,
    "familySloganEditOnce": MessageLookupByLibrary.simpleMessage(
      "Family slogan is revised once a week",
    ),
    "familyTask": MessageLookupByLibrary.simpleMessage("Family Task"),
    "familyTip": MessageLookupByLibrary.simpleMessage(
      "Born in Java, currently working and living only child and a well.off family. My dailv life circle is narrow and hope to meet my crush here.",
    ),
    "familyTreasury": MessageLookupByLibrary.simpleMessage("Family Treasury"),
    "familyUpLevel": m87,
    "fanClubList": MessageLookupByLibrary.simpleMessage("fan club list"),
    "fanLevelUpgrade": m88,
    "fansCongratulationUserLevelMsg": m89,
    "fansGroupTabTitleFansList": MessageLookupByLibrary.simpleMessage(
      "Fans List",
    ),
    "fansGroupTabTitleHostBenefits": MessageLookupByLibrary.simpleMessage(
      "Host benefits",
    ),
    "fansGroupTabTitleTreasureRecord": MessageLookupByLibrary.simpleMessage(
      "Treasure record",
    ),
    "fansGroupTips": MessageLookupByLibrary.simpleMessage(
      "Both giving and receiving gifts boost experience.",
    ),
    "fansGroupTotalNum": m90,
    "fansNoDrawTimesTip": MessageLookupByLibrary.simpleMessage(
      "Go and seize the opportunity by completing the task",
    ),
    "fantasy": MessageLookupByLibrary.simpleMessage("Fantasy"),
    "faq": MessageLookupByLibrary.simpleMessage("FAQ"),
    "fateBell": MessageLookupByLibrary.simpleMessage("Fate Bell"),
    "fateBellSetting": MessageLookupByLibrary.simpleMessage(
      "Fate Bell Setting",
    ),
    "fateNewMatch": MessageLookupByLibrary.simpleMessage(
      "The fate bell has rung, there’s a new match",
    ),
    "favoriteSticker": MessageLookupByLibrary.simpleMessage(
      "We have added the sticker feature.\nYou can add stickers to your favorites now.",
    ),
    "favoritesList": MessageLookupByLibrary.simpleMessage("Gift sent"),
    "favoritesRank": MessageLookupByLibrary.simpleMessage("Favorites rank"),
    "favouriteHashtag": MessageLookupByLibrary.simpleMessage(
      "Click here to choose your favourite hashtag! ",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
    "feltOffended": MessageLookupByLibrary.simpleMessage("Felt offended? "),
    "female": MessageLookupByLibrary.simpleMessage("Female"),
    "femaleShareGuideContent": MessageLookupByLibrary.simpleMessage(
      "Safe and privacy friendship!",
    ),
    "fillAccountInfo": MessageLookupByLibrary.simpleMessage(
      "Please fill in account info",
    ),
    "film": MessageLookupByLibrary.simpleMessage("Film"),
    "filmDesc": MessageLookupByLibrary.simpleMessage(
      "Want to take yourself back to the Middle Ages?",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "filterTimesToday": MessageLookupByLibrary.simpleMessage(
      "You tried too much today, please come back tomorrow",
    ),
    "findFriends": MessageLookupByLibrary.simpleMessage("Find friends"),
    "findSoulmateFirst": MessageLookupByLibrary.simpleMessage(
      "Find your soulmate first",
    ),
    "findYourFatedMate": MessageLookupByLibrary.simpleMessage(
      "Find Your Fated Mate",
    ),
    "findingPerfectMatchTitle": MessageLookupByLibrary.simpleMessage(
      "Finding your perfect match",
    ),
    "finish": MessageLookupByLibrary.simpleMessage("Finish"),
    "finishTreasureTask": MessageLookupByLibrary.simpleMessage(
      "Completing the task gives you the opportunity to receive a larger reward.",
    ),
    "firstLevelTips": m91,
    "firstName": MessageLookupByLibrary.simpleMessage("First name"),
    "firstRechargeTitle": MessageLookupByLibrary.simpleMessage("Recharge"),
    "firstSelectYourFavourite": MessageLookupByLibrary.simpleMessage(
      "First, select your favourite text to read with your voice. You can change the words.",
    ),
    "firstStep": MessageLookupByLibrary.simpleMessage("First step"),
    "fishFailed": MessageLookupByLibrary.simpleMessage("Fish failed"),
    "fishFailedTryAgain": MessageLookupByLibrary.simpleMessage(
      "Fish failed, try again.",
    ),
    "fishTheBottle": MessageLookupByLibrary.simpleMessage("Fish a bottle"),
    "follow": MessageLookupByLibrary.simpleMessage("Follow"),
    "followAll": MessageLookupByLibrary.simpleMessage("Follow All"),
    "followAndJoinTheFanClub": MessageLookupByLibrary.simpleMessage(
      "Follow and join the fan club",
    ),
    "followBack": MessageLookupByLibrary.simpleMessage("Follow back"),
    "followBestSurprise": MessageLookupByLibrary.simpleMessage(
      "Follow them for the best surprise!",
    ),
    "followGuide": m92,
    "followHostNow": MessageLookupByLibrary.simpleMessage(
      "Follow the host now",
    ),
    "followRoomContent": MessageLookupByLibrary.simpleMessage(
      "Follow the room to receive party news!",
    ),
    "followSomeInterest": MessageLookupByLibrary.simpleMessage(
      "Follow some interesting mates now!",
    ),
    "followSuccess": MessageLookupByLibrary.simpleMessage(
      "Followed successfully",
    ),
    "followTheRoom": MessageLookupByLibrary.simpleMessage("Follow the room"),
    "followThisRoom": MessageLookupByLibrary.simpleMessage("Follow this room"),
    "followUserGuideContent": MessageLookupByLibrary.simpleMessage(
      "Follow the user, join next time!",
    ),
    "followYou": MessageLookupByLibrary.simpleMessage("Follow you"),
    "followed": MessageLookupByLibrary.simpleMessage("Followed"),
    "followedRoomEmpty": MessageLookupByLibrary.simpleMessage(
      "You have not followed any rooms.",
    ),
    "followedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Followed successfully",
    ),
    "followedYou": MessageLookupByLibrary.simpleMessage("followed you"),
    "follower": MessageLookupByLibrary.simpleMessage("Follower"),
    "followerWithNum": m93,
    "followers": MessageLookupByLibrary.simpleMessage("Followers"),
    "following": MessageLookupByLibrary.simpleMessage("Following"),
    "followingRoom": MessageLookupByLibrary.simpleMessage("Following Room"),
    "followingUser": MessageLookupByLibrary.simpleMessage("Following user"),
    "forSomeoneToMatchYou": MessageLookupByLibrary.simpleMessage(
      "Someone matches you in 3 seconds, please give us 5 stars!",
    ),
    "forYou": MessageLookupByLibrary.simpleMessage("For you"),
    "forceClosedTips": MessageLookupByLibrary.simpleMessage(
      "Violating community rules, this room has been closed",
    ),
    "formIdEmpty": MessageLookupByLibrary.simpleMessage("Form id empty"),
    "fourteenCentury": MessageLookupByLibrary.simpleMessage("14th Century"),
    "free": MessageLookupByLibrary.simpleMessage("Free"),
    "freeChance": m94,
    "freeGift": MessageLookupByLibrary.simpleMessage("Free gift"),
    "freeJoin": MessageLookupByLibrary.simpleMessage("Free join"),
    "freeMode": MessageLookupByLibrary.simpleMessage("Free mode"),
    "freeModeContent": MessageLookupByLibrary.simpleMessage(
      "Free mode not restrict sensitive content.",
    ),
    "freeModeDetailContent": MessageLookupByLibrary.simpleMessage(
      "Under free mode you may express yourself freely, but the other side shall accept your invitation to switch on the same mode. ",
    ),
    "freeToGet": MessageLookupByLibrary.simpleMessage("Free to get"),
    "friendNotFound": MessageLookupByLibrary.simpleMessage(
      "This friend is not found",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("Friends"),
    "friendsMessages": MessageLookupByLibrary.simpleMessage(
      "Friend\'s Messages",
    ),
    "friendsRequestSettings": MessageLookupByLibrary.simpleMessage(
      "Settings -> VIP Settings -> Who can chat to me?",
    ),
    "friendsToBeSent": MessageLookupByLibrary.simpleMessage(
      "Friends to be sent:",
    ),
    "friendsYouMayKnow": MessageLookupByLibrary.simpleMessage(
      "Friends you may know",
    ),
    "friendshipMatching": MessageLookupByLibrary.simpleMessage(
      "Friendship matching",
    ),
    "from": MessageLookupByLibrary.simpleMessage("From"),
    "fromLuckyBag": MessageLookupByLibrary.simpleMessage("from lucky bag."),
    "fromTreasureBox": MessageLookupByLibrary.simpleMessage(
      "from Treasure Box",
    ),
    "full": MessageLookupByLibrary.simpleMessage("Full"),
    "fundDiamond": MessageLookupByLibrary.simpleMessage("1 fund = 1 diamond"),
    "game": MessageLookupByLibrary.simpleMessage("Game"),
    "gameBadge": MessageLookupByLibrary.simpleMessage("Game badge"),
    "gameBalance": MessageLookupByLibrary.simpleMessage("Balance:"),
    "gameCenter": MessageLookupByLibrary.simpleMessage("Game Center"),
    "gameCenterRule": MessageLookupByLibrary.simpleMessage("Game Center Rule"),
    "gameCenterRuleContent1": MessageLookupByLibrary.simpleMessage(
      "Players who meet the conditions required in the game will have the opportunity to receive task rewards - points. Use points for a lucky draw, with a 100% winning rate!",
    ),
    "gameCenterRuleContent2": MessageLookupByLibrary.simpleMessage(
      "Use 10 points for one lucky draw with a 100% winning rate!",
    ),
    "gameCenterRuleContent3": MessageLookupByLibrary.simpleMessage(
      "The ranking is arranged in descending order based on the golds currently won by the user in all games.",
    ),
    "gameCenterRuleContent4": MessageLookupByLibrary.simpleMessage(
      "The ranking list is divided into daily and weekly rankings. The daily ranking will reset at 00:00 every day; the weekly ranking will reset at 00:00 every Monday.",
    ),
    "gameCenterRuleContent5": MessageLookupByLibrary.simpleMessage(
      "Yes, during the settlement of the weekly ranking, corresponding avatar frames will be awarded to the top three users on the list. Reward distribution time: 1:00 AM on Monday.",
    ),
    "gameCenterRuleTitle1": MessageLookupByLibrary.simpleMessage(
      "1 How to play the 1-point lucky draw?",
    ),
    "gameCenterRuleTitle2": MessageLookupByLibrary.simpleMessage(
      "2 How is the prize pool of the 1-point lucky draw calculated?",
    ),
    "gameCenterRuleTitle3": MessageLookupByLibrary.simpleMessage(
      "3 How is the ranking calculated?",
    ),
    "gameCenterRuleTitle4": MessageLookupByLibrary.simpleMessage(
      "4 How is the ranking list refreshed?",
    ),
    "gameCenterRuleTitle5": MessageLookupByLibrary.simpleMessage(
      "5 Are there rewards for the ranking list?",
    ),
    "gameCharge": MessageLookupByLibrary.simpleMessage("Charge 10% commission"),
    "gameError": m95,
    "gameMatch": MessageLookupByLibrary.simpleMessage("Game Match"),
    "gameNotFound": MessageLookupByLibrary.simpleMessage("Game not Found!"),
    "gameOver": MessageLookupByLibrary.simpleMessage("Game Over"),
    "gameOverAndWinner": m96,
    "gameOverLowerCase": MessageLookupByLibrary.simpleMessage("Game over"),
    "gameRecords": MessageLookupByLibrary.simpleMessage(
      "Participation Records",
    ),
    "gameRoom": MessageLookupByLibrary.simpleMessage("Game room"),
    "gameSelectTips": MessageLookupByLibrary.simpleMessage(
      "Select the quantity of diamonds > Select items",
    ),
    "gameStart": MessageLookupByLibrary.simpleMessage("Game start"),
    "gaming": MessageLookupByLibrary.simpleMessage("Gaming"),
    "gemBalance": MessageLookupByLibrary.simpleMessage("Ruby Balance"),
    "gemExchangeDiamond": m97,
    "gems": MessageLookupByLibrary.simpleMessage("Rubies"),
    "gemsLowCase": MessageLookupByLibrary.simpleMessage("rubies"),
    "generalPrediction": MessageLookupByLibrary.simpleMessage(
      "General prediction",
    ),
    "get": MessageLookupByLibrary.simpleMessage("Get"),
    "getBigBonus": MessageLookupByLibrary.simpleMessage(
      "get big bonus values 1000 diamonds",
    ),
    "getDiamondDay": m98,
    "getDiamondsEveryDay": MessageLookupByLibrary.simpleMessage(
      "Get diamonds every day",
    ),
    "getDrawNum": MessageLookupByLibrary.simpleMessage(
      "Get the number of draws",
    ),
    "getForFree": MessageLookupByLibrary.simpleMessage("Get for free"),
    "getIt": MessageLookupByLibrary.simpleMessage("Get it"),
    "getItTomorrow": MessageLookupByLibrary.simpleMessage("Get it tomorrow"),
    "getMoreInWinker": MessageLookupByLibrary.simpleMessage(
      "Can get more exposure and recommendation in Winker.",
    ),
    "getMoreRewards": MessageLookupByLibrary.simpleMessage("Get more rewards"),
    "getTheTreasureRewards": MessageLookupByLibrary.simpleMessage(
      "Get the rewards from the treasure now!",
    ),
    "getTitle": MessageLookupByLibrary.simpleMessage("Get title"),
    "gif": MessageLookupByLibrary.simpleMessage("Gif"),
    "gift": MessageLookupByLibrary.simpleMessage("Gift"),
    "giftAmount": m99,
    "giftAwardResetTime": MessageLookupByLibrary.simpleMessage(
      "Daily reset at 00:00 (GMT+3)",
    ),
    "giftAwardRules": MessageLookupByLibrary.simpleMessage(
      "1. Only the rooms that reach Level 5 have treasure box.\n2. The box key is obtained by sending diamonds gifts in the room.\n3. When the progress bar of the box key is filled up, the treasure box will be opened.\n4. Everyone in the room has a chance to get the rewards in the treasure box.\n5. It may take a few seconds for the system to deliver the rewards to your account after the treasure box is opened. Diamonds will be added to your balance, Avatar frame, room theme will be added to your backpack.\n6. The higher the level of the treasure box, the bigger the rewards in it.\n7. The progress bar of the box key will be reset at 00:00 (GMT+3) everyday.",
    ),
    "giftBadge": MessageLookupByLibrary.simpleMessage("Gift Badge"),
    "giftBox": MessageLookupByLibrary.simpleMessage("Gift box:"),
    "giftBoxDesc": MessageLookupByLibrary.simpleMessage("Gift Box"),
    "giftDiscountPercent": m100,
    "giftFloatScreen": m101,
    "giftFloatScreenByLottery": m102,
    "giftFrom": MessageLookupByLibrary.simpleMessage("Gifts from"),
    "giftGallery": MessageLookupByLibrary.simpleMessage("Gift gallery"),
    "giftGalleryIntroIlluminationRuleContent": MessageLookupByLibrary.simpleMessage(
      "1. The Gift Exhibition Hall is an interactive gameplay feature in voice rooms. When you receive the specified number of gifts, you can illuminate the corresponding gift (the specific number of gifts required is subject to the information on the page). Click on the gift image to view the gift-giving leaderboard details.\n\n2. The gift-giving leaderboard for individual gifts will only display the top three users. By giving the specified number of gifts, you can have the naming rights for that gift (display range: limited to the gift wall of the recipient users).\n\n3. The gifts in the exhibition hall will be updated irregularly. The newly added gifts will be displayed simultaneously on the gift wall. Giving the specified number of new gifts can also illuminate them.\n",
    ),
    "giftGalleryIntroIlluminationSectionTitle":
        MessageLookupByLibrary.simpleMessage("Gift Illumination Rules"),
    "giftGalleryIntroNameRuleContent": MessageLookupByLibrary.simpleMessage(
      "1. After a gift is illuminated, the user who ranks first on the gift-giving leaderboard can have the naming rights for that gift. The profile picture of the top-ranking user will be displayed below the task of that gift, and the profile picture will be updated in real-time. If multiple users have given the same number of gifts, the user who gave the gift first will be considered the top-ranking user.\n\n2. Once a user successfully names a gift, a public screen notification will be triggered in the room where the gift recipient is currently located.",
    ),
    "giftGalleryIntroNameSectionTitle": MessageLookupByLibrary.simpleMessage(
      "Gift Naming Rules",
    ),
    "giftGalleryIntroductionTitle": MessageLookupByLibrary.simpleMessage(
      "Gift Exhibition Hall Description",
    ),
    "giftGalleryListSubtitle": MessageLookupByLibrary.simpleMessage(
      "Gift will light up when you send the total number required.",
    ),
    "giftGivingDuringTheEvent": MessageLookupByLibrary.simpleMessage(
      "Gift giving during the event",
    ),
    "giftLightUpRank": MessageLookupByLibrary.simpleMessage(
      "Gift light up rank",
    ),
    "giftNewerGuideTip": MessageLookupByLibrary.simpleMessage(
      "You just got this gift, give it to the host you like~",
    ),
    "giftNumUpLimit": m103,
    "giftPk": MessageLookupByLibrary.simpleMessage("Gift PK"),
    "giftReceive": MessageLookupByLibrary.simpleMessage("Gift receive"),
    "giftSendGuid": MessageLookupByLibrary.simpleMessage(
      "Click it to send a gift to show your appreciation!",
    ),
    "giftSendingDetails": MessageLookupByLibrary.simpleMessage(
      "Gift sending details",
    ),
    "giftSent": MessageLookupByLibrary.simpleMessage("Gift sent"),
    "giftSentYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Gift have been sent to your backpack.",
    ),
    "giftUpperLimit": MessageLookupByLibrary.simpleMessage(
      "This gift has reached the upper limit of sending today",
    ),
    "giftValue": MessageLookupByLibrary.simpleMessage("Gift Value:"),
    "giftsCanSendFriends": MessageLookupByLibrary.simpleMessage(
      "Send gifts to the friends you like.",
    ),
    "giftsReceived": MessageLookupByLibrary.simpleMessage("Gift received"),
    "giftsSent": MessageLookupByLibrary.simpleMessage("Gifts sent"),
    "gitGalleryRankSubtitle": MessageLookupByLibrary.simpleMessage(
      "Noce this gift lit up, it’s No.1 gifter will get recognized as its title gifter.",
    ),
    "gitGalleryRankTitle": MessageLookupByLibrary.simpleMessage(
      "Top 3 gifters",
    ),
    "giveUpCard": MessageLookupByLibrary.simpleMessage(
      "Whether to give up your id card?",
    ),
    "globalMute": MessageLookupByLibrary.simpleMessage("Global Mute"),
    "gloryFamily": MessageLookupByLibrary.simpleMessage("Glory Family"),
    "go": MessageLookupByLibrary.simpleMessage("Go"),
    "goCoupleZone": MessageLookupByLibrary.simpleMessage("Go love zone"),
    "goFindRooms": MessageLookupByLibrary.simpleMessage("Go Find Rooms"),
    "goForVerification": MessageLookupByLibrary.simpleMessage(
      "Go for verification",
    ),
    "goProposal": MessageLookupByLibrary.simpleMessage("Go proposal"),
    "goSetting": MessageLookupByLibrary.simpleMessage("Go setting"),
    "goStore": MessageLookupByLibrary.simpleMessage("Go store"),
    "goToChat": MessageLookupByLibrary.simpleMessage("Go to chat"),
    "goToCheck": MessageLookupByLibrary.simpleMessage("Go to check!"),
    "goToFind": MessageLookupByLibrary.simpleMessage("Go to find"),
    "goToGet": MessageLookupByLibrary.simpleMessage("Go to get"),
    "goToMoments": MessageLookupByLibrary.simpleMessage(
      "Go to moments to find interesting mates!",
    ),
    "goToRecharge": MessageLookupByLibrary.simpleMessage("Go to recharge"),
    "goToSetNotifications": MessageLookupByLibrary.simpleMessage(
      "Go to Settings-Winker-Notifications-Allow notifications.",
    ),
    "goToSettings": MessageLookupByLibrary.simpleMessage("Go to settings"),
    "goToSystemSettings": MessageLookupByLibrary.simpleMessage(
      "Go to system settings",
    ),
    "goToVerify": MessageLookupByLibrary.simpleMessage("Go to verify"),
    "goToWear": MessageLookupByLibrary.simpleMessage("Go to wear"),
    "goldFamily": MessageLookupByLibrary.simpleMessage("Gold Family"),
    "goldGet1": MessageLookupByLibrary.simpleMessage(
      "1. Golds can be gained by signing in.",
    ),
    "goldGet2": MessageLookupByLibrary.simpleMessage(
      "2. Golds can be gained by doing tasks and milestones.",
    ),
    "goldTask": MessageLookupByLibrary.simpleMessage("Gold task"),
    "golds": MessageLookupByLibrary.simpleMessage("Golds"),
    "goldsCanSendGift": MessageLookupByLibrary.simpleMessage(
      "Golds can be used to send gifts.",
    ),
    "goldsLowCase": MessageLookupByLibrary.simpleMessage("golds"),
    "goldsUses1": MessageLookupByLibrary.simpleMessage(
      "1. Golds can be used to send gifts.",
    ),
    "goldsUses2": MessageLookupByLibrary.simpleMessage(
      "2. Golds can be used to increase match times.",
    ),
    "goodsHasExpired": MessageLookupByLibrary.simpleMessage(
      "The item has expired",
    ),
    "googlePay": MessageLookupByLibrary.simpleMessage("Google pay"),
    "got": MessageLookupByLibrary.simpleMessage("got"),
    "gotFromTreasureBox": m104,
    "gotIt": MessageLookupByLibrary.simpleMessage("Got it"),
    "gotRechargeRewards": MessageLookupByLibrary.simpleMessage(
      "You got recharge rewards!",
    ),
    "gotten": MessageLookupByLibrary.simpleMessage("Gotten"),
    "grab": MessageLookupByLibrary.simpleMessage("Grab!"),
    "grabMicMode": MessageLookupByLibrary.simpleMessage("Grab mic mode"),
    "grabPoints": m105,
    "grabbedTheMic": MessageLookupByLibrary.simpleMessage("Grabbed the mic!"),
    "grabbing": MessageLookupByLibrary.simpleMessage("grabbing"),
    "grabbingUp": MessageLookupByLibrary.simpleMessage("Grabbing"),
    "greatStart": MessageLookupByLibrary.simpleMessage("Great start"),
    "greaterThanCount": m106,
    "greeting1": MessageLookupByLibrary.simpleMessage("Hi"),
    "greeting2": MessageLookupByLibrary.simpleMessage("Hello Hello~"),
    "greeting3": MessageLookupByLibrary.simpleMessage("Nice to meet you"),
    "groupMessageNotDisturb": MessageLookupByLibrary.simpleMessage(
      "Group message do not disturb",
    ),
    "groupYourInfo": MessageLookupByLibrary.simpleMessage(
      "You can choose your verification audio to be invisible. Are you sure exit?",
    ),
    "growthBadge": MessageLookupByLibrary.simpleMessage("Growth badge"),
    "guessGuideTip": MessageLookupByLibrary.simpleMessage("Input your guess!"),
    "guest": MessageLookupByLibrary.simpleMessage("GUEST"),
    "guestSpeak": MessageLookupByLibrary.simpleMessage("Guests speak"),
    "guideLines": MessageLookupByLibrary.simpleMessage("Guidelines"),
    "guidePostMoment": MessageLookupByLibrary.simpleMessage(
      "Click here to post today story to others!",
    ),
    "guidePublishInput": MessageLookupByLibrary.simpleMessage(
      "Introduce yourself in simple words...",
    ),
    "hall": MessageLookupByLibrary.simpleMessage("Hall"),
    "hangUp": MessageLookupByLibrary.simpleMessage("Hang up"),
    "hangUpConfirm": MessageLookupByLibrary.simpleMessage("Hang up Confirm"),
    "harassment": MessageLookupByLibrary.simpleMessage("Harassment"),
    "hasClosedConversation": MessageLookupByLibrary.simpleMessage(
      "The other side has ended this conversation.",
    ),
    "hasFollowedYou": MessageLookupByLibrary.simpleMessage("has followed you"),
    "hashTag": MessageLookupByLibrary.simpleMessage("Hashtag"),
    "hateSpeech": MessageLookupByLibrary.simpleMessage("Hate speech"),
    "haveDrawDays": m107,
    "haveOpenedLuckyBag": MessageLookupByLibrary.simpleMessage(
      "You have opened this lucky bag",
    ),
    "haveReceivedLuckyBag": MessageLookupByLibrary.simpleMessage(
      "have received the Lucky bag",
    ),
    "haveTogetherDays": m108,
    "haveUncovered": MessageLookupByLibrary.simpleMessage(
      "Both of you have uncovered your Winker profile from Mystery letter.",
    ),
    "he": MessageLookupByLibrary.simpleMessage("he"),
    "heFollowYou": MessageLookupByLibrary.simpleMessage("He’s following you"),
    "heLiving": MessageLookupByLibrary.simpleMessage(
      "He living in: your nearby!",
    ),
    "heWinkedYou": m109,
    "heat": MessageLookupByLibrary.simpleMessage("Heat"),
    "heicNotSupported": MessageLookupByLibrary.simpleMessage(
      "Unsupported HEIC file type.",
    ),
    "helloNiceToMeetYou": MessageLookupByLibrary.simpleMessage(
      "Hello, nice to meet you!",
    ),
    "helpAndFeedback": MessageLookupByLibrary.simpleMessage("Help & feedback"),
    "helpUsImprove": MessageLookupByLibrary.simpleMessage("Help us improve"),
    "her": MessageLookupByLibrary.simpleMessage("her"),
    "hide": MessageLookupByLibrary.simpleMessage("Hide"),
    "highCaseTimes": MessageLookupByLibrary.simpleMessage("Duration"),
    "him": MessageLookupByLibrary.simpleMessage("him"),
    "hintNetError": MessageLookupByLibrary.simpleMessage("Connection failed."),
    "hintsWithContent": m110,
    "his": MessageLookupByLibrary.simpleMessage("his"),
    "history": MessageLookupByLibrary.simpleMessage("History"),
    "holdToSpeak": MessageLookupByLibrary.simpleMessage("Hold to speak"),
    "homeBlindDateStatusNormalTitle": MessageLookupByLibrary.simpleMessage(
      "Join",
    ),
    "homeChatPartySubtitle": MessageLookupByLibrary.simpleMessage(
      "Multi person online chat",
    ),
    "homeCountdownTip": m111,
    "homeFateBellStatusNormalTitle": MessageLookupByLibrary.simpleMessage(
      "Match",
    ),
    "homeFateBellTitle": MessageLookupByLibrary.simpleMessage("Match Chat"),
    "homeLudoSubtitle": MessageLookupByLibrary.simpleMessage(
      "The most popular board game",
    ),
    "homePage": MessageLookupByLibrary.simpleMessage("Homepage"),
    "homePageBackground": MessageLookupByLibrary.simpleMessage(
      "Home page background",
    ),
    "homePageEffect": MessageLookupByLibrary.simpleMessage("Home page effect"),
    "homeStatePopSubtitle": MessageLookupByLibrary.simpleMessage(
      "Your status will expire in 24 hours",
    ),
    "homeStatePopTitle": MessageLookupByLibrary.simpleMessage(
      "Choose your current status",
    ),
    "homeToBottom": MessageLookupByLibrary.simpleMessage(
      "It\'s already at the bottom",
    ),
    "homeTruthDareSubtitle": MessageLookupByLibrary.simpleMessage(
      "Meet interesting friends",
    ),
    "honor": MessageLookupByLibrary.simpleMessage("Honor"),
    "honorBadge": MessageLookupByLibrary.simpleMessage("Honor badge"),
    "honour": MessageLookupByLibrary.simpleMessage("Honour"),
    "host": MessageLookupByLibrary.simpleMessage("Host"),
    "hostColsedTheRoom": MessageLookupByLibrary.simpleMessage(
      "Host closed the room",
    ),
    "hot": MessageLookupByLibrary.simpleMessage("Hot"),
    "hotFamily": MessageLookupByLibrary.simpleMessage("Recommended Family"),
    "hotRoom": MessageLookupByLibrary.simpleMessage("Hot Room"),
    "hottest": MessageLookupByLibrary.simpleMessage("Hottest"),
    "hour": MessageLookupByLibrary.simpleMessage("h"),
    "hourAgo": m112,
    "hours": m113,
    "hoursTimeout": m114,
    "howGetIntimacy": MessageLookupByLibrary.simpleMessage(
      "How to get Intimacy",
    ),
    "howToPlay": MessageLookupByLibrary.simpleMessage("How to Play"),
    "huaweiPay": MessageLookupByLibrary.simpleMessage("Huawei pay"),
    "hungUp": MessageLookupByLibrary.simpleMessage("Hung up"),
    "iAm": MessageLookupByLibrary.simpleMessage("I am"),
    "iAmNewToFamily": MessageLookupByLibrary.simpleMessage(
      "Hi, I am new to the family.",
    ),
    "iDontLikeWinker": MessageLookupByLibrary.simpleMessage(
      "I don\'t like Winker",
    ),
    "iGetIt": MessageLookupByLibrary.simpleMessage("I get it"),
    "iGot": MessageLookupByLibrary.simpleMessage("I got"),
    "iGotIt": MessageLookupByLibrary.simpleMessage("I got it"),
    "iHere": MessageLookupByLibrary.simpleMessage("Hi, i\'m here!"),
    "iKnow": MessageLookupByLibrary.simpleMessage("I Know"),
    "iSentYou": MessageLookupByLibrary.simpleMessage("I sent you"),
    "iSentYouMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "I sent you magic box key",
    ),
    "iStartedLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "I started the Lucky wheel.",
    ),
    "iUnderstand": MessageLookupByLibrary.simpleMessage("I understand"),
    "icebreakerNewbie": m115,
    "id": MessageLookupByLibrary.simpleMessage("ID"),
    "idCardTitle": MessageLookupByLibrary.simpleMessage("Winker ID Card"),
    "identityShowed": MessageLookupByLibrary.simpleMessage("Identity showed"),
    "ifTheFamilyLuckyBagIsNotClaimedWithin": MessageLookupByLibrary.simpleMessage(
      "If the family lucky bag is not claimed within 24 hours, the remaining balance will be reclaimed by the system",
    ),
    "ignore": MessageLookupByLibrary.simpleMessage("Ignore"),
    "ignored": MessageLookupByLibrary.simpleMessage("Ignored"),
    "illegal": MessageLookupByLibrary.simpleMessage("Illegal"),
    "illegalContent": MessageLookupByLibrary.simpleMessage(
      "Your answer have the illegal content, please follow the community rules.",
    ),
    "illegalContentReEdit": MessageLookupByLibrary.simpleMessage(
      "The text contains illegal content, please re-edit",
    ),
    "imCallInstruction": MessageLookupByLibrary.simpleMessage(
      "Follow each other to start voice calling",
    ),
    "imageIllegal": MessageLookupByLibrary.simpleMessage(
      "The image contains illegal content, please re-upload",
    ),
    "imageSizeLimit": m116,
    "imageTooSmall": MessageLookupByLibrary.simpleMessage(
      "The selected image size is too small.",
    ),
    "inCall": MessageLookupByLibrary.simpleMessage("In call"),
    "inCallNotice": MessageLookupByLibrary.simpleMessage(
      "Line busy, please try later",
    ),
    "inChatRoom": m117,
    "inDraw": MessageLookupByLibrary.simpleMessage("in draw"),
    "inGameTag": m118,
    "inPk": MessageLookupByLibrary.simpleMessage("in Pk"),
    "inReview": MessageLookupByLibrary.simpleMessage("In Review"),
    "inRoomCannotCallMatch": MessageLookupByLibrary.simpleMessage(
      "You cannot call match in chat room.",
    ),
    "inTheRoomNow": MessageLookupByLibrary.simpleMessage("In the room now"),
    "inWord": MessageLookupByLibrary.simpleMessage("in"),
    "inappropriateName": MessageLookupByLibrary.simpleMessage(
      "inappropriate nickname",
    ),
    "inappropriatePhotos": MessageLookupByLibrary.simpleMessage(
      "inappropriate photos",
    ),
    "income": MessageLookupByLibrary.simpleMessage("Income"),
    "incomeTasks": MessageLookupByLibrary.simpleMessage("Income tasks"),
    "incomplete": MessageLookupByLibrary.simpleMessage("Incomplete"),
    "increaseContent": m119,
    "incredible": MessageLookupByLibrary.simpleMessage("Incredible!"),
    "inputAnswerHere": MessageLookupByLibrary.simpleMessage(
      "Input answer here...",
    ),
    "inputCannotBeNull": MessageLookupByLibrary.simpleMessage(
      "The input cannot be empty",
    ),
    "instagram": MessageLookupByLibrary.simpleMessage("Instagram"),
    "install": MessageLookupByLibrary.simpleMessage("Install"),
    "instructionChatLuckyBag": MessageLookupByLibrary.simpleMessage(
      "1、Steps to send a lucky bag: choose the quantity of the gifts or diamonds to be put into a luck bag then choose the number of recepients, finally tap the send buttion.\n2、Each recipients will get a random quantity of gifts or diamonds from the lucky bag. Diamonds will be sent to your wallet and gifts will sent to your backpack. Please check the details in chat notification.",
    ),
    "instructionDetail": MessageLookupByLibrary.simpleMessage(
      "1、Steps to send a lucky bag: choose the quantity of the gifts or diamonds to be put into a luck bag then choose the number of recepients, finally tap the send buttion.\n2、Each recipients will get a random quantity of gifts or diamonds from the lucky bag. Diamonds will be sent to your wallet and gifts will sent to your backpack. Please check the details in chat notification.\n3、A lucky bag invalid for 10 minutes. The gifts or diamonds in a lucky bag if not claimed within 10 minutes will be refunded to the sender.",
    ),
    "instructions": MessageLookupByLibrary.simpleMessage("Instructions"),
    "insufficientBalance": MessageLookupByLibrary.simpleMessage(
      "Insufficient balance",
    ),
    "insufficientBalanceTip": m120,
    "insufficientCoins": MessageLookupByLibrary.simpleMessage(
      "Insufficient coins, recharge now!",
    ),
    "insufficientDiamonds": MessageLookupByLibrary.simpleMessage(
      "Insufficient diamonds, recharge now!",
    ),
    "insufficientNumberGrabMic": MessageLookupByLibrary.simpleMessage(
      "Insufficient number of people to grab mic",
    ),
    "interact": MessageLookupByLibrary.simpleMessage("Interact"),
    "interestDesTip": MessageLookupByLibrary.simpleMessage(
      "Choose a label to quickly showcase yourself.",
    ),
    "interests": MessageLookupByLibrary.simpleMessage("Interests"),
    "intervalSessionsShort": MessageLookupByLibrary.simpleMessage(
      "The interval between sessions is too short",
    ),
    "intimacy": MessageLookupByLibrary.simpleMessage("Intimacy"),
    "intimacyBottomTips": MessageLookupByLibrary.simpleMessage(
      "Chat intimacy is consistent with intimacy . Gifts sent in chat and voice rooms both increase intimacy.",
    ),
    "intimacyImageTipsTitle1": MessageLookupByLibrary.simpleMessage(
      "Couple center for proposal",
    ),
    "intimacyImageTipsTitle2": MessageLookupByLibrary.simpleMessage(
      "Propose / Divorce entrance",
    ),
    "intimacyImageTipsTitle3": MessageLookupByLibrary.simpleMessage(
      "Couple profile reference",
    ),
    "intimacyImageTipsTitle4": MessageLookupByLibrary.simpleMessage(
      "Love zone reference",
    ),
    "intimacyInfoTips1": MessageLookupByLibrary.simpleMessage(
      "1、First, you can find the opposite sex friends you want to make through the match or chat room.",
    ),
    "intimacyInfoTips2": MessageLookupByLibrary.simpleMessage(
      "2、Chatting and gifting within the Romance category can increase the intimacy of both parties. The daily limit for chatting with a partner is 50, and gifting is unlimited. After 5,000, you can make a proposal.",
    ),
    "intimacyInfoTips3": MessageLookupByLibrary.simpleMessage(
      "3、If you have no interaction with the other person after 7 days, the intimacy will drop by 10 points every day, to a minimum of 0.",
    ),
    "intimacyInfoTips4": MessageLookupByLibrary.simpleMessage(
      "4、You need to purchase a ring in the store to propose to a friend of the opposite sex. When the other person agrees your proposal to become a couple. If the other party does not deal with the proposal for 3 days, it will be invalid automatically. If you propose failed, the ring will be returned to your backpack.",
    ),
    "intimacyInfoTips5": MessageLookupByLibrary.simpleMessage(
      "5、If you terminate the couple relationship, you can apply to the other party to terminate the relationship with mutual consent. The ring will disappear when the relationship is dissolved. You have to wait three days before you can propose again.",
    ),
    "intimacyInfoTitle1": MessageLookupByLibrary.simpleMessage(
      "Just three steps, get a soulmate!",
    ),
    "intimacyInfoTitle2": MessageLookupByLibrary.simpleMessage(
      "Details for couple",
    ),
    "intimacyLevelIncreasedTo": MessageLookupByLibrary.simpleMessage(
      "Intimacy level increased to",
    ),
    "intimacyLowerCase": MessageLookupByLibrary.simpleMessage("intimacy"),
    "intimacyProposal": MessageLookupByLibrary.simpleMessage(
      "Only the intimacy over 5000 can proposal",
    ),
    "intimacyRank": MessageLookupByLibrary.simpleMessage("Intimate Ranking"),
    "intimacyRanking": MessageLookupByLibrary.simpleMessage("Intimacy ranking"),
    "intimacyRewardTips": MessageLookupByLibrary.simpleMessage(
      "Congrats! \nYou both intimacy increase",
    ),
    "intimacyRule1": MessageLookupByLibrary.simpleMessage(
      "A gift worth 3 diamonds = 1 intimacy point.",
    ),
    "intimacyRule2": MessageLookupByLibrary.simpleMessage(
      "A gift worth 40 gold coins = 1 intimacy point conversion.",
    ),
    "intimacyScoreIncrease": MessageLookupByLibrary.simpleMessage(
      "Congrats! The intimacy between you has increased by ",
    ),
    "intimacySecondStepTips": MessageLookupByLibrary.simpleMessage(
      "Chat or Romance gifts increase the intimacy",
    ),
    "intimacyThirdStepTips": MessageLookupByLibrary.simpleMessage(
      "Propose for your mate",
    ),
    "intimacyTip": MessageLookupByLibrary.simpleMessage(
      "There are currently no candidates available. Rewarding the other party can enhance intimacy.",
    ),
    "intimacyTips": MessageLookupByLibrary.simpleMessage(
      "Intimacy over 5000 can make proposal to become couple",
    ),
    "intimacyWithScore": m121,
    "intimateRanking": MessageLookupByLibrary.simpleMessage("Intimate Ranking"),
    "introduce": MessageLookupByLibrary.simpleMessage("Introduce"),
    "invalid": MessageLookupByLibrary.simpleMessage("Invalid"),
    "invalidDescription": MessageLookupByLibrary.simpleMessage(
      "Invalid description",
    ),
    "invalidMobileNumber": MessageLookupByLibrary.simpleMessage(
      "Invalid mobile number",
    ),
    "invalidName": MessageLookupByLibrary.simpleMessage("Invalid name"),
    "invalidTime": MessageLookupByLibrary.simpleMessage("Invalid time"),
    "invitation": MessageLookupByLibrary.simpleMessage("Invite mic"),
    "invitationCode": MessageLookupByLibrary.simpleMessage("Invitation code"),
    "invitationHasSent": MessageLookupByLibrary.simpleMessage(
      "Invitation has been sent.",
    ),
    "invitationShort": MessageLookupByLibrary.simpleMessage("Invitation"),
    "invitationToMic": MessageLookupByLibrary.simpleMessage(
      "Invitation to Mic",
    ),
    "invite": MessageLookupByLibrary.simpleMessage("Invite"),
    "inviteFriends": MessageLookupByLibrary.simpleMessage("Invite friends"),
    "inviteJoinPerformer": m122,
    "inviteJoinRoom": MessageLookupByLibrary.simpleMessage("Invite to chat!"),
    "inviteJoinRoomClub": MessageLookupByLibrary.simpleMessage(
      "Invite you to join the room club！",
    ),
    "inviteParticipateEvent": m123,
    "invitePk": MessageLookupByLibrary.simpleMessage("Invite PK"),
    "invitePlayDomino": MessageLookupByLibrary.simpleMessage(
      "Invite to play Dominoes",
    ),
    "invitePlayDrawGuess": MessageLookupByLibrary.simpleMessage(
      "Play Draw&Guess",
    ),
    "invitePlayKnife": MessageLookupByLibrary.simpleMessage(
      "Invite to knife challenge",
    ),
    "invitePlayLudo": MessageLookupByLibrary.simpleMessage(
      "Invite to play Ludo",
    ),
    "inviteRoomOnlineAlertTitle": m124,
    "inviteRoomOnlineEmpty": MessageLookupByLibrary.simpleMessage(
      "There are no other online users in the room",
    ),
    "inviteRoomOnlineUsers": MessageLookupByLibrary.simpleMessage(
      "Recruit Room Online Users",
    ),
    "inviteSuccess": MessageLookupByLibrary.simpleMessage(
      "Invite successfully",
    ),
    "inviteToEnable": MessageLookupByLibrary.simpleMessage("Invite to enable"),
    "inviteToGuest": MessageLookupByLibrary.simpleMessage("Invite to guest"),
    "inviteToHost": MessageLookupByLibrary.simpleMessage("Invite to host"),
    "inviteToList": MessageLookupByLibrary.simpleMessage("Invite to list"),
    "inviteToMicMessage": MessageLookupByLibrary.simpleMessage(
      "Invite you join discuss",
    ),
    "inviteToTalent": MessageLookupByLibrary.simpleMessage(
      "Invite to watch talent show",
    ),
    "inviteToTruthDare": MessageLookupByLibrary.simpleMessage(
      "Invite to play Truth&Dare",
    ),
    "inviteToWatchVideo": MessageLookupByLibrary.simpleMessage(
      "Invite to watch video",
    ),
    "inviteUndercover": MessageLookupByLibrary.simpleMessage(
      "Invite to play Undercover",
    ),
    "inviteYouChat": MessageLookupByLibrary.simpleMessage("Invite you to chat"),
    "inviteYouForDate": MessageLookupByLibrary.simpleMessage(
      "Invite you for a date",
    ),
    "inviteYouForTalent": MessageLookupByLibrary.simpleMessage(
      "Invite you for talent show",
    ),
    "inviteYouJoinFamily": MessageLookupByLibrary.simpleMessage(
      "invites you to join the family",
    ),
    "inviteYouJoinRoom": m125,
    "inviteYouPlayGames": MessageLookupByLibrary.simpleMessage(
      "Invite you play games",
    ),
    "inviteYouToChat": MessageLookupByLibrary.simpleMessage(
      "Invite you to voice chat",
    ),
    "inviteYouToGame": MessageLookupByLibrary.simpleMessage(
      "Invite you to game",
    ),
    "inviteYouToTakeMic": m126,
    "inviteYouWatchVideo": MessageLookupByLibrary.simpleMessage(
      "Invite you watch a video",
    ),
    "inviteYourFriends": MessageLookupByLibrary.simpleMessage(
      "Invite your friends",
    ),
    "invited": MessageLookupByLibrary.simpleMessage("Invited"),
    "invitedOnly": MessageLookupByLibrary.simpleMessage("Invited only"),
    "invitedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Invited successfully",
    ),
    "invitedToJoinFamily": MessageLookupByLibrary.simpleMessage(
      "+ Invited to join family",
    ),
    "ironFamily": MessageLookupByLibrary.simpleMessage("Iron Family"),
    "isDropping": MessageLookupByLibrary.simpleMessage("is dropping"),
    "isSending": MessageLookupByLibrary.simpleMessage("is sending"),
    "issued": MessageLookupByLibrary.simpleMessage("Issued"),
    "itsMatch": MessageLookupByLibrary.simpleMessage("It\'s a match!"),
    "jackPot": MessageLookupByLibrary.simpleMessage("JackPot"),
    "jackPotTips": MessageLookupByLibrary.simpleMessage(
      "Completing tasks earns points，which can be redeemed for rewards.",
    ),
    "jackpotMode": MessageLookupByLibrary.simpleMessage("Jackpot mode"),
    "jackpotModeRule": MessageLookupByLibrary.simpleMessage(
      "PK joiner will not receive gift refund during the PK. PK Winner will get 30% gift value after the PK.",
    ),
    "join": MessageLookupByLibrary.simpleMessage("Join"),
    "joinFamilyErrorTitle": MessageLookupByLibrary.simpleMessage(
      "You do not meet the following requirement to join this family:",
    ),
    "joinFamilySuccess": MessageLookupByLibrary.simpleMessage(
      "Joined family successfully.",
    ),
    "joinFanClubSuc": MessageLookupByLibrary.simpleMessage(
      "Successfully joined the fan club!",
    ),
    "joinFanGroup": MessageLookupByLibrary.simpleMessage("Join a fan group"),
    "joinMode": MessageLookupByLibrary.simpleMessage("Join mode"),
    "joinMyFamilySuccess": MessageLookupByLibrary.simpleMessage(
      "join the family successfully!",
    ),
    "joinNow": MessageLookupByLibrary.simpleMessage("Join now"),
    "joinRoomClub": MessageLookupByLibrary.simpleMessage("Join the room club"),
    "joinTheFamily": MessageLookupByLibrary.simpleMessage("Join the family"),
    "joinTheLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Join the Lucky wheel",
    ),
    "joinTheRoom": MessageLookupByLibrary.simpleMessage("Join the room"),
    "joinWealthLevel": MessageLookupByLibrary.simpleMessage(
      "User Wealth Level Limit",
    ),
    "joinWinkerDays": m127,
    "joined": MessageLookupByLibrary.simpleMessage("Joined"),
    "joinedTheFanGroup": MessageLookupByLibrary.simpleMessage(
      "joined the fan group",
    ),
    "justListenSwipeLike": MessageLookupByLibrary.simpleMessage(
      "Just listen the voice from other user, swipe right if you liked it.",
    ),
    "keep": MessageLookupByLibrary.simpleMessage("Keep"),
    "keepDeviceCalendar": MessageLookupByLibrary.simpleMessage(
      "Keep tracking this event by adding it to your device\'s calendar",
    ),
    "keepEditing": MessageLookupByLibrary.simpleMessage("Keep Editing"),
    "keepPosting": MessageLookupByLibrary.simpleMessage("Keep posting"),
    "keepSending": MessageLookupByLibrary.simpleMessage("Keep sending"),
    "keepStaying": MessageLookupByLibrary.simpleMessage("Stay"),
    "keepTalking": MessageLookupByLibrary.simpleMessage("Keep talking"),
    "keysAndDiamondsOpenThisBox": m128,
    "keysCanBeGifted": m129,
    "keysGiftedByOthers": m130,
    "keysOpenThisBox": m131,
    "kickOut": MessageLookupByLibrary.simpleMessage(
      "Your account logged in on another device.",
    ),
    "kickOutList": MessageLookupByLibrary.simpleMessage("Kick out list"),
    "kicked": MessageLookupByLibrary.simpleMessage("Kicked"),
    "kickedOutFamilyBy": MessageLookupByLibrary.simpleMessage(
      "was kicked out of the family by",
    ),
    "kindTips": MessageLookupByLibrary.simpleMessage("Kind tips"),
    "king": MessageLookupByLibrary.simpleMessage("King"),
    "knifeChallenge": MessageLookupByLibrary.simpleMessage("Knife game"),
    "knight": MessageLookupByLibrary.simpleMessage("Knight"),
    "ktv": MessageLookupByLibrary.simpleMessage("KTV"),
    "labels": MessageLookupByLibrary.simpleMessage("Labels"),
    "lackOfCoins": MessageLookupByLibrary.simpleMessage(
      "Lack of golds! please finish more tasks",
    ),
    "lackOfCoinsPleaseFinishMoreTasksOrExchangeDiamonds":
        MessageLookupByLibrary.simpleMessage(
          "Lack of golds! please finish more tasks or exchange diamonds to get golds.",
        ),
    "lackOfGems": MessageLookupByLibrary.simpleMessage(
      "Lack of rubies! please finish more tasks to get rubies.",
    ),
    "lastUpdateTime": m132,
    "lastWeek": MessageLookupByLibrary.simpleMessage("Last week"),
    "later": MessageLookupByLibrary.simpleMessage("Later"),
    "latest": MessageLookupByLibrary.simpleMessage("Latest"),
    "latestVersion": MessageLookupByLibrary.simpleMessage(
      "Your current app is the latest version.",
    ),
    "leave": MessageLookupByLibrary.simpleMessage("Leave"),
    "leaveChat": MessageLookupByLibrary.simpleMessage("Leave chat"),
    "leaveMicStopPerforming": MessageLookupByLibrary.simpleMessage(
      "Leave mic will stop performing. Still leave?",
    ),
    "leaveNowWillCloseRoom": MessageLookupByLibrary.simpleMessage(
      "leave now will close the room.",
    ),
    "leaveRoomStopPerforming": MessageLookupByLibrary.simpleMessage(
      "Leave room will stop performing. still leave?",
    ),
    "leaveTheFamily": MessageLookupByLibrary.simpleMessage("leave the family."),
    "leaveTheList": MessageLookupByLibrary.simpleMessage("Leave the list"),
    "leaveTheMic": MessageLookupByLibrary.simpleMessage("Leave the Mic"),
    "lengthOfPurchase": MessageLookupByLibrary.simpleMessage("Validity period"),
    "less": MessageLookupByLibrary.simpleMessage("Less"),
    "lessThanOneMinute": MessageLookupByLibrary.simpleMessage("< 1 minute"),
    "letTheGameStart": MessageLookupByLibrary.simpleMessage(
      "Let the game start",
    ),
    "letUsKnowTheReasonYouLeave": MessageLookupByLibrary.simpleMessage(
      "Please let us know the reason you are leaving.",
    ),
    "letter": MessageLookupByLibrary.simpleMessage("Letter paper"),
    "level": MessageLookupByLibrary.simpleMessage("Level"),
    "levelIsIncreasing": MessageLookupByLibrary.simpleMessage(
      "Your level is increasing at standard speed.",
    ),
    "levelMax": MessageLookupByLibrary.simpleMessage("Level Max"),
    "levelMedal": MessageLookupByLibrary.simpleMessage("Level Medal"),
    "levelPrivilege": MessageLookupByLibrary.simpleMessage("Level privilege"),
    "levelPrivilegeTip1": MessageLookupByLibrary.simpleMessage(
      "1、Get more attention on your level medal.",
    ),
    "levelPrivilegeTip2": MessageLookupByLibrary.simpleMessage(
      "2、More features can be explored.",
    ),
    "levelUnlock": m133,
    "levelUpRewards": MessageLookupByLibrary.simpleMessage("Level Up rewards"),
    "levelUpSpeed": MessageLookupByLibrary.simpleMessage("Level up speed"),
    "levelUpSpeedDesc": MessageLookupByLibrary.simpleMessage(
      "Gain more level experience.",
    ),
    "levelUpgradeTitle": MessageLookupByLibrary.simpleMessage(
      "Congrats! Your level up to",
    ),
    "lightingGift": m134,
    "link": MessageLookupByLibrary.simpleMessage("Link"),
    "listDescription": MessageLookupByLibrary.simpleMessage("List description"),
    "listenToTheVoiceQuestion": MessageLookupByLibrary.simpleMessage(
      "Listen to the question and select your answer",
    ),
    "listener": MessageLookupByLibrary.simpleMessage("Listener"),
    "live": MessageLookupByLibrary.simpleMessage("Live"),
    "liveNow": MessageLookupByLibrary.simpleMessage("Live Now"),
    "liveRoomShareContent": m135,
    "liveRoomShareContentAndPwd": m136,
    "livingIn": MessageLookupByLibrary.simpleMessage("Living in"),
    "loadFailed": MessageLookupByLibrary.simpleMessage("Loading Failed"),
    "loadGameFailed": m137,
    "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
    "loadingNum": m138,
    "localMusic": MessageLookupByLibrary.simpleMessage("Local Music"),
    "locate": MessageLookupByLibrary.simpleMessage("Locate"),
    "location": MessageLookupByLibrary.simpleMessage("Location"),
    "locationServiceTipDes": MessageLookupByLibrary.simpleMessage(
      "To match with nearby users, please enable location services in your phone settings.",
    ),
    "locationServiceTipTitle": MessageLookupByLibrary.simpleMessage(
      "Location service is not enabled",
    ),
    "locationServiceTurnOn": MessageLookupByLibrary.simpleMessage(
      "Turn on location",
    ),
    "lock": MessageLookupByLibrary.simpleMessage("Lock"),
    "lockTheMic": MessageLookupByLibrary.simpleMessage("Lock the Mic"),
    "loginAgree": MessageLookupByLibrary.simpleMessage(
      "By log in, you agree to",
    ),
    "loginFailed": MessageLookupByLibrary.simpleMessage(
      "Login failed, please try another way.",
    ),
    "loginSlogan": MessageLookupByLibrary.simpleMessage(
      "Right place to meet right person",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Log out"),
    "logoutAlert": MessageLookupByLibrary.simpleMessage(
      "Once log out, you\'ll no longer receive any new messages.",
    ),
    "loveZone": MessageLookupByLibrary.simpleMessage("Love Zone"),
    "loveZoneAnswer1": MessageLookupByLibrary.simpleMessage(
      "1、How to have couple love zone?",
    ),
    "loveZoneAnswer2": MessageLookupByLibrary.simpleMessage(
      "2、What is the effect to change the ring of wearing?",
    ),
    "loveZoneAnswer3": MessageLookupByLibrary.simpleMessage(
      "3、How to increase the bless value?",
    ),
    "loveZoneQA": MessageLookupByLibrary.simpleMessage("Love zone Q&A "),
    "loveZoneQuestion1": MessageLookupByLibrary.simpleMessage(
      "First, you need have an opposite sex friend over 5000 intimacy then you can propose for her/him. When the other party agrees to your proposal, you can have a couple love zone.",
    ),
    "loveZoneQuestion2": MessageLookupByLibrary.simpleMessage(
      "New ring effect will show in your profile and love zone.",
    ),
    "loveZoneQuestion3": MessageLookupByLibrary.simpleMessage(
      "Sending gifts in the Love Zone can increases the bless value. But the zone owner can not get the diamonds rebate and charisma.",
    ),
    "luck": MessageLookupByLibrary.simpleMessage("Luck"),
    "luckiestDraw": MessageLookupByLibrary.simpleMessage("Luckiest draw"),
    "luckyBag": MessageLookupByLibrary.simpleMessage("Lucky bag"),
    "luckyBagCharge": MessageLookupByLibrary.simpleMessage(
      "5% commission is charged for each diamond lucky bag",
    ),
    "luckyBagClaimed": m139,
    "luckyBagDetails": MessageLookupByLibrary.simpleMessage(
      "Lucky bag details",
    ),
    "luckyBagEmpty": MessageLookupByLibrary.simpleMessage(
      "Sorry, the lucky bag is empty..",
    ),
    "luckyBagFrom": MessageLookupByLibrary.simpleMessage("Lucky bag from:"),
    "luckyBagHasExpired": MessageLookupByLibrary.simpleMessage(
      "The lucky bag has expired",
    ),
    "luckyBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Lucky bag is empty.",
    ),
    "luckyBagRecord": MessageLookupByLibrary.simpleMessage("Lucky Bag Record"),
    "luckyBonusNum": MessageLookupByLibrary.simpleMessage("Lucky bonus:"),
    "luckyNumber": MessageLookupByLibrary.simpleMessage("Lucky number"),
    "luckySign": MessageLookupByLibrary.simpleMessage("Lucky sign"),
    "luckyWheel": MessageLookupByLibrary.simpleMessage("Lucky Wheel"),
    "luckyWheelIsAutoEnd": MessageLookupByLibrary.simpleMessage(
      "Due to the Lucky Wheel hasn’t started for a long time, the event is closed. Diamonds has been returned to your balance.",
    ),
    "luckyWheelIsEnd": MessageLookupByLibrary.simpleMessage(
      "The Lucky wheel is ended.",
    ),
    "luckyWheelIsEndAndReturnYourBalance": MessageLookupByLibrary.simpleMessage(
      "The Lucky Wheel is ended. Diamonds has been returned to your balance. ",
    ),
    "luckyWheelRules": MessageLookupByLibrary.simpleMessage(
      "1. Only the room owner and admin can start the Lucky Wheel and set the entry fee.\n\n2. After being started, the Lucky Wheel requires a minimum of 3 participants within 5 minutes to start spinning.\n\n3. If there are 3 or more participants within 5 minutes, the room owner ann admin can manually spin the Lucky wheel, or the Lucky wheel will start spinning automatically 5 minutes after being started. If there are less than 3 participants within 5 minutes, the Lucky Wheel will automatically closed and the entry fee will be refunded.\n\n4. When the Lucky Wheel starts spinning, it will randomly pick an eliminated participant until there\'s only one participant left. This participant wins 90% of the total entry fee.",
    ),
    "luckyWheelWinner": m140,
    "ludo": MessageLookupByLibrary.simpleMessage("Ludo"),
    "ludoGame": MessageLookupByLibrary.simpleMessage("Ludo game"),
    "ludoMatch": MessageLookupByLibrary.simpleMessage("Ludo Match"),
    "lv": MessageLookupByLibrary.simpleMessage("Lv."),
    "lvNum": m141,
    "lvUnLock": m142,
    "magicBox": MessageLookupByLibrary.simpleMessage("Magic box"),
    "magicBoxTask": MessageLookupByLibrary.simpleMessage("Magic box task"),
    "mainCountry": MessageLookupByLibrary.simpleMessage("Indonesia"),
    "makeAProposal": MessageLookupByLibrary.simpleMessage("Make a proposal"),
    "makeFirstStarted": MessageLookupByLibrary.simpleMessage(
      "Click to quickly initiate a chat",
    ),
    "makeFriends": MessageLookupByLibrary.simpleMessage("Make friends"),
    "makeGoodImpression": MessageLookupByLibrary.simpleMessage(
      "Send gifts to make a good impression.",
    ),
    "male": MessageLookupByLibrary.simpleMessage("Male"),
    "maleShareGuideContent": MessageLookupByLibrary.simpleMessage(
      "Find intimate friendship!",
    ),
    "mall": MessageLookupByLibrary.simpleMessage("Mall"),
    "manageSticker": MessageLookupByLibrary.simpleMessage(
      "More stickers can be managed and added in \"Collection management\".",
    ),
    "managed": MessageLookupByLibrary.simpleMessage("Managed"),
    "manager": MessageLookupByLibrary.simpleMessage("Manager"),
    "master": MessageLookupByLibrary.simpleMessage("Elders"),
    "match": MessageLookupByLibrary.simpleMessage("Match"),
    "matchAgain": MessageLookupByLibrary.simpleMessage("Match again"),
    "matchCountEmptyDesc": MessageLookupByLibrary.simpleMessage(
      "Please come tomorrow to meet new friends",
    ),
    "matchCountEmptyTitle": MessageLookupByLibrary.simpleMessage(
      "You used up all chances today",
    ),
    "matchFailed": MessageLookupByLibrary.simpleMessage("Match failed"),
    "matchFilter": MessageLookupByLibrary.simpleMessage("Select match type"),
    "matchFriends": MessageLookupByLibrary.simpleMessage("Find friends"),
    "matchGuide": MessageLookupByLibrary.simpleMessage(
      "Tap to start chatting with new friend",
    ),
    "matchInRoomError": MessageLookupByLibrary.simpleMessage(
      "Game matching is not supported in the room",
    ),
    "matchLikeEachOther": MessageLookupByLibrary.simpleMessage(
      "You will have a match only if you both like each other.Try it!",
    ),
    "matchNow": MessageLookupByLibrary.simpleMessage("Match now"),
    "matchPk": MessageLookupByLibrary.simpleMessage("Match PK"),
    "matchSucceeds": MessageLookupByLibrary.simpleMessage("Match succeeds"),
    "matchSuccess": MessageLookupByLibrary.simpleMessage("Match successfully"),
    "matchSwitch": MessageLookupByLibrary.simpleMessage("Match switch"),
    "matchSwitchContent": MessageLookupByLibrary.simpleMessage(
      "Close will not match people for you.",
    ),
    "matchTimeout": MessageLookupByLibrary.simpleMessage("Match timeout"),
    "matchTimesIsOver": MessageLookupByLibrary.simpleMessage(
      "Today\'s match times is over. Please wait until tomorrow.",
    ),
    "matchedImTips": MessageLookupByLibrary.simpleMessage(
      "Match you from the voice test.",
    ),
    "matchedYouByVoice": m143,
    "matching": MessageLookupByLibrary.simpleMessage("Matching"),
    "matchingASuitableRoom": MessageLookupByLibrary.simpleMessage(
      "Matching a suitable room for you",
    ),
    "matchingHqUsers": MessageLookupByLibrary.simpleMessage(
      "Matching high-quality users",
    ),
    "matchingPeoplePlayWith": MessageLookupByLibrary.simpleMessage(
      "Matching people to play with",
    ),
    "matesOnlineNow": MessageLookupByLibrary.simpleMessage("Active now"),
    "max": MessageLookupByLibrary.simpleMessage("Max"),
    "maxSelectOption": m144,
    "me": MessageLookupByLibrary.simpleMessage("Me"),
    "mediasOverSize": MessageLookupByLibrary.simpleMessage(
      "Size of a single video cannot exceed 20 MB",
    ),
    "member": MessageLookupByLibrary.simpleMessage("Member"),
    "message": MessageLookupByLibrary.simpleMessage("Message"),
    "messageNotification": MessageLookupByLibrary.simpleMessage(
      "Message Notification",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("Messages"),
    "micAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Mic access not enabled",
    ),
    "micApplyHasSent": MessageLookupByLibrary.simpleMessage(
      "Take mic application has been sent",
    ),
    "micApplyNotice": MessageLookupByLibrary.simpleMessage("Apply notice"),
    "micIsLocked": MessageLookupByLibrary.simpleMessage(
      "Mic position is locked",
    ),
    "micLayoutUse": m145,
    "micPermission": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" would like to access your Microphone and collect your voice data to enable voice message, identity verification only when the app is in use.",
    ),
    "microphoneOff": MessageLookupByLibrary.simpleMessage("Microphone off"),
    "microphoneOn": MessageLookupByLibrary.simpleMessage("Microphone on"),
    "mics_10": MessageLookupByLibrary.simpleMessage("10 Mics"),
    "mics_2": MessageLookupByLibrary.simpleMessage("2 Mics"),
    "mics_5": MessageLookupByLibrary.simpleMessage("5 Mics"),
    "mics_8_2": MessageLookupByLibrary.simpleMessage("8+2 Mics"),
    "mics_9": MessageLookupByLibrary.simpleMessage("9 Mics"),
    "mightContainRisk": MessageLookupByLibrary.simpleMessage(
      "This message might contain unfriendly or risky content",
    ),
    "milestoneListTasks": MessageLookupByLibrary.simpleMessage(
      "Milestone Tasks",
    ),
    "milestones": MessageLookupByLibrary.simpleMessage("Milestones"),
    "minExchangeDiamond": MessageLookupByLibrary.simpleMessage(
      "Minimum 10 Diamonds",
    ),
    "minExchangeGems": MessageLookupByLibrary.simpleMessage(
      "Minimum 10 rubies",
    ),
    "minSelectOption": m146,
    "mine": MessageLookupByLibrary.simpleMessage("Mine"),
    "mineMenuBackpack": MessageLookupByLibrary.simpleMessage("Bag"),
    "miniPostal": MessageLookupByLibrary.simpleMessage("Mini postal"),
    "minimize": MessageLookupByLibrary.simpleMessage("Minimize"),
    "mins": MessageLookupByLibrary.simpleMessage("mins"),
    "minute": MessageLookupByLibrary.simpleMessage("min"),
    "minuteAgo": m147,
    "minutes": m148,
    "missWinker": MessageLookupByLibrary.simpleMessage("Miss Winker"),
    "mobileNumberConfirmation": MessageLookupByLibrary.simpleMessage(
      "Mobile number confirmation",
    ),
    "mode": MessageLookupByLibrary.simpleMessage("Mode"),
    "modeWillBeEnd": MessageLookupByLibrary.simpleMessage(
      "This mode will be ended. You can switch to other mode after ending.",
    ),
    "moment": MessageLookupByLibrary.simpleMessage("Moment"),
    "momentDetailViewProfile": MessageLookupByLibrary.simpleMessage(
      "View Profile",
    ),
    "momentNoExist": MessageLookupByLibrary.simpleMessage(
      "The post has been deleted",
    ),
    "moments": MessageLookupByLibrary.simpleMessage("Moments"),
    "money": MessageLookupByLibrary.simpleMessage("Money"),
    "month": MessageLookupByLibrary.simpleMessage("Month"),
    "monthly": MessageLookupByLibrary.simpleMessage("Monthly"),
    "monthlyContribution": MessageLookupByLibrary.simpleMessage(
      "Monthly Contribution",
    ),
    "mood": MessageLookupByLibrary.simpleMessage("Mood"),
    "more": MessageLookupByLibrary.simpleMessage("More"),
    "moreFreeChance": MessageLookupByLibrary.simpleMessage("More free chance"),
    "morePriority": MessageLookupByLibrary.simpleMessage(
      "One step away from getting more priority to chat with others.",
    ),
    "moreStickers": MessageLookupByLibrary.simpleMessage("More stickers"),
    "moreTasks": MessageLookupByLibrary.simpleMessage("More tasks"),
    "moreThan3ParticipantsToStartLuckyWheel": m149,
    "moreVerifiedUser": MessageLookupByLibrary.simpleMessage(
      "More verified user",
    ),
    "moreWaysToLogIn": MessageLookupByLibrary.simpleMessage(
      "More ways to login>",
    ),
    "moveFront": MessageLookupByLibrary.simpleMessage("Move to front"),
    "moveTheme": MessageLookupByLibrary.simpleMessage("Move Theme"),
    "moveThemeSucc": MessageLookupByLibrary.simpleMessage("Move Successfully"),
    "moveToAnotherTheme": MessageLookupByLibrary.simpleMessage(
      "Move to another theme",
    ),
    "mrWinker": MessageLookupByLibrary.simpleMessage("Mr.Winker"),
    "msgDescActivity": MessageLookupByLibrary.simpleMessage("[Activity]"),
    "msgDescAudio": MessageLookupByLibrary.simpleMessage("[Voice]"),
    "msgDescAudioCall": MessageLookupByLibrary.simpleMessage("[Audio call]"),
    "msgDescChatMode": MessageLookupByLibrary.simpleMessage(
      "[Change chat mode]",
    ),
    "msgDescExpression": MessageLookupByLibrary.simpleMessage("[sticker]"),
    "msgDescGift": MessageLookupByLibrary.simpleMessage("[Gift]"),
    "msgDescImage": MessageLookupByLibrary.simpleMessage("[Photo]"),
    "msgDescLuckyBag": MessageLookupByLibrary.simpleMessage("[Lucky bag]"),
    "msgDescQuestionBox": MessageLookupByLibrary.simpleMessage(
      "[Question game]",
    ),
    "msgDescReward": MessageLookupByLibrary.simpleMessage("[Reward]"),
    "msgDescShare": MessageLookupByLibrary.simpleMessage("[Share]"),
    "msgDescSystem": MessageLookupByLibrary.simpleMessage("[Notification]"),
    "msgDescUncoverIdentity": MessageLookupByLibrary.simpleMessage(
      "[Uncover Winker profile]",
    ),
    "msgDescUnknown": MessageLookupByLibrary.simpleMessage(
      "You received a new message that unsupported in the current version, please update.",
    ),
    "msgDescVideo": MessageLookupByLibrary.simpleMessage("[Video]"),
    "msgDescVideoCall": MessageLookupByLibrary.simpleMessage("[Video call]"),
    "msgDescWink": MessageLookupByLibrary.simpleMessage("[Wink]"),
    "msgDigestUnknown": MessageLookupByLibrary.simpleMessage(
      "You received an unsupported message.",
    ),
    "msgNew": MessageLookupByLibrary.simpleMessage("New"),
    "msgTooShort": MessageLookupByLibrary.simpleMessage("Message too short"),
    "multipleFace": MessageLookupByLibrary.simpleMessage(
      "Several faces have been detected. \nSelect one to continue.",
    ),
    "music": MessageLookupByLibrary.simpleMessage("Music"),
    "musicFileNotExist": MessageLookupByLibrary.simpleMessage(
      "Music file does not exist",
    ),
    "mute": MessageLookupByLibrary.simpleMessage("Mute"),
    "muteFamilyNotification": MessageLookupByLibrary.simpleMessage(
      "Mute family notification",
    ),
    "muteNotifications": MessageLookupByLibrary.simpleMessage(
      "Mute notifications",
    ),
    "my": MessageLookupByLibrary.simpleMessage("My"),
    "myBadges": MessageLookupByLibrary.simpleMessage("My Badges"),
    "myBalance": MessageLookupByLibrary.simpleMessage("My Balance"),
    "myBirthIs": MessageLookupByLibrary.simpleMessage("My birthday is"),
    "myBottle": MessageLookupByLibrary.simpleMessage("My bottles"),
    "myCollection": MessageLookupByLibrary.simpleMessage("My collection"),
    "myCustomers": MessageLookupByLibrary.simpleMessage("My customers"),
    "myFamily": MessageLookupByLibrary.simpleMessage("My Family"),
    "myFamilyRoomId": MessageLookupByLibrary.simpleMessage(
      "My family room ID:",
    ),
    "myFeedback": MessageLookupByLibrary.simpleMessage("My feedback"),
    "myFollowRoomTitle": MessageLookupByLibrary.simpleMessage("My follow room"),
    "myGolds": MessageLookupByLibrary.simpleMessage("My Golds"),
    "myIncome": MessageLookupByLibrary.simpleMessage("My Income"),
    "myIntegrations": MessageLookupByLibrary.simpleMessage("My integrations"),
    "myLevel": MessageLookupByLibrary.simpleMessage("My Level"),
    "myLocalMusic": MessageLookupByLibrary.simpleMessage("My local music"),
    "myLoveZone": MessageLookupByLibrary.simpleMessage("My Love Zone"),
    "myMusic": MessageLookupByLibrary.simpleMessage("Play Music"),
    "myNameIs": MessageLookupByLibrary.simpleMessage("My name is"),
    "myPersonalId": MessageLookupByLibrary.simpleMessage("My personal ID:"),
    "myRankPlace": m150,
    "myResults": MessageLookupByLibrary.simpleMessage("My results"),
    "myRoom": MessageLookupByLibrary.simpleMessage("My Room"),
    "myRoomId": MessageLookupByLibrary.simpleMessage("My room ID:"),
    "myTask": MessageLookupByLibrary.simpleMessage("My task"),
    "myTheme": MessageLookupByLibrary.simpleMessage("My Theme"),
    "myThemeSubtitle": m151,
    "myVoiceVerification": MessageLookupByLibrary.simpleMessage(
      "My voice verification",
    ),
    "myWallet": MessageLookupByLibrary.simpleMessage("My Wallet"),
    "myWord": m152,
    "myself": MessageLookupByLibrary.simpleMessage("Myself"),
    "mysteriousVisitor": MessageLookupByLibrary.simpleMessage(
      "Mysterious Visitor",
    ),
    "mysteriousVisitorFunction": MessageLookupByLibrary.simpleMessage(
      "Mysterious visitor function on-off",
    ),
    "mysteryLetter": MessageLookupByLibrary.simpleMessage("Mystery letter"),
    "mysteryLetterImTitle": MessageLookupByLibrary.simpleMessage(
      "Mystery Letter",
    ),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "nameContainsIllegal": m153,
    "nameEnteredTheRoom": m154,
    "natural": MessageLookupByLibrary.simpleMessage("Natural"),
    "naturalReverb": MessageLookupByLibrary.simpleMessage("Natural"),
    "nearby": MessageLookupByLibrary.simpleMessage("Nearby"),
    "nearbyNoSettingDes": MessageLookupByLibrary.simpleMessage(
      "Turn on location access and Winker will recommend the right people in your neighborhood",
    ),
    "nearbyNoSettingTitle": MessageLookupByLibrary.simpleMessage(
      "Don\'t know where you are yet.",
    ),
    "needToVerifyVoice": MessageLookupByLibrary.simpleMessage(
      "Winker does its best to ensure you a healthy real social environment so please verify your voice now so we can get to know your real identity!",
    ),
    "networkError": MessageLookupByLibrary.simpleMessage(
      "Network connection failure",
    ),
    "networkErrorAndReload": MessageLookupByLibrary.simpleMessage(
      "Network error, please try again reload.",
    ),
    "newFans": MessageLookupByLibrary.simpleMessage("New fans"),
    "newFriendsRequest": MessageLookupByLibrary.simpleMessage(
      "New friends request",
    ),
    "newHandRewardContent": MessageLookupByLibrary.simpleMessage(
      "Here are some greeting gifts for you",
    ),
    "newMessages": m155,
    "newRound": MessageLookupByLibrary.simpleMessage("New round"),
    "newText": MessageLookupByLibrary.simpleMessage("New"),
    "newTheme": MessageLookupByLibrary.simpleMessage("New Theme"),
    "newVersion": MessageLookupByLibrary.simpleMessage("New Version :"),
    "newbieTasks": MessageLookupByLibrary.simpleMessage("Newbie Tasks"),
    "newbieTitle": MessageLookupByLibrary.simpleMessage(
      "At the start of a new journey, Winker has prepared a welcome gift for you. Enjoy your time on Winker!",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "nextNoShow": MessageLookupByLibrary.simpleMessage("Don\'t show next time"),
    "nextOne": MessageLookupByLibrary.simpleMessage("Next"),
    "nextRevisionDate": m156,
    "niceToMeetYou": MessageLookupByLibrary.simpleMessage(
      "Hi! Nice to meet you!",
    ),
    "niceToMeetYouInBioGuide": MessageLookupByLibrary.simpleMessage(
      "Hi, Nice to meet you!",
    ),
    "nickname": MessageLookupByLibrary.simpleMessage("Nickname"),
    "nineteenCentury": MessageLookupByLibrary.simpleMessage("19th Century"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "no1": MessageLookupByLibrary.simpleMessage("NO. 1"),
    "noContent": MessageLookupByLibrary.simpleMessage(
      "No result, please try a different keyword.",
    ),
    "noContentHere": MessageLookupByLibrary.simpleMessage("No content here"),
    "noConversation": MessageLookupByLibrary.simpleMessage("No conversation"),
    "noCouponsAvailable": MessageLookupByLibrary.simpleMessage(
      "No coupons available",
    ),
    "noCurrentRanking": MessageLookupByLibrary.simpleMessage(
      "No current ranking",
    ),
    "noFeedbackYet": MessageLookupByLibrary.simpleMessage("No feedback yet"),
    "noFirstStep": MessageLookupByLibrary.simpleMessage(
      "You didn’t make the first step",
    ),
    "noFollowers": MessageLookupByLibrary.simpleMessage("No Followers here"),
    "noFollowing": MessageLookupByLibrary.simpleMessage("No Following here"),
    "noFreeToday": MessageLookupByLibrary.simpleMessage("No free chance today"),
    "noFriendsOnline": MessageLookupByLibrary.simpleMessage(
      "No friends online",
    ),
    "noGps": MessageLookupByLibrary.simpleMessage("Failed to obtain location"),
    "noGpsRec": MessageLookupByLibrary.simpleMessage(
      "Unable to recommend nearby people for you",
    ),
    "noLongerInRoom": MessageLookupByLibrary.simpleMessage(
      "You are no longer in the room",
    ),
    "noMore": MessageLookupByLibrary.simpleMessage("No more"),
    "noMoreCoupons": MessageLookupByLibrary.simpleMessage("No more coupons"),
    "noMoreFamily": MessageLookupByLibrary.simpleMessage("No more families"),
    "noMoreRooms": MessageLookupByLibrary.simpleMessage("No more rooms"),
    "noMoreSeat": MessageLookupByLibrary.simpleMessage("No extra seats"),
    "noMoreSuitableRoom": MessageLookupByLibrary.simpleMessage(
      "No more suitable room, please subscribe to join the activity below!",
    ),
    "noMoreTopic": MessageLookupByLibrary.simpleMessage("Sorry, no content"),
    "noMoreUsersInThisCity": MessageLookupByLibrary.simpleMessage(
      "No more users in this city.",
    ),
    "noNewFriendRequest": MessageLookupByLibrary.simpleMessage(
      "You have no new friend request",
    ),
    "noNotifications": MessageLookupByLibrary.simpleMessage("No notifications"),
    "noOneCanSpeak": MessageLookupByLibrary.simpleMessage("No one can speak"),
    "noOneCanSpeakTip": MessageLookupByLibrary.simpleMessage(
      "After turning it on,only the family leader\nand vice-family leader can speak.",
    ),
    "noOneYouAreFollowing": MessageLookupByLibrary.simpleMessage(
      "You haven\'t followed any users yet.",
    ),
    "noOut": m157,
    "noPeopleInRoom": MessageLookupByLibrary.simpleMessage("No people in room"),
    "noPermissionToListenVoice": MessageLookupByLibrary.simpleMessage(
      "You have no permission to listen the voice verification.",
    ),
    "noQualifiedFriends": MessageLookupByLibrary.simpleMessage(
      "No qualified friends",
    ),
    "noRank": MessageLookupByLibrary.simpleMessage("No rank"),
    "noRemindMeAgain": MessageLookupByLibrary.simpleMessage(
      "Don\'t remind me again",
    ),
    "noResult": MessageLookupByLibrary.simpleMessage("No results found"),
    "noRoomCreateEvent": MessageLookupByLibrary.simpleMessage(
      "Before creating a room event, please create your own room first",
    ),
    "noRoomPkRecord": MessageLookupByLibrary.simpleMessage(
      "No room pk record yet",
    ),
    "noSelectedThisRound": MessageLookupByLibrary.simpleMessage(
      "No participant selected this round",
    ),
    "noSubscribeEvent": MessageLookupByLibrary.simpleMessage(
      "You haven\'t subscribed to any events yet. Check out the Room Activities and subscribe to the ones that interest you!\n",
    ),
    "noSupporterOnThisRound": MessageLookupByLibrary.simpleMessage(
      "No supporter on this round.",
    ),
    "noTitleGifters": MessageLookupByLibrary.simpleMessage("No title gifters"),
    "noVip": MessageLookupByLibrary.simpleMessage("NO VIP"),
    "noVisitors": MessageLookupByLibrary.simpleMessage("No Visitors here"),
    "noVotesForUndercover": MessageLookupByLibrary.simpleMessage(
      "No votes were cast in this round. Automatically proceed to the next round.",
    ),
    "nonFamilyMembersAreNotAllowedToEnter": MessageLookupByLibrary.simpleMessage(
      "The family mode has been turned on in this room. Non-family members will be kicked out of the room.",
    ),
    "nonFamilyMembersAreNotAllowedToEnterInThisRoom":
        MessageLookupByLibrary.simpleMessage(
          "The family mode has been turned on in this room. Non-family members are not allowed to enter.",
        ),
    "noneFavorites": MessageLookupByLibrary.simpleMessage(
      "You haven\'t added any stickers to your favorites yet.",
    ),
    "noneMyCollections": MessageLookupByLibrary.simpleMessage(
      "You did not add any collections.",
    ),
    "normal": MessageLookupByLibrary.simpleMessage("Normal"),
    "normalLudoTips": MessageLookupByLibrary.simpleMessage(
      "Normal mode, 4 tokens, magic items.",
    ),
    "notAllowedCloseTheMic": MessageLookupByLibrary.simpleMessage(
      "Not allowed close the mic during the description round.",
    ),
    "notAllowedDuringGame": MessageLookupByLibrary.simpleMessage(
      "Not allowed during the game",
    ),
    "notAllowedKickGamerDuringGame": MessageLookupByLibrary.simpleMessage(
      "Not allowed to kick gamer during the game",
    ),
    "notAllowedToSpeak": MessageLookupByLibrary.simpleMessage(
      "Not allowed to speak during not your game round.",
    ),
    "notAvailable": MessageLookupByLibrary.simpleMessage("Not available"),
    "notBeUsed": MessageLookupByLibrary.simpleMessage(
      "Not be used at the same time",
    ),
    "notConnectedWifi": MessageLookupByLibrary.simpleMessage(
      "Not connected to Wi-Fi",
    ),
    "notFollowedAnyRooms": MessageLookupByLibrary.simpleMessage(
      "you have not followed any rooms.",
    ),
    "notJoinedAnyRoom": MessageLookupByLibrary.simpleMessage(
      "You haven\'t joined any room yet.",
    ),
    "notMatchForHer": MessageLookupByLibrary.simpleMessage(
      "She is not a match for you today, let\'s chat tomorrow!",
    ),
    "notMatchForHim": MessageLookupByLibrary.simpleMessage(
      "He is not a match for you today, let\'s chat tomorrow!",
    ),
    "notMatchWith": MessageLookupByLibrary.simpleMessage(
      "Sorry, you have not match with ",
    ),
    "notMeetRuleJoinFamily": MessageLookupByLibrary.simpleMessage(
      "You do not meet the following requirement to join this family:",
    ),
    "notNow": MessageLookupByLibrary.simpleMessage("Not now"),
    "notObtained": MessageLookupByLibrary.simpleMessage("Not obtained"),
    "notOriginal": MessageLookupByLibrary.simpleMessage(
      "This content is not original",
    ),
    "notOriginalHint": MessageLookupByLibrary.simpleMessage(
      "Please let us know the original creator with proof if possible so we can proceed to verify faster.",
    ),
    "notRecommended": MessageLookupByLibrary.simpleMessage("Not Recommended"),
    "notSale": MessageLookupByLibrary.simpleMessage("Not for sale"),
    "notVipUseFunction": MessageLookupByLibrary.simpleMessage(
      "You can not use this function. Please enable privilege.",
    ),
    "notVoiceInput": MessageLookupByLibrary.simpleMessage(
      "There is no voice input, please record your question in a quiet environment.",
    ),
    "notYet": MessageLookupByLibrary.simpleMessage("Not yet"),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "notice": MessageLookupByLibrary.simpleMessage("Notice"),
    "noticeFollow": MessageLookupByLibrary.simpleMessage(
      "started following you",
    ),
    "noticeForUse": MessageLookupByLibrary.simpleMessage("Notice for use"),
    "noticeLike": MessageLookupByLibrary.simpleMessage("Liked your post"),
    "noticeTitle": MessageLookupByLibrary.simpleMessage(
      "Moments notifications",
    ),
    "notification": MessageLookupByLibrary.simpleMessage("Notification"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notifyMe": MessageLookupByLibrary.simpleMessage("Notify me"),
    "now": MessageLookupByLibrary.simpleMessage("Just now"),
    "nowForReal": MessageLookupByLibrary.simpleMessage("Now"),
    "nowMode": MessageLookupByLibrary.simpleMessage("NOW"),
    "numDays": m158,
    "numDiamonds": m159,
    "numGolds": m160,
    "numGoldsCountTimes": m161,
    "numOfRecipients": MessageLookupByLibrary.simpleMessage(
      "Number of recipients:",
    ),
    "numOfStarlight": MessageLookupByLibrary.simpleMessage(
      "Number of Starlight",
    ),
    "numRound": m162,
    "numStars": m163,
    "numSupporter": m164,
    "numTimesForFruity": m165,
    "numberOfDiamonds": MessageLookupByLibrary.simpleMessage(
      "Number of Diamonds",
    ),
    "numberOfGems": MessageLookupByLibrary.simpleMessage("Number of Rubies"),
    "numberOfLuckyBags": MessageLookupByLibrary.simpleMessage(
      "Number of lucky bags:",
    ),
    "numberOfMic": MessageLookupByLibrary.simpleMessage("Number of Mic"),
    "nw": MessageLookupByLibrary.simpleMessage("New"),
    "ofToday": MessageLookupByLibrary.simpleMessage(" of Today"),
    "off": MessageLookupByLibrary.simpleMessage("Off"),
    "official": MessageLookupByLibrary.simpleMessage("Official"),
    "ok": MessageLookupByLibrary.simpleMessage("Ok"),
    "okay": MessageLookupByLibrary.simpleMessage("Okay"),
    "on": MessageLookupByLibrary.simpleMessage("On"),
    "onMic": MessageLookupByLibrary.simpleMessage("On Mic"),
    "online": MessageLookupByLibrary.simpleMessage("Online"),
    "onlineMostTip": MessageLookupByLibrary.simpleMessage(
      "Display up to 50 online users at most.",
    ),
    "onlineUserCount": m166,
    "onlineUserList": MessageLookupByLibrary.simpleMessage("Online user list"),
    "onlineUsersListOfVoiceRoom": MessageLookupByLibrary.simpleMessage(
      "Online users list of voice room",
    ),
    "only30SearchData": MessageLookupByLibrary.simpleMessage(
      "Only show 30 search data, please enter more keywords",
    ),
    "onlyAdminCanPlayMusic": MessageLookupByLibrary.simpleMessage(
      "Only owner and admin can play the music",
    ),
    "onlyApplication": MessageLookupByLibrary.simpleMessage("Only application"),
    "onlyByInvitation": MessageLookupByLibrary.simpleMessage(
      "Only by invitation",
    ),
    "onlyCalculatedDesignateGift": MessageLookupByLibrary.simpleMessage(
      "Only calculated designate gift",
    ),
    "onlyCalculatedFamilyGift": m167,
    "onlyFamilyMembers": MessageLookupByLibrary.simpleMessage(
      "Only family members",
    ),
    "onlyFamilyMembersCanEnterTheRoom": MessageLookupByLibrary.simpleMessage(
      "Only family members can enter the room?",
    ),
    "onlyFriends": MessageLookupByLibrary.simpleMessage("Only friends"),
    "onlyFriendsCanChat": MessageLookupByLibrary.simpleMessage(
      "You have not opened only friends can chat",
    ),
    "onlyMessage": MessageLookupByLibrary.simpleMessage("Only message alerts"),
    "onlyMessageMoment": MessageLookupByLibrary.simpleMessage(
      "Only message, moments & interaction alerts",
    ),
    "onlyPublishOneMoment": MessageLookupByLibrary.simpleMessage(
      "You can only post one moment at a time,  are you sure you want to send the new moment?",
    ),
    "onlyVipEmoji": MessageLookupByLibrary.simpleMessage(
      "Only vip user can send this emoji",
    ),
    "onlyVipPurchase": MessageLookupByLibrary.simpleMessage(
      "Only vip can purchase it",
    ),
    "onlyWiredHeadsets": MessageLookupByLibrary.simpleMessage(
      "Only wired headsets supported",
    ),
    "ooops": MessageLookupByLibrary.simpleMessage("Ooops.."),
    "open": MessageLookupByLibrary.simpleMessage("Open"),
    "openAddFriendsFunction": MessageLookupByLibrary.simpleMessage(
      "1.Open add friends function in the settings",
    ),
    "openBluetoothDesc": MessageLookupByLibrary.simpleMessage(
      "Turn on Bluetooth settings and complete tasks to get free rewards.",
    ),
    "openBluetoothTitle": MessageLookupByLibrary.simpleMessage(
      "Open Bluetooth settings",
    ),
    "openBox": MessageLookupByLibrary.simpleMessage("Open box"),
    "openFateBell": MessageLookupByLibrary.simpleMessage("Open Fate Bell"),
    "openItNow": MessageLookupByLibrary.simpleMessage("Open it now > "),
    "openMuteAll": m168,
    "openMysteriousSetting": MessageLookupByLibrary.simpleMessage(
      "1.Open mysterious visitor in settings",
    ),
    "openPositioning": MessageLookupByLibrary.simpleMessage("Open positioning"),
    "openTreasure": MessageLookupByLibrary.simpleMessage("open treasure"),
    "opened": MessageLookupByLibrary.simpleMessage("Opened"),
    "openedNum": m169,
    "operation": MessageLookupByLibrary.simpleMessage("Operation"),
    "operationTooFrequent": MessageLookupByLibrary.simpleMessage(
      "Operation too frequent. Please try again later.",
    ),
    "opponentGets": MessageLookupByLibrary.simpleMessage("The opponent gets"),
    "oppositeSexPeople": MessageLookupByLibrary.simpleMessage(
      "At least have an opposite sex people can start next round",
    ),
    "oppositeVoice": MessageLookupByLibrary.simpleMessage("Opposite Voice"),
    "optionTitleNum": m170,
    "optional": MessageLookupByLibrary.simpleMessage("Optional"),
    "or": MessageLookupByLibrary.simpleMessage("or"),
    "original": MessageLookupByLibrary.simpleMessage("Original"),
    "other": MessageLookupByLibrary.simpleMessage("Other"),
    "otherCannotFollowYou": MessageLookupByLibrary.simpleMessage(
      "2.Other users cannot follow you to your room via profile",
    ),
    "otherLoginWays": MessageLookupByLibrary.simpleMessage("Other login ways"),
    "otherPopularTests": MessageLookupByLibrary.simpleMessage(
      "Other popular tests",
    ),
    "otherUsersWillSendYou": MessageLookupByLibrary.simpleMessage(
      "2.Other users will send you a friend request",
    ),
    "otherViewers": m171,
    "others": MessageLookupByLibrary.simpleMessage("Others"),
    "our": MessageLookupByLibrary.simpleMessage("Our"),
    "out": MessageLookupByLibrary.simpleMessage("OUT"),
    "overCallMatch": MessageLookupByLibrary.simpleMessage(
      "Currently few users. Please try later.",
    ),
    "overseasCountries": MessageLookupByLibrary.simpleMessage(
      "Overseas Countries/Regions",
    ),
    "owner": MessageLookupByLibrary.simpleMessage("Owner"),
    "package": MessageLookupByLibrary.simpleMessage("Package"),
    "paintGuideTip": MessageLookupByLibrary.simpleMessage(
      "Use finger draw your words!",
    ),
    "painter": MessageLookupByLibrary.simpleMessage("Painter"),
    "painting": MessageLookupByLibrary.simpleMessage("Painting"),
    "paintingDesc": MessageLookupByLibrary.simpleMessage(
      "Turn your selfie into pretty painting!",
    ),
    "participant": MessageLookupByLibrary.simpleMessage("Participant"),
    "participationRecord": MessageLookupByLibrary.simpleMessage(
      "Participation Record",
    ),
    "party": MessageLookupByLibrary.simpleMessage("Party"),
    "partyDur": m172,
    "partyListEmptyTip": MessageLookupByLibrary.simpleMessage(
      "Create a room and invite friends to chat",
    ),
    "partyReverb": MessageLookupByLibrary.simpleMessage("Party"),
    "pass": MessageLookupByLibrary.simpleMessage("Pass"),
    "passGetMatch": MessageLookupByLibrary.simpleMessage(
      "Passing the verification can get priority for the match. Are you sure exit?",
    ),
    "passNoOneKnow": MessageLookupByLibrary.simpleMessage(
      "If you don\'t like them, simply pass. No one will know.",
    ),
    "passingVerification": MessageLookupByLibrary.simpleMessage(
      "Passing the verification to chat with her~",
    ),
    "passionate": MessageLookupByLibrary.simpleMessage("Interests"),
    "password": m173,
    "passwordWrong": MessageLookupByLibrary.simpleMessage("Password wrong"),
    "past": MessageLookupByLibrary.simpleMessage("Past"),
    "patriarch": MessageLookupByLibrary.simpleMessage("Family Leader"),
    "pauseMusicWhileTuringOffMic": MessageLookupByLibrary.simpleMessage(
      "Pause music while turning off the microphone",
    ),
    "pausedVideo": MessageLookupByLibrary.simpleMessage(
      "paused the video play",
    ),
    "people": MessageLookupByLibrary.simpleMessage("People"),
    "peopleChattingNow": MessageLookupByLibrary.simpleMessage(
      "People chatting now",
    ),
    "performanceEndAdvance": m174,
    "performanceTime": MessageLookupByLibrary.simpleMessage(
      "Performance time（min）",
    ),
    "performerList": MessageLookupByLibrary.simpleMessage("Performer list"),
    "performing": MessageLookupByLibrary.simpleMessage("performing"),
    "performingSwitchMode": MessageLookupByLibrary.simpleMessage(
      "Performing now. Switch room mode denied",
    ),
    "permanent": MessageLookupByLibrary.simpleMessage("Permanent"),
    "permission": MessageLookupByLibrary.simpleMessage("Permission"),
    "personal": MessageLookupByLibrary.simpleMessage("personal"),
    "personalIncome": MessageLookupByLibrary.simpleMessage("Personal Income"),
    "personalInfo": MessageLookupByLibrary.simpleMessage("Personal info"),
    "personalInformation": MessageLookupByLibrary.simpleMessage(
      "Personal information page",
    ),
    "personalityTest": MessageLookupByLibrary.simpleMessage("Personality Test"),
    "personalityTests": MessageLookupByLibrary.simpleMessage(
      "Personality tests",
    ),
    "personalityTestsDetail": MessageLookupByLibrary.simpleMessage(
      "Meet friends matching your interests",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone number"),
    "photoToAvatar": MessageLookupByLibrary.simpleMessage("Photo to Avatar"),
    "piano": MessageLookupByLibrary.simpleMessage("Piano"),
    "pick": MessageLookupByLibrary.simpleMessage("Pick"),
    "pickBottleAndTalk": MessageLookupByLibrary.simpleMessage(
      "Click it, pick the bottle up and talk with them.",
    ),
    "pictureNotExist": MessageLookupByLibrary.simpleMessage(
      "This picture does not exist",
    ),
    "pictureUploaded": MessageLookupByLibrary.simpleMessage(
      "The picture has been uploaded",
    ),
    "picturesTips": MessageLookupByLibrary.simpleMessage(
      "Pictures are more touching!",
    ),
    "pin": MessageLookupByLibrary.simpleMessage("Pin"),
    "pinChat": MessageLookupByLibrary.simpleMessage("Pin chat"),
    "pingName": MessageLookupByLibrary.simpleMessage("Ping!"),
    "pk": MessageLookupByLibrary.simpleMessage("PK"),
    "pkEndedTips": MessageLookupByLibrary.simpleMessage(
      "The PK ended in a draw",
    ),
    "pkIsNotFinished": MessageLookupByLibrary.simpleMessage(
      "PK is not finished",
    ),
    "pkMatching": MessageLookupByLibrary.simpleMessage("Matching"),
    "pkPoint": MessageLookupByLibrary.simpleMessage("PK point:"),
    "pkResult": MessageLookupByLibrary.simpleMessage("PK Result"),
    "pkWinner": MessageLookupByLibrary.simpleMessage("PK Winner"),
    "plagiarism": MessageLookupByLibrary.simpleMessage("Plagiarism"),
    "play": MessageLookupByLibrary.simpleMessage("Play"),
    "playAgain": MessageLookupByLibrary.simpleMessage("Play again"),
    "playAndPause": MessageLookupByLibrary.simpleMessage("PLAY AND PAUSE"),
    "playCenter": MessageLookupByLibrary.simpleMessage("Play center"),
    "playDefaultMusic": MessageLookupByLibrary.simpleMessage(
      "Play the default music",
    ),
    "playError": MessageLookupByLibrary.simpleMessage("Play error"),
    "playInOrder": MessageLookupByLibrary.simpleMessage("Play in order"),
    "playQuestionWithYou": m175,
    "playRandomly": MessageLookupByLibrary.simpleMessage("Play randomly"),
    "playThisVideo": MessageLookupByLibrary.simpleMessage("Play this video"),
    "playWithFriend": MessageLookupByLibrary.simpleMessage(
      "Click and play with your friend!",
    ),
    "playerNotReady": MessageLookupByLibrary.simpleMessage(
      "Players not ready or not enough",
    ),
    "playerPosition": m176,
    "playing": MessageLookupByLibrary.simpleMessage("Playing"),
    "playingGame": m177,
    "playingVoiceRoom": MessageLookupByLibrary.simpleMessage("In voice room"),
    "pleaseChooseAnswer": MessageLookupByLibrary.simpleMessage(
      "Please choose your answer",
    ),
    "pleaseChooseLengthOfPurchase": MessageLookupByLibrary.simpleMessage(
      "Please choose validity period",
    ),
    "pleaseChooseLove": MessageLookupByLibrary.simpleMessage(
      "Please choose your love.",
    ),
    "pleaseConnectWifi": MessageLookupByLibrary.simpleMessage(
      "Please connect your phone to Wi-Fi first",
    ),
    "pleaseContactCustomerToDisbandFamily": MessageLookupByLibrary.simpleMessage(
      "The family leader cannot leave the family directly. You can contact the official customer service for assistance.",
    ),
    "pleaseCreateRoom": MessageLookupByLibrary.simpleMessage(
      "You do not currently have a room, please create your own room.",
    ),
    "pleaseDescribe": MessageLookupByLibrary.simpleMessage(
      "Please describe the problem you are experiencing...",
    ),
    "pleaseDescribeReport": MessageLookupByLibrary.simpleMessage(
      "Please describe the problem you want to report.",
    ),
    "pleaseEnterChar": m178,
    "pleaseEnterRoomPw": MessageLookupByLibrary.simpleMessage(
      "Please enter a 4-digit password",
    ),
    "pleaseEnterTitle": MessageLookupByLibrary.simpleMessage(
      "Please enter title.",
    ),
    "pleaseExitRoom": MessageLookupByLibrary.simpleMessage(
      "Please exit the room first",
    ),
    "pleaseGiveMeAKey": MessageLookupByLibrary.simpleMessage(
      "Please give me a key to help me open the treasure chest!",
    ),
    "pleaseGiveMeKey": MessageLookupByLibrary.simpleMessage(
      "Please give me the key to help me open the treasure chest!",
    ),
    "pleaseInputAgain": MessageLookupByLibrary.simpleMessage(
      "Network error, please input again",
    ),
    "pleaseKindlyComment": MessageLookupByLibrary.simpleMessage(
      "Please kindly comment",
    ),
    "pleaseRateThisCall": MessageLookupByLibrary.simpleMessage(
      "Please rate this call",
    ),
    "pleaseReadAndAgree": MessageLookupByLibrary.simpleMessage(
      "Please read and agree",
    ),
    "pleaseReadTheWords": MessageLookupByLibrary.simpleMessage(
      "Read the text below:",
    ),
    "pleaseRecord": MessageLookupByLibrary.simpleMessage(
      "Please click the record button first, and then read the above words aloud.",
    ),
    "pleaseSelectReason": MessageLookupByLibrary.simpleMessage(
      "Please select the reason for reporting\nWe won\'t tell this user",
    ),
    "pleaseSelectUser": MessageLookupByLibrary.simpleMessage(
      "Please select the user who want to send",
    ),
    "pleaseSetUpYourCity": MessageLookupByLibrary.simpleMessage(
      "Please set up your city for easier location by your local friends.",
    ),
    "pleaseTakeMicAndSpeak": MessageLookupByLibrary.simpleMessage(
      "Please take the mic and speak.",
    ),
    "pleaseTryAgain": MessageLookupByLibrary.simpleMessage(
      "Network problem please try again",
    ),
    "pleaseWaitApproval": MessageLookupByLibrary.simpleMessage(
      "Please wait for the approval",
    ),
    "pointsChange": m179,
    "pokedYou": MessageLookupByLibrary.simpleMessage("poked you!"),
    "politicsRelated": MessageLookupByLibrary.simpleMessage("politics related"),
    "popular": MessageLookupByLibrary.simpleMessage("Popular"),
    "popularity": MessageLookupByLibrary.simpleMessage("Popularity"),
    "popularityList": MessageLookupByLibrary.simpleMessage("Gift received"),
    "popularityRank": MessageLookupByLibrary.simpleMessage("Ranking"),
    "porn": MessageLookupByLibrary.simpleMessage("Porn"),
    "post": MessageLookupByLibrary.simpleMessage("Post"),
    "postMoment": MessageLookupByLibrary.simpleMessage(
      "High quality moments can get you more replies from other mates, <h>",
    ),
    "postMoments": MessageLookupByLibrary.simpleMessage("Post moment"),
    "postMoreMoments": MessageLookupByLibrary.simpleMessage(
      "Post more moments to get more attention!",
    ),
    "postNow": MessageLookupByLibrary.simpleMessage("add a new post now!"),
    "postSuccess": MessageLookupByLibrary.simpleMessage("Post successfully"),
    "postTipGourmet": MessageLookupByLibrary.simpleMessage(
      "Checked in a gourmet restaurant recently",
    ),
    "postTipJournal": MessageLookupByLibrary.simpleMessage(
      "Checked in a gourmet restaurant recently",
    ),
    "postTipPet": MessageLookupByLibrary.simpleMessage(
      "Checked in a gourmet restaurant recently",
    ),
    "postVoiceMoment": MessageLookupByLibrary.simpleMessage(
      "You can post a voice note in moments now!",
    ),
    "postWhichTheme": MessageLookupByLibrary.simpleMessage(
      "Post to which theme",
    ),
    "powerBar": MessageLookupByLibrary.simpleMessage("Power bar"),
    "premium": MessageLookupByLibrary.simpleMessage("Premium"),
    "premiumAvatarFrameDesc": MessageLookupByLibrary.simpleMessage(
      "Get more attention with your special Premium frame",
    ),
    "premiumBadge": MessageLookupByLibrary.simpleMessage("Premium badge"),
    "premiumSettings": MessageLookupByLibrary.simpleMessage("Premium settings"),
    "prev": MessageLookupByLibrary.simpleMessage("Prev"),
    "preventFollowRoom": MessageLookupByLibrary.simpleMessage(
      "Prevent follow into room",
    ),
    "preventFollowRoomDesc": MessageLookupByLibrary.simpleMessage(
      "You can keep others from following you into room.",
    ),
    "preventFollowingIntoRoom": MessageLookupByLibrary.simpleMessage(
      "Prevent following into room",
    ),
    "preview": MessageLookupByLibrary.simpleMessage("Preview"),
    "previous": MessageLookupByLibrary.simpleMessage("Previous"),
    "priorityReport": MessageLookupByLibrary.simpleMessage(
      "Priority reporting",
    ),
    "priorityReportDesc": MessageLookupByLibrary.simpleMessage(
      "Customer service will process your report information faster",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy policy"),
    "privacyProblem": MessageLookupByLibrary.simpleMessage("Privacy problem"),
    "private": MessageLookupByLibrary.simpleMessage("Private"),
    "privateFlights": MessageLookupByLibrary.simpleMessage("Private flights"),
    "privatePhotoTipHelp": MessageLookupByLibrary.simpleMessage(
      "You can view each other\'s private photos only when your intimacy reaches 10~.\nText chat and gifts can increase your intimacy",
    ),
    "privatePhotos": MessageLookupByLibrary.simpleMessage("Private photos"),
    "privileged": MessageLookupByLibrary.simpleMessage("Privileged"),
    "privileges": MessageLookupByLibrary.simpleMessage("Privileges"),
    "privilegesTitle": m180,
    "probabilityGiftTitle": MessageLookupByLibrary.simpleMessage(
      "Congratulations on popping out of your gift：",
    ),
    "probabilityGiftWinTip": m181,
    "problemDesc": MessageLookupByLibrary.simpleMessage("Problem description"),
    "processingRequest": MessageLookupByLibrary.simpleMessage(
      "We are processing your request",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profileChattingInRoom": m182,
    "promotingOtherApps": MessageLookupByLibrary.simpleMessage(
      "promoting other apps",
    ),
    "proposal": MessageLookupByLibrary.simpleMessage("Proposal"),
    "proposalDesc": MessageLookupByLibrary.simpleMessage(
      "Two parties can reach the intimacy of 5000 to propose. You can increase intimacy by chatting or sending gifts.",
    ),
    "proposalMsg": MessageLookupByLibrary.simpleMessage("Message"),
    "proposalPaperDesc": MessageLookupByLibrary.simpleMessage(
      "To the world you may be one person, but to one person you may be the world.",
    ),
    "proposalSendWait": m183,
    "proposalVowsHint": MessageLookupByLibrary.simpleMessage(
      "Write your words to your love...",
    ),
    "proposalVowsTitle": MessageLookupByLibrary.simpleMessage("Write vows"),
    "propose": MessageLookupByLibrary.simpleMessage("Propose"),
    "proposeMsgDesc": m184,
    "proposeTopic": MessageLookupByLibrary.simpleMessage(
      "Recommend your topic to us",
    ),
    "props": MessageLookupByLibrary.simpleMessage("Props"),
    "protectYourInfo": MessageLookupByLibrary.simpleMessage(
      "We will protect your privacy and personal information.",
    ),
    "protectYourPrivacy": MessageLookupByLibrary.simpleMessage(
      "Don\'t worry we ensure to protect your privacy and information.",
    ),
    "proxyErr": MessageLookupByLibrary.simpleMessage(
      "Please make sure you are not using a proxy.",
    ),
    "public": MessageLookupByLibrary.simpleMessage("Public"),
    "publishTitleHintText": MessageLookupByLibrary.simpleMessage(
      "Enter the title",
    ),
    "punch": MessageLookupByLibrary.simpleMessage("Punch"),
    "punchGame": MessageLookupByLibrary.simpleMessage("Punch Game"),
    "punchGameFrequent": MessageLookupByLibrary.simpleMessage(
      "Invitations are too frequent, please wait a moment.",
    ),
    "punchJoinTitle": MessageLookupByLibrary.simpleMessage(
      "The other party wants to play [Guess Fist Q&A]",
    ),
    "punchLost": MessageLookupByLibrary.simpleMessage(
      "😅You lost, waiting for the other party to choose a question...",
    ),
    "punchQuestionTitle": m185,
    "punchQuick": MessageLookupByLibrary.simpleMessage("Q&A invitation"),
    "punchReceive": MessageLookupByLibrary.simpleMessage(
      "The other party accepted the \'Guess First Q&A\' invitation.",
    ),
    "punchReq": MessageLookupByLibrary.simpleMessage(
      "Sent out an invitation to \'Guess Fist Q&A\' and waiting for the other party to join...",
    ),
    "punish": MessageLookupByLibrary.simpleMessage("Punish"),
    "punishLevel": MessageLookupByLibrary.simpleMessage("Punish Level"),
    "punishSelect": MessageLookupByLibrary.simpleMessage("Punish Select"),
    "punishSilentRemaining": MessageLookupByLibrary.simpleMessage(
      "You have been banned from speaking on the public screen by the administrator",
    ),
    "punishedInRound": MessageLookupByLibrary.simpleMessage(
      "Punished in this round",
    ),
    "purchase": MessageLookupByLibrary.simpleMessage("Purchase"),
    "purchaseSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Purchase successfully",
    ),
    "purchasedIt": MessageLookupByLibrary.simpleMessage(
      "You have purchased it!",
    ),
    "purchasingWait": MessageLookupByLibrary.simpleMessage(
      "Purchasing...Please wait",
    ),
    "quantityShortage": MessageLookupByLibrary.simpleMessage("Gifts shortage"),
    "queryOneMonth": MessageLookupByLibrary.simpleMessage(
      "Only supports querying records within 1 month",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("Quickly"),
    "quicklyLudoTips": MessageLookupByLibrary.simpleMessage(
      "Quickly mode, 1 tokens, magic items.",
    ),
    "quit": MessageLookupByLibrary.simpleMessage("Quit"),
    "quitGroup": MessageLookupByLibrary.simpleMessage("Quit group"),
    "quizGuideText": MessageLookupByLibrary.simpleMessage(
      "Take the test to find your ideal mate based on your result",
    ),
    "quizProvided": MessageLookupByLibrary.simpleMessage(
      "Quiz provided by third party content providers.",
    ),
    "quizShare": MessageLookupByLibrary.simpleMessage(
      "Hi~Mate! Take personality tests now to find friends match your results!",
    ),
    "racism": MessageLookupByLibrary.simpleMessage("racism"),
    "random": MessageLookupByLibrary.simpleMessage("Random"),
    "rank": MessageLookupByLibrary.simpleMessage("Rank"),
    "rank1st": MessageLookupByLibrary.simpleMessage("1st"),
    "rank2nd": MessageLookupByLibrary.simpleMessage("2nd"),
    "rank3rd": MessageLookupByLibrary.simpleMessage("3rd"),
    "rankGlobalBanner": m186,
    "rankList": MessageLookupByLibrary.simpleMessage("Ranking list"),
    "ranking": MessageLookupByLibrary.simpleMessage("Ranking"),
    "rateApp": MessageLookupByLibrary.simpleMessage(
      "Please rate our application!",
    ),
    "reJoinFamilyTip": MessageLookupByLibrary.simpleMessage(
      "After leaving the family, you can only rejoin after 24 hours.",
    ),
    "read": MessageLookupByLibrary.simpleMessage("Read"),
    "ready": MessageLookupByLibrary.simpleMessage("Ready"),
    "readyGrab": MessageLookupByLibrary.simpleMessage("Ready grabbing"),
    "readyToPerform": MessageLookupByLibrary.simpleMessage("Ready to perform!"),
    "realPersonVerified": MessageLookupByLibrary.simpleMessage(
      "Real person verified",
    ),
    "receive": MessageLookupByLibrary.simpleMessage("Receive"),
    "receiveDiamonds": MessageLookupByLibrary.simpleMessage("Receive diamonds"),
    "receiveNewMsg": MessageLookupByLibrary.simpleMessage(
      "You received a new message",
    ),
    "received": MessageLookupByLibrary.simpleMessage("Received"),
    "receivedABottle": MessageLookupByLibrary.simpleMessage(
      "Received a bottle",
    ),
    "receivedBottle": MessageLookupByLibrary.simpleMessage(
      "You received a bottle",
    ),
    "receivedBottleFrom": MessageLookupByLibrary.simpleMessage(
      "You received a bottle from ",
    ),
    "receivedGifts": MessageLookupByLibrary.simpleMessage("Gifts"),
    "receivedRankExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on the diamond value of gifts you received",
    ),
    "receiverCharmAddNumExp": m187,
    "receiverNumRubies": m188,
    "recently": MessageLookupByLibrary.simpleMessage("Recently"),
    "recentlyLoveZoneGifts": MessageLookupByLibrary.simpleMessage(
      "Recently love zone gifts",
    ),
    "recentlyRoomEmpty": MessageLookupByLibrary.simpleMessage(
      "You have not joined any rooms.",
    ),
    "recentlyUsed": MessageLookupByLibrary.simpleMessage("Recently used"),
    "recharge": MessageLookupByLibrary.simpleMessage("Recharge"),
    "rechargeNotOpen": MessageLookupByLibrary.simpleMessage(
      "The recharge service within the APP has not been opened yet.",
    ),
    "rechargeNow": MessageLookupByLibrary.simpleMessage("Recharge now"),
    "rechargeSucceeds": MessageLookupByLibrary.simpleMessage(
      "Recharge successfully",
    ),
    "rechargeTip": MessageLookupByLibrary.simpleMessage(
      "Please select the recharge amount:",
    ),
    "recommend": MessageLookupByLibrary.simpleMessage("Recommend"),
    "recommendFamilyTitle": MessageLookupByLibrary.simpleMessage(
      "Some families recommend for you",
    ),
    "recommendOtherRoom": MessageLookupByLibrary.simpleMessage(
      "Recommend other rooms for you",
    ),
    "recommendTopic": MessageLookupByLibrary.simpleMessage("Recommend topic"),
    "recommendUser": MessageLookupByLibrary.simpleMessage("Recommend user"),
    "record": MessageLookupByLibrary.simpleMessage("Record"),
    "recordAtLeastSeconds": m189,
    "recordQueTips": MessageLookupByLibrary.simpleMessage(
      "Record your questions to unlock the chat,make a match and you will get new friends!",
    ),
    "recordQuestions": MessageLookupByLibrary.simpleMessage(
      "Record my questions",
    ),
    "recordUnlockChat": MessageLookupByLibrary.simpleMessage("Unlock the chat"),
    "recordVoiceGuidContent": MessageLookupByLibrary.simpleMessage(
      "Click it to read the words by your voice",
    ),
    "recordYourQuestion": MessageLookupByLibrary.simpleMessage(
      "Record your question",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Recording..."),
    "recordingWillStop": MessageLookupByLibrary.simpleMessage(
      "Recording will stop in",
    ),
    "records": MessageLookupByLibrary.simpleMessage("Records"),
    "recreateAgain": MessageLookupByLibrary.simpleMessage(
      "You can recreate by completing the profile again.",
    ),
    "recruit": MessageLookupByLibrary.simpleMessage("Recruit"),
    "redPackDetail": MessageLookupByLibrary.simpleMessage("Get angpao detail"),
    "redPackExpired": MessageLookupByLibrary.simpleMessage(
      "Angpao envelope has expired",
    ),
    "redPackGet": MessageLookupByLibrary.simpleMessage("Get"),
    "redPackResultEmptyTip": MessageLookupByLibrary.simpleMessage(
      "The red packet is empty, your hands are not fast enough! Be faster next time!",
    ),
    "redPackResultGetMessage": MessageLookupByLibrary.simpleMessage(
      "You grab from the current red packet",
    ),
    "redPackResultTip": MessageLookupByLibrary.simpleMessage(
      "Remember! The faster your hand speed, the more diamonds you will get!",
    ),
    "redPackTitle": MessageLookupByLibrary.simpleMessage(
      "Heat bonus triggered",
    ),
    "refreshComplete": MessageLookupByLibrary.simpleMessage("Refreshed"),
    "refreshFailed": MessageLookupByLibrary.simpleMessage("Refresh failed"),
    "refuse": MessageLookupByLibrary.simpleMessage("Refuse"),
    "refuseFamilyApply": MessageLookupByLibrary.simpleMessage(
      "refuse your family application.",
    ),
    "refuseFamilyInvitation": MessageLookupByLibrary.simpleMessage(
      "refuse your family invitation.",
    ),
    "regAccessWinker": MessageLookupByLibrary.simpleMessage("Access Winker"),
    "regAnsNum": m190,
    "regAnsRemaining": m191,
    "regAnswer": MessageLookupByLibrary.simpleMessage("Answer Test"),
    "regAvatar": MessageLookupByLibrary.simpleMessage(
      "Please select your Avatar.",
    ),
    "regBirthSame": m192,
    "regBirthday": m193,
    "regCardBirthdayTip": MessageLookupByLibrary.simpleMessage(
      "First visit to Winker",
    ),
    "regCompleteInfo": MessageLookupByLibrary.simpleMessage(
      "Next, we need you to refine your personal information.",
    ),
    "regGender": m194,
    "regGenderFemale": MessageLookupByLibrary.simpleMessage("👩Female"),
    "regGenderMale": MessageLookupByLibrary.simpleMessage("👱Male"),
    "regGenderSexTip": m195,
    "regInvitationCode": MessageLookupByLibrary.simpleMessage("Invite Code"),
    "regMr": MessageLookupByLibrary.simpleMessage("Mr."),
    "regMs": MessageLookupByLibrary.simpleMessage("Ms."),
    "regMyInvitationCode": MessageLookupByLibrary.simpleMessage(
      "My Invitation Code x",
    ),
    "regNiceNickname": m196,
    "regNickInputHint": MessageLookupByLibrary.simpleMessage(
      "Enter your nickname",
    ),
    "regNickname": MessageLookupByLibrary.simpleMessage(
      "How would you like the friends in winker to address you?",
    ),
    "regNotPass": MessageLookupByLibrary.simpleMessage(
      "🙁Sorry, you did not pass this test.",
    ),
    "regPassed": MessageLookupByLibrary.simpleMessage(
      "👏Congratulations on passing the test, and you will soon be able to meet Winker\'s friends.",
    ),
    "regRight": MessageLookupByLibrary.simpleMessage("Right"),
    "regSuccess": m197,
    "regTestTip": MessageLookupByLibrary.simpleMessage(
      "Winker is currently in internal testing，and to maintain a positive community atmosphere, we have set up some small barriers during registration.\nYou can access Winker in two ways：\n👇👇🏻👇🏾",
    ),
    "regVerificationPassed": MessageLookupByLibrary.simpleMessage(
      "Congratulations🎉🎉🎉, verification passed!",
    ),
    "regWelcome": MessageLookupByLibrary.simpleMessage(
      "Hey there, I\'m Winker!\nI\'ll help you settle in quickly and make new friends around here.",
    ),
    "region": MessageLookupByLibrary.simpleMessage("Region"),
    "reject": MessageLookupByLibrary.simpleMessage("reject"),
    "rejectCapital": MessageLookupByLibrary.simpleMessage("Reject"),
    "rejectPkTips": MessageLookupByLibrary.simpleMessage(
      " reject your room PK invitation.",
    ),
    "rejectReview": MessageLookupByLibrary.simpleMessage("Reject review"),
    "rejectYourDivorce": MessageLookupByLibrary.simpleMessage(
      "rejected your divorce.",
    ),
    "rejected": MessageLookupByLibrary.simpleMessage("Rejected"),
    "related": MessageLookupByLibrary.simpleMessage("Related"),
    "religionRelated": MessageLookupByLibrary.simpleMessage("religion related"),
    "reload": MessageLookupByLibrary.simpleMessage("Reload"),
    "remaining": m198,
    "remarks": MessageLookupByLibrary.simpleMessage("Remarks"),
    "remove": MessageLookupByLibrary.simpleMessage("Remove"),
    "removeAdmin": MessageLookupByLibrary.simpleMessage("Remove admin"),
    "removeFromFamily": MessageLookupByLibrary.simpleMessage(
      "Remove from the family",
    ),
    "removeUserOutFamily": m199,
    "replied": MessageLookupByLibrary.simpleMessage("Replied"),
    "reply": MessageLookupByLibrary.simpleMessage("Reply"),
    "replyToUnlockMore": MessageLookupByLibrary.simpleMessage(
      "Reply to conversations to unlock more!",
    ),
    "report": MessageLookupByLibrary.simpleMessage("Report"),
    "reportAttachmentContent": MessageLookupByLibrary.simpleMessage(
      "Optional, the video size does not exceed 20M",
    ),
    "reportAvatar": MessageLookupByLibrary.simpleMessage("Report avatar"),
    "reportCenter": MessageLookupByLibrary.simpleMessage("Report Center"),
    "reportDescribe": MessageLookupByLibrary.simpleMessage(
      "Describe your problem you are experiencing...",
    ),
    "reportIntroduction": MessageLookupByLibrary.simpleMessage(
      "Report introduction",
    ),
    "reportNickname": MessageLookupByLibrary.simpleMessage("Report nickname"),
    "reportSuccess": MessageLookupByLibrary.simpleMessage(
      "Reported successfully",
    ),
    "reportTheme": MessageLookupByLibrary.simpleMessage("Report Theme"),
    "reportUserTitle": MessageLookupByLibrary.simpleMessage("Report User"),
    "reportVoiceCard": MessageLookupByLibrary.simpleMessage(
      "Report voice card",
    ),
    "requestAMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "Request a magic box key",
    ),
    "requestKey": MessageLookupByLibrary.simpleMessage("Request key"),
    "requestSend": MessageLookupByLibrary.simpleMessage("Request send"),
    "requestStorageData": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" requests access to your storage data.",
    ),
    "requestSuccessful": MessageLookupByLibrary.simpleMessage(
      "Request successful, please wait for response",
    ),
    "requestTimeout": MessageLookupByLibrary.simpleMessage(
      "Request time-out, please try again.",
    ),
    "requestTooMuch": MessageLookupByLibrary.simpleMessage(
      "Request failed. You can send invitations to this user twice per day.",
    ),
    "requestsHasReachedTheUpperLimit": MessageLookupByLibrary.simpleMessage(
      "The number of requests for this user has reached the upper limit.",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("Resend"),
    "resendCode": MessageLookupByLibrary.simpleMessage("Resend"),
    "resendCodeIn": MessageLookupByLibrary.simpleMessage("Resend after"),
    "resendCodeMax": MessageLookupByLibrary.simpleMessage(
      "You have reached the maximum requests, please try again tomorrow.",
    ),
    "resendMsg": MessageLookupByLibrary.simpleMessage("Resend this message?"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "respondWithGift": MessageLookupByLibrary.simpleMessage(
      "Respond with gift",
    ),
    "restore": MessageLookupByLibrary.simpleMessage("Restore"),
    "result": MessageLookupByLibrary.simpleMessage("Result"),
    "resultWithWords": m200,
    "results": MessageLookupByLibrary.simpleMessage("Results:"),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "reverb": MessageLookupByLibrary.simpleMessage("Reverb："),
    "reviewEvent": MessageLookupByLibrary.simpleMessage("Review event"),
    "reward": MessageLookupByLibrary.simpleMessage("Reward"),
    "rewardDetail": MessageLookupByLibrary.simpleMessage("Reward detail"),
    "rewardStEnd": MessageLookupByLibrary.simpleMessage(
      "Reward statistics ended",
    ),
    "rewardStatistics": MessageLookupByLibrary.simpleMessage(
      "Reward\nStatistics",
    ),
    "rewardToGet": MessageLookupByLibrary.simpleMessage(
      "There are rewards to be claimed",
    ),
    "rewardedToast": m201,
    "rewards": MessageLookupByLibrary.simpleMessage("Rewards"),
    "rewardsHasSent": MessageLookupByLibrary.simpleMessage(
      "The rewards has been sent to you.",
    ),
    "rewardsSentYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Rewards have been sent to your backpack.",
    ),
    "ring": MessageLookupByLibrary.simpleMessage("Ring"),
    "ringBox": MessageLookupByLibrary.simpleMessage("Ring box"),
    "ringDisplay": MessageLookupByLibrary.simpleMessage("Ring display"),
    "ringGuidelines": MessageLookupByLibrary.simpleMessage("Ring guidelines"),
    "ripple": MessageLookupByLibrary.simpleMessage("Ripple"),
    "riskyTips": MessageLookupByLibrary.simpleMessage(
      "Involves risky information; beware of scams.",
    ),
    "room": MessageLookupByLibrary.simpleMessage("Room"),
    "roomAdmins": m202,
    "roomAdminsTitle": MessageLookupByLibrary.simpleMessage("Room Admins"),
    "roomAnnounceInputHint": MessageLookupByLibrary.simpleMessage(
      "All members of the room will see the announcement",
    ),
    "roomAnnounceWordCount": m203,
    "roomApplyMic": MessageLookupByLibrary.simpleMessage("Apply to mic"),
    "roomBackground": MessageLookupByLibrary.simpleMessage("Background"),
    "roomCloseAndExit": MessageLookupByLibrary.simpleMessage("Close and exit"),
    "roomClosedDueToProlonged": MessageLookupByLibrary.simpleMessage(
      "The room has been closed due to prolonged inactivity.",
    ),
    "roomCover": MessageLookupByLibrary.simpleMessage("Room Cover"),
    "roomEvent": MessageLookupByLibrary.simpleMessage("Room Event"),
    "roomEventMaximum": MessageLookupByLibrary.simpleMessage(
      "The room has reached its maximum activity",
    ),
    "roomExitDes": MessageLookupByLibrary.simpleMessage(
      "Are you sure to exit the room?",
    ),
    "roomExitTitle": MessageLookupByLibrary.simpleMessage("Leave the room"),
    "roomFanClubNameLimit": m204,
    "roomFanClubNameLimitContent": MessageLookupByLibrary.simpleMessage(
      "· Character limit for fan group name: 2~6 letters/numbers, other characters are not supported\n· The name of the fan group cannot use sensitive words such as pornography or pornography, cannot slander or impersonate other people, and cannot use official names.",
    ),
    "roomFanClubNameLimitTitle": MessageLookupByLibrary.simpleMessage(
      "Setup requirements",
    ),
    "roomFansNameModifyTip": m205,
    "roomFansUpdateTip": MessageLookupByLibrary.simpleMessage(
      "The tasks will reset every day at 00:00.",
    ),
    "roomFreeMic": MessageLookupByLibrary.simpleMessage("Free to mic"),
    "roomFunction": MessageLookupByLibrary.simpleMessage("Room function"),
    "roomGifts": MessageLookupByLibrary.simpleMessage("Room gifts"),
    "roomHasBanned": MessageLookupByLibrary.simpleMessage(
      "The room has been banned",
    ),
    "roomId": m206,
    "roomIdKey": MessageLookupByLibrary.simpleMessage("Room ID"),
    "roomIsClosed": MessageLookupByLibrary.simpleMessage(
      "The current room is not online, please come back later",
    ),
    "roomIsOpening": m207,
    "roomKickOut": MessageLookupByLibrary.simpleMessage("Kick out"),
    "roomLeveUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Room Exp daily upper limit: ",
    ),
    "roomLevel": MessageLookupByLibrary.simpleMessage("Room level"),
    "roomLevelUpgrade": m208,
    "roomLostConnect": MessageLookupByLibrary.simpleMessage(
      "The room is disconnected",
    ),
    "roomMemberTabAdmin": MessageLookupByLibrary.simpleMessage("Admin"),
    "roomMemberTabBlocked": MessageLookupByLibrary.simpleMessage("Blocked"),
    "roomMemberTabOnline": MessageLookupByLibrary.simpleMessage("Online"),
    "roomMemberTabSpeakRequest": MessageLookupByLibrary.simpleMessage(
      "Speak request",
    ),
    "roomMicPermission": MessageLookupByLibrary.simpleMessage("Mic permission"),
    "roomMode": MessageLookupByLibrary.simpleMessage("Room Mode"),
    "roomModeChat": MessageLookupByLibrary.simpleMessage("Chat"),
    "roomModeCp": MessageLookupByLibrary.simpleMessage("Cp"),
    "roomModeTruthDare": MessageLookupByLibrary.simpleMessage("Truth or Dare"),
    "roomMsgRoomName": m209,
    "roomName": MessageLookupByLibrary.simpleMessage("Room name"),
    "roomNameInputHint": MessageLookupByLibrary.simpleMessage(
      "Please give your room a cool name",
    ),
    "roomPk": MessageLookupByLibrary.simpleMessage("Room PK"),
    "roomPkExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is baesd on the diamond value of gifts during the room pk.",
    ),
    "roomPkInvitation": MessageLookupByLibrary.simpleMessage(
      "Room PK invitation",
    ),
    "roomPkInviteTips": MessageLookupByLibrary.simpleMessage(
      " sent you a room PK invitation. Whether accept the challenge?",
    ),
    "roomPkMatching": MessageLookupByLibrary.simpleMessage(
      "Matching room pk...",
    ),
    "roomPkNotice": MessageLookupByLibrary.simpleMessage(
      "Room PK notification",
    ),
    "roomPkRanking": MessageLookupByLibrary.simpleMessage("Room PK ranking"),
    "roomPkRecord": MessageLookupByLibrary.simpleMessage("Room PK Record"),
    "roomPkRule": MessageLookupByLibrary.simpleMessage(
      "1. The room owner and admin can start the PK in room functions.\n\n2. PK has two modes, namely vote PK and gift PK. In vote PK, each person can choose a favorite friend to support. After vote, the voting cannot be withdrawn. Gift PK You need to send any gift or designated gift to support him. If the creator create the designated gift PK, only the designated gift in the PK will be counted in the PK.\n\n3. The creator can enable the jackpot PK mode. PK joiner will not receive gift refund during the PK. PK Winner will get 30% gift value after the PK.",
    ),
    "roomProfile": MessageLookupByLibrary.simpleMessage("Room profile"),
    "roomRankExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on the diamond value of gifts room received",
    ),
    "roomReEnterErrTip": m210,
    "roomSettingClearScreen": MessageLookupByLibrary.simpleMessage(
      "Clear screen",
    ),
    "roomSettingMusic": MessageLookupByLibrary.simpleMessage("Music"),
    "roomSettings": MessageLookupByLibrary.simpleMessage("Room settings"),
    "roomTag": MessageLookupByLibrary.simpleMessage("Room tag"),
    "roomTask": MessageLookupByLibrary.simpleMessage("Room task"),
    "roomTaskGuideText": MessageLookupByLibrary.simpleMessage(
      "Click here to get room task!",
    ),
    "roomTaskMsgText": MessageLookupByLibrary.simpleMessage(
      "Complete the room task to get more rewards !",
    ),
    "roomTaskRefreshTime": MessageLookupByLibrary.simpleMessage(
      "Refresh at 23:59(UTC+3)",
    ),
    "roomTheme": MessageLookupByLibrary.simpleMessage("Room theme"),
    "roomType": MessageLookupByLibrary.simpleMessage("Room type"),
    "roomTypeTips": MessageLookupByLibrary.simpleMessage("Type..."),
    "roomWelcome": MessageLookupByLibrary.simpleMessage("Welcome!!"),
    "roomWillCloseTips": m211,
    "roomsUser": m212,
    "round": MessageLookupByLibrary.simpleMessage("Round "),
    "roundVoting": m213,
    "rules": MessageLookupByLibrary.simpleMessage("Rules"),
    "rules1": MessageLookupByLibrary.simpleMessage(
      "❌ Asking for personal information",
    ),
    "rules2": MessageLookupByLibrary.simpleMessage(
      "❌ Harassing or bullying other users",
    ),
    "rules3": MessageLookupByLibrary.simpleMessage("❌ Posting sexual content"),
    "rules4": MessageLookupByLibrary.simpleMessage(
      "❌ Posting religious or political content",
    ),
    "rules5": MessageLookupByLibrary.simpleMessage(
      "❌ Using abusive inappropriate language",
    ),
    "rulesIntro": MessageLookupByLibrary.simpleMessage(
      "Following behaviours will lead to permanent account ban.",
    ),
    "rush": MessageLookupByLibrary.simpleMessage("Rush"),
    "sActionPlayHint": MessageLookupByLibrary.simpleMessage("Play"),
    "sActionPreviewHint": MessageLookupByLibrary.simpleMessage("Preview"),
    "sActionSelectHint": MessageLookupByLibrary.simpleMessage("Select"),
    "sActionSwitchPathLabel": MessageLookupByLibrary.simpleMessage(
      "Switch Path",
    ),
    "sActionUseCameraHint": MessageLookupByLibrary.simpleMessage("Use Camera"),
    "sNameDurationLabel": MessageLookupByLibrary.simpleMessage("Duration"),
    "sTypeAudioLabel": MessageLookupByLibrary.simpleMessage("Audio"),
    "sTypeImageLabel": MessageLookupByLibrary.simpleMessage("Image"),
    "sTypeOtherLabel": MessageLookupByLibrary.simpleMessage("Other resource"),
    "sTypeVideoLabel": MessageLookupByLibrary.simpleMessage("Video"),
    "sUnitAssetCountLabel": MessageLookupByLibrary.simpleMessage("count"),
    "safeDistanceProtection": MessageLookupByLibrary.simpleMessage(
      "Safe distance protection",
    ),
    "safeDistanceProtectionDesc": MessageLookupByLibrary.simpleMessage(
      "Doesn\'t match users within 500m of me",
    ),
    "safeMode": MessageLookupByLibrary.simpleMessage(
      "Safe mode activated, your information is under our protection.",
    ),
    "safeModeContent": MessageLookupByLibrary.simpleMessage(
      "Safe mode protect your information.",
    ),
    "safeModeContentToast": MessageLookupByLibrary.simpleMessage(
      "Safe mode activated, also can change to free mode here!",
    ),
    "safeModeDetailContent": MessageLookupByLibrary.simpleMessage(
      "Under safe mode we will protect your privacy and secure your personal information from the other users.",
    ),
    "safeModeName": MessageLookupByLibrary.simpleMessage("Safe mode"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "saveAvatar": MessageLookupByLibrary.simpleMessage("Save avatar"),
    "saveFailed": MessageLookupByLibrary.simpleMessage("Save failed"),
    "saveSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Saved successfully",
    ),
    "savedGallery": MessageLookupByLibrary.simpleMessage("Saved to Gallery!"),
    "sayFreeHasSent": MessageLookupByLibrary.simpleMessage("Has been sent"),
    "sayFreeWink": m214,
    "sayFreeWinkRepeat": m215,
    "sayHello": MessageLookupByLibrary.simpleMessage("[Say hello]"),
    "sayHi": MessageLookupByLibrary.simpleMessage("Say hi"),
    "sayHiBottomTip": m216,
    "sayHiGiftSendChat": MessageLookupByLibrary.simpleMessage("Send to chat"),
    "sayHiLimitTitle": m217,
    "sayHiSubtitle": MessageLookupByLibrary.simpleMessage(
      "This limit encourages thoughtful choices. New opportunities available after a while",
    ),
    "sayHiTitle": m218,
    "sayHiToYourMate": MessageLookupByLibrary.simpleMessage(
      "Say hi to your mate!",
    ),
    "saySomething": m219,
    "scanning": MessageLookupByLibrary.simpleMessage("Scanning..."),
    "score": MessageLookupByLibrary.simpleMessage("Score"),
    "scoreAddNum": MessageLookupByLibrary.simpleMessage(
      "got it right and score ",
    ),
    "scoresPerformersHere": MessageLookupByLibrary.simpleMessage(
      "Scores for all performers are displayed here.",
    ),
    "screenMessage": MessageLookupByLibrary.simpleMessage("Screen message"),
    "screenshot": MessageLookupByLibrary.simpleMessage("Screenshot"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchByFamilyID": MessageLookupByLibrary.simpleMessage(
      "Search by family ID",
    ),
    "searchByRoomId": MessageLookupByLibrary.simpleMessage("Search by room ID"),
    "searchByUserId": MessageLookupByLibrary.simpleMessage("Search by user ID"),
    "searchByUserNickname": MessageLookupByLibrary.simpleMessage(
      "Search by user nickname",
    ),
    "searchFamilyTip": MessageLookupByLibrary.simpleMessage(
      "Search family name or ID",
    ),
    "searchForSongs": MessageLookupByLibrary.simpleMessage("Search for songs"),
    "searchHashtag": MessageLookupByLibrary.simpleMessage("Search topic"),
    "searchHistory": MessageLookupByLibrary.simpleMessage("Search History"),
    "searchResult": MessageLookupByLibrary.simpleMessage("Search result"),
    "searchResults": MessageLookupByLibrary.simpleMessage("Search Results"),
    "searchRoomIdUserId": MessageLookupByLibrary.simpleMessage(
      "Search by room ID/user ID",
    ),
    "searchSticker": MessageLookupByLibrary.simpleMessage(
      "Click it! You can search for any sticker you want.",
    ),
    "searchStickers": MessageLookupByLibrary.simpleMessage("Search stickers"),
    "searchTheFamilyUser": MessageLookupByLibrary.simpleMessage(
      "Search family members by user nickname",
    ),
    "searchUniqueIds": MessageLookupByLibrary.simpleMessage(
      "Search unique IDs",
    ),
    "searchUserName": MessageLookupByLibrary.simpleMessage("Search user name"),
    "secondClickToRead": MessageLookupByLibrary.simpleMessage(
      "Second, click it to read the text with your voice. ",
    ),
    "secondStep": MessageLookupByLibrary.simpleMessage("Second step"),
    "secondsTimeout": m220,
    "seeMoreReplies": MessageLookupByLibrary.simpleMessage(
      "The reported content has been hidden",
    ),
    "select": MessageLookupByLibrary.simpleMessage("Select"),
    "selectAGift": MessageLookupByLibrary.simpleMessage("Select a gift"),
    "selectAll": MessageLookupByLibrary.simpleMessage("Select all"),
    "selectBackground": MessageLookupByLibrary.simpleMessage(
      "Select background",
    ),
    "selectCountry": MessageLookupByLibrary.simpleMessage("Select country"),
    "selectCover": MessageLookupByLibrary.simpleMessage(
      "Please upload the family cover.",
    ),
    "selectDuration": MessageLookupByLibrary.simpleMessage(
      "Select duration(minutes)",
    ),
    "selectFilter": MessageLookupByLibrary.simpleMessage("Select filter"),
    "selectFriends": MessageLookupByLibrary.simpleMessage("Select friends"),
    "selectGift": MessageLookupByLibrary.simpleMessage("Select gift"),
    "selectGiftType": MessageLookupByLibrary.simpleMessage("Select gift type"),
    "selectLeastInterest": MessageLookupByLibrary.simpleMessage(
      "Please select at least one interest",
    ),
    "selectRing": MessageLookupByLibrary.simpleMessage(
      "Select a ring to propose",
    ),
    "selectRoom": MessageLookupByLibrary.simpleMessage("Select room"),
    "selectSendingTime": MessageLookupByLibrary.simpleMessage(
      "Select sending time",
    ),
    "selectSinglePhoto": MessageLookupByLibrary.simpleMessage(
      "Select a single photo",
    ),
    "selectSuitor": MessageLookupByLibrary.simpleMessage(
      "Select your soulmate",
    ),
    "selectTheLetter": MessageLookupByLibrary.simpleMessage(
      "Select the letter",
    ),
    "selectTheTopic": MessageLookupByLibrary.simpleMessage("Select the topic"),
    "selectTheUser": MessageLookupByLibrary.simpleMessage("Select the user"),
    "selectTheVideo": MessageLookupByLibrary.simpleMessage("Select the video"),
    "selectTheme": MessageLookupByLibrary.simpleMessage("Select Theme"),
    "selectTime": MessageLookupByLibrary.simpleMessage("Select Time"),
    "selectTimes": MessageLookupByLibrary.simpleMessage("Select times"),
    "selectUpTen": MessageLookupByLibrary.simpleMessage(
      "You can select up to 10 tags",
    ),
    "selectYourAppLan": MessageLookupByLibrary.simpleMessage(
      "Select your app language",
    ),
    "selectYourAvatar": MessageLookupByLibrary.simpleMessage(
      "Please select your avatar",
    ),
    "selectYourCity": MessageLookupByLibrary.simpleMessage("Select your city"),
    "selectYourCountry": MessageLookupByLibrary.simpleMessage(
      "Select your country",
    ),
    "selectYourFavorite": MessageLookupByLibrary.simpleMessage(
      "Select your favorite",
    ),
    "selectYourPunishment": MessageLookupByLibrary.simpleMessage(
      "Select your punishment",
    ),
    "selectYourRoom": MessageLookupByLibrary.simpleMessage("Select your room"),
    "selected": MessageLookupByLibrary.simpleMessage("Selected"),
    "selectedFruits": MessageLookupByLibrary.simpleMessage("Selected Fruits:"),
    "selectedTopic": MessageLookupByLibrary.simpleMessage("Selected topic"),
    "selectedTopicCount": m221,
    "selfDelete": MessageLookupByLibrary.simpleMessage(
      "Auto-delete after viewing",
    ),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendAGift": MessageLookupByLibrary.simpleMessage(
      "Please send a gift to show your appreciation.",
    ),
    "sendAKey": MessageLookupByLibrary.simpleMessage("Send a key"),
    "sendALuckyBag": MessageLookupByLibrary.simpleMessage("Send a lucky bag"),
    "sendAndUnlock": m222,
    "sendBottleFirstContent": MessageLookupByLibrary.simpleMessage(
      "You haven\'t sent a bottle today, please send a bottle first.",
    ),
    "sendBottleFirstTitle": MessageLookupByLibrary.simpleMessage(
      "To continue, send your today\'s bottle first.",
    ),
    "sendChatModeRequest": m223,
    "sendFeedback": MessageLookupByLibrary.simpleMessage("Send a feedback"),
    "sendGiftCanIncreaseBlessValue": MessageLookupByLibrary.simpleMessage(
      "Send gifts can increase bless value",
    ),
    "sendGiftFailNoCamp": MessageLookupByLibrary.simpleMessage(
      "Not joined the camp, can\'t send gifts",
    ),
    "sendGiftGetIntimacy": MessageLookupByLibrary.simpleMessage(
      "Send gift to get intimacy",
    ),
    "sendGiftTips": MessageLookupByLibrary.simpleMessage(
      "Send goods need use a Gift box",
    ),
    "sendGiftToAdmin": MessageLookupByLibrary.simpleMessage(
      "Send free newcomer gift to the admin",
    ),
    "sendGiftToOwner": MessageLookupByLibrary.simpleMessage(
      "Send free newcomer gift to the owner",
    ),
    "sendGifts": MessageLookupByLibrary.simpleMessage("Send Gifts"),
    "sendGiftsSupportHer": MessageLookupByLibrary.simpleMessage(
      "Send gifts to support her",
    ),
    "sendGiftsSupportHim": MessageLookupByLibrary.simpleMessage(
      "Send gifts to support him",
    ),
    "sendGoodsByUser": m224,
    "sendImageInRoom": MessageLookupByLibrary.simpleMessage(
      "Send image in room",
    ),
    "sendImageInRoomDesc": MessageLookupByLibrary.simpleMessage(
      "You can send image in room",
    ),
    "sendKeys": MessageLookupByLibrary.simpleMessage("Send keys"),
    "sendLowercase": MessageLookupByLibrary.simpleMessage("send"),
    "sendLuckyBag": MessageLookupByLibrary.simpleMessage("Send a lucky bag"),
    "sendLuckyBagWithoutA": MessageLookupByLibrary.simpleMessage(
      "Send lucky bag",
    ),
    "sendSomethingNew": MessageLookupByLibrary.simpleMessage(
      "Send something new",
    ),
    "sendSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Sent successfully",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage("Send to"),
    "sendToChat": MessageLookupByLibrary.simpleMessage("Send to chat"),
    "sendToFriend": MessageLookupByLibrary.simpleMessage("Send to friend"),
    "sendUser": MessageLookupByLibrary.simpleMessage("send"),
    "sendWink": MessageLookupByLibrary.simpleMessage("Send a wink"),
    "sendYouGift": m225,
    "sendYouQuestion": m226,
    "senderWealthAddNumExp": m227,
    "sendingImmediately": MessageLookupByLibrary.simpleMessage(
      "sending immediately",
    ),
    "sendingMoment": MessageLookupByLibrary.simpleMessage("Sending moment…"),
    "sendingTime": MessageLookupByLibrary.simpleMessage("Sending time:"),
    "sent": MessageLookupByLibrary.simpleMessage("Sent"),
    "sentButReject": MessageLookupByLibrary.simpleMessage(
      "This message is successfully sent but rejected by the receiver",
    ),
    "sentMsgOnceTakeScreen": MessageLookupByLibrary.simpleMessage(
      "A message will be sent to user once you take screen of the chat.",
    ),
    "sentRankExplain": MessageLookupByLibrary.simpleMessage(
      "Ranking is based on the diamond value of gifts you sent",
    ),
    "sentYouAMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "sent you a magic box key",
    ),
    "sentYouGift": MessageLookupByLibrary.simpleMessage("Sent you a gift"),
    "serviceExpert": MessageLookupByLibrary.simpleMessage(
      "Customer service expert",
    ),
    "serviceExpertDesc": MessageLookupByLibrary.simpleMessage(
      "Enjoy the best service from our customer service expert.",
    ),
    "setAsMaster": MessageLookupByLibrary.simpleMessage("Set as Elders"),
    "setAsVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Set as Vice-Family Leader",
    ),
    "setAvatar": MessageLookupByLibrary.simpleMessage("Set avatar"),
    "setMicWhenRoomLevelReaches": m228,
    "setPassword": MessageLookupByLibrary.simpleMessage("Set password"),
    "setPatriarch": MessageLookupByLibrary.simpleMessage(
      "Set as Family Leader",
    ),
    "setRemarks": MessageLookupByLibrary.simpleMessage("Set Remarks"),
    "setSuccessfully": MessageLookupByLibrary.simpleMessage("Set successfully"),
    "setTheCity": MessageLookupByLibrary.simpleMessage("Set the city"),
    "setToAvatar": MessageLookupByLibrary.simpleMessage("Set to avatar"),
    "setUp": MessageLookupByLibrary.simpleMessage("Set up"),
    "setUpProfile": MessageLookupByLibrary.simpleMessage("Set up profile"),
    "setVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Set as Vice-Family Leader",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "setupAdmin": MessageLookupByLibrary.simpleMessage("Setup admin"),
    "sexGuideText": MessageLookupByLibrary.simpleMessage(
      "Once gender is selected, it cannot be modified. Please choose carefully.",
    ),
    "sexualHarassment": MessageLookupByLibrary.simpleMessage(
      "sexual harassment",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Share"),
    "shareApp": MessageLookupByLibrary.simpleMessage("Share app"),
    "shareAppContent": MessageLookupByLibrary.simpleMessage(
      "Winker~Free voice call & quizzes with people around the world!",
    ),
    "shareContent": MessageLookupByLibrary.simpleMessage(
      "Hey~ Never miss such awesome moment.",
    ),
    "shareEventTagContent": MessageLookupByLibrary.simpleMessage(
      "Come join the special event on Winker now!",
    ),
    "shareFail": MessageLookupByLibrary.simpleMessage(
      "Failed to share, please try again",
    ),
    "shareHaContent": MessageLookupByLibrary.simpleMessage(
      "Share the happiness of Winker with your friends.",
    ),
    "shareHalama": MessageLookupByLibrary.simpleMessage(
      "Share Winker to your mate",
    ),
    "shareMomentPhotoToAvatar": MessageLookupByLibrary.simpleMessage(
      "I turned my photo into this profile photo provided by Winker to change my avatar!",
    ),
    "shareMomentTodayContent": MessageLookupByLibrary.simpleMessage(
      "Your stories would gain more likes and friends.",
    ),
    "shareScreenShotTo": MessageLookupByLibrary.simpleMessage(
      "Share screen shot to",
    ),
    "shareSuccess": MessageLookupByLibrary.simpleMessage("Shared successfully"),
    "shareTheRoom": MessageLookupByLibrary.simpleMessage("Shared the room."),
    "shareToFriends": MessageLookupByLibrary.simpleMessage("Share to friends"),
    "shareToMoment": MessageLookupByLibrary.simpleMessage("Share to moment"),
    "shareToOthers": MessageLookupByLibrary.simpleMessage("Share to others"),
    "shareToYouFriends": MessageLookupByLibrary.simpleMessage(
      "Share to you friends",
    ),
    "shareYourMomentContent": MessageLookupByLibrary.simpleMessage(
      "Your stories would gain more likes and friends.",
    ),
    "shareYourMomentTitle": MessageLookupByLibrary.simpleMessage(
      "Share your moment",
    ),
    "shareYourRoom": MessageLookupByLibrary.simpleMessage(
      "Invite your friends to chat!",
    ),
    "shared": MessageLookupByLibrary.simpleMessage("Shared"),
    "she": MessageLookupByLibrary.simpleMessage("she"),
    "sheFollowYou": MessageLookupByLibrary.simpleMessage("She’s following you"),
    "sheLiving": MessageLookupByLibrary.simpleMessage(
      "She living in: your nearby!",
    ),
    "showIdentity": MessageLookupByLibrary.simpleMessage("Show identity"),
    "showInWinkerWorld": MessageLookupByLibrary.simpleMessage(
      "Show in Winker world!",
    ),
    "showInWorld": MessageLookupByLibrary.simpleMessage(
      "Show in Winker world!",
    ),
    "showOnMic": MessageLookupByLibrary.simpleMessage("Show on mic position"),
    "showOnProfile": MessageLookupByLibrary.simpleMessage("Show on profile"),
    "showOnRoomProfile": MessageLookupByLibrary.simpleMessage(
      "Show on room profile",
    ),
    "showYourCharm": MessageLookupByLibrary.simpleMessage(
      "Make the first move to show your charm!",
    ),
    "signContent": MessageLookupByLibrary.simpleMessage(
      "Sign in for 7 days to get a surprise",
    ),
    "signIn": MessageLookupByLibrary.simpleMessage("Sign in"),
    "signInApple": MessageLookupByLibrary.simpleMessage("Sign in with Apple"),
    "signInBackpack": MessageLookupByLibrary.simpleMessage(
      "Task rewards have been sent to backpack.",
    ),
    "signInCoins": MessageLookupByLibrary.simpleMessage(
      "Sign in to receive golds",
    ),
    "signInDays": m229,
    "signInFaceGoogle": MessageLookupByLibrary.simpleMessage(
      "Sign in with Google",
    ),
    "signInFacebook": MessageLookupByLibrary.simpleMessage(
      "Sign in with Facebook",
    ),
    "signInFailed": MessageLookupByLibrary.simpleMessage(
      "Sign in failed, please retry",
    ),
    "signInForDays": m230,
    "signInReceived": MessageLookupByLibrary.simpleMessage("Received"),
    "signInRewardNumCoins": m231,
    "signInRewards": MessageLookupByLibrary.simpleMessage("Daily rewards"),
    "signInWithPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Sign in with Mobile",
    ),
    "signReceiveRewards": MessageLookupByLibrary.simpleMessage(
      "Sign in to receive rewards",
    ),
    "signedInNumDay": m232,
    "signedInToday": MessageLookupByLibrary.simpleMessage("Signed in today"),
    "silverFamily": MessageLookupByLibrary.simpleMessage("Silver Family"),
    "singleCycle": MessageLookupByLibrary.simpleMessage("Single cycle"),
    "skillCD": MessageLookupByLibrary.simpleMessage(
      "Cooling down, please try again later",
    ),
    "skin": MessageLookupByLibrary.simpleMessage("Skin"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "skipPerformConfirm": MessageLookupByLibrary.simpleMessage(
      "The performance will be skipped. The point of this round will be counted now. Still want to skip?",
    ),
    "skipPerformanceConfirm": MessageLookupByLibrary.simpleMessage(
      "The performance will be skipped. The point of this round will be counted now. Still want to skip?",
    ),
    "smokeDrugsDrinking": MessageLookupByLibrary.simpleMessage(
      "smoke,drugs,drinking",
    ),
    "snapchat": MessageLookupByLibrary.simpleMessage("Snapchat"),
    "someoneAtYou": MessageLookupByLibrary.simpleMessage("Someone @you"),
    "someoneAtYouWithBrackets": MessageLookupByLibrary.simpleMessage("[@you]"),
    "someoneCallingYou": MessageLookupByLibrary.simpleMessage(
      "Someone is calling you!",
    ),
    "someoneMaybeYouLove": MessageLookupByLibrary.simpleMessage(
      "Chat with your soulmate",
    ),
    "somethingIsBroken": MessageLookupByLibrary.simpleMessage(
      "Something is broken",
    ),
    "sorryNotFoundVoiceMatch": MessageLookupByLibrary.simpleMessage(
      "Sorry, we can\'t find a perfect user for you now, please try again later.",
    ),
    "soundEffect": MessageLookupByLibrary.simpleMessage("Sound effect"),
    "soundEffectIsPlaying": MessageLookupByLibrary.simpleMessage(
      "Sound effect is playing",
    ),
    "spam": MessageLookupByLibrary.simpleMessage("Spam"),
    "speakTooFast": MessageLookupByLibrary.simpleMessage(
      "Hey, be patient~Grab a coffee and wait till you get a reply!",
    ),
    "speaker": MessageLookupByLibrary.simpleMessage("Speaker"),
    "specialDiscount": MessageLookupByLibrary.simpleMessage("Special discount"),
    "specialDiscountGift": MessageLookupByLibrary.simpleMessage(
      "Special discount gift",
    ),
    "specifyMode": MessageLookupByLibrary.simpleMessage("Specify mode"),
    "specifyPerformer": MessageLookupByLibrary.simpleMessage(
      "Specify performer:",
    ),
    "spendCoinsToSendGift": m233,
    "spendLetterConfirm": m234,
    "stageRewards": MessageLookupByLibrary.simpleMessage("Agency incentives"),
    "stalking": MessageLookupByLibrary.simpleMessage("stalking"),
    "starSign": MessageLookupByLibrary.simpleMessage("Zodiac sign"),
    "starTime": MessageLookupByLibrary.simpleMessage("start time"),
    "starlightRewardNum": m235,
    "start": MessageLookupByLibrary.simpleMessage("Start"),
    "startAgain": MessageLookupByLibrary.simpleMessage("Start again"),
    "startDateAndTime": MessageLookupByLibrary.simpleMessage(
      "Start date and time",
    ),
    "startPK": MessageLookupByLibrary.simpleMessage("Start PK"),
    "startQuestionGame": m236,
    "statePublishSuccess": MessageLookupByLibrary.simpleMessage(
      "Status set successfully, will expire after 24h",
    ),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "statusExpired": MessageLookupByLibrary.simpleMessage(
      "The Status expired, please reset your status",
    ),
    "stay1minInvite": MessageLookupByLibrary.simpleMessage(
      "It’s been a minute since he entered the room, invite him to chat on the mic",
    ),
    "stay2minFollow": MessageLookupByLibrary.simpleMessage(
      "We have been chatting for a while. Follow me and you will be notified immediately if you create a room in the future.",
    ),
    "stayInRoomNumMin": m237,
    "stayInThePage": MessageLookupByLibrary.simpleMessage(
      "Stay in the page while uploading",
    ),
    "stayTimeTag": m238,
    "stereo": MessageLookupByLibrary.simpleMessage("Stereo"),
    "stickerLimit": MessageLookupByLibrary.simpleMessage(
      "Stickers reached limit",
    ),
    "stopEditProfile": MessageLookupByLibrary.simpleMessage(
      "Stop editing your profile?",
    ),
    "storageAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Storage access not enabled",
    ),
    "storagePermission": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" would like to collect read and write storage data to enable sending pictures in messages only when the app is in use.",
    ),
    "store": MessageLookupByLibrary.simpleMessage("Store"),
    "studio": MessageLookupByLibrary.simpleMessage("Studio"),
    "subject": MessageLookupByLibrary.simpleMessage("subject"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "submitRepeated": MessageLookupByLibrary.simpleMessage(
      "Please don’t submit repeated applications",
    ),
    "submitSuccess": MessageLookupByLibrary.simpleMessage(
      "Submitted successfully",
    ),
    "submitSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Submit successfully",
    ),
    "subscribe": MessageLookupByLibrary.simpleMessage("Subscribe"),
    "subscribed": MessageLookupByLibrary.simpleMessage("Subscribed"),
    "successfully": MessageLookupByLibrary.simpleMessage("Successfully"),
    "successfullyApplied": MessageLookupByLibrary.simpleMessage(
      "successfully applied",
    ),
    "suggestion": MessageLookupByLibrary.simpleMessage("Suggestion"),
    "suggestions": MessageLookupByLibrary.simpleMessage("Suggestions"),
    "superBenefits": MessageLookupByLibrary.simpleMessage("Super benefits"),
    "support": MessageLookupByLibrary.simpleMessage("Support"),
    "supporter": MessageLookupByLibrary.simpleMessage("Supporter"),
    "sure": MessageLookupByLibrary.simpleMessage("Sure"),
    "sureDelete": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete it?",
    ),
    "sureKickOutRoom": MessageLookupByLibrary.simpleMessage(
      "Sure to kick this user out of room?he/she will not able to enter your room in 24h",
    ),
    "sureLeaveTheList": MessageLookupByLibrary.simpleMessage(
      "Leave the list will not able to perform. Still leave?",
    ),
    "sureSpendDiamonds": m239,
    "sureStopVideo": MessageLookupByLibrary.simpleMessage(
      "Are you sure wanna stop watching videos?",
    ),
    "sureToCloseLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Are you sure close the Lucky wheel? The entry fee will be refunded to participants",
    ),
    "sureToJoinLuckyWheel": m240,
    "sureTurnoff": MessageLookupByLibrary.simpleMessage(
      "Confirm to turnoff the turntable?",
    ),
    "sureUnlockRoom": MessageLookupByLibrary.simpleMessage(
      "Sure to unlock your room",
    ),
    "suspiciousLink": MessageLookupByLibrary.simpleMessage(
      "this user sent me suspicious link",
    ),
    "swipeLeftToPass": MessageLookupByLibrary.simpleMessage(
      "SWIPE LEFT TO PASS",
    ),
    "swipeRightToLike": MessageLookupByLibrary.simpleMessage(
      "SWIPE RIGHT TO LIKE",
    ),
    "swipeUpToCancel": MessageLookupByLibrary.simpleMessage(
      "Swipe up to cancel",
    ),
    "switchAvatar": MessageLookupByLibrary.simpleMessage("Change an avatar"),
    "switchMic": MessageLookupByLibrary.simpleMessage(
      "Are you sure change the mic position?",
    ),
    "switchMode": MessageLookupByLibrary.simpleMessage(
      "Want to switch it to this mode?",
    ),
    "switchRoomCloseCalculator": MessageLookupByLibrary.simpleMessage(
      "Switch the room mode will close the calculator.",
    ),
    "switchRoomMode": MessageLookupByLibrary.simpleMessage("Switch room mode"),
    "switchRoomModeContent": MessageLookupByLibrary.simpleMessage(
      "Are you sure change the room mode?",
    ),
    "switchRoomTitle": MessageLookupByLibrary.simpleMessage("Change room"),
    "switchTalentMode": MessageLookupByLibrary.simpleMessage(
      "Switch now will close the talent room. The perform will end. Still want to switch?",
    ),
    "tabChat": MessageLookupByLibrary.simpleMessage("Chat"),
    "tabHome": MessageLookupByLibrary.simpleMessage("Home"),
    "tabMine": MessageLookupByLibrary.simpleMessage("Mine"),
    "tabMoments": MessageLookupByLibrary.simpleMessage("Moments"),
    "tag": MessageLookupByLibrary.simpleMessage("Tag"),
    "takeMic": MessageLookupByLibrary.simpleMessage("Take Mic"),
    "takeMicFirst": MessageLookupByLibrary.simpleMessage(
      "Please take the mic first",
    ),
    "takeMicToJoinGame": MessageLookupByLibrary.simpleMessage(
      "Take the mic first to join the game",
    ),
    "takeTheMic": MessageLookupByLibrary.simpleMessage("Take the Mic"),
    "takeTheTest": MessageLookupByLibrary.simpleMessage("Take the test"),
    "talent": MessageLookupByLibrary.simpleMessage("Talent"),
    "talentBegins": MessageLookupByLibrary.simpleMessage("Talent room begins!"),
    "talentRule": MessageLookupByLibrary.simpleMessage(
      "1. Welcome to the talent room! In this room, you can be a performer and give a fantastic show. Or you can be a active audience and BOOM for the performers.\n2. This room includes two mode: grab mic mode or specify mode\n  a. Grab mic mode: User will be invited by the host (or room owner/admin. The following only takes the host as an example) or can apply to join to the performer list and become the performer candidate. The host will start the mode and every candidate can grab the mic. The one who grab the mic successfully will be on stage and perform. After performing, the guest can comment and after that, the host will start a new round.\n  b. Specify mode: The candidates in the performer list will be specified by the host and get on to the stage to perform.\n3. What can the audience do?\n  a. Send gift to support the performer and increase the points. Ordinary gift worth 1 diamond = 1 point. Special gift (3 gifts displayed when performing) worth 1 diamond = 2 points\n  b. Click [BOOM] to support the performer. It\'s free! 1 audience [BOOM] = 10 points. 1 guest [BOOM] = 100 points.\n4. How can i be the guest?\n  a. The guest can be only invited by the host (or room owner/admin)\n  b. The guest can also send gift, click [BOOM] and comment to the performer.\n5. How can i be the host?\n  a. The host can be only invited by the room owner or admin.\n6. The points of all performers will be accumulated and displayed on the cumulative scoreboard according to the ranking.\n7. If you leave mic, leave room or being kicked out while performing, the points you have already got will not be counted.",
    ),
    "talkLittleBitLonger": MessageLookupByLibrary.simpleMessage(
      "Talk a little bit longer. Maybe you will find more surprise! Still want to hang up?",
    ),
    "talkSomething": MessageLookupByLibrary.simpleMessage(
      "Talk about something…",
    ),
    "tapAgainToExit": MessageLookupByLibrary.simpleMessage(
      "Tap back again to exit",
    ),
    "tapToOpenMic": MessageLookupByLibrary.simpleMessage("Tap to open the mic"),
    "tapToReport": MessageLookupByLibrary.simpleMessage("Tap here to report"),
    "tapToSee": MessageLookupByLibrary.simpleMessage("Tap to see"),
    "tapToView": MessageLookupByLibrary.simpleMessage(
      "tap here to view hidden message",
    ),
    "task": MessageLookupByLibrary.simpleMessage("Task"),
    "taskBadge": MessageLookupByLibrary.simpleMessage("Task badge"),
    "taskRewardTitle": MessageLookupByLibrary.simpleMessage(
      "Free rewards! completed task now to get!",
    ),
    "taskToAchieve": MessageLookupByLibrary.simpleMessage("To Achieve"),
    "tasks": MessageLookupByLibrary.simpleMessage("Tasks"),
    "termOfUse": MessageLookupByLibrary.simpleMessage("Terms of use"),
    "terrorism": MessageLookupByLibrary.simpleMessage("terrorism"),
    "test": MessageLookupByLibrary.simpleMessage("Test"),
    "testNow": MessageLookupByLibrary.simpleMessage("Test now"),
    "text": MessageLookupByLibrary.simpleMessage("Text"),
    "thankForSubmit": MessageLookupByLibrary.simpleMessage(
      "Thanks for your submit",
    ),
    "thanksForYourRating": MessageLookupByLibrary.simpleMessage(
      "Thanks for your rating",
    ),
    "thanksReachingOut": MessageLookupByLibrary.simpleMessage(
      "Thanks for reaching out about your experience",
    ),
    "theFamilyHasBeenFrozenUnableToDistributeLuckyBags":
        MessageLookupByLibrary.simpleMessage(
          "The family has been frozen, unable to distribute lucky bags",
        ),
    "theFamilyLeaderCannotLeaveTheFamilyDirectlyYouCan":
        MessageLookupByLibrary.simpleMessage(
          "The family leader cannot leave the family directly. You can contact the official customer service for assistance.",
        ),
    "theFamilyLuckyBagHasBeenSentPleaseGoTo": MessageLookupByLibrary.simpleMessage(
      "The family lucky bag has been sent, please go to the family room to grab it!",
    ),
    "theFamilyLuckyBagHasNotBeenFullyClaimedYet":
        MessageLookupByLibrary.simpleMessage(
          "The family lucky bag has not been fully claimed yet, hurry up and grab it in the family room!",
        ),
    "theFamilyTreasuryWillBeUsedForTheFamilyBlessing":
        MessageLookupByLibrary.simpleMessage(
          "The Family Treasury will be used for the family lucky bag, and family members can get more benefits and rewards!",
        ),
    "theGamePositionIsFull": MessageLookupByLibrary.simpleMessage(
      "The game position is full",
    ),
    "theGamePositionIsLocked": MessageLookupByLibrary.simpleMessage(
      "The game position is locked",
    ),
    "theHostJustKickedYouOutOfTheGamePlease":
        MessageLookupByLibrary.simpleMessage(
          "The host just kicked you out of the game, please try again later",
        ),
    "theJackPot": MessageLookupByLibrary.simpleMessage("the jackPot"),
    "theLuckyBagHasBeenFullyClaimed": MessageLookupByLibrary.simpleMessage(
      "The lucky bag has been all claimed!",
    ),
    "theMicAreFullUnableToJoinTheGame": MessageLookupByLibrary.simpleMessage(
      "The mic are full, unable to join the game",
    ),
    "theNicknameYouEnteredIsTooShort": MessageLookupByLibrary.simpleMessage(
      "The nickname you entered is too short",
    ),
    "theOppositeRoomDefenseBuff": m241,
    "theOppositeRoomPointsChange": m242,
    "theOppositeRoomReceived": MessageLookupByLibrary.simpleMessage(
      "The opposite room received",
    ),
    "thePerformanceIsEnd": MessageLookupByLibrary.simpleMessage(
      "the performance is end",
    ),
    "thePerformerListFull": MessageLookupByLibrary.simpleMessage(
      "The performer list is full",
    ),
    "thePkEndInDraw": MessageLookupByLibrary.simpleMessage(
      "The PK end in a draw.",
    ),
    "theRoomOwnerEndsTheGame": MessageLookupByLibrary.simpleMessage(
      "The room owner ends the game.",
    ),
    "theVoiceMatchFindTips": MessageLookupByLibrary.simpleMessage(
      "Find friends who share the same tastes with you from voice test.",
    ),
    "theme": MessageLookupByLibrary.simpleMessage("Theme"),
    "thereAreTimeUntilTheStart": m243,
    "thereIsNoContentHere": MessageLookupByLibrary.simpleMessage(
      "There is no content here",
    ),
    "theyLikeYouBackUnlockChat": MessageLookupByLibrary.simpleMessage(
      "If they like you back you can unlock the chat.",
    ),
    "thinkAgain": MessageLookupByLibrary.simpleMessage("Think again"),
    "thinkWink": MessageLookupByLibrary.simpleMessage(
      "Thank you for your wink!",
    ),
    "thirdLoginCancel": MessageLookupByLibrary.simpleMessage(
      "Your account info will only be used for log in.  We will ensure all user in a private and safe environment.",
    ),
    "thirdLoginNetworkError": MessageLookupByLibrary.simpleMessage(
      "Network error, please try again.",
    ),
    "thirdStep": MessageLookupByLibrary.simpleMessage("Third step"),
    "thisAccountHadDeleted": MessageLookupByLibrary.simpleMessage(
      "This account has been deleted",
    ),
    "thisFamilyHasBeenDisbanded": MessageLookupByLibrary.simpleMessage(
      "This family has been disbanded.",
    ),
    "thisUserHadBanned": MessageLookupByLibrary.simpleMessage(
      "This user has been banned",
    ),
    "thisVoiceCallDisconnected": MessageLookupByLibrary.simpleMessage(
      "Sorry. This voice call is disconnected, please try again",
    ),
    "thisWeek": MessageLookupByLibrary.simpleMessage("This week"),
    "thisWontAffectOther": MessageLookupByLibrary.simpleMessage(
      "This won\'t affect other",
    ),
    "threeDayReject": MessageLookupByLibrary.simpleMessage(
      "Three days without processing will automatically reject",
    ),
    "throwAway": MessageLookupByLibrary.simpleMessage("Throw away"),
    "throwBottleInOcean": MessageLookupByLibrary.simpleMessage(
      "Click it, put what you want to say in a bottle, and throw it into the sea, someone may pick it up.",
    ),
    "throwTheBottle": MessageLookupByLibrary.simpleMessage("Throw a bottle"),
    "ticket": MessageLookupByLibrary.simpleMessage("Ticket"),
    "tiePk": MessageLookupByLibrary.simpleMessage("Tie PK"),
    "time": MessageLookupByLibrary.simpleMessage("Time"),
    "timeLeftToday": m244,
    "timeOfDuration": MessageLookupByLibrary.simpleMessage("Time of duration"),
    "timeToComment": MessageLookupByLibrary.simpleMessage(
      "The performance is end. Time to comment the performance",
    ),
    "times": MessageLookupByLibrary.simpleMessage("times"),
    "timesFree": m245,
    "timesNum": m246,
    "timesUsedUp": MessageLookupByLibrary.simpleMessage("Times used up"),
    "tipCompleteInfo": MessageLookupByLibrary.simpleMessage(
      "Complete my profile",
    ),
    "tipFollowBack": MessageLookupByLibrary.simpleMessage(
      "Follow back to become friends.",
    ),
    "tipHasFollowing": MessageLookupByLibrary.simpleMessage(
      "You are following this user.",
    ),
    "tipPassions": MessageLookupByLibrary.simpleMessage(
      "It\'s the perfect opportunity to show a little more about yourself.",
    ),
    "tipPublishInput": MessageLookupByLibrary.simpleMessage("What\'s fun?"),
    "tipSetAvatar": MessageLookupByLibrary.simpleMessage("Select your avatar"),
    "tips": MessageLookupByLibrary.simpleMessage("Tips"),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "titleEffectPreviewMsgContent": MessageLookupByLibrary.simpleMessage(
      "Welcome to the studio.",
    ),
    "titleGifters": MessageLookupByLibrary.simpleMessage("Title gifters"),
    "titleList": MessageLookupByLibrary.simpleMessage("Title list"),
    "titleVoiceVerifyGuide": MessageLookupByLibrary.simpleMessage(
      "How to get more replies?",
    ),
    "titleWearDesc": MessageLookupByLibrary.simpleMessage(
      "You can wear titles you earned in Winker. The source of the Title comes from the activities and achievements you finish in the Winker. The titles you are wearing will be displayed in the profile and room profile. You can wear a maximum of three titles.",
    ),
    "titleWearInstructions": MessageLookupByLibrary.simpleMessage(
      "Title wear instructions",
    ),
    "tmo": MessageLookupByLibrary.simpleMessage("TMO"),
    "to": MessageLookupByLibrary.simpleMessage("To"),
    "toAddIntimacy": MessageLookupByLibrary.simpleMessage("to add Intimacy"),
    "toChange": MessageLookupByLibrary.simpleMessage("to change"),
    "toChoose": MessageLookupByLibrary.simpleMessage("To choose"),
    "toLater": MessageLookupByLibrary.simpleMessage("To later"),
    "toMatch": MessageLookupByLibrary.simpleMessage(" to match"),
    "toUseProp": MessageLookupByLibrary.simpleMessage(
      "Do you want to use this prop?",
    ),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "todayBestMatch": MessageLookupByLibrary.simpleMessage(
      "Today\'s best match",
    ),
    "todayForCast": MessageLookupByLibrary.simpleMessage("Forcast for today"),
    "todayTimesRunOut": MessageLookupByLibrary.simpleMessage(
      "Today‘s times run out",
    ),
    "todayUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Today\'s upper limit: ",
    ),
    "todayWinnings": MessageLookupByLibrary.simpleMessage("Today\'s Winnings"),
    "together": MessageLookupByLibrary.simpleMessage("Together"),
    "togetherDays": m247,
    "togetherDaysNewLine": m248,
    "tomorrow": MessageLookupByLibrary.simpleMessage("Tomorrow"),
    "tomorrowGetMoreRewards": MessageLookupByLibrary.simpleMessage(
      "Tomorrow sign-in will get more rewards",
    ),
    "tooFastAndTakeARest": MessageLookupByLibrary.simpleMessage(
      "You are too fast, please take a rest...",
    ),
    "tooFrequentlyTip": MessageLookupByLibrary.simpleMessage(
      "Request too frequently, please try again later",
    ),
    "topCharming": MessageLookupByLibrary.simpleMessage("Top charming"),
    "topCharmingIs": MessageLookupByLibrary.simpleMessage(
      "The top charming is",
    ),
    "topIndex": m249,
    "topSupporter": MessageLookupByLibrary.simpleMessage("Top supporter"),
    "topSupporterIs": MessageLookupByLibrary.simpleMessage(
      "The top supporter is",
    ),
    "topWinnersOfToday": MessageLookupByLibrary.simpleMessage(
      "Top winners of today",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalMusic": m250,
    "totalPeople": m251,
    "transfer": MessageLookupByLibrary.simpleMessage("Transfer"),
    "transferCaptain": MessageLookupByLibrary.simpleMessage("Transfer Captain"),
    "treasureBoxRoomLevelLimit": m252,
    "treasureChestItemTips": MessageLookupByLibrary.simpleMessage(
      "Just received a treasure chest",
    ),
    "treasureChestNum": MessageLookupByLibrary.simpleMessage(
      "Treasure chest number: ",
    ),
    "treasureChestRecord": MessageLookupByLibrary.simpleMessage(
      "Treasure chest record",
    ),
    "treasureChestTips": MessageLookupByLibrary.simpleMessage(
      "The more treasure chests fans receive, the more generous rewards they will receive as a host !",
    ),
    "treasureTips": MessageLookupByLibrary.simpleMessage(
      "Come and share the rewards from the treasure now!",
    ),
    "treasureTitle": MessageLookupByLibrary.simpleMessage(
      "Task for the treasure",
    ),
    "treasureTotal": MessageLookupByLibrary.simpleMessage(
      "Treasure total today:",
    ),
    "triedTooMuchAndComeTomorrow": MessageLookupByLibrary.simpleMessage(
      "You tried too much today, please come back tomorrow.",
    ),
    "truth": MessageLookupByLibrary.simpleMessage("Truth"),
    "truthDare": MessageLookupByLibrary.simpleMessage("Truth & Dare"),
    "truthDarePeopleCount": m253,
    "truthDareQuestion": m254,
    "truthDareRunAway": m255,
    "truthGameIntroSubtitle1": MessageLookupByLibrary.simpleMessage(
      "Users are considered to have joined the game once they are on the mic.",
    ),
    "truthGameIntroSubtitle2": MessageLookupByLibrary.simpleMessage(
      "When the wheel is spinning, all players involved in the game have a chance of being selected to be chosen, completely randomly.",
    ),
    "truthGameIntroSubtitle3": MessageLookupByLibrary.simpleMessage(
      "The player who is drawn, completes either Truth or Dare, given randomly by the system. The drawn theme needs to be completed by the chosen player.",
    ),
    "truthGameIntroTitle1": MessageLookupByLibrary.simpleMessage(
      "Get ready to go on the mic.",
    ),
    "truthGameIntroTitle2": MessageLookupByLibrary.simpleMessage(
      "The wheel turns.",
    ),
    "truthGameIntroTitle3": MessageLookupByLibrary.simpleMessage(
      "Question archive",
    ),
    "truthOngoing": MessageLookupByLibrary.simpleMessage("Ongoing"),
    "truthSelected": MessageLookupByLibrary.simpleMessage("Selected"),
    "truthStart": MessageLookupByLibrary.simpleMessage("START"),
    "truthStartLimit": MessageLookupByLibrary.simpleMessage(
      "At least two or more people join the game",
    ),
    "tryAgain": MessageLookupByLibrary.simpleMessage("Try again"),
    "tryAnotherWay": MessageLookupByLibrary.simpleMessage(
      "Would you like to try another way?",
    ),
    "tryBottle": MessageLookupByLibrary.simpleMessage(
      "Try mystery letter, you can find more friends in Winker",
    ),
    "tryBtn": MessageLookupByLibrary.simpleMessage("Try"),
    "tryIt": MessageLookupByLibrary.simpleMessage("Try it"),
    "tryToTalkHerLabels": MessageLookupByLibrary.simpleMessage(
      "Try to talk her labels",
    ),
    "tryToTalkHisLabels": MessageLookupByLibrary.simpleMessage(
      "Try to talk his labels",
    ),
    "tryVoiceMatching": MessageLookupByLibrary.simpleMessage(
      "Try voice test, you can find more friends in Winker",
    ),
    "tryWriting": MessageLookupByLibrary.simpleMessage("Try writing like this"),
    "turnOn": MessageLookupByLibrary.simpleMessage("Turn On"),
    "turnOnNotifications": MessageLookupByLibrary.simpleMessage(
      "Turn on notifications to know new messages",
    ),
    "turnOnSwitchCanHearOppositeVoice": MessageLookupByLibrary.simpleMessage(
      "Turn on the switch can hear the opposite room voice(all mic)",
    ),
    "turnOnToKnowMessages": MessageLookupByLibrary.simpleMessage(
      "Turn on notifications to know new messages and matches.",
    ),
    "turntable": MessageLookupByLibrary.simpleMessage("Turntable"),
    "turntableGameFail": MessageLookupByLibrary.simpleMessage(
      "Sorry, you didn\'t win in this round.",
    ),
    "turntableGameNoJoin": MessageLookupByLibrary.simpleMessage(
      "You didn\'t participate in this round.",
    ),
    "turntableGameRules1": MessageLookupByLibrary.simpleMessage(
      "1. Choose the quantity of diamonds, and then choose a fruit to spend the diamonds on.",
    ),
    "turntableGameRules2": MessageLookupByLibrary.simpleMessage(
      "2. You can select up to 8 fruits in each round. There\' s no upper limit to the quantity of diamonds that you can spend.",
    ),
    "turntableGameRules3": MessageLookupByLibrary.simpleMessage(
      "3. For each round, you have 40 seconds to select fruits, after which the winning fruit will drawn.",
    ),
    "turntableGameRules4": MessageLookupByLibrary.simpleMessage(
      "4. If you have spend diamonds on the winning fruit, you\'ll win the corresponding prize.",
    ),
    "turntableGameRules5": MessageLookupByLibrary.simpleMessage(
      "5. The final interpretation right of this game belongs to Winker.",
    ),
    "turntableGameWin": m256,
    "turntableIsClosed": MessageLookupByLibrary.simpleMessage(
      "The turntable is closed.",
    ),
    "turntableResult": MessageLookupByLibrary.simpleMessage(
      "Turntable result: ",
    ),
    "turntableStartTip": MessageLookupByLibrary.simpleMessage(
      "You have been selected and you can play the turntable",
    ),
    "twentyOneCentury": MessageLookupByLibrary.simpleMessage("21st Century"),
    "twoDays": MessageLookupByLibrary.simpleMessage("2 Days"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage("Type a message"),
    "typeAnswer": m257,
    "typeInPcBrowser": MessageLookupByLibrary.simpleMessage(
      "Type in the following link on PC browser",
    ),
    "typeTourSuggestionsToUs": MessageLookupByLibrary.simpleMessage(
      "Type your suggestions to us...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("Typing..."),
    "unCoverIdentityAlert": MessageLookupByLibrary.simpleMessage(
      "Confirm to send uncover Winker profile invitation？",
    ),
    "unCoverIdentityMsg": MessageLookupByLibrary.simpleMessage(
      "Invite the other side to uncover Winker profile?",
    ),
    "unCoverIdentitySystemTip": m258,
    "unFollow": MessageLookupByLibrary.simpleMessage("Unfollow"),
    "unFollow2": MessageLookupByLibrary.simpleMessage("Unfollow"),
    "unFollowConfirm": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to unfollow this user?",
    ),
    "unLockFreeChatTips": MessageLookupByLibrary.simpleMessage(
      "Unlock free message chat will no longer increase intimacy, sending gifts can increase intimacy.",
    ),
    "unSubscribeTip": MessageLookupByLibrary.simpleMessage(
      "Unsubscribe event you will miss the surprise!",
    ),
    "unSupportedAssetType": MessageLookupByLibrary.simpleMessage(
      "Unsupported HEIC file type.",
    ),
    "unableToAccessAll": MessageLookupByLibrary.simpleMessage(
      "Unable to access all files on the device",
    ),
    "unblock": MessageLookupByLibrary.simpleMessage("Unblock"),
    "unblockSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Unblock successfully",
    ),
    "unburden": MessageLookupByLibrary.simpleMessage("Unburden"),
    "uncoverIdentity": MessageLookupByLibrary.simpleMessage("Uncover profile"),
    "uncoverLimit": MessageLookupByLibrary.simpleMessage(
      "You can only uncover once a day.",
    ),
    "underReview": MessageLookupByLibrary.simpleMessage("Under review"),
    "undercover": MessageLookupByLibrary.simpleMessage("Undercover"),
    "undercoverDefeat": MessageLookupByLibrary.simpleMessage(
      "Undercover defeat",
    ),
    "undercoverFaq": MessageLookupByLibrary.simpleMessage(
      "1.  At the start of the game, each person will be given a word. Please note that one of the words will be different from the others. Find out who the undercover is. Undercover, please hide your identity and survive until the end. \n2.  Each player has 30 seconds to describe their word in each round, taking turns. \n3.  After the descriptions are completed, all players have 20 seconds to vote. \n4.  The player with the highest number of votes will be eliminated. In case of a tie, a sudden death round will be conducted for those players to describe their words again. If no votes are cast, the game proceeds to the next round. ",
    ),
    "undercoverNotAllowedToSpeak": MessageLookupByLibrary.simpleMessage(
      "During the game, users who are not currently in the description round are not allowed to speak.",
    ),
    "undercoverTip": MessageLookupByLibrary.simpleMessage(
      "Please determine whether you are an undercover or a civilian, and keep your identity hidden or vote to eliminate the undercover.",
    ),
    "undercoverVictory": MessageLookupByLibrary.simpleMessage(
      "Undercover victory",
    ),
    "undone": MessageLookupByLibrary.simpleMessage("Undone"),
    "unfollow": MessageLookupByLibrary.simpleMessage("Unfollow"),
    "unfollowRoomWillRemoveAdminIdentity": MessageLookupByLibrary.simpleMessage(
      "Unfollow room will remove your admin identity",
    ),
    "unfollowSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Unfollow successfully",
    ),
    "unfortunately": MessageLookupByLibrary.simpleMessage("Unfortunately"),
    "unionIncome": MessageLookupByLibrary.simpleMessage("Agency Income"),
    "uniquePersonalId": MessageLookupByLibrary.simpleMessage(
      "Unique personal ID",
    ),
    "uniqueRoomId": MessageLookupByLibrary.simpleMessage("Unique room ID"),
    "unknownError": MessageLookupByLibrary.simpleMessage("Unknown error"),
    "unlimited": MessageLookupByLibrary.simpleMessage("Unlimited"),
    "unlimitedChat": MessageLookupByLibrary.simpleMessage("Unlimited chat"),
    "unlimitedChatDesc": MessageLookupByLibrary.simpleMessage(
      "You can chat whatever you want without restriction.",
    ),
    "unlock": MessageLookupByLibrary.simpleMessage("Unlock"),
    "unlockChat": MessageLookupByLibrary.simpleMessage(
      "Send gift to unlock the chat",
    ),
    "unlockFeatureDigest": MessageLookupByLibrary.simpleMessage(
      "[Unlocked feature]",
    ),
    "unlockHerAnswer": MessageLookupByLibrary.simpleMessage(
      "Answer the question to unlock her answers",
    ),
    "unlockHisAnswer": MessageLookupByLibrary.simpleMessage(
      "Answer the question to unlock his answers",
    ),
    "unlockMore": MessageLookupByLibrary.simpleMessage("Unlock more"),
    "unlockTheMic": MessageLookupByLibrary.simpleMessage("Unlock the Mic"),
    "unlocked": MessageLookupByLibrary.simpleMessage("Unlocked"),
    "unmute": MessageLookupByLibrary.simpleMessage("Unmute"),
    "uno": MessageLookupByLibrary.simpleMessage("Uno"),
    "unpin": MessageLookupByLibrary.simpleMessage("Unpin"),
    "unqualified": MessageLookupByLibrary.simpleMessage("Unqualified"),
    "unqualifiedUser": MessageLookupByLibrary.simpleMessage("Unqualified user"),
    "unsupportedVideo": MessageLookupByLibrary.simpleMessage(
      "Unsupported video, please upgrade the app version.",
    ),
    "upMicGuideContent": MessageLookupByLibrary.simpleMessage(
      "Join the Conversation!",
    ),
    "update": MessageLookupByLibrary.simpleMessage("Update"),
    "updateNow": MessageLookupByLibrary.simpleMessage("Update now"),
    "updateTime": m259,
    "updatingNow": MessageLookupByLibrary.simpleMessage("Update now"),
    "upgradeRole": MessageLookupByLibrary.simpleMessage("Upgrade Role"),
    "upgradeRoomLevelTip": m260,
    "upgradeYourWinkerVersion": MessageLookupByLibrary.simpleMessage(
      "Please upgrade your Winker version",
    ),
    "uploadBackground": MessageLookupByLibrary.simpleMessage(
      "Upload background",
    ),
    "uploadClearPhoto": MessageLookupByLibrary.simpleMessage(
      "Please upload clear upper body photos, otherwise conversion may fail.",
    ),
    "uploadFailed": MessageLookupByLibrary.simpleMessage(
      "Upload failed, please try again later",
    ),
    "uploadGifAvatar": MessageLookupByLibrary.simpleMessage(
      "Upload Gif Avatar",
    ),
    "uploadList": MessageLookupByLibrary.simpleMessage("Upload list"),
    "uploadMusic": MessageLookupByLibrary.simpleMessage("Upload Music"),
    "uploadMusicToYourPhone": MessageLookupByLibrary.simpleMessage(
      "No music available, please upload music to your phone first",
    ),
    "uploadPicAvatar": MessageLookupByLibrary.simpleMessage(
      "Upload pic Avatar",
    ),
    "uploadSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Upload successfully",
    ),
    "uploadTheme": MessageLookupByLibrary.simpleMessage("Upload theme"),
    "uploadTimeUsedUpDesc": MessageLookupByLibrary.simpleMessage(
      "Your upload times used up today, come back tomorrow!",
    ),
    "upperLimit": MessageLookupByLibrary.simpleMessage("Upper limit"),
    "use": MessageLookupByLibrary.simpleMessage("use"),
    "useDiamond": m261,
    "used": MessageLookupByLibrary.simpleMessage("Used"),
    "user": MessageLookupByLibrary.simpleMessage("User"),
    "userA": MessageLookupByLibrary.simpleMessage("User A"),
    "userAboutMe": MessageLookupByLibrary.simpleMessage("About Me"),
    "userAboutOther": m262,
    "userApplyTakeMic": MessageLookupByLibrary.simpleMessage("applies for mic"),
    "userB": MessageLookupByLibrary.simpleMessage("User B"),
    "userCar": MessageLookupByLibrary.simpleMessage("Ride"),
    "userCleanedChat": MessageLookupByLibrary.simpleMessage(
      " cleaned the chat",
    ),
    "userCloseVideoMsg": m263,
    "userContent": MessageLookupByLibrary.simpleMessage("User content"),
    "userCpSuccess": m264,
    "userEmptyPostTip": MessageLookupByLibrary.simpleMessage(
      "Posting to get to know you better",
    ),
    "userExitTheFamily": m265,
    "userGetDiamondsInPk": m266,
    "userGuide": MessageLookupByLibrary.simpleMessage("User guide"),
    "userHasItem": MessageLookupByLibrary.simpleMessage(
      "The user has purchased the item",
    ),
    "userHavingParty": m267,
    "userIntroEmptyTip": MessageLookupByLibrary.simpleMessage(
      "Write something to introduce yourself~",
    ),
    "userIsWinnerInPk": m268,
    "userJoinTheFamily": m269,
    "userJoinedTheGame": MessageLookupByLibrary.simpleMessage(
      "The user has joined the game",
    ),
    "userKickedOutOfFamilyBy": m270,
    "userLevel": MessageLookupByLibrary.simpleMessage("User Level"),
    "userMatchingYourResult": MessageLookupByLibrary.simpleMessage(
      "Users matching your result",
    ),
    "userMute": MessageLookupByLibrary.simpleMessage(
      "You are banned from speaking",
    ),
    "userNotInRoom": MessageLookupByLibrary.simpleMessage(
      "The user not in the room.",
    ),
    "userPausedVideoMsg": m271,
    "userPickUser": m272,
    "userPlayVideoMsg": m273,
    "userProfileCard": MessageLookupByLibrary.simpleMessage(
      "Personal information page in voice room",
    ),
    "userSetAsMaster": m274,
    "userSetAsVicePatriarch": m275,
    "userStartGiftPk": m276,
    "userStartVotePk": m277,
    "userTmpBanned": MessageLookupByLibrary.simpleMessage(
      "This user was banned because of violation community rule.",
    ),
    "userTookScreenshot": m278,
    "userTurnedResult": m279,
    "userUpdateStatus": m280,
    "usernameInvitedYouToJoinFamilynameDoYouAgreeTo": m281,
    "usernameRefuseYourFamilyApplication": m282,
    "usernameRejectedYourApplicationGoCheckOutOtherFamiliesGo": m283,
    "usernameWantsToJoinYourFamilyPleaseReviewIt": m284,
    "usesOfGolds": MessageLookupByLibrary.simpleMessage("Uses of Golds"),
    "using": MessageLookupByLibrary.simpleMessage("Using"),
    "usingLivingArea": MessageLookupByLibrary.simpleMessage(
      "Using living area",
    ),
    "validEmailTip": MessageLookupByLibrary.simpleMessage(
      "Please enter a valid email format.",
    ),
    "validUntilData": m285,
    "valueDiamond": m286,
    "verificationCodeError": MessageLookupByLibrary.simpleMessage(
      "Verification code error",
    ),
    "verificationInReview": MessageLookupByLibrary.simpleMessage(
      "Your verification is under review",
    ),
    "verificationPassed": MessageLookupByLibrary.simpleMessage(
      "Verification passed",
    ),
    "verificationProtectYou": MessageLookupByLibrary.simpleMessage(
      "Verification can protect you",
    ),
    "verificationToGet": MessageLookupByLibrary.simpleMessage(
      "Verification to get",
    ),
    "verifyFailed": MessageLookupByLibrary.simpleMessage("Verify failed"),
    "verifyProfile": MessageLookupByLibrary.simpleMessage("Verify profile"),
    "verifyProfileSecurely": MessageLookupByLibrary.simpleMessage(
      "Verify profile securely",
    ),
    "verifyToUnlock": MessageLookupByLibrary.simpleMessage("Verify to unlock"),
    "verifyVoiceBeforeListening": MessageLookupByLibrary.simpleMessage(
      "You haven\'t verified your voice yet. Please verify your voice before listening. ",
    ),
    "verifyVoiceBeforeMatching": MessageLookupByLibrary.simpleMessage(
      "You haven\'t verified your voice yet. Please go and verify your voice now to unlock our features!",
    ),
    "versionOutOfDate": MessageLookupByLibrary.simpleMessage(
      "Your current app version is out of date, please get the latest version to enjoy new features.",
    ),
    "vicePatriarch": MessageLookupByLibrary.simpleMessage("Vice-Family Leader"),
    "victory": MessageLookupByLibrary.simpleMessage("Victory"),
    "video": MessageLookupByLibrary.simpleMessage("Video"),
    "videoCall": MessageLookupByLibrary.simpleMessage("Video Call"),
    "videoCannotPlay": MessageLookupByLibrary.simpleMessage(
      "This video cannot play, please change to another one",
    ),
    "videoRoom": MessageLookupByLibrary.simpleMessage("Video room"),
    "videoTime": m287,
    "videoTryAgain": MessageLookupByLibrary.simpleMessage(
      "Error occurred, please try again.",
    ),
    "view": MessageLookupByLibrary.simpleMessage("View"),
    "viewAll": MessageLookupByLibrary.simpleMessage("View all"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("View details > "),
    "viewLuckyBagClaimDetails": MessageLookupByLibrary.simpleMessage(
      "View lucky bag claim details",
    ),
    "viewProfile": MessageLookupByLibrary.simpleMessage("View profile"),
    "viewRules": MessageLookupByLibrary.simpleMessage("View rules"),
    "viewingLimitedAssetsTip": MessageLookupByLibrary.simpleMessage(
      "Only view files and albums accessible to app.",
    ),
    "viewingProfile": MessageLookupByLibrary.simpleMessage("Viewing profile"),
    "violence": MessageLookupByLibrary.simpleMessage("violence"),
    "vip": MessageLookupByLibrary.simpleMessage("VIP"),
    "vipActivityPromotion": MessageLookupByLibrary.simpleMessage(
      "Room activity promotion",
    ),
    "vipActivityPromotionTips": m288,
    "vipAdSendGifts": MessageLookupByLibrary.simpleMessage(
      "Customer representatives send gifts",
    ),
    "vipAdSendGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Contact VIP customer service to send gifts in your room",
    ),
    "vipAddFriends": MessageLookupByLibrary.simpleMessage(
      "Add friends function",
    ),
    "vipAddFriendsTips": MessageLookupByLibrary.simpleMessage(
      "When someone initiates a conversation with you, they have to ask for your consent",
    ),
    "vipAvatarFrame": MessageLookupByLibrary.simpleMessage(
      "Exclusive VIP avatar frame",
    ),
    "vipAvatarFrameTips": MessageLookupByLibrary.simpleMessage(
      "Get more attention with your special VIP frame",
    ),
    "vipBadge": MessageLookupByLibrary.simpleMessage("VIP Badge"),
    "vipBadgeTips1": MessageLookupByLibrary.simpleMessage(
      "Chat messages in room",
    ),
    "vipBadgeTips2": MessageLookupByLibrary.simpleMessage(
      "Profile card in room",
    ),
    "vipBadgeTips3": MessageLookupByLibrary.simpleMessage("Online user list"),
    "vipBadgeTips4": MessageLookupByLibrary.simpleMessage("Profile"),
    "vipBadgeTips5": MessageLookupByLibrary.simpleMessage("Moment"),
    "vipCanChangeFriendsRequestSettings": MessageLookupByLibrary.simpleMessage(
      "VIP users can change friends request settings.",
    ),
    "vipCenter": MessageLookupByLibrary.simpleMessage("VIP Center"),
    "vipChatFrame": MessageLookupByLibrary.simpleMessage(
      "Exclusive VIP chat frame",
    ),
    "vipChatFrameTips": MessageLookupByLibrary.simpleMessage(
      "Your message with gorgeous frame show your friends",
    ),
    "vipColoredUsername": MessageLookupByLibrary.simpleMessage(
      "Colored username",
    ),
    "vipColoredUsernameTips": MessageLookupByLibrary.simpleMessage(
      "Attract everyone\'s attention with a colored name",
    ),
    "vipCustomerService": MessageLookupByLibrary.simpleMessage(
      "Customer service expert",
    ),
    "vipCustomerServiceTips": MessageLookupByLibrary.simpleMessage(
      "Enjoy the best service from our customer service expert",
    ),
    "vipCustomizeAvatar": MessageLookupByLibrary.simpleMessage(
      "Customize avatar",
    ),
    "vipCustomizeAvatarTips": MessageLookupByLibrary.simpleMessage(
      "You can upload your favorite photo and make it your avatar",
    ),
    "vipCustomizeRoomTheme": MessageLookupByLibrary.simpleMessage(
      "Customize room theme",
    ),
    "vipCustomizeRoomThemeTips": MessageLookupByLibrary.simpleMessage(
      "Set a custom room theme to show your style",
    ),
    "vipDoubleRewards": MessageLookupByLibrary.simpleMessage(
      "Double sign in and tasks rewards",
    ),
    "vipDoubleRewardsTips": MessageLookupByLibrary.simpleMessage(
      "Get double rewards for signing in and completing tasks",
    ),
    "vipEnterEffect": MessageLookupByLibrary.simpleMessage(
      "Exclusive Enter effect",
    ),
    "vipEnterEffectTips": MessageLookupByLibrary.simpleMessage(
      "Attract everyone\'s attention with a gorgeous enter effect",
    ),
    "vipExclusiveAvatarFrame": MessageLookupByLibrary.simpleMessage(
      "Exclusive customize avatar frame",
    ),
    "vipExclusiveAvatarFrameTips": MessageLookupByLibrary.simpleMessage(
      "Contact our VIP service. We will create your own avatar frame for you",
    ),
    "vipExpRequirement": MessageLookupByLibrary.simpleMessage(
      "VIP exp requirement",
    ),
    "vipExps": MessageLookupByLibrary.simpleMessage("3、VIP Exps"),
    "vipExpsDetail": MessageLookupByLibrary.simpleMessage(
      "You can earn VIP Exps by purchasing diamonds, 1 USD equals to 10 VIP Exps. There\'s no upper limit on how many VIP Exps you can earn every day. If you refund a purchase, all VIP Exps you earned for that purchase will be deducted.The image below shows the VIP Points needed from previous level.",
    ),
    "vipFreeze": MessageLookupByLibrary.simpleMessage("4、VIP Freeze"),
    "vipFreezeDetail": MessageLookupByLibrary.simpleMessage(
      "If you don\'t have enough VIP Exps to be deducted for a refund, your VIP membership will be frozen until you pay off the VIP Exps you owe. Please note that if your VIP membership is not unfrozen within 90 days of freeze, your VIP Points will not be reset to 0.",
    ),
    "vipImage": MessageLookupByLibrary.simpleMessage("VIP image"),
    "vipLevel": MessageLookupByLibrary.simpleMessage("1、VIP Level"),
    "vipLevelDetail": MessageLookupByLibrary.simpleMessage(
      "VIP membership is acquired by earning VIP Exps through your in-app purchases.",
    ),
    "vipMicProtection": MessageLookupByLibrary.simpleMessage("MIC Protection"),
    "vipMicProtectionTips": MessageLookupByLibrary.simpleMessage(
      "Protection from removal out of MIC",
    ),
    "vipMysteriousVistors": MessageLookupByLibrary.simpleMessage(
      "Mysterious vistors",
    ),
    "vipMysteriousVistorsTips": MessageLookupByLibrary.simpleMessage(
      "Visit someone\'s profile without letting them know who you are",
    ),
    "vipNamedGifts": MessageLookupByLibrary.simpleMessage("Named gifts"),
    "vipNamedGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Contact our VIP service. Your user name will show on special gifts",
    ),
    "vipNum": m289,
    "vipPersonalId": MessageLookupByLibrary.simpleMessage("Unique personal id"),
    "vipPersonalIdTips": MessageLookupByLibrary.simpleMessage(
      "Contact VIP customer service to get a unique personal ID for free",
    ),
    "vipProfileBackground": MessageLookupByLibrary.simpleMessage(
      "Customize profile background",
    ),
    "vipProfileBackgroundTips": MessageLookupByLibrary.simpleMessage(
      "You can upload your favorite photo and make it your profile background",
    ),
    "vipProfileCard": MessageLookupByLibrary.simpleMessage(
      "Exclusive Profile card",
    ),
    "vipProfileCardTips": MessageLookupByLibrary.simpleMessage(
      "Show your honor with a distinguished profile card",
    ),
    "vipRoomProtection": MessageLookupByLibrary.simpleMessage(
      "Room Protection",
    ),
    "vipRoomProtectionTips": MessageLookupByLibrary.simpleMessage(
      "Protection from removal out of room",
    ),
    "vipSendRoomImage": MessageLookupByLibrary.simpleMessage(
      "Send image in room",
    ),
    "vipSendRoomImageTips": MessageLookupByLibrary.simpleMessage(
      "You can send image in room",
    ),
    "vipSettled": MessageLookupByLibrary.simpleMessage("VIP Level Settled"),
    "vipStickersOnMIC": MessageLookupByLibrary.simpleMessage(
      "Exclusive stickers on MIC",
    ),
    "vipStickersOnMICTips": MessageLookupByLibrary.simpleMessage(
      "Use exclusive stickers when you\'re speaking on MIC.",
    ),
    "vipUnlimitedText": MessageLookupByLibrary.simpleMessage("Unlimited text"),
    "vipUnlimitedTextTips": MessageLookupByLibrary.simpleMessage(
      "You can send text whatever you want without restriction",
    ),
    "vipUserList": MessageLookupByLibrary.simpleMessage(
      "Front row on User list",
    ),
    "vipUserListTips": MessageLookupByLibrary.simpleMessage(
      "Enjoy a front row on the list of any rooms",
    ),
    "vipValidity": MessageLookupByLibrary.simpleMessage("2、VIP Validity"),
    "vipValidityDetail": MessageLookupByLibrary.simpleMessage(
      "The period of validity for each VIP level is 90 days. During the 90-day period, if your VIP Exps meet the requirement for a higher level, your VIP level will be immediately upgraded and your VIP Exps will be reset to 0. Upgrading by skipping a certain level is supported.\nIf your VIP level is not upgraded during the period, when the period is over, your VIP level will be reset according to the VIP Exps you earned within the period, and your VIP Exps will be reset to 0. For maintain your current VIP level, you need to accumulate the VIP Exps needed from the previous level during the period. Otherwise, your VIP level will be downgraded.\nIf you are not VIP yet, when your VIP Exps meet the requirements of a certain VIP level during a 90-day period, you will reach this VIP level and your VIP Exps will be reset to 0. If you fail to reach any VIP level during the 90-day period,your accumulated VIP Points will be reset to 0 when the period ends.",
    ),
    "vipVipGifts": MessageLookupByLibrary.simpleMessage("Exclusive VIP gifts"),
    "vipVipGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Only VIP user can send this gifts",
    ),
    "vip_setting": MessageLookupByLibrary.simpleMessage("VIP Setting"),
    "visibleToFriends": MessageLookupByLibrary.simpleMessage(
      "Visible to friends",
    ),
    "visibleToMyself": MessageLookupByLibrary.simpleMessage(
      "Visible to myself",
    ),
    "visibleToPublic": MessageLookupByLibrary.simpleMessage(
      "Visible to public",
    ),
    "visitSomeoneProfile": MessageLookupByLibrary.simpleMessage(
      "2.Visit someone’s profile without letting them know who you are",
    ),
    "visitors": MessageLookupByLibrary.simpleMessage("Visitors"),
    "voice": MessageLookupByLibrary.simpleMessage("Voice"),
    "voiceAdjust": MessageLookupByLibrary.simpleMessage("Voice adjust"),
    "voiceDatingNoPeople": MessageLookupByLibrary.simpleMessage(
      "Your perfect match is not here right now, please try again later.",
    ),
    "voiceDatingTimeUsedUp": MessageLookupByLibrary.simpleMessage(
      "No time left today, please try again tomorrow.",
    ),
    "voiceMatch": MessageLookupByLibrary.simpleMessage("Voice match"),
    "voiceParty": MessageLookupByLibrary.simpleMessage("Voice Party"),
    "voiceRecording": MessageLookupByLibrary.simpleMessage("Recording"),
    "voiceSeconds": m290,
    "voiceTest": MessageLookupByLibrary.simpleMessage("Voice test"),
    "voiceVerification": MessageLookupByLibrary.simpleMessage("Verification"),
    "voiceVerified": MessageLookupByLibrary.simpleMessage("Voice verified"),
    "voiceVerifyChat1": MessageLookupByLibrary.simpleMessage("Hi"),
    "voiceVerifyChat2": MessageLookupByLibrary.simpleMessage("How are you?"),
    "voiceVerifyChat3": MessageLookupByLibrary.simpleMessage(
      "Your voice sounds lovely!",
    ),
    "voiceVerifyDesc": MessageLookupByLibrary.simpleMessage(
      "Only user who have passed voice verification can listen to each other’s voice.",
    ),
    "voiceVerifyMatchCount": m291,
    "vote": MessageLookupByLibrary.simpleMessage("Vote"),
    "voteForUndercover": MessageLookupByLibrary.simpleMessage(
      "Vote for the user you believe most resembles an undercover.",
    ),
    "votePk": MessageLookupByLibrary.simpleMessage("Vote PK"),
    "voted": MessageLookupByLibrary.simpleMessage("Voted"),
    "voting": MessageLookupByLibrary.simpleMessage("Voting"),
    "vulgarOrOffensive": MessageLookupByLibrary.simpleMessage(
      "vulgar or offensive language",
    ),
    "waitBeforeDivorce": MessageLookupByLibrary.simpleMessage(
      "Wait 2 days before reinitiating the divorce",
    ),
    "waitForApproval": MessageLookupByLibrary.simpleMessage(
      "Please wait for approval.",
    ),
    "waitForReply": MessageLookupByLibrary.simpleMessage(
      "Wait for a reply before sending another message.",
    ),
    "waitSent": MessageLookupByLibrary.simpleMessage("Wait a while to send."),
    "waiting": MessageLookupByLibrary.simpleMessage("Waiting"),
    "waitingFemaleChoice": MessageLookupByLibrary.simpleMessage(
      "Wait for her choice",
    ),
    "waitingFirstStep": MessageLookupByLibrary.simpleMessage(
      "Waiting for your first step",
    ),
    "waitingForDrawing": MessageLookupByLibrary.simpleMessage(
      "Waiting for last draw to end",
    ),
    "waitingForOtherPlayersToSettleGame": MessageLookupByLibrary.simpleMessage(
      "Waiting for other players to settle game",
    ),
    "waitingForOtherSideAccept": MessageLookupByLibrary.simpleMessage(
      "Waiting for acceptance",
    ),
    "waitingForTheGameToStart": MessageLookupByLibrary.simpleMessage(
      "Waiting for the game to start",
    ),
    "waitingMaleChoice": MessageLookupByLibrary.simpleMessage(
      "Wait for his choice",
    ),
    "waitingPlayer": MessageLookupByLibrary.simpleMessage("Waiting player..."),
    "waitingReply": MessageLookupByLibrary.simpleMessage("Waiting for reply"),
    "waitingStart": MessageLookupByLibrary.simpleMessage("Waiting to start！"),
    "waitingToPlay": MessageLookupByLibrary.simpleMessage("Waiting to play"),
    "wallet": MessageLookupByLibrary.simpleMessage("Wallet"),
    "wantJoinFamily": MessageLookupByLibrary.simpleMessage(
      "want to join the family",
    ),
    "wayCharmLevelUp": MessageLookupByLibrary.simpleMessage(
      "Receive gifts in Winker",
    ),
    "wayToGetGolds": MessageLookupByLibrary.simpleMessage("Way to Get Golds"),
    "wayWealthLevelUp": MessageLookupByLibrary.simpleMessage(
      "Spend diamonds in Winker",
    ),
    "waysToLevelUp": MessageLookupByLibrary.simpleMessage("Ways to Level Up"),
    "weHaveTheInterests": MessageLookupByLibrary.simpleMessage(
      "We have the same interests!",
    ),
    "weakSignal": MessageLookupByLibrary.simpleMessage("Weak signal"),
    "wealth": MessageLookupByLibrary.simpleMessage("Contribution"),
    "wealthCharmLevelUpTitle": MessageLookupByLibrary.simpleMessage(
      "Congratulations on your upgrade!",
    ),
    "wealthLevel": MessageLookupByLibrary.simpleMessage("Wealth Level"),
    "wealthLevelUp": MessageLookupByLibrary.simpleMessage(
      "Congrats! Your wealth level up to",
    ),
    "wear": MessageLookupByLibrary.simpleMessage("Wear"),
    "wearANewRing": MessageLookupByLibrary.simpleMessage("Wear a new ring"),
    "wearBadge": MessageLookupByLibrary.simpleMessage("Wear Badge"),
    "wearBadgeRule": MessageLookupByLibrary.simpleMessage("Wear badge"),
    "wearBadgeRule1": MessageLookupByLibrary.simpleMessage(
      "Light up badge to wear it",
    ),
    "wearBadgeRule2": MessageLookupByLibrary.simpleMessage(
      "Every user can wear 3 badges at most",
    ),
    "wearBadgeRule3": MessageLookupByLibrary.simpleMessage(
      "Badges will be displayed on the personal information page, voice room, and chat page",
    ),
    "wearRingExplain": MessageLookupByLibrary.simpleMessage(
      "1、You can use any value rings.\n2、Wear a new ring do not need others approve.\n3、When you use a new ring, the original ring will not disappear, will be put into the ring box.\n4、It will not change your anniversary times.",
    ),
    "wearTitle": MessageLookupByLibrary.simpleMessage("Wear Title"),
    "wearing": MessageLookupByLibrary.simpleMessage("Wearing"),
    "weekly": MessageLookupByLibrary.simpleMessage("Weekly"),
    "weeklyContribution": MessageLookupByLibrary.simpleMessage(
      "Weekly Contribution",
    ),
    "weeklyList": MessageLookupByLibrary.simpleMessage("Weekly list"),
    "weeklyRank": MessageLookupByLibrary.simpleMessage("Weekly rank"),
    "welcomeJoinRoom": MessageLookupByLibrary.simpleMessage(
      " , welcome to the room! Don\'t hesitate to grab the mic and follow us!",
    ),
    "welcomeToAlo": MessageLookupByLibrary.simpleMessage("Welcome to Winker"),
    "welcomeToCompleteSpace": MessageLookupByLibrary.simpleMessage(
      "Welcome to complete anonymous space.",
    ),
    "welcomeToTheFamily": m292,
    "welcomeToVoiceParty": MessageLookupByLibrary.simpleMessage(
      "Welcome to Voice party",
    ),
    "welcomeToWinker": MessageLookupByLibrary.simpleMessage(
      "Welcome to Winker",
    ),
    "whatsApp": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "whenTheFamilyExperienceValueReachesTheUpperLimit":
        MessageLookupByLibrary.simpleMessage(
          "1. When the family experience value reaches the upper limit, the personal experience value also reaches the upper limit, and no more experience will be gained on that day.",
        ),
    "whenTheFamilyTreasuryReachesTodaysLimitOrThe":
        MessageLookupByLibrary.simpleMessage(
          "2. When the family treasury reaches today\'s limit or the total limit, no more family treasury will be added today.",
        ),
    "whetherSpendGoldsToFish": m293,
    "whichDay": m294,
    "whoBirthday": m295,
    "whoCanChatMe": MessageLookupByLibrary.simpleMessage("Who can chat to me?"),
    "whoEnterTheRoom": MessageLookupByLibrary.simpleMessage("enter the room"),
    "whoHadWink": MessageLookupByLibrary.simpleMessage("Who i had wink"),
    "whoIsTheUndercover": MessageLookupByLibrary.simpleMessage(
      "Who is the undercover?",
    ),
    "whoWinkAtYou": MessageLookupByLibrary.simpleMessage("Who wink at you"),
    "whos": m296,
    "willLoseMatch": MessageLookupByLibrary.simpleMessage(
      "You will lose this match if you leave now.",
    ),
    "win": MessageLookupByLibrary.simpleMessage("Win"),
    "wink": MessageLookupByLibrary.simpleMessage("Wink"),
    "winkCountOutToast": MessageLookupByLibrary.simpleMessage(
      "The wink count has run out.",
    ),
    "winkList": MessageLookupByLibrary.simpleMessage("New Friends"),
    "winkerPremium": MessageLookupByLibrary.simpleMessage("Winker Premium"),
    "winningFruit": m297,
    "wishYouLuck": MessageLookupByLibrary.simpleMessage("Wish you luck!"),
    "withdrawAvailable": MessageLookupByLibrary.simpleMessage(
      "Withdraw available",
    ),
    "withdrawRequest": MessageLookupByLibrary.simpleMessage(
      "The other side withdrew the request.",
    ),
    "wonTheAward": m298,
    "wordCountExceeded": MessageLookupByLibrary.simpleMessage(
      "Word count exceeded",
    ),
    "wordCountInsufficient": MessageLookupByLibrary.simpleMessage(
      "Word count insufficient",
    ),
    "work": MessageLookupByLibrary.simpleMessage("Work"),
    "workTip": MessageLookupByLibrary.simpleMessage(
      "I started my own business and opened a Honey Snow ice City milk tea shop.",
    ),
    "world": MessageLookupByLibrary.simpleMessage("World"),
    "worldwide": MessageLookupByLibrary.simpleMessage("Worldwide"),
    "wouldYouLikeToShareVoice": MessageLookupByLibrary.simpleMessage(
      "Would you like to share to moments to make others hear your voice?",
    ),
    "writeABio": MessageLookupByLibrary.simpleMessage(
      "Write a bio to introduce yourself",
    ),
    "wrong": MessageLookupByLibrary.simpleMessage("Wrong"),
    "xGolds": m299,
    "year": MessageLookupByLibrary.simpleMessage("Year"),
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
    "you": MessageLookupByLibrary.simpleMessage("You"),
    "youAlreadyGotConversations": m300,
    "youAlreadySentAMagicKey": MessageLookupByLibrary.simpleMessage(
      "You already sent a magic box key to",
    ),
    "youAreGuestNow": MessageLookupByLibrary.simpleMessage(
      "Denied. you are on the guest mic now",
    ),
    "youAreNotAMemberOfThisFamilyAndCannot": MessageLookupByLibrary.simpleMessage(
      "You are not a member of this family and cannot participate in grabbing the lucky bag",
    ),
    "youAreNotFamilyMember": MessageLookupByLibrary.simpleMessage(
      "You are not a member of this family.",
    ),
    "youAreSetAdmin": MessageLookupByLibrary.simpleMessage(
      "You are set as an administrator",
    ),
    "youAreSuperAdmin": MessageLookupByLibrary.simpleMessage(
      "You are in super admin mode and cannot join this function.",
    ),
    "youBastExperience": MessageLookupByLibrary.simpleMessage(
      "Your Place, Your Mates, Your Best Experience.",
    ),
    "youCanAccessEachOtherProfiles": MessageLookupByLibrary.simpleMessage(
      "In call with hidden identity. You can access each other\'s profiles by showing your identities.",
    ),
    "youCanCreateEventTips": MessageLookupByLibrary.simpleMessage(
      "You can create event for your manage room.",
    ),
    "youCanInitiateConversation": MessageLookupByLibrary.simpleMessage(
      "4.Once you agree, you can initiate a conversation",
    ),
    "youCanManagerWhoCanTalkYou": MessageLookupByLibrary.simpleMessage(
      "3.You can manage who can talk to you",
    ),
    "youCanNotFollowThisUserIntoRoom": MessageLookupByLibrary.simpleMessage(
      "You can not follow this user into room",
    ),
    "youCanOnlyOpenOnePlay": MessageLookupByLibrary.simpleMessage(
      "You can only open one play at a time",
    ),
    "youCanThrowIntoSea": MessageLookupByLibrary.simpleMessage(
      "No one will know you, just bottle up what you want to say and write and throw it into the sea.",
    ),
    "youCanUnlockTips": MessageLookupByLibrary.simpleMessage(
      "You can unlock restriction by <h>",
    ),
    "youCannotChangeSeat": MessageLookupByLibrary.simpleMessage(
      "You cannot change seat",
    ),
    "youCantChangeInThisRound": MessageLookupByLibrary.simpleMessage(
      "You can’t change participant in this round",
    ),
    "youCantSendBottle": MessageLookupByLibrary.simpleMessage(
      "You ran out of your times today, please come back tomorrow!",
    ),
    "youCantTakeMicOnOppositeRoom": MessageLookupByLibrary.simpleMessage(
      "You can\'t take mic on the opposite room",
    ),
    "youDoNotBelongTheFamily": MessageLookupByLibrary.simpleMessage(
      "You do not belong to that family",
    ),
    "youDoNotHaveCouple": MessageLookupByLibrary.simpleMessage(
      "You do not have couple",
    ),
    "youDontHaveTheAccessToUploadGifPicture":
        MessageLookupByLibrary.simpleMessage(
          "You don\'t have the access to upload gif picture",
        ),
    "youGet": MessageLookupByLibrary.simpleMessage("You get"),
    "youGetAGift": MessageLookupByLibrary.simpleMessage("You received a gift."),
    "youGetMatchEverySeconds": MessageLookupByLibrary.simpleMessage(
      "You get a match every three seconds…",
    ),
    "youGetTwoGifts": MessageLookupByLibrary.simpleMessage(
      "You received two gifts.",
    ),
    "youGot": MessageLookupByLibrary.simpleMessage("You got"),
    "youGotFriendsInHala": m301,
    "youGotFrom": m302,
    "youGotNumGifts": m303,
    "youHadGot": m304,
    "youHadRevokedAdmin": MessageLookupByLibrary.simpleMessage(
      "You had revoked admin",
    ),
    "youHasItem": MessageLookupByLibrary.simpleMessage(
      "You have purchased the item",
    ),
    "youHaveBeenRemovedFromTheFamilynameGoCheckOut": m305,
    "youHaveDownVip": m306,
    "youHaveFollowersAndFriends": m307,
    "youHaveGot": MessageLookupByLibrary.simpleMessage("You have got"),
    "youHaveInterests": MessageLookupByLibrary.simpleMessage(
      "You have three interests in common!",
    ),
    "youHaveKickedOutMic": MessageLookupByLibrary.simpleMessage(
      "You will leave the mic now. Come later!",
    ),
    "youHaveKickedOutRoom": MessageLookupByLibrary.simpleMessage(
      "You have been kicked out of the room",
    ),
    "youHaveNoAuditRights": MessageLookupByLibrary.simpleMessage(
      "You have no audit rights.",
    ),
    "youHaveNoJoinTheFamily": MessageLookupByLibrary.simpleMessage(
      "You have no join the family",
    ),
    "youHaveNotGot": MessageLookupByLibrary.simpleMessage("You haven\'t got"),
    "youHaveReachedTheDeviceClaimLimitAndCannotClaim":
        MessageLookupByLibrary.simpleMessage(
          "You have reached the device claim limit and cannot claim the lucky bag",
        ),
    "youHaveReachedVip": m308,
    "youHaveRewards": MessageLookupByLibrary.simpleMessage(
      "Tap to get rewards",
    ),
    "youHaveSentRequest": MessageLookupByLibrary.simpleMessage(
      "You have sent a request to this user",
    ),
    "youInvitedGuest": MessageLookupByLibrary.simpleMessage(
      "You are invited to take the guest mic",
    ),
    "youInvitedHost": MessageLookupByLibrary.simpleMessage(
      "You are invited to take the host mic",
    ),
    "youKickedOut": MessageLookupByLibrary.simpleMessage(
      "You have been kicked out the list",
    ),
    "youLackOfKeys": MessageLookupByLibrary.simpleMessage(
      "You lack of keys! Please request keys from other family members.",
    ),
    "youMatchedWith": MessageLookupByLibrary.simpleMessage(
      "You have been matched with ",
    ),
    "youMayInterest": MessageLookupByLibrary.simpleMessage("You may interest"),
    "youNotWinkerPremium": MessageLookupByLibrary.simpleMessage(
      "You are not a Winker Premium",
    ),
    "youRLuckyBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      ", Your lucky bag is empty!",
    ),
    "youReceivedANewMsg": MessageLookupByLibrary.simpleMessage(
      "You received a new message",
    ),
    "youRejectDivorce": m309,
    "youRoomDefenseBuff": m310,
    "youSendGift": m311,
    "youSentTheRequest": MessageLookupByLibrary.simpleMessage(
      "You‘ve sent the request, please wait for approval.",
    ),
    "youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": m312,
    "youTookScreenshot": MessageLookupByLibrary.simpleMessage(
      "You took a screenshot of chat",
    ),
    "youWillHaveANewId": MessageLookupByLibrary.simpleMessage(
      "You will have a new identity that include avatar and nickname.",
    ),
    "youWillLosePkOnceLeaveRoom": MessageLookupByLibrary.simpleMessage(
      "You will lose the PK once you leave the room",
    ),
    "youWinkedYou": m313,
    "yourAccountWasDeleted": MessageLookupByLibrary.simpleMessage(
      "Your Winker account was deleted.",
    ),
    "yourBalance": MessageLookupByLibrary.simpleMessage("Your balance :"),
    "yourCity": MessageLookupByLibrary.simpleMessage("Your city"),
    "yourContributionWillBeClearedIfExitFamily":
        MessageLookupByLibrary.simpleMessage(
          "After you quit the family, you will clear all the related assets in the family. Are you sure you want to quit the family?",
        ),
    "yourEventInfoNotFill": m314,
    "yourFamilyApplicationAlreadySent": MessageLookupByLibrary.simpleMessage(
      "Your application to join has been submitted. Please wait patiently for the family leader\'s response. ",
    ),
    "yourFamilyHasBeenDisbanded": MessageLookupByLibrary.simpleMessage(
      "Your family has been disbanded!",
    ),
    "yourFamilyHasBeenDisbandedGoCheckOutOtherFamilies":
        MessageLookupByLibrary.simpleMessage(
          "Your family has been disbanded! Go check out other families. Go to Family Square!",
        ),
    "yourGoldReturnedBalance": MessageLookupByLibrary.simpleMessage(
      "Fish failed, your golds have been returned to your balance.",
    ),
    "yourKeyMustBeSentFromAnother": MessageLookupByLibrary.simpleMessage(
      "Your open key must be sent from another user.",
    ),
    "yourLevelIsIncreasingAtPremiumSpeed": MessageLookupByLibrary.simpleMessage(
      "Your level is increasing at Premium speed.",
    ),
    "yourName": MessageLookupByLibrary.simpleMessage("Your Name"),
    "yourPhoneStorageInsufficient": MessageLookupByLibrary.simpleMessage(
      "The storage space of your mobile phone is insufficient.",
    ),
    "yourQuestion": MessageLookupByLibrary.simpleMessage("Your question:"),
    "yourRoomGrabPoints": m315,
    "yourRoomIsGrabbedPoints": m316,
    "yourRoomPointsChange": m317,
    "yourVoiceLow": MessageLookupByLibrary.simpleMessage(
      "Your voice is too low, please speak loudly.",
    ),
    "yourVoiceVerificationFailed": MessageLookupByLibrary.simpleMessage(
      "Your voice verification failed, please try to record again. ",
    ),
    "yseWithExclamation": MessageLookupByLibrary.simpleMessage("Yes!"),
  };
}
