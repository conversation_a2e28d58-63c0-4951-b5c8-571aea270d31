// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  static String m0(room) =>
      "Lucky bag family dibagikan pada ${room}, silakan masuk ke ruang family untuk mengambilnya!";

  static String m1(other) => "Tentang informasi ${other}";

  static String m2(diamond, name) =>
      "Apakah Anda yakin ingin membayar ${diamond} berlian untuk mengaktifkan premium? ${name}?";

  static String m3(name, time) =>
      "Anda telah berhasil mengaktifkan ${name} premium, berlaku hingga ${time}";

  static String m4(name, id) => "· Agensi: ${name} (ID:${id})";

  static String m5(count) => "buff pertahanan semua peserta ${count}";

  static String m6(points) => "semua peserta ${points} poin";

  static String m7(nick) => "${nick} sudah menjawab Game pertanyaan.";

  static String m8(days) =>
      "Yakin ingin membubarkan family? Setelah pembubaran, semua aset pribadi dan aset family akan dihapus. Anda memiliki ${days} hari masa tenang untuk membatalkan sebelum family benar-benar dibubarkan. Setelah masa tenang selama ${days} hari berakhir, family akan dibubarkan.";

  static String m9(num1, num2, where1, where2) =>
      "Yakin ingin membagikan lucky bag senilai total ${num1} berlian pada ${where2} ${where1}, dengan ${num2} orang yang dapat mengklaimnya?";

  static String m10(num) => "Setidaknya ${num} berlian";

  static String m11(name) => "Nama";

  static String m12(num) => "Mulai otomatis:${num}detik";

  static String m13(time) => "Diberikan pada ${time}";

  static String m14(ranking) => "Peringkat:${ranking}";

  static String m15(username) =>
      "Ikuti ${username} untuk membuka lebih banyak Fitur!";

  static String m16(num) =>
      "${Intl.plural(num, zero: '${num} hari', one: '${num} hari', other: '${num} hari')} lalu";

  static String m17(num) =>
      "${Intl.plural(num, zero: '${num} jam', one: '${num} jam', other: '${num} jam')} lalu";

  static String m18(count) => "${count} menit lalu";

  static String m19(num) =>
      "${Intl.plural(num, zero: '${num} bulan', one: '${num} bulan', other: '${num} bulan')} lalu";

  static String m20(num) =>
      "${Intl.plural(num, zero: '${num} minggu', one: '${num} minggu', other: '${num} minggu')} lalu";

  static String m21(num) =>
      "${Intl.plural(num, zero: '${num} tahun', one: '${num} tahun', other: '${num} tahun')} lalu";

  static String m22(time) => "Waktu blokir${time}";

  static String m23(star, type) =>
      "Apakah Anda yakin ingin menghabiskan ${star} koin untuk beli ${type}?";

  static String m24(price) =>
      "Kirim hadiah diperlukan kotak hadiah, apakah Anda ingin menghabiskan ${price} star untuk beli kotak hadiah?";

  static String m25(amount) => "${amount} beli";

  static String m26(time) => "Waktu panggilan: ${time}";

  static String m27(name) => "${name} mengubah mode obrolan";

  static String m28(count) => "Ubah jumlah mikrofon menjadi ${count} kursi.";

  static String m29(name) =>
      "Selamat telah membuka kunci ${name} fitur! Alami sekarang dan bawa diri Anda lebih dekat dengan orang lain!";

  static String m30(num) => "Masih ${num} Buka kunci keintiman";

  static String m31(num) => "Chatting bisa mendapatkan keintiman (+${num} ）";

  static String m32(name) => "${name} klik [BOOM]!!";

  static String m33(user) => "Bisu global tidak tidiaktifkan ${user}.";

  static String m34(count) => "Tutup otomatis setelah ${count} detik";

  static String m35(day, coins) =>
      "${coins} / ${Intl.plural(day, zero: '${day}hari', one: '${day}hari', two: '${day}hari', few: '${day}hari', other: '${day}hari')}";

  static String m36(percent) => "${percent}% selesai";

  static String m37(percent) => "Profil selesai ${percent}%";

  static String m38(count) =>
      "Habiskan ${count} berlian untuk memaksa perceraian. Pernikahan Anda akan berakhir dan cincinnya akan hilang";

  static String m39(badge) => "Selamat! Anda telah menyalakan ${badge} medali";

  static String m40(familyName, position) =>
      "Selamat! Anda telah diangkat sebagai ${position} di family ${familyName}!";

  static String m41(familyName) =>
      "Selamat! Anda telah bergabung dengan family ${familyName}. Yuk! Cek sekarang!";

  static String m42(familyName) =>
      "Selamat! Family ${familyName} telah berhasil dibuat. Sekarang Anda bisa merekrut lebih banyak anggota. Cek sekarang!";

  static String m43(familyName, level) =>
      "Selamat! Family Anda ${familyName} telah naik ke level ${level}! Yuk. Cek sekarang!";

  static String m44(userName, giftName) =>
      "Selamat untuk ${userName} atas ${giftName}";

  static String m45(n, sex) =>
      "Apakah kamu ingin menghabiskan ${n} diamond untuk menaikkan hadiah ini ke ${sex}?";

  static String m46(uid) => "Hubungi CS ${uid}";

  static String m47(content) => "${content} mati";

  static String m48(num) => "Starlight Konvertibel:${num}";

  static String m49(days) => "${days} hari masa tenang";

  static String m50(code) =>
      "Kode Undangan: ${code}. Saya menggunakan aplikasi sosial baru bernama \'Winker\' dan mengundang anda untuk bergabung dengan kami. Kode undangan saya hanya dapat digunakan satu kali dan jumlahnya terbatas. Ayo gabung secepatnya~Untuk mendownload, silakan klik👉🏻 https://winkerchat.com/";

  static String m51(count, currency) => "${count} ${currency}";

  static String m52(name) => "Host ${name} memulai babak baru.";

  static String m53(name) =>
      "${name} terbuka, datang dan bergabunglah dengan posisi mikrofon untuk berpartisipasi dalam Pemilihan pasangan.";

  static String m54(num) => "Buat(${num} Berlian)";

  static String m55(amount) => "Buat dengan bayar ${amount}";

  static String m56(num) =>
      "${Intl.plural(num, zero: '${num} hari', one: '${num} hari', two: '${num} hari', few: '${num} hari', other: '${num} hari')}";

  static String m57(content) => "Penurunan Konten";

  static String m58(host) => "Ruangan ${host}";

  static String m59(count) => "buff pertahanan${count}";

  static String m60(num) => "Hapus (${num})";

  static String m61(num) => "${num} diamond berhasil diterima";

  static String m62(num) => "${num} berlian untuk perpanjangan";

  static String m63(discount) => "${discount}% diskon";

  static String m64(num) => "Jarak ${num}km";

  static String m65(user) =>
      "Apakah Anda ingin mentransfer ${user} sebagai kapten permainan?";

  static String m66(num) => "Selesai(${num})";

  static String m67(num) => "Mengundi ${num} sekali";

  static String m68(time) => "Durasi: ${time}";

  static String m69(num) => "${num} telah dimasukkan";

  static String m70(code) =>
      "Terjadi kesalahan, harap muat ulang.(kode: ${code})";

  static String m71(num) => "Subscriber ${num}";

  static String m72(num) => "${num} Subscriber";

  static String m73(id, type) => "Room ID:${id} (${type})";

  static String m74(name) => "Tema:${name}";

  static String m75(n) => "Berhasil menukar ${n} Berlian.";

  static String m76(num) => "Exp";

  static String m77(num) => "Nomor EXP";

  static String m78(time) => "· Akan berakhir pada ${time}";

  static String m79(date) => "Tanggal kedaluwarsa: ${date}";

  static String m80(content) => "Ekstra ${content}";

  static String m81(day) =>
      "Cover family hanya diubah seminggu sekali, silakan dicoba lagi setelah ${day} hari.";

  static String m82(name, position) => "${name} ${position}";

  static String m83(num) =>
      "Jika jumlah anggota family kurang dari ${num} orang, maka lucky bag tidak dapat dibagikan.";

  static String m84(from, to) =>
      "Nama family harus antara ${from} - ${to}karakter.";

  static String m85(name) =>
      "Datang dan bergabunglah dengan family Winker ${name}! Mari mengobrol dan bersenang-senang!";

  static String m86(from, to) => "Silakan unggah cover family";

  static String m87(num) =>
      "Selamat! Family Anda levelnya telah naik ke Lv. ${num}";

  static String m88(num) => "Level fansmu telah ditingkatkan ke ${num} !";

  static String m89(username, num) =>
      "Selamat! ${username} Level klub fans telah dinaikkan menjadi lv.${num}!";

  static String m90(sum) => "Total: ${sum} orang";

  static String m91(num) => "${num} poin untuk naik level.";

  static String m92(username) =>
      "Ikuti ${username} untuk membuka lebih banyak Fitur!";

  static String m93(num) => "Pengikut: ${num}";

  static String m94(num) => "${num} kesempatan gratis";

  static String m95(code) => "Kesalahan game, kode salah:${code}";

  static String m96(name) => "Game telah berakhir! Pemenangnya adalah ${name}";

  static String m97(gem, diamond) => "Rubi = berlian.";

  static String m98(num, type) => "Dapatkan ${num} ${type} per hari";

  static String m99(amount) => "Jumlah hadiah: ${amount}";

  static String m100(percent) => "Persen Diskon Hadiah";

  static String m101(a, b, c) => "${a} memberikan ${b} sebuah ${c}";

  static String m102(a, b) => "${a} won ${b} dari klub kamar ~";

  static String m103(count) => "Maksimum${count}";

  static String m104(name, award, count) =>
      "${name} mendapatkan ${award} ${count} dari Kotak Harta Karun";

  static String m105(points) => "raih ${points} poin";

  static String m106(count) =>
      "Pesona atau Kontribusi harus lebih besar dari ${count}";

  static String m107(num) =>
      "Anda telah mengundi untuk ${Intl.plural(num, zero: '${num} day', one: '${num} day', two: '${num} days', few: '${num} days', other: '${num} days')} terus-menerus.";

  static String m108(num) =>
      "Anda telah bersama ${Intl.plural(num, zero: '${num} day', one: '${num} day', two: '${num} days', few: '${num} days', other: '${num} days')}, harap hargai hubungan Anda..";

  static String m109(sex) => "${sex} wink ke kamu";

  static String m110(words) => "Petunjuk: ${words}";

  static String m111(time) => "Diperbarui setelah ${time}";

  static String m112(time) =>
      "${Intl.plural(time, zero: '${time} hour', one: '${time} hour', two: '${time} hours', few: '${time} hours', other: '${time} hours')} sebelumnya";

  static String m113(hour) => "${hour} jam";

  static String m114(num) =>
      "${Intl.plural(num, zero: '${num} jam', one: '${num} jam', two: '${num} jam', few: '${num} jam', other: '${num} jam')}";

  static String m115(he, him) =>
      "${he} adalah pendatang baru dan belum memiliki banyak teman. Ayo sapa ${him}.";

  static String m116(size) =>
      "Foto tidak bisa lebih dari ukuran yang sudah ditentukan";

  static String m117(name) => "Di ruang obrolan ${name}";

  static String m118(game) => "Main ${game}";

  static String m119(content) => "Kenaikan Konten";

  static String m120(remaining) =>
      "(Masih membutuhkan ${remaining} diamond untuk dibeli)";

  static String m121(score) => "Keintiman: ${score}";

  static String m122(username) =>
      "${username} mengundang Anda untuk bergabung ke dalam daftar pemain";

  static String m123(userName, eventName) =>
      "Saya mengundang anda untuk bergabung dalam pesta ${eventName} ${userName}, datang dan bergabunglah dengan Winker untuk bermain bersama.";

  static String m124(username) =>
      "${username} mengundang Anda untuk bergabung dalam permainan dan bermain.";

  static String m125(name) => "Mengundang anda bergabung di ${name}";

  static String m126(who) => "${who} mengundang anda untuk mengambil mic";

  static String m127(num) => "Gabung dengan winker:${num} hari";

  static String m128(key, diamond) =>
      "${key} kunci dan ${diamond} berlian dapat membuka kotak ini";

  static String m129(num) => "Kunci dapat dihadiahkan: ${num}";

  static String m130(num) => "Kunci yang diberikan oleh orang lain: ${num}";

  static String m131(key) => "${key} kunci dapat membuka kotak ini";

  static String m132(time) => "Waktu pembaruan terakhir: ${time}";

  static String m133(level) => "Buka kunci level ${level}";

  static String m134(num) => "Naikkan ${num} Hadiah";

  static String m135(name, roomId) =>
      "Kami memiliki percakapan seru di sini! Masuk ke Winker untuk bergabung 「${name}」${roomId}!";

  static String m136(name, roomId, pwd) =>
      "Kami memiliki percakapan seru di sini! Masuk ke Winker untuk bergabung 「${name}」${roomId}! Kata sandi ruangan adalah ${pwd}";

  static String m137(game) => "${game} gagal dimuat, silakan coba lagi";

  static String m138(num) => "Sedang memuat:${num}%";

  static String m139(num, time) =>
      "${num} lucky bag diambil dalam ${time} menit";

  static String m140(name, count) =>
      "${name} adalah pemenang yang beruntung dalam event Roda Keberuntungan dan memenangkan ${count} diamond!";

  static String m141(num) => "Lv";

  static String m142(num) => "Membuka Lv.${num}";

  static String m143(nick) => "${nick} mencocokkan anda dari fungsi tes suara.";

  static String m144(num) => "Kamu dapat menambahkan maksimal ${num} tag";

  static String m145(name) => "Apakah Anda ingin menggunakan ${name} ？";

  static String m146(num) => "Harap tambahkan minimal ${num} tag";

  static String m147(time) =>
      "${Intl.plural(time, zero: '${time} min', one: '${time} min', two: '${time} mins', few: '${time} mins', other: '${time} mins')} sebelumnya";

  static String m148(time) =>
      "${Intl.plural(time, zero: '${time} menit', one: '${time} menit', two: '${time} menit', few: '${time} menit', other: '${time} menit')}";

  static String m149(count) =>
      "Diperlukan minimal ${count} peserta untuk memulai Roda Keberuntungan. Setelah dimulai, orang lain tidak dapat bergabung.";

  static String m150(sum) => "Kekurangan posisi terakhir: ${sum}";

  static String m151(count) => "${count} Postingan";

  static String m152(word) => "Kata-kata saya: ${word}";

  static String m153(name) =>
      "${name} berisi konten ilegal, harap di-edit kembali";

  static String m154(name) => "${name}... masuki ruangan ini";

  static String m155(amount) => "${amount} pesan baru";

  static String m156(date) => "Tanggal perubahan berikutnya:${date}";

  static String m157(position) => "No.${position} KELUAR!";

  static String m158(count) => "${count} hari";

  static String m159(num) =>
      "${Intl.plural(num, zero: '${num} berlian', one: '${num} berlian', two: '${num} berlian', few: '${num} berlian', other: '${num} berlian')}";

  static String m160(num) =>
      "${Intl.plural(num, zero: '${num} koin', one: '${num} koin', two: '${num} koin', few: '${num} koin', other: '${num} koin')}";

  static String m161(num, count) =>
      "Habiskan ${Intl.plural(num, zero: '${num} koin', one: '${num} koin', two: '${num} koin', few: '${num} koin', other: '${num} koin')} untuk meningkatkan ${Intl.plural(count, zero: '${count} time', one: '${count} time', two: '${count} times', few: '${count} times', other: '${count} times')}.";

  static String m162(num) => "Ronde: ${num}";

  static String m163(stars) => "${stars}bintang";

  static String m164(num) => "${num} pendukung";

  static String m165(num) => "${num} kali";

  static String m166(count) => "Pengguna Online : ${count}";

  static String m167(name) => "Hanya hadiah ${name} yang dihitung";

  static String m168(user) => "Bisu global tidiaktifkan ${user}.";

  static String m169(receiveNum, total) => "Buka ${receiveNum}/${total}";

  static String m170(num) => "Opsi (${num}/8)";

  static String m171(num) => "${num} pemirsa lainnya";

  static String m172(time) => "Durasi pesta: ${time}";

  static String m173(pw) => "Kata Sandi";

  static String m174(name) =>
      "Pertunjukan ${name} berakhir lebih awal. Ini saatnya para juri berbicara";

  static String m175(nick) => "${nick} ingin bermain pertanyaan dengan anda.";

  static String m176(pos) => "Pemain ${pos}";

  static String m177(game) => "Main ${game}";

  static String m178(from, to) => "Silakan masukkan ${from}-${to} karakter";

  static String m179(points) => "${points} poin";

  static String m180(num, total) => "Hak istimewa";

  static String m181(num) =>
      "Anda akan mendapatkannya dalam ${num} undian lagi";

  static String m182(she) => "${she} sedang mengobrol di ruangan~";

  static String m183(name) =>
      "Lamaran telah dikirimkan ke kekasih Anda. Harap bersabar sementara ${name} mempertimbangkan permintaan Anda. Doakan yang terbaik untuk Anda.";

  static String m184(name) => "Saya membeli ${name}，melamar Anda";

  static String m185(her) =>
      "Selamat! Kamu menang! Kamu dapat mengajukan pertanyaan kepada ${her}";

  static String m186(user, rankName, order) =>
      "${user} menjadi ${rankName} top ${order}";

  static String m187(num) => "Exp pesona penerima +${num}";

  static String m188(num) => "Penerima mendapat ${num} rubi";

  static String m189(second) => "Rekam setidaknya ${second} detik";

  static String m190(count) =>
      "Anda bisa lolos dengan menjawab ${count} soal dengan benar.";

  static String m191(count) =>
      "😃Jawaban benar, cukup jawab ${count} pertanyaan lagi dengan benar dan kamu akan lolos.";

  static String m192(count) =>
      "🤖Wow, ada ${count} teman Winker yang memiliki hari ulang tahun yang sama denganmu, mungkin kamu akan menemukan sesuatu yang seru dari mereka.";

  static String m193(gender, name) =>
      "${gender}${name}, harap isi tanggal lahir anda agar kami dapat merekomendasikan konten dan teman yang sesuai untuk anda.";

  static String m194(name) => "${name}，silakan pilih jenis kelamin anda";

  static String m195(sex) =>
      "Jenis kelamin tidak dapat diubah setelah dipilih. Harap cek kembali bahwa pilihan anda adalah ${sex}";

  static String m196(name) => "${name}，Saya suka nama ini😊.";

  static String m197(uid) =>
      "🎉Selamat telah mendapatkan Akses Winker「${uid} 」. Sekarang kamu bisa masuk ke komunitas Winker dan temukan teman barumu di sini 👇🏻";

  static String m198(time) => "Sisa\n${time}";

  static String m199(name) => "Keluarkan ${name} dari family?";

  static String m200(words) => "Hasil: ${words}";

  static String m201(num) => "Sukses Mengirim Koin";

  static String m202(num) => "${num} admin";

  static String m203(count) => "Hitung/1000";

  static String m204(min, max) => "${min}~${max} huruf/angka";

  static String m205(sum) =>
      "Nama klub ruangan diizinkan untuk diubah setiap ${sum} Hari sekali";

  static String m206(id) => "ID: ${id}";

  static String m207(nick) => "Ruangan ${nick} yang anda ikuti dibuka!";

  static String m208(num) => "Selamat! Ruangan di-upgrade ke LV.${num}";

  static String m209(roomName) => "Nama ruangan diubah menjadi:${roomName}";

  static String m210(code) =>
      "[Kode Kesalahan：${code}]Silakan coba keluar dari ruangan dan masuk kembali.";

  static String m211(time) =>
      "Karena Anda tidak aktif di ruang dalam waktu yang lama, ruang Anda akan otomatis ditutup dalam ${time} detik.";

  static String m212(room, user) => "Ruang Pengguna";

  static String m213(round) => "Pemungutan Suara ${round} Ronde";

  static String m214(sex) => "Wink ke ${sex}";

  static String m215(sex) => "Kamu sudah wink ke ${sex}";

  static String m216(sex) =>
      "Mengirimkan winker akan menarik perhatian ${sex}, dan jika saling winker dapat memulai percakapan.";

  static String m217(num) =>
      "Batas sapa harian tercapai (${num}/Hari).\nBuka lebih banyak percakapan dengan mengirimkan hadiah";

  static String m218(She, her) =>
      "${She} sangat populer, berikan hadiah untuk ${her} agar ia merasakan ketulusanmu.";

  static String m219(sex) => "Bicarakan tentang momen ini dengan ${sex}";

  static String m220(num) =>
      "${Intl.plural(num, zero: '${num} detik', one: '${num} detik', two: '${num} detik', few: '${num} detik', other: '${num} detik')}";

  static String m221(count, max) => "${count}/${max}";

  static String m222(gift) => "Kirim hadiah ${gift} untuk membuka obrolan.";

  static String m223(name) =>
      "${name} mengirim permintaan untuk mengubah mode obrolan anda.";

  static String m224(user, goods, name) =>
      "${user} mengirimkan ${goods} ${name} kepada anda.";

  static String m225(n) => "Mengirim Anda [${n}]";

  static String m226(nick) => "${nick} Kirimi Anda pertanyaan.";

  static String m227(num) => "Exp kekayaan pengirim +${num}";

  static String m228(num) =>
      "Anda dapat menyetelnya saat level kamar Anda mencapai Lv.${num}";

  static String m229(day) => "Masuk selama ${day} hari";

  static String m230(day) =>
      "Check-in berturut-turut selama ${day} ${Intl.plural(day, zero: 'hari', one: 'hari', two: 'hari', few: 'hari', other: 'hari')}";

  static String m231(num) => "Selamat Mendapatkan Koin";

  static String m232(num) => "Anda telah masuk selama ${num} hari.";

  static String m233(coin) =>
      "Apakah anda akan menggunakan ${coin} koin untuk mengirim hadiah ini?";

  static String m234(money, name) =>
      "Habiskan ${money} berlian untuk membuat lamaran ke ${name}";

  static String m235(num) => "${num} starlight berhasil diterima";

  static String m236(nick) => "${nick} sudah memulai Game pertanyaan.";

  static String m237(num) => "Tetap di kamar selama 30 menit (${num}/30)";

  static String m238(num) => "Tetap di ruangan selama ${num}";

  static String m239(num) => "Yakin ingin menghabiskan ${num} untuk item ini?";

  static String m240(diamonds) =>
      "Yakinkah ingin menghabiskan ${diamonds} diamond untuk bergabung dengan roda Keberuntungan. Pemenang akan menerima 90% dari kumpulan hadiah.";

  static String m241(count) => "buff pertahanan kamar seberang ${count}";

  static String m242(points) => "kamar seberang ${points} poin";

  static String m243(time) =>
      "Tinggal ${time} lagi sampai waktu mulai mengambil";

  static String m244(num) => "${num} kali tersisa hari ini";

  static String m245(num) =>
      "${Intl.plural(num, zero: '${num} time', one: '${num} time', two: '${num} times', few: '${num} times', other: '${num} times')} gratis";

  static String m246(num) =>
      "${Intl.plural(num, zero: '${num} waktu', one: '${num} waktu', two: '${num} waktu', few: '${num} waktu', other: '${num} waktu')}";

  static String m247(num) =>
      "${Intl.plural(num, zero: '${num}  hari', one: '${num}  hari', two: '${num}  hari', few: '${num}  hari', other: '${num}  hari')}";

  static String m248(num) =>
      "${Intl.plural(num, zero: '${num}\n Hari', one: '${num}\nHari', two: '${num}\n Hari', few: '${num}\nHari', other: '${num}\nHari')}";

  static String m249(index) => "Peringkat${index}:";

  static String m250(count) => "Total";

  static String m251(sum) => "Total: ${sum} orang";

  static String m252(level) =>
      "Hanya kamar yang levelnya mencapai LV.${level} yang memiliki kotak harta karun";

  static String m253(count) => "hitung/10";

  static String m254(user, question) =>
      "${user} dihukum di ronde ini, harap terima hukumannya:\n${question}";

  static String m255(user) =>
      "${user} sudah keluar dari room, game dalam ronde ini sudah berakhir.";

  static String m256(num) =>
      "Selamat! Anda memenangkan ${num} diamond di ronde ini.";

  static String m257(content) => "Ketik jawaban anda${content}";

  static String m258(str) => "Ungkap profil Winker satu sama lain?${str}";

  static String m259(time, timeZone, timeOffset) =>
      "Waktu pembaruan: ${time}(${timeZone}${timeOffset})";

  static String m260(num) =>
      "Tingkatkan ruangan anda ke level ${num} untuk mengubah setelan";

  static String m261(diamond) => "Gunakan${diamond}";

  static String m262(other) => "Tentang ${other}";

  static String m263(identity, name) =>
      "${identity} ${name} mematikan video ini.";

  static String m264(mainUser, targetUser) =>
      "Selamat! ${mainUser} dan ${targetUser} telah berhasil dicocokkan, selesaikan tugas berpasangan sekarang juga! ";

  static String m265(user) => "${user} keluar family.";

  static String m266(user, count) =>
      "${user} mendapatkan${count} berlian dalam Pertarungan.";

  static String m267(nick) => "${nick} sedang membuka pesta!";

  static String m268(user) => "${user} adalah pemenang di Pertarungan.";

  static String m269(user) => "${user} bergabung dengan family ini.";

  static String m270(user, manager) =>
      "${user} dikeluarkan dari family oleh ${manager}.";

  static String m271(identity, name) =>
      "${identity} ${name} menjeda pemutaran video.";

  static String m272(mainUser, targetUser) =>
      "${mainUser} Pilih ${targetUser} di babak ini.";

  static String m273(identity, name) =>
      "${identity} ${name} memutar video ini.";

  static String m274(user) => "${user} diangkat sebagai sesepuh.";

  static String m275(user) => "${user} diangkat sebagai Wakil Ketua Family.";

  static String m276(user) => " ${user} memulai Pertarungan hadiah.";

  static String m277(user) => " ${user} memulai Pertarungan suara.";

  static String m278(username) => "${username} telah menangkap layar";

  static String m279(username, result) =>
      "${username} memutar meja putar ini. Hasilnya adalah: ${result}";

  static String m280(text) => "Status diperbarui: ${text}";

  static String m281(userName, familyName) =>
      "${userName} mengundang Anda untuk bergabung dengan ${familyName}, apakah Anda setuju untuk bergabung?";

  static String m282(userName) => "${userName} telah menolak permintaan Anda.";

  static String m283(userName) =>
      "Permohonan Anda ditolak ${userName}. Coba lihat family lain! Pergi ke pusat family!";

  static String m284(userName) =>
      "${userName} ingin masuk ke family Anda! Cek permintaannya!";

  static String m285(date) => "Berlaku hingga ${date}";

  static String m286(diamond) => "Nilai: ${diamond}";

  static String m287(time, total) => "Total waktu";

  static String m288(num) =>
      "Hubungi layanan pelanggan VIP setiap bulan untuk mengirim postingan yang mempromosikan acara kamar Anda ${num}";

  static String m289(num) => "Nomor VIP";

  static String m290(seconds) => "${seconds}detik";

  static String m291(count) => "${count} kali tersisa hari ini";

  static String m292(user) => "${user}, selamat datang di family ini!";

  static String m293(coins) =>
      "Anda kehabisan waktu bebas hari ini dan ingin menghabiskan ${coins} untuk memancing sekali lagi?";

  static String m294(day) => "Hari ${day}";

  static String m295(who) => "ulang tahun ${who}\'";

  static String m296(who) => "Milik ${who}";

  static String m297(name) => "Memenangkan Buah: ${name}";

  static String m298(who, multiple, award) =>
      "${who} menang ${multiple}, dapat ${award}";

  static String m299(coins) => "${coins} koin";

  static String m300(count) =>
      "Anda sudah mendapatkan ${count} percakapan di Winker, tolong tinggalkan bintang 5";

  static String m301(count) =>
      "Kamu punya ${count} teman di Winker, tolong tinggalkan bintang 5~";

  static String m302(name, from) => "Anda mendapatkan ${name} dari ${from}.";

  static String m303(num) =>
      "Kamu menerima ${Intl.plural(num, zero: '${num} gift', one: '${num} gift', two: '${num} gifts', few: '${num} gifts', other: '${num} gifts')}.";

  static String m304(you) => "${you} telah mendapat";

  static String m305(familyName) =>
      "Anda telah dikeluarkan dari family ${familyName}. Coba cari family lain! Pergi ke pusat family!";

  static String m306(level) => "Level VIP Anda turun ke level ${level}, ";

  static String m307(followers, friends) =>
      "Anda memiliki ${followers} pengikut dan${friends} teman di Winker yang sedang menunggu anda";

  static String m308(level) =>
      "Selamat! Anda telah mencapai level VIP ${level}, ";

  static String m309(name) =>
      "Anda menolak permohonan cerai ${name}, harap hargai…";

  static String m310(count) => "buff pertahanan kamar Anda ${count}";

  static String m311(n) => "Anda mengirim [${n}]";

  static String m312(time, days) =>
      "Anda mengajukan pembubaran family pada ${time}. Saat ini dalam ${days} hari masa tenang dan dapat membatalkan kapan saja.";

  static String m313(sex) => "Kamu wink ${sex}";

  static String m314(name) => "Silakan lengkapi informasi untuk ${name} event";

  static String m315(points) => "kamar Anda meraih ${points} poin";

  static String m316(points) => "kamar Anda meraih ${points} poin";

  static String m317(points) => "kamar Anda ${points} poin";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "GoToPost": MessageLookupByLibrary.simpleMessage("Pergi ke postingan"),
    "JoinFanGroup": MessageLookupByLibrary.simpleMessage(
      "Bergabung dengan grup fans",
    ),
    "NoAvailablePerformer": MessageLookupByLibrary.simpleMessage(
      "tidak ada pemain yang tersedia, harap tambahkan terlebih dahulu",
    ),
    "NoPeopleOnMic": MessageLookupByLibrary.simpleMessage(
      "Tidak ada siapa pun di mic",
    ),
    "NoShowNextTime": MessageLookupByLibrary.simpleMessage(
      "Jangan tampilkan lain kali",
    ),
    "OnlinePeopleAtParty": MessageLookupByLibrary.simpleMessage(
      "Yang online di pesta",
    ),
    "RebateGiftSend": MessageLookupByLibrary.simpleMessage(
      "Rabat untuk pengiriman hadiah",
    ),
    "RebateGiftSendDesc": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah ke orang lain dan terima rabat berlian.",
    ),
    "ScreenshotShare": MessageLookupByLibrary.simpleMessage(
      "Berbagi Screenshot",
    ),
    "SelectActivityProgress": MessageLookupByLibrary.simpleMessage(
      "Pilih progres event",
    ),
    "Unknown": MessageLookupByLibrary.simpleMessage("Tidak Diketahui"),
    "VerifyBeforeListening": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara anda sebelum mendengarkan!",
    ),
    "VerifyUnlockCreate": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara anda untuk membuka kunci fitur ruangan!",
    ),
    "VerifyUnlockMatch": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara anda untuk membuka kunci fitur pencocokan!",
    ),
    "WelcomeToJoinParty": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di pesta kami!",
    ),
    "aFamilyLuckyBagWasSentAtRoomPleaseGo": m0,
    "about": MessageLookupByLibrary.simpleMessage("Tentang"),
    "aboutBadgeDesc1": MessageLookupByLibrary.simpleMessage(
      "Semua lencana bisa dipakai.\nAnda dapat memakai hingga 3 lencana sekaligus.\nJika saat ini Anda memakai kurang dari 3 lencana, lencana tersebut akan dikenakan secara otomatis saat Anda mendapatkannya.\nSetiap lencana prestasi memiliki empat tingkat, dari terendah hingga tertinggi: Perunggu-Perak-Emas",
    ),
    "aboutBadgeDesc2": MessageLookupByLibrary.simpleMessage(
      "Medali kehormatan diberikan kepada pengguna yang memenuhi syarat setiap hari Minggu dan berlaku selama sekitar satu minggu.",
    ),
    "aboutBadgeTitle1": MessageLookupByLibrary.simpleMessage("1. Pakai Medali"),
    "aboutBadgeTitle2": MessageLookupByLibrary.simpleMessage(
      "2. Medali Kehormatan",
    ),
    "aboutBadges": MessageLookupByLibrary.simpleMessage("Tentang Medali"),
    "aboutMe": MessageLookupByLibrary.simpleMessage("Tentang saya"),
    "aboutOtherInformation": m1,
    "aboutUs": MessageLookupByLibrary.simpleMessage("Tentang kami"),
    "academic": MessageLookupByLibrary.simpleMessage("Akademik"),
    "academicTip": MessageLookupByLibrary.simpleMessage(
      "Aku kuliah di Universitas Indonesia dengan prestasi akademik yang baik.",
    ),
    "accept": MessageLookupByLibrary.simpleMessage("Setuju"),
    "accepted": MessageLookupByLibrary.simpleMessage("Disetujui"),
    "accessAllTip": MessageLookupByLibrary.simpleMessage(
      "Aplikasi hanya dapat mengakses beberapa File di perangkat. Masuk ke pengaturan sistem dan izinkan aplikasi untuk mengakses semua file di perangkat.",
    ),
    "accessLimitedAssets": MessageLookupByLibrary.simpleMessage(
      "Lanjutkan dengan akses terbatas",
    ),
    "accessPermission": MessageLookupByLibrary.simpleMessage("Hak akses ruang"),
    "accessiblePathName": MessageLookupByLibrary.simpleMessage(
      "file yang dapat diakses",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Akun"),
    "accountDataCantBeRecovered": MessageLookupByLibrary.simpleMessage(
      "Data akun tidak dapat dipulihkan setelah dihapus.",
    ),
    "accountDeleted": MessageLookupByLibrary.simpleMessage("Akun dihapus"),
    "accumulated": MessageLookupByLibrary.simpleMessage("Akumulasi"),
    "accumulatedStarlight": MessageLookupByLibrary.simpleMessage(
      "Akumulasi starlight",
    ),
    "accumulatedSubmit": MessageLookupByLibrary.simpleMessage(
      "Akumulasi Pengiriman",
    ),
    "achievementBadge": MessageLookupByLibrary.simpleMessage(
      "Medali Kehormatan",
    ),
    "achievementTime": MessageLookupByLibrary.simpleMessage(
      "Waktu pencapaian:",
    ),
    "actNow": MessageLookupByLibrary.simpleMessage("Tidak sekarang"),
    "activate": MessageLookupByLibrary.simpleMessage("mengaktifkan"),
    "activatePremium": MessageLookupByLibrary.simpleMessage("Aktifkan Premium"),
    "activatePremiumConfirm": m2,
    "activatedValidUntil": m3,
    "activationBroadcast": MessageLookupByLibrary.simpleMessage(
      "Aktifkan siaran",
    ),
    "activationBroadcastDesc": MessageLookupByLibrary.simpleMessage(
      "Tarik perhatian semua orang dengan siaran Premium Anda",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Aktif"),
    "activeLevel": MessageLookupByLibrary.simpleMessage("Tingkat Keaktifan"),
    "activity": MessageLookupByLibrary.simpleMessage("Event"),
    "activityBadge": MessageLookupByLibrary.simpleMessage("Medali Event"),
    "activityProgress": MessageLookupByLibrary.simpleMessage("Progres event"),
    "add": MessageLookupByLibrary.simpleMessage("Tambahkan"),
    "addCoverPhoto": MessageLookupByLibrary.simpleMessage(
      "Tambahkan foto sampul",
    ),
    "addFriend": MessageLookupByLibrary.simpleMessage("Tambahkan teman"),
    "addMusic": MessageLookupByLibrary.simpleMessage("Tambah Musik"),
    "addNewRingInYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Silakan tambahkan cincin baru di ransel Anda",
    ),
    "addPassionTip": MessageLookupByLibrary.simpleMessage(
      "Tambahkan Tag minat Anda untuk menarik lebih banyak teman yang berpikiran sama",
    ),
    "addScore": MessageLookupByLibrary.simpleMessage("Skor Audisi"),
    "addToCalendar": MessageLookupByLibrary.simpleMessage(
      "Tambahkan ke Kalender",
    ),
    "addToFavorite": MessageLookupByLibrary.simpleMessage(
      "Tambahkan ke favorit",
    ),
    "addToMyCollection": MessageLookupByLibrary.simpleMessage(
      "Tambahkan ke koleksi saya",
    ),
    "addToYourCalendar": MessageLookupByLibrary.simpleMessage(
      "Tambahkan acara ke Kalender Anda",
    ),
    "addTopic": MessageLookupByLibrary.simpleMessage("Tambahkan Topik"),
    "addUpcomingEvent": MessageLookupByLibrary.simpleMessage(
      "Tambahkan acara mendatang yang Anda langgan ke kalender",
    ),
    "added": MessageLookupByLibrary.simpleMessage("Ditambahkan"),
    "addedSuccess": MessageLookupByLibrary.simpleMessage(
      "Berhasil ditambahkan",
    ),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "adminRoom": MessageLookupByLibrary.simpleMessage("Kamar admin"),
    "administrator": MessageLookupByLibrary.simpleMessage("Jumlah Admin"),
    "advertising": MessageLookupByLibrary.simpleMessage(
      "pengguna ini sedang memberikan iklan",
    ),
    "afterTurningItOnOnlyFamilyMembersCanEnterThe":
        MessageLookupByLibrary.simpleMessage(
          "Hanya anggota family yang dapat memasuki ruang setelah fitur ini diaktifkan, dan pengguna lain akan otomatis dikeluarkan",
        ),
    "afterYouQuitTheFamilyYouWillClearAllThe": MessageLookupByLibrary.simpleMessage(
      "Setelah Anda keluar dari family, semua aset yang terkait dengan keluarga akan dihapus. Apakah Anda yakin ingin keluar dari family?",
    ),
    "agencyContactTitle": MessageLookupByLibrary.simpleMessage(
      "Ikuti Instagram kami, dan bekerja sama dengan kami.",
    ),
    "agencyNameId": m4,
    "agoraReconnect": MessageLookupByLibrary.simpleMessage(
      "Sambungkan kembali jaringan, Harap tunggu…",
    ),
    "agree": MessageLookupByLibrary.simpleMessage("Setuju"),
    "agreeAlert": MessageLookupByLibrary.simpleMessage(
      "Harap setujui dengan Kebijakan privasi dan Ketentuan penggunaan untuk masuk",
    ),
    "agreeAndContinue": MessageLookupByLibrary.simpleMessage(
      "Setuju dan lanjutkan",
    ),
    "agreePrivacyContent": MessageLookupByLibrary.simpleMessage(
      "Info anda hanya akan digunakan untuk membuat akun. Kami tidak akan membocorkan privasi pengguna.",
    ),
    "agreeTo": MessageLookupByLibrary.simpleMessage("Setuju "),
    "agreeToTermAndPolicy": MessageLookupByLibrary.simpleMessage(
      "Setuju dengan Ketentuan Penggunaan dan Kebijakan Privasi",
    ),
    "agreed": MessageLookupByLibrary.simpleMessage("Disetujui"),
    "album": MessageLookupByLibrary.simpleMessage("Album"),
    "albumNotEnabledContent": MessageLookupByLibrary.simpleMessage(
      "winker tidak dapat mengakses foto. Izinkan Winker untuk mengakses foto di setelan perangkat.",
    ),
    "albumNotEnabledTitle": MessageLookupByLibrary.simpleMessage(
      "Akses album tidak diaktifkan",
    ),
    "alias": MessageLookupByLibrary.simpleMessage("Nama lain"),
    "all": MessageLookupByLibrary.simpleMessage("Semua"),
    "allInRoom": MessageLookupByLibrary.simpleMessage("Semua di kamar"),
    "allMessages": MessageLookupByLibrary.simpleMessage("Semua Pesan"),
    "allNotifications": MessageLookupByLibrary.simpleMessage(
      "Semua notifikasi",
    ),
    "allOnMic": MessageLookupByLibrary.simpleMessage("Semua di Mikrofon"),
    "allParticipantsDefenseBuff": m5,
    "allParticipantsPoints": m6,
    "allRings": MessageLookupByLibrary.simpleMessage("Semua Cincin"),
    "allRoomEvent": MessageLookupByLibrary.simpleMessage("Semua event ruang"),
    "allStickers": MessageLookupByLibrary.simpleMessage("Semua Stiker"),
    "allUserInYourRoomCanHearVoice": MessageLookupByLibrary.simpleMessage(
      "Semua pengguna di room Anda dapat mendengar suara di seberang sekarang! Cobalah!",
    ),
    "allUsers": MessageLookupByLibrary.simpleMessage("Semua pengguna"),
    "allUsersCanEnterTheRoom": MessageLookupByLibrary.simpleMessage(
      "Semua pengguna dapat memasuki ruang family?",
    ),
    "allow": MessageLookupByLibrary.simpleMessage("Izinkan"),
    "alreadyAdded": MessageLookupByLibrary.simpleMessage("Sudah ditambahkan"),
    "alreadyGotLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Sudah dapat lucky bag ini.",
    ),
    "alreadyInThisRoom": MessageLookupByLibrary.simpleMessage(
      "Sudah di ruangan ini",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("Jumlah:"),
    "amountToExchange": MessageLookupByLibrary.simpleMessage(
      "Masukkan jumlah yang akan ditukar",
    ),
    "and": MessageLookupByLibrary.simpleMessage(" dan "),
    "anime": MessageLookupByLibrary.simpleMessage("Anime"),
    "anniversary": MessageLookupByLibrary.simpleMessage("Hari jadi"),
    "announce": MessageLookupByLibrary.simpleMessage("Umumkan"),
    "announceDefault": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di family!",
    ),
    "announceHerPick": MessageLookupByLibrary.simpleMessage(
      "Umumkan pilihannya",
    ),
    "announceHisPick": MessageLookupByLibrary.simpleMessage(
      "Umumkan pilihannya",
    ),
    "announceRule": MessageLookupByLibrary.simpleMessage(
      "1. Sekarang Anda dapat memilih untuk mengakhiri bagian ini dan mengumumkan hasilnya\n2. Jika dua pemain saling memilih, artinya pencocokan berhasil",
    ),
    "announceRuleTitle": MessageLookupByLibrary.simpleMessage("Hasil final"),
    "announcement": MessageLookupByLibrary.simpleMessage("Pengumuman"),
    "annulledCouple": MessageLookupByLibrary.simpleMessage(
      "Anda telah membatalkan hubungan pasangan",
    ),
    "answerAreRevealed": MessageLookupByLibrary.simpleMessage(
      "Jawabannya akan terungkap ketika Anda semua menjawab",
    ),
    "answerQuestionGame": m7,
    "appLanguage": MessageLookupByLibrary.simpleMessage("Bahasa"),
    "appName": MessageLookupByLibrary.simpleMessage("Winker\ner"),
    "appProblems": MessageLookupByLibrary.simpleMessage("Masalah aplikasi"),
    "applicationFailed": MessageLookupByLibrary.simpleMessage(
      "Permintaan gagal",
    ),
    "applicationHasBeenSent": MessageLookupByLibrary.simpleMessage(
      "Permintaan telah dikirim, harap tunggu proses pengguna",
    ),
    "applicationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Permintaan berhasil",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Terapkan"),
    "applyAgencySubmit": MessageLookupByLibrary.simpleMessage(
      "Aplikasi telah diajukan dan sedang menunggu peninjauan agensi",
    ),
    "applyCompulsoryDivorce": MessageLookupByLibrary.simpleMessage(
      "ajukan permohonan untuk perceraian wajib, silakan buat pilihan Anda.",
    ),
    "applyCondition": MessageLookupByLibrary.simpleMessage("Syarat menerapkan"),
    "applyEventRewards": MessageLookupByLibrary.simpleMessage(
      "Terapkan hadiah acara",
    ),
    "applyJoinAgency": MessageLookupByLibrary.simpleMessage(
      "Mendaftar ke Agensi",
    ),
    "applyList": MessageLookupByLibrary.simpleMessage("Daftar Pengajuan"),
    "applyMicList": MessageLookupByLibrary.simpleMessage(
      "Mendaftar untuk daftar mic",
    ),
    "applySuccess": MessageLookupByLibrary.simpleMessage("Berhasil menerapkan"),
    "applyTakeMic": MessageLookupByLibrary.simpleMessage(
      "Ajukan permohonan untuk mengambil mic",
    ),
    "applyTakeMicSuccess": MessageLookupByLibrary.simpleMessage(
      "ajukan agar berhasil mengambil mic.",
    ),
    "applyTooFrequently": MessageLookupByLibrary.simpleMessage(
      "Permintaan untuk mengambil mic terlalu sering. Coba lagi nanti",
    ),
    "applyingJoin": MessageLookupByLibrary.simpleMessage("Mendaftar"),
    "approvalRequiredToCreateConversation":
        MessageLookupByLibrary.simpleMessage(
          "Persetujuan diperlukan untuk membuat percakapan",
        ),
    "approvedMic": MessageLookupByLibrary.simpleMessage(
      "Disetujui untuk mengambil mic",
    ),
    "areYouSureToDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin untuk menghapus akun Anda?",
    ),
    "areYouSureYouWantToDisbandTheFamilyOnce": m8,
    "areYouSureYouWantToDistributeATotalOf": m9,
    "areYourSureExit": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin untuk keluar?",
    ),
    "askAQuestion": MessageLookupByLibrary.simpleMessage(
      "Ajukan pertanyaan agar anda berdua menjawabnya",
    ),
    "askAnotherQuestion": MessageLookupByLibrary.simpleMessage(
      "Ketuk untuk mengajukan pertanyaan lain",
    ),
    "askShareToMoment": MessageLookupByLibrary.simpleMessage(
      "Apa anda ingin membagikannya ke momen？",
    ),
    "askedForMoney": MessageLookupByLibrary.simpleMessage(
      "pengguna ini meminta uang kepada saya",
    ),
    "askingNumber": MessageLookupByLibrary.simpleMessage(
      "pengguna ini sedang meminta nomor saya",
    ),
    "assetData": MessageLookupByLibrary.simpleMessage("Data aset"),
    "assetDataCharisma": MessageLookupByLibrary.simpleMessage(
      "Pesona: Anda dapat memperoleh poin pesona saat menerima hadiah atau items dari pengguna lain:\n1 emas = 1 poin\n1 diamond = 10 poin\nJika anda mengirimkan hadiah kepada diri anda sendiri, anda hanya akan mendapatkan poin kontribusi, bukan poin pesona.",
    ),
    "assetDataTitle": MessageLookupByLibrary.simpleMessage(
      "Bagaimana cara mendapatkan kontribusi atau pesona?",
    ),
    "assetDataWealth": MessageLookupByLibrary.simpleMessage(
      "Kontribusi: Anda dapat memperoleh poin kontribusi dengan mengirimkan hadiah atau items kepada orang lain:\n1 emas = 1 poin\n1 diamond = 10 poin",
    ),
    "atLeastNumDiamonds": m10,
    "atTa": MessageLookupByLibrary.simpleMessage("at/et"),
    "atUserName": MessageLookupByLibrary.simpleMessage("@Nama User"),
    "atWho": m11,
    "attachment": MessageLookupByLibrary.simpleMessage("Lampiran"),
    "audioCall": MessageLookupByLibrary.simpleMessage("Panggilan Audio"),
    "auditPass": MessageLookupByLibrary.simpleMessage("Audit lulus"),
    "auto": MessageLookupByLibrary.simpleMessage("Otomatis"),
    "autoRejected": MessageLookupByLibrary.simpleMessage("Ditolak otomatis"),
    "automaticStartNum": m12,
    "automaticStartWhenFull": MessageLookupByLibrary.simpleMessage(
      "Mulai otomatis ketika penuh",
    ),
    "automaticallyConnected": MessageLookupByLibrary.simpleMessage(
      "Sebuah penggilan suara terkoneksi secara otomatis untuk Anda. Harap bersiap-siap.",
    ),
    "automaticallyPass": MessageLookupByLibrary.simpleMessage(
      "Lulus secara otomatis",
    ),
    "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
    "avatarFrame": MessageLookupByLibrary.simpleMessage("Bingkai"),
    "avatarHistory": MessageLookupByLibrary.simpleMessage("Riwayat avatar"),
    "avatarPreview": MessageLookupByLibrary.simpleMessage("Pratinjau avatar"),
    "awaitingResponse": MessageLookupByLibrary.simpleMessage(
      "Menunggu respon…...",
    ),
    "awaitingWear": MessageLookupByLibrary.simpleMessage("Menunggu keausan"),
    "awardedOn": m13,
    "away": MessageLookupByLibrary.simpleMessage("Jauh"),
    "baby": MessageLookupByLibrary.simpleMessage("Bayi"),
    "background": MessageLookupByLibrary.simpleMessage("Latar belakang"),
    "backgroundMusic": MessageLookupByLibrary.simpleMessage(
      "Latarbelakang musik",
    ),
    "backpack": MessageLookupByLibrary.simpleMessage("Ransel"),
    "backpackEmpty": MessageLookupByLibrary.simpleMessage(
      "Ransel anda kosong.",
    ),
    "backpackRings": MessageLookupByLibrary.simpleMessage("Cincin ransel"),
    "badge": MessageLookupByLibrary.simpleMessage("Medali"),
    "badgeDisplay": MessageLookupByLibrary.simpleMessage("Tampilan medali"),
    "badgeDisplayFollowingPlaces": MessageLookupByLibrary.simpleMessage(
      "Medali akan ditampilkan di halaman informasi pribadi, ruangan, dan obrolan",
    ),
    "badgeLeaderboard": MessageLookupByLibrary.simpleMessage(
      "Peringkat Medali",
    ),
    "badgeRankRule": MessageLookupByLibrary.simpleMessage("Peringkat Medali"),
    "badgeRankRule1": MessageLookupByLibrary.simpleMessage(
      "Menyala medali dengan level yang berbeda bisa mendapatkan bintang:",
    ),
    "badgeRankRule2": MessageLookupByLibrary.simpleMessage(
      "Pengguna akan diberi peringkat berdasarkan bintang",
    ),
    "badgeRanking": m14,
    "badgeRankingRule": MessageLookupByLibrary.simpleMessage(
      "1. Peringkat medali dihitung berdasarkan jumlah bintang yang diperoleh pengguna dari menyala medali",
    ),
    "badgeRule": MessageLookupByLibrary.simpleMessage("Aturan Medali"),
    "badgeRuleBronze": MessageLookupByLibrary.simpleMessage("Perunggu"),
    "badgeRuleDiamond": MessageLookupByLibrary.simpleMessage("Berlian"),
    "badgeRuleGold": MessageLookupByLibrary.simpleMessage("Emas"),
    "badgeRuleSilver": MessageLookupByLibrary.simpleMessage("Perak"),
    "badgeTypeRule": MessageLookupByLibrary.simpleMessage(
      "Bagaimana mendapatkan medali?",
    ),
    "badgeTypeRule1": MessageLookupByLibrary.simpleMessage(
      "Penuhi persyaratan yang sesuai untuk menyalakan medali",
    ),
    "badgeTypeRule2": MessageLookupByLibrary.simpleMessage(
      "Level medali: perunggu-perak-emas-berlian",
    ),
    "badgeWearDesc": MessageLookupByLibrary.simpleMessage(
      "Anda bisa memakai medali yang anda peroleh di Winker. Sumber medali berasal dari aktivitas dan pencapaian yang anda selesaikan di Winker. Medali yang Anda kenakan akan ditampilkan di profil dan profil ruangan. Anda dapat memakai maksimal tiga lencana.",
    ),
    "badgeWearInstructions": MessageLookupByLibrary.simpleMessage(
      "Instruksi pemakaian medali",
    ),
    "bag": MessageLookupByLibrary.simpleMessage("Ransel"),
    "bagExpired": MessageLookupByLibrary.simpleMessage(
      "Tas ini telah kedaluwarsa. ",
    ),
    "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
    "baloot": MessageLookupByLibrary.simpleMessage("Baloot"),
    "banner": MessageLookupByLibrary.simpleMessage("banner"),
    "baron": MessageLookupByLibrary.simpleMessage("Baron"),
    "beFollowedGuide": m15,
    "beMyBetterHalf": MessageLookupByLibrary.simpleMessage(
      " jadilah separuh saya yang lebih baik",
    ),
    "bePermanentlyRestricted": MessageLookupByLibrary.simpleMessage(
      "Akun anda telah Diblokir secara permanen karena melanggar aturan komunitas",
    ),
    "beRestrictedFor": MessageLookupByLibrary.simpleMessage(
      "Akun anda telah dibatasi karena melanggar aturan komunitas.",
    ),
    "beauty": MessageLookupByLibrary.simpleMessage("Cantik"),
    "becomeCouple30Days": MessageLookupByLibrary.simpleMessage(
      "Menjadi pasangan 30 hari",
    ),
    "becomeCouple7Days": MessageLookupByLibrary.simpleMessage(
      "Menjadi pasangan 7 hari",
    ),
    "becomeCoupleHalfYear": MessageLookupByLibrary.simpleMessage(
      "Menjadi pasangan setengah tahun",
    ),
    "becomeCoupleOneYear": MessageLookupByLibrary.simpleMessage(
      "Menjadi pasangan setahun",
    ),
    "beforeDaysAgo": m16,
    "beforeHoursAgo": m17,
    "beforeMiuntesAgo": m18,
    "beforeMonthsAgo": m19,
    "beforeWeeksAgo": m20,
    "beforeYearsAgo": m21,
    "benefitsHighLevel": MessageLookupByLibrary.simpleMessage(
      "Manfaat Level Tinggi",
    ),
    "benefitsLevelContent1": MessageLookupByLibrary.simpleMessage(
      "1. Anda akan mendapatkan hadiah saat level naik ke level khusus.",
    ),
    "benefitsLevelContent2": MessageLookupByLibrary.simpleMessage(
      "2. Level yang lebih tinggi akan membantu anda mendapatkan lebih banyak perhatian.",
    ),
    "bestLuck": MessageLookupByLibrary.simpleMessage("Paling Beruntung"),
    "betterLuckNextTime": MessageLookupByLibrary.simpleMessage(
      "Semoga beruntung lain kali",
    ),
    "biggestWinners": MessageLookupByLibrary.simpleMessage(
      "Pemenang terbesar ronde ini",
    ),
    "bioCardContent": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda ingin berbagi momen agar orang lain lebih mengenal Anda",
    ),
    "bioCardTitle": MessageLookupByLibrary.simpleMessage(
      "Selamat!\nAnda mendapat Kartu Identitas Winker！",
    ),
    "birthday": MessageLookupByLibrary.simpleMessage("Tanggal Lahir"),
    "blessValue": MessageLookupByLibrary.simpleMessage("Nilai keberkahan"),
    "blindDate": MessageLookupByLibrary.simpleMessage("Kencan Buta"),
    "block": MessageLookupByLibrary.simpleMessage("Blokir"),
    "blockConfirm": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin ingin memblokir pengguna ini?",
    ),
    "blockHint": MessageLookupByLibrary.simpleMessage(
      "Keamanan anda adalah prioritas utama kami. Kami akan menghandlenya secepat mungkin. Untuk menghindari gangguan lebih lanjut, anda juga dapat memblokir pengguna tersebut.",
    ),
    "blockLost": MessageLookupByLibrary.simpleMessage("Daftar Blokir"),
    "blockRoomConfirm": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin ingin memblokir ruangan ini?",
    ),
    "blockSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Blokir berhasil",
    ),
    "blockUser": MessageLookupByLibrary.simpleMessage("Memblokir pengguna"),
    "blockUserPermanently": MessageLookupByLibrary.simpleMessage(
      "Blokir pengguna ini secara permanen",
    ),
    "blockUserTitle": MessageLookupByLibrary.simpleMessage("Blokir Pengguna"),
    "blockedByThisUser": MessageLookupByLibrary.simpleMessage(
      "Anda Diblokir oleh pengguna ini",
    ),
    "blockedList": MessageLookupByLibrary.simpleMessage(
      "Daftar yang Anda blokir",
    ),
    "blockedThisUser": MessageLookupByLibrary.simpleMessage(
      "Anda telah memblokir pengguna ini",
    ),
    "blockedTime": m22,
    "bloodyViolence": MessageLookupByLibrary.simpleMessage(
      "kekerasan berdarah",
    ),
    "bonus": MessageLookupByLibrary.simpleMessage("Bonus"),
    "bonusPackage": MessageLookupByLibrary.simpleMessage("Paket bonus"),
    "bothOfYouShowId": MessageLookupByLibrary.simpleMessage(
      "Anda berdua telah menunjukkan identitas! Anda berdua dapat mengobrol satu sama lain.",
    ),
    "bothSidesShowIdentities": MessageLookupByLibrary.simpleMessage(
      "Kedua pihak mengidentifikasi diri mereka sendiri, tanpa batasan waktu",
    ),
    "bottleAvatar": MessageLookupByLibrary.simpleMessage("Avatar botol"),
    "bottleAvatarLabel": MessageLookupByLibrary.simpleMessage(
      "Setel avatar untuk ruang anonim ini.",
    ),
    "bottleFrom": MessageLookupByLibrary.simpleMessage("Botol dari "),
    "bottleNickname": MessageLookupByLibrary.simpleMessage(
      "Nama panggilan botol",
    ),
    "bottleSettingDesc": MessageLookupByLibrary.simpleMessage(
      "Surat rahasia adalah ruang yang sepenuhnya anonim. Avatar dan nama panggilan yang Anda atur hanya dapat ditampilkan di sini.",
    ),
    "brand": MessageLookupByLibrary.simpleMessage("Merek"),
    "bronzeFamily": MessageLookupByLibrary.simpleMessage("Family Perunggu"),
    "bug": MessageLookupByLibrary.simpleMessage("Bug"),
    "bugGoodsDialogTips": m23,
    "buyGiftBoxDesc": m24,
    "buyWithAmount": m25,
    "calculatedByGifts": MessageLookupByLibrary.simpleMessage(
      "Dihitung dari hadiah yang diterima",
    ),
    "calculating": MessageLookupByLibrary.simpleMessage("Menghitung"),
    "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator"),
    "calculatorClosed": MessageLookupByLibrary.simpleMessage(
      "Kalkulator ditutup.",
    ),
    "calculatorHint": MessageLookupByLibrary.simpleMessage(
      "Masukkan tema event...",
    ),
    "calculatorOpenedBy": MessageLookupByLibrary.simpleMessage(
      "Kalkulator dibuka oleh",
    ),
    "calculatorRanking": MessageLookupByLibrary.simpleMessage(
      "Peringkat kalkulator",
    ),
    "calculatorWillClosed": MessageLookupByLibrary.simpleMessage(
      "Kalkulator akan ditutup dalam 1 menit.",
    ),
    "callCancelled": MessageLookupByLibrary.simpleMessage(
      "Panggilan dibatalkan oleh penelpon",
    ),
    "callDeclined": MessageLookupByLibrary.simpleMessage("Panggilan ditolak"),
    "callEnded": MessageLookupByLibrary.simpleMessage("Panggilan berakhir"),
    "callError": MessageLookupByLibrary.simpleMessage("Kesalahan panggilan"),
    "callFailed": MessageLookupByLibrary.simpleMessage("Panggilan gagal"),
    "callInRoomError": MessageLookupByLibrary.simpleMessage(
      "Panggilan tidak didukung di dalam ruangan",
    ),
    "callInviteAudio": MessageLookupByLibrary.simpleMessage(
      "Anda diundang untuk  panggilan suara",
    ),
    "callInviteVideo": MessageLookupByLibrary.simpleMessage(
      "Anda diundang untuk panggilan video",
    ),
    "callMatch": MessageLookupByLibrary.simpleMessage("Pencocokan panggilan"),
    "callMatchRule": MessageLookupByLibrary.simpleMessage(
      "1. Selamat datang di Call Match! Anda dapat mencocokkan dengan pengguna nyata dan mengobrol dengan mereka sesuka hati Anda.\n2. Setiap pencocokan membutuhkan waktu. Itu tergantung pada berapa banyak pengguna yang online sekarang. Jadi harap bersabar.\n3. Untuk setiap bicara, Anda memiliki waktu 4 menit untuk memberikan bicara dengan menyembunyikan identitas Anda. Jika Anda suka banyak bicara, Anda bisa menunjukkan siapa diri Anda dan meyakinkan pihak lawan untuk melakukan hal yang sama. Setelah kedua belah pihak mengidentifikasi, Anda akan diberikan waktu bicara tambahan.\n4. Selain itu, menunjukkan identitas Anda memungkinkan pihak lawan untuk dapat melihat informasi pribadi Anda dan mengetahui lebih banyak tentang Anda! Menunjukkan identitas pihak lawan juga dapat membuka kunci panggilan setelah panggilan selesai\n5. Jika Anda mengalami pelecehan, provokasi atau perilaku tidak sopan, harap laporkan kepada kami. Selain itu, Anda dapat menilai setiap panggilan (baik atau buruk) dan kami akan menerima tanggapan Anda.",
    ),
    "callMatchSetTips": MessageLookupByLibrary.simpleMessage(
      "Tutup tidak akan memanggil call match untuk Anda.",
    ),
    "callMatchTip1": MessageLookupByLibrary.simpleMessage(
      "Kami mencocokkan pengguna untuk Anda. Harap tunggu",
    ),
    "callMatchTip2": MessageLookupByLibrary.simpleMessage(
      "Bagikan momen Anda dengan orang lain",
    ),
    "callMatchTip3": MessageLookupByLibrary.simpleMessage(
      "Perlakuan kejam dan provokasi sangat dilarang",
    ),
    "callMatchTips": MessageLookupByLibrary.simpleMessage(
      "Selama panggilan suara, identitas kedua belah pihak disembunyikan secara default. Anda dapat mengungkapkan identitas Anda kapan saja.",
    ),
    "callTime": m26,
    "calling": MessageLookupByLibrary.simpleMessage("Memanggil"),
    "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
    "cameraAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Akses kamera tidak diaktifkan",
    ),
    "cameraPermission": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" ingin mengakses kamera anda dan mengumpulkan data foto anda untuk mengaktifkan pengunggahan foto, fungsi kamera hanya tersedia saat aplikasi sedang digunakan.",
    ),
    "canNotEditAvatar": MessageLookupByLibrary.simpleMessage(
      "Kamu dapat mengedit nama panggilan setiap 24 jam, silakan coba lagi nanti",
    ),
    "canNotEditName": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengedit nama panggilan setiap 24 jam, silakan coba lagi nanti",
    ),
    "canNotEnterRoom": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat masuk ke ruang obrolan",
    ),
    "canWeGetKnow": MessageLookupByLibrary.simpleMessage(
      "Bisakah kita berkenalan?",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Batalkan"),
    "cancelAll": MessageLookupByLibrary.simpleMessage("Batalkan semua"),
    "cancelDissolution": MessageLookupByLibrary.simpleMessage(
      "Batal pembubaran",
    ),
    "cancelEvent": MessageLookupByLibrary.simpleMessage("Batalkan Acara"),
    "cancelEventConfirm": MessageLookupByLibrary.simpleMessage(
      "Pembatalan acara akan menyebabkan penghapusan pelanggan dan Anda harus membuat ulang acara untuk mengumpulkan teman-teman Anda.",
    ),
    "cancelEventCreateContent": MessageLookupByLibrary.simpleMessage(
      "Jika Anda keluar sekarang, acara Anda tidak akan dibuat dan progres Anda tidak akan disimpan.",
    ),
    "cancelEventCreateTitle": MessageLookupByLibrary.simpleMessage(
      "Keluar tanpa menyelesaikan?",
    ),
    "cancelMaster": MessageLookupByLibrary.simpleMessage("Batalkan Sesepuh"),
    "cancelPickTopic": MessageLookupByLibrary.simpleMessage(
      "Keluar dari rekaman akan dapat menghapus apa yang anda rekam",
    ),
    "cancelPin": MessageLookupByLibrary.simpleMessage("Batalkan pin"),
    "cancelRequest": MessageLookupByLibrary.simpleMessage(
      "Batalkan permintaan",
    ),
    "cancelRoomEvent": MessageLookupByLibrary.simpleMessage("Batalkan event"),
    "cancelRoomEventTips": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin ingin membatalkan event di ruang ini?",
    ),
    "cancelVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Batalkan Wakil Ketua Family",
    ),
    "canceled": MessageLookupByLibrary.simpleMessage("Dibatalkan"),
    "candy": MessageLookupByLibrary.simpleMessage("Candy"),
    "cannotBeSetDuringTheGame": MessageLookupByLibrary.simpleMessage(
      "Tidak dapat mengubah pengaturan selama permainan berlangsung",
    ),
    "cannotDownMicCaptain": MessageLookupByLibrary.simpleMessage(
      "Kapten dalam game tidak bisa down mic",
    ),
    "cannotDownMicInPlaying": MessageLookupByLibrary.simpleMessage(
      "Pemain dalam game tidak bisa ditendang keluar",
    ),
    "cannotExitRoomDuringGame": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat menutup ruangan selama permainan berlangsung, tetapi Anda dapat memilih untuk meninggalkan ruangan.",
    ),
    "cannotKickMicInPlaying": MessageLookupByLibrary.simpleMessage(
      "Pemain dalam game tidak bisa ditendang keluar",
    ),
    "cannotStartLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat memulai roda keberuntungan saat meja putar menyala",
    ),
    "cannotStartTurntable": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat memulai meja putar saat roda keberuntungan menyala",
    ),
    "cannotSwitchMode": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat beralih mode selama game ini.",
    ),
    "cannotSwitchRoomModes": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat beralih mode kamar selama game ini",
    ),
    "cartoon": MessageLookupByLibrary.simpleMessage("Kartun"),
    "cartoonDesc": MessageLookupByLibrary.simpleMessage(
      "Menjadi superhero kartun dan seni digital dalam 5 detik!",
    ),
    "cd": MessageLookupByLibrary.simpleMessage("CD"),
    "change": MessageLookupByLibrary.simpleMessage("Ganti"),
    "changeAccessibleLimitedAssets": MessageLookupByLibrary.simpleMessage(
      "Perbarui daftar File akses terbatas",
    ),
    "changeAvatar": MessageLookupByLibrary.simpleMessage("Ganti Avatar"),
    "changeCampFailed": MessageLookupByLibrary.simpleMessage(
      "Gagal bergabung dengan kamp, tidak dapat mengubah kamp",
    ),
    "changeChatMode": m27,
    "changeChatModeTips": MessageLookupByLibrary.simpleMessage(
      "ubah mode obrolan",
    ),
    "changeCover": MessageLookupByLibrary.simpleMessage("Ganti sampul"),
    "changeFanClubName": MessageLookupByLibrary.simpleMessage(
      "Mengubah nama klub penggemar",
    ),
    "changeModeContent": MessageLookupByLibrary.simpleMessage(
      "Anda harus mengakhiri permainan saat ini untuk melakukan operasi ini. Apakah Anda ingin mengakhiri permainan?",
    ),
    "changeNumberOfMic": m28,
    "changePremiumSetting": MessageLookupByLibrary.simpleMessage(
      "1.Ubah Pengaturan Anda di atas",
    ),
    "changeQuestion": MessageLookupByLibrary.simpleMessage("Ganti pertanyaan"),
    "changeRoomModeTitle": MessageLookupByLibrary.simpleMessage(
      "Mengubah Tipe Kamar",
    ),
    "changeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil mengubah",
    ),
    "changeTheNumberMicsWillRemoveUser": MessageLookupByLibrary.simpleMessage(
      "Mengubah jumlah mikrofon akan dapat menghapus semua pengguna di mikrofon tersebut, apakah Anda yakin akan mengubah jumlah mikrofon?",
    ),
    "changeVideoModeTips": MessageLookupByLibrary.simpleMessage(
      "Yakin ingin mengubah mode kamar? Ini akan menyebabkan video berhenti diputar.",
    ),
    "changeVideoVolume": MessageLookupByLibrary.simpleMessage(
      "Ubah Volume Video",
    ),
    "changedInviteOnly": MessageLookupByLibrary.simpleMessage(
      "Pemilik dapat mengubah izin mic hanya penerima undangan",
    ),
    "characterTitle": MessageLookupByLibrary.simpleMessage(
      "Tipe kepribadian Anda",
    ),
    "charisma": MessageLookupByLibrary.simpleMessage("Pesona"),
    "charmLevel": MessageLookupByLibrary.simpleMessage("Pesona"),
    "charmLevelUp": MessageLookupByLibrary.simpleMessage(
      "Selamat! Pesona Anda levelnya naik ke",
    ),
    "chat": MessageLookupByLibrary.simpleMessage("Chat"),
    "chatBubble": MessageLookupByLibrary.simpleMessage("Gelembung"),
    "chatConsumeGoldCoin": MessageLookupByLibrary.simpleMessage(
      "Konsumsi gold coin terlebih dahulu, jika gold coin kurang konsumsi diamond",
    ),
    "chatConsumes": MessageLookupByLibrary.simpleMessage(
      "Mengirim pesan menghabiskan",
    ),
    "chatFrame": MessageLookupByLibrary.simpleMessage("Gelembung"),
    "chatGuideTipsFate": MessageLookupByLibrary.simpleMessage(
      "Daftar Bola Takdir yang cocok untuk Anda, klik yang Anda minati untuk mulai mengobrol!",
    ),
    "chatGuideTipsMsg": MessageLookupByLibrary.simpleMessage(
      "Semua pesan akan ditampilkan di sini, dan Anda dapat mengklik untuk mengobrol dengannya.",
    ),
    "chatHistory": MessageLookupByLibrary.simpleMessage("Riwayat Obrolan"),
    "chatHotTip": MessageLookupByLibrary.simpleMessage(
      "Dia sudah menerima banyak hadiah ini. Ayo coba hadiah lainnya",
    ),
    "chatInTheRoom": MessageLookupByLibrary.simpleMessage("Ngobrol di ruangan"),
    "chatInputFucGuessFistOff": MessageLookupByLibrary.simpleMessage(
      "Mematikan Tebakan Tos",
    ),
    "chatInputFucGuessFistOn": MessageLookupByLibrary.simpleMessage(
      "Mengundang Tebakan Tos",
    ),
    "chatInputFucPhoto": MessageLookupByLibrary.simpleMessage("Foto"),
    "chatInputFucQA": MessageLookupByLibrary.simpleMessage(
      "Pertanyaan dan Jawaban",
    ),
    "chatIntimacyFeatureDes": MessageLookupByLibrary.simpleMessage(
      "Memberi hadiah dapat meningkatkan keintiman. Keintiman yang lebih tinggi akan membuka lebih banyak fitur interaktif",
    ),
    "chatIntimacyInfoFemale": MessageLookupByLibrary.simpleMessage(
      "Saat nilai keintiman diperoleh, nilai cahaya bintang juga akan diperoleh",
    ),
    "chatIntimacyUnlockMsgTip": m29,
    "chatIntimacyUnlockNum": m30,
    "chatIssue": MessageLookupByLibrary.simpleMessage("Masalah Obrolan"),
    "chatMode": MessageLookupByLibrary.simpleMessage("Mode obrolan"),
    "chatNoMoneyTaskTitle": MessageLookupByLibrary.simpleMessage(
      "Diamond telah digunakan hari ini. Kamu bisa mendapatkannya dengan cara-cara berikut.",
    ),
    "chatNow": MessageLookupByLibrary.simpleMessage("Obrol sekarang"),
    "chatParty": MessageLookupByLibrary.simpleMessage("Pesta Suara"),
    "chatPerIntimacyIncrease": m31,
    "chatReplayRewardMsgTip1": MessageLookupByLibrary.simpleMessage(
      "Balas pesan sekarang untuk mendapatkan",
    ),
    "chatReplayRewardMsgTip2": MessageLookupByLibrary.simpleMessage(
      "hadiah dari menerima hadiah.",
    ),
    "chatRoom": MessageLookupByLibrary.simpleMessage("Ruang obrolan"),
    "chatRoomInvite": MessageLookupByLibrary.simpleMessage(
      "Undang kamu ke pesta",
    ),
    "chatRoomUpgrade": MessageLookupByLibrary.simpleMessage(
      "Peningkatan Ruangan",
    ),
    "chatSettings": MessageLookupByLibrary.simpleMessage("Pengaturan Obrolan"),
    "chatShouldStayPrivate": MessageLookupByLibrary.simpleMessage(
      "Hei, ini tidak keren.\nobrolan harus tetap jadi rahasia",
    ),
    "chatUnlockedSuccess": MessageLookupByLibrary.simpleMessage(
      "Berhasil dibuka, mulai obrolan sekarang!",
    ),
    "chats": MessageLookupByLibrary.simpleMessage("obrolan"),
    "chattingInRoom": MessageLookupByLibrary.simpleMessage(
      "mengobrol di pesta suara sekarang.",
    ),
    "chattingNow": MessageLookupByLibrary.simpleMessage("Mengobrol sekarang"),
    "check": MessageLookupByLibrary.simpleMessage("Cek"),
    "checkAndWearBadge": MessageLookupByLibrary.simpleMessage(
      "Anda dapat memeriksa dan memakai medali di halaman \"Saya\"-\"Medali\"",
    ),
    "checkDetailInfo": MessageLookupByLibrary.simpleMessage(
      "Periksa info detail",
    ),
    "checkDetails": MessageLookupByLibrary.simpleMessage("Periksa detail"),
    "checkInBackpack": MessageLookupByLibrary.simpleMessage("Cek in ransel"),
    "checkInDetail": MessageLookupByLibrary.simpleMessage("Cek detail"),
    "checkInDetails": MessageLookupByLibrary.simpleMessage("cek detail! > "),
    "checkMewPrivilegeDetails": MessageLookupByLibrary.simpleMessage(
      "silakan periksa detail hak istimewa baru Anda.",
    ),
    "checkMyProposal": MessageLookupByLibrary.simpleMessage("Cek lamaran saya"),
    "checkNetWork": MessageLookupByLibrary.simpleMessage(
      "Harap periksa koneksi jaringan Anda dan coba lagi",
    ),
    "checkPrivilegeDetails": MessageLookupByLibrary.simpleMessage(
      "silakan periksa detail hak istimewa Anda!",
    ),
    "checkUpdate": MessageLookupByLibrary.simpleMessage("Cek pembaruan"),
    "checkingUpgrade": MessageLookupByLibrary.simpleMessage(
      "Sedang memeriksa versi terbaru, harap tunggu...",
    ),
    "cherishTheChance": MessageLookupByLibrary.simpleMessage(
      "Hargai sebuah kesempatan untuk memilih dengan hati-hati",
    ),
    "chief": MessageLookupByLibrary.simpleMessage("Kaisar"),
    "choose": MessageLookupByLibrary.simpleMessage("Pilih"),
    "chooseAnswerTip": MessageLookupByLibrary.simpleMessage(
      "Pilih jawaban yang paling sesuai dengan anda",
    ),
    "chooseAtLeastTwo": MessageLookupByLibrary.simpleMessage(
      "Pilih setidaknya dua orang",
    ),
    "chooseChatTopic": MessageLookupByLibrary.simpleMessage(
      "Pilih topik obrolan",
    ),
    "chooseRoomLabel": MessageLookupByLibrary.simpleMessage(
      "Pilih model ruang",
    ),
    "chooseWordGuideTip": MessageLookupByLibrary.simpleMessage(
      "Pilih kata-kata yang ingin Anda gambar, Anda juga dapat mengganti kata-katanya.",
    ),
    "chooseYourWords": MessageLookupByLibrary.simpleMessage(
      "Memilih kata-kata Anda",
    ),
    "choosingWords": MessageLookupByLibrary.simpleMessage("Memilih kata-kata"),
    "church": MessageLookupByLibrary.simpleMessage("Gereja"),
    "civilian": MessageLookupByLibrary.simpleMessage("Orang Sipil"),
    "civilianDefeat": MessageLookupByLibrary.simpleMessage("Orang sipil kalah"),
    "civilianVictory": MessageLookupByLibrary.simpleMessage(
      "Orang sipil menang",
    ),
    "claimAKey": MessageLookupByLibrary.simpleMessage("Minta sekunci"),
    "clashEmpire": MessageLookupByLibrary.simpleMessage("Perang Kekaisaran"),
    "cleanChat": MessageLookupByLibrary.simpleMessage("Bersihkan obrolan"),
    "cleanChatNoticeContent": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin menghapus obrolan?",
    ),
    "clear": MessageLookupByLibrary.simpleMessage("Bersihkan"),
    "clearCache": MessageLookupByLibrary.simpleMessage("Hapus Cache"),
    "clearChatHistory": MessageLookupByLibrary.simpleMessage(
      "Hapus riwayat Obrolan",
    ),
    "clearPoint": MessageLookupByLibrary.simpleMessage("Bersihkan poin"),
    "clearPointConfirm": MessageLookupByLibrary.simpleMessage(
      "Semua poin pemain akan dibersihkan. Tetap bersihkannya?",
    ),
    "clearSuccess": MessageLookupByLibrary.simpleMessage("cache dihapus"),
    "cleared": MessageLookupByLibrary.simpleMessage("Dibersihkan"),
    "clickBoom": MessageLookupByLibrary.simpleMessage("klik BOOM!!"),
    "clickChangeBottleAvatar": MessageLookupByLibrary.simpleMessage(
      "Klik ini untuk mengubah avatar dan nama panggilan.",
    ),
    "clickTheBoom": m32,
    "clickTheMicPosition": MessageLookupByLibrary.simpleMessage(
      "Klik posisi mic untuk mengobrol!",
    ),
    "clickToChangePayMethod": MessageLookupByLibrary.simpleMessage(
      "Klik di sini untuk mengubah metode pembayaran",
    ),
    "clickToGetTheFree": MessageLookupByLibrary.simpleMessage(
      "Kliknya untuk mendapatkan hadiah gratis.",
    ),
    "clickToGetTt": MessageLookupByLibrary.simpleMessage(
      "klik untuk mendapatkannya! >",
    ),
    "clickToGrabTheLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Klik untuk ambil lucky bag",
    ),
    "clickToPlay": MessageLookupByLibrary.simpleMessage("Klik untuk memutar"),
    "clickToRecord": MessageLookupByLibrary.simpleMessage("Klik untuk merekam"),
    "clickToRecordVoice": MessageLookupByLibrary.simpleMessage(
      "Klik untuk merekam suara anda",
    ),
    "clientError": MessageLookupByLibrary.simpleMessage("Kesalahan sisi klien"),
    "close": MessageLookupByLibrary.simpleMessage("Tutup"),
    "closeIn": MessageLookupByLibrary.simpleMessage("Tutup"),
    "closeMuteAll": m33,
    "closeSeconds": m34,
    "closeVideoRoomTips": MessageLookupByLibrary.simpleMessage(
      "Ini dapat menyebabkan ruang ditutup dan berhenti menonton video.",
    ),
    "closeWillNotShow": MessageLookupByLibrary.simpleMessage(
      "Tutup tidak akan menampikannya",
    ),
    "closed": MessageLookupByLibrary.simpleMessage("Ditutup"),
    "clubSelectContent": MessageLookupByLibrary.simpleMessage(
      "Di ruangan ini, kamu bukan bagian dari klub ruangan, kamu dapat menampilkan penggemar yang ada.",
    ),
    "clubSelectSecondTitle": MessageLookupByLibrary.simpleMessage(
      "Klub ruangan tempatmu bergabung",
    ),
    "clubTitleSelect": MessageLookupByLibrary.simpleMessage("Pilih nama klub"),
    "cocosGameLoadFailed": MessageLookupByLibrary.simpleMessage(
      "Kegagalan pemuatan game!",
    ),
    "coins": MessageLookupByLibrary.simpleMessage("Koin"),
    "coinsAndDay": m35,
    "coinsGift": MessageLookupByLibrary.simpleMessage("Hadiah Koin"),
    "collectionDetail": MessageLookupByLibrary.simpleMessage("Detail koleksi"),
    "collections": MessageLookupByLibrary.simpleMessage("Koleksi"),
    "coloredNameRoomScreen": MessageLookupByLibrary.simpleMessage(
      "Menarik perhatian semua orang dengan nama berwarna yang ditampilkan di layar kamar.",
    ),
    "combo": MessageLookupByLibrary.simpleMessage("Kombo"),
    "comboBtnTextBottom": MessageLookupByLibrary.simpleMessage("Kombo"),
    "comboBtnTextTop": MessageLookupByLibrary.simpleMessage("Menekan untuk"),
    "comments": MessageLookupByLibrary.simpleMessage("Komentar"),
    "communityRules": MessageLookupByLibrary.simpleMessage("Aturan komunitas"),
    "completeAccount": MessageLookupByLibrary.simpleMessage(
      "Lengkapi info akun anda untuk menarik lebih banyak teman satu frekuensi",
    ),
    "completePercent": m36,
    "completePercentInUserCard": m37,
    "completeProfile": MessageLookupByLibrary.simpleMessage("Profil lengkap"),
    "completeProfileBubble": MessageLookupByLibrary.simpleMessage(
      "Lengkapi info akun anda untuk menarik lebih banyak teman satu frekuensi",
    ),
    "completeProfileContentGiftChat": MessageLookupByLibrary.simpleMessage(
      "Buat orang lain membalas anda dengan lebih mudah dengan melengkapi profil anda!",
    ),
    "completeProfileContentMatch": MessageLookupByLibrary.simpleMessage(
      "Dapatkan lebih banyak prioritas dan kesempatan mengobrol dengan melengkapi profil anda.",
    ),
    "completeToFindFriends": MessageLookupByLibrary.simpleMessage(
      "Lengkapi semua info akun akan membantu Anda menemukan teman.",
    ),
    "completeYourProfile": MessageLookupByLibrary.simpleMessage(
      "Hampir selesai! Lengkapi profil anda untuk menemukan orang yang paling cocok untuk anda!",
    ),
    "completeYourProfileInChat": MessageLookupByLibrary.simpleMessage(
      "Lengkapi profil anda, dapatkan balasan dengan mudah!",
    ),
    "completedProfile": MessageLookupByLibrary.simpleMessage(
      "Anda telah menyelesaikan profil",
    ),
    "compulsoryDivorce": MessageLookupByLibrary.simpleMessage("Memaksa"),
    "compulsoryDivorceTip": m38,
    "condition": MessageLookupByLibrary.simpleMessage("Syarat"),
    "conditionForFamilyApplication": MessageLookupByLibrary.simpleMessage(
      "Persyaratan untuk permintaan family",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Konfirmasi"),
    "confirmDeleteSong": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi untuk menghapus lagu ini?",
    ),
    "confirmToClear": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi untuk menghapus semua riwayat Obrolan?",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("Konfirmasi"),
    "congNameToGift": MessageLookupByLibrary.simpleMessage(" telah menang"),
    "congOnWinning": MessageLookupByLibrary.simpleMessage(
      "Selamat atas perolehannya",
    ),
    "congrats": MessageLookupByLibrary.simpleMessage("Selamat!"),
    "congratsNoExclamation": MessageLookupByLibrary.simpleMessage("Selamat"),
    "congratsYouGot": MessageLookupByLibrary.simpleMessage(
      "Selamat! Anda telah dapat",
    ),
    "congratsYouHaveLightedUp": m39,
    "congratulations": MessageLookupByLibrary.simpleMessage("Selamat!"),
    "congratulationsOnBeingAppointedAsFamilynamesPosition": m40,
    "congratulationsOnJoiningTheFamilynameFamilyComeCheckItOut": m41,
    "congratulationsOnReceiving": MessageLookupByLibrary.simpleMessage(
      "Selamat mendapatkan",
    ),
    "congratulationsOnSuccessfullyCreatingTheFamilynameFamilyYouCanNow": m42,
    "congratulationsToFamilynameFamilyForUpgradingToLevellevelComeCheck": m43,
    "congratulationsToGotten": m44,
    "connectedWifiTips": MessageLookupByLibrary.simpleMessage(
      "Pastikan Ponsel dan PC terhubung ke Wi-Fi yang sama, lalu buka browser PC dan masukkan tautan berikut ini.",
    ),
    "connecting": MessageLookupByLibrary.simpleMessage(
      "Sedang menghubungkan...",
    ),
    "consensualDivorce": MessageLookupByLibrary.simpleMessage("Konsensual"),
    "consensualDivorceTip": MessageLookupByLibrary.simpleMessage(
      "Ketika pihak lain setuju, cincinnya akan hilang dan Anda akan menjadi lajang. Apakah Anda yakin ingin mengajukan permintaan cerai?",
    ),
    "consumptionReminder": MessageLookupByLibrary.simpleMessage(
      "Pengingat konsumsi",
    ),
    "consumptionReminderContent": m45,
    "contactCS": m46,
    "contactUs": MessageLookupByLibrary.simpleMessage("Hubungi kami"),
    "contacts": MessageLookupByLibrary.simpleMessage("Kontak"),
    "containInvaildCharacters": MessageLookupByLibrary.simpleMessage(
      "Mungkin mengandung karakter khusus seperti “ \' / <>. Silakan coba yang lain...",
    ),
    "containSensitive": MessageLookupByLibrary.simpleMessage(
      "Berisi kata-kata sensitif",
    ),
    "contentCannotEmpty": MessageLookupByLibrary.simpleMessage(
      "Konten tidak boleh kosong",
    ),
    "contentOff": m47,
    "contentVoiceVerifyGuide": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara anda untuk memastikan bahwa anda adalah orang yang nyata.\nLolos validasi untuk prioritas pencocokan.",
    ),
    "continueMatch": MessageLookupByLibrary.simpleMessage(
      "Teruskan pencocokan",
    ),
    "continueText": MessageLookupByLibrary.simpleMessage("Lanjutkan"),
    "continueToDelete": MessageLookupByLibrary.simpleMessage(
      "Lanjut untuk menghapus",
    ),
    "contribute": MessageLookupByLibrary.simpleMessage("Kontribusi"),
    "contribution": MessageLookupByLibrary.simpleMessage("Kontribusi"),
    "convertStarlight": m48,
    "coolingOff": m49,
    "copy": MessageLookupByLibrary.simpleMessage("Salin"),
    "copyInvitationCode": MessageLookupByLibrary.simpleMessage(
      "Salin Kode Undangan",
    ),
    "copyInviteCode": m50,
    "copyNumber": MessageLookupByLibrary.simpleMessage("Salin Nomor"),
    "copySuccess": MessageLookupByLibrary.simpleMessage("Berhasil disalin"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "Hak Cipta © 2023 Hak cipta dilindungi undang-undang",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Betul"),
    "cost": MessageLookupByLibrary.simpleMessage("Biaya:"),
    "count": MessageLookupByLibrary.simpleMessage("Menteri"),
    "countAndCurrency": m51,
    "countDetail": MessageLookupByLibrary.simpleMessage("Hitung detailnya"),
    "countDownTimeTips": MessageLookupByLibrary.simpleMessage(
      "Waktu pencocokan anda telah habis. Waktu pencocokan akan dipulihkan dengan hitungan mundur.",
    ),
    "countdown": MessageLookupByLibrary.simpleMessage("Hitung mundur"),
    "countriesRegions": MessageLookupByLibrary.simpleMessage("Negara/Wilayah"),
    "country": MessageLookupByLibrary.simpleMessage("Negara"),
    "coupleCenter": MessageLookupByLibrary.simpleMessage("Pasangan"),
    "coupleList": MessageLookupByLibrary.simpleMessage("Daftar pasangan"),
    "coupon": MessageLookupByLibrary.simpleMessage("Kupon"),
    "cpAnnounceTips": MessageLookupByLibrary.simpleMessage(
      "Host akan mengumumkan pilihan dari semua orang, harap tunggu dengan sabar.",
    ),
    "cpNewRound": m52,
    "cpRoomOpen": m53,
    "cpTask": MessageLookupByLibrary.simpleMessage("Tugas pasangan"),
    "cpTaskDailyTime": MessageLookupByLibrary.simpleMessage(
      "Reset harian pada 0:00 (GMT+3)",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Buat"),
    "createByNumDiamonds": m54,
    "createConversationBySendGift": MessageLookupByLibrary.simpleMessage(
      "Buat percakapan dengan Anda dengan mengirimkan hadiah",
    ),
    "createEvent": MessageLookupByLibrary.simpleMessage("Buat acara"),
    "createEventForYourRoom": MessageLookupByLibrary.simpleMessage(
      "Buat acara khusus untuk kamar Anda",
    ),
    "createFamily": MessageLookupByLibrary.simpleMessage("Buat family"),
    "createFamilyFailed": MessageLookupByLibrary.simpleMessage(
      "gagal dibuat, silakan hubungi layanan pelanggan.",
    ),
    "createFamilySuccessfully": MessageLookupByLibrary.simpleMessage(
      "Anda telah berhasil mengirimkan informasi family Anda. Mohon tunggu untuk ditinjau",
    ),
    "createMyEvent": MessageLookupByLibrary.simpleMessage("Buat acara saya"),
    "createMyRoom": MessageLookupByLibrary.simpleMessage("Buat ruangan saya"),
    "createMyRoomUpCase": MessageLookupByLibrary.simpleMessage(
      "Buat Ruangan Saya",
    ),
    "createRoom": MessageLookupByLibrary.simpleMessage("Buat ruangan"),
    "createRoomGuideFirst": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengubah nama dan cover ruangan anda kapan saja, pilih tag favorit anda!",
    ),
    "createRoomGuideSecond": MessageLookupByLibrary.simpleMessage(
      "Undang teman anda untuk bergabung di pesta!",
    ),
    "createWithAmount": m55,
    "csaeOrCsam": MessageLookupByLibrary.simpleMessage("CSAE/CSAM"),
    "csaeOrCsamDetail": MessageLookupByLibrary.simpleMessage(
      "CSAE/CSAM: Pelecehan dan Eksploitasi Seksual Anak/Materi Pelecehan Seksual Anak",
    ),
    "currentIntimacy": MessageLookupByLibrary.simpleMessage(
      "Keintiman Saat Ini",
    ),
    "currentTitle": MessageLookupByLibrary.simpleMessage(
      "Titel yang dipakai saat ini",
    ),
    "currentVersion": MessageLookupByLibrary.simpleMessage("Versi terkini:"),
    "currentlyRing": MessageLookupByLibrary.simpleMessage("Cincin saat ini"),
    "currentlyWornBadges": MessageLookupByLibrary.simpleMessage(
      "Medali yang dipakai saat ini",
    ),
    "customBackgroundCard": MessageLookupByLibrary.simpleMessage(
      "kartu custom latar belakang",
    ),
    "customTurntableFaq": MessageLookupByLibrary.simpleMessage(
      "1. Hanya pemilik dan admin kamar yang dapat mengedit, memperbarui konten, membuka dan menutup meja putar.\n\n2. Pemilik dan admin dapat memutar meja putar. Mereka juga dapat menetapkan anggota di mikrofon untuk berpartisipasi dalam meja putar.\n\n3. Setelah meja putar dihidupkan, peserta yang dapat memutar meja putar untuk ronde  ini tidak akan berubah sampai dimatikan\n\n4. Saat meja putar berputar, tidak ada yang bisa memutarnya sampai berhenti.",
    ),
    "customizedThemeNameExist": MessageLookupByLibrary.simpleMessage(
      "Tema telah diperbarui",
    ),
    "customizedThemeSubtitlePlaceholder": MessageLookupByLibrary.simpleMessage(
      "Perkenalkan deskripsi tema baru anda",
    ),
    "customizedThemeTitlePlaceholder": MessageLookupByLibrary.simpleMessage(
      "Perkenalkan tema baru anda",
    ),
    "customizedThemeToastTitle": MessageLookupByLibrary.simpleMessage(
      "Titel sudah terisi",
    ),
    "customizedThemes": MessageLookupByLibrary.simpleMessage("Tema Custom"),
    "daily": MessageLookupByLibrary.simpleMessage(" Harian"),
    "dailyList": MessageLookupByLibrary.simpleMessage("Daftar Harian"),
    "dailyRewards": MessageLookupByLibrary.simpleMessage("Hadiah harian"),
    "dailyTasks": MessageLookupByLibrary.simpleMessage("Misi harian"),
    "dare": MessageLookupByLibrary.simpleMessage("Tantangan"),
    "date": MessageLookupByLibrary.simpleMessage("Tanggal"),
    "day": MessageLookupByLibrary.simpleMessage("Hari"),
    "dayLowCase": MessageLookupByLibrary.simpleMessage("hari"),
    "days": MessageLookupByLibrary.simpleMessage("hari"),
    "daysTimeout": m56,
    "dearFemale": MessageLookupByLibrary.simpleMessage("Sayang"),
    "dearMale": MessageLookupByLibrary.simpleMessage("Sayang"),
    "declined": MessageLookupByLibrary.simpleMessage("Ditolak"),
    "decorations": MessageLookupByLibrary.simpleMessage("Hiasan"),
    "decreaseContent": m57,
    "deeplinkPhotoToAvatar": MessageLookupByLibrary.simpleMessage(
      "Foto untuk avatar di Winker",
    ),
    "defaultError": MessageLookupByLibrary.simpleMessage(
      "Server kami sedang sibuk sekarang, silakan coba la",
    ),
    "defaultMatchError": MessageLookupByLibrary.simpleMessage(
      "Pasangan anda belum ditemukan, tapi ini bukanlah akhir. Coba lagi nanti!",
    ),
    "defaultRoomAnnounce": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di ruanganku, tempat kamu dapat berbicara dengan bebas！",
    ),
    "defaultRoomName": m58,
    "defeat": MessageLookupByLibrary.simpleMessage("Kalahkan"),
    "defenseBuff": m59,
    "delete": MessageLookupByLibrary.simpleMessage("Hapus"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Hapus akun"),
    "deleteBottleConfirm": MessageLookupByLibrary.simpleMessage(
      "Setelah Anda menghapus obrolan ini, pihak terkait tidak akan lagi menerima pesan Anda.",
    ),
    "deleteCollectionConfirm": MessageLookupByLibrary.simpleMessage(
      "stiker tidak dapat dipulihkan setelah dihapus. Hapus sekarang?",
    ),
    "deleteCommentSure": MessageLookupByLibrary.simpleMessage(
      "Komentar yang dihapus tidak dapat dipulihkan",
    ),
    "deleteHistory": MessageLookupByLibrary.simpleMessage("Hapus riwayat？"),
    "deleteNum": m60,
    "deleteSureContent": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin untuk menghapus?",
    ),
    "deleteUser": MessageLookupByLibrary.simpleMessage("Hapus Pengguna"),
    "deleted": MessageLookupByLibrary.simpleMessage("Dihapus"),
    "desYour": MessageLookupByLibrary.simpleMessage("Deskripsikan dirimu"),
    "describeYourWords": MessageLookupByLibrary.simpleMessage(
      "Deskripsikan kata-kata",
    ),
    "describeYourself": MessageLookupByLibrary.simpleMessage(
      "Deskripsikan diri sendiri",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Deskripsi"),
    "destructHint": MessageLookupByLibrary.simpleMessage(
      "Dengan memilih ini, foto atau video hanya dapat dilihat satu kali, dan akan otomatis terhapus sendiri setelahnya.",
    ),
    "destructMsgHint": MessageLookupByLibrary.simpleMessage(
      "Tekan lama layar\nHanya dapat diperiksa sekali",
    ),
    "detail": MessageLookupByLibrary.simpleMessage("Rincian"),
    "details": MessageLookupByLibrary.simpleMessage("Detail"),
    "detailsForCouple": MessageLookupByLibrary.simpleMessage("Detail pasangan"),
    "diamond": MessageLookupByLibrary.simpleMessage("Berlian"),
    "diamondBalance": MessageLookupByLibrary.simpleMessage("Saldo Diamond"),
    "diamondCanBroadcast": MessageLookupByLibrary.simpleMessage(
      "Hanya nilai > 1000 berlian yang dapat disiarkan ke semua kamar",
    ),
    "diamondGifts": MessageLookupByLibrary.simpleMessage("Hadiah Berlian"),
    "diamondQuantity": MessageLookupByLibrary.simpleMessage(
      "Kuntitas berlian:",
    ),
    "diamondRank": MessageLookupByLibrary.simpleMessage("Peringkat diamond"),
    "diamondRankExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat didasarkan pada nilai diamond dari hadiah yang anda kirim di ruangan ini.",
    ),
    "diamondRecharger": MessageLookupByLibrary.simpleMessage(
      "Isi ulang Diamond",
    ),
    "diamondRewardNum": m61,
    "diamondTask": MessageLookupByLibrary.simpleMessage("Tugas Diamond"),
    "diamonds": MessageLookupByLibrary.simpleMessage("Diamond"),
    "diamondsBalance": MessageLookupByLibrary.simpleMessage("Saldo Diamond"),
    "diamondsForRenewal": m62,
    "diamondsGift": MessageLookupByLibrary.simpleMessage("Hadiah diamond"),
    "diamondsLowCase": MessageLookupByLibrary.simpleMessage("berlian"),
    "digitalArt": MessageLookupByLibrary.simpleMessage("Seni Digital"),
    "disbandFamily": MessageLookupByLibrary.simpleMessage("Bubarkan family"),
    "disbandTheFamily": MessageLookupByLibrary.simpleMessage("Bubarkan Family"),
    "disbandingTheFamily": MessageLookupByLibrary.simpleMessage(
      "Bubarkan Family",
    ),
    "discardNow": MessageLookupByLibrary.simpleMessage("Buang sekarang?"),
    "discardPost": MessageLookupByLibrary.simpleMessage("Hapus"),
    "discardPublishEdit": MessageLookupByLibrary.simpleMessage(
      "Jika anda meninggalkan halaman ini, anda akan kehilangan Unggahan ini",
    ),
    "discardSelected": MessageLookupByLibrary.simpleMessage(
      "Buang yang dipilih",
    ),
    "discountDay": MessageLookupByLibrary.simpleMessage("Hari diskon toko"),
    "discountDayDesc": MessageLookupByLibrary.simpleMessage(
      "Setiap hari Minggu Anda dapat membeli barang-barang di toko dengan diskon khusus!",
    ),
    "discountOff": m63,
    "discoverMore": MessageLookupByLibrary.simpleMessage(
      "Temukan lebih banyak",
    ),
    "distanceTag": m64,
    "distanceToTheTopOne": MessageLookupByLibrary.simpleMessage(
      "Jarak ke Top 1",
    ),
    "distant": MessageLookupByLibrary.simpleMessage("Jarak"),
    "divorce": MessageLookupByLibrary.simpleMessage("Cerai"),
    "divorceCoolingPeriod": MessageLookupByLibrary.simpleMessage(
      "Anda masih dalam masa tenang untuk perceraian, harap tunggu 3 hari sebelum melamar",
    ),
    "divorcePetitionWait": MessageLookupByLibrary.simpleMessage(
      "Pengajuan cerai sudah terkirim ke pasangan Anda, mohon tunggu diprosesi oleh pihak lain.",
    ),
    "doNotBeShy": MessageLookupByLibrary.simpleMessage("Jangan malu!"),
    "doNotRemindMeNextTime": MessageLookupByLibrary.simpleMessage(
      "Jangan ingatkan saya lain kali",
    ),
    "doYouWantToKickThisUserOutOfThe": MessageLookupByLibrary.simpleMessage(
      "Ingin mengeluarkan pengguna ini dari game?",
    ),
    "doYouWantToSpend": MessageLookupByLibrary.simpleMessage(
      "Apakah kamu ingin menghabiskan?",
    ),
    "doYouWantToTransferUserAsTheGameCaptain": m65,
    "doYouWantToUseTheCustomBackgroundCardsDirectly":
        MessageLookupByLibrary.simpleMessage(
          "Apakah Anda ingin menggunakan kartu latar belakang custom secara langsung?",
        ),
    "domino": MessageLookupByLibrary.simpleMessage("Domino"),
    "dominoes": MessageLookupByLibrary.simpleMessage("Domino"),
    "done": MessageLookupByLibrary.simpleMessage("Selesai"),
    "doneNum": m66,
    "dontMind": MessageLookupByLibrary.simpleMessage(
      "Jika Anda tidak keberatan, ",
    ),
    "dontMiss": MessageLookupByLibrary.simpleMessage(
      "Jangan lewatkan satu pesan pun!",
    ),
    "dontRemindMeToday": MessageLookupByLibrary.simpleMessage(
      "Jangan ingatkan aku hari ini",
    ),
    "dontReminder": MessageLookupByLibrary.simpleMessage("Jangan ingatkan"),
    "doubleConfirmation": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi ganda",
    ),
    "doubleConfirmationDes": MessageLookupByLibrary.simpleMessage(
      "Apakah kamu yakin ingin melakukan ini?",
    ),
    "downMic": MessageLookupByLibrary.simpleMessage("Turun dari mic"),
    "download": MessageLookupByLibrary.simpleMessage("unduh"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage("Berhasil Diunduh"),
    "downloadingToLater": MessageLookupByLibrary.simpleMessage(
      "Gagal mengunduh, silahkan coba lagi",
    ),
    "draw": MessageLookupByLibrary.simpleMessage("Gambar"),
    "drawFanNumReward": m67,
    "drawForFree": MessageLookupByLibrary.simpleMessage("Undian\ngratis"),
    "drawGuess": MessageLookupByLibrary.simpleMessage("Gambar&Tebak"),
    "drawGuessFaq": MessageLookupByLibrary.simpleMessage(
      "1. Diperlukan minimal 2 pemain untuk memulai game ini.\n2. Setiap pemain bergiliran menjadi pelukis, menggambar kata yang dipilih sementara pemain lain menebak.\n3. Pelukis akan memberi skor sesuai dengan jumlah orang yang menebak dengan benar.\n4. Penerbak mendapatkan poin yang berbeda sesuai dengan urutan jawaban yang benar.\n5. Terakhir, pemain diberi peringkat berdasarkan skor final yang mereka peroleh.",
    ),
    "drawRewards": MessageLookupByLibrary.simpleMessage(
      "Mengundi untuk memenangkan hadiah besar",
    ),
    "drawing": MessageLookupByLibrary.simpleMessage("Mengundi"),
    "duration": MessageLookupByLibrary.simpleMessage("Durasi:"),
    "durationWith": m68,
    "eachPersonCanVoteOnce": MessageLookupByLibrary.simpleMessage(
      "Setiap orang hanya dapat bersuara satu kali",
    ),
    "earnGold": MessageLookupByLibrary.simpleMessage("Hasilkan koin emas >"),
    "earnGolds": MessageLookupByLibrary.simpleMessage("Hasilkan koin"),
    "earphone": MessageLookupByLibrary.simpleMessage("Earphone"),
    "earphoneFeedback": MessageLookupByLibrary.simpleMessage(
      "Umpan balik earphone",
    ),
    "echo": MessageLookupByLibrary.simpleMessage("Gema"),
    "edit": MessageLookupByLibrary.simpleMessage("Sunting"),
    "editAnniversary": MessageLookupByLibrary.simpleMessage("Edit Hari Jadi"),
    "editAvatar": MessageLookupByLibrary.simpleMessage("Edit Avatar"),
    "editFamilyName": MessageLookupByLibrary.simpleMessage("Edit nama family"),
    "editFamilySlogan": MessageLookupByLibrary.simpleMessage(
      "Edit slogan family",
    ),
    "editInformation": MessageLookupByLibrary.simpleMessage("Edit informasi"),
    "editName": MessageLookupByLibrary.simpleMessage("Edit nama"),
    "editNickNameAlert": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengedit nama panggilan setiap 24 jam, yakin ingin mengubahnya?",
    ),
    "editPhoto": MessageLookupByLibrary.simpleMessage("Edit foto"),
    "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profil"),
    "editProfileLivingDes": MessageLookupByLibrary.simpleMessage(
      "Sistem akan merekomendasikan pengguna terdekat untukmu.",
    ),
    "editProfileLivingTitle": MessageLookupByLibrary.simpleMessage(
      "Pilih daerah tempatmu tinggal",
    ),
    "editQuestions": MessageLookupByLibrary.simpleMessage("Edit pertanyaan"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage("Berhasil diedit"),
    "editTheme": MessageLookupByLibrary.simpleMessage("Edit Tema"),
    "editWithoutPermission": MessageLookupByLibrary.simpleMessage(
      "Mengedit tanpa izin",
    ),
    "effectPreview": MessageLookupByLibrary.simpleMessage("Pratinjau Efek"),
    "elders": MessageLookupByLibrary.simpleMessage("Sesepuh"),
    "email": MessageLookupByLibrary.simpleMessage("email"),
    "empirePropBuff": MessageLookupByLibrary.simpleMessage(
      "BUFF: Meningkatkan Kekuatan",
    ),
    "empirePropCamel": MessageLookupByLibrary.simpleMessage(
      "Memanggil Unta Tempur",
    ),
    "empirePropDragon": MessageLookupByLibrary.simpleMessage(
      "Memanggil Naga Terkuat",
    ),
    "empty": MessageLookupByLibrary.simpleMessage("Kosong"),
    "emptyComments": MessageLookupByLibrary.simpleMessage("Belum ada komentar"),
    "emptyIntimacy": MessageLookupByLibrary.simpleMessage(
      "Ups, Anda tidak memiliki belahan jiwa.\nHanya keintiman lebih dari 5000 yang dapat melamar",
    ),
    "emptyList": MessageLookupByLibrary.simpleMessage("Kosongkan daftar"),
    "emptyMoment": MessageLookupByLibrary.simpleMessage(
      "Masih belum ada postingan.",
    ),
    "emptyNotice": MessageLookupByLibrary.simpleMessage("Belum ada notifikasi"),
    "emptyRing": MessageLookupByLibrary.simpleMessage(
      "Beli cincin untuk melamar, tunjukkan hatimu!",
    ),
    "emptyTheList": MessageLookupByLibrary.simpleMessage("Kosongkan daftar"),
    "enable": MessageLookupByLibrary.simpleMessage("Aktifkan"),
    "enableCameraAccess": MessageLookupByLibrary.simpleMessage(
      "Buka \"Pengaturan\"> \"Winker\" dan aktifkan akses kamera.",
    ),
    "enableForMoment": MessageLookupByLibrary.simpleMessage(
      "Aktifkan notifikasi untuk menerima momen dan komentar yang diperbarui",
    ),
    "enableForMsgFemale": MessageLookupByLibrary.simpleMessage(
      "Aktifkan notifikasi untuk menerima pesannya",
    ),
    "enableForMsgMale": MessageLookupByLibrary.simpleMessage(
      "Aktifkan notifikasi untuk menerima pesannya",
    ),
    "enableMicAccess": MessageLookupByLibrary.simpleMessage(
      "Buka \"Pengaturan\"> \"winker\" dan aktifkan akses mic.",
    ),
    "enablePhotoAccess": MessageLookupByLibrary.simpleMessage(
      "Harap izinkan winker untuk mengakses foto perangkat anda di \"Pengaturan> Privasi> Foto",
    ),
    "enableStorageAccess": MessageLookupByLibrary.simpleMessage(
      "Buka \"Pengaturan\" > \"winker\" dan aktifkan akses penyimpanan.",
    ),
    "enabled": MessageLookupByLibrary.simpleMessage("Diaktifkan"),
    "end": MessageLookupByLibrary.simpleMessage("Akhir"),
    "endDateAndTime": MessageLookupByLibrary.simpleMessage(
      "Tanggal dan waktu berakhir",
    ),
    "endGame": MessageLookupByLibrary.simpleMessage("Akhiri permainan"),
    "endMode": MessageLookupByLibrary.simpleMessage("Mode berakhir"),
    "ended": MessageLookupByLibrary.simpleMessage("Berakhir"),
    "energy": MessageLookupByLibrary.simpleMessage("Energi"),
    "ensuresSafety": MessageLookupByLibrary.simpleMessage(
      "Winker memastikan bahwa semua pengguna berada di lingkungan yang aman dan otentik.",
    ),
    "enter": MessageLookupByLibrary.simpleMessage("Masuk"),
    "enterEffect": MessageLookupByLibrary.simpleMessage("Efek masuk"),
    "enterEffectTitle": MessageLookupByLibrary.simpleMessage(
      "Masukkan gelar efek",
    ),
    "enterEffectTitleDesc": MessageLookupByLibrary.simpleMessage(
      "Menarik perhatian semua orang dengan pesan masuk yang menarik",
    ),
    "enterEventDescription": MessageLookupByLibrary.simpleMessage(
      "Isi deskripsi event",
    ),
    "enterFamily": MessageLookupByLibrary.simpleMessage("Masuki family"),
    "enterMessage": MessageLookupByLibrary.simpleMessage("Masukkan pesan"),
    "enterNumLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Silakan masukkan jumlah lucky bag",
    ),
    "enterPermission": MessageLookupByLibrary.simpleMessage("Izin masuk"),
    "enterSmsCodeSentTo": MessageLookupByLibrary.simpleMessage(
      "Kirimkan kode verifikasi ke nomor berikut:",
    ),
    "enterTheNumberOfLuckyBags": MessageLookupByLibrary.simpleMessage(
      "Isi jumlah lucky bag",
    ),
    "enterThePassword": MessageLookupByLibrary.simpleMessage(
      "Masukkan Kata Sandi",
    ),
    "enterTheRoom": MessageLookupByLibrary.simpleMessage("Masuki kamar ini"),
    "enterTheTotalDiamondAmountForTheLuckyBag":
        MessageLookupByLibrary.simpleMessage(
          "Masukkan total berlian lucky bag",
        ),
    "enteredDays": m69,
    "enteredTheRoom": MessageLookupByLibrary.simpleMessage("Masuk ruangan ini"),
    "entranceMessage": MessageLookupByLibrary.simpleMessage("Efek Masuk"),
    "entryFee": MessageLookupByLibrary.simpleMessage("Biaya Masuk:"),
    "entryFees": MessageLookupByLibrary.simpleMessage("Biaya masuk:"),
    "errorOccurred": m70,
    "estimatedRefund": MessageLookupByLibrary.simpleMessage(
      "Estimasi pengembalian dana: ",
    ),
    "evaluateContent": MessageLookupByLibrary.simpleMessage(
      "Jika anda menyukai winker, tolong tinggalkan bintang 5, terima kasih~",
    ),
    "evenDetailMedal": MessageLookupByLibrary.simpleMessage("Medal"),
    "evenDetailRoomId": MessageLookupByLibrary.simpleMessage("Room ID"),
    "evenDetailSubject": MessageLookupByLibrary.simpleMessage("Tema"),
    "evenDetailSubscribers": m71,
    "evenDetailTime": MessageLookupByLibrary.simpleMessage("Waktu"),
    "evenShareSubscribers": m72,
    "event": MessageLookupByLibrary.simpleMessage("Acara"),
    "eventBannerLimit": MessageLookupByLibrary.simpleMessage(
      "Unggah banner dengan ukuran 300×600 dan maksimal 5MB",
    ),
    "eventCreateNoRoom": MessageLookupByLibrary.simpleMessage(
      "Anda perlu membuat ruang suara Anda sendiri sebelum Anda membuat event ruang",
    ),
    "eventCreateToast": MessageLookupByLibrary.simpleMessage(
      "Anda belum mengisi semua informasi",
    ),
    "eventCreatedShare": MessageLookupByLibrary.simpleMessage(
      "Anda telah berhasil membuat event ruang Anda. Bagikan ke Moment sekarang!",
    ),
    "eventDescTips": MessageLookupByLibrary.simpleMessage(
      "Informasi selengkapnya agar tamu mengetahui detail acara Anda.",
    ),
    "eventDescription": MessageLookupByLibrary.simpleMessage("Deskripsi event"),
    "eventDetails": MessageLookupByLibrary.simpleMessage("Detail acara"),
    "eventGiftRankTips": MessageLookupByLibrary.simpleMessage(
      "Hadiah berlian dan koin yang dikirim selama event akan mendapatkan poin.",
    ),
    "eventInReview": MessageLookupByLibrary.simpleMessage(
      "Acara sedang ditinjau",
    ),
    "eventMineEmpty": MessageLookupByLibrary.simpleMessage(
      "Anda belum membuat event ruang~",
    ),
    "eventName": MessageLookupByLibrary.simpleMessage("Nama event"),
    "eventRoom": MessageLookupByLibrary.simpleMessage("Ruang Event"),
    "eventRoomIDType": m73,
    "eventRule1": MessageLookupByLibrary.simpleMessage(
      "1. Pengguna harus membuat ruang suara sendiri terlebih dahulu sebelum dapat membuat event ruang",
    ),
    "eventRule2": MessageLookupByLibrary.simpleMessage(
      "2. Pengguna dapat membuat event ruang untuk jadwal hingga 7 hari ke depan",
    ),
    "eventRule3": MessageLookupByLibrary.simpleMessage(
      "3. Saat membuat event ruang, informasi event harus melalui proses verifikasi. Selama proses ini, pengguna masih dapat mengubah informasi event. Setelah disetujui, hanya waktu event yang dapat diubah. Setiap event hanya bisa mengubah informasi 1 kali.",
    ),
    "eventRuleTitle": MessageLookupByLibrary.simpleMessage("Aturan Event"),
    "eventSelectPeriod": MessageLookupByLibrary.simpleMessage(
      "Pilih period...",
    ),
    "eventShareHost": MessageLookupByLibrary.simpleMessage("Host:"),
    "eventShareTitle": MessageLookupByLibrary.simpleMessage(
      "Subscribe dan ikutan pesta bareng kami",
    ),
    "eventShareToMoment": MessageLookupByLibrary.simpleMessage(
      "Bagikan ke Momen",
    ),
    "eventSubject": m74,
    "eventSubjectPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Isi tema event...",
    ),
    "eventTag": MessageLookupByLibrary.simpleMessage("Tag acara"),
    "eventTime": MessageLookupByLibrary.simpleMessage("Waktu acara"),
    "eventUploadBanner": MessageLookupByLibrary.simpleMessage("Unggah Banner"),
    "eventWithEmoji": MessageLookupByLibrary.simpleMessage("🎉 Acara"),
    "events": MessageLookupByLibrary.simpleMessage("Acara"),
    "everyone": MessageLookupByLibrary.simpleMessage("Setiap orang"),
    "exchange": MessageLookupByLibrary.simpleMessage("Tukar"),
    "exchangeAll": MessageLookupByLibrary.simpleMessage("Tukarkan semua"),
    "exchangeDiamonds": MessageLookupByLibrary.simpleMessage(
      "Tukarkan Diamond",
    ),
    "exchangeGemToDiamond": MessageLookupByLibrary.simpleMessage(
      "Tukarkan rubi ke berlian",
    ),
    "exchangeHint": MessageLookupByLibrary.simpleMessage("Jumlah Diamond"),
    "exchangeKeysToUnlockBox": MessageLookupByLibrary.simpleMessage(
      "Tukarkan kunci untuk membuka kotak bonus",
    ),
    "exchangeNumDiamond": m75,
    "exchangeStarlight": MessageLookupByLibrary.simpleMessage(
      "Tukar dengan Starlight",
    ),
    "exchangeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Penukaran berhasil",
    ),
    "exchangeToDiamond": MessageLookupByLibrary.simpleMessage(
      "Tukarkan ke Diamond",
    ),
    "exclusivePremiumAvatar": MessageLookupByLibrary.simpleMessage(
      "Bingkai avatar Premium Eksklusif",
    ),
    "exclusivePremiumChat": MessageLookupByLibrary.simpleMessage(
      "Bingkai obrolan Premium Eksklusif",
    ),
    "exclusivePremiumChatDesc": MessageLookupByLibrary.simpleMessage(
      "Tampilkan pesan Anda ke teman Anda dengan bingkai cantik",
    ),
    "exclusivePrivileges": MessageLookupByLibrary.simpleMessage(
      "Hak Istimewa Eksklusif",
    ),
    "exit": MessageLookupByLibrary.simpleMessage("Keluar"),
    "exitFamily": MessageLookupByLibrary.simpleMessage("Keluar dari family"),
    "exitFansGroupTipsConfirm": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi",
    ),
    "exitFansGroupTipsContent": MessageLookupByLibrary.simpleMessage(
      "Setelah keluar, akumulasi poin popularitas dan hak istimewa akan diatur ulang ke nol, Apa kamu yakin ingin melakukan operasi ini?",
    ),
    "exitFansGroupTipsThinkAgain": MessageLookupByLibrary.simpleMessage(
      "Pikirkan lagi",
    ),
    "exitFansGroupTitle": MessageLookupByLibrary.simpleMessage(
      "Pengingat Keluar",
    ),
    "exitNoSaveInfo": MessageLookupByLibrary.simpleMessage(
      "Informasi yang ada tidak akan disimpan jika keluar",
    ),
    "exitRecordingTips": MessageLookupByLibrary.simpleMessage(
      "Buang sekarang? Catatan audio anda tidak disimpan.",
    ),
    "exitRoomContent": MessageLookupByLibrary.simpleMessage(
      "Ini akan menyebabkan ruangan ditutup.",
    ),
    "exitRoomTitle": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin keluar dari ruangan ini?",
    ),
    "exitRoomWillCauseGameFailed": MessageLookupByLibrary.simpleMessage(
      "Keluar dari ruangan akan menyebabkan game gagal",
    ),
    "exitRoomWillLeaveGame": MessageLookupByLibrary.simpleMessage(
      "Keluar dari ruangan akan meninggalkan game",
    ),
    "exitTheRoom": MessageLookupByLibrary.simpleMessage("Keluar dari ruangan"),
    "exitWillDelete": MessageLookupByLibrary.simpleMessage(
      "Keluar akan menghapus catatan yang Anda pilih",
    ),
    "exp": m76,
    "expNum": m77,
    "expStr": MessageLookupByLibrary.simpleMessage("Exp"),
    "expText": MessageLookupByLibrary.simpleMessage("Exp"),
    "expireTime": m78,
    "expired": MessageLookupByLibrary.simpleMessage("kedaluwarsa"),
    "expiredCapital": MessageLookupByLibrary.simpleMessage("Kedaluwarsa"),
    "expiredDate": m79,
    "explore": MessageLookupByLibrary.simpleMessage("Jelajahi"),
    "extraContent": m80,
    "faceNotDetected": MessageLookupByLibrary.simpleMessage(
      "Wajah tidak terdeteksi. Coba lagi.",
    ),
    "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
    "failed": MessageLookupByLibrary.simpleMessage("Gagal"),
    "failedToSend": MessageLookupByLibrary.simpleMessage("Gagal mengirim"),
    "failedToSubmit": MessageLookupByLibrary.simpleMessage(
      "Gagal mengirim, silakan coba lagi",
    ),
    "failureMatching": MessageLookupByLibrary.simpleMessage(
      "Kegagalan Pencocokan",
    ),
    "fakeMsgFemale": MessageLookupByLibrary.simpleMessage("Hai"),
    "fakeMsgMale": MessageLookupByLibrary.simpleMessage("Hai"),
    "fakeUserFemale": MessageLookupByLibrary.simpleMessage("Nancy Ramy"),
    "fakeUserMale": MessageLookupByLibrary.simpleMessage("Taim Anas"),
    "family": MessageLookupByLibrary.simpleMessage("family"),
    "familyAnnouncement": MessageLookupByLibrary.simpleMessage(
      "Pengumuman family",
    ),
    "familyCapital": MessageLookupByLibrary.simpleMessage("Family"),
    "familyCover": MessageLookupByLibrary.simpleMessage("Cover family"),
    "familyCoverEditAfterDays": m81,
    "familyCreateSuccess": MessageLookupByLibrary.simpleMessage(
      "Family Anda berhasil dibuat!",
    ),
    "familyData": MessageLookupByLibrary.simpleMessage("Data family"),
    "familyDisbanded": MessageLookupByLibrary.simpleMessage(
      "Family Anda telah dibubarkan.",
    ),
    "familyGroup": MessageLookupByLibrary.simpleMessage("Grup family"),
    "familyHonorRanking": m82,
    "familyInfoEdit": MessageLookupByLibrary.simpleMessage(
      "Edit informasi family",
    ),
    "familyInfoRejected": MessageLookupByLibrary.simpleMessage(
      "Informasi family yang Anda ungguh telah ditolak karena melanggar peraturan komunitas Winker. Silakan pergi ke halaman pembuatan family untuk melihat detailnya!",
    ),
    "familyLevel": MessageLookupByLibrary.simpleMessage("Level family"),
    "familyLevelExp": MessageLookupByLibrary.simpleMessage("Exp Level Family"),
    "familyList": MessageLookupByLibrary.simpleMessage("Daftar family"),
    "familyLuckyBag": MessageLookupByLibrary.simpleMessage("Lucky Bag Family"),
    "familyLuckyBagCannotBeDistributed": m83,
    "familyManage": MessageLookupByLibrary.simpleMessage("Manajemen family"),
    "familyManagement": MessageLookupByLibrary.simpleMessage(
      "Manajemen family",
    ),
    "familyMemberOnly": MessageLookupByLibrary.simpleMessage(
      "Hanya anggota family",
    ),
    "familyName": MessageLookupByLibrary.simpleMessage("Nama family"),
    "familyNameCountInvalid": m84,
    "familyNameEditOnce": MessageLookupByLibrary.simpleMessage(
      "Nama family hanya diubah seminggu sekali",
    ),
    "familyPrivilege": MessageLookupByLibrary.simpleMessage(
      "Hak istimewa family",
    ),
    "familyRankExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat dihitung berdasarkan nilai berlian hadiah yang anggota menerima",
    ),
    "familyRankRule": MessageLookupByLibrary.simpleMessage(
      "Peringkat dihitung berdasarkan kontribusi anggota family",
    ),
    "familyRepresentative": MessageLookupByLibrary.simpleMessage(
      "Anggota Family",
    ),
    "familyRepresentativeRule": MessageLookupByLibrary.simpleMessage(
      "Ketua Family, Wakil Ketua Family, Sesepuh",
    ),
    "familyRequest": MessageLookupByLibrary.simpleMessage("Permintaan Family"),
    "familyRoom": MessageLookupByLibrary.simpleMessage("Ruang family"),
    "familyRoomCoverCannotBeModified": MessageLookupByLibrary.simpleMessage(
      "Cover ruang family tidak dapat diubah",
    ),
    "familyRoomNameCannotBeModified": MessageLookupByLibrary.simpleMessage(
      "Nama ruang family tidak dapat diubah",
    ),
    "familyRoomRule": MessageLookupByLibrary.simpleMessage(
      "Setiap family bisa menjalankan ruang. Ruang dimiliki ketua family.",
    ),
    "familySettings": MessageLookupByLibrary.simpleMessage("Pengaturan Family"),
    "familyShareContent": m85,
    "familySlogan": MessageLookupByLibrary.simpleMessage("Slogan Family"),
    "familySloganCountInvalid": m86,
    "familySloganEditOnce": MessageLookupByLibrary.simpleMessage(
      "Slogan family hanya diubah seminggu sekali",
    ),
    "familyTask": MessageLookupByLibrary.simpleMessage("Misi Family"),
    "familyTip": MessageLookupByLibrary.simpleMessage(
      "Lahir di Jawa, sekarang sedang bekerja dan hidup sebagai anak tunggal dari keluarga sederhana. Lingkaran pertemananku cukup sempit jadi aku berharap bisa bertemu crushku di sini.",
    ),
    "familyTreasury": MessageLookupByLibrary.simpleMessage("Harta Family"),
    "familyUpLevel": m87,
    "fanClubList": MessageLookupByLibrary.simpleMessage("daftar klub fans"),
    "fanLevelUpgrade": m88,
    "fansCongratulationUserLevelMsg": m89,
    "fansGroupTabTitleFansList": MessageLookupByLibrary.simpleMessage(
      "Daftar Fans",
    ),
    "fansGroupTabTitleHostBenefits": MessageLookupByLibrary.simpleMessage(
      "Keuntungan Host",
    ),
    "fansGroupTabTitleTreasureRecord": MessageLookupByLibrary.simpleMessage(
      "Catatan Harta Karun",
    ),
    "fansGroupTips": MessageLookupByLibrary.simpleMessage(
      "Memberi dan menerima hadiah meningkatkan pengalaman.",
    ),
    "fansGroupTotalNum": m90,
    "fansNoDrawTimesTip": MessageLookupByLibrary.simpleMessage(
      "Cek dan raih kesempatan dengan menyelesaikan tugas",
    ),
    "fantasy": MessageLookupByLibrary.simpleMessage("Fantasi"),
    "faq": MessageLookupByLibrary.simpleMessage("Pertanyaan Umum"),
    "fateBell": MessageLookupByLibrary.simpleMessage("Lonceng Takdir"),
    "fateBellSetting": MessageLookupByLibrary.simpleMessage(
      "Pengaturan Bel Takdir",
    ),
    "fateNewMatch": MessageLookupByLibrary.simpleMessage(
      "Lonceng takdir telah berbunyi, ada match baru",
    ),
    "favoriteSticker": MessageLookupByLibrary.simpleMessage(
      "fitur stiker telah kami tambahkan.\nAnda dapat menambahkan stiker ke favorit anda sekarang.",
    ),
    "favoritesList": MessageLookupByLibrary.simpleMessage("Hadiah Terkirim"),
    "favoritesRank": MessageLookupByLibrary.simpleMessage("Peringkat Favorit"),
    "favouriteHashtag": MessageLookupByLibrary.simpleMessage(
      "Klik di sini untuk memilih Tagar favorit anda! ",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Umpan balik"),
    "feltOffended": MessageLookupByLibrary.simpleMessage(
      "Merasa tersinggung? ",
    ),
    "female": MessageLookupByLibrary.simpleMessage("Wanita"),
    "femaleShareGuideContent": MessageLookupByLibrary.simpleMessage(
      "Persahabatan yang aman campur privasi!",
    ),
    "fillAccountInfo": MessageLookupByLibrary.simpleMessage(
      "Silakan isi info akun",
    ),
    "film": MessageLookupByLibrary.simpleMessage("Film"),
    "filmDesc": MessageLookupByLibrary.simpleMessage(
      "Apakah anda ingin kembali ke Abad Pertengahan?",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "filterTimesToday": MessageLookupByLibrary.simpleMessage(
      "Anda mencoba terlalu banyak hari ini, silakan coba lagi besok",
    ),
    "findFriends": MessageLookupByLibrary.simpleMessage("Temukan teman"),
    "findSoulmateFirst": MessageLookupByLibrary.simpleMessage(
      "Temukan belahan jiwamu terlebih dahulu",
    ),
    "findYourFatedMate": MessageLookupByLibrary.simpleMessage(
      "Temukan Pasangan Takdirmu",
    ),
    "findingPerfectMatchTitle": MessageLookupByLibrary.simpleMessage(
      "Mencari pasangan Anda yang cocok",
    ),
    "finish": MessageLookupByLibrary.simpleMessage("Selesai"),
    "finishTreasureTask": MessageLookupByLibrary.simpleMessage(
      "Menyelesaikan tugas dapat memberi Anda kesempatan untuk menerima hadiah yang lebih besar.",
    ),
    "firstLevelTips": m91,
    "firstName": MessageLookupByLibrary.simpleMessage("Nama depan"),
    "firstRechargeTitle": MessageLookupByLibrary.simpleMessage("Isi ulang"),
    "firstSelectYourFavourite": MessageLookupByLibrary.simpleMessage(
      "Pertama, pilih teks favorit anda dan bacalah dengan suara anda. Anda dapat mengubah kata-katanya.",
    ),
    "firstStep": MessageLookupByLibrary.simpleMessage("Langkah pertama"),
    "fishFailed": MessageLookupByLibrary.simpleMessage("Gagal memancing"),
    "fishFailedTryAgain": MessageLookupByLibrary.simpleMessage(
      "Gagal memancing, coba lagi.",
    ),
    "fishTheBottle": MessageLookupByLibrary.simpleMessage("Memancing botol"),
    "follow": MessageLookupByLibrary.simpleMessage("Ikuti"),
    "followAll": MessageLookupByLibrary.simpleMessage("Ikuti Semua"),
    "followAndJoinTheFanClub": MessageLookupByLibrary.simpleMessage(
      "Ikuti dan gabunglah dengan klub fans",
    ),
    "followBack": MessageLookupByLibrary.simpleMessage("Ikuti kembali"),
    "followBestSurprise": MessageLookupByLibrary.simpleMessage(
      "Ikuti mereka untuk kejutan terbaik!",
    ),
    "followGuide": m92,
    "followHostNow": MessageLookupByLibrary.simpleMessage(
      "Ikuti host sekarang",
    ),
    "followRoomContent": MessageLookupByLibrary.simpleMessage(
      "Ikuti ruangan untuk menerima berita pesta!",
    ),
    "followSomeInterest": MessageLookupByLibrary.simpleMessage(
      "Ikuti beberapa teman menarik sekarang!",
    ),
    "followSuccess": MessageLookupByLibrary.simpleMessage("Berhasil diikuti"),
    "followTheRoom": MessageLookupByLibrary.simpleMessage("Ikuti ruang ini"),
    "followThisRoom": MessageLookupByLibrary.simpleMessage("Ikuti ruangan ini"),
    "followUserGuideContent": MessageLookupByLibrary.simpleMessage(
      "Ikuti pengguna, bergabunglah lain kali!",
    ),
    "followYou": MessageLookupByLibrary.simpleMessage("Mengikutimu"),
    "followed": MessageLookupByLibrary.simpleMessage("Sudah mengikuti"),
    "followedRoomEmpty": MessageLookupByLibrary.simpleMessage(
      "Anda belum mengikuti ruangan mana pun.",
    ),
    "followedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil mengikuti",
    ),
    "followedYou": MessageLookupByLibrary.simpleMessage("mengikutimu"),
    "follower": MessageLookupByLibrary.simpleMessage("Pengikut"),
    "followerWithNum": m93,
    "followers": MessageLookupByLibrary.simpleMessage("Pengikut"),
    "following": MessageLookupByLibrary.simpleMessage("Mengikuti"),
    "followingRoom": MessageLookupByLibrary.simpleMessage("Room yang ikuti"),
    "followingUser": MessageLookupByLibrary.simpleMessage(
      "Pengguna yang kamu ikut",
    ),
    "forSomeoneToMatchYou": MessageLookupByLibrary.simpleMessage(
      "Seseorang match dengan anda dalam 3 detik, tolong tinggalkan bintang 5!",
    ),
    "forYou": MessageLookupByLibrary.simpleMessage("Untukmu"),
    "forceClosedTips": MessageLookupByLibrary.simpleMessage(
      "Melanggar aturan komunitas, ruangan ini telah ditutup.",
    ),
    "formIdEmpty": MessageLookupByLibrary.simpleMessage("ID formulir kosong"),
    "fourteenCentury": MessageLookupByLibrary.simpleMessage("Abad ke-14"),
    "free": MessageLookupByLibrary.simpleMessage("Gratis"),
    "freeChance": m94,
    "freeGift": MessageLookupByLibrary.simpleMessage("Hadiah gratis"),
    "freeJoin": MessageLookupByLibrary.simpleMessage("Gratis untuk bergabung"),
    "freeMode": MessageLookupByLibrary.simpleMessage("Model bebas"),
    "freeModeContent": MessageLookupByLibrary.simpleMessage(
      "Konten sensitif tidak dapat dibatasi dalam mode bebas.",
    ),
    "freeModeDetailContent": MessageLookupByLibrary.simpleMessage(
      "Dalam mode bebas, anda dapat mengekspresikan diri dengan bebas, tetapi pihak lain harus menerima undangan anda untuk membuka mode yang sama. ",
    ),
    "freeToGet": MessageLookupByLibrary.simpleMessage("Bebas ambil mic"),
    "friendNotFound": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah perlu menggunakan 1 kotak hadiah",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("Teman"),
    "friendsMessages": MessageLookupByLibrary.simpleMessage("Pesan Teman"),
    "friendsRequestSettings": MessageLookupByLibrary.simpleMessage(
      "Pengaturan -> Pengaturan VIP -> Siapa yang dapat mengobrol dengan saya?",
    ),
    "friendsToBeSent": MessageLookupByLibrary.simpleMessage(
      "Teman yang akan dikirim:",
    ),
    "friendsYouMayKnow": MessageLookupByLibrary.simpleMessage(
      "Teman yang mungkin Anda kenal",
    ),
    "friendshipMatching": MessageLookupByLibrary.simpleMessage(
      "Pencocokan persahabatan",
    ),
    "from": MessageLookupByLibrary.simpleMessage("Dari"),
    "fromLuckyBag": MessageLookupByLibrary.simpleMessage(
      "dari tas keberuntungan.",
    ),
    "fromTreasureBox": MessageLookupByLibrary.simpleMessage(
      "dari Kotak Harta Karun",
    ),
    "full": MessageLookupByLibrary.simpleMessage("Penuh"),
    "fundDiamond": MessageLookupByLibrary.simpleMessage("1 dana = 1 berlian"),
    "game": MessageLookupByLibrary.simpleMessage("Gim"),
    "gameBadge": MessageLookupByLibrary.simpleMessage("Medali Game"),
    "gameBalance": MessageLookupByLibrary.simpleMessage("Saldo:"),
    "gameCenter": MessageLookupByLibrary.simpleMessage("Pusat Game"),
    "gameCenterRule": MessageLookupByLibrary.simpleMessage("Aturan Pusat Game"),
    "gameCenterRuleContent1": MessageLookupByLibrary.simpleMessage(
      "Pemain yang memenuhi persyaratan yang diperlukan dalam permainan akan memiliki kesempatan untuk menerima hadiah tugas - poin. Gunakan poin untuk undian berhadiah, dengan tingkat kemenangan 100%!",
    ),
    "gameCenterRuleContent2": MessageLookupByLibrary.simpleMessage(
      "Gunakan 10 poin untuk satu undian dengan tingkat kemenangan 100%!",
    ),
    "gameCenterRuleContent3": MessageLookupByLibrary.simpleMessage(
      "Pemeringkatan disusun dalam urutan menurun berdasarkan koin yang saat ini dimenangkan oleh pengguna di semua game.",
    ),
    "gameCenterRuleContent4": MessageLookupByLibrary.simpleMessage(
      "Daftar peringkat dibagi menjadi peringkat harian dan mingguan. Peringkat harian akan diatur ulang pada pukul 00:00 setiap hari; peringkat mingguan akan diatur ulang pada pukul 00:00 setiap hari Senin.",
    ),
    "gameCenterRuleContent5": MessageLookupByLibrary.simpleMessage(
      "Ya, selama penyelesaian peringkat mingguan, bingkai avatar yang sesuai akan diberikan kepada tiga pengguna teratas dalam daftar. Waktu distribusi hadiah: 1:00 pagi pada hari Senin.",
    ),
    "gameCenterRuleTitle1": MessageLookupByLibrary.simpleMessage(
      "1 Bagaimana cara memainkan undian berhadiah 1 poin?",
    ),
    "gameCenterRuleTitle2": MessageLookupByLibrary.simpleMessage(
      "2 Bagaimana jumlah hadiah undian berhadiah 1 poin dihitung?",
    ),
    "gameCenterRuleTitle3": MessageLookupByLibrary.simpleMessage(
      "3 Bagaimana peringkat dihitung?",
    ),
    "gameCenterRuleTitle4": MessageLookupByLibrary.simpleMessage(
      "4 Bagaimana daftar peringkat diperbarui?",
    ),
    "gameCenterRuleTitle5": MessageLookupByLibrary.simpleMessage(
      "5 Apakah ada hadiah untuk daftar peringkat?",
    ),
    "gameCharge": MessageLookupByLibrary.simpleMessage("Isi komisi 10%"),
    "gameError": m95,
    "gameMatch": MessageLookupByLibrary.simpleMessage("Pencocokan game"),
    "gameNotFound": MessageLookupByLibrary.simpleMessage(
      "Game tidak ditemukan!",
    ),
    "gameOver": MessageLookupByLibrary.simpleMessage("Permainan Berakhir"),
    "gameOverAndWinner": m96,
    "gameOverLowerCase": MessageLookupByLibrary.simpleMessage("Kekalahan"),
    "gameRecords": MessageLookupByLibrary.simpleMessage("Catatan Partisipasi"),
    "gameRoom": MessageLookupByLibrary.simpleMessage("Ruang game"),
    "gameSelectTips": MessageLookupByLibrary.simpleMessage(
      "Pilih kuantitas diamond > Pilih item",
    ),
    "gameStart": MessageLookupByLibrary.simpleMessage("Mulai game"),
    "gaming": MessageLookupByLibrary.simpleMessage("Berjudi"),
    "gemBalance": MessageLookupByLibrary.simpleMessage("Saldo Rubi"),
    "gemExchangeDiamond": m97,
    "gems": MessageLookupByLibrary.simpleMessage("Rubi"),
    "gemsLowCase": MessageLookupByLibrary.simpleMessage("rubi"),
    "generalPrediction": MessageLookupByLibrary.simpleMessage("prediksi umum"),
    "get": MessageLookupByLibrary.simpleMessage("Dapatkan"),
    "getBigBonus": MessageLookupByLibrary.simpleMessage(
      "dapatkan nilai bonus besar 1000 diamond",
    ),
    "getDiamondDay": m98,
    "getDiamondsEveryDay": MessageLookupByLibrary.simpleMessage(
      "Dapatkan berlian setiap hari",
    ),
    "getDrawNum": MessageLookupByLibrary.simpleMessage("Ambil jumlah undian"),
    "getForFree": MessageLookupByLibrary.simpleMessage(
      "Dapatkan secara gratis",
    ),
    "getIt": MessageLookupByLibrary.simpleMessage("Mengerti"),
    "getItTomorrow": MessageLookupByLibrary.simpleMessage("Dapatkannya besok"),
    "getMoreInWinker": MessageLookupByLibrary.simpleMessage(
      "Kamu bisa mendapatkan lebih banyak eksposur dan rekomendasi di Winker.",
    ),
    "getMoreRewards": MessageLookupByLibrary.simpleMessage(
      "Dapatkan lebih banyak hadiah",
    ),
    "getTheTreasureRewards": MessageLookupByLibrary.simpleMessage(
      "Dapatkan hadiah dari harta karun sekarang!",
    ),
    "getTitle": MessageLookupByLibrary.simpleMessage("Titel"),
    "gif": MessageLookupByLibrary.simpleMessage("Gambar"),
    "gift": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "giftAmount": m99,
    "giftAwardResetTime": MessageLookupByLibrary.simpleMessage(
      "Reset harian pada 00:00 (GMT+3)",
    ),
    "giftAwardRules": MessageLookupByLibrary.simpleMessage(
      "1. Hanya kamar yang mencapai level 5 yang memiliki kotak harta karun.\n2. Kunci kotak dapat diperoleh dengan mengirimkan hadiah berlian di kamar.\n3. Kotak harta karun akan terbuka saat bilah progres tombol kotak terisi.\n4. Setiap orang di kamar dapat memiliki kesempatan untuk memperoleh hadiah di kotak harta karun.\n5. Setelah membuka kotak, beberapa detik mungkin diperlukan agar hadiah dapat dikirim ke akun Anda. Berlian akan ditambahkan ke saldo Anda, bingkai avatar, dan tema kamar akan ditambahkan ke inventaris Anda.\n6. Semakin tinggi level kotak harta karun, semakin besar hadiah di dalamnya.\n7. Bilah progres tombol kotak akan disetel ulang pada pukul 00:00 (GMT+3) setiap hari.",
    ),
    "giftBadge": MessageLookupByLibrary.simpleMessage("Medali Hadiah"),
    "giftBox": MessageLookupByLibrary.simpleMessage("Kotak hadiah:"),
    "giftBoxDesc": MessageLookupByLibrary.simpleMessage("Kotak Hadiah"),
    "giftDiscountPercent": m100,
    "giftFloatScreen": m101,
    "giftFloatScreenByLottery": m102,
    "giftFrom": MessageLookupByLibrary.simpleMessage("Kamar hadiah"),
    "giftGallery": MessageLookupByLibrary.simpleMessage("Galeri hadiah"),
    "giftGalleryIntroIlluminationRuleContent": MessageLookupByLibrary.simpleMessage(
      "1. Ruang Pameran Hadiah adalah fitur gameplay interaktif di ruang suara. Ketika kamu menerima jumlah hadiah yang ditentukan, kamu dapat menaikkan hadiah yang sesuai (jumlah hadiah tertentu yang diperlukan tergantung pada informasi di halaman). Klik gambar hadiah untuk melihat detail papan peringkat pemberi hadiah.\n\n2. Papan peringkat pemberi hadiah untuk hadiah individu hanya akan menampilkan tiga pengguna teratas. Dengan memberikan jumlah hadiah yang ditentukan, kamu dapat memiliki hak penamaan untuk hadiah tersebut (rentang tampilan: terbatas pada dinding hadiah pengguna penerima).\n\n3. Hadiah di ruang pameran akan diperbarui secara teratur. Hadiah yang baru ditambahkan akan ditampilkan secara bersamaan di dinding hadiah. Memberikan jumlah hadiah baru yang ditentukan juga dapat menaikkan mereka.\n",
    ),
    "giftGalleryIntroIlluminationSectionTitle":
        MessageLookupByLibrary.simpleMessage("Aturan Menaikkan Hadiah"),
    "giftGalleryIntroNameRuleContent": MessageLookupByLibrary.simpleMessage(
      "1. Setelah hadiah dinaikkan, pengguna yang menempati peringkat pertama di papan peringkat pemberian hadiah dapat memiliki hak penamaan untuk hadiah tersebut. Gambar profil pengguna peringkat teratas akan ditampilkan di bawah tugas hadiah tersebut, dan gambar profil akan diperbarui secara real-time. Jika beberapa pengguna telah memberikan jumlah hadiah yang sama, pengguna yang memberikan hadiah terlebih dahulu akan dianggap sebagai pengguna peringkat teratas.\n\n2. Setelah pengguna berhasil memberi nama hadiah, pemberitahuan layar publik akan muncul di ruangan tempat penerima hadiah saat ini berada.\n\n3. Data untuk fitur gameplay ini akan direset pada pukul 00:00 setiap tanggal 1 setiap bulannya. Pada saat itu, hadiah yang naik akan kembali ke keadaan normal, dan status penamaan pengguna akan diatur ulang.",
    ),
    "giftGalleryIntroNameSectionTitle": MessageLookupByLibrary.simpleMessage(
      "Aturan Penamaan Hadiah",
    ),
    "giftGalleryIntroductionTitle": MessageLookupByLibrary.simpleMessage(
      "Deskripsi Ruang Pameran Hadiah",
    ),
    "giftGalleryListSubtitle": MessageLookupByLibrary.simpleMessage(
      "Hadiah akan naik ketika kamu mengirim jumlah total yang diperlukan.",
    ),
    "giftGivingDuringTheEvent": MessageLookupByLibrary.simpleMessage(
      "Riwayat Pengiriman Hadiah Selama Event",
    ),
    "giftLightUpRank": MessageLookupByLibrary.simpleMessage(
      "Hadiah naik pangkat",
    ),
    "giftNewerGuideTip": MessageLookupByLibrary.simpleMessage(
      "Anda baru saja mendapat hadiah ini, berikan kepada tuan rumah yang Anda suka ~",
    ),
    "giftNumUpLimit": m103,
    "giftPk": MessageLookupByLibrary.simpleMessage("Pertarungan Hadiah"),
    "giftReceive": MessageLookupByLibrary.simpleMessage("Hadiah yang diterima"),
    "giftSendGuid": MessageLookupByLibrary.simpleMessage(
      "Klik untuk mengirim hadiah untuk menunjukkan penghargaan Anda!",
    ),
    "giftSendingDetails": MessageLookupByLibrary.simpleMessage(
      "Detail pengiriman hadiah event",
    ),
    "giftSent": MessageLookupByLibrary.simpleMessage("Mengirim"),
    "giftSentYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Hadiah telah dikirim ke ransel Anda.",
    ),
    "giftUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Hadiah ini telah mencapai batas atas pengiriman hari ini",
    ),
    "giftValue": MessageLookupByLibrary.simpleMessage("Nilai Hadiah:"),
    "giftsCanSendFriends": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah ke teman yang anda sukai.",
    ),
    "giftsReceived": MessageLookupByLibrary.simpleMessage("Menerima"),
    "giftsSent": MessageLookupByLibrary.simpleMessage("Mengirim"),
    "gitGalleryRankSubtitle": MessageLookupByLibrary.simpleMessage(
      "Jika hadiah ini naik, No.1 pemberi hadiah akan diakui titel pemberi hadiahnya.",
    ),
    "gitGalleryRankTitle": MessageLookupByLibrary.simpleMessage(
      "3 Pemberi Hadiah Teratas",
    ),
    "giveUpCard": MessageLookupByLibrary.simpleMessage(
      "Apakah anda ingin menyerah dengan kartu identitas anda?",
    ),
    "globalMute": MessageLookupByLibrary.simpleMessage(
      "Semua Anggota Dibisukan",
    ),
    "gloryFamily": MessageLookupByLibrary.simpleMessage("Family Kemuliaan"),
    "go": MessageLookupByLibrary.simpleMessage("Lanjut"),
    "goCoupleZone": MessageLookupByLibrary.simpleMessage("Buka zona cinta"),
    "goFindRooms": MessageLookupByLibrary.simpleMessage("Temukan Ruangan"),
    "goForVerification": MessageLookupByLibrary.simpleMessage(
      "Klik untuk verifikasi",
    ),
    "goProposal": MessageLookupByLibrary.simpleMessage("Lamar"),
    "goSetting": MessageLookupByLibrary.simpleMessage("Pergi ke pengaturan"),
    "goStore": MessageLookupByLibrary.simpleMessage("Pergi ke toko"),
    "goToChat": MessageLookupByLibrary.simpleMessage("Lanjut untuk obrol"),
    "goToCheck": MessageLookupByLibrary.simpleMessage("Lanjut untuk cek!"),
    "goToFind": MessageLookupByLibrary.simpleMessage("Pergi untuk menemukan"),
    "goToGet": MessageLookupByLibrary.simpleMessage("Lanjut untuk dapatkan"),
    "goToMoments": MessageLookupByLibrary.simpleMessage(
      "Buka momen untuk menemukan teman yang menarik!",
    ),
    "goToRecharge": MessageLookupByLibrary.simpleMessage("Pergi ke isi ulang"),
    "goToSetNotifications": MessageLookupByLibrary.simpleMessage(
      "Buka Pengaturan-Winker-notifikasi-Izinkan notifikasi.",
    ),
    "goToSettings": MessageLookupByLibrary.simpleMessage("Pergi ke setelan"),
    "goToSystemSettings": MessageLookupByLibrary.simpleMessage(
      "Buka pengaturan sistem",
    ),
    "goToVerify": MessageLookupByLibrary.simpleMessage("Klik untuk verifikasi"),
    "goToWear": MessageLookupByLibrary.simpleMessage("Memakai"),
    "goldFamily": MessageLookupByLibrary.simpleMessage("Family Emas"),
    "goldGet1": MessageLookupByLibrary.simpleMessage(
      "1. Koin dapat diperoleh dengan sign-in.",
    ),
    "goldGet2": MessageLookupByLibrary.simpleMessage(
      "2. Koin dapat diperoleh dengan melakukan tugas dan pencapaian.",
    ),
    "goldTask": MessageLookupByLibrary.simpleMessage("Tugas Gold"),
    "golds": MessageLookupByLibrary.simpleMessage("Koin"),
    "goldsCanSendGift": MessageLookupByLibrary.simpleMessage(
      "Koin dapat digunakan untuk mengirim hadiah.",
    ),
    "goldsLowCase": MessageLookupByLibrary.simpleMessage("koin"),
    "goldsUses1": MessageLookupByLibrary.simpleMessage(
      "1. Koin dapat digunakan untuk mengirim hadiah.",
    ),
    "goldsUses2": MessageLookupByLibrary.simpleMessage(
      "2. Koin dapat digunakan untuk menambah waktu pencocokan.",
    ),
    "goodsHasExpired": MessageLookupByLibrary.simpleMessage(
      "Item ini telah kedaluwarsa",
    ),
    "googlePay": MessageLookupByLibrary.simpleMessage("Pembayaran Google"),
    "got": MessageLookupByLibrary.simpleMessage("dapatkan"),
    "gotFromTreasureBox": m104,
    "gotIt": MessageLookupByLibrary.simpleMessage("Mengerti"),
    "gotRechargeRewards": MessageLookupByLibrary.simpleMessage(
      "Anda mendapat hadiah isi ulang!",
    ),
    "gotten": MessageLookupByLibrary.simpleMessage("Didapat"),
    "grab": MessageLookupByLibrary.simpleMessage("Merebut!"),
    "grabMicMode": MessageLookupByLibrary.simpleMessage("Mode grab mikrofon"),
    "grabPoints": m105,
    "grabbedTheMic": MessageLookupByLibrary.simpleMessage("Raih mikrofonnya!"),
    "grabbing": MessageLookupByLibrary.simpleMessage("grab/rebut"),
    "grabbingUp": MessageLookupByLibrary.simpleMessage("Grab/rebut"),
    "greatStart": MessageLookupByLibrary.simpleMessage("Mulai yang baik"),
    "greaterThanCount": m106,
    "greeting1": MessageLookupByLibrary.simpleMessage("Hai"),
    "greeting2": MessageLookupByLibrary.simpleMessage("Hai Halo~"),
    "greeting3": MessageLookupByLibrary.simpleMessage(
      "Senang berkenalan denganmu",
    ),
    "groupMessageNotDisturb": MessageLookupByLibrary.simpleMessage(
      "Jangan Ganggu Grup",
    ),
    "groupYourInfo": MessageLookupByLibrary.simpleMessage(
      "Anda dapat memilih verifikasi audio tidak terlihat. Apakah anda yakin ingin keluar?",
    ),
    "growthBadge": MessageLookupByLibrary.simpleMessage("Medali Pertumbuhan"),
    "guessGuideTip": MessageLookupByLibrary.simpleMessage(
      "Masukkan tebakan Anda!",
    ),
    "guest": MessageLookupByLibrary.simpleMessage("TAMU"),
    "guestSpeak": MessageLookupByLibrary.simpleMessage("Tamu berbicara"),
    "guideLines": MessageLookupByLibrary.simpleMessage("Panduan"),
    "guidePostMoment": MessageLookupByLibrary.simpleMessage(
      "Klik di sini untuk memposting cerita hari ini kepada orang lain!",
    ),
    "guidePublishInput": MessageLookupByLibrary.simpleMessage(
      "Perkenalkan diri sendiri dengan kata-kata sederhana...",
    ),
    "hall": MessageLookupByLibrary.simpleMessage("Aula"),
    "hangUp": MessageLookupByLibrary.simpleMessage("Angkat"),
    "hangUpConfirm": MessageLookupByLibrary.simpleMessage("Konfirmasi Hang up"),
    "harassment": MessageLookupByLibrary.simpleMessage("Gangguan"),
    "hasClosedConversation": MessageLookupByLibrary.simpleMessage(
      "Pihak lain telah mengakhiri percakapan ini.",
    ),
    "hasFollowedYou": MessageLookupByLibrary.simpleMessage("sudah mengikutimu"),
    "hashTag": MessageLookupByLibrary.simpleMessage("tagar"),
    "hateSpeech": MessageLookupByLibrary.simpleMessage("Bahasa kebencian"),
    "haveDrawDays": m107,
    "haveOpenedLuckyBag": MessageLookupByLibrary.simpleMessage(
      "Anda telah membuka lucky bag ini",
    ),
    "haveReceivedLuckyBag": MessageLookupByLibrary.simpleMessage(
      "telah menerima lucky bag ini",
    ),
    "haveTogetherDays": m108,
    "haveUncovered": MessageLookupByLibrary.simpleMessage(
      "Anda berdua menemukan profil Winker anda dari surat rahasia.",
    ),
    "he": MessageLookupByLibrary.simpleMessage("dia"),
    "heFollowYou": MessageLookupByLibrary.simpleMessage("Dia mengikutimu"),
    "heLiving": MessageLookupByLibrary.simpleMessage(
      "Dia tinggal di: tempat dekat anda!",
    ),
    "heWinkedYou": m109,
    "heat": MessageLookupByLibrary.simpleMessage("Panas"),
    "heicNotSupported": MessageLookupByLibrary.simpleMessage(
      "Jenis File HEIC tidak didukung.",
    ),
    "helloNiceToMeetYou": MessageLookupByLibrary.simpleMessage(
      "Halo, salam kenal!",
    ),
    "helpAndFeedback": MessageLookupByLibrary.simpleMessage(
      "Bantuan & Umpan balik",
    ),
    "helpUsImprove": MessageLookupByLibrary.simpleMessage(
      "Bantu kami meningkatkan",
    ),
    "her": MessageLookupByLibrary.simpleMessage("dia"),
    "hide": MessageLookupByLibrary.simpleMessage("Sembunyikan"),
    "highCaseTimes": MessageLookupByLibrary.simpleMessage("Durasi"),
    "him": MessageLookupByLibrary.simpleMessage("dia"),
    "hintNetError": MessageLookupByLibrary.simpleMessage("Koneksi gagal."),
    "hintsWithContent": m110,
    "his": MessageLookupByLibrary.simpleMessage("dia"),
    "history": MessageLookupByLibrary.simpleMessage("Riwayat"),
    "holdToSpeak": MessageLookupByLibrary.simpleMessage(
      "Tahan untuk berbicara",
    ),
    "homeBlindDateStatusNormalTitle": MessageLookupByLibrary.simpleMessage(
      "Ikut",
    ),
    "homeChatPartySubtitle": MessageLookupByLibrary.simpleMessage(
      "Mengobrol Seru",
    ),
    "homeCountdownTip": m111,
    "homeFateBellStatusNormalTitle": MessageLookupByLibrary.simpleMessage(
      "Cocok",
    ),
    "homeFateBellTitle": MessageLookupByLibrary.simpleMessage("Cari match"),
    "homeLudoSubtitle": MessageLookupByLibrary.simpleMessage(
      "Permainan papan paling populer",
    ),
    "homePage": MessageLookupByLibrary.simpleMessage("Beranda"),
    "homePageBackground": MessageLookupByLibrary.simpleMessage(
      "Latar belakang halaman beranda",
    ),
    "homePageEffect": MessageLookupByLibrary.simpleMessage(
      "Efek halaman beranda",
    ),
    "homeStatePopSubtitle": MessageLookupByLibrary.simpleMessage(
      "Status anda akan hilang dalam 24 jam",
    ),
    "homeStatePopTitle": MessageLookupByLibrary.simpleMessage(
      "Pilih statusmu saat ini",
    ),
    "homeToBottom": MessageLookupByLibrary.simpleMessage(
      "Sudah dibagian bawah",
    ),
    "homeTruthDareSubtitle": MessageLookupByLibrary.simpleMessage(
      "Temukan teman baru",
    ),
    "honor": MessageLookupByLibrary.simpleMessage("Hormat"),
    "honorBadge": MessageLookupByLibrary.simpleMessage("Medali Kehormatan"),
    "honour": MessageLookupByLibrary.simpleMessage("Hargai"),
    "host": MessageLookupByLibrary.simpleMessage("Host"),
    "hostColsedTheRoom": MessageLookupByLibrary.simpleMessage(
      "Host menutup ruangan",
    ),
    "hot": MessageLookupByLibrary.simpleMessage("Populer"),
    "hotFamily": MessageLookupByLibrary.simpleMessage(
      "Family yang Direkomendasikan",
    ),
    "hotRoom": MessageLookupByLibrary.simpleMessage("Ruangan Hot"),
    "hottest": MessageLookupByLibrary.simpleMessage("Tes populer"),
    "hour": MessageLookupByLibrary.simpleMessage("Jam"),
    "hourAgo": m112,
    "hours": m113,
    "hoursTimeout": m114,
    "howGetIntimacy": MessageLookupByLibrary.simpleMessage(
      "Cara mendapatkan Keintiman",
    ),
    "howToPlay": MessageLookupByLibrary.simpleMessage("Cara bermain"),
    "huaweiPay": MessageLookupByLibrary.simpleMessage("Pembayaran Huawei"),
    "hungUp": MessageLookupByLibrary.simpleMessage("Hang up"),
    "iAm": MessageLookupByLibrary.simpleMessage("Saya adalah"),
    "iAmNewToFamily": MessageLookupByLibrary.simpleMessage(
      "Hai, saya baru datang ke family ini.",
    ),
    "iDontLikeWinker": MessageLookupByLibrary.simpleMessage(
      "Saya tidak suka Winker",
    ),
    "iGetIt": MessageLookupByLibrary.simpleMessage("Saya mengerti"),
    "iGot": MessageLookupByLibrary.simpleMessage("Saya mendapatkan"),
    "iGotIt": MessageLookupByLibrary.simpleMessage("Saya mengerti"),
    "iHere": MessageLookupByLibrary.simpleMessage("Hai, aku disini!"),
    "iKnow": MessageLookupByLibrary.simpleMessage("Saya mengerti"),
    "iSentYou": MessageLookupByLibrary.simpleMessage("Saya mengirim ke anda"),
    "iSentYouMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "Saya mengirimi Anda kunci kotak ajaib",
    ),
    "iStartedLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Saya memulai Roda Keberuntungan.",
    ),
    "iUnderstand": MessageLookupByLibrary.simpleMessage("Saya mengerti"),
    "icebreakerNewbie": m115,
    "id": MessageLookupByLibrary.simpleMessage("ID"),
    "idCardTitle": MessageLookupByLibrary.simpleMessage(
      "Kartu Identitas Winker",
    ),
    "identityShowed": MessageLookupByLibrary.simpleMessage(
      "Identitas ditunjukkan",
    ),
    "ifTheFamilyLuckyBagIsNotClaimedWithin": MessageLookupByLibrary.simpleMessage(
      "Jika lucky bag family tidak diambil dalam 24 jam, sisa keberuntungan akan dikembalikan oleh sistem",
    ),
    "ignore": MessageLookupByLibrary.simpleMessage("Abaikan"),
    "ignored": MessageLookupByLibrary.simpleMessage("Diabaikan"),
    "illegal": MessageLookupByLibrary.simpleMessage("Ilegal"),
    "illegalContent": MessageLookupByLibrary.simpleMessage(
      "Jawaban anda berisi Konten ilegal, harap patuhi aturan komunitas.",
    ),
    "illegalContentReEdit": MessageLookupByLibrary.simpleMessage(
      "Teks berisi konten ilegal, harap di-edit kembali",
    ),
    "imCallInstruction": MessageLookupByLibrary.simpleMessage(
      "Ikuti yang lain untuk memulai panggilan suara",
    ),
    "imageIllegal": MessageLookupByLibrary.simpleMessage(
      "Gambar ini berisi konten ilegal, harap di-unggah kembali",
    ),
    "imageSizeLimit": m116,
    "imageTooSmall": MessageLookupByLibrary.simpleMessage(
      "Ukuran gambar yang dipilih terlalu kecil.",
    ),
    "inCall": MessageLookupByLibrary.simpleMessage("Dalam panggilan"),
    "inCallNotice": MessageLookupByLibrary.simpleMessage(
      "Jalur sedang sibuk, silakan coba lagi nanti",
    ),
    "inChatRoom": m117,
    "inDraw": MessageLookupByLibrary.simpleMessage("dalam undian"),
    "inGameTag": m118,
    "inPk": MessageLookupByLibrary.simpleMessage("dalam Pertarungan"),
    "inReview": MessageLookupByLibrary.simpleMessage("Sedang Ditinjau"),
    "inRoomCannotCallMatch": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat memanggil pencocokan di ruang obrolan.",
    ),
    "inTheRoomNow": MessageLookupByLibrary.simpleMessage(
      "Di dalam ruangan sekarang",
    ),
    "inWord": MessageLookupByLibrary.simpleMessage("di"),
    "inappropriateName": MessageLookupByLibrary.simpleMessage(
      "nama panggilan yang tidak pantas",
    ),
    "inappropriatePhotos": MessageLookupByLibrary.simpleMessage(
      "foto yang tidak pantas",
    ),
    "income": MessageLookupByLibrary.simpleMessage("Pendapatan"),
    "incomeTasks": MessageLookupByLibrary.simpleMessage("Tugas pendapatan"),
    "incomplete": MessageLookupByLibrary.simpleMessage("Tidak Lengkap"),
    "increaseContent": m119,
    "incredible": MessageLookupByLibrary.simpleMessage("Luar biasa!"),
    "inputAnswerHere": MessageLookupByLibrary.simpleMessage(
      "Masukan jawaban disini...",
    ),
    "inputCannotBeNull": MessageLookupByLibrary.simpleMessage(
      "Input tidak boleh kosong",
    ),
    "instagram": MessageLookupByLibrary.simpleMessage("Instagram"),
    "install": MessageLookupByLibrary.simpleMessage("unduh"),
    "instructionChatLuckyBag": MessageLookupByLibrary.simpleMessage(
      "1. Langkah-langkah pengiriman lucky bag: Pilih jumlah hadiah atau berlian yang akan dimasukkan ke dalam lucky bag, lalu pilih jumlah penerima, dan terakhir klik tombol kirim.\n2. Setiap penerima akan secara acak menerima hadiah atau berlian dari lucky bag. Berlian akan dikirim ke dompet Anda dan hadiah akan dikirim ke ransel Anda. Silakan periksa detailnya di notifikasi obrolan.",
    ),
    "instructionDetail": MessageLookupByLibrary.simpleMessage(
      "1. Langkah-langkah pengiriman tas keberuntungan: pilih jumlah hadiah atau diamond yang akan dimasukkan ke dalam tas keberuntungan, lalu pilih jumlah penerima, dan terakhir klik tombol kirim.\n2. Setiap penerima akan secara acak menerima hadiah atau berlian dari tas keberuntungan. Berlian akan dikirim ke dompet Anda dan hadiah akan dikirim ke ransel Anda. Silakan periksa detailnya di notifikasi obrolan.\n3. Tas keberuntungan berlaku selama 10 menit. Jika hadiah atau berlian tidak diklaim dalam waktu 10 menit, maka akan dikembalikan ke pengirim.",
    ),
    "instructions": MessageLookupByLibrary.simpleMessage("Instruksi"),
    "insufficientBalance": MessageLookupByLibrary.simpleMessage(
      "Saldo tidak mencukupi",
    ),
    "insufficientBalanceTip": m120,
    "insufficientCoins": MessageLookupByLibrary.simpleMessage(
      "Koin tidak cukup, isi ulang sekarang!",
    ),
    "insufficientDiamonds": MessageLookupByLibrary.simpleMessage(
      "Berlian tidak mencukupi, isi ulang sekarang!",
    ),
    "insufficientNumberGrabMic": MessageLookupByLibrary.simpleMessage(
      "Jumlah orang tidak cukup untuk merebut mikrofon",
    ),
    "interact": MessageLookupByLibrary.simpleMessage("Pilih"),
    "interestDesTip": MessageLookupByLibrary.simpleMessage(
      "Pilih label untuk menampilkan dirimu dengan cepat.",
    ),
    "interests": MessageLookupByLibrary.simpleMessage("Minat"),
    "intervalSessionsShort": MessageLookupByLibrary.simpleMessage(
      "Interval antar sesi terlalu pendek",
    ),
    "intimacy": MessageLookupByLibrary.simpleMessage("Keintiman"),
    "intimacyBottomTips": MessageLookupByLibrary.simpleMessage(
      "Keintiman obrolan sesuai dengan keintiman. Hadiah yang dikirim di obrolan dan ruangan sama-sama meningkatkan keintiman.",
    ),
    "intimacyImageTipsTitle1": MessageLookupByLibrary.simpleMessage(
      "Pusat pasangan untuk lamaran",
    ),
    "intimacyImageTipsTitle2": MessageLookupByLibrary.simpleMessage(
      "Pintu masuk melamar/bercerai",
    ),
    "intimacyImageTipsTitle3": MessageLookupByLibrary.simpleMessage(
      "Referensi profil pasangan",
    ),
    "intimacyImageTipsTitle4": MessageLookupByLibrary.simpleMessage(
      "Referensi zona cinta",
    ),
    "intimacyInfoTips1": MessageLookupByLibrary.simpleMessage(
      "1. Pertama, Anda bisa mencari teman lawan jenis yang ingin kamu jalin melalui pencocokan atau kamar obrolan.",
    ),
    "intimacyInfoTips2": MessageLookupByLibrary.simpleMessage(
      "2. Obrolan dan hadiah gaya Romantik dapat meningkatkan kemesraan antara kedua belah pihak. Batas harian untuk mengobrol dengan pasangan adalah 50 kali, sedangkan mengirim hadiah tidak terbatas. Setelah hngga 5.000, Anda jadi bisa melamar.",
    ),
    "intimacyInfoTips3": MessageLookupByLibrary.simpleMessage(
      "3. Jika tidak ada interaksi dengan pihak lawan jenis setelah 7 hari, keintiman akan turun 10 poin setiap hari, dan poin terendah adalah 0 poin.",
    ),
    "intimacyInfoTips4": MessageLookupByLibrary.simpleMessage(
      "Anda perlu membeli cincin di toko untuk melamar teman lawan jenis. Ketika pihak lawan menyetujui lamaran Anda untuk menjadi pasangan. Jika pihak lawan tidak memproses lamaran ini dalam waktu 3 hari, lamaran tersebut secara otomatis akan kedaluwarsa. Jika lamaran Anda gagal, cincin itu akan dimasukkan kembali ke dalam ransel Anda.",
    ),
    "intimacyInfoTips5": MessageLookupByLibrary.simpleMessage(
      "5. Dalam hal putusnya hubungan pasangan, permohonan untuk memutuskan hubungan dapat diajukan kepada pihak lawan dengan kesepakatan bersama . Cincin ini akan hilang dengan putusnya hubungan. Anda harus menunggu tiga hari sebelum dapat melamar lagi.",
    ),
    "intimacyInfoTitle1": MessageLookupByLibrary.simpleMessage(
      "Hanya tiga langkah, dapatkan belahan jiwa!",
    ),
    "intimacyInfoTitle2": MessageLookupByLibrary.simpleMessage(
      "Detail pasangan",
    ),
    "intimacyLevelIncreasedTo": MessageLookupByLibrary.simpleMessage(
      "Level Keintiman naik ke",
    ),
    "intimacyLowerCase": MessageLookupByLibrary.simpleMessage("keintiman"),
    "intimacyProposal": MessageLookupByLibrary.simpleMessage(
      "Hanya keintiman lebih dari 5000 yang dapat melamar",
    ),
    "intimacyRank": MessageLookupByLibrary.simpleMessage("Peringkat Keintiman"),
    "intimacyRanking": MessageLookupByLibrary.simpleMessage(
      "Peringkat keintiman",
    ),
    "intimacyRewardTips": MessageLookupByLibrary.simpleMessage(
      "Selamat!\nKeintiman Anda berdua meningkat",
    ),
    "intimacyRule1": MessageLookupByLibrary.simpleMessage(
      "Hadiah senilai 3 diamond = 1 poin keintiman.",
    ),
    "intimacyRule2": MessageLookupByLibrary.simpleMessage(
      "Hadiah senilai 40 koin emas = 1 poin keintiman.",
    ),
    "intimacyScoreIncrease": MessageLookupByLibrary.simpleMessage(
      "Selamat! Keintiman di antara Anda berdua telah meningkat ",
    ),
    "intimacySecondStepTips": MessageLookupByLibrary.simpleMessage(
      "Obrolan atau hadiah romantis dapat meningkatkan keintiman",
    ),
    "intimacyThirdStepTips": MessageLookupByLibrary.simpleMessage(
      "Melamar ke pasangan Anda",
    ),
    "intimacyTip": MessageLookupByLibrary.simpleMessage(
      "Saat ini tidak ada kandidat yang tersedia. Memberi penghargaan kepada pihak lain dapat meningkatkan keintiman.",
    ),
    "intimacyTips": MessageLookupByLibrary.simpleMessage(
      "Jika keintiman lebih dari 5000, Anda dapat melamar menjadi pasangan",
    ),
    "intimacyWithScore": m121,
    "intimateRanking": MessageLookupByLibrary.simpleMessage(
      "Peringkat Keintiman",
    ),
    "introduce": MessageLookupByLibrary.simpleMessage("Perkenalan"),
    "invalid": MessageLookupByLibrary.simpleMessage("Tidak valid"),
    "invalidDescription": MessageLookupByLibrary.simpleMessage(
      "Deskripsi tidak valid",
    ),
    "invalidMobileNumber": MessageLookupByLibrary.simpleMessage(
      "Nomor Ponsel tidak valid",
    ),
    "invalidName": MessageLookupByLibrary.simpleMessage("Nama tidak valid"),
    "invalidTime": MessageLookupByLibrary.simpleMessage("Waktu tidak valid"),
    "invitation": MessageLookupByLibrary.simpleMessage("Undang mikrofon"),
    "invitationCode": MessageLookupByLibrary.simpleMessage("Kode Undangan"),
    "invitationHasSent": MessageLookupByLibrary.simpleMessage(
      "Undangan telah dikirim.",
    ),
    "invitationShort": MessageLookupByLibrary.simpleMessage("Undangan"),
    "invitationToMic": MessageLookupByLibrary.simpleMessage(
      "Undangan naik Mic",
    ),
    "invite": MessageLookupByLibrary.simpleMessage("Ajak"),
    "inviteFriends": MessageLookupByLibrary.simpleMessage("Undang teman"),
    "inviteJoinPerformer": m122,
    "inviteJoinRoom": MessageLookupByLibrary.simpleMessage(
      "Undang untuk ngobrol!",
    ),
    "inviteJoinRoomClub": MessageLookupByLibrary.simpleMessage(
      "Undangan untuk bergabung ke klub ruangan!",
    ),
    "inviteParticipateEvent": m123,
    "invitePk": MessageLookupByLibrary.simpleMessage("Undang Pertarungan"),
    "invitePlayDomino": MessageLookupByLibrary.simpleMessage(
      "Undang untuk bermain Domino",
    ),
    "invitePlayDrawGuess": MessageLookupByLibrary.simpleMessage(
      "Bermain Gambar&Tebak",
    ),
    "invitePlayKnife": MessageLookupByLibrary.simpleMessage(
      "Undang ke tantangan pisau",
    ),
    "invitePlayLudo": MessageLookupByLibrary.simpleMessage(
      "Undang untuk bermain Ludo",
    ),
    "inviteRoomOnlineAlertTitle": m124,
    "inviteRoomOnlineEmpty": MessageLookupByLibrary.simpleMessage(
      "Tidak ada pengguna online lain di ruangan tersebut",
    ),
    "inviteRoomOnlineUsers": MessageLookupByLibrary.simpleMessage(
      "Pengguna Ruang Rekrutmen Online",
    ),
    "inviteSuccess": MessageLookupByLibrary.simpleMessage("Undang berhasil"),
    "inviteToEnable": MessageLookupByLibrary.simpleMessage(
      "Undang untuk mengaktifkan",
    ),
    "inviteToGuest": MessageLookupByLibrary.simpleMessage("Undang tamu"),
    "inviteToHost": MessageLookupByLibrary.simpleMessage("Undang untuk host"),
    "inviteToList": MessageLookupByLibrary.simpleMessage("Daftar Undangan"),
    "inviteToMicMessage": MessageLookupByLibrary.simpleMessage(
      "Mengundangmu bergabung pada obrolan",
    ),
    "inviteToTalent": MessageLookupByLibrary.simpleMessage(
      "Undang untuk menonton pertunjukan berbakat",
    ),
    "inviteToTruthDare": MessageLookupByLibrary.simpleMessage(
      "Undang untuk bermain Jujur&Tantangan",
    ),
    "inviteToWatchVideo": MessageLookupByLibrary.simpleMessage(
      "Undang untuk menonton video",
    ),
    "inviteUndercover": MessageLookupByLibrary.simpleMessage(
      "Undang untuk bermain Undercover",
    ),
    "inviteYouChat": MessageLookupByLibrary.simpleMessage(
      "Undang Anda untuk mengobrol",
    ),
    "inviteYouForDate": MessageLookupByLibrary.simpleMessage(
      "Undang Anda untuk berkencan",
    ),
    "inviteYouForTalent": MessageLookupByLibrary.simpleMessage(
      "Undang Anda untuk pertunjukan berbakat",
    ),
    "inviteYouJoinFamily": MessageLookupByLibrary.simpleMessage(
      "mengundang Anda untuk bergabung dengan family ini",
    ),
    "inviteYouJoinRoom": m125,
    "inviteYouPlayGames": MessageLookupByLibrary.simpleMessage(
      "Undang Anda untuk bermain game",
    ),
    "inviteYouToChat": MessageLookupByLibrary.simpleMessage(
      "Mengundang Anda untuk obrolan suara",
    ),
    "inviteYouToGame": MessageLookupByLibrary.simpleMessage(
      "Mengundang Anda untuk mengobrol",
    ),
    "inviteYouToTakeMic": m126,
    "inviteYouWatchVideo": MessageLookupByLibrary.simpleMessage(
      "Undang Anda untuk menonton video",
    ),
    "inviteYourFriends": MessageLookupByLibrary.simpleMessage(
      "Undang teman anda",
    ),
    "invited": MessageLookupByLibrary.simpleMessage("Diundang"),
    "invitedOnly": MessageLookupByLibrary.simpleMessage("Hanya yang diundang"),
    "invitedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil diundang",
    ),
    "invitedToJoinFamily": MessageLookupByLibrary.simpleMessage(
      "+ Diundang untuk bergabung dengan family",
    ),
    "ironFamily": MessageLookupByLibrary.simpleMessage("Family Besi"),
    "isDropping": MessageLookupByLibrary.simpleMessage("sedang bagikan"),
    "isSending": MessageLookupByLibrary.simpleMessage("sedang bagikan"),
    "issued": MessageLookupByLibrary.simpleMessage("Dikeluarkan"),
    "itsMatch": MessageLookupByLibrary.simpleMessage("Cocok!"),
    "jackPot": MessageLookupByLibrary.simpleMessage("Jackpot"),
    "jackPotTips": MessageLookupByLibrary.simpleMessage(
      "Menyelesaikan tugas mendapatkan poin, yang dapat ditukarkan dengan hadiah.",
    ),
    "jackpotMode": MessageLookupByLibrary.simpleMessage("Mode jackpot"),
    "jackpotModeRule": MessageLookupByLibrary.simpleMessage(
      "Peserta Pertarungan tidak akan menerima pengembalian hadiah selama periode Pertarungan. Pemenang Pertarungan akan mendapatkan hadiah senilai 30% setelah Pertarungan selesai.",
    ),
    "join": MessageLookupByLibrary.simpleMessage("Gabung"),
    "joinFamilyErrorTitle": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memenuhi persyaratan berikut untuk bergabung dengan family ini:",
    ),
    "joinFamilySuccess": MessageLookupByLibrary.simpleMessage(
      "Berhasil bergabung dengan family.",
    ),
    "joinFanClubSuc": MessageLookupByLibrary.simpleMessage(
      "Berhasil bergabung dengan klub fans!",
    ),
    "joinFanGroup": MessageLookupByLibrary.simpleMessage(
      "Bergabung dengan grup fans",
    ),
    "joinMode": MessageLookupByLibrary.simpleMessage("Mode bergabung"),
    "joinMyFamilySuccess": MessageLookupByLibrary.simpleMessage(
      "berhasil bergabung dengan family besar!",
    ),
    "joinNow": MessageLookupByLibrary.simpleMessage("Gabung"),
    "joinRoomClub": MessageLookupByLibrary.simpleMessage(
      "Bergabung dengan klub ruangan",
    ),
    "joinTheFamily": MessageLookupByLibrary.simpleMessage(
      "Bergabung dengan family ini",
    ),
    "joinTheLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Gabung di Roda Keberuntungan",
    ),
    "joinTheRoom": MessageLookupByLibrary.simpleMessage(
      "Bergabung dengan kamar ini",
    ),
    "joinWealthLevel": MessageLookupByLibrary.simpleMessage(
      "Batas Level Kekayaan",
    ),
    "joinWinkerDays": m127,
    "joined": MessageLookupByLibrary.simpleMessage("Bergabung"),
    "joinedTheFanGroup": MessageLookupByLibrary.simpleMessage(
      "bergabung dengan grup fans",
    ),
    "justListenSwipeLike": MessageLookupByLibrary.simpleMessage(
      "Dengarkan saja suara dari pengguna lain, geser ke kanan jika Anda Menyukainya.",
    ),
    "keep": MessageLookupByLibrary.simpleMessage("Terus"),
    "keepDeviceCalendar": MessageLookupByLibrary.simpleMessage(
      "Melacak acara ini terus dengan menambahkannya ke kalender perangkat Anda",
    ),
    "keepEditing": MessageLookupByLibrary.simpleMessage("Terus Mengedit"),
    "keepPosting": MessageLookupByLibrary.simpleMessage("Terus posting"),
    "keepSending": MessageLookupByLibrary.simpleMessage("Kirim terus"),
    "keepStaying": MessageLookupByLibrary.simpleMessage("tetap"),
    "keepTalking": MessageLookupByLibrary.simpleMessage("Tetap berbicara"),
    "keysAndDiamondsOpenThisBox": m128,
    "keysCanBeGifted": m129,
    "keysGiftedByOthers": m130,
    "keysOpenThisBox": m131,
    "kickOut": MessageLookupByLibrary.simpleMessage(
      "Akun Anda telah masuk di perangkat lain.",
    ),
    "kickOutList": MessageLookupByLibrary.simpleMessage(
      "Daftar tendang keluar",
    ),
    "kicked": MessageLookupByLibrary.simpleMessage("Tendang keluar"),
    "kickedOutFamilyBy": MessageLookupByLibrary.simpleMessage(
      "dikeluarkan dari family oleh",
    ),
    "kindTips": MessageLookupByLibrary.simpleMessage("Tips jenis"),
    "king": MessageLookupByLibrary.simpleMessage("Raja"),
    "knifeChallenge": MessageLookupByLibrary.simpleMessage("Game pisau"),
    "knight": MessageLookupByLibrary.simpleMessage("Ksatria"),
    "ktv": MessageLookupByLibrary.simpleMessage("KTV"),
    "labels": MessageLookupByLibrary.simpleMessage("Label"),
    "lackOfCoins": MessageLookupByLibrary.simpleMessage(
      "Kurang koin! Ayo selesaikan lebih banyak tugas",
    ),
    "lackOfCoinsPleaseFinishMoreTasksOrExchangeDiamonds":
        MessageLookupByLibrary.simpleMessage(
          "Kurang koin! Silakan selesaikan lebih banyak tugas atau tukar koin dengan berlian.",
        ),
    "lackOfGems": MessageLookupByLibrary.simpleMessage(
      "Kurangnya rubi! tolong selesaikan lebih banyak tugas untuk mendapatkan rubi.",
    ),
    "lastUpdateTime": m132,
    "lastWeek": MessageLookupByLibrary.simpleMessage("Minggu lalu"),
    "later": MessageLookupByLibrary.simpleMessage("Nanti"),
    "latest": MessageLookupByLibrary.simpleMessage("Terbaru"),
    "latestVersion": MessageLookupByLibrary.simpleMessage(
      "Aplikasi anda saat ini adalah versi terbaru.",
    ),
    "leave": MessageLookupByLibrary.simpleMessage("Keluar"),
    "leaveChat": MessageLookupByLibrary.simpleMessage("Tinggalkan Obrolan"),
    "leaveMicStopPerforming": MessageLookupByLibrary.simpleMessage(
      "Keluar dari mikrofon akan dapat berhenti tampil. Tetap keluar?",
    ),
    "leaveNowWillCloseRoom": MessageLookupByLibrary.simpleMessage(
      "pergi sekarang akan dapat menutup kamar.",
    ),
    "leaveRoomStopPerforming": MessageLookupByLibrary.simpleMessage(
      "Keluar dari kamar akan dapat berhenti tampil. tetap keluar?",
    ),
    "leaveTheFamily": MessageLookupByLibrary.simpleMessage(
      "tinggalkan family ini.",
    ),
    "leaveTheList": MessageLookupByLibrary.simpleMessage("Tinggalkan list ini"),
    "leaveTheMic": MessageLookupByLibrary.simpleMessage("Tinggalkan Mikrofon"),
    "lengthOfPurchase": MessageLookupByLibrary.simpleMessage("Masa berlaku"),
    "less": MessageLookupByLibrary.simpleMessage("Lebih sedikit"),
    "lessThanOneMinute": MessageLookupByLibrary.simpleMessage("< 1 menit"),
    "letTheGameStart": MessageLookupByLibrary.simpleMessage(
      "Mari gamenya dimulai",
    ),
    "letUsKnowTheReasonYouLeave": MessageLookupByLibrary.simpleMessage(
      "Harap beri tahu kami alasan Anda pergi.",
    ),
    "letter": MessageLookupByLibrary.simpleMessage("Kertas surat"),
    "level": MessageLookupByLibrary.simpleMessage("Level"),
    "levelIsIncreasing": MessageLookupByLibrary.simpleMessage(
      "Level anda sedang meningkat dengan kecepatan standar.",
    ),
    "levelMax": MessageLookupByLibrary.simpleMessage("Level Maksimal"),
    "levelMedal": MessageLookupByLibrary.simpleMessage("Medali Level"),
    "levelPrivilege": MessageLookupByLibrary.simpleMessage(
      "Level hak istimewa",
    ),
    "levelPrivilegeTip1": MessageLookupByLibrary.simpleMessage(
      "1. Dapatkan lebih banyak perhatian pada medali level Anda.",
    ),
    "levelPrivilegeTip2": MessageLookupByLibrary.simpleMessage(
      "2. Lebih banyak fitur yang bisa dieksplorasi.",
    ),
    "levelUnlock": m133,
    "levelUpRewards": MessageLookupByLibrary.simpleMessage("Hadiah Naik Level"),
    "levelUpSpeed": MessageLookupByLibrary.simpleMessage(
      "Tingkatkan kecepatan",
    ),
    "levelUpSpeedDesc": MessageLookupByLibrary.simpleMessage(
      "Dapatkan lebih banyak pengalaman level.",
    ),
    "levelUpgradeTitle": MessageLookupByLibrary.simpleMessage(
      "Selamat! Level anda naik ke",
    ),
    "lightingGift": m134,
    "link": MessageLookupByLibrary.simpleMessage("Tautan"),
    "listDescription": MessageLookupByLibrary.simpleMessage("Daftar Deskripsi"),
    "listenToTheVoiceQuestion": MessageLookupByLibrary.simpleMessage(
      "Dengarkan pertanyaannya dan pilih jawaban anda",
    ),
    "listener": MessageLookupByLibrary.simpleMessage("Pendengar"),
    "live": MessageLookupByLibrary.simpleMessage("Live"),
    "liveNow": MessageLookupByLibrary.simpleMessage("Live Sekarang"),
    "liveRoomShareContent": m135,
    "liveRoomShareContentAndPwd": m136,
    "livingIn": MessageLookupByLibrary.simpleMessage("Tinggal di"),
    "loadFailed": MessageLookupByLibrary.simpleMessage("Gagal Memuat"),
    "loadGameFailed": m137,
    "loading": MessageLookupByLibrary.simpleMessage("Sedang Memuat..."),
    "loadingNum": m138,
    "localMusic": MessageLookupByLibrary.simpleMessage("Musik Lokal"),
    "locate": MessageLookupByLibrary.simpleMessage("Menemukan"),
    "location": MessageLookupByLibrary.simpleMessage("Lokasi"),
    "locationServiceTipDes": MessageLookupByLibrary.simpleMessage(
      "Untuk match dengan pengguna terdekat, aktifkan layanan lokasi di pengaturan ponsel anda.",
    ),
    "locationServiceTipTitle": MessageLookupByLibrary.simpleMessage(
      "Layanan lokasi tidak diaktifkan",
    ),
    "locationServiceTurnOn": MessageLookupByLibrary.simpleMessage(
      "Aktifkan Lokasi",
    ),
    "lock": MessageLookupByLibrary.simpleMessage("Mengunci"),
    "lockTheMic": MessageLookupByLibrary.simpleMessage("Kunci Mikrofon"),
    "loginAgree": MessageLookupByLibrary.simpleMessage(
      "Dengan masuk, anda setuju",
    ),
    "loginFailed": MessageLookupByLibrary.simpleMessage(
      "Gagal masuk, silakan coba dengan cara lain.",
    ),
    "loginSlogan": MessageLookupByLibrary.simpleMessage(
      "Tempat paling pas untuk menemukan dia yang cocok",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Keluar"),
    "logoutAlert": MessageLookupByLibrary.simpleMessage(
      "Setelah Keluar, Anda tidak akan menerima pesan baru lagi.",
    ),
    "loveZone": MessageLookupByLibrary.simpleMessage("Zona Cinta"),
    "loveZoneAnswer1": MessageLookupByLibrary.simpleMessage(
      "1、Bagaimana memiliki zona cinta pasangan?",
    ),
    "loveZoneAnswer2": MessageLookupByLibrary.simpleMessage(
      "2, Apa efek untuk mengganti cincin yang dikenakan?",
    ),
    "loveZoneAnswer3": MessageLookupByLibrary.simpleMessage(
      "3. Bagaimana cara meningkatkan nilai keberkahan?",
    ),
    "loveZoneQA": MessageLookupByLibrary.simpleMessage("Q&A zona cinta "),
    "loveZoneQuestion1": MessageLookupByLibrary.simpleMessage(
      "Pertama, Anda harus memiliki teman lawan jenis yang memiliki hubungan dengan keintiman lebih dari 5000 sebelum Anda dapat melamarnya. Ketika pihak lawan itu menyetujui lamaran Anda, Anda dapat memiliki zona cinta pasangan.",
    ),
    "loveZoneQuestion2": MessageLookupByLibrary.simpleMessage(
      "Efek cincin baru akan ditampilkan di profil dan zona cinta Anda.",
    ),
    "loveZoneQuestion3": MessageLookupByLibrary.simpleMessage(
      "Mengirim hadiah di zona cinta dapat meningkatkan nilai keberkahan. Tetapi pemilik zona tidak bisa mendapatkan rabat berlian dan pesona.",
    ),
    "luck": MessageLookupByLibrary.simpleMessage("Beruntung"),
    "luckiestDraw": MessageLookupByLibrary.simpleMessage(
      "Undian paling beruntung",
    ),
    "luckyBag": MessageLookupByLibrary.simpleMessage("Lucky bag"),
    "luckyBagCharge": MessageLookupByLibrary.simpleMessage(
      "Komisi 5% ditagih untuk setiap lucky bag",
    ),
    "luckyBagClaimed": m139,
    "luckyBagDetails": MessageLookupByLibrary.simpleMessage("Detail lucky bag"),
    "luckyBagEmpty": MessageLookupByLibrary.simpleMessage(
      "Maaf, lucky bag ini kosong..",
    ),
    "luckyBagFrom": MessageLookupByLibrary.simpleMessage("Lucky bag dari:"),
    "luckyBagHasExpired": MessageLookupByLibrary.simpleMessage(
      "Lucky bag ini telah kedaluwarsa",
    ),
    "luckyBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Lucky bag kosong.",
    ),
    "luckyBagRecord": MessageLookupByLibrary.simpleMessage("Riwayat Lucky bag"),
    "luckyBonusNum": MessageLookupByLibrary.simpleMessage(
      "Bonus keberuntungan:",
    ),
    "luckyNumber": MessageLookupByLibrary.simpleMessage("Nomor keberuntungan"),
    "luckySign": MessageLookupByLibrary.simpleMessage("Tanda keberuntungan"),
    "luckyWheel": MessageLookupByLibrary.simpleMessage("Roda Keberuntungan"),
    "luckyWheelIsAutoEnd": MessageLookupByLibrary.simpleMessage(
      "Karena Roda Keberuntungan belum juga dimulai, event ini ditutup. Diamond telah dikembalikan ke saldo anda.",
    ),
    "luckyWheelIsEnd": MessageLookupByLibrary.simpleMessage(
      "Roda Keberuntungan telah berakhir.",
    ),
    "luckyWheelIsEndAndReturnYourBalance": MessageLookupByLibrary.simpleMessage(
      "Roda Keberuntungan berakhir. Diamond telah dikembalikan ke saldo anda. ",
    ),
    "luckyWheelRules": MessageLookupByLibrary.simpleMessage(
      "1. Hanya pemilik dan admin ruangan yang dapat mengaktifkan Roda Keberuntungan dan menetapkan biaya masuk.\n\n2. Setelah roda keberuntungan dimulai, setidaknya 3 orang harus mulai spin dalam waktu 5 menit.\n\n3. Jika ada 3 peserta atau lebih dalam waktu 5 menit, admin ruangan dapat spin roda keberuntungan secara manual, atau roda keberuntungan akan mulai spin secara otomatis setelah 5 menit dimulai. Jika peserta kurang dari 3 orang dalam waktu 5 menit, roda akan ditutup secara otomatis dan biaya masuk akan dikembalikan.\n\n4. Saat roda keberuntungan mulai spin, peserta yang tersingkir akan dapat dipilih secara acak hingga hanya tersisa satu peserta. Peserta tersebut memenangkan 90% dari total biaya masuk.",
    ),
    "luckyWheelWinner": m140,
    "ludo": MessageLookupByLibrary.simpleMessage("Ludo"),
    "ludoGame": MessageLookupByLibrary.simpleMessage("Game Ludo"),
    "ludoMatch": MessageLookupByLibrary.simpleMessage("Pencocokan Ludo"),
    "lv": MessageLookupByLibrary.simpleMessage("Lv"),
    "lvNum": m141,
    "lvUnLock": m142,
    "magicBox": MessageLookupByLibrary.simpleMessage("Kotak ajaib"),
    "magicBoxTask": MessageLookupByLibrary.simpleMessage("Tugas kotak ajaib"),
    "mainCountry": MessageLookupByLibrary.simpleMessage("Indonesia"),
    "makeAProposal": MessageLookupByLibrary.simpleMessage("Melamar"),
    "makeFirstStarted": MessageLookupByLibrary.simpleMessage(
      "Klik untuk memulai Obrolan",
    ),
    "makeFriends": MessageLookupByLibrary.simpleMessage("Berteman"),
    "makeGoodImpression": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk membuat kesan yang baik.",
    ),
    "male": MessageLookupByLibrary.simpleMessage("Pria"),
    "maleShareGuideContent": MessageLookupByLibrary.simpleMessage(
      "Temukan persahabatan yang intim!",
    ),
    "mall": MessageLookupByLibrary.simpleMessage("Mal"),
    "manageSticker": MessageLookupByLibrary.simpleMessage(
      "Lebih banyak stiker dapat dikelola dan ditambahkan di \"manajemen Koleksi\".",
    ),
    "managed": MessageLookupByLibrary.simpleMessage("Dikelola"),
    "manager": MessageLookupByLibrary.simpleMessage("Pengelola"),
    "master": MessageLookupByLibrary.simpleMessage("Sesepuh"),
    "match": MessageLookupByLibrary.simpleMessage("Cocok"),
    "matchAgain": MessageLookupByLibrary.simpleMessage("Cocokkan lagi"),
    "matchCountEmptyDesc": MessageLookupByLibrary.simpleMessage(
      "Silakan datang besok untuk bertemu teman baru",
    ),
    "matchCountEmptyTitle": MessageLookupByLibrary.simpleMessage(
      "Semua kesempatan anda hari ini telah digunakan",
    ),
    "matchFailed": MessageLookupByLibrary.simpleMessage("Pencocokan gagal"),
    "matchFilter": MessageLookupByLibrary.simpleMessage(
      "Pilih jenis pencocokan",
    ),
    "matchFriends": MessageLookupByLibrary.simpleMessage("Temukan teman"),
    "matchGuide": MessageLookupByLibrary.simpleMessage(
      "Klik untuk mulai mengobrol dengan teman baru",
    ),
    "matchInRoomError": MessageLookupByLibrary.simpleMessage(
      "Pencocokan game tidak didukung di dalam kamar",
    ),
    "matchLikeEachOther": MessageLookupByLibrary.simpleMessage(
      "Anda akan memiliki Match hanya jika anda berdua saling menyukai. Cobalah!",
    ),
    "matchNow": MessageLookupByLibrary.simpleMessage("Cocokkan sekarang"),
    "matchPk": MessageLookupByLibrary.simpleMessage("Pertarungan Match"),
    "matchSucceeds": MessageLookupByLibrary.simpleMessage(
      "Pencocokan berhasil",
    ),
    "matchSuccess": MessageLookupByLibrary.simpleMessage("Pencocokan berhasil"),
    "matchSwitch": MessageLookupByLibrary.simpleMessage("Ganti match"),
    "matchSwitchContent": MessageLookupByLibrary.simpleMessage(
      "Tidak akan mencocokkan orang untuk anda saat ditutup.",
    ),
    "matchTimeout": MessageLookupByLibrary.simpleMessage(
      "Pencocokan kehabisan waktu",
    ),
    "matchTimesIsOver": MessageLookupByLibrary.simpleMessage(
      "Waktu pencocokan hari ini telah berakhir. Harap tunggu sampai besok.",
    ),
    "matchedImTips": MessageLookupByLibrary.simpleMessage(
      "Mencocokkan anda dari tes suara.",
    ),
    "matchedYouByVoice": m143,
    "matching": MessageLookupByLibrary.simpleMessage("Mencocokkan"),
    "matchingASuitableRoom": MessageLookupByLibrary.simpleMessage(
      "Mencocokkan kamar yang cocok untuk Anda",
    ),
    "matchingHqUsers": MessageLookupByLibrary.simpleMessage(
      "Mencocokkan pengguna berkualitas tinggi",
    ),
    "matchingPeoplePlayWith": MessageLookupByLibrary.simpleMessage(
      "Mencocokkan orang untuk bermain dengan",
    ),
    "matesOnlineNow": MessageLookupByLibrary.simpleMessage("Aktifkan sekarang"),
    "max": MessageLookupByLibrary.simpleMessage("Maksimal"),
    "maxSelectOption": m144,
    "me": MessageLookupByLibrary.simpleMessage("Saya"),
    "mediasOverSize": MessageLookupByLibrary.simpleMessage(
      "Ukuran satu video tidak boleh melebihi 20 MB",
    ),
    "member": MessageLookupByLibrary.simpleMessage("Anggota"),
    "message": MessageLookupByLibrary.simpleMessage("Pesan"),
    "messageNotification": MessageLookupByLibrary.simpleMessage(
      "Notifikasi Pesan",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("Pesan"),
    "micAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Akses Mic tidak diaktifkan",
    ),
    "micApplyHasSent": MessageLookupByLibrary.simpleMessage(
      "Permohonan mengambil mic telah dikirim",
    ),
    "micApplyNotice": MessageLookupByLibrary.simpleMessage(
      "Terapkan pemberitahuan",
    ),
    "micIsLocked": MessageLookupByLibrary.simpleMessage("Posisi mic terkunci"),
    "micLayoutUse": m145,
    "micPermission": MessageLookupByLibrary.simpleMessage(
      "\"winker\" ingin mengakses mic anda dan mengumpulkan data suara anda untuk mengaktifkan pesan suara, verifikasi identitas hanya saat aplikasi sedang digunakan.",
    ),
    "microphoneOff": MessageLookupByLibrary.simpleMessage("Mikrofon mati"),
    "microphoneOn": MessageLookupByLibrary.simpleMessage("Mikrofon aktif"),
    "mics_10": MessageLookupByLibrary.simpleMessage("10 Mikrofon"),
    "mics_2": MessageLookupByLibrary.simpleMessage("2 Mikrofon"),
    "mics_5": MessageLookupByLibrary.simpleMessage("5 Mikrofon"),
    "mics_8_2": MessageLookupByLibrary.simpleMessage("8+2 Mikrofon"),
    "mics_9": MessageLookupByLibrary.simpleMessage("9 Mikrofon"),
    "mightContainRisk": MessageLookupByLibrary.simpleMessage(
      "Pesan ini mungkin berisi konten yang tidak bersahabat atau Beresiko",
    ),
    "milestoneListTasks": MessageLookupByLibrary.simpleMessage(
      "Tugas Pencapaian",
    ),
    "milestones": MessageLookupByLibrary.simpleMessage("Pencapaian"),
    "minExchangeDiamond": MessageLookupByLibrary.simpleMessage(
      "Minimal 10 Berlian",
    ),
    "minExchangeGems": MessageLookupByLibrary.simpleMessage("Minimal 10 rubi"),
    "minSelectOption": m146,
    "mine": MessageLookupByLibrary.simpleMessage("Milikku"),
    "mineMenuBackpack": MessageLookupByLibrary.simpleMessage("Tas"),
    "miniPostal": MessageLookupByLibrary.simpleMessage("Kartu pos mini"),
    "minimize": MessageLookupByLibrary.simpleMessage("Memperkecil"),
    "mins": MessageLookupByLibrary.simpleMessage("menit"),
    "minute": MessageLookupByLibrary.simpleMessage("Menit"),
    "minuteAgo": m147,
    "minutes": m148,
    "missWinker": MessageLookupByLibrary.simpleMessage("Nona Winker"),
    "mobileNumberConfirmation": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi nomor Ponsel",
    ),
    "mode": MessageLookupByLibrary.simpleMessage("Mode"),
    "modeWillBeEnd": MessageLookupByLibrary.simpleMessage(
      "Mode ini akan diakhiri. Anda dapat beralih ke mode lain setelah berakhir.",
    ),
    "moment": MessageLookupByLibrary.simpleMessage("Momen"),
    "momentDetailViewProfile": MessageLookupByLibrary.simpleMessage(
      "Lihat Profil",
    ),
    "momentNoExist": MessageLookupByLibrary.simpleMessage(
      "Unggahan ini telah dihapus",
    ),
    "moments": MessageLookupByLibrary.simpleMessage("Momen"),
    "money": MessageLookupByLibrary.simpleMessage("Uang"),
    "month": MessageLookupByLibrary.simpleMessage("Bulan"),
    "monthly": MessageLookupByLibrary.simpleMessage("Bulanan"),
    "monthlyContribution": MessageLookupByLibrary.simpleMessage(
      "Kontribusi Bulanan",
    ),
    "mood": MessageLookupByLibrary.simpleMessage("Perasaan"),
    "more": MessageLookupByLibrary.simpleMessage("Selengkapnya"),
    "moreFreeChance": MessageLookupByLibrary.simpleMessage(
      "Lebih banyak kesempatan gratis",
    ),
    "morePriority": MessageLookupByLibrary.simpleMessage(
      "Selangkah lagi untuk mendapatkan prioritas lebih untuk mengobrol dengan orang lain.",
    ),
    "moreStickers": MessageLookupByLibrary.simpleMessage("Lebih banyak Stiker"),
    "moreTasks": MessageLookupByLibrary.simpleMessage("Tugas lainnya"),
    "moreThan3ParticipantsToStartLuckyWheel": m149,
    "moreVerifiedUser": MessageLookupByLibrary.simpleMessage(
      "Lebih banyak pengguna terverifikasi",
    ),
    "moreWaysToLogIn": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan lebih banyak cara>",
    ),
    "moveFront": MessageLookupByLibrary.simpleMessage("Pindah ke depan"),
    "moveTheme": MessageLookupByLibrary.simpleMessage("Ubah Tema"),
    "moveThemeSucc": MessageLookupByLibrary.simpleMessage("Berhasil disimpan"),
    "moveToAnotherTheme": MessageLookupByLibrary.simpleMessage(
      "Pindah ke tema lain",
    ),
    "mrWinker": MessageLookupByLibrary.simpleMessage("Tuan Winker"),
    "msgDescActivity": MessageLookupByLibrary.simpleMessage("[Event]"),
    "msgDescAudio": MessageLookupByLibrary.simpleMessage("Pesan Suara"),
    "msgDescAudioCall": MessageLookupByLibrary.simpleMessage(
      "[Panggilan audio]",
    ),
    "msgDescChatMode": MessageLookupByLibrary.simpleMessage(
      "[Ubah mode obrolan]",
    ),
    "msgDescExpression": MessageLookupByLibrary.simpleMessage("Stiker"),
    "msgDescGift": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "msgDescImage": MessageLookupByLibrary.simpleMessage("[Foto]"),
    "msgDescLuckyBag": MessageLookupByLibrary.simpleMessage("[Lucky bag]"),
    "msgDescQuestionBox": MessageLookupByLibrary.simpleMessage(
      "[game pertanyaan]",
    ),
    "msgDescReward": MessageLookupByLibrary.simpleMessage("[Reward]"),
    "msgDescShare": MessageLookupByLibrary.simpleMessage("[Bagikan]"),
    "msgDescSystem": MessageLookupByLibrary.simpleMessage("[Notifikasi]"),
    "msgDescUncoverIdentity": MessageLookupByLibrary.simpleMessage(
      "[Ungkapkan profil Winker]",
    ),
    "msgDescUnknown": MessageLookupByLibrary.simpleMessage(
      "Anda menerima pesan baru yang tidak didukung di versi saat ini, harap perbaharui.",
    ),
    "msgDescVideo": MessageLookupByLibrary.simpleMessage("Pesan Video"),
    "msgDescVideoCall": MessageLookupByLibrary.simpleMessage(
      "[Panggilan video]",
    ),
    "msgDescWink": MessageLookupByLibrary.simpleMessage("[Wink]"),
    "msgDigestUnknown": MessageLookupByLibrary.simpleMessage(
      "Anda menerima pesan yang tidak didukung.",
    ),
    "msgNew": MessageLookupByLibrary.simpleMessage("Baru"),
    "msgTooShort": MessageLookupByLibrary.simpleMessage(
      "Pesan terlalu singkat",
    ),
    "multipleFace": MessageLookupByLibrary.simpleMessage(
      "Beberapa wajah telah terdeteksi.\nPilih satu untuk melanjutkan.",
    ),
    "music": MessageLookupByLibrary.simpleMessage("Musik"),
    "musicFileNotExist": MessageLookupByLibrary.simpleMessage(
      "File musik tidak ada",
    ),
    "mute": MessageLookupByLibrary.simpleMessage("bisukan"),
    "muteFamilyNotification": MessageLookupByLibrary.simpleMessage(
      "Bisukan notifikasi family",
    ),
    "muteNotifications": MessageLookupByLibrary.simpleMessage(
      "Notifikasi Bisukan",
    ),
    "my": MessageLookupByLibrary.simpleMessage("Saya"),
    "myBadges": MessageLookupByLibrary.simpleMessage("Medali Saya"),
    "myBalance": MessageLookupByLibrary.simpleMessage("Saldo Saya"),
    "myBirthIs": MessageLookupByLibrary.simpleMessage("Tanggal Lahir saya"),
    "myBottle": MessageLookupByLibrary.simpleMessage("Botol saya"),
    "myCollection": MessageLookupByLibrary.simpleMessage("Koneksi saya"),
    "myCustomers": MessageLookupByLibrary.simpleMessage("Pelanggan Saya"),
    "myFamily": MessageLookupByLibrary.simpleMessage("Family Saya"),
    "myFamilyRoomId": MessageLookupByLibrary.simpleMessage(
      "ID ruang family saya:",
    ),
    "myFeedback": MessageLookupByLibrary.simpleMessage("Umpan balik saya"),
    "myFollowRoomTitle": MessageLookupByLibrary.simpleMessage(
      "Kamar yang saya ikuti",
    ),
    "myGolds": MessageLookupByLibrary.simpleMessage("Koin saya"),
    "myIncome": MessageLookupByLibrary.simpleMessage("Pendapatan Saya"),
    "myIntegrations": MessageLookupByLibrary.simpleMessage("Integrasi saya"),
    "myLevel": MessageLookupByLibrary.simpleMessage("Level Saya"),
    "myLocalMusic": MessageLookupByLibrary.simpleMessage("Musik Lokal Saya"),
    "myLoveZone": MessageLookupByLibrary.simpleMessage("Zona Cinta Saya"),
    "myMusic": MessageLookupByLibrary.simpleMessage("Putar Musik"),
    "myNameIs": MessageLookupByLibrary.simpleMessage("Nama saya adalah"),
    "myPersonalId": MessageLookupByLibrary.simpleMessage("ID pribadi saya:"),
    "myRankPlace": m150,
    "myResults": MessageLookupByLibrary.simpleMessage("Hasil saya"),
    "myRoom": MessageLookupByLibrary.simpleMessage("Ruang saya"),
    "myRoomId": MessageLookupByLibrary.simpleMessage("ID kamar saya:"),
    "myTask": MessageLookupByLibrary.simpleMessage("Misi saya"),
    "myTheme": MessageLookupByLibrary.simpleMessage("Tema saya"),
    "myThemeSubtitle": m151,
    "myVoiceVerification": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara saya",
    ),
    "myWallet": MessageLookupByLibrary.simpleMessage("Dompet ku"),
    "myWord": m152,
    "myself": MessageLookupByLibrary.simpleMessage("Saya sendiri"),
    "mysteriousVisitor": MessageLookupByLibrary.simpleMessage(
      "Pengunjung Misterius",
    ),
    "mysteriousVisitorFunction": MessageLookupByLibrary.simpleMessage(
      "Fungsi pengunjung misterius aktif-nonaktif",
    ),
    "mysteryLetter": MessageLookupByLibrary.simpleMessage("Surat rahasia"),
    "mysteryLetterImTitle": MessageLookupByLibrary.simpleMessage(
      "Surat rahasia",
    ),
    "name": MessageLookupByLibrary.simpleMessage("Nama"),
    "nameContainsIllegal": m153,
    "nameEnteredTheRoom": m154,
    "natural": MessageLookupByLibrary.simpleMessage("Natural"),
    "naturalReverb": MessageLookupByLibrary.simpleMessage("Natural"),
    "nearby": MessageLookupByLibrary.simpleMessage("Sekitar"),
    "nearbyNoSettingDes": MessageLookupByLibrary.simpleMessage(
      "Aktifkan akses lokasi dan Winker akan merekomendasikan orang yang tepat disekitarmu",
    ),
    "nearbyNoSettingTitle": MessageLookupByLibrary.simpleMessage(
      "Lokasimu berada tidak diketahui.",
    ),
    "needToVerifyVoice": MessageLookupByLibrary.simpleMessage(
      "winker melakukan yang terbaik untuk memastikan anda memiliki lingkungan sosial nyata yang sehat, jadi harap verifikasi suara anda sekarang agar kami dapat mengetahui hal menarik dari anda!",
    ),
    "networkError": MessageLookupByLibrary.simpleMessage(
      "Kegagalan koneksi jaringan",
    ),
    "networkErrorAndReload": MessageLookupByLibrary.simpleMessage(
      "Kesalahan jaringan, coba lagi muat ulang.",
    ),
    "newFans": MessageLookupByLibrary.simpleMessage("Penggemar baru"),
    "newFriendsRequest": MessageLookupByLibrary.simpleMessage(
      "Permintaan teman baru",
    ),
    "newHandRewardContent": MessageLookupByLibrary.simpleMessage(
      "Beberapa hadiah ucapan untuk anda",
    ),
    "newMessages": m155,
    "newRound": MessageLookupByLibrary.simpleMessage("Ronde baru"),
    "newText": MessageLookupByLibrary.simpleMessage("Baru"),
    "newTheme": MessageLookupByLibrary.simpleMessage("Tema baru"),
    "newVersion": MessageLookupByLibrary.simpleMessage("Versi Baru :"),
    "newbieTasks": MessageLookupByLibrary.simpleMessage("Tugas Pemula"),
    "newbieTitle": MessageLookupByLibrary.simpleMessage(
      "Di awal perjalanan baru, Winker telah menyiapkan hadiah sambutan untuk Anda. Nikmati waktu Anda di Winker!",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Berikutnya"),
    "nextNoShow": MessageLookupByLibrary.simpleMessage(
      "Jangan tampilkan lain kali",
    ),
    "nextOne": MessageLookupByLibrary.simpleMessage("Berikutnya"),
    "nextRevisionDate": m156,
    "niceToMeetYou": MessageLookupByLibrary.simpleMessage(
      "Hai! Senang berkenalan dengan anda!",
    ),
    "niceToMeetYouInBioGuide": MessageLookupByLibrary.simpleMessage(
      "Hai, senang berkenalan denganmu!",
    ),
    "nickname": MessageLookupByLibrary.simpleMessage("Nama"),
    "nineteenCentury": MessageLookupByLibrary.simpleMessage("Abad ke-19"),
    "no": MessageLookupByLibrary.simpleMessage("Tidak"),
    "no1": MessageLookupByLibrary.simpleMessage("Nomor 1"),
    "noContent": MessageLookupByLibrary.simpleMessage(
      "Tidak ada hasil, silakan coba kata kunci lain.",
    ),
    "noContentHere": MessageLookupByLibrary.simpleMessage(
      "Tidak ada konten disini",
    ),
    "noConversation": MessageLookupByLibrary.simpleMessage(
      "Tidak ada percakapan",
    ),
    "noCouponsAvailable": MessageLookupByLibrary.simpleMessage(
      "Tidak ada kupon yang tersedia",
    ),
    "noCurrentRanking": MessageLookupByLibrary.simpleMessage(
      "Tidak ada peringkat saat ini",
    ),
    "noFeedbackYet": MessageLookupByLibrary.simpleMessage(
      "Belum ada umpan balik.",
    ),
    "noFirstStep": MessageLookupByLibrary.simpleMessage(
      "Anda belum mengambil langkah pertama",
    ),
    "noFollowers": MessageLookupByLibrary.simpleMessage("Tidak ada Pengikut"),
    "noFollowing": MessageLookupByLibrary.simpleMessage(
      "Tidak ada yang Mengikuti",
    ),
    "noFreeToday": MessageLookupByLibrary.simpleMessage(
      "Tidak ada kesempatan gratis hari ini",
    ),
    "noFriendsOnline": MessageLookupByLibrary.simpleMessage(
      "Tidak ada teman online",
    ),
    "noGps": MessageLookupByLibrary.simpleMessage("Gagal mendapatkan lokasi"),
    "noGpsRec": MessageLookupByLibrary.simpleMessage(
      "Tidak dapat merekomendasikan orang terdekat untukmu",
    ),
    "noLongerInRoom": MessageLookupByLibrary.simpleMessage(
      "Kamu tidak lagi berada di dalam ruangan",
    ),
    "noMore": MessageLookupByLibrary.simpleMessage("Tiada yang lebih banyak"),
    "noMoreCoupons": MessageLookupByLibrary.simpleMessage(
      "Tidak ada kupon yang lain",
    ),
    "noMoreFamily": MessageLookupByLibrary.simpleMessage(
      "Tidak ada lagi family",
    ),
    "noMoreRooms": MessageLookupByLibrary.simpleMessage(
      "Tidak ada ruangan lain",
    ),
    "noMoreSeat": MessageLookupByLibrary.simpleMessage(
      "Tidak ada kursi tambahan",
    ),
    "noMoreSuitableRoom": MessageLookupByLibrary.simpleMessage(
      "Tiada ruang lagi yang cocok, silakan subscribe untuk mengikuti event di bawah ini!",
    ),
    "noMoreTopic": MessageLookupByLibrary.simpleMessage(
      "Maaf, tidak ada Konten",
    ),
    "noMoreUsersInThisCity": MessageLookupByLibrary.simpleMessage(
      "Tidak ada lagi pengguna di kota ini.",
    ),
    "noNewFriendRequest": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memiliki permintaan pertemanan baru",
    ),
    "noNotifications": MessageLookupByLibrary.simpleMessage("Tiada notifikasi"),
    "noOneCanSpeak": MessageLookupByLibrary.simpleMessage(
      "Semua anggota dibisukan",
    ),
    "noOneCanSpeakTip": MessageLookupByLibrary.simpleMessage(
      "Setelah diaktifkan, hanya ketua family dan wakil ketua family bisa berbicara.",
    ),
    "noOneYouAreFollowing": MessageLookupByLibrary.simpleMessage(
      "Anda belum mengikuti pengguna siapa pun.",
    ),
    "noOut": m157,
    "noPeopleInRoom": MessageLookupByLibrary.simpleMessage(
      "Tidak ada siapa pun di ruangan",
    ),
    "noPermissionToListenVoice": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memiliki izin untuk mendengarkan verifikasi suara.",
    ),
    "noQualifiedFriends": MessageLookupByLibrary.simpleMessage(
      "Tidak ada teman yang memenuhi syarat",
    ),
    "noRank": MessageLookupByLibrary.simpleMessage("Tiada peringkat"),
    "noRemindMeAgain": MessageLookupByLibrary.simpleMessage(
      "Jangan ingatkan lagi",
    ),
    "noResult": MessageLookupByLibrary.simpleMessage(
      "Tidak ada hasil yang ditemukan",
    ),
    "noRoomCreateEvent": MessageLookupByLibrary.simpleMessage(
      "Saat ini tidak ada ruang, dan event ruang tidak dapat dibuat",
    ),
    "noRoomPkRecord": MessageLookupByLibrary.simpleMessage(
      "Belum ada catatan pertarungan kamar",
    ),
    "noSelectedThisRound": MessageLookupByLibrary.simpleMessage(
      "Tidak ada peserta yang dipilih untuk ronde ini",
    ),
    "noSubscribeEvent": MessageLookupByLibrary.simpleMessage(
      "Anda belum subscribe event apapun. Silakan kunjungi halaman event dan subscribe event yang Anda sukai!\n",
    ),
    "noSupporterOnThisRound": MessageLookupByLibrary.simpleMessage(
      "Tidak ada pendukung di babak ini.",
    ),
    "noTitleGifters": MessageLookupByLibrary.simpleMessage(
      "Tidak ada titel pemberi hadiah",
    ),
    "noVip": MessageLookupByLibrary.simpleMessage("TIADA VIP"),
    "noVisitors": MessageLookupByLibrary.simpleMessage("Tidak ada Pengunjung"),
    "noVotesForUndercover": MessageLookupByLibrary.simpleMessage(
      "Tiada suara yang diberikan pada ronde ini. Lanjutkan ronde berikutnya secara otomatis.",
    ),
    "nonFamilyMembersAreNotAllowedToEnter": MessageLookupByLibrary.simpleMessage(
      "Mode family telah diaktifkan di ruang ini. Yang bukan anggota family akan dikeluarkan dari ruang ini.",
    ),
    "nonFamilyMembersAreNotAllowedToEnterInThisRoom":
        MessageLookupByLibrary.simpleMessage(
          "Mode family telah diaktifkan di ruang ini. Yang bukan anggota family tidak bisa masuk ke ruang ini.",
        ),
    "noneFavorites": MessageLookupByLibrary.simpleMessage(
      "Anda belum menambahkan Stiker apa pun ke favorit anda.",
    ),
    "noneMyCollections": MessageLookupByLibrary.simpleMessage(
      "Anda tidak menambahkan koleksi apa pun.",
    ),
    "normal": MessageLookupByLibrary.simpleMessage("Biasa"),
    "normalLudoTips": MessageLookupByLibrary.simpleMessage(
      "Mode Normal, 4 Token, Item Ajaib.",
    ),
    "notAllowedCloseTheMic": MessageLookupByLibrary.simpleMessage(
      "Tidak diperbolehkan menutup mikrofon selama ronde deskripsi.",
    ),
    "notAllowedDuringGame": MessageLookupByLibrary.simpleMessage(
      "Tidak diperbolehkan selama game ini",
    ),
    "notAllowedKickGamerDuringGame": MessageLookupByLibrary.simpleMessage(
      "Tidak diperbolehkan untuk mengeluarkan gamer selama game",
    ),
    "notAllowedToSpeak": MessageLookupByLibrary.simpleMessage(
      "Tidak diperbolehkan untuk berbicara selama bukan ronde game Anda.",
    ),
    "notAvailable": MessageLookupByLibrary.simpleMessage("Tidak tersedia"),
    "notBeUsed": MessageLookupByLibrary.simpleMessage(
      "Tidak dapat digunakan pada waktu yang sama",
    ),
    "notConnectedWifi": MessageLookupByLibrary.simpleMessage(
      "Tidak terhubung ke Wi-Fi",
    ),
    "notFollowedAnyRooms": MessageLookupByLibrary.simpleMessage(
      "Anda belum ikuti ruangan apapun.",
    ),
    "notJoinedAnyRoom": MessageLookupByLibrary.simpleMessage(
      "Anda belum bergabung dengan ruangan mana pun.",
    ),
    "notMatchForHer": MessageLookupByLibrary.simpleMessage(
      "Dia bukan Matchmu hari ini, ayo coba mengobrol lagi besok!",
    ),
    "notMatchForHim": MessageLookupByLibrary.simpleMessage(
      "Dia bukan Matchmu hari ini, ayo coba mengobrol lagi besok!",
    ),
    "notMatchWith": MessageLookupByLibrary.simpleMessage(
      "Maaf, anda belum Match dengan ",
    ),
    "notMeetRuleJoinFamily": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memenuhi persyaratan berikut untuk bergabung dengan family ini:",
    ),
    "notNow": MessageLookupByLibrary.simpleMessage("Tidak sekarang"),
    "notObtained": MessageLookupByLibrary.simpleMessage("Tidak dapatkan"),
    "notOriginal": MessageLookupByLibrary.simpleMessage(
      "Konten ini tidak orisinil",
    ),
    "notOriginalHint": MessageLookupByLibrary.simpleMessage(
      "Jika memungkinkan, beri tahu kami pembuat aslinya dengan bukti agar kami dapat memverifikasinya lebih cepat.",
    ),
    "notRecommended": MessageLookupByLibrary.simpleMessage(
      "Tidak direkomendasikan",
    ),
    "notSale": MessageLookupByLibrary.simpleMessage("Tidak untuk dijual"),
    "notVipUseFunction": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat menggunakan fungsi ini. Harap aktifkan hak istimewa.",
    ),
    "notVoiceInput": MessageLookupByLibrary.simpleMessage(
      "Tidak ada suara Diinput, harap rekam pertanyaan anda di tempat yang tenang.",
    ),
    "notYet": MessageLookupByLibrary.simpleMessage("Lanjutkan"),
    "note": MessageLookupByLibrary.simpleMessage("Catatan"),
    "notice": MessageLookupByLibrary.simpleMessage("Pemberitahuan"),
    "noticeFollow": MessageLookupByLibrary.simpleMessage(
      "mulai mengikuti Anda",
    ),
    "noticeForUse": MessageLookupByLibrary.simpleMessage(
      "Ketentuan penggunaan",
    ),
    "noticeLike": MessageLookupByLibrary.simpleMessage("Menyukai pos Anda"),
    "noticeTitle": MessageLookupByLibrary.simpleMessage("Notifikasi momen"),
    "notification": MessageLookupByLibrary.simpleMessage("Notifikasi"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifikasi"),
    "notifyMe": MessageLookupByLibrary.simpleMessage("Beritahu saya"),
    "now": MessageLookupByLibrary.simpleMessage("Baru saja"),
    "nowForReal": MessageLookupByLibrary.simpleMessage("Sekarang"),
    "nowMode": MessageLookupByLibrary.simpleMessage("SEKARANG"),
    "numDays": m158,
    "numDiamonds": m159,
    "numGolds": m160,
    "numGoldsCountTimes": m161,
    "numOfRecipients": MessageLookupByLibrary.simpleMessage("Jumlah penerima:"),
    "numOfStarlight": MessageLookupByLibrary.simpleMessage("Jumlah Starlight"),
    "numRound": m162,
    "numStars": m163,
    "numSupporter": m164,
    "numTimesForFruity": m165,
    "numberOfDiamonds": MessageLookupByLibrary.simpleMessage("Jumlah Diamond"),
    "numberOfGems": MessageLookupByLibrary.simpleMessage("Jumlah Rubi"),
    "numberOfLuckyBags": MessageLookupByLibrary.simpleMessage(
      "Jumlah lucky bag:",
    ),
    "numberOfMic": MessageLookupByLibrary.simpleMessage("Jumlah Mikrofon"),
    "nw": MessageLookupByLibrary.simpleMessage("Baru"),
    "ofToday": MessageLookupByLibrary.simpleMessage(" hari ini"),
    "off": MessageLookupByLibrary.simpleMessage("Nonaktif"),
    "official": MessageLookupByLibrary.simpleMessage("Resmi"),
    "ok": MessageLookupByLibrary.simpleMessage("Oke"),
    "okay": MessageLookupByLibrary.simpleMessage("Oke"),
    "on": MessageLookupByLibrary.simpleMessage("Aktif"),
    "onMic": MessageLookupByLibrary.simpleMessage("Di Mic"),
    "online": MessageLookupByLibrary.simpleMessage("Aktif"),
    "onlineMostTip": MessageLookupByLibrary.simpleMessage(
      "Tampilkan maksimal 50 pengguna online.",
    ),
    "onlineUserCount": m166,
    "onlineUserList": MessageLookupByLibrary.simpleMessage(
      "Daftar pengguna online",
    ),
    "onlineUsersListOfVoiceRoom": MessageLookupByLibrary.simpleMessage(
      "Daftar pengguna online di ruangan",
    ),
    "only30SearchData": MessageLookupByLibrary.simpleMessage(
      "Hanya 30 data pencarian yang ditampilkan, silakan masukkan lebih banyak kata kunci",
    ),
    "onlyAdminCanPlayMusic": MessageLookupByLibrary.simpleMessage(
      "Hanya pemilik dan admin yang dapat memutar musik",
    ),
    "onlyApplication": MessageLookupByLibrary.simpleMessage("Hanya aplikasi"),
    "onlyByInvitation": MessageLookupByLibrary.simpleMessage(
      "Hanya melalui undangan",
    ),
    "onlyCalculatedDesignateGift": MessageLookupByLibrary.simpleMessage(
      "Hanya hadiah yang ditunjuk dihitung",
    ),
    "onlyCalculatedFamilyGift": m167,
    "onlyFamilyMembers": MessageLookupByLibrary.simpleMessage(
      "Hanya untuk anggota family",
    ),
    "onlyFamilyMembersCanEnterTheRoom": MessageLookupByLibrary.simpleMessage(
      "Hanya anggota family yang dapat memasuki ruang?",
    ),
    "onlyFriends": MessageLookupByLibrary.simpleMessage("Hanya teman"),
    "onlyFriendsCanChat": MessageLookupByLibrary.simpleMessage(
      "Anda belum membuka dan hanya teman yang bisa mengobrol",
    ),
    "onlyMessage": MessageLookupByLibrary.simpleMessage(
      "Hanya peringatan pesan",
    ),
    "onlyMessageMoment": MessageLookupByLibrary.simpleMessage(
      "Hanya peringatan pesan, momen & interaksi",
    ),
    "onlyPublishOneMoment": MessageLookupByLibrary.simpleMessage(
      "Anda hanya dapat mengunggah momen satu per satu, yakin ingin mengirim momen baru?",
    ),
    "onlyVipEmoji": MessageLookupByLibrary.simpleMessage(
      "Hanya pengguna vip yang dapat mengirim emoji ini",
    ),
    "onlyVipPurchase": MessageLookupByLibrary.simpleMessage(
      "Hanya vip yang dapat membelinya",
    ),
    "onlyWiredHeadsets": MessageLookupByLibrary.simpleMessage(
      "Hanya headset berkabel yang didukung",
    ),
    "ooops": MessageLookupByLibrary.simpleMessage("ups.."),
    "open": MessageLookupByLibrary.simpleMessage("Buka"),
    "openAddFriendsFunction": MessageLookupByLibrary.simpleMessage(
      "1. Buka fungsi menambahkan teman di pengaturan",
    ),
    "openBluetoothDesc": MessageLookupByLibrary.simpleMessage(
      "Aktifkan pengaturan Bluetooth dan selesaikan tugas untuk mendapatkan hadiah gratis.",
    ),
    "openBluetoothTitle": MessageLookupByLibrary.simpleMessage(
      "Buka pengaturan Bluetooth",
    ),
    "openBox": MessageLookupByLibrary.simpleMessage("Buka kotak"),
    "openFateBell": MessageLookupByLibrary.simpleMessage("Buka Lonceng Takdir"),
    "openItNow": MessageLookupByLibrary.simpleMessage("Bukanya sekarang > "),
    "openMuteAll": m168,
    "openMysteriousSetting": MessageLookupByLibrary.simpleMessage(
      "1. Buka pengunjung misterius di pengaturan",
    ),
    "openPositioning": MessageLookupByLibrary.simpleMessage("Posisi terbuka"),
    "openTreasure": MessageLookupByLibrary.simpleMessage("buka harta"),
    "opened": MessageLookupByLibrary.simpleMessage("Dibuka"),
    "openedNum": m169,
    "operation": MessageLookupByLibrary.simpleMessage("Operasi"),
    "operationTooFrequent": MessageLookupByLibrary.simpleMessage(
      "Operasi terlalu sering, coba lagi nanti.",
    ),
    "opponentGets": MessageLookupByLibrary.simpleMessage("Lawan mendapat"),
    "oppositeSexPeople": MessageLookupByLibrary.simpleMessage(
      "Ronde berikutnya dapat dimulai setelah setidaknya ada seorang dari lawan jenis baru",
    ),
    "oppositeVoice": MessageLookupByLibrary.simpleMessage("Suara Lawan"),
    "optionTitleNum": m170,
    "optional": MessageLookupByLibrary.simpleMessage("Pilihan"),
    "or": MessageLookupByLibrary.simpleMessage("atau"),
    "original": MessageLookupByLibrary.simpleMessage("Asli"),
    "other": MessageLookupByLibrary.simpleMessage("Lain-lain"),
    "otherCannotFollowYou": MessageLookupByLibrary.simpleMessage(
      "2.Pengguna lain tidak dapat mengikuti Anda ke kamar Anda melalui profil",
    ),
    "otherLoginWays": MessageLookupByLibrary.simpleMessage("Cara masuk lain"),
    "otherPopularTests": MessageLookupByLibrary.simpleMessage(
      "Tes populer lainnya",
    ),
    "otherUsersWillSendYou": MessageLookupByLibrary.simpleMessage(
      "2. Pengguna lain akan mengirimi Anda permintaan pertemanan",
    ),
    "otherViewers": m171,
    "others": MessageLookupByLibrary.simpleMessage("Lainnya"),
    "our": MessageLookupByLibrary.simpleMessage("Kami"),
    "out": MessageLookupByLibrary.simpleMessage("KELUAR"),
    "overCallMatch": MessageLookupByLibrary.simpleMessage(
      "Saat ini sedikit pengguna. Silakan coba lagi nanti.",
    ),
    "overseasCountries": MessageLookupByLibrary.simpleMessage(
      "Negara/Wilayah Luar Negeri",
    ),
    "owner": MessageLookupByLibrary.simpleMessage("Pemilik"),
    "package": MessageLookupByLibrary.simpleMessage("Paket"),
    "paintGuideTip": MessageLookupByLibrary.simpleMessage(
      "Gunakan jari dan menggambar kata-kata Anda!",
    ),
    "painter": MessageLookupByLibrary.simpleMessage("Pelukis"),
    "painting": MessageLookupByLibrary.simpleMessage("Lukisan"),
    "paintingDesc": MessageLookupByLibrary.simpleMessage(
      "Ubah selfie anda menjadi lukisan cantik!",
    ),
    "participant": MessageLookupByLibrary.simpleMessage("Peserta"),
    "participationRecord": MessageLookupByLibrary.simpleMessage(
      "Catatan Partisipasi",
    ),
    "party": MessageLookupByLibrary.simpleMessage("Pesta"),
    "partyDur": m172,
    "partyListEmptyTip": MessageLookupByLibrary.simpleMessage(
      "Buat ruangan dan undang teman untuk mengobrol",
    ),
    "partyReverb": MessageLookupByLibrary.simpleMessage("Pesta"),
    "pass": MessageLookupByLibrary.simpleMessage("Lulus"),
    "passGetMatch": MessageLookupByLibrary.simpleMessage(
      "Prioritas pencocokan dapat diperoleh setelah lolos verifikasi. Apakah anda yakin untuk keluar ?",
    ),
    "passNoOneKnow": MessageLookupByLibrary.simpleMessage(
      "Jika anda tidak Menyukainya, lewati saja. Tidak ada yang akan tahu.",
    ),
    "passingVerification": MessageLookupByLibrary.simpleMessage(
      "Lolos verifikasi untuk mengobrol dengannya~",
    ),
    "passionate": MessageLookupByLibrary.simpleMessage("Minat"),
    "password": m173,
    "passwordWrong": MessageLookupByLibrary.simpleMessage("Kata sandi salah"),
    "past": MessageLookupByLibrary.simpleMessage("Masa lalu"),
    "patriarch": MessageLookupByLibrary.simpleMessage("Ketua Family"),
    "pauseMusicWhileTuringOffMic": MessageLookupByLibrary.simpleMessage(
      "Menjeda musik saat mematikan mikrofon",
    ),
    "pausedVideo": MessageLookupByLibrary.simpleMessage(
      "pemutaran video ini dijeda",
    ),
    "people": MessageLookupByLibrary.simpleMessage("Orang"),
    "peopleChattingNow": MessageLookupByLibrary.simpleMessage(
      "Orang-orang mengobrol sekarang",
    ),
    "performanceEndAdvance": m174,
    "performanceTime": MessageLookupByLibrary.simpleMessage(
      "Waktu pertunjukan（menit）",
    ),
    "performerList": MessageLookupByLibrary.simpleMessage("Datar pemain"),
    "performing": MessageLookupByLibrary.simpleMessage("tampil"),
    "performingSwitchMode": MessageLookupByLibrary.simpleMessage(
      "Sedang tampil sekarang. Beralih mode kamar ditolak",
    ),
    "permanent": MessageLookupByLibrary.simpleMessage("Permanen"),
    "permission": MessageLookupByLibrary.simpleMessage("Permit"),
    "personal": MessageLookupByLibrary.simpleMessage("personal"),
    "personalIncome": MessageLookupByLibrary.simpleMessage(
      "Pendapatan Pribadi",
    ),
    "personalInfo": MessageLookupByLibrary.simpleMessage("Informasi pribadi"),
    "personalInformation": MessageLookupByLibrary.simpleMessage(
      "Halaman informasi pribadi",
    ),
    "personalityTest": MessageLookupByLibrary.simpleMessage("Tes Kepribadian"),
    "personalityTests": MessageLookupByLibrary.simpleMessage("Tes kepribadian"),
    "personalityTestsDetail": MessageLookupByLibrary.simpleMessage(
      "Temukan teman yang cocok dengan hobi Anda",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Nomor telepon"),
    "photoToAvatar": MessageLookupByLibrary.simpleMessage("Foto ke Avatar"),
    "piano": MessageLookupByLibrary.simpleMessage("Piano"),
    "pick": MessageLookupByLibrary.simpleMessage("Pilih"),
    "pickBottleAndTalk": MessageLookupByLibrary.simpleMessage(
      "Klik ini, ambil botolnya dan mengobrollah dengan mereka.",
    ),
    "pictureNotExist": MessageLookupByLibrary.simpleMessage(
      "Gambar ini tidak ada",
    ),
    "pictureUploaded": MessageLookupByLibrary.simpleMessage(
      "Gambar telah diunggah",
    ),
    "picturesTips": MessageLookupByLibrary.simpleMessage(
      "Terlalu banyak sentuhan pada gambar!",
    ),
    "pin": MessageLookupByLibrary.simpleMessage("Semat"),
    "pinChat": MessageLookupByLibrary.simpleMessage("Sematkan Obrolan"),
    "pingName": MessageLookupByLibrary.simpleMessage("Ping!"),
    "pk": MessageLookupByLibrary.simpleMessage("Pertarungan"),
    "pkEndedTips": MessageLookupByLibrary.simpleMessage(
      "Pertarungan berakhir imbang",
    ),
    "pkIsNotFinished": MessageLookupByLibrary.simpleMessage(
      "Pertarungan belum selesai",
    ),
    "pkMatching": MessageLookupByLibrary.simpleMessage("Mencocokkan"),
    "pkPoint": MessageLookupByLibrary.simpleMessage("Poin pertarungan:"),
    "pkResult": MessageLookupByLibrary.simpleMessage("Hasil Pertarungan"),
    "pkWinner": MessageLookupByLibrary.simpleMessage("Pemenang pertarungan"),
    "plagiarism": MessageLookupByLibrary.simpleMessage("Plagiat"),
    "play": MessageLookupByLibrary.simpleMessage("Bermain"),
    "playAgain": MessageLookupByLibrary.simpleMessage("Main lagi"),
    "playAndPause": MessageLookupByLibrary.simpleMessage("PUTAR DAN JEDA"),
    "playCenter": MessageLookupByLibrary.simpleMessage("Pusat bermain"),
    "playDefaultMusic": MessageLookupByLibrary.simpleMessage(
      "Putar musik default",
    ),
    "playError": MessageLookupByLibrary.simpleMessage("Kesalahan pemutaran"),
    "playInOrder": MessageLookupByLibrary.simpleMessage("Putar Berurutan"),
    "playQuestionWithYou": m175,
    "playRandomly": MessageLookupByLibrary.simpleMessage("Putar Secara Acak"),
    "playThisVideo": MessageLookupByLibrary.simpleMessage("Putar video ini"),
    "playWithFriend": MessageLookupByLibrary.simpleMessage(
      "Klik dan Mainlah dengan teman anda!",
    ),
    "playerNotReady": MessageLookupByLibrary.simpleMessage(
      "Pemain tidak siap atau tidak cukup",
    ),
    "playerPosition": m176,
    "playing": MessageLookupByLibrary.simpleMessage("Sedang putar"),
    "playingGame": m177,
    "playingVoiceRoom": MessageLookupByLibrary.simpleMessage("Di ruang suara"),
    "pleaseChooseAnswer": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih jawaban anda",
    ),
    "pleaseChooseLengthOfPurchase": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih masa berlaku",
    ),
    "pleaseChooseLove": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih jawaban anda",
    ),
    "pleaseConnectWifi": MessageLookupByLibrary.simpleMessage(
      "Harap sambungkan ponsel Anda ke Wi-Fi terlebih dahulu",
    ),
    "pleaseContactCustomerToDisbandFamily": MessageLookupByLibrary.simpleMessage(
      "Ketua family tidak bisa keluar langsung. Silakan hubungi layanan pelanggan resmi untuk proses lebih lanjut.",
    ),
    "pleaseCreateRoom": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memiliki kamar saat ini, silakan buat kamar Anda sendiri.",
    ),
    "pleaseDescribe": MessageLookupByLibrary.simpleMessage(
      "Tolong jelaskan masalah yang anda alami...",
    ),
    "pleaseDescribeReport": MessageLookupByLibrary.simpleMessage(
      "Harap deskripsikan masalah yang ingin anda laporkan.",
    ),
    "pleaseEnterChar": m178,
    "pleaseEnterRoomPw": MessageLookupByLibrary.simpleMessage(
      "Silahkan masukkan kata sandi dengan 4 digit",
    ),
    "pleaseEnterTitle": MessageLookupByLibrary.simpleMessage(
      "Silakan masukkan gelar.",
    ),
    "pleaseExitRoom": MessageLookupByLibrary.simpleMessage(
      "Harap keluar dari ruangan dulu",
    ),
    "pleaseGiveMeAKey": MessageLookupByLibrary.simpleMessage(
      "Tolong berikan satu kunci agar saya dapat membuka kotak harta karun ini!",
    ),
    "pleaseGiveMeKey": MessageLookupByLibrary.simpleMessage(
      "Tolong berikan kunci agar saya dapat membuka kotak harta karun ini!",
    ),
    "pleaseInputAgain": MessageLookupByLibrary.simpleMessage(
      "Kesalahan jaringan, silakan masukkan lagi",
    ),
    "pleaseKindlyComment": MessageLookupByLibrary.simpleMessage(
      "Silakan berkomentar",
    ),
    "pleaseRateThisCall": MessageLookupByLibrary.simpleMessage(
      "Silakan menilai panggilan ini",
    ),
    "pleaseReadAndAgree": MessageLookupByLibrary.simpleMessage(
      "Harap baca dan setujui",
    ),
    "pleaseReadTheWords": MessageLookupByLibrary.simpleMessage(
      "Baca teks Dibawah ini:",
    ),
    "pleaseRecord": MessageLookupByLibrary.simpleMessage(
      "Harap tekan tombol rekam, lalu baca kata-kata di atas dengan lantang.",
    ),
    "pleaseSelectReason": MessageLookupByLibrary.simpleMessage(
      "Harap pilih alasan untuk laporan\nKami tidak akan memberi tahu pengguna ini",
    ),
    "pleaseSelectUser": MessageLookupByLibrary.simpleMessage(
      "Silahkan pilih pengguna yang ingin dikirim",
    ),
    "pleaseSetUpYourCity": MessageLookupByLibrary.simpleMessage(
      "Silakan atur kota Anda agar teman-teman lokal Anda lebih mudah menemukannya.",
    ),
    "pleaseTakeMicAndSpeak": MessageLookupByLibrary.simpleMessage(
      "Silakan ambil mikrofon dan berbicara.",
    ),
    "pleaseTryAgain": MessageLookupByLibrary.simpleMessage(
      "Masalah jaringan silakan coba lagi",
    ),
    "pleaseWaitApproval": MessageLookupByLibrary.simpleMessage(
      "Harap tunggu persetujuan",
    ),
    "pointsChange": m179,
    "pokedYou": MessageLookupByLibrary.simpleMessage("memanggilmu!"),
    "politicsRelated": MessageLookupByLibrary.simpleMessage("terkait politik"),
    "popular": MessageLookupByLibrary.simpleMessage("Popular"),
    "popularity": MessageLookupByLibrary.simpleMessage("Popularitas"),
    "popularityList": MessageLookupByLibrary.simpleMessage("Hadiah Diterima"),
    "popularityRank": MessageLookupByLibrary.simpleMessage("Peringkat"),
    "porn": MessageLookupByLibrary.simpleMessage("Porno"),
    "post": MessageLookupByLibrary.simpleMessage("Memposting"),
    "postMoment": MessageLookupByLibrary.simpleMessage(
      "Momen berkualitas tinggi dapat memungkinkan lebih banyak balasan dari teman lain, <h>",
    ),
    "postMoments": MessageLookupByLibrary.simpleMessage("memposting momen"),
    "postMoreMoments": MessageLookupByLibrary.simpleMessage(
      "memposting lebih banyak momen untuk mendapatkan lebih banyak perhatian!",
    ),
    "postNow": MessageLookupByLibrary.simpleMessage(
      "tambahkan unggahan baru sekarang!",
    ),
    "postSuccess": MessageLookupByLibrary.simpleMessage("Mengunggahberhasil"),
    "postTipGourmet": MessageLookupByLibrary.simpleMessage(
      "Check in di restoran rekomendasi baru-baru ini",
    ),
    "postTipJournal": MessageLookupByLibrary.simpleMessage(
      "Check in di restoran rekomendasi baru-baru ini",
    ),
    "postTipPet": MessageLookupByLibrary.simpleMessage(
      "Check in di restoran rekomendasi baru-baru ini",
    ),
    "postVoiceMoment": MessageLookupByLibrary.simpleMessage(
      "Anda dapat Memposting voice note dalam beberapa saat sekarang!",
    ),
    "postWhichTheme": MessageLookupByLibrary.simpleMessage(
      "Posting ke tema pilihan",
    ),
    "powerBar": MessageLookupByLibrary.simpleMessage("Batang penggerak"),
    "premium": MessageLookupByLibrary.simpleMessage("Premium"),
    "premiumAvatarFrameDesc": MessageLookupByLibrary.simpleMessage(
      "Dapatkan lebih banyak perhatian dengan bingkai Premium khusus Anda",
    ),
    "premiumBadge": MessageLookupByLibrary.simpleMessage("Medali Premium"),
    "premiumSettings": MessageLookupByLibrary.simpleMessage(
      "Pengaturan premium",
    ),
    "prev": MessageLookupByLibrary.simpleMessage("Sebelumnya"),
    "preventFollowRoom": MessageLookupByLibrary.simpleMessage(
      "Hindari ikut ke kamar",
    ),
    "preventFollowRoomDesc": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mencegah orang lain mengikuti Anda ke kamar.",
    ),
    "preventFollowingIntoRoom": MessageLookupByLibrary.simpleMessage(
      "Hindari ikut ke kamar",
    ),
    "preview": MessageLookupByLibrary.simpleMessage("pratinjau"),
    "previous": MessageLookupByLibrary.simpleMessage("Sebelumnya"),
    "priorityReport": MessageLookupByLibrary.simpleMessage(
      "Pelaporan prioritas",
    ),
    "priorityReportDesc": MessageLookupByLibrary.simpleMessage(
      "Layanan pelanggan akan memproses informasi laporan Anda lebih cepat",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Kebijakan  privasi"),
    "privacyProblem": MessageLookupByLibrary.simpleMessage("Masalah privasi"),
    "private": MessageLookupByLibrary.simpleMessage("Pribadi"),
    "privateFlights": MessageLookupByLibrary.simpleMessage(
      "Penerbangan pribadi",
    ),
    "privatePhotoTipHelp": MessageLookupByLibrary.simpleMessage(
      "Anda dapat melihat foto pribadi satu sama lain hanya ketika keintiman Anda mencapai 10~.Obrolan teks dan hadiah dapat meningkatkan keintiman Anda",
    ),
    "privatePhotos": MessageLookupByLibrary.simpleMessage("Foto pribadi"),
    "privileged": MessageLookupByLibrary.simpleMessage("Hak Istimewa"),
    "privileges": MessageLookupByLibrary.simpleMessage("Hak Istimewa"),
    "privilegesTitle": m180,
    "probabilityGiftTitle": MessageLookupByLibrary.simpleMessage(
      "Selamat atas keluarnya hadiah Anda：",
    ),
    "probabilityGiftWinTip": m181,
    "problemDesc": MessageLookupByLibrary.simpleMessage("Deskripsi masalah"),
    "processingRequest": MessageLookupByLibrary.simpleMessage(
      "Kami sedang Memproses permintaan anda",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("Profil"),
    "profileChattingInRoom": m182,
    "promotingOtherApps": MessageLookupByLibrary.simpleMessage(
      "mempromosikan aplikasi lain",
    ),
    "proposal": MessageLookupByLibrary.simpleMessage("Lamaran"),
    "proposalDesc": MessageLookupByLibrary.simpleMessage(
      "Kedua pihak bisa melamar dengan keintiman 5.000. Anda bisa meningkatkan keakraban dengan mengobrol atau memberi hadiah.",
    ),
    "proposalMsg": MessageLookupByLibrary.simpleMessage("Pesan"),
    "proposalPaperDesc": MessageLookupByLibrary.simpleMessage(
      "Bagi dunia, kamu adalah seorang; tetapi bagi seseorang, kamu adalah seluruh dunianya.",
    ),
    "proposalSendWait": m183,
    "proposalVowsHint": MessageLookupByLibrary.simpleMessage(
      "Tuliskan kata-kata untuk cinta Anda...",
    ),
    "proposalVowsTitle": MessageLookupByLibrary.simpleMessage("Buat sumpah"),
    "propose": MessageLookupByLibrary.simpleMessage("Lamar"),
    "proposeMsgDesc": m184,
    "proposeTopic": MessageLookupByLibrary.simpleMessage(
      "rekomendasikan topik anda kepada kami",
    ),
    "props": MessageLookupByLibrary.simpleMessage("Alat"),
    "protectYourInfo": MessageLookupByLibrary.simpleMessage(
      "Kami akan melindungi privasi dan informasi pribadi Anda.",
    ),
    "protectYourPrivacy": MessageLookupByLibrary.simpleMessage(
      "Jangan khawatir, kami pastikan akan melindungi privasi dan informasi anda.",
    ),
    "proxyErr": MessageLookupByLibrary.simpleMessage(
      "Harap untuk memastikan kamu tidak menggunakan proxy.",
    ),
    "public": MessageLookupByLibrary.simpleMessage("Publik"),
    "publishTitleHintText": MessageLookupByLibrary.simpleMessage(
      "Masukkan titel",
    ),
    "punch": MessageLookupByLibrary.simpleMessage("Tos"),
    "punchGame": MessageLookupByLibrary.simpleMessage("Game Tos"),
    "punchGameFrequent": MessageLookupByLibrary.simpleMessage(
      "Terlalu sering mengundang, harap tunggu sebentar.",
    ),
    "punchJoinTitle": MessageLookupByLibrary.simpleMessage(
      "Temanmu mengajak bermain Tebakan Tos",
    ),
    "punchLost": MessageLookupByLibrary.simpleMessage(
      "😅Kamu kalah, menunggu pihak lain untuk memilih pertanyaan...",
    ),
    "punchQuestionTitle": m185,
    "punchQuick": MessageLookupByLibrary.simpleMessage("Q&A Undangan"),
    "punchReceive": MessageLookupByLibrary.simpleMessage(
      "Pihak lain menerima undangan \'Q&A Tebakan Tos\'.",
    ),
    "punchReq": MessageLookupByLibrary.simpleMessage(
      "Mengirim undangan untuk \'Q&A Tebakan Tos\' dan menunggu pihak lain untuk bergabung...",
    ),
    "punish": MessageLookupByLibrary.simpleMessage("Hukuman"),
    "punishLevel": MessageLookupByLibrary.simpleMessage("Level Hukuman"),
    "punishSelect": MessageLookupByLibrary.simpleMessage("Pilih Hukuman"),
    "punishSilentRemaining": MessageLookupByLibrary.simpleMessage(
      "Kamu telah dilarang berbicara di layar publik oleh administrator",
    ),
    "punishedInRound": MessageLookupByLibrary.simpleMessage(
      "Dihukum di ronde ini",
    ),
    "purchase": MessageLookupByLibrary.simpleMessage("Beli"),
    "purchaseSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Pembelian berhasil",
    ),
    "purchasedIt": MessageLookupByLibrary.simpleMessage(
      "Anda telah membelinya!",
    ),
    "purchasingWait": MessageLookupByLibrary.simpleMessage(
      "Sedang membeli... Harap tunggu",
    ),
    "quantityShortage": MessageLookupByLibrary.simpleMessage(
      "Kekurangan hadiah",
    ),
    "queryOneMonth": MessageLookupByLibrary.simpleMessage(
      "Hanya catatan kueri dalam 1 bulan yang dapat didukung",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("Dengan cepat"),
    "quicklyLudoTips": MessageLookupByLibrary.simpleMessage(
      "Mode Cepat, 1 Token, Item Ajaib.",
    ),
    "quit": MessageLookupByLibrary.simpleMessage("Berhenti"),
    "quitGroup": MessageLookupByLibrary.simpleMessage("Keluar grup"),
    "quizGuideText": MessageLookupByLibrary.simpleMessage(
      "Ikuti tes untuk menemukan pasangan ideal anda berdasarkan hasil anda",
    ),
    "quizProvided": MessageLookupByLibrary.simpleMessage(
      "Kuis disediakan oleh penyedia konten pihak ketiga.",
    ),
    "quizShare": MessageLookupByLibrary.simpleMessage(
      "Hai~ bestie! Ikuti tes kepribadian sekarang untuk menemukan teman yang cocok dengan hasilmu !",
    ),
    "racism": MessageLookupByLibrary.simpleMessage("rasisme"),
    "random": MessageLookupByLibrary.simpleMessage("Acak"),
    "rank": MessageLookupByLibrary.simpleMessage("Ranking"),
    "rank1st": MessageLookupByLibrary.simpleMessage("1"),
    "rank2nd": MessageLookupByLibrary.simpleMessage("2"),
    "rank3rd": MessageLookupByLibrary.simpleMessage("3"),
    "rankGlobalBanner": m186,
    "rankList": MessageLookupByLibrary.simpleMessage("Daftar peringkat"),
    "ranking": MessageLookupByLibrary.simpleMessage("Peringkat"),
    "rateApp": MessageLookupByLibrary.simpleMessage(
      "Silakan beri peringkat aplikasi kami!",
    ),
    "reJoinFamilyTip": MessageLookupByLibrary.simpleMessage(
      "Setelah keluar dari family, Anda hanya dapat bergabung kembali setelah 24 jam.",
    ),
    "read": MessageLookupByLibrary.simpleMessage("Baca"),
    "ready": MessageLookupByLibrary.simpleMessage("Siap"),
    "readyGrab": MessageLookupByLibrary.simpleMessage("Siap merebut"),
    "readyToPerform": MessageLookupByLibrary.simpleMessage(
      "Siap untuk tampil!",
    ),
    "realPersonVerified": MessageLookupByLibrary.simpleMessage(
      "Orang asli diverifikasi",
    ),
    "receive": MessageLookupByLibrary.simpleMessage("Menerima"),
    "receiveDiamonds": MessageLookupByLibrary.simpleMessage("Terima diamonds"),
    "receiveNewMsg": MessageLookupByLibrary.simpleMessage(
      "Anda menerima pesan baru",
    ),
    "received": MessageLookupByLibrary.simpleMessage("Menerima"),
    "receivedABottle": MessageLookupByLibrary.simpleMessage("Botol diterima"),
    "receivedBottle": MessageLookupByLibrary.simpleMessage(
      "Anda menerima botol",
    ),
    "receivedBottleFrom": MessageLookupByLibrary.simpleMessage(
      "Anda menerima botol dari ",
    ),
    "receivedGifts": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "receivedRankExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat berdasar pada nilai diamond dari hadiah yang anda terima",
    ),
    "receiverCharmAddNumExp": m187,
    "receiverNumRubies": m188,
    "recently": MessageLookupByLibrary.simpleMessage("Terkini"),
    "recentlyLoveZoneGifts": MessageLookupByLibrary.simpleMessage(
      "Hadiah zona cinta terbaru",
    ),
    "recentlyRoomEmpty": MessageLookupByLibrary.simpleMessage(
      "Anda belum bergabung dengan ruangan mana pun.",
    ),
    "recentlyUsed": MessageLookupByLibrary.simpleMessage(
      "Digunakan akhir-akhir ini",
    ),
    "recharge": MessageLookupByLibrary.simpleMessage("Isi ulang"),
    "rechargeNotOpen": MessageLookupByLibrary.simpleMessage(
      "Layanan isi ulang dalam APP belum dibuka.",
    ),
    "rechargeNow": MessageLookupByLibrary.simpleMessage("Isi ulang sekarang"),
    "rechargeSucceeds": MessageLookupByLibrary.simpleMessage(
      "Pengisi ulang berhasil",
    ),
    "rechargeTip": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih jumlah isi ulang:",
    ),
    "recommend": MessageLookupByLibrary.simpleMessage("Rekomendasikan"),
    "recommendFamilyTitle": MessageLookupByLibrary.simpleMessage(
      "Beberapa family direkomendasikan untuk Anda",
    ),
    "recommendOtherRoom": MessageLookupByLibrary.simpleMessage(
      "Merekomendasikan ruangan lain untuk anda",
    ),
    "recommendTopic": MessageLookupByLibrary.simpleMessage(
      "Topik yang Direkomendasikan",
    ),
    "recommendUser": MessageLookupByLibrary.simpleMessage(
      "Pengguna yang direkomendasikan",
    ),
    "record": MessageLookupByLibrary.simpleMessage("Catatan"),
    "recordAtLeastSeconds": m189,
    "recordQueTips": MessageLookupByLibrary.simpleMessage(
      "Rekam pertanyaan anda untuk membuka kunci Obrolan, lalu membuat pencocokan, dan anda akan mendapatkan teman baru!",
    ),
    "recordQuestions": MessageLookupByLibrary.simpleMessage(
      "Rekam pertanyaan saya",
    ),
    "recordUnlockChat": MessageLookupByLibrary.simpleMessage(
      "Buka kunci Obrolan",
    ),
    "recordVoiceGuidContent": MessageLookupByLibrary.simpleMessage(
      "Klik untuk membaca kata-kata dengan suara anda",
    ),
    "recordYourQuestion": MessageLookupByLibrary.simpleMessage(
      "Rekam pertanyaan anda",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Merekam..."),
    "recordingWillStop": MessageLookupByLibrary.simpleMessage(
      "Rekaman akan berhenti",
    ),
    "records": MessageLookupByLibrary.simpleMessage("Catatan"),
    "recreateAgain": MessageLookupByLibrary.simpleMessage(
      "Anda dapat membuat ulang dengan melengkapi profil lagi.",
    ),
    "recruit": MessageLookupByLibrary.simpleMessage("Rekrut"),
    "redPackDetail": MessageLookupByLibrary.simpleMessage(
      "Dapatkan detail angpao",
    ),
    "redPackExpired": MessageLookupByLibrary.simpleMessage(
      "Amplop Angpao telah kedaluwarsa",
    ),
    "redPackGet": MessageLookupByLibrary.simpleMessage("Cek"),
    "redPackResultEmptyTip": MessageLookupByLibrary.simpleMessage(
      "Angpao kosong, tanganmu tidak cukup cepat! Lebih cepat lain kali!",
    ),
    "redPackResultGetMessage": MessageLookupByLibrary.simpleMessage(
      "Kamu mengambil dari angpao saat ini",
    ),
    "redPackResultTip": MessageLookupByLibrary.simpleMessage(
      "Ingat! Semakin cepat kecepatan tanganmu, semakin banyak diamond yang akan kamu dapatkan!",
    ),
    "redPackTitle": MessageLookupByLibrary.simpleMessage("Bonus panas dipicu"),
    "refreshComplete": MessageLookupByLibrary.simpleMessage(" segarkan ulang"),
    "refreshFailed": MessageLookupByLibrary.simpleMessage("refresh gagal"),
    "refuse": MessageLookupByLibrary.simpleMessage("Menolak"),
    "refuseFamilyApply": MessageLookupByLibrary.simpleMessage(
      "menolak permintaan family Anda.",
    ),
    "refuseFamilyInvitation": MessageLookupByLibrary.simpleMessage(
      "menolak undangan family Anda.",
    ),
    "regAccessWinker": MessageLookupByLibrary.simpleMessage("Akses Winker"),
    "regAnsNum": m190,
    "regAnsRemaining": m191,
    "regAnswer": MessageLookupByLibrary.simpleMessage("Jawaban Tes"),
    "regAvatar": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih avatar anda.",
    ),
    "regBirthSame": m192,
    "regBirthday": m193,
    "regCardBirthdayTip": MessageLookupByLibrary.simpleMessage(
      "Kunjungan pertama ke Winker",
    ),
    "regCompleteInfo": MessageLookupByLibrary.simpleMessage(
      "Selanjutnya, kami membutuhkan anda untuk memperbaiki informasi pribadi anda.",
    ),
    "regGender": m194,
    "regGenderFemale": MessageLookupByLibrary.simpleMessage("👩Wanita"),
    "regGenderMale": MessageLookupByLibrary.simpleMessage("👱Pria"),
    "regGenderSexTip": m195,
    "regInvitationCode": MessageLookupByLibrary.simpleMessage("Kode Undangan"),
    "regMr": MessageLookupByLibrary.simpleMessage("Tn."),
    "regMs": MessageLookupByLibrary.simpleMessage("Nn."),
    "regMyInvitationCode": MessageLookupByLibrary.simpleMessage(
      "Kode Undangan Saya x",
    ),
    "regNiceNickname": m196,
    "regNickInputHint": MessageLookupByLibrary.simpleMessage(
      "Masukkan nama panggilan anda",
    ),
    "regNickname": MessageLookupByLibrary.simpleMessage(
      "Silakan masukkan nama panggilan Anda.",
    ),
    "regNotPass": MessageLookupByLibrary.simpleMessage(
      "🙁Maaf, anda belum lulus tes ini.",
    ),
    "regPassed": MessageLookupByLibrary.simpleMessage(
      "👏Selamat kamu telah lolos tes, dan sebentar lagi kamu bisa bertemu dengan teman-teman di Winker.",
    ),
    "regRight": MessageLookupByLibrary.simpleMessage("Benar"),
    "regSuccess": m197,
    "regTestTip": MessageLookupByLibrary.simpleMessage(
      "Winker saat ini sedang dalam pengujian internal, dan untuk menjaga suasana komunitas yang positif, kami telah menyiapkan beberapa pertanyaan selama pendaftaran.\nAnda dapat mengakses Winker dengan dua cara：\n👇👇🏻👇🏾",
    ),
    "regVerificationPassed": MessageLookupByLibrary.simpleMessage(
      "Selamat🎉🎉🎉, verifikasi berhasil!",
    ),
    "regWelcome": MessageLookupByLibrary.simpleMessage(
      "Hai, aku Winker!\nSaya akan membantu anda menyesuaikan diri dengan cepat dan mencari teman baru di sekitar sini.",
    ),
    "region": MessageLookupByLibrary.simpleMessage("Wilayah"),
    "reject": MessageLookupByLibrary.simpleMessage("tolak"),
    "rejectCapital": MessageLookupByLibrary.simpleMessage("Tolak"),
    "rejectPkTips": MessageLookupByLibrary.simpleMessage(
      " menolak undangan pertarungan kamar Anda.",
    ),
    "rejectReview": MessageLookupByLibrary.simpleMessage("Ditolak"),
    "rejectYourDivorce": MessageLookupByLibrary.simpleMessage(
      "perceraian Anda ditolak.",
    ),
    "rejected": MessageLookupByLibrary.simpleMessage("Ditolak"),
    "related": MessageLookupByLibrary.simpleMessage("Terkait"),
    "religionRelated": MessageLookupByLibrary.simpleMessage("terkait agama"),
    "reload": MessageLookupByLibrary.simpleMessage("Muat ulang"),
    "remaining": m198,
    "remarks": MessageLookupByLibrary.simpleMessage("Keterangan"),
    "remove": MessageLookupByLibrary.simpleMessage("Hapus"),
    "removeAdmin": MessageLookupByLibrary.simpleMessage("Hapus admin"),
    "removeFromFamily": MessageLookupByLibrary.simpleMessage(
      "Hapus dari family",
    ),
    "removeUserOutFamily": m199,
    "replied": MessageLookupByLibrary.simpleMessage("Dibalas"),
    "reply": MessageLookupByLibrary.simpleMessage("Balas"),
    "replyToUnlockMore": MessageLookupByLibrary.simpleMessage(
      "Balas percakapan untuk membuka kunci lebih banyak!",
    ),
    "report": MessageLookupByLibrary.simpleMessage("Lapor"),
    "reportAttachmentContent": MessageLookupByLibrary.simpleMessage(
      "Opsional, ukuran video tidak melebihi 20M",
    ),
    "reportAvatar": MessageLookupByLibrary.simpleMessage("Laporkan avatar"),
    "reportCenter": MessageLookupByLibrary.simpleMessage("Pusat Laporan"),
    "reportDescribe": MessageLookupByLibrary.simpleMessage(
      "Deskripsikan masalah yang sedang anda alami...",
    ),
    "reportIntroduction": MessageLookupByLibrary.simpleMessage(
      "Laporkan Perkenalan",
    ),
    "reportNickname": MessageLookupByLibrary.simpleMessage("Laporkan Nama"),
    "reportSuccess": MessageLookupByLibrary.simpleMessage(
      "Berhasil dilaporkan",
    ),
    "reportTheme": MessageLookupByLibrary.simpleMessage("Tipe Laporan"),
    "reportUserTitle": MessageLookupByLibrary.simpleMessage(
      "Laporkan Pengguna",
    ),
    "reportVoiceCard": MessageLookupByLibrary.simpleMessage(
      "Laporkan Kartu Suara",
    ),
    "requestAMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "Meminta kunci kotak ajaib",
    ),
    "requestKey": MessageLookupByLibrary.simpleMessage("Permintaan kunci"),
    "requestSend": MessageLookupByLibrary.simpleMessage("Terkirim"),
    "requestStorageData": MessageLookupByLibrary.simpleMessage(
      "\"Winker\" meminta akses ke penyimpanan data Anda.",
    ),
    "requestSuccessful": MessageLookupByLibrary.simpleMessage(
      "Permintaan berhasil, harap tunggu respon",
    ),
    "requestTimeout": MessageLookupByLibrary.simpleMessage(
      "Permintaan kehabisan waktu, harap coba lagi.",
    ),
    "requestTooMuch": MessageLookupByLibrary.simpleMessage(
      "Permintaan gagal. Anda dapat mengirimkan dua undangan per hari kepada pengguna ini.",
    ),
    "requestsHasReachedTheUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Jumlah permintaan untuk pengguna ini telah mencapai batas atas.",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("Dikirim ulang"),
    "resendCode": MessageLookupByLibrary.simpleMessage("Dikirim ulang"),
    "resendCodeIn": MessageLookupByLibrary.simpleMessage("Kirim ulang setelah"),
    "resendCodeMax": MessageLookupByLibrary.simpleMessage(
      "Jumlah permintaan telah mencapai batas maksimal, coba lagi besok.",
    ),
    "resendMsg": MessageLookupByLibrary.simpleMessage("Kirim ulang pesan ini?"),
    "reset": MessageLookupByLibrary.simpleMessage("Setel Ulang"),
    "respondWithGift": MessageLookupByLibrary.simpleMessage(
      "Tanggapi dengan hadiah",
    ),
    "restore": MessageLookupByLibrary.simpleMessage("Pulihkan"),
    "result": MessageLookupByLibrary.simpleMessage("Hasil"),
    "resultWithWords": m200,
    "results": MessageLookupByLibrary.simpleMessage("Hasil:"),
    "retry": MessageLookupByLibrary.simpleMessage("Coba lagi"),
    "reverb": MessageLookupByLibrary.simpleMessage("Berkumandang:"),
    "reviewEvent": MessageLookupByLibrary.simpleMessage("Meninjau acara"),
    "reward": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "rewardDetail": MessageLookupByLibrary.simpleMessage("Detail hadiah"),
    "rewardStEnd": MessageLookupByLibrary.simpleMessage(
      "Statistik hadiah berakhir",
    ),
    "rewardStatistics": MessageLookupByLibrary.simpleMessage(
      "Hadiah\nStatistik",
    ),
    "rewardToGet": MessageLookupByLibrary.simpleMessage(
      "Ada hadiah yang harus diklaim",
    ),
    "rewardedToast": m201,
    "rewards": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "rewardsHasSent": MessageLookupByLibrary.simpleMessage(
      "Hadiah telah dikirimkan kepada anda.",
    ),
    "rewardsSentYourBackpack": MessageLookupByLibrary.simpleMessage(
      "Hadiah telah dikirim ke ransel anda.",
    ),
    "ring": MessageLookupByLibrary.simpleMessage("Cincin"),
    "ringBox": MessageLookupByLibrary.simpleMessage("Kotak cincin"),
    "ringDisplay": MessageLookupByLibrary.simpleMessage("Tampilan cincin"),
    "ringGuidelines": MessageLookupByLibrary.simpleMessage("Panduan cincin"),
    "ripple": MessageLookupByLibrary.simpleMessage("Riak"),
    "riskyTips": MessageLookupByLibrary.simpleMessage(
      "Informasi ini mengandung risiko; waspada terhadap penipuan.",
    ),
    "room": MessageLookupByLibrary.simpleMessage("Ruangan"),
    "roomAdmins": m202,
    "roomAdminsTitle": MessageLookupByLibrary.simpleMessage("Admin Ruangan"),
    "roomAnnounceInputHint": MessageLookupByLibrary.simpleMessage(
      "Semua orang di ruangan akan melihat pengumuman tersebut",
    ),
    "roomAnnounceWordCount": m203,
    "roomApplyMic": MessageLookupByLibrary.simpleMessage("Mengajukan naik mic"),
    "roomBackground": MessageLookupByLibrary.simpleMessage("Background"),
    "roomCloseAndExit": MessageLookupByLibrary.simpleMessage(
      "Hapus dan keluar",
    ),
    "roomClosedDueToProlonged": MessageLookupByLibrary.simpleMessage(
      "Ruang telah ditutup karena tidak ada yang aktif dalam waktu yang lama.",
    ),
    "roomCover": MessageLookupByLibrary.simpleMessage("Penutup Kamar"),
    "roomEvent": MessageLookupByLibrary.simpleMessage("Event Ruang"),
    "roomEventMaximum": MessageLookupByLibrary.simpleMessage(
      "Ruang ini telah mencapai event maksimum",
    ),
    "roomExitDes": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin keluar dari ruangan ini?",
    ),
    "roomExitTitle": MessageLookupByLibrary.simpleMessage(
      "Meninggalkan ruangan",
    ),
    "roomFanClubNameLimit": m204,
    "roomFanClubNameLimitContent": MessageLookupByLibrary.simpleMessage(
      "· Batas karakter untuk nama grup penggemar: 2 ~ 6 huruf / angka, karakter lain tidak didukung\n· Nama grup penggemar tidak boleh menggunakan kata-kata sensitif seperti pornografi atau pornoaksi, tidak boleh memfitnah atau menyamar sebagai orang lain, dan tidak boleh menggunakan nama official.",
    ),
    "roomFanClubNameLimitTitle": MessageLookupByLibrary.simpleMessage(
      "Persyaratan penyiapan",
    ),
    "roomFansNameModifyTip": m205,
    "roomFansUpdateTip": MessageLookupByLibrary.simpleMessage(
      "Tugas akan diatur ulang setiap hari pada pukul 00:00.",
    ),
    "roomFreeMic": MessageLookupByLibrary.simpleMessage("Bebas ambil mic"),
    "roomFunction": MessageLookupByLibrary.simpleMessage("Fungsi ruangan"),
    "roomGifts": MessageLookupByLibrary.simpleMessage("Hadiah ruangan"),
    "roomHasBanned": MessageLookupByLibrary.simpleMessage(
      "Ruangan telah di banned",
    ),
    "roomId": m206,
    "roomIdKey": MessageLookupByLibrary.simpleMessage("ID ruangan"),
    "roomIsClosed": MessageLookupByLibrary.simpleMessage(
      "Ruangan saat ini tidak online, silakan kembali lagi nanti",
    ),
    "roomIsOpening": m207,
    "roomKickOut": MessageLookupByLibrary.simpleMessage("Keluarkan"),
    "roomLeveUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Batas atas harian Exp ruangan: ",
    ),
    "roomLevel": MessageLookupByLibrary.simpleMessage("Level ruangan"),
    "roomLevelUpgrade": m208,
    "roomLostConnect": MessageLookupByLibrary.simpleMessage(
      "Ruangan ini terputus",
    ),
    "roomMemberTabAdmin": MessageLookupByLibrary.simpleMessage("Admin"),
    "roomMemberTabBlocked": MessageLookupByLibrary.simpleMessage("Diblokir"),
    "roomMemberTabOnline": MessageLookupByLibrary.simpleMessage("Aktif"),
    "roomMemberTabSpeakRequest": MessageLookupByLibrary.simpleMessage(
      "Permintaan berbicara",
    ),
    "roomMicPermission": MessageLookupByLibrary.simpleMessage("Izin mikrofon"),
    "roomMode": MessageLookupByLibrary.simpleMessage("Mode Kamar"),
    "roomModeChat": MessageLookupByLibrary.simpleMessage("Mengobrol"),
    "roomModeCp": MessageLookupByLibrary.simpleMessage("Cp"),
    "roomModeTruthDare": MessageLookupByLibrary.simpleMessage(
      "Jujur & Tantangan",
    ),
    "roomMsgRoomName": m209,
    "roomName": MessageLookupByLibrary.simpleMessage("Nama ruang"),
    "roomNameInputHint": MessageLookupByLibrary.simpleMessage(
      "Berikan nama yang keren pada ruanganmu",
    ),
    "roomPk": MessageLookupByLibrary.simpleMessage("Pertarungan Kamar"),
    "roomPkExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat didasarkan pada nilai berlian dari hadiah selama pertarungan kamar.",
    ),
    "roomPkInvitation": MessageLookupByLibrary.simpleMessage(
      "Undangan Pertarungan kamar",
    ),
    "roomPkInviteTips": MessageLookupByLibrary.simpleMessage(
      " undangan pertarungan kamar telah dikirimkan kepada Anda. Apakah Anda menerima tantangan?",
    ),
    "roomPkMatching": MessageLookupByLibrary.simpleMessage(
      "Cocokkan pertarungan kamar...",
    ),
    "roomPkNotice": MessageLookupByLibrary.simpleMessage(
      "Notifikasi pertarungan kamar",
    ),
    "roomPkRanking": MessageLookupByLibrary.simpleMessage(
      "Peringkat Pertarungan Kamar",
    ),
    "roomPkRecord": MessageLookupByLibrary.simpleMessage(
      "Rekaman Pertarungan Kamar",
    ),
    "roomPkRule": MessageLookupByLibrary.simpleMessage(
      "1. Pemilik dan admin kamar dapat memulai Pertarungan dalam fungsi kamar.\n\n2. Terdapat dua mode untuk Pertarungan, yaitu Pertarungan suara dan Pertarungan hadiah. Dalam Pertarungan suara, setiap orang dapat memilih teman favorit untuk memberikan dukungan. Setelah pemungutan suara, suara tidak dapat ditarik kembali. Anda perlu mendukungnya dengan hadiah apa pun atau hadiah yang ditunjuk. Jika pencipta membuat Pertarungan hadiah yang ditentukan, hanya hadiah yang ditentukan dalam Pertarungan yang akan dihitung dalam Pertarungan.\n\n3. Pencipta dapat mengaktifkan mode Pertarungan jackpot . Peserta Pertarungan tidak akan dapat menerima pengembalian uang hadiah selama Pertarungan. Pemenang akan menerima 30% dari nilai hadiah setelah Pertarungan.",
    ),
    "roomProfile": MessageLookupByLibrary.simpleMessage("Profil ruang"),
    "roomRankExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat berdasar pada nilai diamond dari hadiah ruangan yang anda terima",
    ),
    "roomReEnterErrTip": m210,
    "roomSettingClearScreen": MessageLookupByLibrary.simpleMessage(
      "Membersihkan layar",
    ),
    "roomSettingMusic": MessageLookupByLibrary.simpleMessage("Musik"),
    "roomSettings": MessageLookupByLibrary.simpleMessage("Pengaturan ruangan"),
    "roomTag": MessageLookupByLibrary.simpleMessage("Tag Ruangan"),
    "roomTask": MessageLookupByLibrary.simpleMessage("Tugas kamar"),
    "roomTaskGuideText": MessageLookupByLibrary.simpleMessage(
      "Klik di sini untuk mendapatkan tugas kamar!",
    ),
    "roomTaskMsgText": MessageLookupByLibrary.simpleMessage(
      "Selesaikan tugas kamar untuk mendapatkan lebih banyak hadiah!",
    ),
    "roomTaskRefreshTime": MessageLookupByLibrary.simpleMessage(
      "Segarkan pada 23:59(UTC+3)",
    ),
    "roomTheme": MessageLookupByLibrary.simpleMessage("Tema ruangan"),
    "roomType": MessageLookupByLibrary.simpleMessage("Tema ruangan"),
    "roomTypeTips": MessageLookupByLibrary.simpleMessage("Tipe..."),
    "roomWelcome": MessageLookupByLibrary.simpleMessage("Selamat datang!!"),
    "roomWillCloseTips": m211,
    "roomsUser": m212,
    "round": MessageLookupByLibrary.simpleMessage("Ronde "),
    "roundVoting": m213,
    "rules": MessageLookupByLibrary.simpleMessage("Aturan"),
    "rules1": MessageLookupByLibrary.simpleMessage(
      "❌Meminta informasi pribadi",
    ),
    "rules2": MessageLookupByLibrary.simpleMessage(
      "❌Melecehkan atau Membulipengguna lain",
    ),
    "rules3": MessageLookupByLibrary.simpleMessage(
      "❌ Mengunggah konten seksual",
    ),
    "rules4": MessageLookupByLibrary.simpleMessage(
      "❌ Memposting Konten agama atau politik",
    ),
    "rules5": MessageLookupByLibrary.simpleMessage(
      "❌Menggunakan bahasa yang kasar dan tidak pantas",
    ),
    "rulesIntro": MessageLookupByLibrary.simpleMessage(
      "Tindakan berikut akan mengakibatkan akun Banned permanen.",
    ),
    "rush": MessageLookupByLibrary.simpleMessage("Buka"),
    "sActionPlayHint": MessageLookupByLibrary.simpleMessage("Bermain"),
    "sActionPreviewHint": MessageLookupByLibrary.simpleMessage("pratinjau"),
    "sActionSelectHint": MessageLookupByLibrary.simpleMessage("Pilih"),
    "sActionSwitchPathLabel": MessageLookupByLibrary.simpleMessage(
      "Jalur Peralihan",
    ),
    "sActionUseCameraHint": MessageLookupByLibrary.simpleMessage(
      "Gunakan Kamera",
    ),
    "sNameDurationLabel": MessageLookupByLibrary.simpleMessage("Durasi"),
    "sTypeAudioLabel": MessageLookupByLibrary.simpleMessage("Suara"),
    "sTypeImageLabel": MessageLookupByLibrary.simpleMessage("Gambar"),
    "sTypeOtherLabel": MessageLookupByLibrary.simpleMessage(
      "Sumber daya lainnya",
    ),
    "sTypeVideoLabel": MessageLookupByLibrary.simpleMessage("Video"),
    "sUnitAssetCountLabel": MessageLookupByLibrary.simpleMessage("hitung"),
    "safeDistanceProtection": MessageLookupByLibrary.simpleMessage(
      "Perlindungan jarak aman",
    ),
    "safeDistanceProtectionDesc": MessageLookupByLibrary.simpleMessage(
      "Tidak match dengan pengguna dalam jarak 500m dari saya",
    ),
    "safeMode": MessageLookupByLibrary.simpleMessage(
      "Mode aman diaktifkan, informasi anda berada di bawah perlindungan kami.",
    ),
    "safeModeContent": MessageLookupByLibrary.simpleMessage(
      "Informasi anda dapat dilindungi dalam mode aman.",
    ),
    "safeModeContentToast": MessageLookupByLibrary.simpleMessage(
      "Mode aman diaktifkan, juga dapat diubah ke mode bebas disini!",
    ),
    "safeModeDetailContent": MessageLookupByLibrary.simpleMessage(
      "Dalam mode aman, kami akan melindungi privasi anda dan memastikan bahwa informasi pribadi anda tidak diperoleh oleh pengguna lain.",
    ),
    "safeModeName": MessageLookupByLibrary.simpleMessage("Mode aman"),
    "save": MessageLookupByLibrary.simpleMessage("Simpan"),
    "saveAvatar": MessageLookupByLibrary.simpleMessage("Simpan avatar"),
    "saveFailed": MessageLookupByLibrary.simpleMessage("Gagal simpan"),
    "saveSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil disimpan",
    ),
    "savedGallery": MessageLookupByLibrary.simpleMessage(
      "Disimpan ke Galeri !",
    ),
    "sayFreeHasSent": MessageLookupByLibrary.simpleMessage("Telah dikirim"),
    "sayFreeWink": m214,
    "sayFreeWinkRepeat": m215,
    "sayHello": MessageLookupByLibrary.simpleMessage("[Katakan halo]"),
    "sayHi": MessageLookupByLibrary.simpleMessage("Katakan hai"),
    "sayHiBottomTip": m216,
    "sayHiGiftSendChat": MessageLookupByLibrary.simpleMessage(
      "Kirim ke obrolan",
    ),
    "sayHiLimitTitle": m217,
    "sayHiSubtitle": MessageLookupByLibrary.simpleMessage(
      "Batasan ini mendorong pilihan yang bijaksana. Peluang baru tersedia setelah beberapa saat",
    ),
    "sayHiTitle": m218,
    "sayHiToYourMate": MessageLookupByLibrary.simpleMessage(
      "Ucapkan hai ke teman anda!",
    ),
    "saySomething": m219,
    "scanning": MessageLookupByLibrary.simpleMessage("Sedang memindai..."),
    "score": MessageLookupByLibrary.simpleMessage("Nilai"),
    "scoreAddNum": MessageLookupByLibrary.simpleMessage(
      "dapatkannya dengan benar dan skor ",
    ),
    "scoresPerformersHere": MessageLookupByLibrary.simpleMessage(
      "Skor untuk semua pemain ditampilkan di sini.",
    ),
    "screenMessage": MessageLookupByLibrary.simpleMessage("Pesan layar"),
    "screenshot": MessageLookupByLibrary.simpleMessage("Tangkapan layar"),
    "search": MessageLookupByLibrary.simpleMessage("Cari"),
    "searchByFamilyID": MessageLookupByLibrary.simpleMessage(
      "Cari berdasarkan ID family",
    ),
    "searchByRoomId": MessageLookupByLibrary.simpleMessage(
      "Cari menurut ID ruangan",
    ),
    "searchByUserId": MessageLookupByLibrary.simpleMessage(
      "Cari berdasar ID pengguna",
    ),
    "searchByUserNickname": MessageLookupByLibrary.simpleMessage(
      "Cari berdasarkan nama",
    ),
    "searchFamilyTip": MessageLookupByLibrary.simpleMessage(
      "Cari nama atau ID family",
    ),
    "searchForSongs": MessageLookupByLibrary.simpleMessage("Cari lagu"),
    "searchHashtag": MessageLookupByLibrary.simpleMessage("Cari topik"),
    "searchHistory": MessageLookupByLibrary.simpleMessage("Riwayat Pencarian"),
    "searchResult": MessageLookupByLibrary.simpleMessage("Hasil pencarian"),
    "searchResults": MessageLookupByLibrary.simpleMessage("Hasil Pencarian"),
    "searchRoomIdUserId": MessageLookupByLibrary.simpleMessage(
      "Cari berdasarkan ID ruangan/ID pengguna",
    ),
    "searchSticker": MessageLookupByLibrary.simpleMessage(
      "Klik ini! Anda dapat mencari Stiker apa pun yang anda inginkan.",
    ),
    "searchStickers": MessageLookupByLibrary.simpleMessage("Cari Stiker"),
    "searchTheFamilyUser": MessageLookupByLibrary.simpleMessage(
      "Cari anggota family berdasarkan nama pengguna",
    ),
    "searchUniqueIds": MessageLookupByLibrary.simpleMessage("Cari ID unik"),
    "searchUserName": MessageLookupByLibrary.simpleMessage(
      "Cari nama pengguna",
    ),
    "secondClickToRead": MessageLookupByLibrary.simpleMessage(
      "Kedua, klik untuk membaca teks dengan suara anda. ",
    ),
    "secondStep": MessageLookupByLibrary.simpleMessage("Langkah kedua"),
    "secondsTimeout": m220,
    "seeMoreReplies": MessageLookupByLibrary.simpleMessage(
      "Konten yang dilaporkan telah disembunyikan",
    ),
    "select": MessageLookupByLibrary.simpleMessage("Pilih"),
    "selectAGift": MessageLookupByLibrary.simpleMessage("Pilih satu hadiah"),
    "selectAll": MessageLookupByLibrary.simpleMessage("Pilih semua"),
    "selectBackground": MessageLookupByLibrary.simpleMessage(
      "Pilih latar belakang",
    ),
    "selectCountry": MessageLookupByLibrary.simpleMessage("Pilih negara"),
    "selectCover": MessageLookupByLibrary.simpleMessage(
      "Silakan unggah cover family.",
    ),
    "selectDuration": MessageLookupByLibrary.simpleMessage(
      "Pilih durasi (menit)",
    ),
    "selectFilter": MessageLookupByLibrary.simpleMessage("Memilih filter"),
    "selectFriends": MessageLookupByLibrary.simpleMessage("Pilih teman"),
    "selectGift": MessageLookupByLibrary.simpleMessage("Pilih hadiah"),
    "selectGiftType": MessageLookupByLibrary.simpleMessage("Pilih tipe hadiah"),
    "selectLeastInterest": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih satu minat minimal",
    ),
    "selectRing": MessageLookupByLibrary.simpleMessage(
      "Pilih cincin untuk melamar",
    ),
    "selectRoom": MessageLookupByLibrary.simpleMessage("Pilih kamar"),
    "selectSendingTime": MessageLookupByLibrary.simpleMessage(
      "Pihim waktu pengiriman",
    ),
    "selectSinglePhoto": MessageLookupByLibrary.simpleMessage(
      "Pilih satu foto",
    ),
    "selectSuitor": MessageLookupByLibrary.simpleMessage(
      "Pilih belahan jiwa Anda",
    ),
    "selectTheLetter": MessageLookupByLibrary.simpleMessage("Pilih surat ini"),
    "selectTheTopic": MessageLookupByLibrary.simpleMessage("Pilih topiknya"),
    "selectTheUser": MessageLookupByLibrary.simpleMessage("Cari pengguna ini"),
    "selectTheVideo": MessageLookupByLibrary.simpleMessage("Memilih video ini"),
    "selectTheme": MessageLookupByLibrary.simpleMessage("Pilih Tema"),
    "selectTime": MessageLookupByLibrary.simpleMessage("Pilih Waktu"),
    "selectTimes": MessageLookupByLibrary.simpleMessage("Pilih waktu"),
    "selectUpTen": MessageLookupByLibrary.simpleMessage(
      "Anda dapat memilih hingga 10 Tag",
    ),
    "selectYourAppLan": MessageLookupByLibrary.simpleMessage(
      "Pilih bahasa aplikasi anda",
    ),
    "selectYourAvatar": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih avatar nda",
    ),
    "selectYourCity": MessageLookupByLibrary.simpleMessage("Pilih kota Anda"),
    "selectYourCountry": MessageLookupByLibrary.simpleMessage(
      "Pilih negara anda",
    ),
    "selectYourFavorite": MessageLookupByLibrary.simpleMessage(
      "Pilih favorit Anda",
    ),
    "selectYourPunishment": MessageLookupByLibrary.simpleMessage(
      "Pilih hukuman Anda",
    ),
    "selectYourRoom": MessageLookupByLibrary.simpleMessage("Pilih kamar Anda"),
    "selected": MessageLookupByLibrary.simpleMessage("Dipilih"),
    "selectedFruits": MessageLookupByLibrary.simpleMessage("Buah Pilihan:"),
    "selectedTopic": MessageLookupByLibrary.simpleMessage("Topik yang dipilih"),
    "selectedTopicCount": m221,
    "selfDelete": MessageLookupByLibrary.simpleMessage(
      "Hapus otomatis setelah melihat",
    ),
    "send": MessageLookupByLibrary.simpleMessage("Kirim"),
    "sendAGift": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk menunjukkan apresiasi anda.",
    ),
    "sendAKey": MessageLookupByLibrary.simpleMessage("Kirim satu kunci"),
    "sendALuckyBag": MessageLookupByLibrary.simpleMessage("Kirim lucky bag"),
    "sendAndUnlock": m222,
    "sendBottleFirstContent": MessageLookupByLibrary.simpleMessage(
      "Anda belum mengirim botol hari ini, kirimkan botol dulu.",
    ),
    "sendBottleFirstTitle": MessageLookupByLibrary.simpleMessage(
      "Untuk melanjutkan, kirim botol dulu hari ini.",
    ),
    "sendChatModeRequest": m223,
    "sendFeedback": MessageLookupByLibrary.simpleMessage("Kirim umpan balik"),
    "sendGiftCanIncreaseBlessValue": MessageLookupByLibrary.simpleMessage(
      "Mengirim hadiah dapat meningkatkan nilai berkat",
    ),
    "sendGiftFailNoCamp": MessageLookupByLibrary.simpleMessage(
      "Tidak bergabung dengan kamp, tidak dapat mengirim hadiah",
    ),
    "sendGiftGetIntimacy": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk mendapatkan keintiman",
    ),
    "sendGiftTips": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah diperlukan 1 kotak hadiah",
    ),
    "sendGiftToAdmin": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah pendatang baru gratis kepada admin",
    ),
    "sendGiftToOwner": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah pendatang baru gratis kepada pemilik",
    ),
    "sendGifts": MessageLookupByLibrary.simpleMessage("Kirim Hadiah"),
    "sendGiftsSupportHer": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk mendukungnya",
    ),
    "sendGiftsSupportHim": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk mendukungnya",
    ),
    "sendGoodsByUser": m224,
    "sendImageInRoom": MessageLookupByLibrary.simpleMessage(
      "Kirim gambar di kamar",
    ),
    "sendImageInRoomDesc": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengirim gambar di kamar",
    ),
    "sendKeys": MessageLookupByLibrary.simpleMessage("Kirim kunci"),
    "sendLowercase": MessageLookupByLibrary.simpleMessage("kirim"),
    "sendLuckyBag": MessageLookupByLibrary.simpleMessage("Kirim lucky bag"),
    "sendLuckyBagWithoutA": MessageLookupByLibrary.simpleMessage(
      "Memberikan lucky bag",
    ),
    "sendSomethingNew": MessageLookupByLibrary.simpleMessage(
      "Kirim sesuatu yang baru",
    ),
    "sendSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil dikirim",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage("Kirim ke"),
    "sendToChat": MessageLookupByLibrary.simpleMessage("Kirim ke obrolan"),
    "sendToFriend": MessageLookupByLibrary.simpleMessage("Kirim ke teman"),
    "sendUser": MessageLookupByLibrary.simpleMessage("kirim"),
    "sendWink": MessageLookupByLibrary.simpleMessage("Kirim kedipan"),
    "sendYouGift": m225,
    "sendYouQuestion": m226,
    "senderWealthAddNumExp": m227,
    "sendingImmediately": MessageLookupByLibrary.simpleMessage(
      "segera dibagikan",
    ),
    "sendingMoment": MessageLookupByLibrary.simpleMessage("Mengirim momen…"),
    "sendingTime": MessageLookupByLibrary.simpleMessage("Waktu pengiriman:"),
    "sent": MessageLookupByLibrary.simpleMessage("Terkirim"),
    "sentButReject": MessageLookupByLibrary.simpleMessage(
      "Pesan berhasil dikirim tetapi ditolak oleh penerima",
    ),
    "sentMsgOnceTakeScreen": MessageLookupByLibrary.simpleMessage(
      "Sebuah pesan akan dikirim ke pengguna setelah anda menangkap layar Obrolan.",
    ),
    "sentRankExplain": MessageLookupByLibrary.simpleMessage(
      "Peringkat berdasar pada nilai diamond dari hadiah yang anda kirim",
    ),
    "sentYouAMagicBoxKey": MessageLookupByLibrary.simpleMessage(
      "mengirimi Anda kunci kotak ajaib",
    ),
    "sentYouGift": MessageLookupByLibrary.simpleMessage(
      "Mengirimi anda hadiah",
    ),
    "serviceExpert": MessageLookupByLibrary.simpleMessage(
      "Ahli layanan pelanggan",
    ),
    "serviceExpertDesc": MessageLookupByLibrary.simpleMessage(
      "Nikmati layanan terbaik dari ahli layanan pelanggan kami.",
    ),
    "setAsMaster": MessageLookupByLibrary.simpleMessage(
      "Angkat sebagai Sesepuh",
    ),
    "setAsVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Angkat sebagai Wakil Ketua Family",
    ),
    "setAvatar": MessageLookupByLibrary.simpleMessage("Atur avatar"),
    "setMicWhenRoomLevelReaches": m228,
    "setPassword": MessageLookupByLibrary.simpleMessage("Mengatur kata sandi"),
    "setPatriarch": MessageLookupByLibrary.simpleMessage(
      "Angkat sebagai Ketua Family",
    ),
    "setRemarks": MessageLookupByLibrary.simpleMessage("Setel Keterangan"),
    "setSuccessfully": MessageLookupByLibrary.simpleMessage("Berhasil disetel"),
    "setTheCity": MessageLookupByLibrary.simpleMessage("Setel kota ini"),
    "setToAvatar": MessageLookupByLibrary.simpleMessage("Setel ke avatar"),
    "setUp": MessageLookupByLibrary.simpleMessage("Setel"),
    "setUpProfile": MessageLookupByLibrary.simpleMessage("Setel profil"),
    "setVicePatriarch": MessageLookupByLibrary.simpleMessage(
      "Angkat sebagai Wakil Ketua Family",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Setelan"),
    "setupAdmin": MessageLookupByLibrary.simpleMessage("Pengaturan admin"),
    "sexGuideText": MessageLookupByLibrary.simpleMessage(
      "Jenis kelamin tidak dapat diubah setelah dipilih.",
    ),
    "sexualHarassment": MessageLookupByLibrary.simpleMessage(
      "pelecehan seksual",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Bagikan"),
    "shareApp": MessageLookupByLibrary.simpleMessage("Bagikan Aplikasi"),
    "shareAppContent": MessageLookupByLibrary.simpleMessage(
      "winker ~ Panggilan suara dan mengobrol dengan siapa pun dari seluruh dunia!",
    ),
    "shareContent": MessageLookupByLibrary.simpleMessage(
      "Hey~ Jangan lewatkan momen luar biasa ini.",
    ),
    "shareEventTagContent": MessageLookupByLibrary.simpleMessage(
      "Ayo bergabung dalam event spesial di Winker sekarang!",
    ),
    "shareFail": MessageLookupByLibrary.simpleMessage(
      "Gagal dibagikan, silakan coba lagi",
    ),
    "shareHaContent": MessageLookupByLibrary.simpleMessage(
      "Bagikan kebahagiaan Winker dengan teman-teman anda.",
    ),
    "shareHalama": MessageLookupByLibrary.simpleMessage(
      "Bagikan Winker ke teman anda",
    ),
    "shareMomentPhotoToAvatar": MessageLookupByLibrary.simpleMessage(
      "Saya mengubah foto saya ke avatar yang disediakan oleh Winker!",
    ),
    "shareMomentTodayContent": MessageLookupByLibrary.simpleMessage(
      "Cerita anda akan menambah lebih banyak suka dan teman.",
    ),
    "shareScreenShotTo": MessageLookupByLibrary.simpleMessage(
      "Bagikan tangkapan layar ke",
    ),
    "shareSuccess": MessageLookupByLibrary.simpleMessage("Berhasil dibagikan"),
    "shareTheRoom": MessageLookupByLibrary.simpleMessage("Membagikan ruangan."),
    "shareToFriends": MessageLookupByLibrary.simpleMessage(
      "Bagikan ke teman-teman",
    ),
    "shareToMoment": MessageLookupByLibrary.simpleMessage("Bagikan ke momen"),
    "shareToOthers": MessageLookupByLibrary.simpleMessage(
      "Bagikan ke orang lain",
    ),
    "shareToYouFriends": MessageLookupByLibrary.simpleMessage(
      "Bagikan ke teman anda",
    ),
    "shareYourMomentContent": MessageLookupByLibrary.simpleMessage(
      "Cerita anda akan menambah lebih banyak suka dan teman.",
    ),
    "shareYourMomentTitle": MessageLookupByLibrary.simpleMessage(
      "Bagikan momen anda",
    ),
    "shareYourRoom": MessageLookupByLibrary.simpleMessage(
      "Undang temanmu untuk mengobrol!",
    ),
    "shared": MessageLookupByLibrary.simpleMessage("Sudah"),
    "she": MessageLookupByLibrary.simpleMessage("dia"),
    "sheFollowYou": MessageLookupByLibrary.simpleMessage("Dia mengikutimu"),
    "sheLiving": MessageLookupByLibrary.simpleMessage(
      "Dia tinggal di: tempat dekat anda!",
    ),
    "showIdentity": MessageLookupByLibrary.simpleMessage("Tunjukkan identitas"),
    "showInWinkerWorld": MessageLookupByLibrary.simpleMessage(
      "Tampilkan di dunia Winker!",
    ),
    "showInWorld": MessageLookupByLibrary.simpleMessage(
      "Tampilkan di dunia Winker!",
    ),
    "showOnMic": MessageLookupByLibrary.simpleMessage(
      "Tampilkan di posisi mikrofon",
    ),
    "showOnProfile": MessageLookupByLibrary.simpleMessage(
      "Tampilkan di profil",
    ),
    "showOnRoomProfile": MessageLookupByLibrary.simpleMessage(
      "Tampilkan di profil kamar",
    ),
    "showYourCharm": MessageLookupByLibrary.simpleMessage(
      "Lakukan langkah pertama untuk menunjukkan pesona Anda!",
    ),
    "signContent": MessageLookupByLibrary.simpleMessage(
      "Masuk selama 7 hari untuk mendapatkan kejutan",
    ),
    "signIn": MessageLookupByLibrary.simpleMessage("Masuk"),
    "signInApple": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan akun Apple",
    ),
    "signInBackpack": MessageLookupByLibrary.simpleMessage(
      "Hadiah tugas telah dikirim ke ransel.",
    ),
    "signInCoins": MessageLookupByLibrary.simpleMessage(
      "Masuk Untuk Menerima Koin",
    ),
    "signInDays": m229,
    "signInFaceGoogle": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan akun Google",
    ),
    "signInFacebook": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan facebook",
    ),
    "signInFailed": MessageLookupByLibrary.simpleMessage(
      "Gagal masuk, harap coba lagi",
    ),
    "signInForDays": m230,
    "signInReceived": MessageLookupByLibrary.simpleMessage("Diterima"),
    "signInRewardNumCoins": m231,
    "signInRewards": MessageLookupByLibrary.simpleMessage("Hadiah harian"),
    "signInWithPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan ponsel",
    ),
    "signReceiveRewards": MessageLookupByLibrary.simpleMessage(
      "Masuk untuk mendapatkan hadiah",
    ),
    "signedInNumDay": m232,
    "signedInToday": MessageLookupByLibrary.simpleMessage("Masuk hari ini"),
    "silverFamily": MessageLookupByLibrary.simpleMessage("Family Perak"),
    "singleCycle": MessageLookupByLibrary.simpleMessage(
      "Putar Ulang Satu Lagu",
    ),
    "skillCD": MessageLookupByLibrary.simpleMessage(
      "Tenang, silakan coba lagi nanti",
    ),
    "skin": MessageLookupByLibrary.simpleMessage("Kulit"),
    "skip": MessageLookupByLibrary.simpleMessage("Lewati"),
    "skipPerformConfirm": MessageLookupByLibrary.simpleMessage(
      "Pertunjukan ini akan dilewati. Poin untuk babak ini akan dihitung sekarang. Masih mau lewati?",
    ),
    "skipPerformanceConfirm": MessageLookupByLibrary.simpleMessage(
      "Pertunjukan ini akan dilewati. Poin untuk babak ini akan dihitung sekarang. Masih mau lewati?",
    ),
    "smokeDrugsDrinking": MessageLookupByLibrary.simpleMessage(
      "merokok, narkoba, alkohol",
    ),
    "snapchat": MessageLookupByLibrary.simpleMessage("Snapchat"),
    "someoneAtYou": MessageLookupByLibrary.simpleMessage("Seseorang @Anda"),
    "someoneAtYouWithBrackets": MessageLookupByLibrary.simpleMessage("[@Anda]"),
    "someoneCallingYou": MessageLookupByLibrary.simpleMessage(
      "Seseorang sedang memanggil Anda!",
    ),
    "someoneMaybeYouLove": MessageLookupByLibrary.simpleMessage(
      "Mengobrol dengan belahan jiwa Anda",
    ),
    "somethingIsBroken": MessageLookupByLibrary.simpleMessage("Ada yang rusak"),
    "sorryNotFoundVoiceMatch": MessageLookupByLibrary.simpleMessage(
      "Maaf, kami tidak dapat menemukan pengguna yang tepat untuk Anda sekarang, silakan coba lagi nanti.",
    ),
    "soundEffect": MessageLookupByLibrary.simpleMessage("Efek suara"),
    "soundEffectIsPlaying": MessageLookupByLibrary.simpleMessage(
      "Efek suara sedang diputar",
    ),
    "spam": MessageLookupByLibrary.simpleMessage("Spam"),
    "speakTooFast": MessageLookupByLibrary.simpleMessage(
      "Hai, bersabarlah~Minum kopi Dulu dan tunggu balasannya!",
    ),
    "speaker": MessageLookupByLibrary.simpleMessage("Pembicara"),
    "specialDiscount": MessageLookupByLibrary.simpleMessage("Diskon khusus"),
    "specialDiscountGift": MessageLookupByLibrary.simpleMessage(
      "Hadiah diskon spesial",
    ),
    "specifyMode": MessageLookupByLibrary.simpleMessage("Mode yang ditunjuk"),
    "specifyPerformer": MessageLookupByLibrary.simpleMessage(
      "Pemain yang ditunjuk:",
    ),
    "spendCoinsToSendGift": m233,
    "spendLetterConfirm": m234,
    "stageRewards": MessageLookupByLibrary.simpleMessage("Insentif agensi"),
    "stalking": MessageLookupByLibrary.simpleMessage("Menguntit"),
    "starSign": MessageLookupByLibrary.simpleMessage("Zodiak"),
    "starTime": MessageLookupByLibrary.simpleMessage("waktu mulai"),
    "starlightRewardNum": m235,
    "start": MessageLookupByLibrary.simpleMessage("Mulai"),
    "startAgain": MessageLookupByLibrary.simpleMessage("Mulai lagi"),
    "startDateAndTime": MessageLookupByLibrary.simpleMessage(
      "Tanggal dan waktu mulai",
    ),
    "startPK": MessageLookupByLibrary.simpleMessage("Mulai Pertarungan"),
    "startQuestionGame": m236,
    "statePublishSuccess": MessageLookupByLibrary.simpleMessage(
      "Status berhasil diposting, akan hilang setelah 24 jam",
    ),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "statusExpired": MessageLookupByLibrary.simpleMessage(
      "Status sudah kadaluwarsa, silahkan ubah statusmu",
    ),
    "stay1minInvite": MessageLookupByLibrary.simpleMessage(
      "Dia sudah masuk ruangan beberapa menit, ajak dia ngobrol di mic",
    ),
    "stay2minFollow": MessageLookupByLibrary.simpleMessage(
      "Kita sudah mengobrol sebentar. Ikuti aku dan kamu akan mendapat notifikasi jika kamu membuat ruangan di masa mendatang.",
    ),
    "stayInRoomNumMin": m237,
    "stayInThePage": MessageLookupByLibrary.simpleMessage(
      "Tetap di halaman saat mengunggah",
    ),
    "stayTimeTag": m238,
    "stereo": MessageLookupByLibrary.simpleMessage("Stereo"),
    "stickerLimit": MessageLookupByLibrary.simpleMessage(
      "stiker mencapai batas",
    ),
    "stopEditProfile": MessageLookupByLibrary.simpleMessage(
      "Berhenti mengedit profil anda?",
    ),
    "storageAccessBanned": MessageLookupByLibrary.simpleMessage(
      "Akses penyimpanan tidak diaktifkan",
    ),
    "storagePermission": MessageLookupByLibrary.simpleMessage(
      "\"winker\" ingin mengumpulkan data penyimpanan baca dan tulis untuk mengaktifkan pengiriman gambar dalam pesan hanya saat aplikasi sedang digunakan.",
    ),
    "store": MessageLookupByLibrary.simpleMessage("Toko"),
    "studio": MessageLookupByLibrary.simpleMessage("Studio"),
    "subject": MessageLookupByLibrary.simpleMessage("tema"),
    "submit": MessageLookupByLibrary.simpleMessage("Kirim"),
    "submitRepeated": MessageLookupByLibrary.simpleMessage(
      "Harap jangan mengirimkan permohonan berulang kali",
    ),
    "submitSuccess": MessageLookupByLibrary.simpleMessage("Berhasil mengirim"),
    "submitSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil dikirim",
    ),
    "subscribe": MessageLookupByLibrary.simpleMessage("Subscribe"),
    "subscribed": MessageLookupByLibrary.simpleMessage("Telah Subscribe"),
    "successfully": MessageLookupByLibrary.simpleMessage("Berhasil"),
    "successfullyApplied": MessageLookupByLibrary.simpleMessage(
      "permintaan berhasil",
    ),
    "suggestion": MessageLookupByLibrary.simpleMessage("Saran"),
    "suggestions": MessageLookupByLibrary.simpleMessage("Saran"),
    "superBenefits": MessageLookupByLibrary.simpleMessage("Keuntungan Super"),
    "support": MessageLookupByLibrary.simpleMessage("Dukung"),
    "supporter": MessageLookupByLibrary.simpleMessage("Pendukung"),
    "sure": MessageLookupByLibrary.simpleMessage("Yakin"),
    "sureDelete": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin ingin menghapusnya?",
    ),
    "sureKickOutRoom": MessageLookupByLibrary.simpleMessage(
      "Yakin untuk mengeluarkan pengguna ini dari ruangan? dia tidak akan bisa masuk ke ruangan anda dalam 24 jam",
    ),
    "sureLeaveTheList": MessageLookupByLibrary.simpleMessage(
      "Tinggalkan daftar tidak akan bisa tampil. Tetap pergi?",
    ),
    "sureSpendDiamonds": m239,
    "sureStopVideo": MessageLookupByLibrary.simpleMessage(
      "Anda yakin ingin berhenti menonton video?",
    ),
    "sureToCloseLuckyWheel": MessageLookupByLibrary.simpleMessage(
      "Yakin menutup Roda Keberuntungan? Biaya masuk akan dikembalikan kepada peserta",
    ),
    "sureToJoinLuckyWheel": m240,
    "sureTurnoff": MessageLookupByLibrary.simpleMessage(
      "Anda yakin ingin menutup meja putar?",
    ),
    "sureUnlockRoom": MessageLookupByLibrary.simpleMessage(
      "Yakin untuk membuka kunci kamar Anda",
    ),
    "suspiciousLink": MessageLookupByLibrary.simpleMessage(
      "pengguna ini mengirimkan tautan yang mencurigakan kepada saya",
    ),
    "swipeLeftToPass": MessageLookupByLibrary.simpleMessage(
      "SWIPE KE KIRI UNTUK LEWATI",
    ),
    "swipeRightToLike": MessageLookupByLibrary.simpleMessage(
      "SWIPE KE KANAN UNTUK SUKA",
    ),
    "swipeUpToCancel": MessageLookupByLibrary.simpleMessage(
      "Geser ke atas untuk batalkan",
    ),
    "switchAvatar": MessageLookupByLibrary.simpleMessage("Ganti sebuah avatar"),
    "switchMic": MessageLookupByLibrary.simpleMessage(
      "Yakinkah Anda untuk mengubah posisi mikrofon?",
    ),
    "switchMode": MessageLookupByLibrary.simpleMessage(
      "Ingin beralih ke mode ini?",
    ),
    "switchRoomCloseCalculator": MessageLookupByLibrary.simpleMessage(
      "Beralih mode kamar akan mematikan kalkulator.",
    ),
    "switchRoomMode": MessageLookupByLibrary.simpleMessage(
      "Beralih mode ruangan",
    ),
    "switchRoomModeContent": MessageLookupByLibrary.simpleMessage(
      "Apakah anda yakin mengubah mode ruangan?",
    ),
    "switchRoomTitle": MessageLookupByLibrary.simpleMessage("Pindah Ruangan"),
    "switchTalentMode": MessageLookupByLibrary.simpleMessage(
      "Beralih sekarang akan dapat menutup kamar berbakat. Pertunjukan akan berakhir. Tetap ingin beralih?",
    ),
    "tabChat": MessageLookupByLibrary.simpleMessage("Chat"),
    "tabHome": MessageLookupByLibrary.simpleMessage("Beranda"),
    "tabMine": MessageLookupByLibrary.simpleMessage("Saya"),
    "tabMoments": MessageLookupByLibrary.simpleMessage("Momen"),
    "tag": MessageLookupByLibrary.simpleMessage("Tag"),
    "takeMic": MessageLookupByLibrary.simpleMessage("Ambil Mic"),
    "takeMicFirst": MessageLookupByLibrary.simpleMessage(
      "Silakan ambil mic dulu",
    ),
    "takeMicToJoinGame": MessageLookupByLibrary.simpleMessage(
      "Ambil mic terlebih dahulu untuk bergabung dengan game",
    ),
    "takeTheMic": MessageLookupByLibrary.simpleMessage("Ambil Mikrofon"),
    "takeTheTest": MessageLookupByLibrary.simpleMessage("Ikuti Tesnya"),
    "talent": MessageLookupByLibrary.simpleMessage("Bakat"),
    "talentBegins": MessageLookupByLibrary.simpleMessage(
      "Kamar berbakat dimulai!",
    ),
    "talentRule": MessageLookupByLibrary.simpleMessage(
      "1. Selamat datang di Kamar Berbakat! Di kamar ini, Anda bisa menjadi penampil dan menampilkan pertunjukan yang luar biasa. Atau Anda bisa menjadi anggota audiens yang aktif, yang berfungsi untuk menyemangati para penampil.\n2. Kamar ini meliputi dua mode: Mode grab mikrofon atau mode yang ditunjuk\nMode grab mikrofon: host (atau pemilik/admin kamar) dapat mengundang pengguna. Berikut ini hanya menggunakan host sebagai contoh) atau Anda dapat mendaftar untuk bergabung dengan daftar pemain dan menjadi kandidat pemain. Host akan memulai mode dan setiap kandidat dapat merebut mikrofon. Siapa pun yang meraih mikrofon akan tampil di atas panggung. Setelah pertunjukan, para tamu dapat berkomentar, setelah itu host akan memulai babak baru.\nb. Mode yang ditunjuk: kandidat dalam daftar pemain ditunjuk oleh host dan tampil di atas panggung.\n3. Apa yang dapat dilakukan audiens?\na. Berikan hadiah untuk mendukung pemain dan meningkatkan poin. Hadiah umum senilai 1 berlian = 1 poin. Hadiah spesial (3 hadiah saat tampil) senilai 1 berlian = 2 poin. \nKlik [BOOM] untuk mendukung pemain. Gratis! 1 [BOOM] dari penonton= 10 poin. 1 [BOOM] dari tamu = 100 poin.\n4. Bagaimana saya bisa menjadi tamu?\na. Tamu hanya dapat diundang oleh host (atau pemilik/admin kamar)\nb. Para tamu juga dapat mengirimkan hadiah, klik [BOOM] untuk meninggalkan pesan untuk penampil.\n5. Bagaimana saya bisa menjadi host?\na. Host hanya dapat diundang oleh pemilik atau admin kamar.\n6. Poin dari semua pemain akan diakumulasikan dan ditampilkan di papan skor kumulatif sesuai dengan peringkat mereka.\n7. Dalam hal Anda meninggalkan mikrofon, meninggalkan kamar atau dikeluarkan dari kamar selama pertunjukan, poin yang Anda peroleh tidak akan dihitung.",
    ),
    "talkLittleBitLonger": MessageLookupByLibrary.simpleMessage(
      "Bicara yang lebih banyak lagi. Mungkin Anda akan menemukan lebih banyak kejutan! Tetap mau hang up?",
    ),
    "talkSomething": MessageLookupByLibrary.simpleMessage(
      "Mengobrol tentang sesuatu…",
    ),
    "tapAgainToExit": MessageLookupByLibrary.simpleMessage(
      "Ketuk kembali lagi untuk keluar",
    ),
    "tapToOpenMic": MessageLookupByLibrary.simpleMessage(
      "Ketuk untuk membuka mic",
    ),
    "tapToReport": MessageLookupByLibrary.simpleMessage(
      "Ketuk di sini untuk melaporkan",
    ),
    "tapToSee": MessageLookupByLibrary.simpleMessage("Klik untuk melihat"),
    "tapToView": MessageLookupByLibrary.simpleMessage(
      "ketuk di sini untuk melihat pesan tersembunyi",
    ),
    "task": MessageLookupByLibrary.simpleMessage("Tugas"),
    "taskBadge": MessageLookupByLibrary.simpleMessage("Tugas medali"),
    "taskRewardTitle": MessageLookupByLibrary.simpleMessage(
      "Hadiah gratis! Selesaikan tugas sekarang untuk dapatkan!",
    ),
    "taskToAchieve": MessageLookupByLibrary.simpleMessage("Mencapai"),
    "tasks": MessageLookupByLibrary.simpleMessage("Tugas"),
    "termOfUse": MessageLookupByLibrary.simpleMessage("Ketentuan Penggunaan"),
    "terrorism": MessageLookupByLibrary.simpleMessage("terorisme"),
    "test": MessageLookupByLibrary.simpleMessage("Tes"),
    "testNow": MessageLookupByLibrary.simpleMessage("Tes sekarang"),
    "text": MessageLookupByLibrary.simpleMessage("Teks"),
    "thankForSubmit": MessageLookupByLibrary.simpleMessage(
      "Terima kasih atas submit anda",
    ),
    "thanksForYourRating": MessageLookupByLibrary.simpleMessage(
      "Terima kasih atas penilaian Anda",
    ),
    "thanksReachingOut": MessageLookupByLibrary.simpleMessage(
      "Terima kasih telah menyampaikan pengalaman anda",
    ),
    "theFamilyHasBeenFrozenUnableToDistributeLuckyBags":
        MessageLookupByLibrary.simpleMessage(
          "Family ini telah dibekukan dan tidak dapat membagikan lucky bag",
        ),
    "theFamilyLeaderCannotLeaveTheFamilyDirectlyYouCan":
        MessageLookupByLibrary.simpleMessage(
          "Ketua family tidak bisa keluar langsung. Silakan hubungi layanan pelanggan resmi untuk proses lebih lanjut.",
        ),
    "theFamilyLuckyBagHasBeenSentPleaseGoTo": MessageLookupByLibrary.simpleMessage(
      "Lucky bag family sudah dibagikan, silakan masuk ke ruang family untuk mengambilnya!",
    ),
    "theFamilyLuckyBagHasNotBeenFullyClaimedYet":
        MessageLookupByLibrary.simpleMessage(
          "Lucky bag family belum habis diklaim, cepat masuk ke ruang family untuk ambil sekarang!",
        ),
    "theFamilyTreasuryWillBeUsedForTheFamilyBlessing":
        MessageLookupByLibrary.simpleMessage(
          "Harta family akan dipakai untuk Tas aket Keberuntungan Family, memberikan lebih banyak keuntungan dan hadiah bagi anggota family!",
        ),
    "theGamePositionIsFull": MessageLookupByLibrary.simpleMessage(
      "Posisi game sudah penuh",
    ),
    "theGamePositionIsLocked": MessageLookupByLibrary.simpleMessage(
      "Posisi game terkunci",
    ),
    "theHostJustKickedYouOutOfTheGamePlease": MessageLookupByLibrary.simpleMessage(
      "Tuan rumah baru saja mengeluarkan Anda dari permainan, coba lagi nanti",
    ),
    "theJackPot": MessageLookupByLibrary.simpleMessage("jackPot"),
    "theLuckyBagHasBeenFullyClaimed": MessageLookupByLibrary.simpleMessage(
      "Lucky bag sudah habis diambil!",
    ),
    "theMicAreFullUnableToJoinTheGame": MessageLookupByLibrary.simpleMessage(
      "Micnya penuh, tidak bisa ikut game",
    ),
    "theNicknameYouEnteredIsTooShort": MessageLookupByLibrary.simpleMessage(
      "Julukan yang Anda masukkan terlalu pendek",
    ),
    "theOppositeRoomDefenseBuff": m241,
    "theOppositeRoomPointsChange": m242,
    "theOppositeRoomReceived": MessageLookupByLibrary.simpleMessage(
      "Kamar seberang diterima",
    ),
    "thePerformanceIsEnd": MessageLookupByLibrary.simpleMessage(
      "pertunjukan ini telah berakhir",
    ),
    "thePerformerListFull": MessageLookupByLibrary.simpleMessage(
      "Daftar pemain penuh",
    ),
    "thePkEndInDraw": MessageLookupByLibrary.simpleMessage(
      "Pertarungan berakhir imbang.",
    ),
    "theRoomOwnerEndsTheGame": MessageLookupByLibrary.simpleMessage(
      "Pemilik ruangan mengakhiri permainan.",
    ),
    "theVoiceMatchFindTips": MessageLookupByLibrary.simpleMessage(
      "Temukan teman yang satu frekuensi dengan anda dari tes suara.",
    ),
    "theme": MessageLookupByLibrary.simpleMessage("Tema"),
    "thereAreTimeUntilTheStart": m243,
    "thereIsNoContentHere": MessageLookupByLibrary.simpleMessage(
      "Tidak ada konten di sini",
    ),
    "theyLikeYouBackUnlockChat": MessageLookupByLibrary.simpleMessage(
      "Jika mereka menyukai anda juga, anda dapat membuka kunci Obrolan.",
    ),
    "thinkAgain": MessageLookupByLibrary.simpleMessage("Pikirkan lagi"),
    "thinkWink": MessageLookupByLibrary.simpleMessage(
      "Terima kasih atas wink kamu!",
    ),
    "thirdLoginCancel": MessageLookupByLibrary.simpleMessage(
      "Info akun anda hanya akan digunakan untuk login. Kami akan memastikan semua pengguna dalam lingkungan aman dan dalam privasi.",
    ),
    "thirdLoginNetworkError": MessageLookupByLibrary.simpleMessage(
      "Kesalahan jaringan, silakan coba lagi nanti.",
    ),
    "thirdStep": MessageLookupByLibrary.simpleMessage("Langkah ketiga"),
    "thisAccountHadDeleted": MessageLookupByLibrary.simpleMessage(
      "Akun ini telah dihapus",
    ),
    "thisFamilyHasBeenDisbanded": MessageLookupByLibrary.simpleMessage(
      "Family ini telah dibubarkan.",
    ),
    "thisUserHadBanned": MessageLookupByLibrary.simpleMessage(
      "Pengguna ini telah diblokir",
    ),
    "thisVoiceCallDisconnected": MessageLookupByLibrary.simpleMessage(
      "Maaf. Panggilan suara ini terputus, harap coba lagi",
    ),
    "thisWeek": MessageLookupByLibrary.simpleMessage("Minggu ini"),
    "thisWontAffectOther": MessageLookupByLibrary.simpleMessage(
      "Yang lain tidak akan terpengaruhi olehnya",
    ),
    "threeDayReject": MessageLookupByLibrary.simpleMessage(
      "Tiga hari tanpa pemrosesan akan ditolak secara otomatis",
    ),
    "throwAway": MessageLookupByLibrary.simpleMessage("Menyingkirkan"),
    "throwBottleInOcean": MessageLookupByLibrary.simpleMessage(
      "Klik ini, tulis apa pun yang ingin anda katakan ke dalam botol, kemudian buang botol tersebut ke laut, mungkin seseorang akan memungutnya.",
    ),
    "throwTheBottle": MessageLookupByLibrary.simpleMessage("Lemparkan botol"),
    "ticket": MessageLookupByLibrary.simpleMessage("Tiket"),
    "tiePk": MessageLookupByLibrary.simpleMessage("Pertarungan Seri"),
    "time": MessageLookupByLibrary.simpleMessage("Waktu"),
    "timeLeftToday": m244,
    "timeOfDuration": MessageLookupByLibrary.simpleMessage("Durasi"),
    "timeToComment": MessageLookupByLibrary.simpleMessage(
      "Pertunjukan sudah berakhir. Ini saatnya untuk memberikan komentar",
    ),
    "times": MessageLookupByLibrary.simpleMessage("kali"),
    "timesFree": m245,
    "timesNum": m246,
    "timesUsedUp": MessageLookupByLibrary.simpleMessage("Kehabisan waktu"),
    "tipCompleteInfo": MessageLookupByLibrary.simpleMessage(
      "Lengkapi profil saya",
    ),
    "tipFollowBack": MessageLookupByLibrary.simpleMessage(
      "Ikuti kembali untuk menjadi teman.",
    ),
    "tipHasFollowing": MessageLookupByLibrary.simpleMessage(
      "Mengikuti Pengguna ini.",
    ),
    "tipPassions": MessageLookupByLibrary.simpleMessage(
      "Ini adalah kesempatan sempurna untuk menunjukkan lebih banyak tentang anda.",
    ),
    "tipPublishInput": MessageLookupByLibrary.simpleMessage(
      "Apa yang menarik?",
    ),
    "tipSetAvatar": MessageLookupByLibrary.simpleMessage("Pilih avatarmu"),
    "tips": MessageLookupByLibrary.simpleMessage("Tips"),
    "title": MessageLookupByLibrary.simpleMessage("Titel"),
    "titleEffectPreviewMsgContent": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di keluarga.",
    ),
    "titleGifters": MessageLookupByLibrary.simpleMessage(
      "Titel pemberi hadiah",
    ),
    "titleList": MessageLookupByLibrary.simpleMessage("Daftar judul"),
    "titleVoiceVerifyGuide": MessageLookupByLibrary.simpleMessage(
      "Bagaimana cara mendapatkan lebih banyak balasan?",
    ),
    "titleWearDesc": MessageLookupByLibrary.simpleMessage(
      "Anda dapat memakai titel yang diperoleh di Winker. Titel berasal dari aktivitas dan pencapaian yang anda selesaikan di Winker. Titel yang anda pakai akan ditampilkan di profil dan profil ruangan anda. Anda dapat memakai hingga tiga titel.",
    ),
    "titleWearInstructions": MessageLookupByLibrary.simpleMessage(
      "Instruksi pemakaian titel",
    ),
    "tmo": MessageLookupByLibrary.simpleMessage("TMO"),
    "to": MessageLookupByLibrary.simpleMessage("Ke"),
    "toAddIntimacy": MessageLookupByLibrary.simpleMessage(
      "untuk menambah keintiman",
    ),
    "toChange": MessageLookupByLibrary.simpleMessage("untuk mengubah"),
    "toChoose": MessageLookupByLibrary.simpleMessage("Memilih"),
    "toLater": MessageLookupByLibrary.simpleMessage("Nanti"),
    "toMatch": MessageLookupByLibrary.simpleMessage(" cocokan"),
    "toUseProp": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda ingin menggunakan alat ini?",
    ),
    "today": MessageLookupByLibrary.simpleMessage("Hari ini"),
    "todayBestMatch": MessageLookupByLibrary.simpleMessage(
      "Pencocokan terbaik hari ini",
    ),
    "todayForCast": MessageLookupByLibrary.simpleMessage(
      "Prakiraan untuk hari ini",
    ),
    "todayTimesRunOut": MessageLookupByLibrary.simpleMessage(
      "Waktu hari ini habis",
    ),
    "todayUpperLimit": MessageLookupByLibrary.simpleMessage(
      "Batas atas hari ini: ",
    ),
    "todayWinnings": MessageLookupByLibrary.simpleMessage(
      "Kemenangan Hari Ini",
    ),
    "together": MessageLookupByLibrary.simpleMessage("Bersama"),
    "togetherDays": m247,
    "togetherDaysNewLine": m248,
    "tomorrow": MessageLookupByLibrary.simpleMessage("Besok"),
    "tomorrowGetMoreRewards": MessageLookupByLibrary.simpleMessage(
      "Besok login akan mendapatkan lebih banyak hadiah",
    ),
    "tooFastAndTakeARest": MessageLookupByLibrary.simpleMessage(
      "Kamu terlalu cepat, tolong istirahatlah...",
    ),
    "tooFrequentlyTip": MessageLookupByLibrary.simpleMessage(
      "Permintaan terlalu sering, coba lagi nanti",
    ),
    "topCharming": MessageLookupByLibrary.simpleMessage("Penawan top"),
    "topCharmingIs": MessageLookupByLibrary.simpleMessage(
      "Yang paling menawan adalah",
    ),
    "topIndex": m249,
    "topSupporter": MessageLookupByLibrary.simpleMessage("Pendukung top"),
    "topSupporterIs": MessageLookupByLibrary.simpleMessage(
      "Pendukung top adalah",
    ),
    "topWinnersOfToday": MessageLookupByLibrary.simpleMessage(
      "Pemenang teratas hari ini",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalMusic": m250,
    "totalPeople": m251,
    "transfer": MessageLookupByLibrary.simpleMessage("Mengirim"),
    "transferCaptain": MessageLookupByLibrary.simpleMessage("Kapten Transfer"),
    "treasureBoxRoomLevelLimit": m252,
    "treasureChestItemTips": MessageLookupByLibrary.simpleMessage(
      "Baru saja menerima peti harta karun",
    ),
    "treasureChestNum": MessageLookupByLibrary.simpleMessage(
      "Nomor peti harta karun: ",
    ),
    "treasureChestRecord": MessageLookupByLibrary.simpleMessage(
      "Catatan Peti Harta Karun",
    ),
    "treasureChestTips": MessageLookupByLibrary.simpleMessage(
      "Semakin banyak peti harta karun yang diterima fans, semakin banyak hadiah luar biasa yang akan diterima kalian sebagai Host!",
    ),
    "treasureTips": MessageLookupByLibrary.simpleMessage(
      "Datang dan bagikan hadiah dari harta karun sekarang!",
    ),
    "treasureTitle": MessageLookupByLibrary.simpleMessage(
      "Tugas untuk harta karun",
    ),
    "treasureTotal": MessageLookupByLibrary.simpleMessage(
      "Total harta karun hari ini:",
    ),
    "triedTooMuchAndComeTomorrow": MessageLookupByLibrary.simpleMessage(
      "Anda mencoba terlalu banyak hari ini, silakan coba lagi besok.",
    ),
    "truth": MessageLookupByLibrary.simpleMessage("Jujur"),
    "truthDare": MessageLookupByLibrary.simpleMessage("Jujur&Tantangan"),
    "truthDarePeopleCount": m253,
    "truthDareQuestion": m254,
    "truthDareRunAway": m255,
    "truthGameIntroSubtitle1": MessageLookupByLibrary.simpleMessage(
      "Pengguna dianggap telah bergabung dalam game setelah mereka naik ke mic.",
    ),
    "truthGameIntroSubtitle2": MessageLookupByLibrary.simpleMessage(
      "Saat roda berputar, semua pemain yang terlibat dalam permainan memiliki peluang untuk dipilih, sepenuhnya secara acak.",
    ),
    "truthGameIntroSubtitle3": MessageLookupByLibrary.simpleMessage(
      "Pemain yang dipilih, harus menyelesaikan Truth or Dare, yang diberikan secara acak oleh sistem. Tema yang dipilih harus diselesaikan oleh pemain yang dipilih.",
    ),
    "truthGameIntroTitle1": MessageLookupByLibrary.simpleMessage(
      "Bersiaplah untuk naik ke mic.",
    ),
    "truthGameIntroTitle2": MessageLookupByLibrary.simpleMessage(
      "Roda berputar.",
    ),
    "truthGameIntroTitle3": MessageLookupByLibrary.simpleMessage(
      "Arsip pertanyaan",
    ),
    "truthOngoing": MessageLookupByLibrary.simpleMessage("Process"),
    "truthSelected": MessageLookupByLibrary.simpleMessage("Dipilih"),
    "truthStart": MessageLookupByLibrary.simpleMessage("MULAI"),
    "truthStartLimit": MessageLookupByLibrary.simpleMessage(
      "Setidaknya harus ada dua orang atau lebih bergabung dalam permainan",
    ),
    "tryAgain": MessageLookupByLibrary.simpleMessage("Coba lagi"),
    "tryAnotherWay": MessageLookupByLibrary.simpleMessage(
      "Ingin mencoba cara lain?",
    ),
    "tryBottle": MessageLookupByLibrary.simpleMessage(
      "Coba surat rahasia, anda dapat menemukan lebih banyak teman di Winker",
    ),
    "tryBtn": MessageLookupByLibrary.simpleMessage("Coba"),
    "tryIt": MessageLookupByLibrary.simpleMessage("Cobanya"),
    "tryToTalkHerLabels": MessageLookupByLibrary.simpleMessage(
      "Cobalah untuk membicarakan labelnya",
    ),
    "tryToTalkHisLabels": MessageLookupByLibrary.simpleMessage(
      "Cobalah untuk membicarakan labelnya",
    ),
    "tryVoiceMatching": MessageLookupByLibrary.simpleMessage(
      "Cobalah untuk tes suara, anda akan menemukan lebih banyak teman di winker",
    ),
    "tryWriting": MessageLookupByLibrary.simpleMessage(
      "Cobalah menulis seperti ini",
    ),
    "turnOn": MessageLookupByLibrary.simpleMessage("Buka"),
    "turnOnNotifications": MessageLookupByLibrary.simpleMessage(
      "Aktifkan notifikasi untuk mengetahui pesan baru",
    ),
    "turnOnSwitchCanHearOppositeVoice": MessageLookupByLibrary.simpleMessage(
      "Nyalakan sakelar dapat mendengar suara ruangan seberang (semua mikrofon)",
    ),
    "turnOnToKnowMessages": MessageLookupByLibrary.simpleMessage(
      "Aktifkan notifikasi untuk mengetahui pesan dan Match baru.",
    ),
    "turntable": MessageLookupByLibrary.simpleMessage("Meja putar"),
    "turntableGameFail": MessageLookupByLibrary.simpleMessage(
      "Maaf, anda tidak menang di ronde ini.",
    ),
    "turntableGameNoJoin": MessageLookupByLibrary.simpleMessage(
      "Anda tidak berpartisipasi dalam ronde ini.",
    ),
    "turntableGameRules1": MessageLookupByLibrary.simpleMessage(
      "1. Pilih jumlah berlian, lalu pilih buah untuk menghabiskan berlian.",
    ),
    "turntableGameRules2": MessageLookupByLibrary.simpleMessage(
      "2. Anda dapat memilih hingga 8 buah di setiap ronde. Tidak ada batas atas jumlah berlian yang dapat Anda habiskan.",
    ),
    "turntableGameRules3": MessageLookupByLibrary.simpleMessage(
      "3. Untuk setiap ronde, Anda memiliki waktu 40 detik untuk memilih buah, dan mengundi buah yang menang setelahnya.",
    ),
    "turntableGameRules4": MessageLookupByLibrary.simpleMessage(
      "4. Jika Anda menghabiskan berlian pada buah yang menang, Anda akan memenangkan hadiah yang sesuai.",
    ),
    "turntableGameRules5": MessageLookupByLibrary.simpleMessage(
      "5. Hak interpretasi terakhir dari game ini adalah milik Winker.",
    ),
    "turntableGameWin": m256,
    "turntableIsClosed": MessageLookupByLibrary.simpleMessage(
      "Meja putar ini ditutup.",
    ),
    "turntableResult": MessageLookupByLibrary.simpleMessage(
      "Hasil meja putar: ",
    ),
    "turntableStartTip": MessageLookupByLibrary.simpleMessage(
      "Anda telah dipilih dan Anda dapat memutar meja putar ini",
    ),
    "twentyOneCentury": MessageLookupByLibrary.simpleMessage("Abad ke-21"),
    "twoDays": MessageLookupByLibrary.simpleMessage("2 Hari"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage("Ketik pesan"),
    "typeAnswer": m257,
    "typeInPcBrowser": MessageLookupByLibrary.simpleMessage(
      "Ketik tautan berikut di browser PC",
    ),
    "typeTourSuggestionsToUs": MessageLookupByLibrary.simpleMessage(
      "Ketik saran Anda kepada kami...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("Ketik..."),
    "unCoverIdentityAlert": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi untuk mengirim undangan mengungkap profil Winker?",
    ),
    "unCoverIdentityMsg": MessageLookupByLibrary.simpleMessage(
      "Undang pihak lain untuk mengungkap profil Winker ?",
    ),
    "unCoverIdentitySystemTip": m258,
    "unFollow": MessageLookupByLibrary.simpleMessage("Batal mengikuti"),
    "unFollow2": MessageLookupByLibrary.simpleMessage("Batal mengikuti"),
    "unFollowConfirm": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda yakin untuk berhenti mengikuti pengguna ini?",
    ),
    "unLockFreeChatTips": MessageLookupByLibrary.simpleMessage(
      "Membuka obrolan pesan gratis tidak akan lagi meningkatkan keintiman, mengirimkan hadiah dapat meningkatkan keintiman.",
    ),
    "unSubscribeTip": MessageLookupByLibrary.simpleMessage(
      "Berhenti berlangganan acara, Anda akan melewatkan kejutannya!",
    ),
    "unSupportedAssetType": MessageLookupByLibrary.simpleMessage(
      "Jenis File HEIC tidak didukung.",
    ),
    "unableToAccessAll": MessageLookupByLibrary.simpleMessage(
      "Tidak dapat mengakses semua File di perangkat",
    ),
    "unblock": MessageLookupByLibrary.simpleMessage("Buka blokir"),
    "unblockSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Buka blokir berhasil",
    ),
    "unburden": MessageLookupByLibrary.simpleMessage("Ungkapkan"),
    "uncoverIdentity": MessageLookupByLibrary.simpleMessage("Ungkapkan profil"),
    "uncoverLimit": MessageLookupByLibrary.simpleMessage(
      "Anda hanya dapat mengungkap sekali sehari.",
    ),
    "underReview": MessageLookupByLibrary.simpleMessage("Sedang Ditinjau"),
    "undercover": MessageLookupByLibrary.simpleMessage("Penyamar"),
    "undercoverDefeat": MessageLookupByLibrary.simpleMessage("Penyamar kalah"),
    "undercoverFaq": MessageLookupByLibrary.simpleMessage(
      "1. Pada saatnya game dimulai di awal, setiap orang akan mendapatkan satu kata. Perhatikan bahwa ada salah satu kata yang berbeda dari kata-kata yang lain. Cari tahu siapa penyamar itu. Si penyamar, harap sembunyikan identitas Anda dan hidup sampai akhir.\n2. Setiap pemain memiliki waktu 30 detik di setiap ronde untuk mendeskripsikan kata mereka, secara bergiliran.\n3. Setelah deskripsi selesai, semua pemain memiliki waktu 20 detik untuk bersuara.\n4. Pemain dengan suara terbanyak akan dieliminasi. Jika seri, akan ada ronde kematian mendadak di mana para pemain mendeskripsikan kata-kata mereka lagi. Jika tidak ada suara, permainan berlanjut ke babak berikutnya. ",
    ),
    "undercoverNotAllowedToSpeak": MessageLookupByLibrary.simpleMessage(
      "Selama game, pengguna yang tidak berada di babak deskripsi saat ini tidak diperbolehkan untuk berbicara.",
    ),
    "undercoverTip": MessageLookupByLibrary.simpleMessage(
      "Harap tentukan apakah Anda seorang penyamar atau orang sipil, tetaplah sembunyikan identitas Anda atau bersuara untuk menghilangkan penyamar.",
    ),
    "undercoverVictory": MessageLookupByLibrary.simpleMessage(
      "Penyamar menang",
    ),
    "undone": MessageLookupByLibrary.simpleMessage("Belum selesai"),
    "unfollow": MessageLookupByLibrary.simpleMessage("Berhenti mengikuti"),
    "unfollowRoomWillRemoveAdminIdentity": MessageLookupByLibrary.simpleMessage(
      "Berhenti mengikuti ruangan akan menghapus identitas admin anda",
    ),
    "unfollowSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhenti mengikut",
    ),
    "unfortunately": MessageLookupByLibrary.simpleMessage("Sangat disayangkan"),
    "unionIncome": MessageLookupByLibrary.simpleMessage("Pendapatan Agen"),
    "uniquePersonalId": MessageLookupByLibrary.simpleMessage(
      "Id pribadi yang unik",
    ),
    "uniqueRoomId": MessageLookupByLibrary.simpleMessage("ID kamar unik"),
    "unknownError": MessageLookupByLibrary.simpleMessage(
      "Kesalahan yang belum diketahui",
    ),
    "unlimited": MessageLookupByLibrary.simpleMessage("Tak terbatas"),
    "unlimitedChat": MessageLookupByLibrary.simpleMessage(
      "Obrolan tanpa batas",
    ),
    "unlimitedChatDesc": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengobrol apa pun yang Anda inginkan tanpa batasan.",
    ),
    "unlock": MessageLookupByLibrary.simpleMessage("Terkunci"),
    "unlockChat": MessageLookupByLibrary.simpleMessage(
      "Kirim hadiah untuk membuka kunci obrolan",
    ),
    "unlockFeatureDigest": MessageLookupByLibrary.simpleMessage(
      "[Buka kunci fitur]",
    ),
    "unlockHerAnswer": MessageLookupByLibrary.simpleMessage(
      "Jawab pertanyaan untuk membuka kunci jawabannya",
    ),
    "unlockHisAnswer": MessageLookupByLibrary.simpleMessage(
      "Jawab pertanyaan untuk membuka kunci jawabannya",
    ),
    "unlockMore": MessageLookupByLibrary.simpleMessage(
      "Buka kunci lebih banyak",
    ),
    "unlockTheMic": MessageLookupByLibrary.simpleMessage("Buka Kunci Mikrofon"),
    "unlocked": MessageLookupByLibrary.simpleMessage("Buka kunci"),
    "unmute": MessageLookupByLibrary.simpleMessage("Batal Bisukan"),
    "uno": MessageLookupByLibrary.simpleMessage("Uno"),
    "unpin": MessageLookupByLibrary.simpleMessage("Tidak Semat"),
    "unqualified": MessageLookupByLibrary.simpleMessage(
      "Tidak memenuhi syarat",
    ),
    "unqualifiedUser": MessageLookupByLibrary.simpleMessage(
      "Pengguna yang tidak memenuhi syarat",
    ),
    "unsupportedVideo": MessageLookupByLibrary.simpleMessage(
      "Video tidak didukung, harap tingkatkan versi aplikasi.",
    ),
    "upMicGuideContent": MessageLookupByLibrary.simpleMessage(
      "Bergabung dalam Percakapan!",
    ),
    "update": MessageLookupByLibrary.simpleMessage("Memperbarui"),
    "updateNow": MessageLookupByLibrary.simpleMessage("Perbaharui sekarang"),
    "updateTime": m259,
    "updatingNow": MessageLookupByLibrary.simpleMessage("perbarui sekarang"),
    "upgradeRole": MessageLookupByLibrary.simpleMessage("Perbarui Sekarang"),
    "upgradeRoomLevelTip": m260,
    "upgradeYourWinkerVersion": MessageLookupByLibrary.simpleMessage(
      "Tingkatkan versi Winker anda",
    ),
    "uploadBackground": MessageLookupByLibrary.simpleMessage(
      "Unggah latar belakang",
    ),
    "uploadClearPhoto": MessageLookupByLibrary.simpleMessage(
      "Harap unggah foto tubuh bagian atas yang jelas, jika tidak, konversi akan gagal.",
    ),
    "uploadFailed": MessageLookupByLibrary.simpleMessage(
      "Upload gagal, coba lagi nanti",
    ),
    "uploadGifAvatar": MessageLookupByLibrary.simpleMessage(
      "Unggah Avatar Gif",
    ),
    "uploadList": MessageLookupByLibrary.simpleMessage("Daftar unggah"),
    "uploadMusic": MessageLookupByLibrary.simpleMessage("Unggah Musik"),
    "uploadMusicToYourPhone": MessageLookupByLibrary.simpleMessage(
      "Tidak ada musik yang tersedia, unggah musik ke ponsel anda terlebih dahulu",
    ),
    "uploadPicAvatar": MessageLookupByLibrary.simpleMessage(
      "Unggah Avatar foto",
    ),
    "uploadSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Berhasil diunggah",
    ),
    "uploadTheme": MessageLookupByLibrary.simpleMessage("Unggah tema"),
    "uploadTimeUsedUpDesc": MessageLookupByLibrary.simpleMessage(
      "Waktu upload anda habis hari ini, kembali lagi besok!",
    ),
    "upperLimit": MessageLookupByLibrary.simpleMessage("Batas"),
    "use": MessageLookupByLibrary.simpleMessage("menggunakan"),
    "useDiamond": m261,
    "used": MessageLookupByLibrary.simpleMessage("Bekas"),
    "user": MessageLookupByLibrary.simpleMessage("Pengguna"),
    "userA": MessageLookupByLibrary.simpleMessage("Pengguna A"),
    "userAboutMe": MessageLookupByLibrary.simpleMessage("Tentang saya"),
    "userAboutOther": m262,
    "userApplyTakeMic": MessageLookupByLibrary.simpleMessage(
      "mengajukan naik mic",
    ),
    "userB": MessageLookupByLibrary.simpleMessage("Pengguna B"),
    "userCar": MessageLookupByLibrary.simpleMessage("Kendaraan"),
    "userCleanedChat": MessageLookupByLibrary.simpleMessage(
      " membersihkan obrolan ini",
    ),
    "userCloseVideoMsg": m263,
    "userContent": MessageLookupByLibrary.simpleMessage("Konten pengguna"),
    "userCpSuccess": m264,
    "userEmptyPostTip": MessageLookupByLibrary.simpleMessage(
      "Posting untuk mengenal anda lebih baik",
    ),
    "userExitTheFamily": m265,
    "userGetDiamondsInPk": m266,
    "userGuide": MessageLookupByLibrary.simpleMessage("Panduan pengguna"),
    "userHasItem": MessageLookupByLibrary.simpleMessage(
      "Pengguna ini telah membeli item tersebut",
    ),
    "userHavingParty": m267,
    "userIntroEmptyTip": MessageLookupByLibrary.simpleMessage(
      "Tulis bio untuk memperkenalkan dirimu~",
    ),
    "userIsWinnerInPk": m268,
    "userJoinTheFamily": m269,
    "userJoinedTheGame": MessageLookupByLibrary.simpleMessage(
      "Pengguna telah bergabung dalam permainan",
    ),
    "userKickedOutOfFamilyBy": m270,
    "userLevel": MessageLookupByLibrary.simpleMessage("Level Pengguna"),
    "userMatchingYourResult": MessageLookupByLibrary.simpleMessage(
      "Pengguna yang cocok dengan hasil Anda",
    ),
    "userMute": MessageLookupByLibrary.simpleMessage("Anda sudah dibisukan"),
    "userNotInRoom": MessageLookupByLibrary.simpleMessage(
      "Pengguna tidak ada di dalam ruangan.",
    ),
    "userPausedVideoMsg": m271,
    "userPickUser": m272,
    "userPlayVideoMsg": m273,
    "userProfileCard": MessageLookupByLibrary.simpleMessage(
      "Halaman informasi pribadi di ruangan",
    ),
    "userSetAsMaster": m274,
    "userSetAsVicePatriarch": m275,
    "userStartGiftPk": m276,
    "userStartVotePk": m277,
    "userTmpBanned": MessageLookupByLibrary.simpleMessage(
      "Pengguna ini diblokir karena melanggar peraturan komunitas.",
    ),
    "userTookScreenshot": m278,
    "userTurnedResult": m279,
    "userUpdateStatus": m280,
    "usernameInvitedYouToJoinFamilynameDoYouAgreeTo": m281,
    "usernameRefuseYourFamilyApplication": m282,
    "usernameRejectedYourApplicationGoCheckOutOtherFamiliesGo": m283,
    "usernameWantsToJoinYourFamilyPleaseReviewIt": m284,
    "usesOfGolds": MessageLookupByLibrary.simpleMessage("Penggunaan Koin"),
    "using": MessageLookupByLibrary.simpleMessage("Menggunakan"),
    "usingLivingArea": MessageLookupByLibrary.simpleMessage(
      "Menggunakan area tinggal",
    ),
    "validEmailTip": MessageLookupByLibrary.simpleMessage(
      "Silakan masukkan format email dengan benar.",
    ),
    "validUntilData": m285,
    "valueDiamond": m286,
    "verificationCodeError": MessageLookupByLibrary.simpleMessage(
      "Kode verifikasi salah",
    ),
    "verificationInReview": MessageLookupByLibrary.simpleMessage(
      "Verifikasi anda sedang ditinjau",
    ),
    "verificationPassed": MessageLookupByLibrary.simpleMessage(
      "Verifikasi lulus",
    ),
    "verificationProtectYou": MessageLookupByLibrary.simpleMessage(
      "Verifikasi dapat melindungi anda",
    ),
    "verificationToGet": MessageLookupByLibrary.simpleMessage(
      "Verifikasi untuk mendapatkan",
    ),
    "verifyFailed": MessageLookupByLibrary.simpleMessage("Verifikasi gagal"),
    "verifyProfile": MessageLookupByLibrary.simpleMessage("Verifikasi profil"),
    "verifyProfileSecurely": MessageLookupByLibrary.simpleMessage(
      "Verifikasi profil dengan aman",
    ),
    "verifyToUnlock": MessageLookupByLibrary.simpleMessage(
      "Verifikasi untuk membuka kunci",
    ),
    "verifyVoiceBeforeListening": MessageLookupByLibrary.simpleMessage(
      "Anda belum Memverifikasi suara anda. Harap verifikasi suara anda sebelum mendengarkan. ",
    ),
    "verifyVoiceBeforeMatching": MessageLookupByLibrary.simpleMessage(
      "Anda belum Memverifikasi suara anda. Harap verifikasi suara anda sekarang untuk membuka kunci fitur kami!",
    ),
    "versionOutOfDate": MessageLookupByLibrary.simpleMessage(
      "Versi aplikasi Anda saat ini kedaluwarsa, dapatkan versi terbaru untuk menikmati Fitur baru.",
    ),
    "vicePatriarch": MessageLookupByLibrary.simpleMessage("Wakil Ketua Family"),
    "victory": MessageLookupByLibrary.simpleMessage("Menang"),
    "video": MessageLookupByLibrary.simpleMessage("Video"),
    "videoCall": MessageLookupByLibrary.simpleMessage("Panggilan Video"),
    "videoCannotPlay": MessageLookupByLibrary.simpleMessage(
      "Video ini tidak dapat diputar, harap ganti ke video lain",
    ),
    "videoRoom": MessageLookupByLibrary.simpleMessage("Kamar video"),
    "videoTime": m287,
    "videoTryAgain": MessageLookupByLibrary.simpleMessage(
      "Ada yang salah, harap coba lagi.",
    ),
    "view": MessageLookupByLibrary.simpleMessage("Lihat"),
    "viewAll": MessageLookupByLibrary.simpleMessage("Lihat semua"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("Melihat detailnya > "),
    "viewLuckyBagClaimDetails": MessageLookupByLibrary.simpleMessage(
      "Lihat detail pengambilan lucky bag",
    ),
    "viewProfile": MessageLookupByLibrary.simpleMessage("Lihat profil"),
    "viewRules": MessageLookupByLibrary.simpleMessage("Lihat aturan"),
    "viewingLimitedAssetsTip": MessageLookupByLibrary.simpleMessage(
      "Hanya lihat File  dan album yang dapat diakses oleh aplikasi.",
    ),
    "viewingProfile": MessageLookupByLibrary.simpleMessage("Melihat profil"),
    "violence": MessageLookupByLibrary.simpleMessage("kekerasan"),
    "vip": MessageLookupByLibrary.simpleMessage("Vip"),
    "vipActivityPromotion": MessageLookupByLibrary.simpleMessage(
      "Promosi event ruang",
    ),
    "vipActivityPromotionTips": m288,
    "vipAdSendGifts": MessageLookupByLibrary.simpleMessage(
      "Perwakilan pelanggan mengirim hadiah",
    ),
    "vipAdSendGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Hubungi layanan pelanggan VIP untuk mengirim hadiah di kamar Anda",
    ),
    "vipAddFriends": MessageLookupByLibrary.simpleMessage(
      "Tambahkan fungsi teman",
    ),
    "vipAddFriendsTips": MessageLookupByLibrary.simpleMessage(
      "Ketika seseorang memulai percakapan dengan Anda, mereka harus meminta izin Anda",
    ),
    "vipAvatarFrame": MessageLookupByLibrary.simpleMessage(
      "Bingkai avatar VIP eksklusif",
    ),
    "vipAvatarFrameTips": MessageLookupByLibrary.simpleMessage(
      "Dapatkan lebih banyak perhatian dengan bingkai VIP khusus Anda",
    ),
    "vipBadge": MessageLookupByLibrary.simpleMessage("Medali VIP"),
    "vipBadgeTips1": MessageLookupByLibrary.simpleMessage(
      "Pesan obrolan di kamar",
    ),
    "vipBadgeTips2": MessageLookupByLibrary.simpleMessage(
      "Kartu profil di kamar",
    ),
    "vipBadgeTips3": MessageLookupByLibrary.simpleMessage(
      "Daftar pengguna online",
    ),
    "vipBadgeTips4": MessageLookupByLibrary.simpleMessage("Profil"),
    "vipBadgeTips5": MessageLookupByLibrary.simpleMessage("Momen"),
    "vipCanChangeFriendsRequestSettings": MessageLookupByLibrary.simpleMessage(
      "Pengguna VIP dapat mengubah pengaturan permintaan pertemanan.",
    ),
    "vipCenter": MessageLookupByLibrary.simpleMessage("Pusat VIP"),
    "vipChatFrame": MessageLookupByLibrary.simpleMessage(
      "Bingkai obrolan VIP eksklusif",
    ),
    "vipChatFrameTips": MessageLookupByLibrary.simpleMessage(
      "Tampilkan pesan Anda ke teman Anda dengan bingkai cantik",
    ),
    "vipColoredUsername": MessageLookupByLibrary.simpleMessage(
      "Nama pengguna berwarna",
    ),
    "vipColoredUsernameTips": MessageLookupByLibrary.simpleMessage(
      "Menarik perhatian semua orang dengan nama berwarna",
    ),
    "vipCustomerService": MessageLookupByLibrary.simpleMessage(
      "Ahli layanan pelanggan",
    ),
    "vipCustomerServiceTips": MessageLookupByLibrary.simpleMessage(
      "Nikmati layanan terbaik dari ahli layanan pelanggan kami",
    ),
    "vipCustomizeAvatar": MessageLookupByLibrary.simpleMessage("Avatar khusus"),
    "vipCustomizeAvatarTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengunggah foto favorit Anda dan menjadikannya avatar Anda",
    ),
    "vipCustomizeRoomTheme": MessageLookupByLibrary.simpleMessage(
      "Sesuaikan tema kamar",
    ),
    "vipCustomizeRoomThemeTips": MessageLookupByLibrary.simpleMessage(
      "Tetapkan tema ruang khusus untuk menunjukkan gaya Anda",
    ),
    "vipDoubleRewards": MessageLookupByLibrary.simpleMessage(
      "Masuk ganda dan hadiah tugas",
    ),
    "vipDoubleRewardsTips": MessageLookupByLibrary.simpleMessage(
      "Dapatkan hadiah ganda untuk masuk dan menyelesaikan tugas",
    ),
    "vipEnterEffect": MessageLookupByLibrary.simpleMessage(
      "Efek Enter Eksklusif",
    ),
    "vipEnterEffectTips": MessageLookupByLibrary.simpleMessage(
      "Menarik perhatian semua orang dengan efek enter yang menarik",
    ),
    "vipExclusiveAvatarFrame": MessageLookupByLibrary.simpleMessage(
      "Bingkai avatar kustomisasi eksklusif",
    ),
    "vipExclusiveAvatarFrameTips": MessageLookupByLibrary.simpleMessage(
      "Silahkan hubungi layanan VIP kami. Kami akan membuat bingkai avatar Anda sendiri untuk Anda",
    ),
    "vipExpRequirement": MessageLookupByLibrary.simpleMessage(
      "Persyaratan VIP exp",
    ),
    "vipExps": MessageLookupByLibrary.simpleMessage("3. VIP Exps"),
    "vipExpsDetail": MessageLookupByLibrary.simpleMessage(
      "VIP Exps bisa Anda dapatkan dengan membeli diamond, 10 VIP Exps bisa ditukar dengan 1 USD. Tidak ada batasan atas berapa banyak VIP Exps yang bisa Anda peroleh per hari. Jika Anda mengembalikan dana pembelian, semua VIP Exps yang Anda peroleh untuk pembelian itu akan dikurangi. Poin VIP yang diperlukan untuk level sebelumnya ditunjukan di gambar berikut.",
    ),
    "vipFreeze": MessageLookupByLibrary.simpleMessage("4. Pembekuan VIP"),
    "vipFreezeDetail": MessageLookupByLibrary.simpleMessage(
      "Jika Anda tidak memiliki VIP Exps yang cukup yang dapat dipotong untuk membayar pengembalian dana, keanggotaan VIP Anda akan diblokir hingga Anda melunasi VIP Exps yang terhutang. Harap diperhatikan bahwa jika keanggotaan VIP Anda belum dibatalkan pembekuan dalam waktu 90 hari setelah dibekukan, poin VIP Anda tidak akan direset ke 0.",
    ),
    "vipImage": MessageLookupByLibrary.simpleMessage("Gambar VIP"),
    "vipLevel": MessageLookupByLibrary.simpleMessage("1.Level VIP"),
    "vipLevelDetail": MessageLookupByLibrary.simpleMessage(
      "Keanggotaan VIP diperoleh melalui pembelian dalam aplikasi untuk mendapatkan VIP Exps.",
    ),
    "vipMicProtection": MessageLookupByLibrary.simpleMessage(
      "Perlindungan MIKROFON",
    ),
    "vipMicProtectionTips": MessageLookupByLibrary.simpleMessage(
      "Perlindungan dari penghapusan dari MIKROFON",
    ),
    "vipMysteriousVistors": MessageLookupByLibrary.simpleMessage(
      "Pengunjung misterius",
    ),
    "vipMysteriousVistorsTips": MessageLookupByLibrary.simpleMessage(
      "Kunjungi profil seseorang tanpa memberi tahu mereka siapa Anda",
    ),
    "vipNamedGifts": MessageLookupByLibrary.simpleMessage("Hadiah bernama"),
    "vipNamedGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Hubungi layanan VIP kami. Nama pengguna Anda akan ditampilkan pada hadiah spesial  ",
    ),
    "vipNum": m289,
    "vipPersonalId": MessageLookupByLibrary.simpleMessage(
      "Id pribadi yang unik",
    ),
    "vipPersonalIdTips": MessageLookupByLibrary.simpleMessage(
      "Hubungi layanan pelanggan VIP dan dapatkan ID pribadi unik secara gratis",
    ),
    "vipProfileBackground": MessageLookupByLibrary.simpleMessage(
      "Sesuaikan latar belakang profil",
    ),
    "vipProfileBackgroundTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengunggah foto favorit Anda dan menjadikannya latar belakang avatar Anda",
    ),
    "vipProfileCard": MessageLookupByLibrary.simpleMessage(
      "Kartu Profil Eksklusif",
    ),
    "vipProfileCardTips": MessageLookupByLibrary.simpleMessage(
      "Tunjukkan kehormatan Anda dengan kartu profil yang terkenal",
    ),
    "vipRoomProtection": MessageLookupByLibrary.simpleMessage(
      "Perlindungan Kamar",
    ),
    "vipRoomProtectionTips": MessageLookupByLibrary.simpleMessage(
      "Perlindungan dari pemindahan keluar kamar",
    ),
    "vipSendRoomImage": MessageLookupByLibrary.simpleMessage(
      "Kirim gambar di kamar",
    ),
    "vipSendRoomImageTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengirim gambar di kamar",
    ),
    "vipSettled": MessageLookupByLibrary.simpleMessage(
      "Level VIP Diselesaikan",
    ),
    "vipStickersOnMIC": MessageLookupByLibrary.simpleMessage(
      "Stiker eksklusif di MIKROFON",
    ),
    "vipStickersOnMICTips": MessageLookupByLibrary.simpleMessage(
      "Gunakan stiker eksklusif saat Anda berbicara di MIKROFON.",
    ),
    "vipUnlimitedText": MessageLookupByLibrary.simpleMessage(
      "Teks tanpa batas",
    ),
    "vipUnlimitedTextTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat mengirim teks apa pun yang Anda inginkan tanpa batasan",
    ),
    "vipUserList": MessageLookupByLibrary.simpleMessage(
      "Baris depan pada daftar Pengguna",
    ),
    "vipUserListTips": MessageLookupByLibrary.simpleMessage(
      "Nikmati barisan depan di daftar kamar mana pun",
    ),
    "vipValidity": MessageLookupByLibrary.simpleMessage("2. Validitas VIP"),
    "vipValidityDetail": MessageLookupByLibrary.simpleMessage(
      "Setiap level VIP berlaku selama 90 hari. Selama periode 90 hari, jika VIP Exps Anda mencapai persyaratan level yang lebih tinggi, level VIP Anda akan segera ditingkatkan dan VIP Exps Anda akan diatur ulang ke 0. Peningkatan level dengan melewatkan level tertentu dapat didukung.\nJika level VIP Anda tidak ditingkatkan selama periode ini, setelah periode ini berakhir, level VIP Anda akan direset sesuai dengan VIP Exps yang Anda peroleh selama periode ini, dan \nVIP Exps akan direset ke 0. Untuk mempertahankan level VIP Anda saat ini, Anda perlu mengumpulkan VIP Exps yang diperlukan untuk level sebelumnya selama periode ini. Jika tidak, level VIP Anda akan diturunkan.\nJika Anda belum menjadi VIP, ketika VIP Exps Anda memenuhi persyaratan level VIP tertentu dalam waktu 90 hari, Anda akan mencapai level VIP tersebut dan VIP Exps Anda akan diatur ulang ke 0. Jika Anda tidak mencapai level VIP mana pun dalam 90 hari, akumulasi poin VIP Anda akan disetel ulang menjadi 0 dalam 90 hari.",
    ),
    "vipVipGifts": MessageLookupByLibrary.simpleMessage("Hadiah VIP eksklusif"),
    "vipVipGiftsTips": MessageLookupByLibrary.simpleMessage(
      "Hanya pengguna VIP yang dapat mengirim hadiah ini",
    ),
    "vip_setting": MessageLookupByLibrary.simpleMessage("Pengaturan VIP"),
    "visibleToFriends": MessageLookupByLibrary.simpleMessage(
      "Dapat dilihat oleh teman",
    ),
    "visibleToMyself": MessageLookupByLibrary.simpleMessage(
      "Dapat dilihat oleh saya sendiri",
    ),
    "visibleToPublic": MessageLookupByLibrary.simpleMessage(
      "Dapat dilihat oleh publik",
    ),
    "visitSomeoneProfile": MessageLookupByLibrary.simpleMessage(
      "2.Kunjungi profil seseorang tanpa memberi tahu mereka siapa Anda",
    ),
    "visitors": MessageLookupByLibrary.simpleMessage("Pengunjung"),
    "voice": MessageLookupByLibrary.simpleMessage("Suara"),
    "voiceAdjust": MessageLookupByLibrary.simpleMessage("Penyesuaian suara"),
    "voiceDatingNoPeople": MessageLookupByLibrary.simpleMessage(
      "match terbaik anda tidak ada di sini sekarang, silakan coba lagi nanti.",
    ),
    "voiceDatingTimeUsedUp": MessageLookupByLibrary.simpleMessage(
      "Waktu hari ini telah habis, silakan coba lagi besok.",
    ),
    "voiceMatch": MessageLookupByLibrary.simpleMessage("Pencocokan suara"),
    "voiceParty": MessageLookupByLibrary.simpleMessage("Pesta Suara"),
    "voiceRecording": MessageLookupByLibrary.simpleMessage("Merekam"),
    "voiceSeconds": m290,
    "voiceTest": MessageLookupByLibrary.simpleMessage("Tes suara"),
    "voiceVerification": MessageLookupByLibrary.simpleMessage("Verifikasi"),
    "voiceVerified": MessageLookupByLibrary.simpleMessage(
      "Suara terverifikasi",
    ),
    "voiceVerifyChat1": MessageLookupByLibrary.simpleMessage("Hai"),
    "voiceVerifyChat2": MessageLookupByLibrary.simpleMessage(
      "Bagaimana kabarmu?",
    ),
    "voiceVerifyChat3": MessageLookupByLibrary.simpleMessage(
      "Suaramu terdengar merdu!",
    ),
    "voiceVerifyDesc": MessageLookupByLibrary.simpleMessage(
      "Hanya pengguna yang telah lulus verifikasi suara yang dapat mendengarkan suara satu sama lain.",
    ),
    "voiceVerifyMatchCount": m291,
    "vote": MessageLookupByLibrary.simpleMessage("Bersuara"),
    "voteForUndercover": MessageLookupByLibrary.simpleMessage(
      "Bersuara untuk pengguna yang menurut Anda paling seperti penyamar.",
    ),
    "votePk": MessageLookupByLibrary.simpleMessage("Pertarungan Suara"),
    "voted": MessageLookupByLibrary.simpleMessage("Telah bersuara"),
    "voting": MessageLookupByLibrary.simpleMessage("Bersuara"),
    "vulgarOrOffensive": MessageLookupByLibrary.simpleMessage(
      "bahasa vulgar atau menynggung",
    ),
    "waitBeforeDivorce": MessageLookupByLibrary.simpleMessage(
      "Tunggu 2 hari sebelum mengajukan cerai lagi",
    ),
    "waitForApproval": MessageLookupByLibrary.simpleMessage(
      "Harap tunggu persetujuan.",
    ),
    "waitForReply": MessageLookupByLibrary.simpleMessage(
      "Tunggu balasan sebelum mengirim pesan lainnya.",
    ),
    "waitSent": MessageLookupByLibrary.simpleMessage(
      "Tunggu beberapa saat untuk mengirim.",
    ),
    "waiting": MessageLookupByLibrary.simpleMessage("Menunggu"),
    "waitingFemaleChoice": MessageLookupByLibrary.simpleMessage(
      "Menunggu pilihannya",
    ),
    "waitingFirstStep": MessageLookupByLibrary.simpleMessage(
      "Menunggu langkah pertama Anda",
    ),
    "waitingForDrawing": MessageLookupByLibrary.simpleMessage(
      "Menunggu pengundian terakhir berakhir",
    ),
    "waitingForOtherPlayersToSettleGame": MessageLookupByLibrary.simpleMessage(
      "Menunggu pemain lain menyelesaikan permainan",
    ),
    "waitingForOtherSideAccept": MessageLookupByLibrary.simpleMessage(
      "Menunggu penerimaan",
    ),
    "waitingForTheGameToStart": MessageLookupByLibrary.simpleMessage(
      "Menunggu permainan dimulai",
    ),
    "waitingMaleChoice": MessageLookupByLibrary.simpleMessage(
      "Tunggu pilihannya",
    ),
    "waitingPlayer": MessageLookupByLibrary.simpleMessage("Menunggu pemain..."),
    "waitingReply": MessageLookupByLibrary.simpleMessage("Menunggu balasan"),
    "waitingStart": MessageLookupByLibrary.simpleMessage(
      "Menunggu untuk bermain！",
    ),
    "waitingToPlay": MessageLookupByLibrary.simpleMessage(
      "Menunggu untuk bermain",
    ),
    "wallet": MessageLookupByLibrary.simpleMessage("Dompet"),
    "wantJoinFamily": MessageLookupByLibrary.simpleMessage(
      "ingin bergabung dengan family",
    ),
    "wayCharmLevelUp": MessageLookupByLibrary.simpleMessage(
      "Terima hadiah di Winker",
    ),
    "wayToGetGolds": MessageLookupByLibrary.simpleMessage(
      "Cara Mendapatkan Koin",
    ),
    "wayWealthLevelUp": MessageLookupByLibrary.simpleMessage(
      "Habiskan diamond di Winker",
    ),
    "waysToLevelUp": MessageLookupByLibrary.simpleMessage("Cara Naik Level"),
    "weHaveTheInterests": MessageLookupByLibrary.simpleMessage(
      "Kami memiliki minat yang sama!",
    ),
    "weakSignal": MessageLookupByLibrary.simpleMessage("Sinyal lemah"),
    "wealth": MessageLookupByLibrary.simpleMessage("Kontribusi"),
    "wealthCharmLevelUpTitle": MessageLookupByLibrary.simpleMessage(
      "Selamat atas peningkatanmu!",
    ),
    "wealthLevel": MessageLookupByLibrary.simpleMessage("Kekayaan"),
    "wealthLevelUp": MessageLookupByLibrary.simpleMessage(
      "Selamat! Kekayaan Anda levelnya telah naik ke",
    ),
    "wear": MessageLookupByLibrary.simpleMessage("Pakai"),
    "wearANewRing": MessageLookupByLibrary.simpleMessage("Pakai cincin baru"),
    "wearBadge": MessageLookupByLibrary.simpleMessage("Pakai Lencana"),
    "wearBadgeRule": MessageLookupByLibrary.simpleMessage("Pakai medali"),
    "wearBadgeRule1": MessageLookupByLibrary.simpleMessage(
      "Menyala medali untuk pakai",
    ),
    "wearBadgeRule2": MessageLookupByLibrary.simpleMessage(
      "Maksimal 3 medali dapat dipakai",
    ),
    "wearBadgeRule3": MessageLookupByLibrary.simpleMessage(
      "Medali akan ditampilkan di halaman informasi pribadi, ruangan, dan obrolan",
    ),
    "wearRingExplain": MessageLookupByLibrary.simpleMessage(
      "1. Anda dapat menggunakan cincin bernilai apa pun.\n2. Memakai cincin baru tidak memerlukan persetujuan dari orang lain.\n3. Saat Anda menggunakan cincin baru, cincin lama tidak akan hilang, dan akan dimasukkan ke dalam kotak cincin.\n4. Waktu hari jadi pernikahan Anda tidak akan diubah karenanya.",
    ),
    "wearTitle": MessageLookupByLibrary.simpleMessage("Pakai Titel"),
    "wearing": MessageLookupByLibrary.simpleMessage("Memakai"),
    "weekly": MessageLookupByLibrary.simpleMessage("Mingguan"),
    "weeklyContribution": MessageLookupByLibrary.simpleMessage(
      "Kontribusi Mingguan",
    ),
    "weeklyList": MessageLookupByLibrary.simpleMessage("Daftar Mingguan"),
    "weeklyRank": MessageLookupByLibrary.simpleMessage("Peringkat mingguan"),
    "welcomeJoinRoom": MessageLookupByLibrary.simpleMessage(
      " , selamat datang di kamar! Jangan ragu untuk grab mikrofon dan mengikuti kami!",
    ),
    "welcomeToAlo": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di winker",
    ),
    "welcomeToCompleteSpace": MessageLookupByLibrary.simpleMessage(
      "Selamat datang untuk melengkapi kamar anonim.",
    ),
    "welcomeToTheFamily": m292,
    "welcomeToVoiceParty": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di pesta Suara",
    ),
    "welcomeToWinker": MessageLookupByLibrary.simpleMessage(
      "Selamat datang di Winker",
    ),
    "whatsApp": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "whenTheFamilyExperienceValueReachesTheUpperLimit":
        MessageLookupByLibrary.simpleMessage(
          "1. Jika Exp family telah mencapai batas maksimum, maka kontribusi individu juga akan mencapai batas. Pada hari itu, Anda tidak akan mendapatkan Exp family maupun kontribusi individu lagi.",
        ),
    "whenTheFamilyTreasuryReachesTodaysLimitOrThe":
        MessageLookupByLibrary.simpleMessage(
          "2. Jika harta family telah mencapai batas harian atau batas total, hari itu tidak akan ada lagi peningkatan.",
        ),
    "whetherSpendGoldsToFish": m293,
    "whichDay": m294,
    "whoBirthday": m295,
    "whoCanChatMe": MessageLookupByLibrary.simpleMessage(
      "Siapa yang bisa mengobrol dengan saya?",
    ),
    "whoEnterTheRoom": MessageLookupByLibrary.simpleMessage(
      "masuki ruangan ini",
    ),
    "whoHadWink": MessageLookupByLibrary.simpleMessage("Siapa yang aku wink"),
    "whoIsTheUndercover": MessageLookupByLibrary.simpleMessage(
      "Siapa penyamar?",
    ),
    "whoWinkAtYou": MessageLookupByLibrary.simpleMessage("Yang Wink kamu"),
    "whos": m296,
    "willLoseMatch": MessageLookupByLibrary.simpleMessage(
      "Anda akan kehilangan pasangan ini jika anda pergi sekarang.",
    ),
    "win": MessageLookupByLibrary.simpleMessage("Menang"),
    "wink": MessageLookupByLibrary.simpleMessage("Kedip"),
    "winkCountOutToast": MessageLookupByLibrary.simpleMessage(
      "Hitungan winker telah habis.",
    ),
    "winkList": MessageLookupByLibrary.simpleMessage("Teman Baru"),
    "winkerPremium": MessageLookupByLibrary.simpleMessage("Winker Premium"),
    "winningFruit": m297,
    "wishYouLuck": MessageLookupByLibrary.simpleMessage(
      "Semoga Anda beruntung!",
    ),
    "withdrawAvailable": MessageLookupByLibrary.simpleMessage(
      "Penarikan tersedia",
    ),
    "withdrawRequest": MessageLookupByLibrary.simpleMessage(
      "Pihak lawan mencabut permintaan ini.",
    ),
    "wonTheAward": m298,
    "wordCountExceeded": MessageLookupByLibrary.simpleMessage(
      "Jumlah kata terlampaui",
    ),
    "wordCountInsufficient": MessageLookupByLibrary.simpleMessage(
      "Jumlah kata tidak mencukupi",
    ),
    "work": MessageLookupByLibrary.simpleMessage("Bekerja"),
    "workTip": MessageLookupByLibrary.simpleMessage(
      "Aku baru saja mulai bisnisku sendiri dan membuka toko teh susu Honey Snow Ice City.",
    ),
    "world": MessageLookupByLibrary.simpleMessage("Dunia"),
    "worldwide": MessageLookupByLibrary.simpleMessage("Global"),
    "wouldYouLikeToShareVoice": MessageLookupByLibrary.simpleMessage(
      "Apakah anda ingin membagikan momen anda agar yang lain dapat mendengar suara anda?",
    ),
    "writeABio": MessageLookupByLibrary.simpleMessage(
      "Tulis Bio untuk memperkenalkan dirimu",
    ),
    "wrong": MessageLookupByLibrary.simpleMessage("Salah"),
    "xGolds": m299,
    "year": MessageLookupByLibrary.simpleMessage("Tahun"),
    "yes": MessageLookupByLibrary.simpleMessage("Ya"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Kemarin"),
    "you": MessageLookupByLibrary.simpleMessage("Anda"),
    "youAlreadyGotConversations": m300,
    "youAlreadySentAMagicKey": MessageLookupByLibrary.simpleMessage(
      "Anda sudah mengirim kunci kotak ajaib ke",
    ),
    "youAreGuestNow": MessageLookupByLibrary.simpleMessage(
      "Ditolak. Anda berada di mikrofon tamu sekarang",
    ),
    "youAreNotAMemberOfThisFamilyAndCannot": MessageLookupByLibrary.simpleMessage(
      "Anda bukan anggota family ini, jadi tidak bisa ikut merebut lucky bag",
    ),
    "youAreNotFamilyMember": MessageLookupByLibrary.simpleMessage(
      "Anda bukan anggota family ini.",
    ),
    "youAreSetAdmin": MessageLookupByLibrary.simpleMessage(
      "Anda diatur sebagai administrator",
    ),
    "youAreSuperAdmin": MessageLookupByLibrary.simpleMessage(
      "Anda berada dalam mode admin super dan tidak dapat bergabung dengan fungsi ini.",
    ),
    "youBastExperience": MessageLookupByLibrary.simpleMessage(
      "Tempat anda, Teman anda, Pengalaman terbaik anda.",
    ),
    "youCanAccessEachOtherProfiles": MessageLookupByLibrary.simpleMessage(
      "Panggilan dilakukan secara anonim. Anda dapat mengakses profil satu sama lain dengan mengungkapkan siapa diri Anda.",
    ),
    "youCanCreateEventTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat membuat acara untuk kamar manajemen.",
    ),
    "youCanInitiateConversation": MessageLookupByLibrary.simpleMessage(
      "4. Begitu Anda setuju, Anda dapat memulai percakapan",
    ),
    "youCanManagerWhoCanTalkYou": MessageLookupByLibrary.simpleMessage(
      "3. Anda dapat mengatur siapa yang dapat berbicara dengan Anda",
    ),
    "youCanNotFollowThisUserIntoRoom": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat mengikuti pengguna ini ke dalam kamar",
    ),
    "youCanOnlyOpenOnePlay": MessageLookupByLibrary.simpleMessage(
      "Anda hanya dapat membuka satu permainan dalam satu waktu",
    ),
    "youCanThrowIntoSea": MessageLookupByLibrary.simpleMessage(
      "Tidak ada yang akan mengenal Anda, memendam apa pun yang ingin Anda katakan dan tulis ke dalam botol, dan membuangnya ke laut.",
    ),
    "youCanUnlockTips": MessageLookupByLibrary.simpleMessage(
      "Anda dapat membuka kunci batasan dengan <h>",
    ),
    "youCannotChangeSeat": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat mengubah kursi",
    ),
    "youCantChangeInThisRound": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat mengganti peserta di ronde ini",
    ),
    "youCantSendBottle": MessageLookupByLibrary.simpleMessage(
      "Anda kehabisan waktu hari ini, silakan kembali besok!",
    ),
    "youCantTakeMicOnOppositeRoom": MessageLookupByLibrary.simpleMessage(
      "Anda tidak dapat mengambil mikrofon di ruangan seberang",
    ),
    "youDoNotBelongTheFamily": MessageLookupByLibrary.simpleMessage(
      "Kamu bukan anggota family ini",
    ),
    "youDoNotHaveCouple": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memiliki pasangan",
    ),
    "youDontHaveTheAccessToUploadGifPicture":
        MessageLookupByLibrary.simpleMessage(
          "Anda tidak mempunyai akses untuk meng-upload foto gif",
        ),
    "youGet": MessageLookupByLibrary.simpleMessage("Kamu mendapatkan"),
    "youGetAGift": MessageLookupByLibrary.simpleMessage(
      "Anda menerima hadiah.",
    ),
    "youGetMatchEverySeconds": MessageLookupByLibrary.simpleMessage(
      "Anda mendapatkan match setiap tiga detik…",
    ),
    "youGetTwoGifts": MessageLookupByLibrary.simpleMessage(
      "Anda menerima dua hadiah.",
    ),
    "youGot": MessageLookupByLibrary.simpleMessage("Anda mendapatkan"),
    "youGotFriendsInHala": m301,
    "youGotFrom": m302,
    "youGotNumGifts": m303,
    "youHadGot": m304,
    "youHadRevokedAdmin": MessageLookupByLibrary.simpleMessage(
      "Anda telah mencabut admin",
    ),
    "youHasItem": MessageLookupByLibrary.simpleMessage(
      "Anda telah membeli item tersebut",
    ),
    "youHaveBeenRemovedFromTheFamilynameGoCheckOut": m305,
    "youHaveDownVip": m306,
    "youHaveFollowersAndFriends": m307,
    "youHaveGot": MessageLookupByLibrary.simpleMessage(
      "Anda telah mendapatkan",
    ),
    "youHaveInterests": MessageLookupByLibrary.simpleMessage(
      "Anda memiliki tiga minat yang sama!",
    ),
    "youHaveKickedOutMic": MessageLookupByLibrary.simpleMessage(
      "Anda meninggalkan mic sekarang. Datang lagi nanti!",
    ),
    "youHaveKickedOutRoom": MessageLookupByLibrary.simpleMessage(
      "Anda telah dikeluarkan dari ruangan ini",
    ),
    "youHaveNoAuditRights": MessageLookupByLibrary.simpleMessage(
      "Anda tidak memiliki hak audit.",
    ),
    "youHaveNoJoinTheFamily": MessageLookupByLibrary.simpleMessage(
      "Anda belum bergabung dengan family ini",
    ),
    "youHaveNotGot": MessageLookupByLibrary.simpleMessage(
      "Anda belum mendapatkan",
    ),
    "youHaveReachedTheDeviceClaimLimitAndCannotClaim":
        MessageLookupByLibrary.simpleMessage(
          "Perangkat ini sudah mencapai batas klaim, tidak bisa ambil lucky bag",
        ),
    "youHaveReachedVip": m308,
    "youHaveRewards": MessageLookupByLibrary.simpleMessage(
      "Ketuk untuk mendapatkan hadiah",
    ),
    "youHaveSentRequest": MessageLookupByLibrary.simpleMessage(
      "Anda telah mengirim permintaan ke pengguna ini",
    ),
    "youInvitedGuest": MessageLookupByLibrary.simpleMessage(
      "Anda diundang untuk mengambil mikrofon tamu",
    ),
    "youInvitedHost": MessageLookupByLibrary.simpleMessage(
      "Anda diundang untuk mengambil mikrofon host",
    ),
    "youKickedOut": MessageLookupByLibrary.simpleMessage(
      "Anda telah dikeluarkan dari daftar",
    ),
    "youLackOfKeys": MessageLookupByLibrary.simpleMessage(
      "Kekurangan kunci! Silakan minta kunci dari anggota family yang lain.",
    ),
    "youMatchedWith": MessageLookupByLibrary.simpleMessage(
      "Anda telah Dicocokkan dengan ",
    ),
    "youMayInterest": MessageLookupByLibrary.simpleMessage(
      "Anda mungkin berminat",
    ),
    "youNotWinkerPremium": MessageLookupByLibrary.simpleMessage(
      "Anda bukan Winker Premium",
    ),
    "youRLuckyBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      ", Lucky bag Anda kosong!",
    ),
    "youReceivedANewMsg": MessageLookupByLibrary.simpleMessage(
      "Anda menerima pesan baru",
    ),
    "youRejectDivorce": m309,
    "youRoomDefenseBuff": m310,
    "youSendGift": m311,
    "youSentTheRequest": MessageLookupByLibrary.simpleMessage(
      "Anda telah mengirim permintaan, harap tunggu persetujuan.",
    ),
    "youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": m312,
    "youTookScreenshot": MessageLookupByLibrary.simpleMessage(
      "Anda telah menangkap layar Obrolan",
    ),
    "youWillHaveANewId": MessageLookupByLibrary.simpleMessage(
      "Anda akan memiliki identitas baru termasuk avatar dan nama panggilan.",
    ),
    "youWillLosePkOnceLeaveRoom": MessageLookupByLibrary.simpleMessage(
      "Anda akan kehilangan Pertarungan begitu Anda meninggalkan kamar",
    ),
    "youWinkedYou": m313,
    "yourAccountWasDeleted": MessageLookupByLibrary.simpleMessage(
      "Akun Winker anda telah dihapus.",
    ),
    "yourBalance": MessageLookupByLibrary.simpleMessage("Saldo Anda:"),
    "yourCity": MessageLookupByLibrary.simpleMessage("Kota Anda"),
    "yourContributionWillBeClearedIfExitFamily":
        MessageLookupByLibrary.simpleMessage(
          "Setelah Anda keluar dari family, semua aset yang terkait dengan keluarga akan dihapus. Apakah Anda yakin ingin keluar dari family?",
        ),
    "yourEventInfoNotFill": m314,
    "yourFamilyApplicationAlreadySent": MessageLookupByLibrary.simpleMessage(
      "Permohonan Anda telah dikirim. Mohon tunggu tanggapan dari ketua family dengan sabar ",
    ),
    "yourFamilyHasBeenDisbanded": MessageLookupByLibrary.simpleMessage(
      "Family Anda telah dibubarkan!",
    ),
    "yourFamilyHasBeenDisbandedGoCheckOutOtherFamilies":
        MessageLookupByLibrary.simpleMessage(
          "Family Anda telah dibubarkan. Coba cari family lain! Pergi ke pusat family!",
        ),
    "yourGoldReturnedBalance": MessageLookupByLibrary.simpleMessage(
      "Gagal memancing, koin Anda telah dikembalikan ke saldo anda.",
    ),
    "yourKeyMustBeSentFromAnother": MessageLookupByLibrary.simpleMessage(
      "Kunci terbuka Anda harus dikirim dari pengguna lain.",
    ),
    "yourLevelIsIncreasingAtPremiumSpeed": MessageLookupByLibrary.simpleMessage(
      "Level Anda sedang meningkat dengan kecepatan Premium.",
    ),
    "yourName": MessageLookupByLibrary.simpleMessage("Nama Anda"),
    "yourPhoneStorageInsufficient": MessageLookupByLibrary.simpleMessage(
      "Ruang penyimpanan ponsel Anda tidak mencukupi.",
    ),
    "yourQuestion": MessageLookupByLibrary.simpleMessage("Pertanyaan anda:"),
    "yourRoomGrabPoints": m315,
    "yourRoomIsGrabbedPoints": m316,
    "yourRoomPointsChange": m317,
    "yourVoiceLow": MessageLookupByLibrary.simpleMessage(
      "Suara anda terlalu kecil, tolong bicara lebih keras.",
    ),
    "yourVoiceVerificationFailed": MessageLookupByLibrary.simpleMessage(
      "Verifikasi suara anda gagal, coba rekam lagi. ",
    ),
    "yseWithExclamation": MessageLookupByLibrary.simpleMessage("Ya!"),
  };
}
