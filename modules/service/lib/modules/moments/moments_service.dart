import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:image_picker/image_picker.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/moment_statistics.g.dart';
import 'package:service/common/statistics/post_statistics.g.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/db/db_util.dart';
import 'package:service/modules/common/api/center_api.dart';
import 'package:service/modules/common/upload/upload_file.dart';
import 'package:service/modules/evaluate/const/enums.dart';
import 'package:service/modules/moments/api/moments_api.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/model/comment.dart';
import 'package:service/modules/moments/model/comment_list_resp.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/moments/model/moment_list_result.dart';
import 'package:service/modules/moments/model/publish_start_result.dart';
import 'package:service/modules/notification/abs_notification_service.dart';
import 'package:service/modules/notification/handler/i_notification_handler.dart';
import 'package:service/modules/sticker/const/task_action_code.dart';
import 'package:service/modules/sticker/model/sticker.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/voice_verify/const/events.dart';
import 'package:service/modules/voice_verify/model/voice_verify_record_model.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_moment.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';
import 'package:service/modules/moments/model/theme_item.dart';
import 'package:service/modules/moments/model/theme_aggregate_list.dart';
import 'package:service/modules/moments/model/publish_title_item.dart';
import 'package:service/modules/moments/model/create_theme_resp.dart';
import 'package:service/modules/moments/model/theme_move_resp.dart';
import 'package:service/modules/moments/model/follow_user_state.dart';
import '../common/model/audio_x_file.dart';
import 'abs_moments_service.dart';
import 'model/deep_link.dart';
import 'model/user_topic_list.dart';

@Service()
class MomentsService extends AbsMomentsService implements INotificationHandler {
  final _momentApi = MomentsApi(baseUrl: AppConfig.instance.baseUrl);
  MomentsService() {
    getService<AbsNotificationService>()?.addHandler(this);
  }

  @override
  PbPushEvent event() {
    return PbPushEvent.PbPushEvent_MOMENT_ACTION;
  }

  @override
  void onHandle(PbBizPush push, List<int> data, bool isPush,
      {Map<String,dynamic>? extra}) {
    var pushData = PbMomentActionNotice.fromBuffer(data);

    if (isPush) {
      routerUtil.push(R_MOMENTS_DETAIL, params: {
        P_MOMENT_ID: pushData.momentId,
        P_STATISTIC_FROM: StatisticPageFrom.push
      });
      return;
    }
    switch (pushData.type) {
      case PbMomentActionType.PbMomentActionType_COMMENT:
      case PbMomentActionType.PbMomentActionType_PRAISE:
        userService.getUserCount(forceUpdate: true);
        break;
      case PbMomentActionType.PbMomentActionType_FOLLOWING_MOMENT:
        userService.getUserCount().then((userCount) {
          userCount.updateFollowMomentsCount(
              (userCount.followMomentsCount ?? 0) + 1);
        });
        rxUtil.send(MomentsStatusEvent.newFollowMoment, pushData.momentId);
        break;
      default:
        break;
    }
  }

  @override
  Future<FlatHttpResponse<MomentListResp>> getMomentList(
      {MomentsType type = MomentsType.recommend,
      int page = 1,
      int size = 10,
      int? lastTime = 0,
      String? lastItemId,
      String? hashtagId,
        String? maleLastIdx,
        String? femaleLastIdx}) async {
    int startTime = DateTime.now().millisecondsSinceEpoch;
    FlatHttpResponse<MomentListResp> response;
    String momentsType = "recommend";
    String reportContent = 'reco_list';
    bool saveCache = true;
    switch (type) {
      case MomentsType.following:
        momentsType = "following";
        reportContent = 'follow_list';
        response = await _momentApi.followingList(
            lastId: lastItemId ?? '');
        if (response.isSuccess) {
          userService.getUserCount().then((userCount) {
            userCount.updateFollowMomentsCount(0);
          });
        }
        break;
      case MomentsType.recommend:
        momentsType = "recommend";
        reportContent = 'reco_list';
        response = await _momentApi.recommendList(
            page: '$page',
            size: '$size',
            lastTime: '$lastTime',
            lastItemId: lastItemId,
            maleLastIdx: maleLastIdx,
            femaleLastIdx: femaleLastIdx);
        break;
      case MomentsType.newest:
        momentsType = "newest";
        reportContent = 'newest_list';
        response = await _momentApi.newestList(
            page: '$page',
            size: '$size',
            lastTime: '$lastTime',
            lastItemId: lastItemId);
        break;
      case MomentsType.hottest:
        saveCache = false;
        response = await _momentApi.rankingList(
            tag: "tag_hot",
            hashtagId: hashtagId,
            page: '$page',
            size: '$size',
            lastTime: '$lastTime',
            lastItemId: lastItemId);
        break;
      case MomentsType.latest:
        saveCache = false;
        response = await _momentApi.rankingList(
            tag: "tag_new",
            hashtagId: hashtagId,
            page: '$page',
            size: '$size',
            lastTime: '$lastTime',
            lastItemId: lastItemId);
        break;
      case MomentsType.family:
        momentsType = "family";
        reportContent = 'family_list';
        response = await _momentApi.familyList(
            page: '$page',
            size: '$size',
            lastTime: '$lastTime',
            lastItemId: lastItemId);
        break;
    }

    if (saveCache) {
      int endTime = DateTime.now().millisecondsSinceEpoch;
      MomentStatistics.reportMomentListRequestEnd(
        content: reportContent,
        result: '${response.isSuccess ? 'succ' : 'fail'}',
        reason: response.isSuccess ? '' : '${response.code}',
        onTime: '${endTime - startTime}',
      );
      await _updateCache(response, momentsType, page == 1);
    }

    return response;
  }

  @override
  Future<void> publishVoice({VoiceVerifyRecordModel? voice}) async {
    var startTime = DateTime.now();
    var id = (await momentsService.getPublishId())?.itemId;
    if (id != null && id.isNotEmpty) {
      List<XFile> voices = [];
      voices.add(AudioXFile(voice?.voiceLocalPath ?? "", duration: voice?.seconds ?? 0));
      var totalSize = 0.0;
      UploadFiles files = UploadFiles(
          assetList: voices,
          moduleId: id,
          prefix: "opt",
          complete: (list) async {
            await _publishShareMoment(id,
                base64.encode(utf8.encode(jsonEncode(list))), null);
            var dt =
                DateTime.now().difference(startTime).inMilliseconds.toString();
            PostStatistics.reportPostSendEnd(
                from: "verify",
                style: "voice",
                duration: dt,
                size: totalSize == 0 ? "" : totalSize.toString(),
                result: 'succ');
          },
          lengthCallback: (size) {
            totalSize = size;
          },
          uploadFail: (list, {code, msg}) {
            toast(LocaleStrings.instance.pleaseTryAgain);
          });
      files.upload();
    } else {
      toast(LocaleStrings.instance.pleaseTryAgain);
    }
  }

  @override
  Future<Moment?> getMomentFromCacheById({required String momentId}) async {
    ///从缓存中获取
    var info = accountService.getAccountInfo();
    if (info == null) return null;
    Moment? moment = await (await DbUtil.getAppDb())
        ?.momentsDao
        .findMomentById(info.uid, momentId);

    return moment;
  }

  @override
  Future<Moment?> getMomentById({required String momentId}) async {
    var info = accountService.getAccountInfo();
    Moment? moment;
    int startTime = DateTime.now().millisecondsSinceEpoch;

    FlatHttpResponse<MomentListResp> response =
        await _momentApi.getMomentById(momentId: momentId);
    int endTime = DateTime.now().millisecondsSinceEpoch;
    MomentStatistics.reportMomentDetailRequestEnd(
      result: '${response.isSuccess ? 'succ' : 'fail'}',
      onTime: '${endTime - startTime}',
      reason: response.isSuccess ? '' : '${response.code}',
      postId: momentId,
    );

    if (response.isSuccess && (response.data?.list?.isNotEmpty ?? false)) {
      if (info == null) return moment;
      moment = response.data!.list![0]..belongUid = info.uid;
      DbUtil.getAppDb().then((value) async {
        final momentCache = await value?.momentsDao
            .findMomentById(moment!.belongUid!, moment.itemId);
        if (momentCache != null) {
          moment!..momentsType = momentCache.momentsType;
          value?.momentsDao.updateMoment(moment);
        } else {
          value?.momentsDao.insertMoment(moment!);
        }
      });
      rxUtil.send(MomentsStatusEvent.updateMoment, moment);
      return moment;
    }

    if (!response.isSuccess && response.data == null) {
      return null;
    }

    return Moment.none("");
  }


  Future<MomentListResp?> getMomentDetailById({required String momentId}) async {
    var info = accountService.getAccountInfo();
    Moment? moment;
    int startTime = DateTime.now().millisecondsSinceEpoch;

    FlatHttpResponse<MomentListResp> response =
        await _momentApi.getMomentById(momentId: momentId);
    int endTime = DateTime.now().millisecondsSinceEpoch;
    MomentStatistics.reportMomentDetailRequestEnd(
      result: '${response.isSuccess ? 'succ' : 'fail'}',
      onTime: '${endTime - startTime}',
      reason: response.isSuccess ? '' : '${response.code}',
      postId: momentId,
    );

    if (response.isSuccess && (response.data?.list?.isNotEmpty ?? false)) {
      if (info == null) return null;

      moment = response.data!.list![0]..belongUid = info.uid;
      DbUtil.getAppDb().then((value) async {
        final momentCache = await value?.momentsDao
            .findMomentById(moment!.belongUid!, moment.itemId);
        if (momentCache != null) {
          moment!..momentsType = momentCache.momentsType;
          value?.momentsDao.updateMoment(moment);
        } else {
          value?.momentsDao.insertMoment(moment!);
        }
      });
      rxUtil.send(MomentsStatusEvent.updateMoment, moment);
      return response.data;
    }

    if (!response.isSuccess && response.data == null) {
      return null;
    }

    return null;
  }

  /// 发布声音认证分享moment
  Future<bool> _publishShareMoment(
      String momentId, String filesBase, String? hashTags) async {
    final response = await _momentApi.publishVoice(
        momentId: momentId, files: filesBase, hashtags: hashTags);
    bool result = response.isSuccess;
    if (result) {
      toast(LocaleStrings.instance.postSuccess);
      rxUtil.send(VoiceVerifyUpdateEvent.shareMomentSuccess, true);
      var moment = await momentsService.getMomentById(momentId: momentId);
      if (moment != null && (moment.itemId.isNotEmpty)) {
        rxUtil.send(MomentPublish.success, moment);
      } else {
        rxUtil.send(MomentPublish.success, moment);
      }
    } else {
      toast(LocaleStrings.instance.pleaseTryAgain);
    }
    return result;
  }

  /// 更新db缓存
  /// [clean] 是否需要先清除缓存
  Future<void> _updateCache(FlatHttpResponse<MomentListResp> response,
      String momentsType, bool clean) async {
    if (response.isSuccess && (response.data?.list?.isNotEmpty ?? false)) {
      var info = accountService.getAccountInfo();

      var list = response.data?.list?.map((e) {
            return e
              ..belongUid = info!.uid
              ..momentsType = momentsType;
          }).toList() ??
          [];

      var momentDao = (await DbUtil.getAppDb())?.momentsDao;
      if ("recommend" == momentsType &&
          (response.data?.list?.isEmpty ?? false)) {
        return;
      }
      if (clean && list.isNotEmpty) {
        await momentDao?.deleteMoments(info!.uid, momentsType);
      }

      await momentDao?.insertMoments(list);
    }
  }

  @override
  Future<List<Moment>> getMomentsFromCache(
      {MomentsType type = MomentsType.recommend, int size = 10}) async {
    String momentsType;
    switch (type) {
      case MomentsType.following:
        momentsType = "following";
        break;
      case MomentsType.newest:
        momentsType = "newest";
        break;
      default:
        momentsType = "recommend";
        break;
    }

    List<Moment>? list;
    if (accountService.getAccountInfo() != null) {
      list = await (await DbUtil.getAppDb())
          ?.momentsDao
          .findMoments(accountService.getAccountInfo()!.uid, momentsType, size);
    }

    return list ?? [];
  }

  @override
  Future<FlatHttpResponse> praise(
      {required MomentsPraiseType type,
      required String itemId,
      required String publishUid}) async {
    String action;
    MomentsStatusEvent event;
    switch (type) {
      case MomentsPraiseType.praise:
        action = "like";
        event = MomentsStatusEvent.praise;
        break;
      case MomentsPraiseType.unPraise:
        action = "dislike";
        event = MomentsStatusEvent.unPraise;
        break;
    }
    FlatHttpResponse response = await _momentApi.praise(
        action: action, itemId: itemId, publishUid: publishUid);
    _saveAndSendPraise(response, event, itemId);

    if (type == MomentsPraiseType.praise && response.isSuccess) {
      await evaluateService.addRecord(EvaluateType.praise, value: itemId);
      await postGuideService.addRecord(PostGuideType.praise, value: itemId);
    }
    return response;
  }

  ///更新缓存，发送点赞/取消赞全局通知
  void _saveAndSendPraise(FlatHttpResponse response, MomentsStatusEvent event,
      String itemId) async {
    if (response.isSuccess) {
      rxUtil.send(event, itemId);

      var info = accountService.getAccountInfo();

      DbUtil.getAppDb().then((value) async {
        Moment? moment =
            await value?.momentsDao.findMomentById(info?.uid ?? "", itemId);
        if (moment != null) {
          moment.praiseNum = (moment.praiseNum ?? 0) +
              (event == MomentsStatusEvent.praise
                  ? 1
                  : (moment.praiseNum! > 0 ? -1 : 0));
          moment.momentActions?.hasPraise =
              event == MomentsStatusEvent.praise ? 1 : 0;
          value?.momentsDao.updateMoment(moment);
        }
      });
    }
  }

  @override
  Future<PublishStartResult?> getPublishId() async {
    FlatHttpResponse response = await _momentApi.getPublishId();
    return response.data;
  }

  @override
  Future<bool> delete({required String momentId}) async {
    FlatHttpResponse response =
        await _publishOrDelete(action: 'delete', momentId: momentId);
    if (response.isSuccess) {
      rxUtil.send(MomentsStatusEvent.deleteMoment, momentId);
    }
    return response.isSuccess;
  }

  @override
  Future<FlatHttpResponse> publish({
    required String momentId,
    String? visible,
    String? text,
    String? files,
    String? hashtags,
    String? deeplinkInfo,
    String? contentType,
    String? contentTypeInfo,
    String? skipAudit,
    String? topicId,
    String? topicName,
    String? topicDesc,
    String? labelType,
    String? labelIconId,
    String? labelValue}) async {
    FlatHttpResponse response = await _publishOrDelete(
        action: 'create',
        visible: visible,
        momentId: momentId,
        files: files,
        text: text,
        hashtags: hashtags,
        deeplinkInfoStr: deeplinkInfo,
        contentType: contentType,
        contentTypeInfo: contentTypeInfo,
        skipAudit: skipAudit,
        topicId: topicId,
        topicName: topicName,
        topicDesc: topicDesc,
        labelType: labelType,
        labelIconId: labelIconId,
        labelValue: labelValue);

    if (response.isSuccess) {
      var info = accountService.getAccountInfo();
      if (info != null) {
        ///更新本地account信息：用户已发布动态数量+1
        accountService.updateAccountInfo(info..postCount += 1);
      }
    }
    return response;
  }

  Future<FlatHttpResponse> _publishOrDelete(
      {required String momentId,
      required String action,
      String? visible,
      String? text,
      String? files,
      String? hashtags,
      String? deeplinkInfoStr,
      String? contentType,
      String? contentTypeInfo,
      String? skipAudit,
        String? topicId,
        String? topicName,
        String? topicDesc,
        String? labelType,
        String? labelIconId,
        String? labelValue}) async {
    DeepLinkInfo? deepLinkInfo;

    /// 带deeplink
    if (deeplinkInfoStr?.isNotEmpty ?? false) {
      var map = json.decode(deeplinkInfoStr ?? "");
      if (map is Map) {
        deepLinkInfo = DeepLinkInfo.fromJson(Map<String, dynamic>.from(map));
      }
    }

    FlatHttpResponse response = await _momentApi.publishOrDelete(
        action: action,
        visible: visible,
        momentId: momentId,
        files: files,
        text: text,
        hashtags: hashtags,
        deeplink: deepLinkInfo?.link,
        deeplinkIcon: deepLinkInfo?.icon,
        deeplinkTitle: deepLinkInfo?.title,
        deeplinkType: deepLinkInfo?.type,
        contentType: contentType,
        contentTypeInfo: contentTypeInfo,
        skipAudit: skipAudit,
        topicId: topicId,
        topicName: topicName,
        topicDesc: topicDesc,
        labelType: labelType,
        labelIconId: labelIconId,
        labelValue: labelValue);
    return response;
  }

  @override
  Future<FlatHttpResponse> modify(
      {required String momentId, String? visible}) async {
    FlatHttpResponse response = await _momentApi.publishOrDelete(
      action: "modify",
      visible: visible,
      momentId: momentId,
    );
    return response;
  }

  @override
  Future<bool> pin({required String momentId}) async {
    var info = accountService.getAccountInfo();
    FlatHttpResponse response = await _momentApi.praise(
        action: "top", itemId: momentId, publishUid: info!.uid);
    if (response.isSuccess) {
      rxUtil.send(
          UserStatusEvent.updateUsers, [accountService.getAccountInfo()!.uid]);
    }
    return response.isSuccess;
  }

  Map<String, dynamic>? _tagBgMap;

  @override
  Future<String?> hashTagBg(List<String> hashTagIds) async {
    if (hashTagIds.isEmpty) return null;

    final section =
        await getService<AbsRemoteConfigService>()?.getSection('app_ui');

    final tagBg = section?.getValue('hashtag_bg')?.getAll();
    if (tagBg?.isNotEmpty == true) {
      _tagBgMap = tagBg;
    }
    if (_tagBgMap?.isNotEmpty == true) {
      for (var element in hashTagIds) {
        if (_tagBgMap![element] is String) {
          return _tagBgMap![element];
        }
      }
    }
    return null;
  }

  @override
  Future<bool> showNewTabPage() async {
    final section =
        await getService<AbsRemoteConfigService>()?.getSection('app_ui');
    final value = section?.getValue('moments_show_new_tab')?.getAll();
    if (value != null && value["show"] is bool) {
      return value["show"];
    }
    return true;
  }

  @override
  Future<FlatHttpResponse<ThemeListResp>> getThemeList({
    MediaType type = MediaType.text
  }) async {
    String momentType = 'text';
    if (type != MediaType.text) {
      momentType = 'multimedia';
    }
    return _momentApi.getThemeList(momentType: momentType);
  }

  @override
  Future<FlatHttpResponse<CreateThemeResp>> createTheme({
    required String name,
    String? desc
  }) async {
    return _momentApi.createTheme(name: name, desc: desc);
  }

  @override
  Future<FlatHttpResponse> updateTheme({
    required String id,
    required String name,
    String? desc,
  }) async {
    return _momentApi.updateTheme(id: id, name: name, desc: desc);
  }

  @override
  Future<FlatHttpResponse> orderTheme({
    required String ids,
  }) async {
    return _momentApi.orderTheme(ids: ids);
  }

  /// 主题聚合页
  @override
  Future<FlatHttpResponse<ThemeAggregateList>> themeAggregateList({
    required String id,
    required String lastId,
    int? pageSize,
  }) async {
    return _momentApi.themeAggregate(
        topicId: id, lastId: lastId, pageSize: pageSize?.toString() ?? '');
  }

  @override
  Future<FlatHttpResponse<PublishTitleList>> publishTitleList() async {
    return _momentApi.publishTitleList();
  }

  @override
  Future<FlatHttpResponse<UserTopicList>> userTopicList(
      {required String fuid}) async {
    return await _momentApi.userTopicList(fuid: fuid);
  }

  @override
  Future<FlatHttpResponse> reportExposure({
    required String keyIds
  }) async {
    return await _momentApi.reportExposure(keyIds: keyIds);
  }

  Future<FlatHttpResponse<ThemeMoveResp>> moveTheme({
    required String momentId,
    String? oriThemeId,
    String? nowThemeId,
    String? themeName,
    String? themeDesc,
  }) async {
     return await _momentApi.moveTheme(
         momentId: momentId,
         action: 'move_topic',
         oriThemeId: oriThemeId,
         nowThemeId: nowThemeId,
         themeName: themeName,
         themeDesc: themeDesc);
  }

  Future<FlatHttpResponse<FollowUserStateList>> followUserStates() async {
    return await _momentApi.followUserStates();
  }
}