// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'moment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Moment _$MomentFromJson(Map<String, dynamic> json) => Moment(
      json['belongUid'] as String?,
      json['uid'] as String?,
      json['item_id'] as String,
      toInt(json['liked']),
      toInt(json['comment_num']),
      dynamicToString(json['text']),
      (json['files'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(e as Map<String, dynamic>))
          .toList(),
      toInt(json['ctime']),
      json['user'] == null
          ? null
          : UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      json['momentsType'] as String?,
      json['user_action'] == null
          ? null
          : MomentActions.fromJson(json['user_action'] as Map<String, dynamic>),
      json['deeplink_info'] == null
          ? null
          : DeepLinkInfo.fromJson(
              json['deeplink_info'] as Map<String, dynamic>),
      json['topic_info'] == null
          ? null
          : ThemeItem.fromJson(json['topic_info'] as Map<String, dynamic>),
      json['label_info'] == null
          ? null
          : MomentLabelInfo.fromJson(
              json['label_info'] as Map<String, dynamic>),
      dynamicToString(json['key_id']),
      toBool(json['is_online']),
      (json['hashtag_infos'] as List<dynamic>?)
          ?.map((e) => Hashtag.fromJson(e as Map<String, dynamic>))
          .toList(),
      visible: json['visible'] == null ? 1 : toInt(json['visible']),
      itemPageId: json['itemPageId'] as String?,
    )
      ..isPin = json['isPin'] as bool?
      ..roomId = dynamicToString(json['room_id']);

Map<String, dynamic> _$MomentToJson(Moment instance) => <String, dynamic>{
      'belongUid': instance.belongUid,
      'uid': instance.uid,
      'item_id': instance.itemId,
      'liked': instance.praiseNum,
      'comment_num': instance.commentNum,
      'text': instance.text,
      'files': instance.medias,
      'ctime': instance.publishTime,
      'visible': instance.visible,
      'user': instance.userInfo,
      'user_action': instance.momentActions,
      'momentsType': instance.momentsType,
      'deeplink_info': instance.linkInfo,
      'itemPageId': instance.itemPageId,
      'isPin': instance.isPin,
      'topic_info': instance.topicInfo,
      'label_info': instance.labelInfo,
      'key_id': instance.keyId,
      'is_online': instance.isOnline,
      'hashtag_infos': instance.hashtags,
      'room_id': instance.roomId,
    };
