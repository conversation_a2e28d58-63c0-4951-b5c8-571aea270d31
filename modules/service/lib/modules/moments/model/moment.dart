import 'package:floor/floor.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:service/global/db/db_const.dart';
import 'package:service/modules/moments/model/deep_link.dart';
import 'package:service/modules/moments/model/media.dart';
import 'package:service/modules/moments/model/moment_actions.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/value_parsers.dart';
import 'package:service/modules/moments/model/moment_label_info.dart';
import 'package:service/modules/moments/model/theme_item.dart';

import '../../hashtag/model/hashtag.dart';

part 'moment.g.dart';

///动态
@JsonSerializable()
@Entity(tableName: tableMoment)
class Moment {
  ///所属uid:当前登录者uid
  String? belongUid = accountService.getAccountInfo()?.uid;

  ///发布者uid
  String? uid;

  @primaryKey
  @JsonKey(name: "item_id")
  String itemId;

  ///点赞数
  @JsonKey(name: "liked", fromJson: toInt)
  int? praiseNum;

  ///评论数
  @JsonKey(name: "comment_num", fromJson: toInt)
  int? commentNum;

  ///动态文本
  @JsonKey(fromJson: dynamicToString)
  String? text;

  ///动态媒体
  @JsonKey(name: "files")
  List<Media>? medias;

  ///发布时间
  @JsonKey(name: "ctime", fromJson: toInt)
  int? publishTime;

  @ignore
  @JsonKey(name: "visible", fromJson: toInt)
  int? visible = 1;

  ///发布者信息
  @JsonKey(name: "user")
  UserInfo? userInfo;

  ///发布者信息
  @JsonKey(name: "user_action")
  MomentActions? momentActions;

  String? momentsType;

  @JsonKey(name: "deeplink_info")
  DeepLinkInfo? linkInfo;

  @ignore
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool hasReport = false;

  /// itemID+分页id
  @ignore
  String? itemPageId;

  /// 是否置顶moment
  @ignore
  bool? isPin;

  /// 分享相关
  // @JsonKey(name: "room_event")
  // RoomEventInfo? roomEventInfo;
  //
  // @JsonKey(name: "family_info")
  // FamilyInfoModel? familyInfo;

  ///主题
  @JsonKey(name: "topic_info")
  ThemeItem? topicInfo;

  ///标签
  @JsonKey(name: "label_info")
  MomentLabelInfo? labelInfo;

  @JsonKey(name: "key_id", fromJson: dynamicToString)
  String? keyId;

  @JsonKey(name: "is_online", fromJson: toBool)
  bool? isOnline;

  @ignore
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool hasExposure = false;

  ///hashtag
  @JsonKey(name: "hashtag_infos")
  List<Hashtag>? hashtags;

  /// 用户所在房间Id，若不在房间中则为空
  @ignore
  @JsonKey(name: "room_id", fromJson: dynamicToString)
  String? roomId;

  Moment(
      this.belongUid,
      this.uid,
      this.itemId,
      this.praiseNum,
      this.commentNum,
      this.text,
      this.medias,
      this.publishTime,
      this.userInfo,
      this.momentsType,
      this.momentActions,
      this.linkInfo,
      this.topicInfo,
      this.labelInfo,
      this.keyId,
      this.isOnline,
      this.hashtags,
      {
        this.visible = 1,
        this.itemPageId,
      });

  Moment.none(this.itemId);

  factory Moment.fromJson(Map<String, dynamic> json) => _$MomentFromJson(json);

  Map<String, dynamic> toJson() => _$MomentToJson(this);

  String getDeepLink() {
    return "${DeepLink.deepLinkProto}${R_MOMENTS_DETAIL.replaceFirst("/", "")}${"?$P_MOMENT_ID=$itemId"}";
  }

  @override
  String toString() {
    return 'Moment{belongUid: $belongUid, uid: $uid, itemId: $itemId, praiseNum: $praiseNum, '
        'commentNum: $commentNum, text: $text, medias: $medias, publishTime: $publishTime,'
        ' userInfo: $userInfo, momentActions: $momentActions, momentsType: $momentsType, '
        'linkInfo: $linkInfo, visible: $visible}';
  }
}

enum MomentMediaType {
  text,
  image,
  video,
  unKnown,
}

extension MomentExt on Moment {
  MomentMediaType getMomentMediaType() {
    if (medias?.isEmpty ?? true) {
      return MomentMediaType.text;
    }
    var type = medias!.first.type;
    if (type == 'video') {
      return MomentMediaType.video;
    } else if (type == 'pic') {
      return MomentMediaType.image;
    }
    return MomentMediaType.unKnown;
  }
}
