import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';
import 'package:library_base_nullsafety/publish/config/preferences.dart';
import 'package:service/common/statistics/post_statistics.g.dart';
import 'package:service/extension/xfile.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/channel_msg/abs_channel_msg_service.dart';
import 'package:service/modules/common/upload/upload_file.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/model/file_info.dart';
import 'package:service/modules/moments/model/publish_moment.dart';
import 'package:service/pb/local/pb_local.pbenum.dart';
import 'package:service/pb/local/pb_local_enum.pbenum.dart';
import 'package:service/pb/local/pb_local_req.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/fb_reporter.dart';

class PublishMomentManager {
  static PublishMomentManager? _instance;

  PublishMomentManager._();

  factory PublishMomentManager.instance() =>
      _instance ??= PublishMomentManager._();

  final _pref = Preferences.newInstance(spMoment);
  final _keyLastFailedPublishDetail = 'last_failed_public_detail_';

  ///是否正在发布
  bool? publishingMoment = false;

  ///近一次发布失败
  bool? lastPublishFailure = false;

  UploadFiles? uploadFiles;

  ///近一次发布的内容体
  PublishMoment? lastPublishDetail;

  ///计算发布动态耗时
  DateTime startTime = DateTime.now();

  double totalSize = 0;

  void publishMomentHandle(PublishMoment detail) {
    publishingMoment = true;
    lastPublishFailure = false;
    _cleanLastPublishMomentSp();
    lastPublishDetail = detail;

    /// 更新发布起始事件
    startTime = DateTime.now();
    if (detail.files.isEmpty) {
      /// 无媒体动态不需要统计size, 晴空
      totalSize = 0;

      /// 开始发布动态上报
      _handlePostSendStart(detail);
      publish(detail: detail);
      return;
    }
    rxUtil.send(MomentPublish.progress, 0);

    _publishWithFiles(detail);
  }

  void rePublish() {
    if (publishingMoment == true) return;
    if (lastPublishDetail == null) return;
    publishMomentHandle(lastPublishDetail!);
  }

  void cancelPublish() {
    rxUtil.send(MomentPublish.cancel, lastPublishDetail?.momentId);
    publishingMoment = false;
    lastPublishFailure = false;
    _cleanLastPublishMomentSp();
    lastPublishDetail = null;
    uploadFiles?.cancel();
  }

  void _publishWithFiles(PublishMoment detail) {
    uploadFiles = UploadFiles(
        assetList: detail.files as List<XFile>,
        moduleId: detail.momentId,
        prefix: "opt",
        progress: (value) {
          debugPrint("[Progerss] = $value");
          rxUtil.send(MomentPublish.progress, value);
        },
        complete: (list) {
          if (list.length >= detail.files.length) {
            publish(
                detail: detail,
                filesBase: base64.encode(utf8.encode(jsonEncode(list))));
          }
        },
        uploadFail: (list, {code, msg}) {
          rxUtil.send(MomentPublish.failure, detail);
          publishingMoment = false;
          lastPublishFailure = true;
          _saveLastPublishMomentSp(detail);
        },

        /// 获取计算完成的文件大小并存储
        lengthCallback: (size) {
          totalSize = size;

          /// 开始发布动态上报
          _handlePostSendStart(detail);
        });
    uploadFiles?.upload();
  }

  Future<FlatHttpResponse?> publish(
      {String? filesBase,
      required PublishMoment detail,
      List<FileInfo>? listInfo,
      bool checkPublishingMoment = true}) async {
    if (checkPublishingMoment && publishingMoment != true) return null;

    FlatHttpResponse result = await momentsService.publish(
        momentId: detail.momentId,
        visible: detail.visible.toString(),
        text: detail.text,
        files: filesBase,
        hashtags: detail.hashtags,
      deeplinkInfo: detail.deeplink,
      contentType: detail.contentType,
      contentTypeInfo: detail.contentTypeInfo,
      skipAudit: detail.skipAudit,
        topicId: detail.topicId,
        topicName: detail.topicName,
        topicDesc: detail.topicDesc,
        labelType: detail.labelType,
        labelIconId: detail.labelIconId,
        labelValue: detail.labelValue
    );

    /// 发布动态完成上报
    _handlePostSendEnd(detail, result, listInfo: listInfo);
    if (result.isSuccess) {
      toast(LocaleStrings.instance.postSuccess);
      lastPublishFailure = false;
      _getMomentAndPublishSuccess(momentId: detail.momentId);
      _cleanLastPublishMomentSp();
    } else {
      toast(result.msg ?? LocaleStrings.instance.failedToSubmit);
      rxUtil.send(MomentPublish.failure, detail);
      lastPublishFailure = true;
      _saveLastPublishMomentSp(detail);
    }
    publishingMoment = false;

    return result;
  }

  Future<void> _getMomentAndPublishSuccess({required String momentId}) async {
    var moment = await momentsService.getMomentById(momentId: momentId);
    if (moment != null && (moment.itemId.isNotEmpty)) {
      rxUtil.send(MomentPublish.success, moment);
      return;
    }
    rxUtil.send(MomentPublish.success, moment);
  }

  /// 开始上报
  void _handlePostSendStart(PublishMoment detail) {
    bool isLabel = detail.labelIconId != null;
    PostStatistics.reportPostDetailSend(
        from: detail.from,
        style: _getTypeByDetail(detail),
        numImage: getNumImage(detail),
        size: totalSize == 0 ? "" : totalSize.toString(),
        themeName: detail.topicName,
        themeRefer: detail.topicId != null ? "my" : (detail.isRecommend ? 'recommend' : 'create'),
        isLabel: isLabel ? '1' : '0',
        labelType: isLabel ? 'title' : null,
        labelContent: detail.labelType == '4' ? detail.dateLabelValue : 'title',
        titleIcon: detail.labelIconId,
        visible: detail.visible == 1 ? 'public' : (detail.visible == 2 ? 'friends' : 'myself')
    );
  }

  /// 发布动态结束上报
  void _handlePostSendEnd(PublishMoment detail, FlatHttpResponse resp,
      {List<FileInfo>? listInfo}) {
    String onTime = DateTime.now().difference(startTime).inMilliseconds.toString();
    bool isLabel = detail.labelIconId != null;
    if (resp.isSuccess) {
      reportFbEvent(name: 'post_send_succ');
      if (globalSp.getBool(spKeyFbPostSuccess) != true) {
        reportFbEvent(name: 'post_send_succ_first');
        globalSp.setBool(spKeyFbPostSuccess, true);
      }
    }

    PostStatistics.reportPostSendEnd(
        from: detail.from,
        style: _getTypeByDetail(detail),
        numImage: getNumImage(detail),
        result: '${resp.isSuccess ? 'succ' : 'fail'}',
        reason: '${resp.code}',
        duration: onTime,
        size: totalSize == 0 ? "" : totalSize.toString(),
        themeName: detail.topicName,
        themeRefer: detail.topicId != null ? "my" : (detail.isRecommend ? 'recommend' : 'create'),
        isLabel: isLabel ? '1' : '0',
        labelType: isLabel ? 'title' : null,
        labelContent: detail.labelType == '4' ? detail.dateLabelValue : 'title',
        titleIcon: detail.labelIconId,
        visible: detail.visible == 1 ? 'public' : (detail.visible == 2 ? 'friends' : 'myself')
    );
  }

  int _getHashTagNum(PublishMoment detail) {
    if (detail.hashtags?.isEmpty ?? true) return 0;
    List hashtags = detail.hashtags!.split(",");
    return hashtags.length;
  }

  /// 获取动态类型
  String _getTypeByDetail(PublishMoment detail) {
    if (detail.files.isEmpty) return "text";
    List<XFile> list = detail.files as List<XFile>;
    if (list[0].isVideo == AssetType.video) {
      return "video";
    } else if (list[0].isAudio == AssetType.audio) {
      return "voice";
    } else if (list.length == 1) {
      return "sin_image";
    }
    return "multi_image";
  }

  /// 获取动态类型
  String getNumImage(PublishMoment detail) {
    if (detail.files.isEmpty) return "";
    List<XFile> list = detail.files as List<XFile>;
    if (list[0].isVideo == AssetType.video) return "";
    if (list[0].isAudio == AssetType.audio) return "";
    return detail.files.length.toString();
  }

  void _saveLastPublishMomentSp(PublishMoment detail) {
    PublishMoment value = PublishMoment(
        visible: detail.visible,
        from: detail.from,
        text: detail.text,
        momentId: detail.momentId,
        files: detail.files,
         deeplink: detail.deeplink
    );
    List<String> ids = [];
    if (detail.files.isNotEmpty) {
      ids = detail.files.map((e) {
        return e.id.toString();
      }).toList();
    }

    value.files = ids;
    _pref.setString(_getSpKey(), json.encode(value));
  }

  void _cleanLastPublishMomentSp() {
    _pref.remove(_getSpKey());
  }

  String _getSpKey() {
    return _keyLastFailedPublishDetail +
        (accountService.getAccountInfo()?.uid ?? '');
  }

  Future<void> initCheckLastPublishSp() async {
    if (_pref.containsKey(_getSpKey()) != true) return;
    String? info = _pref.getString(_getSpKey());
    if (info?.isEmpty ?? true) return;
    var map = json.decode(info!);
    if (map is Map) {
      var detail = PublishMoment.fromJson(Map<String, dynamic>.from(map));

      if (detail.files.isNotEmpty) {
        List<XFile> list = [];
        for (var path in detail.files) {
          final asset = await XFile(path);
          list.add(asset);
        }
        detail.files = list;
      }
      rxUtil.send(MomentPublish.failure, detail);
      lastPublishDetail = detail;

      publishingMoment = false;
      lastPublishFailure = true;
      _saveLastPublishMomentSp(detail);
      rxUtil.send(MomentPublish.failure, detail);
    }
  }
}
