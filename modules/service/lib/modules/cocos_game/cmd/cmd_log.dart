import 'package:service/service.dart';

import '../cocos_game_const.dart';
import 'command.dart';

class CommandLog extends Command {
  @override
  String get name => "log";

  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) {
    String level = parameters?[GameKey.LEVEL] ?? "";
    String tag = parameters?[GameKey.TAG];
    String log = parameters?[GameKey.LOG] ?? "";

    String newTag = tag.isNotEmpty ? "JS: $tag" : "JS";

    switch (level.toLowerCase()) {
      case "v":
      case "verbose":
        Log.v(newTag, log);
        break;
      case "d":
      case "debug":
        Log.d(newTag, log);
        break;
      case "i":
      case "info":
        Log.i(newTag, log);
        break;
      case "w":
      case "warning":
        Log.w(newTag, log);
        break;
      case "e":
      case "error":
      case "f":
      case "fatal":
        Log.e(newTag, log);
        break;
      default:
        Log.d(newTag, log);
        break;
    }
    return Future.value(null);
  }
}
