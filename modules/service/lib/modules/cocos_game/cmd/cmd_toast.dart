import 'package:service/utils/toast.dart';

import 'command.dart';

class CommandToast extends Command {
  static const String NAME = 'toast';

  final bool isDark;

  CommandToast({required this.isDark});

  @override
  String get name => NAME;

  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    var content = parameters?['content'];
    if (content is String) {
      toast(content, isDark: isDark);
    }
    return Future.value(null);
  }
}
