import 'package:service/global/model/safe_convert.dart';

import 'command.dart';

typedef OnClickUserMiniCard = void Function(String uid);

class CommandUserMiniCard extends Command {
  @override
  String get name => 'minicard';

  final OnClickUserMiniCard callback;

  CommandUserMiniCard(this.callback);

  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    String? uid = asNT<String>(parameters, 'uid');
    if (uid?.isNotEmpty == true) {
      callback(uid!);
    }
    return null;
  }
}
