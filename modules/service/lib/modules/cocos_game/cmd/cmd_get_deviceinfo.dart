import 'package:service/service.dart';

import '../cocos_game_const.dart';
import '../model/game_params.dart';
import 'command.dart';

class CommandGetDeviceInfo extends Command {
  final GameParams params;

  static const String NAME = "getDeviceInfo";

  CommandGetDeviceInfo(this.params);

  String get name {
    return NAME;
  }

  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    final map = await getService<AbsHttpService>()?.getBaseParams() ?? {};
    map[GameKey.GAME_ID] = params.gameInfo.gameId;
    map[GameKey.ROOM_ID] = params.gameInfo.roomId;
    return map;
  }
}
