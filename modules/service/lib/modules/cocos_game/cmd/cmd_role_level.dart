import 'package:service/global/model/safe_convert.dart';
import 'package:service/modules/cocos_game/cmd/command.dart';

typedef LevelCallback = void Function(LevelModel model);

class CommandRoleLevel extends Command {
  @override
  String get name => 'roleLevel';

  final LevelCallback callback;

  CommandRoleLevel(this.callback);

  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) {
    var model = LevelModel.fromJson(parameters);
    callback(model);
    return Future.value(null);
  }
}

class LevelModel {
  final int level;
  final int progress;
  final int need;
  final int num;

  LevelModel({
    this.level = 0,
    this.progress = 0,
    this.need = 0,
    this.num = 0,
  });

  factory LevelModel.fromJson(Map<String, dynamic>? json) => LevelModel(
        level: asT<int>(json, 'level'),
        progress: asT<int>(json, 'progress'),
        need: asT<int>(json, 'need'),
        num: asT<int>(json, 'num'),
      );

  Map<String, dynamic> toJson() => {
        'level': level,
        'progress': progress,
        'need': need,
        'num': num,
      };

  @override
  String toString() {
    return 'LevelModel{level: $level, progress: $progress, need: $need, num: $num}';
  }
}
