import 'dart:async';

import 'package:service/service.dart';

import '../bridge/jsbridge_util.dart';
import '../cocos_game_const.dart';
import '../model/call_native_model.dart';
import 'command.dart';

class CommandCallNative extends Command {
  final OnCallNativeListener listener;

  CommandCallNative(this.listener);

  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) {
    var command = parameters?[GameKey.COMMAND];
    var gameId = parameters?[GameKey.GAME_ID];
    Map<String, dynamic>? data = parseGenericParams<String, dynamic>(parameters?[GameKey.DATA]);
    if (command == null || gameId == null) {
      return Future.value(null);
    }
    final completer = Completer<Map<String, dynamic>?>();
    try {
      listener.onCallNative(command, CallNativeModel(gameId: gameId, data: data), (code, data) {
        completer.complete({
          GameKey.CODE: code,
          GameKey.MSG: '',
          GameKey.GAME_ID: gameId,
          GameKey.TIMESTAMP: DateTime.now().millisecondsSinceEpoch,
          GameKey.DATA: data
        });
      });
    } on Exception catch (e) {
      Log.e(cocosTag, '$name execute [$command] err->$e');
      completer.complete(null);
    }
    return completer.future;
  }

  @override
  String get name => "callNativeHandle";
}
