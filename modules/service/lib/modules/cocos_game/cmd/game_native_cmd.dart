import 'package:service/service.dart';

import '../bridge/commands_manager.dart';
import '../bridge/jsbridge_util.dart';
import '../cocos_game_const.dart';
import '../model/native_rsp_model.dart';

class GameNativeCmd extends NativeResponse {
  final CommandsManager commandsManager;

  GameNativeCmd(this.commandsManager);

  @override
  Future<JsResponseBody?> body(Map<String, dynamic>? data) async {
    Log.d(cocosTag, '$name $data');
    if (data == null) {
      return await createFailureResponseBody(CODE_ILLEGAL_ARGUMENT, null);
    }
    var command = data[GameKey.COMMAND] as String;
    var param = parseGenericParams<String, dynamic>(data[GameKey.PARAM]);
    if (commandsManager.hasCommand(command)) {
      Map<dynamic, dynamic>? resp = await commandsManager.executeCommand(
        command,
        param,
      );
      Map<String, dynamic>? data = parseGenericParams<String, dynamic>(resp);
      var jsonResp = createSuccessResponseBody(data);
      Log.d(cocosTag, 'Command execute Successful [$name] ($command) jsonResp=$jsonResp');
      return jsonResp;
    } else {
      Log.d(cocosTag, 'Command not Found [$name] ($command)');
      return createFailureResponseBody(CODE_NO_METHOD, null);
    }
  }

  @override
  String get name => 'gameNativeCommand';
}
