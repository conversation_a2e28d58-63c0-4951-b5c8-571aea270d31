import 'package:service/service.dart';

import '../bridge/jsbridge_util.dart';
import '../cocos_game_const.dart';
import 'command.dart';

class CommandNetworkRequest extends Command {
  @override
  String get name => 'networkRequest';

  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    Log.d(cocosTag, 'networkRequest $parameters');
    String url = parameters?[GameKey.URL] ?? "";
    Map<String, dynamic>? params = parseGenericParams<String, dynamic>(parameters?[GameKey.PARAM]);
    Uri uri = Uri.parse(url);
    if (url.isNotEmpty) {
      final headers = <String, String>{};
      final newParams = <String, String>{};
      params?.forEach((key, value) {
        if (value != null) {
          newParams[key] = '$value';
        }
      });
      String errMsg = '';
      try {
        final resp = await getService<AbsHttpService>()?.post(url: uri.toString(), headers: headers, params: newParams);
        if (resp != null) {
          return {
            GameKey.CODE: resp.code,
            GameKey.MSG: resp.msg,
            GameKey.TIMESTAMP: DateTime.now().millisecondsSinceEpoch,
            GameKey.DATA: resp.data,
          };
        }
      } catch (e) {
        Log.d(cocosTag, 'networkRequest $url error->$e');
        errMsg = '$e';
      }
      return Future.value({
        GameKey.CODE: CODE_ILLEGAL_ARGUMENT + 1,
        GameKey.TIMESTAMP: DateTime.now().millisecondsSinceEpoch,
        GameKey.MSG: errMsg,
      });
    } else {
      final result = {
        GameKey.CODE: CODE_ILLEGAL_ARGUMENT,
        GameKey.TIMESTAMP: DateTime.now().millisecondsSinceEpoch,
        GameKey.MSG: "",
      };
      Log.d(cocosTag, "onFailure: $result");
      return Future.value(result);
    }
  }
}
