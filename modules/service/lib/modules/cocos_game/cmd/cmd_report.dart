import 'package:service/service.dart';

import '../bridge/jsbridge_util.dart';
import '../cocos_game_const.dart';
import 'command.dart';

class CommandReport extends Command {
  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    String action = parameters?[GameKey.ACTION] ?? "";
    Map<String, String>? param =
        parseGenericParams<String, dynamic>(parameters?[GameKey.PARAM])?.map((key, value) => MapEntry(key, '$value'));

    if (action.isNotEmpty) {
      getService<AbsStatisticsService>()?.report('$action', params: param);
      return Future.value(null);
    } else {
      return {
        GameKey.STATUS: CODE_ILLEGAL_ARGUMENT,
        GameKey.MSG: "",
      };
    }
  }

  @override
  String get name => "report";
}
