import '../cocos_game_const.dart';
import '../model/game_sound_model.dart';
import 'command.dart';

class CommandOnGameStatus extends Command {
  final OnGameStatusChangeListener? listener;

  CommandOnGameStatus(this.listener);

  @override
  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) {
    final status = parameters?[GameKey.STATUS] as String;
    final params = parameters?[GameKey.PARAM] as Map<String, dynamic>?;
    if (params != null) {
      listener?.onGameStatusChange(status, params);
    }
    return Future.value(null);
  }

  @override
  String get name => 'onGameStatusChange';
}
