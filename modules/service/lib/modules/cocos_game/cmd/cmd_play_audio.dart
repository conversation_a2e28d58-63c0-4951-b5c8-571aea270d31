import '../cocos_game_const.dart';
import '../model/game_sound_model.dart';
import 'command.dart';

class CommandPlayAudio extends Command {
  static const String NAME = 'playAudio';
  final String gameId;
  final OnGameStatusChangeListener listener;

  CommandPlayAudio(this.gameId, this.listener);

  Future<Map<String, dynamic>?> execute(Map<String, dynamic>? parameters) async {
    final url = parameters?[GameKey.SOUND_URL] as String?;
    final times = parameters?[GameKey.TIMES] as int? ?? 1;
    final data = parameters?[GameKey.DATA] as Map<dynamic, dynamic>?;

    String? localUrl;
    if (url != null && url.isNotEmpty) {
      // TODO 离线资源
      // if (OfflineResourceClient.isInit()) {
      //   final resInfo = OfflineResourceClient.getNativeGameResourceInfo(gameId, url);
      //   if (resInfo?.cacheStatus == CacheStatus.CACHED) {
      //     localUrl = resInfo.filePath;
      //   }
      // }
    }
    listener.onGameStatusAudioPlay(GameSoundModel(localUrl ?? '', localUrl, times, data));
    return null;
  }

  @override
  String get name => NAME;
}
