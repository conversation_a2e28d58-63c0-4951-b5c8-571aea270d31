const int kGameCampBlue = 1;
const int kGameCampRed = 2;

const cocosTag = 'cocos-game';

const CODE_SUCCESS = 200; //执行成功
const CODE_NO_METHOD = 10000; //找不到方法
const CODE_ILLEGAL_ARGUMENT = 20001; //参数错误，包含缺失

//内置方法，注入js成功后回调
const REGISTER_SUCCESS_NAME = "onRegisterCallback";

//内置方法，查找支持的方法
const SUPPORT_METHOD_NAME = "isSupportMethod";
const SUPPORT_METHOD_KEY = "methods";

const FETCH = "javascript:JSBridge._fetchMessageQueue()";

const GAME_H5_COMMAND = "gameH5Command";

const String GamePropTypeBuff = 'buff';
const String GamePropTypeCamel = 'camel';
const String GamePropTypeDragon = 'dragon';
const String GamePropTypeUpgradeRole = 'upgrade_role';

/// 游戏画布的宽高比
const double kGameScreenAspectRatio = 750 / 800;

class GameKey {
  static const URL = "url";
  static const COMMAND = "command";
  static const PARAM = "param";
  static const DATA = "data";
  static const ACTION = "action";
  static const GAME_ID = "gameId";
  static const CODE = "code";
  static const MSG = "msg";
  static const TIMESTAMP = "timestamp";
  static const ROOM_ID = "roomId";
  static const SOUND_URL = "soundUrl";
  static const TIMES = "times";
  static const STATUS = "status";
  static const SEAT_INDEX = "seatIndex";
  static const TEAM_ID = "teamId";
  static const IS_READY = "isReady";
  static const SOUND_STATUS = "soundStatus";
  static const LEVEL = "level";
  static const TAG = "tag";
  static const LOG = "log";
  static const GOLD = "gold";
  static const IS_FIRST_PLAY = "isFirstPlay";
  static const CAMP = "camp";
  static const HOST1 = "host1";
  static const HOST2 = "host2";
}

class GameStatus {
  //游戏异常状态
  static const STATUS_GAME_ERROR = "status_game_error";

  //初始化状态
  static const STATUS_GAME_INIT = "status_game_init";

  //游戏空闲状态
  static const STATUS_GAME_WAIT = "status_game_wait";

  //准备游戏状态
  static const STATUS_GAME_READY = "status_game_ready";

  //游戏开始状态
  static const STATUS_GAME_START = "status_game_start";

  //游戏结算状态
  static const STATUS_GAME_SETTLE = "status_game_settle";

  static const STATUS_GAME_AUDIO = "status_game_audio";

  //玩家进入游戏
  static const STATUS_PLAYER_IN = "status_player_in";

  //玩家退出游戏
  static const STATUS_PLAYER_LEAVE = "status_player_leave";

  //玩家是否准备
  static const STATUS_PLAYER_READY = "status_player_ready";

  //指定玩家为队长
  static const STATUS_PLAYER_CAPTAIN = "status_player_captain";
}

class GameAction {
  static const JOIN_GAME = "joinGame";
  static const JOIN_CAMP = "joinCamp";
  static const READY_GAME = "readyGame";
  static const START_GAME = "startGame";
  static const LEAVE_GAME = "leaveGame";
  static const DESTROY_GAME = "destroyGame";
  static const SET_GAME_SOUND_STATUS = "setGameSoundStatus";
  static const GET_GAME_SOUND_STATUS = "getGameSoundStatus";
  static const APP_ENTER_BACKGROUND = "appEnterBackground";
  static const APP_BECOME_ACTIVE = "appBecomeActive";
  static const SUB_PACKAGE_PROGRESS = "subPackageProgress";

  /// 客户端金币变化同步到游戏
  static const NOTIFY_BALANCE = "notifyBalance";

  /// 获取战斗数值，包含双方的战力值和拉扯的进度
  static const GET_BATTLE_STATS = "getBattleStats";
}

class GameErrorCode {
  // 切换阵营错误
  static const int SWITCH_CAMP_ERROR = 801;
}
