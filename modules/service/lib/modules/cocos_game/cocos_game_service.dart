import 'dart:async';

import 'package:get/get.dart';
import 'package:service/global/cache/memory_model_cache.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/service.dart';

import '../account/const/events.dart';
import 'api/cocos_game_api.dart';
import 'model/empire_config_model.dart';

class CocosGameService extends GetxController {
  final CocosGameApi _api = CocosGameApi(baseUrl: AppConfig.instance.baseUrl);

  String? cocosUserToken;

  StreamSubscription? _subLogout;

  CocosGameService() {
    _subLogout = rxUtil.observer<String>(AccountEvent.logoutSuccess).listen((value) {
      cocosUserToken = null;
    });
  }

  @override
  void onClose() {
    _subLogout?.cancel();
    super.onClose();
  }

  Future<FlatHttpResponse<EmpireConfigModel>?> getCachedEmpireConfig() async {
    var cache = MemoryModelCache.inst.get<FlatHttpResponse<EmpireConfigModel>?>(CACHE_KEY_EMPIRE_CONFIG);
    if (cache == null) {
      FlatHttpResponse<EmpireConfigModel> resp = await _api.getEmpireConfig();
      if (resp.isSuccess && resp.data != null) {
        cache = resp;
        MemoryModelCache.inst.put<FlatHttpResponse<EmpireConfigModel>?>(CACHE_KEY_EMPIRE_CONFIG, cache);
      }
    }
    return cache;
  }

  Future<String?> getGameToken() async {
    if (cocosUserToken?.isNotEmpty == true) {
      return Future.value(cocosUserToken!);
    }
    var resp = await _api.getBitrushToken();
    if (resp.isSuccess) {
      cocosUserToken = resp.data?.token;
      return resp.data?.token;
    } else {
      toast(resp.msg ?? LocaleStrings.instance.defaultError);
    }
    return Future.value(null);
  }
}
