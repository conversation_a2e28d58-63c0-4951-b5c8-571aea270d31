// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cocos_game_api.dart';

// **************************************************************************
// FlatHttpGenerator
// **************************************************************************

class _CocosGameApi implements CocosGameApi {
  _CocosGameApi({this.baseUrl});

  String? baseUrl;

  @override
  Future<FlatHttpResponse<RspGetEmpireToken>> getBitrushToken() async {
    final url = '${baseUrl}game/get_bitrush_token';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null
              ? RspGetEmpireToken.fromJson(resp.data!)
              : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<EmpireConfigModel>> getEmpireConfig() async {
    final url = '${baseUrl}game/empire_config';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null
              ? EmpireConfigModel.fromJson(resp.data!)
              : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<dynamic>> empireBarrage({
    required roomId,
    required pkId,
    required type,
    num = '1',
  }) async {
    final url = '${baseUrl}game/empire_barrage';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['room_id'] = roomId;
    params['pk_id'] = pkId;
    params['type'] = type;
    if (null != num) params['num'] = num;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg, data: resp.data);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }
}
