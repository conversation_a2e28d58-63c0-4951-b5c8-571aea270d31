import 'package:feature_flat_base/feature_flat_base.dart';

import '../model/empire_config_model.dart';
import '../model/rsp_get_empire_token.dart';

part 'cocos_game_api.g.dart';

@RestApi()
abstract class CocosGameApi {
  factory CocosGameApi({String baseUrl}) = _CocosGameApi;

  /// 获取帝国游戏Token
  @POST('/game/get_bitrush_token')
  Future<FlatHttpResponse<RspGetEmpireToken>> getBitrushToken();

  /// 获取帝国游戏Token
  @POST('/game/empire_config')
  Future<FlatHttpResponse<EmpireConfigModel>> getEmpireConfig();

  /// 发送帝国游戏弹幕
  @POST('/game/empire_barrage')
  Future<FlatHttpResponse> empireBarrage({
    @Param("room_id") required String roomId,
    @Param("pk_id") required String pkId,
    @Param("type") required String type,
    @Param("num") String num = '1',
  });
}
