import 'dart:async';

import 'package:service/service.dart';

import '../cmd/command.dart';

class CommandsManager {
  static const String TAG = 'CommandsManager';
  final Map<String, Command> _commands = <String, Command>{};

  bool addCommand(Command command) {
    if (!hasCommand(command.name)) {
      _commands[command.name] = command;
      return true;
    }
    return false;
  }

  bool removeCommand(Command command) {
    if (hasCommand(command.name)) {
      _commands.remove(command.name);
      return true;
    }
    return false;
  }

  void removeCommandAll() {
    _commands.clear();
  }

  bool hasCommand(String commandName) {
    return _commands.containsKey(commandName);
  }

  Command? getCommand(String commandName) {
    return _commands[commandName];
  }

  Future<Map<String, dynamic>?> executeCommand(String commandName, Map<String, dynamic>? params) async {
    try {
      Completer<Map<String, dynamic>?> completer = Completer<Map<String, dynamic>?>();
      await handleAutoCommand(commandName, params, (response) {
        completer.complete(response ?? {});
      });
      return completer.future;
    } on Exception catch (e) {
      Log.e(TAG, 'handleAutoCommand error: $e');
      return Future.value(null);
    }
  }

  Future<bool> handleAutoCommand(
    String commandName,
    Map<String, dynamic>? params,
    Function(Map<String, dynamic>?) callback,
  ) async {
    final command = getCommand(commandName);
    if (command != null) {
      Log.i(TAG, 'handleAutoCommand Find command: $commandName');
      final response = await command.execute(params);
      callback(response);
      return true;
    } else {
      Log.i(TAG, 'handleAutoCommand Not find command: $commandName');
      return false;
    }
  }
}
