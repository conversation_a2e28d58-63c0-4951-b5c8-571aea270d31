import 'dart:convert';

import 'package:service/service.dart';

import '../cocos_game_const.dart';
import '../model/native_rsp_model.dart';

// 创建成功的响应体
JsResponseBody createSuccessResponseBody(Map<String, dynamic>? data, [String? message]) {
  return JsResponseBody(
    status: CODE_SUCCESS,
    message: message ?? '',
    data: data ?? {},
  );
}

// 创建失败的响应体
JsResponseBody createFailureResponseBody(int status, Map<String, dynamic>? data, [String? message]) {
  return JsResponseBody(
    status: status,
    message: message ?? '',
    data: data ?? {},
  );
}

// 将模型转换为Map
Map<String, dynamic> model2Map(JsResponseBody? model) {
  try {
    final data = model ?? JsResponseBody();
    final resultJson = jsonEncode(data);
    // 转换成map格式，用于js交互
    return jsonDecode(resultJson);
  } on Exception catch (e) {
    Log.d(cocosTag, 'model2Map $model error->$e');
    return {};
  }
}

Map<K, V>? parseGenericParams<K, V>(dynamic srcMap) {
  if (srcMap is Map) {
    final Map<K, V> result = {};
    for (var entry in srcMap.entries) {
      if (entry.key is K && entry.value is V) {
        result[entry.key as K] = entry.value as V;
      }
    }
    return result;
  } else {
    return null;
  }
}

String dartObj2JsJson(dynamic obj) {
  try {
    String jsonString = jsonEncode(obj);
//     return jsonString
//         .replaceAll("\\", "\\\\")
//         .replaceAll("\"", "\\\"")
// //                .replaceAll("\u002f", "\\u002f")
//         .replaceAll("\'", "\\\'")
// //                .replaceAll("\b", "\\b")
//         .replaceAll("\u000c", "\\f")
//         .replaceAll("\t", "\\t")
//         .replaceAll("\r", "\\r")
//         .replaceAll("\n", "\\n")
// //                .replaceAll("\u0025", "\\u0025")
// //                .replaceAll("\u0026", "\\u0026")
//         .replaceAll("\u0028", "\\u0028")
//         .replaceAll("\u0029", "\\u0029");
//                // .replaceAll("\u003C", "\\u003C")
//                // .replaceAll("\u003E", "\\u003E")

    return jsonString.replaceAll("\`", ""); // js 中 `` 为模板字符串，内部不能在有`

  } catch (e) {
    print('JsonUtils error: $e');
    return '{}';
  }
}

String base64Encode(String str) {
  List<int> bytes = utf8.encode(str);
  String base64Name = base64.encode(bytes);
  return base64Name;
}