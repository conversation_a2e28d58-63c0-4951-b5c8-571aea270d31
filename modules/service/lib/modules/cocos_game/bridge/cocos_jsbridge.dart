import 'dart:async';
import 'dart:convert';
import 'dart:io';

// import 'package:flutter/services.dart';
import 'package:service/service.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../cocos_game_const.dart';
import 'cocos_game_js.dart';
import 'jsbridge_util.dart';
import '../model/jsbridge_response_model.dart';
import '../model/native_req_model.dart';
import '../model/native_rsp_model.dart';

mixin JsBridgeInterface {
  WebViewController get webViewController;

  Future<void> runJavaScript(String javaScript) async {
    /// TODO GameSDK
    // webViewController.loadRequest(Uri.parse(javaScript));
  }

  Future<Object> runJavaScriptReturningResult(String javaScript) {
    /// TODO GameSDK
    return Future.value(null);
    // return webViewController.runJavaScriptReturningResult(javaScript);
  }
}

class CocosJsBridge {
  bool _isRegisterJs = false;

  bool _isDealingRequest = false; //是否正在执行原生请求队列

  int _uniqueId = 0;

  //原生调用js的队列，避免还未注入js时调用了方法而未执行
  final List<NativeRequestModel> _nativeRequestQueue = [];

  // js的请求方法处理，先存入此队列再读取执行
  final Map<String, NativeResponse> _nativeResponseMethods = {};

  // 原生请求js的回调，需要保存起来，用户响应后执行
  final Map<String, NativeRequestCallback?> _nativeRequestCallbacks = {};

  late JsBridgeInterface bridgeInterface;

  CocosJsBridge();

  Future<void> registerJs() async {
    Log.d(cocosTag, '----registerJs-----');
    var complete = Completer();
    // String js = await rootBundle.loadString('assets/test/android_jsbridge.js');
    evaluateJavascript("javascript:$injectJs", (result) async {
      Log.d(cocosTag, 'register js code successfully');
      await _registerSuccess();
      _isRegisterJs = true;
      _loopMessage();
      complete.complete();
    });
    return await complete.future;
  }

  void evaluateJavascript(String script, [Function(String)? callback]) {
    // LogExt.largeD(cocosTag, () => "evaluateJavascript: $script , hasCallback=${callback != null}");
    if (callback != null) {
      bridgeInterface.runJavaScriptReturningResult(script).then((result) {
        if (result is String) {
          callback(result);
        }
      }).catchError((error) {
        Log.e(cocosTag, "Failed to evaluate Javascript: $error");
      });
    } else {
      bridgeInterface.runJavaScript(script);
    }
  }

  // 添加单个原生响应事件
  void addNativeResponse(NativeResponse response) {
    _nativeResponseMethods[response.name] = response;
  }

  // 批量添加原生响应事件
  void addNativeResponses(List<NativeResponse> responses) {
    for (var response in responses) {
      _nativeResponseMethods[response.name] = response;
    }
  }

  Future<void> fetchQueue() async {
    /// 由于新版本jsBridge不能自定义方法，第一次接受消息开始注册jsbridge
    if (!_isRegisterJs) {
      await registerJs();
    }
    evaluateJavascript(FETCH, _handleResponse);
  }

  Future<void> _registerSuccess() async {
    final map = await getService<AbsHttpService>()?.getBaseParams() ?? {};
    var model = JsBridgeResponseModel(
      type: "req",
      name: REGISTER_SUCCESS_NAME,
      id: _genRequestPlatformIdTs(),
      data: map,
    );
    _handleMessage(model);
  }

  String _genRequestPlatformIdTs() {
    late String pl;
    if (Platform.isAndroid) {
      pl = "android";
    } else if (Platform.isIOS) {
      pl = "ios";
    }
    return '${pl}_${++_uniqueId}_${DateTime.now().millisecondsSinceEpoch}';
  }

  void _loopMessage() {
    if (isHandlerIdle() && _nativeRequestQueue.isNotEmpty) {
      _isDealingRequest = true;
      _dealNativeRequestQueue();
    }
  }

  void _handleMessage(JsBridgeResponseModel responseModel) {
    try {
      final json = dartObj2JsJson(responseModel);
      Log.d(cocosTag, "native call js: $json");
      evaluateJavascript('javascript:JSBridge._handleMessage(\`$json\`)');
    } on Exception catch (e) {
      Log.e(cocosTag, 'handleMessage error: ${e.toString()}');
    }
  }

  /// 原生请求h5
  void request(String name, Map<String, dynamic>? data, NativeRequestCallback? callback) {
    // 将请求添加到队列
    _nativeRequestQueue.add(NativeRequestModel()
      ..name = name
      ..data = data
      ..callback = callback);

    // 循环处理消息队列
    _loopMessage();
  }

  void _handleResponse(String result) {
    _parse2List(result);
  }

  void _parse2List(String json) {
    LogExt.largeD(cocosTag, () => "js call native raw json: $json");
    json = json.cleanJsonString().unescapeJava();
    LogExt.largeD(cocosTag, () => "js call native unescapeJava json: $json");
    List<dynamic>? jsonArray;
    try {
      jsonArray = jsonDecode(json);
    } on FormatException catch (e) {
      Log.e(cocosTag, 'parse2List array err -> $e');
    }
    if (jsonArray?.isNotEmpty == true) {
      for (var item in jsonArray!) {
        try {
          var model = JsBridgeResponseModel.fromJson(item);
          _parseModel(model);
        } on FormatException catch (e) {
          Log.e(cocosTag, 'parse2List item err -> $e');
        }
      }
    }
  }

  void _parseModel(JsBridgeResponseModel responseModel) {
    if (responseModel.isResponse) {
      var callback = _nativeRequestCallbacks[responseModel.id];
      callback?.call(responseModel.data);
      _nativeRequestCallbacks.remove(responseModel.id);
    } else {
      _dealRequest(responseModel);
    }
  }

  void _dealRequest(JsBridgeResponseModel responseModel) async {
    var method = _nativeResponseMethods[responseModel.name];
    responseModel.type = "resp";
    if (method != null) {
      final responseData = await method.body(responseModel.data);
      responseModel.data = model2Map(responseData);
      _handleMessage(responseModel);
    } else {
      var responseBody;
      if (_isSupportMethod(responseModel.name)) {
        responseBody = _dealSupportMethod(responseModel);
      } else {
        responseBody = JsResponseBody.empty()..status = CODE_NO_METHOD;
      }
      responseModel.data = model2Map(responseBody);
      _handleMessage(responseModel);
    }
  }

  void _dealNativeRequestQueue() {
    if (_nativeRequestQueue.isNotEmpty) {
      final requests = List<NativeRequestModel>.from(_nativeRequestQueue);
      _nativeRequestQueue.clear();
      for (var request in requests) {
        try {
          final requestId = _genRequestPlatformIdTs();
          _nativeRequestCallbacks[requestId] = request.callback;
          final model = JsBridgeResponseModel(
            id: requestId,
            type: 'req',
            name: request.name,
            data: request.data,
          );
          _handleMessage(model);
        } on Exception catch (e) {
          Log.e(cocosTag, 'dealRequest error: ${e.toString()}');
        }
      }
    }
    _isDealingRequest = false;
    _loopMessage();
  }

  bool _isSupportMethod(String name) {
    return name == SUPPORT_METHOD_NAME;
  }

  JsResponseBody _dealSupportMethod(JsBridgeResponseModel responseModel) {
    final requestMethods = _readSupportMethodsList(responseModel.data?[SUPPORT_METHOD_KEY]);
    final supportMethods = _nativeResponseMethods.keys.toList();
    supportMethods.addAll(_getSupportMethod());

    final map = <String, int>{};
    for (var method in requestMethods) {
      map[method] = supportMethods.contains(method) ? 1 : 0;
    }

    return JsResponseBody(data: {SUPPORT_METHOD_KEY: map});
  }

  List<String> _readSupportMethodsList(dynamic any) {
    final result = <String>[];
    if (any is List<String>) {
      result.addAll(any);
    }
    return result;
  }

  List<String> _getSupportMethod() {
    // Implement your method retrieval logic here
    return [];
  }

  bool isHandlerIdle() {
    return _isRegisterJs && !_isDealingRequest;
  }
}

extension StringExtension on String {
  String cleanJsonString() {
    // 检查字符串是否以双引号开头和结尾
    if (startsWith('"') && endsWith('"') && length > 1) {
      return substring(1, length - 1); // 去除第一个和最后一个字符
    }
    return this;
  }

  String unescapeJava() {
    return replaceAll(r'\n', '\n')
        .replaceAll(r'\t', '\t')
        .replaceAll(r'\r', '\r')
        .replaceAll(r'\f', '\f')
        .replaceAll(r'\b', '\b')
        .replaceAll(r'\"', '\"')
        .replaceAll(r"\'", "\'")
        .replaceAll(r'\\', '\\');
  }
}
