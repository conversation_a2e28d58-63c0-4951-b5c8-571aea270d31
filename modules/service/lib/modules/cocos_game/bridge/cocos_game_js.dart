const JSBridgeName = 'AndroidJSBridge';

String get injectJs => r'''
(function() {
    if (window.JSBridge) {
        return;
    }

    // Polyfill for fullscreen requests to avoid errors in certain environments
    document.documentElement.requestFullscreen = function() {};
    document.documentElement.webkitRequestFullscreen = function() {};
    document.documentElement.mozRequestFullScreen = function() {};
    document.documentElement.msRequestFullscreen = function() {};

    // Global error handler for capturing errors
    if (!window.onerror) {
        window.onerror = function(msg, url, line) {
            console.log("JSBridge: ERROR:" + msg + "@" + url + ":" + line);
        }
    }

    // JSBridge object definition
    window.JSBridge = {
        registerHandler: registerHandler,
        callHandler: callHandler,
        _fetchMessageQueue: _fetchMessageQueue,
        _handleMessage: _handleMessage
    };

    var postMessageQueue = [];
    var messageHandlers = {};
    var responseCallbacks = {};
    var uniqueId = 1;

    /**
     * Registers a handler for a specific message name.
     * @param {string} name - The name of the handler.
     * @param {function} handler - The function to handle the message.
     */
    function registerHandler(name, handler) {
        messageHandlers[name] = handler;
    }

    /**
     * Calls a handler with the specified name and data.
     * @param {string} name - The name of the handler.
     * @param {object} data - The data to be passed to the handler.
     * @param {function} responseCallback - The callback to handle the response.
     */
    function callHandler(name, data, responseCallback) {
        var message = { type: 'req', name: name, data: data };

        // Handle case where data is a function
        if (arguments.length == 2 && typeof data == 'function') {
            responseCallback = data;
            data = null;
        }

        // Ensure data is an object if a response callback is provided
        if (responseCallback && (data !== null && typeof data !== 'object')) {
            responseCallback({ "message": "data must be a map", "status": 20001 });
        } else {
            if (responseCallback) {
                var id = 'web_' + (uniqueId++) + '_' + new Date().getTime();
                responseCallbacks[id] = responseCallback;
                message['id'] = id;
            }
            _postMessage(message);
        }
    }

    /**
     * Posts a message to the native layer.
     * @param {object} message - The message to be posted.
     */
    function _postMessage(message) {
        postMessageQueue.push(message);
        if (window.webkit) {
            window.webkit.messageHandlers.callNative.postMessage(null);
        } else {
            window.AndroidJSBridge.register();
        }
    }

    function _fetchMessageQueue() {
        var messageQueueString = JSON.stringify(postMessageQueue);
        postMessageQueue = [];
        return messageQueueString;
    }

    /**
     * Fetches the current message queue.
     * @returns {string} - The JSON string of the message queue.
     */
    function jsonEscape(str)  {
        return str.replace(/\n/g, "\\n")
                  .replace(/\r/g, "\\r")
                  .replace(/\t/g, "\\t")
                  .replace(/^"|"$/g, '')
                  .replace(/\/"/g, '\"');
    }

    /**
     * Handles messages from the native layer.
     * @param {string} messageJSON - The JSON string of the message.
     */
    function _handleMessage(messageJSON) {
        try {
            var cleanedJSON = jsonEscape(messageJSON);
            console.log('_handleMessage cleaned JSON String:', cleanedJSON);
            var message = JSON.parse(cleanedJSON);
            var type = message.type.toLowerCase();
            console.log('_handleMessage type:', type);
            var responseCallback;

            if (type == 'resp') {
                responseCallback = responseCallbacks[message.id];
                if (!responseCallback) {
                    console.warn("JSBridge: WARNING: No response callback for message ID:", message.id);
                    return;
                }
                responseCallback(message.data);
                delete responseCallbacks[message.id];
            } else if (type == 'req') {
                if (message.id) {
                    responseCallback = function(data) {
                        _postMessage({ type: 'resp', name: message.name, id: message.id, data: data });
                    };
                }

                var handler = messageHandlers[message.name];
                if (!handler) {
                    console.warn("JSBridge: WARNING: No handler for message from Native:", message);
                } else {
                    handler(message.data, responseCallback);
                }
            }
        } catch (e) {
            console.error("JSBridge: ERROR: Failed to handle message. Message JSON:", messageJSON, "Error:", e);
        }
    }

    // Ensure JSBridge callbacks are called once the bridge is ready
    setTimeout(_callJBCallbacks, 0);

    /**
     * Calls all registered JSBridge callbacks.
     */
    function _callJBCallbacks() {
        var callbacks = window.JSBridgeCallbacks;
        delete window.JSBridgeCallbacks;
        if (callbacks) {
            for (var i = 0; i < callbacks.length; i++) {
                callbacks[i](JSBridge);
            }
        }
    }
})();
''';
