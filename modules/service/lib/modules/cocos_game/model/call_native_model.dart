typedef CallNativeCallback = void Function(int code, Map<String, dynamic>? data);

mixin OnCallNativeListener {
  void onCallNative(String command, CallNativeModel callNativeModel, CallNativeCallback callback);
}

class CallNativeModel {
  final String gameId;
  final Map<String, dynamic>? data;
  final int timestamp;

  CallNativeModel({
    required this.gameId,
    this.data,
    int? timestamp,
  }) : timestamp = timestamp ?? DateTime.now().millisecondsSinceEpoch;

  @override
  String toString() {
    return 'CallNativeModel(gameId: $gameId, data: $data, timestamp: $timestamp)';
  }
}
