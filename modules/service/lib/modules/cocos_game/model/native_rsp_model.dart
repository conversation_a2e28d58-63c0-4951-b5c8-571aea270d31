class JsResponseBody {
  int status = 200;
  String message = '';
  Map<String, dynamic>? data;

  JsResponseBody({this.status = 200, this.message = '', this.data});

  // Dart中没有直接的空Map表达式，可以使用以下方式初始化空Map
  JsResponseBody.empty() : data = {};

  factory JsResponseBody.fromJson(Map<String, dynamic> json) {
    return JsResponseBody(
      status: json['status'] ?? 0,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data,
    };
  }

  @override
  String toString() {
    return 'JsResponseBody{status: $status, message: $message, data: $data}';
  }
}

abstract class NativeResponse {
  String get name;

  Future<JsResponseBody?> body(Map<String, dynamic>? data);
}
