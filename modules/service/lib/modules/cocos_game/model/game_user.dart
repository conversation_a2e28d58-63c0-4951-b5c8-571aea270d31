class GameUser {
  String uid = '';
  String token = '';
  String userName = '';
  String avatarUrl = '';
  Map<String, dynamic> extension;

  GameUser({
    this.uid = '',
    this.token = '',
    this.userName = '',
    this.avatarUrl = '',
    this.extension = const {},
  });

  factory GameUser.fromJson(Map<String, dynamic> json) {
    return GameUser(
      uid: json['uid'] ?? '',
      token: json['token'] ?? '',
      userName: json['userName'] ?? '',
      avatarUrl: json['avatarUrl'] ?? '',
      extension: json['extension'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'token': token,
      'userName': userName,
      'avatarUrl': avatarUrl,
      'extension': extension,
    };
  }
}
