import 'package:service/global/model/safe_convert.dart';

typedef OnBattleStatsCallback = void Function(BattleStatsModel model);

class BattleStatsModel {
  ///
  /// 游戏距离，放大了10000倍
  /// let dis = this.gameModel.pos / 10000;
  //  dis = Math.min(dis, 10000);
  //  dis = Math.max(-10000, dis);
  //  this.lab_dis_l.string = Math.round(10000 + dis) / 100 + "m";
  //  this.lab_dis_r.string = Math.round(10000 - dis) / 100 + "m";
  final double pos;
  final int power1;
  final int power2;

  BattleStatsModel({
    this.pos = 0.0,
    this.power1 = 0,
    this.power2 = 0,
  });

  factory BattleStatsModel.fromJson(Map<String, dynamic>? json) => BattleStatsModel(
        pos: asT<double>(json, 'pos'),
        power1: asT<int>(json, 'power1'),
        power2: asT<int>(json, 'power2'),
      );

  Map<String, dynamic> toJson() => {
        'pos': pos,
        'power1': power1,
        'power2': power2,
      };

  @override
  String toString() {
    return 'BattleStatsModel{pos: $pos, power1: $power1, power2: $power2}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BattleStatsModel &&
          runtimeType == other.runtimeType &&
          pos == other.pos &&
          power1 == other.power1 &&
          power2 == other.power2;

  @override
  int get hashCode => pos.hashCode ^ power1.hashCode ^ power2.hashCode;
}
