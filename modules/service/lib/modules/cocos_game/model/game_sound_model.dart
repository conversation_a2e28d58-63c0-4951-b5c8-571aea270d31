mixin OnGameStatusChangeListener {
  void onGameStatusChange(String status, Map<String, dynamic> params);

  void onGameStatusAudioPlay(GameSoundModel sound);
}

class GameSoundModel {
  final String soundUrl;
  final String? filePath;
  final int times;
  final Map<dynamic, dynamic>? extData;

  GameSoundModel(this.soundUrl, this.filePath, this.times, [this.extData]);

  @override
  String toString() {
    return 'GameSoundModel{soundUrl: $soundUrl, filePath: $filePath, times: $times, extData: $extData}';
  }
}
