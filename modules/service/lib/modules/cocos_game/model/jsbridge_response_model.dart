class JsBridgeResponseModel {
  String name;
  Map<String, dynamic>? data;
  String type; // "resp" or "req"
  String id; // message id

  JsBridgeResponseModel({required this.name, this.data, required this.type, required this.id});

  // Check if it's a response
  bool get isResponse => type == "resp";

  @override
  String toString() {
    return 'JsBridgeResponseModel{name: $name, type: $type, id: $id, data: $data}';
  }

  factory JsBridgeResponseModel.fromJson(Map<String, dynamic> json) {
    return JsBridgeResponseModel(
      name: json['name'],
      data: json['data'],
      type: json['type'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'data': data,
      'type': type,
      'id': id,
    };
  }
}
