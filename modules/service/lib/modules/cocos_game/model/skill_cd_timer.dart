import 'dart:async';
import 'dart:math';

class SkillCdTimer {
  final String id;
  final int coolDownDuration;
  Timer? _coolDownTimer;
  bool _isOnCoolDown = false;
  void Function(int rest)? onCoolDownChanged;
  int _rest = 0;

  SkillCdTimer(this.id, this.coolDownDuration, this.onCoolDownChanged) : _rest = coolDownDuration;

  void use() {
    if (_isOnCoolDown) {
      return;
    }
    _startCoolDown();
  }

  void _startCoolDown() {
    _isOnCoolDown = true;
    _coolDownTimer?.cancel();
    _coolDownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        _rest = max(_rest - 1, 0);
        onCoolDownChanged?.call(_rest);
        if (_rest == 0) {
          _coolDownTimer?.cancel();
          _isOnCoolDown = false;
          _rest = coolDownDuration;
        }
      },
    );
  }

  void dispose() {
    _coolDownTimer?.cancel();
  }
}
