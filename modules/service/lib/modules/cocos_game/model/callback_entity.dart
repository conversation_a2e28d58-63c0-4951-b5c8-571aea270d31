class CallBackEntity {
  final int code;
  final String msg;
  final String gameId;
  final int timestamp;
  final Map<String, dynamic>? data;

  bool get isSuccess => code == 1;

  CallBackEntity(this.code, this.msg, this.gameId, this.timestamp, this.data);

  CallBackEntity.fromJson(Map<String, dynamic> json)
      : code = json['code'] as int,
        msg = json['msg'] as String,
        gameId = json['gameId'] as String,
        timestamp = json['timestamp'] as int,
        data = json['data'] as Map<String, dynamic>?;

  Map<String, dynamic> toJson() => {
        'code': code,
        'msg': msg,
        'gameId': gameId,
        'timestamp': timestamp,
        'data': data,
      };
}

typedef CallBackEntityListener = void Function(CallBackEntity entity);