import 'package:service/global/model/safe_convert.dart';
import 'package:service/utils/value_parsers.dart';

class EmpireConfigModel {
  final List<GamePropModel> itemList;
  final List<int> levelList;
  final List<int> rateList;
  final String? gameUrl;
  final int version;
  final H5PageInfo? h5PageInfo;

  EmpireConfigModel({
    required this.itemList,
    required this.levelList,
    required this.rateList,
    required this.gameUrl,
    required this.version,
    required this.h5PageInfo,
  });

  factory EmpireConfigModel.fromJson(Map<String, dynamic>? json) => EmpireConfigModel(
        itemList: asT<List>(json, 'item_list').map((e) => GamePropModel.fromJson(e)).toList(),
        levelList: asT<List>(json, 'level_list').map((e) => toInt(e)).toList(),
        rateList: asT<List>(json, 'rate_list').map((e) => toInt(e)).toList(),
        gameUrl: asNT<String>(json, 'game_url'),
        version: asT<int>(json, 'version'),
        h5PageInfo: asNT<H5PageInfo?>(json, 'h5_page_info', convert: (jsonObj) => H5PageInfo.fromJson(jsonObj)),
      );

  Map<String, dynamic> toJson() => {
        'item_list': itemList.map((e) => e.toJson()).toList(),
        'level_list': levelList.map((e) => e).toList(),
        'rate_list': rateList.map((e) => e).toList(),
        'game_url': gameUrl,
        'version': version,
        'h5_page_info': h5PageInfo,
      };
}

class GamePropModel {
  final int diamond;
  final String type;
  final String giftId;
  final int coolTime;

  GamePropModel({
    this.diamond = 0,
    this.type = "",
    this.giftId = "",
    this.coolTime = 0,
  });

  factory GamePropModel.fromJson(Map<String, dynamic>? json) => GamePropModel(
        diamond: asT<int>(json, 'diamond'),
        type: asT<String>(json, 'type'),
        giftId: asT<String>(json, 'gift_id'),
        coolTime: asT<int>(json, 'cool_time'),
      );

  Map<String, dynamic> toJson() => {
        'diamond': diamond,
        'type': type,
        'gift_id': giftId,
        'cool_time': coolTime,
      };
}

class H5PageInfo {
  final String? param;
  final bool show;

  const H5PageInfo({
    required this.param,
    required this.show,
  });

  factory H5PageInfo.fromJson(Map<String, dynamic>? json) => H5PageInfo(
        param: asNT<String>(json, 'param'),
        show: asT<bool>(json, 'show'),
      );

  Map<String, dynamic> toJson() => {
        'param': param,
        'show': show,
      };
}
