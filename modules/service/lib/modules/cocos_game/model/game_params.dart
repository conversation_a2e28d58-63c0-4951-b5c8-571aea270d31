import 'package:flutter/material.dart';

import 'game_user.dart';

class GameInfo {
  final String gameId;
  final String roomId;

  final Map<String, dynamic> data;

  GameInfo({this.gameId = '', this.roomId = '', this.data = const {}})
      : assert(gameId.isNotEmpty, 'gameId cannot be empty');

  Map<String, dynamic> toJson() {
    return {
      'gameId': gameId,
      'roomId': roomId,
      'data': data,
    };
  }

  factory GameInfo.fromJson(Map<String, dynamic> map) {
    return GameInfo(
      gameId: map['gameId'] as String,
      roomId: map['roomId'] as String,
      data: map['data'] as Map<String, dynamic>,
    );
  }
}

class GameParams {
  final String gameUrl;
  final GameInfo gameInfo;
  final GameUser userInfo;
  final GameViewConfig? gameViewConfig;
  final GameConfig? gameConfig;

  GameParams({
    this.gameUrl = '',
    required this.userInfo,
    required this.gameInfo,
    this.gameViewConfig,
    this.gameConfig,
  }) : assert(gameUrl.isNotEmpty, 'gameUrl cannot be empty');

  Map<String, dynamic> toJson() {
    return {
      'gameUrl': gameUrl,
      'gameInfo': gameInfo,
      'userInfo': userInfo.toJson(),
      'gameViewConfig': gameViewConfig?.toJson(),
      'gameConfig': gameConfig?.toJson(),
    };
  }

  factory GameParams.fromJson(Map<String, dynamic> map) {
    return GameParams(
      gameUrl: map['gameUrl'] as String,
      gameInfo: map['gameInfo'] as GameInfo,
      userInfo: GameUser.fromJson(map['userInfo']),
      gameViewConfig: map['gameViewConfig'] != null ? GameViewConfig.fromJson(map['gameViewConfig']) : null,
      gameConfig: map['gameConfig'] != null ? GameConfig.fromJson(map['gameConfig']) : null,
    );
  }
}

@immutable
class GameConfig {
  final Map<String, dynamic> configMap;

  GameConfig({required this.configMap});

  factory GameConfig.fromJson(Map<String, dynamic> json) {
    return GameConfig(
      configMap: json.cast<String, dynamic>(),
    );
  }

  Map<String, dynamic> toJson() => configMap;
}

@immutable
class GameViewConfig {
  Color backgroundColor = Colors.transparent;
  int viewWidth = 0;
  int viewHeight = 0;

  GameViewConfig({
    this.backgroundColor = Colors.transparent,
    this.viewWidth = 0,
    this.viewHeight = 0,
  });

  factory GameViewConfig.fromJson(Map<String, dynamic> json) {
    return GameViewConfig(
      backgroundColor: Color(json['backgroundColor'] as int),
      viewWidth: json['viewWidth'] as int,
      viewHeight: json['viewHeight'] as int,
    );
  }

  Map<String, dynamic> toJson() => {
        'backgroundColor': backgroundColor.value,
        'viewWidth': viewWidth,
        'viewHeight': viewHeight,
      };
}

class JoinGameParams {
  int? seatIndex;
  int? teamId;
  GameUser? host1;
  GameUser? host2;
  Map<String, dynamic>? extMap;

  JoinGameParams({this.seatIndex, this.teamId, this.host1, this.host2, this.extMap});

  @override
  String toString() {
    return 'JoinGameParams{seatIndex: $seatIndex, teamId: $teamId, host1: $host1, host2: $host2, extMap: $extMap}';
  }
}
