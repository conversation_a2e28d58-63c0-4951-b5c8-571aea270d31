import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_archive/flutter_archive.dart';
import 'package:service/global/sp.dart';
import 'package:service/service.dart';

import 'cocos_game_const.dart';

const int kAssetGameVersion = 2;

class CocosGameCache {
  CocosGameCache._();

  static CocosGameCache inst = CocosGameCache._();

  final _pref = Preferences.newInstance(spWeb);

  Completer<String?>? _copyTask;

  Future<String?> getGamePath() async {
    if (_copyTask != null) {
      return await _copyTask!.future;
    }
    _copyTask = Completer<String?>();
    try {
      var indexPath = await _copyAndUnzipAssets();
      _copyTask!.complete(indexPath);
      return indexPath;
    } finally {
      _copyTask = null; // 重置 Completer
    }
  }

  Future<String?> _copyAndUnzipAssets() async {
    Log.d(cocosTag, 'CocosResCache enter copyAndUnzipAssets');
    // 获取文档目录路径
    final directory = await getApplicationDocumentsDirectory();
    final outputPath = directory.path;

    final unzipDirPath = '$outputPath/game/emp/';

    var indexPath = '${unzipDirPath}index.html';
    var oldV = _pref.getInt(spKeyCocosGameVersion);
    if (oldV == kAssetGameVersion && File(indexPath).existsSync()) {
      Log.d(cocosTag, 'CocosResCache has $indexPath file exit,use it,oldV=$oldV, version=$kAssetGameVersion');
      return indexPath;
    }

    // 定义临时目录路径和解压目录路径
    final tempDirPath = '$outputPath/game_temp/';

    // 确保临时目录存在，如果存在则清空
    final tempDir = Directory(tempDirPath);
    if (await tempDir.exists()) {
      await tempDir.delete(recursive: true);
    }
    await tempDir.create(recursive: true);

    Log.d(cocosTag, 'CocosResCache temp dir $tempDirPath');

    var gameZip = Assets.gameEmp;
    final byteData = await rootBundle.load(gameZip);
    final bytes = byteData.buffer.asUint8List();

    // 将 zip 文件写入临时目录
    final zipFilePath = '$tempDirPath/emp.zip';
    final zipFile = File(zipFilePath);
    await zipFile.writeAsBytes(bytes, flush: true);

    final destinationDir = Directory(unzipDirPath);
    if (destinationDir.existsSync()) {
      Log.d(cocosTag, 'CocosResCache delete old dir $unzipDirPath');
      await destinationDir.delete(recursive: true);
    }
    try {
      await ZipFile.extractToDirectory(zipFile: zipFile, destinationDir: destinationDir);
    } on Exception catch (e) {
      print(e);
    }

    // 删除临时目录
    await tempDir.delete(recursive: true);

    if (File(indexPath).existsSync()) {
      Log.d(cocosTag, 'CocosResCache copyAndUnzip success $indexPath , version=$kAssetGameVersion');
      _pref.setInt(spKeyCocosGameVersion, kAssetGameVersion);
      return indexPath;
    }
    return null;
  }
}
