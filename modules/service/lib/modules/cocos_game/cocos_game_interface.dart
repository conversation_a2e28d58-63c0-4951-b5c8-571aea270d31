import 'cmd/cmd_role_level.dart';
import 'cmd/cmd_user_minicard.dart';
import 'model/battle_stats_model.dart';
import 'model/callback_entity.dart';
import 'model/game_params.dart';
import 'model/game_sound_model.dart';

mixin CocosViewMixin {
  void appBecomeActive();

  void appEnterBackground();

  void reloadGame();

  OnGameStatusChangeListener? onGameStatusChangeListener;
  OnBattleStatsCallback? onBattleStatsCallback;
  LevelCallback? levelCallback;
  OnClickUserMiniCard? onClickUserMiniCard;

  void setGameSoundStatus({required bool status, CallBackEntityListener? listener});

  void joinGame({JoinGameParams? params, CallBackEntityListener? listener});

  void joinCamp({required int camp, CallBackEntityListener? listener});

  void readyGame({required bool isReady, CallBackEntityListener? listener});

  void startGame({CallBackEntityListener? listener});

  void leaveGame({CallBackEntityListener? listener});

  void destroyGame();

  Future<bool> getGameSoundStatus();

  void triggerSyncBattleStats();

  void notifyBalance();
}

class CocosViewController with CocosViewMixin {
  CocosViewMixin? _inner;

  void attach(CocosViewMixin mix) {
    _inner = mix;
  }

  @override
  void appBecomeActive() {
    _inner?.appBecomeActive();
  }

  void detach() {
    onGameStatusChangeListener = null;
    _inner = null;
  }

  @override
  void appEnterBackground() {
    _inner?.appEnterBackground();
  }

  @override
  void destroyGame() {
    _inner?.destroyGame();
  }

  @override
  Future<bool> getGameSoundStatus() async {
    return await _inner?.getGameSoundStatus() ?? false;
  }

  @override
  void joinGame({JoinGameParams? params, CallBackEntityListener? listener}) {
    _inner?.joinGame(params: params, listener: listener);
  }

  @override
  void leaveGame({CallBackEntityListener? listener}) {
    _inner?.leaveGame(listener: listener);
  }

  @override
  void readyGame({required bool isReady, CallBackEntityListener? listener}) {
    _inner?.readyGame(isReady: isReady, listener: listener);
  }

  @override
  void reloadGame() {
    _inner?.reloadGame();
  }

  @override
  void setGameSoundStatus({required bool status, CallBackEntityListener? listener}) {
    _inner?.setGameSoundStatus(status: status, listener: listener);
  }

  @override
  void startGame({CallBackEntityListener? listener}) {
    _inner?.startGame(listener: listener);
  }

  @override
  void notifyBalance() {
    _inner?.notifyBalance();
  }

  void joinCamp({required int camp, CallBackEntityListener? listener}) {
    _inner?.joinCamp(camp: camp, listener: listener);
  }

  void triggerSyncBattleStats() {
    _inner?.triggerSyncBattleStats();
  }
}
