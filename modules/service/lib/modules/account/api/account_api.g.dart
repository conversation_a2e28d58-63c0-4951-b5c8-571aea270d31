// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_api.dart';

// **************************************************************************
// FlatHttpGenerator
// **************************************************************************

class _Account<PERSON>pi implements AccountApi {
  _AccountApi({this.baseUrl});

  String? baseUrl;

  @override
  Future<FlatHttpResponse<AccountInfo>> thirdLogin({
    required thirdType,
    required openId,
    token,
    code,
    emailBase64,
    isDua,
  }) async {
    final url = '${baseUrl}account/third_login';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['third_type'] = thirdType;
    params['openid'] = openId;
    if (null != token) params['token'] = token;
    if (null != code) params['code'] = code;
    if (null != emailBase64) params['f_email_b64'] = emailBase64;
    if (null != isDua) params['is_dua'] = isDua;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? AccountInfo.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<LoginOptions>> thirdLoginOptions() async {
    final url = '${baseUrl}account/third_login_option';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? LoginOptions.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<dynamic>> sendHeartbeat({
    required status,
    path,
    roomId,
    inMic,
    longitude,
    latitude,
  }) async {
    final url = '${baseUrl}account/heartbeat';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['status'] = status;
    if (null != path) params['path'] = path;
    if (null != roomId) params['room_id'] = roomId;
    if (null != inMic) params['on_mic'] = inMic;
    if (null != longitude) params['longitude'] = longitude;
    if (null != latitude) params['latitude'] = latitude;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg, data: resp.data);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<OnlineStatusList>> checkUserStatus(targetUids) async {
    final url = '${baseUrl}user/online_state';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['fuids'] = targetUids;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data:
              resp.data != null ? OnlineStatusList.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<AccountInfo>> checkAccountStatus({isDua}) async {
    final url = '${baseUrl}account/discipline_info';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != isDua) params['is_dua'] = isDua;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? AccountInfo.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<dynamic>> sendSmsCode({required phone}) async {
    final url = '${baseUrl}account/send_code';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['phone'] = phone;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg, data: resp.data);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<AccountInfo>> phoneAccountLogin({
    required phone,
    code,
    passwd,
    isDua,
  }) async {
    final url = '${baseUrl}account/login';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['phone'] = phone;
    if (null != code) params['code'] = code;
    if (null != passwd) params['passwd'] = passwd;
    if (null != isDua) params['is_dua'] = isDua;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? AccountInfo.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<AccountInfo>> userActionUpload({required log}) async {
    final url = '${baseUrl}user/upload_log';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    params['log'] = log;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? AccountInfo.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<EvaluateNeedShowModel>> needShowEvaluate(
      {action = 'need_show'}) async {
    final url = '${baseUrl}account/evaluate_oper';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null
              ? EvaluateNeedShowModel.fromJson(resp.data!)
              : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<dynamic>> submitEvaluate({
    action = 'submit',
    required star,
  }) async {
    final url = '${baseUrl}account/evaluate_oper';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    params['star'] = star;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg, data: resp.data);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<AnchorInfo>> anchorInfo() async {
    final url = '${baseUrl}user/anchor_info';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? AnchorInfo.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }
}
