import 'package:feature_flat_base/feature_flat_base.dart';
import 'package:service/modules/account/model/account_info.dart';
import 'package:service/modules/account/model/online_status.dart';

import '../handler/evaluate_need_show_model.dart';
import '../model/anchor_info.dart';
import '../model/login_options.dart';

part 'account_api.g.dart';

@RestApi()
abstract class AccountApi {
  factory AccountApi({String baseUrl}) = _AccountApi;

  @POST('/account/third_login')
  Future<FlatHttpResponse<AccountInfo>> thirdLogin({
    @Param('third_type') required String thirdType,
    @Param('openid') required String openId,
    @Param('token')  String? token,
    @Param('code')  String? code,
    @Param('f_email_b64')  String? emailBase64,
    @Param('is_dua') String? isDua,
  });

  @POST('/account/third_login_option')
  Future<FlatHttpResponse<LoginOptions>> thirdLoginOptions();

  /// 发送心跳
  @POST('/account/heartbeat')
  Future<FlatHttpResponse> sendHeartbeat({
    @Param() required String status,
    @Param() String? path,
    @Param("room_id") String? roomId,
    @Param("on_mic") String? inMic,
    @Param("longitude") String? longitude,
    @Param("latitude") String? latitude,
  });

  /// 查询用户在线状态
  ///
  /// [targetUids] 批量查询用户在线信息 ","分割
  @POST('/user/online_state')
  Future<FlatHttpResponse<OnlineStatusList>> checkUserStatus(
    @Param('fuids') String targetUids,
  );

  /// 查询账号状态 (是否有效)
  @POST('/account/discipline_info')
  Future<FlatHttpResponse<AccountInfo>> checkAccountStatus({
    @Param('is_dua') String? isDua,
  });

  /// 发送短信验证码
  /// [phone] 手机号
  @POST('/account/send_code')
  Future<FlatHttpResponse> sendSmsCode({@Param('phone') required String phone});

  /// 密码登录或短信验证码登录
  @POST('/account/login')
  Future<FlatHttpResponse<AccountInfo>> phoneAccountLogin({
    @Param('phone') required String phone,
    @Param('code') String? code,
    @Param('passwd') String? passwd,
    @Param('is_dua') String? isDua,
  });

  /// 行为上报
  @POST('/user/upload_log')
  Future<FlatHttpResponse<AccountInfo>> userActionUpload({
    @Param('log') required String log,
  });

  @POST('/account/evaluate_oper')
  Future<FlatHttpResponse<EvaluateNeedShowModel>> needShowEvaluate({
    @Param('action') String action = 'need_show',
  });

  @POST('/account/evaluate_oper')
  Future<FlatHttpResponse> submitEvaluate({
    @Param('action') String action = 'submit',
    @Param('star') required String star,
  });

  @POST('/user/anchor_info')
  Future<FlatHttpResponse<AnchorInfo>> anchorInfo();
}
