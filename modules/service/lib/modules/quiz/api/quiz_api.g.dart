// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_api.dart';

// **************************************************************************
// FlatHttpGenerator
// **************************************************************************

class _QuizApi implements QuizApi {
  _QuizApi({this.baseUrl});

  String? baseUrl;

  @override
  Future<FlatHttpResponse<QuizTestModel>> getTests({
    action = 'list',
    page = '1',
  }) async {
    final url = '${baseUrl}game/info_quiz';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    if (null != page) params['page'] = page;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? QuizTestModel.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<QuizTestModel>> getQuiz({
    action = 'list',
    required quizId,
    page = '1',
  }) async {
    final url = '${baseUrl}game/info_quiz';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    params['quiz_ids'] = quizId;
    if (null != page) params['page'] = page;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data: resp.data != null ? QuizTestModel.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<QuizResultModel>> getResult({
    action = 'list_person',
    required fUid,
    page = '1',
    filter,
  }) async {
    final url = '${baseUrl}game/info_quiz';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    params['fuid'] = fUid;
    if (null != page) params['page'] = page;
    if (null != filter) params['filter'] = filter;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data:
              resp.data != null ? QuizResultModel.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<QuizAnswerModel>> submit({
    action = 'submit',
    required quizId,
    required answer,
    isNature = '',
  }) async {
    final url = '${baseUrl}game/update_quiz';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    params['quiz_id'] = quizId;
    params['answer'] = answer;
    if (null != isNature) params['is_nature'] = isNature;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data:
              resp.data != null ? QuizAnswerModel.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<dynamic>> share({
    action = 'share_post',
    required quizId,
    required answer,
    isNature = '',
  }) async {
    final url = '${baseUrl}game/update_quiz';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};
    if (null != action) params['action'] = action;
    params['quiz_id'] = quizId;
    params['answer'] = answer;
    if (null != isNature) params['is_nature'] = isNature;

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg, data: resp.data);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }

  @override
  Future<FlatHttpResponse<QuizDayLuckModel>> dayLuck() async {
    final url = '${baseUrl}game/day_constellation_luck';
    final mergeReq = false;

    final headers = <String, String>{};

    final params = <String, String>{};

    final resp = await getService<AbsHttpService>()
        ?.post(url: url, headers: headers, params: params, merge: mergeReq);
    if (null != resp) {
      return FlatHttpResponse(resp.code, resp.msg,
          data:
              resp.data != null ? QuizDayLuckModel.fromJson(resp.data!) : null);
    }
    return FlatHttpResponse(
        -1, getService<AbsHttpService>()?.getDefaultErrorMsg() ?? "");
  }
}
