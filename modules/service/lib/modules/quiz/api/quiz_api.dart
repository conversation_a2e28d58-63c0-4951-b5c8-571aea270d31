import 'package:feature_flat_base/annotation/http.dart';
import 'package:feature_flat_base/feature_flat_base.dart';
import 'package:service/modules/quiz/model/quiz_answer_model.dart';
import 'package:service/modules/quiz/model/quiz_day_luck_model.dart';
import 'package:service/modules/quiz/model/quiz_result_model.dart';
import 'package:service/modules/quiz/model/quiz_test_model.dart';

part 'quiz_api.g.dart';

@RestApi()
abstract class QuizApi {
  factory QuizApi({String baseUrl}) = _QuizApi;

  /// 查询quiz题目
  @POST('/game/info_quiz')
  Future<FlatHttpResponse<QuizTestModel>> getTests({
    @Param() String action = 'list',
    @Param() String page = '1',
  });

  /// 查询 具体quiz题目
  /// 性格测试传 action = 'list_nature'
  @POST('/game/info_quiz')
  Future<FlatHttpResponse<QuizTestModel>> getQuiz({
    @Param() String action = 'list',
    @Param('quiz_ids') required String quizId,
    @Param() String page = '1',
  });

  /// 查询quiz 已完成结果
  @POST('/game/info_quiz')
  Future<FlatHttpResponse<QuizResultModel>> getResult({
    @Param() String action = 'list_person',
    @Param('fuid') required String fUid,
    @Param() String page = '1',
    @Param() String? filter,
  });


  /// 提交quiz结果
  @POST('/game/update_quiz')
  Future<FlatHttpResponse<QuizAnswerModel>> submit({
    @Param() String action = 'submit',
    @Param('quiz_id') required String quizId,
    @Param() required String answer,
    @Param('is_nature') String isNature = '',
  });

  /// 分享quiz结果
  @POST('/game/update_quiz')
  Future<FlatHttpResponse> share({
    @Param() String action = 'share_post',
    @Param('quiz_id') required String quizId,
    @Param() required String answer,
    @Param('is_nature') String isNature = '',
  });

  /// 性格测试
  @POST('/game/day_constellation_luck')
  Future<FlatHttpResponse<QuizDayLuckModel>> dayLuck();
}
