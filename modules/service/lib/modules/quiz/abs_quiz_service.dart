import 'package:service/modules/quiz/model/quiz_result_model.dart';

import '../../service.dart';
import 'model/quiz_answer_model.dart';
import 'model/quiz_day_luck_model.dart';
import 'model/quiz_test_model.dart';

@IService(desc: "Quiz 服务")
abstract class AbsQuizService extends AbsService {
  Future<FlatHttpResponse<QuizTestModel>> getTests({int page = 1});

  Future<FlatHttpResponse<QuizTestModel>> getQuiz(String quizId,
      {bool isNature = false});

  Future<FlatHttpResponse<QuizResultModel>> getResult(
      {int page = 1, String? filter});

  /// 提交quiz结果
  Future<FlatHttpResponse<QuizAnswerModel>> submit(String quizId, String answer,
      {bool isNature = false});

  /// 分享到动态
  Future<FlatHttpResponse> share(String quizId, String answer,
      {bool isNature = false});

  ///
  Future<FlatHttpResponse<QuizDayLuckModel>> getDayLuck();
}
