import 'package:library_service/service/service.dart';
import 'package:service/common/statistics/quiz_statistics.g.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/modules/quiz/abs_quiz_service.dart';
import 'package:service/modules/quiz/api/quiz_api.dart';
import 'package:service/modules/sticker/const/task_action_code.dart';
import 'package:service/service.dart';

import 'model/quiz_answer_model.dart';
import 'model/quiz_day_luck_model.dart';
import 'model/quiz_result_model.dart';
import 'model/quiz_test_model.dart';

@Service()
class QuizService extends AbsQuizService {
  final _api = QuizApi(baseUrl: AppConfig.instance.baseUrl);

  @override
  Future<FlatHttpResponse<QuizTestModel>> getTests({int page = 1}) async {
    int startTime = DateTime.now().millisecondsSinceEpoch;
    var resp = await _api.getTests(page: '$page');
    int endTime = DateTime.now().millisecondsSinceEpoch;
    QuizStatistics.reportQuizListRequestEnd(
      onTime: '${endTime - startTime}',
      reason: '${resp.code}',
      result: '${resp.isSuccess ? 'succ' : 'fail'}',
    );
    return resp;
  }

  @override
  Future<FlatHttpResponse<QuizTestModel>> getQuiz(String quizId,
      {bool isNature = false}) async {
    int startTime = DateTime.now().millisecondsSinceEpoch;
    var resp = await _api.getQuiz(
        quizId: quizId, action: isNature ? 'list_nature' : 'list');
    int endTime = DateTime.now().millisecondsSinceEpoch;
    QuizStatistics.reportQuizDetailRequestEnd(
      onTime: '${endTime - startTime}',
      reason: resp.isSuccess ? '' : '${resp.code}',
      result: '${resp.isSuccess ? 'succ' : 'fail'}',
      content: quizId,
    );
    return resp;
  }

  @override
  Future<FlatHttpResponse<QuizResultModel>> getResult(
      {int page = 1, String? filter}) {
    return _api.getResult(
        page: '$page',
        fUid: accountService.getAccountInfo()?.uid ?? '',
        filter: filter);
  }

  @override
  Future<FlatHttpResponse<QuizAnswerModel>> submit(String quizId, String answer,
      {bool isNature = false}) async{
    int startTime = DateTime.now().millisecondsSinceEpoch;
    var resp = await _api.submit(
        quizId: quizId, answer: answer, isNature: isNature ? '1' : '');
    int endTime = DateTime.now().millisecondsSinceEpoch;
    QuizStatistics.reportQuizDetailResultRequestEnd(
      onTime: '${endTime - startTime}',
      reason: resp.isSuccess ? '' : '${resp.code}',
      result: '${resp.isSuccess ? 'succ' : 'fail'}',
      content: quizId,
    );
    return resp;
  }

  @override
  Future<FlatHttpResponse> share(String quizId, String answer,
      {bool isNature = false}) {
    return _api.share(
        quizId: quizId, answer: answer, isNature: isNature ? '1' : '');
  }

  @override
  Future<FlatHttpResponse<QuizDayLuckModel>> getDayLuck() {
    return _api.dayLuck();
  }
}
