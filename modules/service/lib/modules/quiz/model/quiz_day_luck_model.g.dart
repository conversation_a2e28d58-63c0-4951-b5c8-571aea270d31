// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_day_luck_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuizDayLuckModel _$QuizDayLuckModelFromJson(Map<String, dynamic> json) =>
    QuizDayLuckModel()
      ..recommendFriends = json['recommend_friends'] == null
          ? null
          : QuizUserRecord.fromJson(
              json['recommend_friends'] as Map<String, dynamic>)
      ..userExt = json['user_ext'] == null
          ? null
          : UserExt.fromJson(json['user_ext'] as Map<String, dynamic>)
      ..constellationLuck = json['constellation_luck'] == null
          ? null
          : ConstellationLuck.fromJson(
              json['constellation_luck'] as Map<String, dynamic>);

Map<String, dynamic> _$QuizDayLuckModelToJson(QuizDayLuckModel instance) =>
    <String, dynamic>{
      'recommend_friends': instance.recommendFriends?.toJson(),
      'user_ext': instance.userExt?.toJson(),
      'constellation_luck': instance.constellationLuck?.toJson(),
    };

UserExt _$UserExtFromJson(Map<String, dynamic> json) =>
    UserExt()..nature = json['nature'] as String?;

Map<String, dynamic> _$UserExtToJson(UserExt instance) => <String, dynamic>{
      'nature': instance.nature,
    };

ConstellationLuck _$ConstellationLuckFromJson(Map<String, dynamic> json) =>
    ConstellationLuck()
      ..energyIndex = toInt(json['energy_index'])
      ..financeIndex = toInt(json['finance_index'])
      ..humorIndex = toInt(json['humor_index'])
      ..luckFemale = json['luck_female'] as String?
      ..luckHoroscopes = json['luck_horoscopes'] as String?
      ..luckIndex = toInt(json['luck_index'])
      ..luckMale = json['luck_male'] as String?
      ..luckNumber = json['luck_number'] as String?
      ..source = json['source'] as String?
      ..sourceShow = json['source_show'] as String?
      ..constellationPic = json['constellation_pic'] as String?;

Map<String, dynamic> _$ConstellationLuckToJson(ConstellationLuck instance) =>
    <String, dynamic>{
      'energy_index': instance.energyIndex,
      'finance_index': instance.financeIndex,
      'humor_index': instance.humorIndex,
      'luck_female': instance.luckFemale,
      'luck_horoscopes': instance.luckHoroscopes,
      'luck_index': instance.luckIndex,
      'luck_male': instance.luckMale,
      'luck_number': instance.luckNumber,
      'source': instance.source,
      'source_show': instance.sourceShow,
      'constellation_pic': instance.constellationPic,
    };
