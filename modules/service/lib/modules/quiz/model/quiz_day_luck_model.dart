import 'package:json_annotation/json_annotation.dart';
import 'package:service/utils/value_parsers.dart';

import 'quiz_answer_model.dart';

part 'quiz_day_luck_model.g.dart';

@JsonSerializable(explicitToJson: true)
class QuizDayLuckModel {
  @JsonKey(name: 'recommend_friends')
  QuizUserRecord? recommendFriends;

  @JsonKey(name: 'user_ext')
  UserExt? userExt;

  @JsonKey(name: 'constellation_luck')
  ConstellationLuck? constellationLuck;



  QuizDayLuckModel();

  factory QuizDayLuckModel.fromJson(Map<String, dynamic> json) =>
      _$QuizDayLuckModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizDayLuckModelToJson(this);
}

@JsonSerializable()
class UserExt {
  @JsonKey(name: "nature")
  String? nature;

  UserExt();

  factory UserExt.fromJson(Map<String, dynamic> json) =>
      _$UserExtFromJson(json);

  Map<String, dynamic> toJson() => _$UserExtToJson(this);
}

@JsonSerializable()
class ConstellationLuck {
  @JsonKey(name: "energy_index", fromJson: toInt)
  int? energyIndex;
  @JsonKey(name: "finance_index", fromJson: toInt)
  int? financeIndex;
  @JsonKey(name: "humor_index", fromJson: toInt)
  int? humorIndex;
  @JsonKey(name: "luck_female")
  String? luckFemale;
  @JsonKey(name: "luck_horoscopes")
  String? luckHoroscopes;
  @JsonKey(name: "luck_index", fromJson: toInt)
  int? luckIndex;
  @JsonKey(name: "luck_male")
  String? luckMale;
  @JsonKey(name: "luck_number")
  String? luckNumber;

  String? source;

  @JsonKey(name: "source_show")
  String? sourceShow;

  @JsonKey(name: "constellation_pic")
  String? constellationPic;

  ConstellationLuck();

  factory ConstellationLuck.fromJson(Map<String, dynamic> json) =>
      _$ConstellationLuckFromJson(json);

  Map<String, dynamic> toJson() => _$ConstellationLuckToJson(this);
}
