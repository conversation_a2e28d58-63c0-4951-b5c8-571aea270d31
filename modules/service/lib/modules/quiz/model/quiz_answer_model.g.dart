// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_answer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuizAnswerModel _$QuizAnswerModelFromJson(Map<String, dynamic> json) =>
    QuizAnswerModel()
      ..quizImageResultUrl = json['quiz_image_result_url'] as String?
      ..quizRecords = (json['quiz_recos'] as List<dynamic>?)
          ?.map((e) => QuizTestItemModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..userRecords = (json['user_recos'] as List<dynamic>?)
          ?.map((e) => QuizUserRecord.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$QuizAnswerModelToJson(QuizAnswerModel instance) =>
    <String, dynamic>{
      'quiz_image_result_url': instance.quizImageResultUrl,
      'quiz_recos': instance.quizRecords?.map((e) => e.toJson()).toList(),
      'user_recos': instance.userRecords?.map((e) => e.toJson()).toList(),
    };

QuizUserRecord _$QuizUserRecordFromJson(Map<String, dynamic> json) =>
    QuizUserRecord()
      ..avatar = json['avatar'] as String?
      ..avatarCode = dynamicToString(json['avatar_code'])
      ..uid = dynamicToString(json['uid'])
      ..nickname = dynamicToString(json['nickname'])
      ..matching = toIntOrNull(json['matching_score'])
      ..userExt = json['user_ext'] == null
          ? null
          : UserExt.fromJson(json['user_ext'] as Map<String, dynamic>);

Map<String, dynamic> _$QuizUserRecordToJson(QuizUserRecord instance) =>
    <String, dynamic>{
      'avatar': instance.avatar,
      'avatar_code': instance.avatarCode,
      'uid': instance.uid,
      'nickname': instance.nickname,
      'matching_score': instance.matching,
      'user_ext': instance.userExt?.toJson(),
    };
