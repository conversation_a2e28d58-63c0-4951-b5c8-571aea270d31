import 'package:json_annotation/json_annotation.dart';
import 'package:service/utils/value_parsers.dart';

part 'quiz_result_model.g.dart';

@JsonSerializable(explicitToJson: true)
class QuizResultModel {
  List<QuizResultItemModel>? data;
  @JsonKey(fromJson: toInt)
  int page = 0;
  @JsonKey(fromJson: toInt)
  int size = 0;

  @JsonKey(name: "total_page", fromJson: toInt)
  int totalPage = 0;

  QuizResultModel();

  factory QuizResultModel.fromJson(Map<String, dynamic> json) =>
      _$QuizResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizResultModelToJson(this);
}

@JsonSerializable()
class QuizResultItemModel {
  @JsonKey(name: "quiz_image_result_url")
  String? quizImageResultUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: "quiz_id", fromJson: dynamicToString)
  String? quizId;

  @JsonKey(name: 'h5_url', fromJson: dynamicToString)
  String? h5Url;

  String? bg;

  String? answer;
  String? title;

  QuizResultItemModel();

  factory QuizResultItemModel.fromJson(Map<String, dynamic> json) =>
      _$QuizResultItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizResultItemModelToJson(this);
}
