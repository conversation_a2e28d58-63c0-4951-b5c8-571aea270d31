import 'package:json_annotation/json_annotation.dart';
import 'package:service/modules/quiz/model/quiz_test_model.dart';
import 'package:service/modules/user/model/user_ext.dart';
import 'package:service/utils/value_parsers.dart';

part 'quiz_answer_model.g.dart';

@JsonSerializable(explicitToJson: true)
class QuizAnswerModel {
  @JsonKey(name: 'quiz_image_result_url')
  String? quizImageResultUrl;

  @JsonKey(name: 'quiz_recos')
  List<QuizTestItemModel>? quizRecords;

  @JsonKey(name: 'user_recos')
  List<QuizUserRecord>? userRecords;

  QuizAnswerModel();

  factory QuizAnswerModel.fromJson(Map<String, dynamic> json) =>
      _$QuizAnswerModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizAnswerModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class QuizUserRecord {
  String? avatar;

  @JsonKey(fromJson: dynamicToString, name: 'avatar_code')
  String? avatarCode;

  @JsonKey(fromJson: dynamicToString)
  String? uid;

  @JsonKey(fromJson: dynamicToString)
  String? nickname;

  @JsonKey(name: 'matching_score', fromJson: toIntOrNull)
  int? matching;

  /// 扩展
  @JsonKey(name: "user_ext")
  UserExt? userExt;

  QuizUserRecord();

  factory QuizUserRecord.fromJson(Map<String, dynamic> json) =>
      _$QuizUserRecordFromJson(json);

  Map<String, dynamic> toJson() => _$QuizUserRecordToJson(this);

  bool get isVoiceAuth => userExt?.voiceAuditStatus == 'online';
}
