// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_test_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuizTestModel _$QuizTestModelFromJson(Map<String, dynamic> json) =>
    QuizTestModel()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => QuizTestItemModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..page = toInt(json['page'])
      ..size = toInt(json['size'])
      ..totalPage = toInt(json['total_page']);

Map<String, dynamic> _$QuizTestModelToJson(QuizTestModel instance) =>
    <String, dynamic>{
      'data': instance.data?.map((e) => e.toJson()).toList(),
      'page': instance.page,
      'size': instance.size,
      'total_page': instance.totalPage,
    };

QuizTestItemModel _$QuizTestItemModelFromJson(Map<String, dynamic> json) =>
    QuizTestItemModel()
      ..bg = json['bg'] as String?
      ..bgBig = json['bg_big'] as String?
      ..questionBg = json['question_bg'] as String?
      ..questionBgBig = json['question_bg_big'] as String?
      ..isNew = json['is_new'] as bool?
      ..quizId = json['quiz_id'] as String?
      ..title = json['title'] as String?
      ..webUrl = json['h5_url'] as String?
      ..tested = json['tested'] as bool?
      ..questions = (json['questions'] as List<dynamic>?)
          ?.map((e) => QuizQuestionItem.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$QuizTestItemModelToJson(QuizTestItemModel instance) =>
    <String, dynamic>{
      'bg': instance.bg,
      'bg_big': instance.bgBig,
      'question_bg': instance.questionBg,
      'question_bg_big': instance.questionBgBig,
      'is_new': instance.isNew,
      'quiz_id': instance.quizId,
      'title': instance.title,
      'h5_url': instance.webUrl,
      'tested': instance.tested,
      'questions': instance.questions?.map((e) => e.toJson()).toList(),
    };

QuizQuestionItem _$QuizQuestionItemFromJson(Map<String, dynamic> json) =>
    QuizQuestionItem()
      ..qType = json['qtype'] as String?
      ..title = json['title'] as String?
      ..content =
          (json['content'] as List<dynamic>?)?.map((e) => e as String).toList();

Map<String, dynamic> _$QuizQuestionItemToJson(QuizQuestionItem instance) =>
    <String, dynamic>{
      'qtype': instance.qType,
      'title': instance.title,
      'content': instance.content,
    };
