import 'package:json_annotation/json_annotation.dart';
import 'package:service/utils/value_parsers.dart';

part 'quiz_test_model.g.dart';

@JsonSerializable(explicitToJson: true)
class QuizTestModel {
  List<QuizTestItemModel>? data;
  @JsonKey(fromJson: toInt)
  int page = 0;
  @JsonKey(fromJson: toInt)
  int size = 0;

  @Json<PERSON><PERSON>(name: "total_page", fromJson: toInt)
  int totalPage = 0;

  QuizTestModel();


  factory QuizTestModel.fromJson(Map<String, dynamic> json) =>
      _$QuizTestModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizTestModelToJson(this);
}


@JsonSerializable(explicitToJson: true)
class QuizTestItemModel {

  String? bg;
  @JsonKey(name: "bg_big")
  String? bgBig;

  @<PERSON><PERSON><PERSON><PERSON>(name: "question_bg")
  String? questionBg;

  @J<PERSON><PERSON><PERSON>(name: "question_bg_big")
  String? questionBgBig;

  @<PERSON><PERSON><PERSON><PERSON>(name: "is_new")
  bool? isNew;

  @J<PERSON><PERSON><PERSON>(name: "quiz_id")
  String? quizId;
  String? title;
  @JsonKey(name: "h5_url")
  String? webUrl;

  bool? tested;

  List<QuizQuestionItem>? questions;

  QuizTestItemModel();

  factory QuizTestItemModel.fromJson(Map<String, dynamic> json) =>
      _$QuizTestItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizTestItemModelToJson(this);


}

@JsonSerializable()
class QuizQuestionItem {
  @JsonKey(name: "qtype")
  String? qType;
  String? title;
  List<String>? content;
  QuizQuestionItem();

  bool get isImage => qType == 'image';

  factory QuizQuestionItem.fromJson(Map<String, dynamic> json) =>
      _$QuizQuestionItemFromJson(json);

  Map<String, dynamic> toJson() => _$QuizQuestionItemToJson(this);
}