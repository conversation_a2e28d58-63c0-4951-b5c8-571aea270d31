// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuizResultModel _$QuizResultModelFromJson(Map<String, dynamic> json) =>
    QuizResultModel()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => QuizResultItemModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..page = toInt(json['page'])
      ..size = toInt(json['size'])
      ..totalPage = toInt(json['total_page']);

Map<String, dynamic> _$QuizResultModelToJson(QuizResultModel instance) =>
    <String, dynamic>{
      'data': instance.data?.map((e) => e.toJson()).toList(),
      'page': instance.page,
      'size': instance.size,
      'total_page': instance.totalPage,
    };

QuizResultItemModel _$QuizResultItemModelFromJson(Map<String, dynamic> json) =>
    QuizResultItemModel()
      ..quizImageResultUrl = json['quiz_image_result_url'] as String?
      ..quizId = dynamicToString(json['quiz_id'])
      ..h5Url = dynamicToString(json['h5_url'])
      ..bg = json['bg'] as String?
      ..answer = json['answer'] as String?
      ..title = json['title'] as String?;

Map<String, dynamic> _$QuizResultItemModelToJson(
        QuizResultItemModel instance) =>
    <String, dynamic>{
      'quiz_image_result_url': instance.quizImageResultUrl,
      'quiz_id': instance.quizId,
      'h5_url': instance.h5Url,
      'bg': instance.bg,
      'answer': instance.answer,
      'title': instance.title,
    };
