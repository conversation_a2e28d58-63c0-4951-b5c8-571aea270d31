import 'package:service/modules/live/chat/model/chat_join_fans_club_msg_content.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/notification/handler/i_notification_handler.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';

import '../../room/model/room_user_info_model.dart';
import '../const.dart';
import '../model/chat_room_name_content.dart';
import '../model/chat_stay_msg_content.dart';
import '../model/chat_user_follow_msg_content.dart';
import '../model/chat_welcome_msg_content.dart';
import '../model/notice_msg_content.dart';
import '../model/room_share_msg_content.dart';

/// 1 进房通知，2 欢迎消息，3房间公告，4分享房间,5.关注消息
/// 6:停留1min邀请
/// 等处理
class RoomOtherMsgHandler implements INotificationHandler {
  RoomOtherMsgHandler();

  @override
  PbPushEvent event() {
    return PbPushEvent.PbPushEvent_ROOM_NOTICE_MSG;
  }

  @override
  void onHandle(PbBizPush push, List<int> data, bool isPush, {Map<String, dynamic>? extra}) {
    final notice = PbRoomOtherMsgNotice.fromBuffer(data);
    _onRoomOtherMsgReceived(notice);
  }

  void _onRoomOtherMsgReceived(PbRoomOtherMsgNotice notice) async {
    final user = await roomService.getCurrentUserInfo();
    switch (notice.type) {
      case RoomOtherMsgType.enter:
      case RoomOtherMsgType.welcome:
        final rsp = await roomService.getRoomUserCard(notice.user.user.uid);
        final isNewbie = (await rsp.data?.isNewbie) ?? false;
        final msg = ChatWelcomeMsgContent(
            isManager: (user?.isOwner ?? false) || (user?.isAdmin ?? false),
            chatUserInfo: RoomUserInfoModel.fromPb(notice.user),
            otherUser: notice.otherUser,
            type: notice.type,
            content: notice.content,
            isNewbie: isNewbie);
        if ((roomService.getCurrentRoom()?.isGameMode ?? false) ||
            (roomService.getCurrentRoom()?.isBlindDate ?? false)) {
          roomChatService.addMsg([msg]);
        } else {
          rxUtil.send(RoomUserEvent.enterTheRoom, msg);
        }
        break;
      case RoomOtherMsgType.announce:
        roomChatService
            .addMsg([NoticeMsgContent(content: notice.content, isRules: false, isManager: user?.isOwner ?? false)]);
        break;
      case RoomOtherMsgType.shareRoom:
        roomChatService.addMsg([
          RoomShareMsgContent(
            chatUserInfo: RoomUserInfoModel.fromPb(notice.user),
          ),
        ]);
        break;
      case RoomOtherMsgType.beFollow:
        var sender = RoomUserInfoModel.fromPb(notice.user);
        var relation = await userService.getRelation(targetUid: sender.uid!);
        roomChatService.addMsg([
          ChatUserFollowMsgContent(
            chatUserInfo: sender,
            isFollowing: relation.isFollowing == 1,
          )
        ]);
        break;
      case RoomOtherMsgType.stayInvite:
        var sender = RoomUserInfoModel.fromPb(notice.user);
        roomChatService.addMsg([ChatStayMsgContent(chatUserInfo: sender, type: invitation)]);
        break;
      case RoomOtherMsgType.roomName:
        roomChatService.addMsg([ChatRoomNameContent(roomName: notice.content)]);
        break;
      case RoomOtherMsgType.joinFansClub:
        var roomUser = RoomUserInfoModel.fromPb(notice.user);
        roomService.updateRoomUser(roomUser);
        roomChatService.addMsg([ChatJoinFansClubMsgContent(chatUserInfo: RoomUserInfoModel.fromPb(notice.user))]);
        break;
    }
  }
}
