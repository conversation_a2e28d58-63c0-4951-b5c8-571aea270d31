import 'package:flutter/foundation.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/modules/cocos_game/model/game_empire_notice_ext.dart';
import 'package:service/modules/notification/handler/i_notification_handler.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_game.pb.dart';
import 'package:service/service.dart';

import '../event/event.dart';
import 'pb_log_manager.dart';

class RoomGameEmpireHandler implements INotificationHandler {
  @override
  PbPushEvent event() {
    return PbPushEvent.PbPushEvent_ROOM_GAME_EMPIRE_NOTICE;
  }

  @override
  void onHandle(PbBizPush push, List<int> data, bool isPush, {Map<String, dynamic>? extra}) {
    var notice = PbRoomGameEmpireNotice.fromBuffer(data);
    if (notice.isMatch()) {
      roomService.cocosGameService.matchInfo = notice.matchInfo;
    } else if (notice.isResult()) {
      roomService.cocosGameService.handleGameResult(notice.resultInfo);
    }
    rxUtil.send<PbRoomGameEmpireNotice>(RoomGameEmpireEvent.notice, notice);

    if (kDebugMode || debugEnv) {
      PbLogManager.instance().addLog(PbLogModel()
        ..code = push.pushEvent.name
        ..time = push.timestamp.toString()
        ..content = notice.writeToJson());
    }
  }
}
