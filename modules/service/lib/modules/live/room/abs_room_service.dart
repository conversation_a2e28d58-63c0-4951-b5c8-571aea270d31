import 'package:service/global/model/list_resp.dart';
import 'package:service/modules/live/room/model/bonus_model.dart';
import 'package:service/modules/live/room/model/cross_room_pk_record.dart';
import 'package:service/modules/live/room/model/cross_room_pk_record_list.dart';
import 'package:service/modules/live/room/model/room_country_list.dart';
import 'package:service/modules/live/room/model/room_member_tab_config.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';
import 'package:service/modules/live/room/model/room_tag_model.dart';
import 'package:service/modules/live/room/model/room_theme_model.dart';
import 'package:service/modules/mall/model/mall_tabs.dart';
import 'package:service/modules/task/model/task_model.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
import 'package:service/service.dart';

import '../game/model/search_result.dart';
import '../room_factory_type/abs_room_factory_service.dart';
import '../room_rank/model/room_rank_list_model.dart';
import 'const/const.dart';
import 'controller/room_cocos_game_service.dart';
import 'controller/room_sign_in_controller.dart';
import '../room_fans/room_fans_service.dart';
import 'model/activity_model.dart';
import 'model/bg_music_play_model.dart';
import 'model/bg_music_style_model.dart';
import 'model/join_room_resp.dart';
import 'model/leave_room_resp.dart';
import 'model/online_user_list_model.dart';
import 'model/ranking_model.dart';
import 'model/room_common_config.dart';
import 'model/room_gift_gallery_rank.dart';
import 'model/room_info_model.dart';
import 'model/room_level.dart';
import 'model/room_list_model.dart';
import 'model/room_mode_list_model.dart';
import 'model/room_name_list_model.dart';
import 'model/room_punish_config.dart';
import 'model/room_stay_time_model.dart';
import 'model/room_user_info_model.dart';
import 'model/room_video_model.dart';
import 'model/user_live_status.dart';

@IService(desc: "房间服务")
abstract class AbsRoomService extends AbsService with AbsRoomFactoryService {
  Preferences get preferences;

  /// 进房间
  Future<FlatHttpResponse<RoomInfoModel>> joinRoom(String roomId, {String pwd = '', String? from});

  /// 匹配进房
  Future<FlatHttpResponse> joinRoomChannel();

  /// 正在房间且开播中
  bool isInLive();

  bool isRoomChannel();

  /// 当前语聊房信息
  RoomInfoModel? getCurrentRoom();

  /// 获取当前用户信息
  Future<RoomUserInfoModel?> getCurrentUserInfo({bool forceUpdate = false});

  /// 获取当前uid
  String? getCurrentUid();

  /// 当前房间号
  String? getCurrentRoomId();

  bool isRoomOwner();

  /// 退出房间
  /// [close] 是否关闭房间
  Future leaveRoom(String roomId,
      {bool needSignal = true,
      bool needMedia = true,
      bool close = false,
      Function(FlatHttpResponse<LeaveRoomResp> value)? leaveRoomCallback});

  /// 获取房间用户信息
  /// [forceUpdate] 强制刷行
  ///
  Future<FlatHttpResponse<RoomUserInfoModel>> getRoomUserCard(String uid,
      {bool forceUpdate = false, bool needRefresh = true});

  /// 获取房间用户，前15个，和房间总人数
  Future<List<RoomUserInfoModel>?> getRoomUserList();

  /// 信令更新当前房间用户信息
  void updateRoomUser(RoomUserInfoModel roomUser);

  /// 更新房间信息
  void updateRoomInfo({PbRoomInfoUpdateNotice? pb, RoomInfoModel? roomInfo});

  /// 更新输入信息中不为空的字段
  bool mergeRoomInfo({
    int? roomUserCount,
    FansClubInfo? fansClubInfo,
    FansClubShow? fansClubShow,
  });

  /// 获取房间可配置的背景
  Future<List<RoomThemeBackpack>?> getRoomThemeList();

  /// 获取房间配置的tag list
  Future<List<RoomTagModel>?> getRoomTagList();

  /// 编辑房间信息
  Future<FlatHttpResponse<EditRoomResult>> editRoomInfo({
    String? roomName,
    String? announce,
    String? roomCover,
    String? tagId,
    String? tagName,
    String? micPermission,
    String? roomMode,
    String? cpStep,
    String? enterPermission,
    String? micMode,
    String? fee,
    String? gameMode,
  });

  /// 编辑房间信息
  Future<FlatHttpResponse> editRoomBg({String? bagId, String? bgId, String? vipImg});

  /// vip上传房间背景
  Future<FlatHttpResponse> uploadRoomBg(String url);

  /// 获取房间成员列表
  Future<OnlineUserListModel?> getFollowerList({required int page, required int size});

  /// 修改房间密码
  Future<bool> editRoomPw({required bool lock, String? pw});

  /// 关注房间
  Future<FlatHttpResponse> followerRoom({String? roomId, String? act, String? from});

  /// 取消关注
  Future<FlatHttpResponse> removeFollowerRoom({String? roomId, String? act, String? from});

  /// 关注房间列表
  Future<FlatHttpResponse<RoomListModel>> getFollowerRoomList({String startId = ''});

  /// 在线用户列表
  Future<FlatHttpResponse<OnlineUserListModel>?> getRoomUserListByPage(
      {required int page, int limit = 50, String? keyword});

  /// 设置管理员
  Future<FlatHttpResponse> setRoomAdmin({required String targetId});

  /// 取消管理员
  Future<FlatHttpResponse> removeRoomAdmin({required String targetId});

  /// 踢出房间
  Future<FlatHttpResponse> kickRoom({required String uid});

  /// 拉黑
  /// [forever] 永远
  Future<FlatHttpResponse> roomBlack({required String uid});

  /// 移除房间黑名单
  Future<FlatHttpResponse> removeRoomBlackList({required String uid});

  /// 查询房间黑名单
  Future<FlatHttpResponse<ListResp<RoomUserInfoModel>>> getRoomBlackList({int page = 0});

  /// 获取房间首页分类列表
  Future<RoomTypeTagList?> getRoomTypeTagList();

  /// 获取房间首页分类列表
  Future<RoomListModel?> getRoomListByType(
      {required String tagCode, String? tagId, String listVer = "", String size = "20", String startId = "0"});

  /// 获取房间列表动态推荐房间
  Future<RoomListModel?> getRecommendRoomList();

  /// 获取我创建的房间信息
  Future<FlatHttpResponse<RoomInfoModel?>> getMyRoomInfo();
  
  /// 获取我的房间信息缓存，减少请求
  Future<RoomInfoModel?> getMyRoomInfoCache();

  /// 获取随机房间名字
  Future<RoomNameListModel?> getRandomRoomName();

  /// 创建或者重开房间
  Future<FlatHttpResponse<RoomInfoModel>> createOrResetRoom(
      {required String mode, String? roomName, String? tagId, bool create = true, String? roomId});

  /// 获取推荐房间，进入停播房间时调用
  Future<List<RoomListItem>?> getRecommendRooms(String roomId, {String? size});

  /// 获取房间总人数
  int getTotalInRoom();

  /// 根据id搜索房间
  Future<RoomListItem?> searchRoomById({required String roomId, String? act});

  /// 刷新token
  Future<String?> refreshToken();

  /// 游戏房匹配
  Future<FlatHttpResponse<JoinRoomResp>> matchGame({required String gameType, String? fee, String? gameMode});

  Future<FlatHttpResponse<JoinRoomResp>> roomMatch({required String matchType});

  /// 初始化用户的黑名单列表
  Future<void> initBlackList();

  /// 获取用户的黑名单列表
  List<String> getUserBlackList();

  Future<FlatHttpResponse<TaskRewardsModel>> getNewHandReward();

  void addBlackList(String uid);

  void removeBlackList(String uid);

  bool containsBlackList(String uid);

  /// 获取在房间的时长
  int getInRoomTime();

  int getJoinRoomTime();

  /// 查询用户在房状态
  Future<UserLiveStatusModel?> getUserRoomStatus(String targetUid);

  /// 查询用户所在的房间
  Future<FlatHttpResponse<UserLiveStatusModel>> getUserInRoom(String targetUid);

  /// 更新用户在房状态
  void updateUserRoomStatus(UserLiveStatusModel model);

  /// 拉黑房间
  Future<FlatHttpResponse> blockRoom(String roomId);

  /// 取消拉黑房间
  Future<FlatHttpResponse> cancelBlockRoom(String roomId);

  /// 获取房间列表
  Future<RoomListModel?> getBlockRoomList({String size = "20", required String page});

  Future<List<RoomUserInfoModel>> getOnlineAdminUsers();

  Future<List<RoomUserInfoModel>> getAllAdminUsers();

  Future<FlatHttpResponse<RankingModel>> getRoomTotalRank();

  Future<FlatHttpResponse<RankingModel>> getRoomRankCoins();

  /// 房间等级
  Future<FlatHttpResponse<RoomLevelDetail>> getRoomLevelDetail(
      {required String roomId, required String action, bool? cache});

  Future<FlatHttpResponse<BonusModel>> getBonusInfo();

  Future<FlatHttpResponse<RoomCountryList>> getCountryList();

  Future<RoomListModel?> getCouRoomList({required String country, required int size, String? startId});

  /// 房间是否缩小化
  bool isRoomMinimum();

  void setRoomMinimum(bool minimum);

  /// 获取房间在线用户推荐列表
  Future<FlatHttpResponse<RoomRecoUsers>> getRoomRecoUsers({int page = 1, required String source, int? size});

  /// 房间视频
  // 动作 1 play， 2 pause， 3 seekTo
  Future<FlatHttpResponse?> sendRoomVideoStatus(
      {int type = 0,
      int action,
      String videoUrl,
      int second = 0,
      int duration = 0,
      List<RoomVideoItem>? list,
      String? title});

  Future<RoomVideoModel?> getRoomVideoStatus({bool fromCache});

  /// 获取视频详情
  Future<FlatHttpResponse<RoomVideoModel>> getFilmVideoDetail({required String itemId});

  void setFilmVideoDetail(RoomVideoModel model);

  void setFilmVideoSeconds(int seconds, int total);

  List<int> getFilmVideoSeconds();

  /// 更新cp相亲房当前步骤阶段
  void updateCpRoomStep(String step, {bool updateMic = true});

  /// 拉黑用户后同步用户在房状态
  void blockUserUpdateStatus({required String targetUid, required bool cancel});

  Future<MyRoomGoodIdInfo?> getMyAllRoomInfo({bool fromServer = false});

  /// 房间活动进度可选项列表
  Future<FlatHttpResponse<ActivityListResp>> activityProgressOption();

  /// 设置房间活动进度选项
  Future<FlatHttpResponse> setActivityProgressOption(String codeList);

  /// 房间正在进行中的活动列表
  Future<FlatHttpResponse<ActivityListResp>> getActivityProgressList();

  Future<CrossRoomPKRecordList?> getCrossRoomPKRecords();

  Future<CrossRoomPKRecord?> getCrossRoomPKRecord(String gameId);

  Future<FlatHttpResponse<RoomMemberTabConfig>> roomUserTabConfig(
      {String? act});

  /// 获取判罚配置
  Future<FlatHttpResponse<RoomPunishConfig>> punishConfig();

  /// 执行判罚
  /// [action] user->判罚用户 room->判罚房间
  /// [level] 1-5 针对用户
  /// 当action为room时,level传1表示不推荐房间
  /// [fuid] 被判罚用户
  Future<FlatHttpResponse<RoomPunishConfig>> doPunish({
    required RoomPunishType act,
    required String level,
    String? fuid,
  });

  /// 房间内禁言
  void punishUserSilent({required int silentTime});

  /// 释放房间禁言相关资源
  void releasePunishUserSilent();

  /// 房间内被超管禁言的时长
  int get punishUserSilentTimeRemaining;

  /// 当前用户是否被超管禁言中
  bool isPunishUserSilent();

  Future<FlatHttpResponse<BgMusicStyleModel>> musicStyles();

  void releaseBgMusic();

  Future<FlatHttpResponse<BgMusicPlayList>> musicStyleRandomList({
    required int styleId,
  });

  /// 获取房间排行榜
  /// [tab] day 日榜，week 周榜
  Future<FlatHttpResponse<RoomRankList>> getRoomRankList({String tab = 'day'});

  Future<FlatHttpResponse<RoomRankList>> getRoomGlobalRankList(
      {String rankType = 'charm', String subType = 'this_week'});

  /// 房间时长上报
  Future<FlatHttpResponse<RoomStayTimeModel>> roomStayTimeReport();

  /// 榜一大哥
  PbRoomUser? onlineTopOneUser;

  Future<FlatHttpResponse<EditRoomResult>> changeRoomMode({required String roomId, required String roomMode});

  Future<FlatHttpResponse<RoomCommonConfig>> commonConfig({
    required String roomId,
  });

  /// --------------start 房主激励任务相关
  bool get enableRoomRewardFeature;

  int get rewardAmountStage;

  int get curRewardTimeSec;

  int get totalRewardTimeRequireSec;

  int get curAmount;

  int get nextStageAmount;

  void onPartyDurationChange({required int cur, required int total});

  void onRewardAmountChange({required WeekRewardAmountInfo? amountInfo});

  /// 获取当前的内部状态日志
  String getRewardLog();

  /// --------------end 房主激励任务相关

  /// 房间签到领取奖励
  RoomSignInController get signInController;

  /// 房间粉丝服务
  RoomFansService get fansService;

  /// cocos游戏服务
  RoomCocosGameService get cocosGameService;

  Future<FlatHttpResponse<RoomGiftGalleryRankListModel>> giftWallRank();

  /// 确认房间活跃
  Future<FlatHttpResponse> keepAlive();

  /// 更新当前房间游戏最小化模式
  void updateRoomModeGameMiniStatus({required bool isMini});

  /// 获取房间模式列表
  Future<FlatHttpResponse<RoomModeList>> getRoomModeList();

  /// 获取可邀请用户列表
  Future<OnlineUserListModel?> getInviteListByPage({required int page, int limit = 20});

  /// 邀请上游戏位
  Future<FlatHttpResponse> posInvite(int pos, String uid);

  /// 记录邀请过的用户
  void logInviteUser(String uid);

  /// 获取该用户邀请 cd 时间
  /// 时间小于 0 则不需要等待
  int getUserInviteTime(String uid);

  /// 通过id/名字 模糊匹配搜索房间或者用户
  Future<FlatHttpResponse<SearchResult>> search({required String keyword, String? opt});

  /// 房间列表
  Future<FlatHttpResponse<RoomPartyListEntity>> roomListByPage({required String tab, int page = 1});

  /// following 房间列表
  Future<FlatHttpResponse<RoomPartyListEntity>> roomFollowingListByPage({int page = 1});

  /// 我的房间列表
  Future<FlatHttpResponse<MyRoomListEntity>> myRooms();

  /// 是否是家族成员
  Future<bool> isFamilyMember({bool refresh = false});
}
