import 'package:service/modules/live/room/model/room_info_model.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';

enum RoomEvent {
  /// 房间信息更新 [RoomInfoModel]
  roomInfoUpdate,

  /// 房间公告更新 [String]
  roomAnnounceUpdate,

  /// 房间tag更新 [RoomTagModel]
  roomTagUpdate,

  /// 成功进房事件 数据是 roomId [String]
  joinRoom,

  /// 退房事件 数据是 roomId [String]
  leaveRoom,

  /// 创建房间事件
  createRoom,

  /// 关闭间事件
  closeRoom,

  /// 退房前事件 数据是 roomId [String]
  beforeLeaveRoom,

  /// 更新在线人数 [int]
  updateOnLineNum,

  /// 更显房间在线用户 前15 [List<RoomUserInfoModel>]
  updateOnLineUsers,

  //有用户进房
  updateEnterRoomOnLineUsers,

  //有用户离开房
  updateLeaveRoomOnLineUsers,

  /// 更新房间用户信息 [RoomUserInfoModel]
  updateUserInfo,

  /// 房间上锁 [bool]
  roomLock,

  /// 邀请上麦 [RoomUserInfoModel]
  inviteMic,

  /// 踢下麦
  kickMic,

  /// 踢出房间
  kickRoom,

  /// ban 房间
  banRoom,

  /// 退出房间
  exitRoom,

  /// 融云退出事件 [String]
  imRoomLeave,

  /// 声网退出事件 [String]
  mediaLeave,

  /// 房间异常退出 room disconnect [String]
  roomDisconnect,

  /// 在房间并且未关注
  unfollowRoomInTimer,

  /// 闭麦
  closeMic,

  /// 在房间未上过麦
  unUpMicInRoomTimer,

  /// 在房间送礼引导
  sendGiftInRoomTimer,

  /// 榜单金币变化
  rankCoinsUpdate,

  /// 用户进入房间 [GiftDisplayMsg]
  enterRoom,

  /// 显示用户进场座驾 [String]
  showRoomCar,

  /// 房间等级变化 [int]
  roomLevelUpdate,

  /// 房间follow关注新增 [int]
  roomFollowerUpdate,

  ///房间follower关注人数
  followerTotalCount,

  /// 房间模式更新 [RoomInfoModel]
  roomModeUpdate,
  videoStatusUpdate,

  /// 收到pb自动上麦
  autoTakeMic,

  /// 房间任务引导
  roomTaskTimer,

  /// 才艺房邀请成为主持人 [RoomUserInfoModel]
  talentInviteHost,

  /// 才艺房邀请成为评委 [RoomUserInfoModel]
  talentInviteJudge,

  /// 才艺房邀请成为选手 [RoomUserInfoModel]
  talentInviteContestant,

  /// 房主激励数据变化
  roomOwnerReward,

  /// 签到奖励弹窗打开
  signInRewardOpen,

  /// 打开水果机弹窗
  openFruitGamePanel,

  /// 公屏Msg礼物墙点亮Try
  chatMsgGiftWallLightTry,

  /// 房间将在X秒后被关闭通知
  willCloseTips,

  /// 邀请游戏桌 [PbGameInviteNotice]
  inviteGamePos,

  openBcGame,
}

enum RoomMicSeatEvent {
  /// 麦序更新 数据是麦序信息 [List<MicSeatItem>]
  update,

  /// 游戏麦位更新
  sudMicUpdate,

  /// 其他房间麦序更新  [List<MicSeatItem>]
  updateOtherRoomMic,

  /// 音量事件 0.5秒间隔 内容有声音的uid数组 [List<String>]
  volumeIndication,

  /// 申请上麦
  apply,

  /// 已查看
  read,

  /// 申请后通过上麦
  afterApplyToTakeMic,

  /// 麦位模式变化 [String]
  micModeUpdate,

  ///取消申请上麦
  cancelApply,

  ///拒绝申请上麦
  rejectApply,

  //申请上麦数量更新
  applyNumUpdate,

  //开启礼物激励
  switchGiftRewardStatistics,

  //更新礼物激励当前值
  updateGiftRewardStatistics
}

/// 房间操作事件
enum RoomOperationEvent {
  /// 点击房间关闭按钮
  clickRoomCloseBtn,

  /// 显示创建房间引导
  showCreateRoomGuide,

  /// 打开房间页面
  openRoomPage,

  /// 点击关注或取消关注
  followRoom,

  /// 键盘显示状态变化，true显示，false隐藏
  keyboardChange,

  /// 关注全部房间
  followAllRoom,

  /// 关闭房间面板显示状态改变
  changeClosePanelStatus,

  /// 房间模式的游戏最小化切换
  roomModeGameMini,

  /// 开始最小化动画
  startGameMini,

  /// 房间可关注
  roomFollowAvailable,
}

/// 房间用户操作事件
enum RoomUserOptEvent {
  /// @用户
  atUser,

  /// 获取黑名单列表
  initBlackList,

  /// 欢迎
  welCome,

  /// 邀麦
  inviteMic,

  /// 同意上麦
  approveMic
}

/// 房间麦克风操作事件
enum RoomMicOptEvent {
  ///下麦
  leaveMic,

  ///上麦
  takeMic,

  /// 匹配房自动上麦
  autoUpMic,

  /// muteMic通知
  muteMic,

  /// 点击麦克风
  clickMuteMic,
  micStatusChange;
}

/// 房间礼物事件
enum RoomGiftEvent {
  /// 新增流光 [PbSendGiftNotice]
  addStreamer,

  /// 送礼成功 [SendGiftResp]
  sendSuccess,

  /// 全屏礼物特效 [GiftDisplayMsg]
  giftDisplay,

  /// 麦位礼物特效 [GiftDisplayMsg]
  giftMicDisplay,
  stopAll,
  initVap,

  ///礼物奖励
  giftAward,

  /// 免费礼物倒计时
  freeGiftCountDown,

  /// 免费礼物列表刷新
  freeGiftListRefresh,

  //免费礼物领取
  freeGiftAward,

  //概率礼物必中进度通知
  probabilityGiftProgress,

  //概率礼物抽中通知
  rewardProbabilityGift,

  /// 消费小红点
  refreshRedDot,

  /// 房间底部礼物按钮数量
  updateBottomBarGiftCount,

  /// 刷新礼物背包数量, [int] 发出，为<0 则从Api取
  refreshBackpackCount,
}

enum RoomSendMsgEvent { sendMsg }

enum RoomMusicEvent {
  /// 添加音乐到歌单
  updateLocalMusic,

  /// 播放状态改变
  playStatusUpdate,

  /// 播放的音乐改变
  playMusicUpdate,
}

enum RoomEffectEvent {
  /// 音效展示
  effectShow,

  /// 音效开始
  effectStart,

  /// 音效暂停
  effectFinish,
}

enum RoomMicStickerEvent { handleMsg }

enum RoomUserEvent {
  inRoomStatus,

  ///撤销管理员
  removeAdmin,

  ///设置管理员
  addAdmin,

  enterTheRoom,
}

/// 声网事件
enum MediaEvent {
  /// 链接状态 [String]
  connectStateChange,
  onError,
}

/// 房间列表首页事件
enum RoomHomeEvent {
  /// 改变下标
  changeIndex,

  /// 打点上报related列表显示事件
  relatedListShow,

  /// 打点上报explore列表显示事件
  exploreListShow,
}

/// 全服广播
enum FullScreenBroadcast {
  /// 礼物广播
  gift,

  /// 房间礼物奖励
  giftAward,

  /// 红包
  luckyBag,

  /// 爵位信息变化
  premiumUpdate,

  /// 自定义全服广播
  userCustom,

  /// 顶部全房飘屏礼物
  giftFloat,

  //抽中礼物飘屏
  gitFloatLottery,

  ///通用全服飘屏
  generalFloat,
}

enum RoomMantleGuideEvent {
  show,
}

enum RoomRecoUserEvent {
  finish,
}

enum RoomVideoEvent {
  changeVideoUrl,

  /// 视频刷新
  refreshVideoInfo,

  /// 关闭视频 [bool] true主动关闭，false被动关闭
  close,
}

/// 播放器内部的回调事件
enum RoomVideoPlayerEvent {
  /// 播放器初始化 [int] 视频总时长
  init,

  /// 播放器播放中 [List<int>] get(0)当前进度，get(1)总时长，get(2)volume
  playing,

  /// 播放器暂停 [List<int>] get(0)当前进度，get(1)总时长
  pause,

  /// 播放器结束 [int] 当前进度
  end,

  /// 播放器播放错误 [String] 错误提示
  error,

  /// 视频加载，初始化loading
  loading,

  /// setNewVideo 只给youtube使用
  setNewVideo,
}

/// ui控制器的事件
enum RoomVideoUiEvent {
  /// ui控制播放 [String] 操作uid
  play,

  /// ui控制暂停 [String] 操作uid
  pause,

  /// ui控制进度 [int]拖动的进度时长
  seekTo,

  /// ui控制音量 [int]音量 0-100
  setVolume,

  /// 上一个视频
  preVideo,

  /// 下一个视频
  nextVideo,

  /// 搜索
  search,

  /// reload
  reload
}

enum RoomTaskEvent { guideBubble }

enum RoomActivityProgressEvent { update }

/// 房间排行事件
enum RoomRankEvent {
  topChange, // 第一名变化事件
  topBannerChange, // 排行榜banner横幅
}

/// 房间粉丝团事件
enum RoomFansGroupEvent {
  infoUpdate, // 数据发生变化
  levelUp, // 等级提升
  drawTimesRedDot, // 获取到免费次数，显示小红点
  ///粉丝抽奖Tab
  fansDrawList,

  /// 打开粉丝团详情
  openFansDetail,
  freeDrawTimes, // 免费抽奖次数
  guideJoinFansDialog, // 引导加入粉丝团弹窗
  editedFansGroup, // 编辑粉丝团信息
}

enum RoomBlindDateEvent {
  stageChange,
}

enum CharmWealthEvent {
  charmLevelUp,
  wealthLevelUp,
}

enum RoomListEvent {
  subListRefresh,
  recommendListRefresh
}

enum RoomGameEmpireEvent {
  notice, // 游戏事件
  timeout, // 游戏超时，兜底策略
}

/// 房间游戏事件
enum RoomGameEvent {
  /// 关闭结算页面
  closeResult,

  /// 关闭背景音乐
  musicEnable,
}