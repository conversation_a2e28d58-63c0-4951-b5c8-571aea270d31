// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'join_room_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JoinRoomResp _$JoinRoomRespFromJson(Map<String, dynamic> json) => JoinRoomResp()
  ..roomId = dynamicToString(json['room_id'])
  ..name = dynamicToStringNick(json['room_name'])
  ..showId = dynamicToString(json['room_show'])
  ..showIdLevel = toInt(json['show_level'])
  ..roomOwner = dynamicToString(json['room_owner'])
  ..roomOwnerNickname = dynamicToString(json['room_owner_nickname'])
  ..roomOwnerAvatarCode = dynamicToString(json['room_owner_avatar_code'])
  ..roomOwnerAvatar = dynamicToString(json['room_owner_avatar'])
  ..roomTag = dynamicToString(json['room_tag'])
  ..roomTagId = dynamicToString(json['room_tag_id'])
  ..roomFollower = dynamicToString(json['room_followers'])
  ..micPermission = dynamicToString(json['microphone_permission'])
  ..password = dynamicToString(json['room_pass'])
  ..hasPw = toInt(json['has_room_pass'])
  ..isFollowed = toInt(json['is_followed'])
  ..hasFollow = toBool(json['has_follow'])
  ..userType = toInt(json['user_type'])
  ..gameList =
      (json['game_list'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..country = json['country'] as String?
  ..eventNum = toInt(json['event_num'])
  ..mode = json['live_mode'] as String?
  ..silentTimeRemaining = toInt(json['silent_time_remaining'])
  ..roomUserCount = toInt(json['room_user_count'])
  ..status = dynamicToString(json['room_status'])
  ..announce = dynamicToString(json['room_announce'])
  ..cover = dynamicToString(json['room_cover'])
  ..countryImg = dynamicToString(json['country_img'])
  ..welcome = dynamicToString(json['room_welcome'])
  ..roomTheme = json['room_theme'] == null
      ? null
      : RoomThemeModel.fromJson(json['room_theme'] as Map<String, dynamic>)
  ..tagImage = json['room_tag_img'] as String?
  ..timestamp = toInt(json['timestamp'])
  ..start = toBool(json['open'])
  ..showBlindbox = toBool(json['show_blindbox'])
  ..blackRoom = toInt(json['black_room'])
  ..level = toInt(json['level'])
  ..gameConfig = json['game_conf'] == null
      ? null
      : GameConfig.fromJson(json['game_conf'] as Map<String, dynamic>)
  ..startCalculator = toBool(json['start_calculator'])
  ..cpStep = dynamicToString(json['cp_step'])
  ..roomType = json['room_type'] as String?
  ..enterPermission = toInt(json['enter_permission'])
  ..micMode = dynamicToString(json['mic_mode'])
  ..micModeLevel = toInt(json['mic_mode_level'])
  ..rewardAmountConfig = json['reward_amount_config'] == null
      ? null
      : RewardAmountConfig.fromJson(
          json['reward_amount_config'] as Map<String, dynamic>)
  ..fansClubInfo = json['fans_club'] == null
      ? null
      : FansClubInfo.fromJson(json['fans_club'] as Map<String, dynamic>)
  ..fansClubShow =
      toFansClubShow(json['fans_club_show'] as Map<String, dynamic>?)
  ..openTurntable = toBool(json['open_turntable'])
  ..gameEmpireInfo = json['game_empire_info'] == null
      ? null
      : GameEmpireInfo.fromJson(
          json['game_empire_info'] as Map<String, dynamic>)
  ..gameCenterCfg = json['game_center_cfg'] == null
      ? null
      : GameCenterCfg.fromJson(json['game_center_cfg'] as Map<String, dynamic>)
  ..h5GameList =
      (json['h5_game_list'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..roomModeList = (json['room_mode_list'] as List<dynamic>?)
      ?.map((e) => RoomModeListItem.fromJson(e as Map<String, dynamic>))
      .toList()
  ..roomTagColor = dynamicToString(json['room_tag_color'])
  ..levelIcon = dynamicToString(json['level_icon'])
  ..officialLogo = json['room_label'] as String?
  ..isOfficial = toBool(json['is_official'])
  ..familyLuckyBag = json['family_lucky_bag'] == null
      ? null
      : FamilyLuckyBag.fromJson(
          json['family_lucky_bag'] as Map<String, dynamic>)
  ..agoraToken = dynamicToString(json['agora_rtc_token'])
  ..channelProfile = json['channel_profile'] as String?
  ..audioProfile = json['audio_profile'] as String?
  ..audioScenario = json['audio_scenario'] as String?;

Map<String, dynamic> _$JoinRoomRespToJson(JoinRoomResp instance) =>
    <String, dynamic>{
      'room_id': instance.roomId,
      'room_name': instance.name,
      'room_show': instance.showId,
      'show_level': instance.showIdLevel,
      'room_owner': instance.roomOwner,
      'room_owner_nickname': instance.roomOwnerNickname,
      'room_owner_avatar_code': instance.roomOwnerAvatarCode,
      'room_owner_avatar': instance.roomOwnerAvatar,
      'room_tag': instance.roomTag,
      'room_tag_id': instance.roomTagId,
      'room_followers': instance.roomFollower,
      'microphone_permission': instance.micPermission,
      'room_pass': instance.password,
      'has_room_pass': instance.hasPw,
      'is_followed': instance.isFollowed,
      'has_follow': instance.hasFollow,
      'user_type': instance.userType,
      'game_list': instance.gameList,
      'country': instance.country,
      'event_num': instance.eventNum,
      'live_mode': instance.mode,
      'silent_time_remaining': instance.silentTimeRemaining,
      'room_user_count': instance.roomUserCount,
      'room_status': instance.status,
      'room_announce': instance.announce,
      'room_cover': instance.cover,
      'country_img': instance.countryImg,
      'room_welcome': instance.welcome,
      'room_theme': instance.roomTheme,
      'room_tag_img': instance.tagImage,
      'timestamp': instance.timestamp,
      'open': instance.start,
      'show_blindbox': instance.showBlindbox,
      'black_room': instance.blackRoom,
      'level': instance.level,
      'game_conf': instance.gameConfig,
      'start_calculator': instance.startCalculator,
      'cp_step': instance.cpStep,
      'room_type': instance.roomType,
      'enter_permission': instance.enterPermission,
      'mic_mode': instance.micMode,
      'mic_mode_level': instance.micModeLevel,
      'reward_amount_config': instance.rewardAmountConfig,
      'fans_club': instance.fansClubInfo,
      'fans_club_show': fansClubShowToJson(instance.fansClubShow),
      'open_turntable': instance.openTurntable,
      'game_empire_info': instance.gameEmpireInfo,
      'game_center_cfg': instance.gameCenterCfg,
      'h5_game_list': instance.h5GameList,
      'room_mode_list': instance.roomModeList,
      'room_tag_color': instance.roomTagColor,
      'level_icon': instance.levelIcon,
      'room_label': instance.officialLogo,
      'is_official': instance.isOfficial,
      'family_lucky_bag': instance.familyLuckyBag,
      'agora_rtc_token': instance.agoraToken,
      'channel_profile': instance.channelProfile,
      'audio_profile': instance.audioProfile,
      'audio_scenario': instance.audioScenario,
    };
