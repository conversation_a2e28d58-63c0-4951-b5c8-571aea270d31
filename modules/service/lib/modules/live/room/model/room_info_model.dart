import 'package:json_annotation/json_annotation.dart';
import 'package:service/modules/finance/const/enums.dart';
import 'package:service/modules/live/room/const/const.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/model/room_mode_list_model.dart';
import 'package:service/modules/live/room/model/room_theme_model.dart';
import 'package:service/modules/live/room_fans/model/room_fans_model.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/pb/net/pb_room.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/value_parsers.dart';

import '../../../../pb/net/pb_push.pb.dart';
import '../../../family/model/family_lucky_bag_info_model.dart';
import '../../game/const/enums.dart';

part 'room_info_model.g.dart';

@JsonSerializable()
class RoomInfoModel {
  @JsonKey(name: 'room_id', fromJson: dynamicToString)
  String? roomId;
  @JsonKey(name: 'room_name', fromJson: dynamicToStringNick)
  String? name;
  @JsonKey(name: 'room_show', fromJson: dynamicToString)
  String? showId;

  /// showId等级，大于1显示特殊样式
  @JsonKey(name: 'show_level', fromJson: toInt)
  int? showIdLevel;

  /// 房主uid
  @JsonKey(name: 'room_owner', fromJson: dynamicToString)
  String? roomOwner;

  /// 房主nickname
  @JsonKey(name: 'room_owner_nickname', fromJson: dynamicToString)
  String? roomOwnerNickname;

  /// 房主avatar_code
  @JsonKey(name: 'room_owner_avatar_code', fromJson: dynamicToString)
  String? roomOwnerAvatarCode;

  /// 房主avatar
  @JsonKey(name: 'room_owner_avatar', fromJson: dynamicToString)
  String? roomOwnerAvatar;

  /// 房间标签
  @JsonKey(name: 'room_tag', fromJson: dynamicToString)
  String? roomTag;

  /// 房间标签id
  @JsonKey(name: 'room_tag_id', fromJson: dynamicToString)
  String? roomTagId;

  /// 房间关注数
  @JsonKey(name: 'room_followers', fromJson: dynamicToString)
  String? roomFollower;

  /// 麦位权限
  @JsonKey(name: 'microphone_permission', fromJson: dynamicToString)
  String? micPermission;

  /// 房间密码
  @JsonKey(name: 'room_pass', fromJson: dynamicToString)
  String? password;

  /// 是否有密码 0:无 1:有
  @JsonKey(name: 'has_room_pass', fromJson: toInt)
  int? hasPw;

  /// 是否关注 0:否 1:是
  @JsonKey(name: 'is_followed', fromJson: toInt)
  int? isFollowed;

  /// 是否关注过房主
  @JsonKey(name: 'has_follow', fromJson: toBool)
  bool? hasFollow;

  /// 用户类型 1房主 2管理员 3成员 5游客
  @JsonKey(name: 'user_type', fromJson: toInt)
  int? userType;

  @JsonKey(name: 'game_list')
  List<String>? gameList;

  /// 国家
  String? country;

  /// 未结束活动数量
  @JsonKey(name: 'event_num', fromJson: toInt)
  int? eventNum;

  /// 房间当前模式，确定展示形式
  /// ludo_game、ludo_game_tmp、chat、
  /// domino_game、domino_game_tmp、knife_game、knife_game_tmp
  @JsonKey(name: 'live_mode')
  String? mode;

  @JsonKey(name: 'silent_time_remaining', fromJson: toInt)
  int? silentTimeRemaining;



  /// 是否临时房间
  bool get isTempRoom => mode?.endsWith("_tmp") ?? false;

  /// 是否新手房
  bool get isNewHandRoom => mode == RoomMode.CHAT_NEWBIE;

  /// 是否语音房
  bool get isChatRoom => mode == RoomMode.CHAT || mode == RoomMode.CHAT_NEWBIE;

  /// 是否cp相亲房
  bool get isCpRoom => mode == RoomMode.CP;

  bool get isGameMode => checkGameMode(mode);

  /// 是否是真心话大冒险
  bool get isTruthDare => mode == RoomMode.TRUTH_DARE;

  /// 是否有幸运转盘
  bool get luckyWheel => gameList?.contains(RoomGameType.luckyWheel) == true;

  bool get isLudo => mode == RoomMode.LUDO_GAME;

  bool get isBlindDate => mode == RoomMode.BLIND_DATE;

  String get sid =>
      "$roomId-${accountService.currentUid()}-${((timestamp ?? 0) / 1000.0).round()}";

  /// 在线人数
  @JsonKey(name: 'room_user_count', fromJson: toInt)
  int? roomUserCount;

  /// 房间状态
  /// normal、close、ban
  @JsonKey(name: 'room_status', fromJson: dynamicToString)
  String? status;

  bool get isBan => status == 'ban';

  bool get isClose => status == 'close';

  bool get isOpen => status == 'normal';

  // room_announce: Welcome!This is Echo81047's room, room_background_id: 1.0,
  @JsonKey(name: 'room_announce', fromJson: dynamicToString)
  String? announce;

  // room_cover: https://res.echochat.live/echo/room/cover/sys/1.png,
  @JsonKey(name: 'room_cover', fromJson: dynamicToString)
  String? cover;

  @JsonKey(name: 'country_img', fromJson: dynamicToString)
  String? countryImg;

  // room_welcome: room_welcome,
  @JsonKey(name: 'room_welcome', fromJson: dynamicToString)
  String? welcome;

  /// 房间背景
  @JsonKey(name: 'room_theme')
  RoomThemeModel? roomTheme;

  @JsonKey(name: 'room_tag_img')
  String? tagImage;

  @JsonKey(name: 'timestamp', fromJson: toInt)
  int? timestamp;

  @JsonKey(name: 'open', fromJson: toBool)
  bool? start;

  @JsonKey(name :'show_blindbox',fromJson: toBool)
  bool? showBlindbox;

  @JsonKey(name: 'black_room', fromJson: toInt)
  int? blackRoom;

  /// 客户端自定义变量，是否是进入一个新房间，还是进入原本的房间
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool joinNewRoom = true;

  ///  客户端自定义变量，是否通过匹配进入房间
  @JsonKey(ignore: true)
  bool joinFromMatching = false;

  @JsonKey(fromJson: toInt)
  int? level;

  /// 房间游戏配置
  @JsonKey(name: 'game_conf')
  GameConfig? gameConfig;

  @JsonKey(name: 'start_calculator', fromJson: toBool)
  bool? startCalculator;

  /// cp相亲房的当前步骤：select/announce
  @JsonKey(name: 'cp_step', fromJson: dynamicToString)
  String? cpStep;

  /// 房间类型：normal/family
  @JsonKey(name: 'room_type')
  String? roomType;

  /// 进入房间限制 0: 所有人， 1 家族
  @JsonKey(name: 'enter_permission', fromJson: toInt)
  int? enterPermission;

  @JsonKey(name: 'mic_mode', fromJson: dynamicToString)
  String? micMode;

  @JsonKey(name: 'mic_mode_level', fromJson: toInt)
  int? micModeLevel;

  @JsonKey(name: 'reward_amount_config')
  RewardAmountConfig? rewardAmountConfig;

  @JsonKey(name: 'fans_club')
  FansClubInfo? fansClubInfo;

  @JsonKey(name: 'fans_club_show', fromJson: toFansClubShow, toJson: fansClubShowToJson)
  FansClubShow? fansClubShow;

  @JsonKey(name: 'open_turntable', fromJson: toBool)
  bool? openTurntable;

  @JsonKey(name: 'game_empire_info')
  GameEmpireInfo? gameEmpireInfo;

  @JsonKey(name: 'game_center_cfg')
  GameCenterCfg? gameCenterCfg;

  @JsonKey(name: 'h5_game_list')
  List<String>? h5GameList;

  @JsonKey(name: 'room_mode_list')
  List<RoomModeListItem>? roomModeList;

  /// 客户端自定义变量，是否最小化当前游戏
  @JsonKey(includeFromJson: false)
  bool gameMini = false;

  ///  客户端自定义变量，是否通过小窗口进入房间
  @JsonKey(ignore: true)
  bool joinFromMini = false;

  @JsonKey(name: "room_tag_color", fromJson: dynamicToString)
  String? roomTagColor;

  @JsonKey(name: "level_icon", fromJson: dynamicToString)
  String? levelIcon;

  /// 官方辨识
  @JsonKey(name: 'room_label')
  String? officialLogo;

  /// 是否官方号
  @JsonKey(name: "is_official", fromJson: toBool)
  bool? isOfficial;

  /// 家族红包数据，为空表示没有可领取的红包
  @JsonKey(name: "family_lucky_bag")
  FamilyLuckyBag? familyLuckyBag;

  /// 是否是家族房间房
  bool get isFamilyRoom => roomType == "family";

  /// 是否只允许家族成员进入
  bool get isOnlyFamilyCanEnter => enterPermission == 1;

  bool get isOwner => userType == RoomUserType.OWNER;

  bool get isAdmin => userType == RoomUserType.ADMINER;

  bool get isOwnerOrAdmin =>
      userType == RoomUserType.OWNER || userType == RoomUserType.ADMINER;

  bool get isQuickMode => gameConfig?.gameMode == LudoMode.quickly.value;

  RoomInfoModel();

  factory RoomInfoModel.fromJson(Map<String, dynamic> json) {
    return _$RoomInfoModelFromJson(json);
  }

  PbRoomInfo toPb() {
    return PbRoomInfo()
      ..name = name ?? ''
      ..id = roomId ?? ''
      ..roomShow = showId ?? ''
      ..mode = mode ?? ''
      ..status = status ?? ''
      ..cover = cover ?? ''
      ..liveMode = mode ?? '';
  }

  Map<String, dynamic> toJson() => _$RoomInfoModelToJson(this);

  @override
  String toString() {
    return 'RoomInfoModel{roomId: $roomId, name: $name, showId: $showId, mode: $mode, status: $status, startCalculator: $startCalculator}';
  }

  static String gameId(String? mode) {
    switch (mode) {
      case RoomMode.LUDO_GAME:
        return "100001";
      case RoomMode.DOMINO_GAME:
        return "100008";
      case RoomMode.CANDY_GAME:
        return "100010";
      case RoomMode.UNO_GAME:
        return "100020";
      default:
        return "";
    }
  }

  static bool checkGameMode(String? mode) {
    return mode == RoomMode.LUDO_GAME ||
        mode == RoomMode.DOMINO_GAME ||
        mode == RoomMode.CANDY_GAME ||
        mode == RoomMode.UNO_GAME;
  }

  bool merge({
    int? roomUserCount,
    FansClubInfo? fansClubInfo,
    FansClubShow? fansClubShow,
  }) {
    bool hasChanged = false;

    void checkAndUpdate<T>(
        T currentValue, T newValue, void Function(T) setter) {
      if (newValue != null && newValue != currentValue) {
        setter(newValue);
        hasChanged = true;
      }
    }

    checkAndUpdate(this.roomUserCount, roomUserCount, (v) => this.roomUserCount = v);
    checkAndUpdate(this.fansClubInfo, fansClubInfo, (v) => this.fansClubInfo = v);
    checkAndUpdate(this.fansClubShow, fansClubShow, (v) => this.fansClubShow = v);

    return hasChanged;
  }
}

@JsonSerializable()
class EditRoomResult {
  @JsonKey(name: 'mic_permission_level', fromJson: toInt)
  int? micPermissionLevel;

  EditRoomResult();

  factory EditRoomResult.fromJson(Map<String, dynamic> json) {
    return _$EditRoomResultFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EditRoomResultToJson(this);
}

@JsonSerializable()
class GameConfig {
  @JsonKey(fromJson: toInt)
  int? id;

  @JsonKey(fromJson: toInt)
  int? cost;

  @JsonKey(fromJson: toInt, name: "total_get")
  int? total;

  @JsonKey(name: "currency_type")
  String? priceType;

  @JsonKey(fromJson: toInt, name: "player_num")
  int? playerNum;

  @JsonKey(fromJson: toInt, name: "game_mode")
  int? gameMode;

  CurrencyType get currencyType {
    switch (priceType) {
      case PriceType.DIAMOND:
        return CurrencyType.diamond;
      case PriceType.INTEGRAL:
        return CurrencyType.integral;
      default:
        return CurrencyType.goldCoin;
    }
  }

  GameConfig();

  factory GameConfig.fromJson(Map<String, dynamic> json) {
    return _$GameConfigFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GameConfigToJson(this);

  factory GameConfig.fromPb(PbGameConfig config) {
    final model = GameConfig()
      ..cost = config.cost
      ..playerNum = config.playerNum
      ..total = config.totalGet
      ..priceType = config.currencyType
      ..gameMode = config.gameMode;
    return model;
  }
}

@JsonSerializable()
class RewardAmountConfig {
  @JsonKey(name: "stage", fromJson: toInt)
  int stage;

  @JsonKey(name: "party_duration_info")
  PartyDurInfo? partyDurInfo;
  @JsonKey(name: "reward_amount_info")
  RewardAmountInfo? rewardAmountInfo;

  RewardAmountConfig({
    this.stage = 0,
    this.partyDurInfo,
    this.rewardAmountInfo,
  });

  factory RewardAmountConfig.fromJson(Map<String, dynamic> json) {
    return _$RewardAmountConfigFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RewardAmountConfigToJson(this);
}

@JsonSerializable()
class PartyDurInfo {
  @JsonKey(name: "time_cur", fromJson: toInt)
  int timeCur;
  @JsonKey(name: "time_require", fromJson: toInt)
  int timeRequire;

  PartyDurInfo({
    this.timeCur = 0,
    this.timeRequire = 0,
  });

  factory PartyDurInfo.fromJson(Map<String, dynamic> json) {
    return _$PartyDurInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PartyDurInfoToJson(this);
}

@JsonSerializable()
class RewardAmountInfo {
  @JsonKey(name: "next_stage_amount", fromJson: toInt)
  int nextStageAmount;
  @JsonKey(name: "cur_amount", fromJson: toInt)
  int curAmount;

  RewardAmountInfo({
    this.nextStageAmount = 0,
    this.curAmount = 0,
  });

  factory RewardAmountInfo.fromJson(Map<String, dynamic> json) {
    return _$RewardAmountInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RewardAmountInfoToJson(this);
}

@JsonSerializable()
class FansClubInfo {
  @JsonKey(name: "id", fromJson: dynamicToString)
  String id;
  @JsonKey(name: "has_join", fromJson: toBool)
  bool hasJoin;
  @JsonKey(name: "level", fromJson: toInt)
  int level;

  FansClubInfo({
    this.id = '',
    this.hasJoin = false,
    this.level = -1,
  });

  factory FansClubInfo.fromJson(Map<String, dynamic> json) {
    return _$FansClubInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$FansClubInfoToJson(this);
}

@JsonSerializable()
class GameEmpireInfo {
  @JsonKey(name: "pk_id", fromJson: toInt)
  int pkId;

  @JsonKey(name: "camp", fromJson: toInt)
  int camp;

  @JsonKey(name: "camp1_uid", fromJson: dynamicToString)
  String camp1Uid;

  @JsonKey(name: "camp2_uid", fromJson: dynamicToString)
  String camp2Uid;

  @JsonKey(name: "end_time", fromJson: toInt)
  int endTime;

  GameEmpireInfo({
    this.pkId = 0,
    this.camp = 0,
    this.camp1Uid = '',
    this.camp2Uid = '',
    this.endTime = 0,
  });

  bool isValid() {
    return pkId > 0 && camp > 0 && camp1Uid.isNotEmpty && camp2Uid.isNotEmpty;
  }

  factory GameEmpireInfo.fromJson(Map<String, dynamic> json) {
    return _$GameEmpireInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GameEmpireInfoToJson(this);
}

@JsonSerializable()
class GameCenterCfg {
  @JsonKey(name: "is_open", fromJson: toBool)
  bool isOpen;

  @JsonKey(name: "deeplink", fromJson: dynamicToString)
  String deeplink;

  GameCenterCfg({
    this.isOpen = false,
    this.deeplink = '',
  });

  factory GameCenterCfg.fromJson(Map<String, dynamic> json) {
    return _$GameCenterCfgFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GameCenterCfgToJson(this);
}
