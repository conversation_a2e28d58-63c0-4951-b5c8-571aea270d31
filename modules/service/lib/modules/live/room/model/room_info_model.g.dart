// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoomInfoModel _$RoomInfoModelFromJson(Map<String, dynamic> json) =>
    RoomInfoModel()
      ..roomId = dynamicToString(json['room_id'])
      ..name = dynamicToStringNick(json['room_name'])
      ..showId = dynamicToString(json['room_show'])
      ..showIdLevel = toInt(json['show_level'])
      ..roomOwner = dynamicToString(json['room_owner'])
      ..roomOwnerNickname = dynamicToString(json['room_owner_nickname'])
      ..roomOwnerAvatarCode = dynamicToString(json['room_owner_avatar_code'])
      ..roomOwnerAvatar = dynamicToString(json['room_owner_avatar'])
      ..roomTag = dynamicToString(json['room_tag'])
      ..roomTagId = dynamicToString(json['room_tag_id'])
      ..roomFollower = dynamicToString(json['room_followers'])
      ..micPermission = dynamicToString(json['microphone_permission'])
      ..password = dynamicToString(json['room_pass'])
      ..hasPw = toInt(json['has_room_pass'])
      ..isFollowed = toInt(json['is_followed'])
      ..hasFollow = toBool(json['has_follow'])
      ..userType = toInt(json['user_type'])
      ..gameList = (json['game_list'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..country = json['country'] as String?
      ..eventNum = toInt(json['event_num'])
      ..mode = json['live_mode'] as String?
      ..silentTimeRemaining = toInt(json['silent_time_remaining'])
      ..roomUserCount = toInt(json['room_user_count'])
      ..status = dynamicToString(json['room_status'])
      ..announce = dynamicToString(json['room_announce'])
      ..cover = dynamicToString(json['room_cover'])
      ..countryImg = dynamicToString(json['country_img'])
      ..welcome = dynamicToString(json['room_welcome'])
      ..roomTheme = json['room_theme'] == null
          ? null
          : RoomThemeModel.fromJson(json['room_theme'] as Map<String, dynamic>)
      ..tagImage = json['room_tag_img'] as String?
      ..timestamp = toInt(json['timestamp'])
      ..start = toBool(json['open'])
      ..showBlindbox = toBool(json['show_blindbox'])
      ..blackRoom = toInt(json['black_room'])
      ..level = toInt(json['level'])
      ..gameConfig = json['game_conf'] == null
          ? null
          : GameConfig.fromJson(json['game_conf'] as Map<String, dynamic>)
      ..startCalculator = toBool(json['start_calculator'])
      ..cpStep = dynamicToString(json['cp_step'])
      ..roomType = json['room_type'] as String?
      ..enterPermission = toInt(json['enter_permission'])
      ..micMode = dynamicToString(json['mic_mode'])
      ..micModeLevel = toInt(json['mic_mode_level'])
      ..rewardAmountConfig = json['reward_amount_config'] == null
          ? null
          : RewardAmountConfig.fromJson(
              json['reward_amount_config'] as Map<String, dynamic>)
      ..fansClubInfo = json['fans_club'] == null
          ? null
          : FansClubInfo.fromJson(json['fans_club'] as Map<String, dynamic>)
      ..fansClubShow =
          toFansClubShow(json['fans_club_show'] as Map<String, dynamic>?)
      ..openTurntable = toBool(json['open_turntable'])
      ..gameEmpireInfo = json['game_empire_info'] == null
          ? null
          : GameEmpireInfo.fromJson(
              json['game_empire_info'] as Map<String, dynamic>)
      ..gameCenterCfg = json['game_center_cfg'] == null
          ? null
          : GameCenterCfg.fromJson(
              json['game_center_cfg'] as Map<String, dynamic>)
      ..h5GameList = (json['h5_game_list'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..roomModeList = (json['room_mode_list'] as List<dynamic>?)
          ?.map((e) => RoomModeListItem.fromJson(e as Map<String, dynamic>))
          .toList()
      ..roomTagColor = dynamicToString(json['room_tag_color'])
      ..levelIcon = dynamicToString(json['level_icon'])
      ..officialLogo = json['room_label'] as String?
      ..isOfficial = toBool(json['is_official'])
      ..familyLuckyBag = json['family_lucky_bag'] == null
          ? null
          : FamilyLuckyBag.fromJson(
              json['family_lucky_bag'] as Map<String, dynamic>);

Map<String, dynamic> _$RoomInfoModelToJson(RoomInfoModel instance) =>
    <String, dynamic>{
      'room_id': instance.roomId,
      'room_name': instance.name,
      'room_show': instance.showId,
      'show_level': instance.showIdLevel,
      'room_owner': instance.roomOwner,
      'room_owner_nickname': instance.roomOwnerNickname,
      'room_owner_avatar_code': instance.roomOwnerAvatarCode,
      'room_owner_avatar': instance.roomOwnerAvatar,
      'room_tag': instance.roomTag,
      'room_tag_id': instance.roomTagId,
      'room_followers': instance.roomFollower,
      'microphone_permission': instance.micPermission,
      'room_pass': instance.password,
      'has_room_pass': instance.hasPw,
      'is_followed': instance.isFollowed,
      'has_follow': instance.hasFollow,
      'user_type': instance.userType,
      'game_list': instance.gameList,
      'country': instance.country,
      'event_num': instance.eventNum,
      'live_mode': instance.mode,
      'silent_time_remaining': instance.silentTimeRemaining,
      'room_user_count': instance.roomUserCount,
      'room_status': instance.status,
      'room_announce': instance.announce,
      'room_cover': instance.cover,
      'country_img': instance.countryImg,
      'room_welcome': instance.welcome,
      'room_theme': instance.roomTheme,
      'room_tag_img': instance.tagImage,
      'timestamp': instance.timestamp,
      'open': instance.start,
      'show_blindbox': instance.showBlindbox,
      'black_room': instance.blackRoom,
      'level': instance.level,
      'game_conf': instance.gameConfig,
      'start_calculator': instance.startCalculator,
      'cp_step': instance.cpStep,
      'room_type': instance.roomType,
      'enter_permission': instance.enterPermission,
      'mic_mode': instance.micMode,
      'mic_mode_level': instance.micModeLevel,
      'reward_amount_config': instance.rewardAmountConfig,
      'fans_club': instance.fansClubInfo,
      'fans_club_show': fansClubShowToJson(instance.fansClubShow),
      'open_turntable': instance.openTurntable,
      'game_empire_info': instance.gameEmpireInfo,
      'game_center_cfg': instance.gameCenterCfg,
      'h5_game_list': instance.h5GameList,
      'room_mode_list': instance.roomModeList,
      'room_tag_color': instance.roomTagColor,
      'level_icon': instance.levelIcon,
      'room_label': instance.officialLogo,
      'is_official': instance.isOfficial,
      'family_lucky_bag': instance.familyLuckyBag,
    };

EditRoomResult _$EditRoomResultFromJson(Map<String, dynamic> json) =>
    EditRoomResult()..micPermissionLevel = toInt(json['mic_permission_level']);

Map<String, dynamic> _$EditRoomResultToJson(EditRoomResult instance) =>
    <String, dynamic>{
      'mic_permission_level': instance.micPermissionLevel,
    };

GameConfig _$GameConfigFromJson(Map<String, dynamic> json) => GameConfig()
  ..id = toInt(json['id'])
  ..cost = toInt(json['cost'])
  ..total = toInt(json['total_get'])
  ..priceType = json['currency_type'] as String?
  ..playerNum = toInt(json['player_num'])
  ..gameMode = toInt(json['game_mode']);

Map<String, dynamic> _$GameConfigToJson(GameConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cost': instance.cost,
      'total_get': instance.total,
      'currency_type': instance.priceType,
      'player_num': instance.playerNum,
      'game_mode': instance.gameMode,
    };

RewardAmountConfig _$RewardAmountConfigFromJson(Map<String, dynamic> json) =>
    RewardAmountConfig(
      stage: json['stage'] == null ? 0 : toInt(json['stage']),
      partyDurInfo: json['party_duration_info'] == null
          ? null
          : PartyDurInfo.fromJson(
              json['party_duration_info'] as Map<String, dynamic>),
      rewardAmountInfo: json['reward_amount_info'] == null
          ? null
          : RewardAmountInfo.fromJson(
              json['reward_amount_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RewardAmountConfigToJson(RewardAmountConfig instance) =>
    <String, dynamic>{
      'stage': instance.stage,
      'party_duration_info': instance.partyDurInfo,
      'reward_amount_info': instance.rewardAmountInfo,
    };

PartyDurInfo _$PartyDurInfoFromJson(Map<String, dynamic> json) => PartyDurInfo(
      timeCur: json['time_cur'] == null ? 0 : toInt(json['time_cur']),
      timeRequire:
          json['time_require'] == null ? 0 : toInt(json['time_require']),
    );

Map<String, dynamic> _$PartyDurInfoToJson(PartyDurInfo instance) =>
    <String, dynamic>{
      'time_cur': instance.timeCur,
      'time_require': instance.timeRequire,
    };

RewardAmountInfo _$RewardAmountInfoFromJson(Map<String, dynamic> json) =>
    RewardAmountInfo(
      nextStageAmount: json['next_stage_amount'] == null
          ? 0
          : toInt(json['next_stage_amount']),
      curAmount: json['cur_amount'] == null ? 0 : toInt(json['cur_amount']),
    );

Map<String, dynamic> _$RewardAmountInfoToJson(RewardAmountInfo instance) =>
    <String, dynamic>{
      'next_stage_amount': instance.nextStageAmount,
      'cur_amount': instance.curAmount,
    };

FansClubInfo _$FansClubInfoFromJson(Map<String, dynamic> json) => FansClubInfo(
      id: json['id'] == null ? '' : dynamicToString(json['id']),
      hasJoin: json['has_join'] == null ? false : toBool(json['has_join']),
      level: json['level'] == null ? -1 : toInt(json['level']),
    );

Map<String, dynamic> _$FansClubInfoToJson(FansClubInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'has_join': instance.hasJoin,
      'level': instance.level,
    };

GameEmpireInfo _$GameEmpireInfoFromJson(Map<String, dynamic> json) =>
    GameEmpireInfo(
      pkId: json['pk_id'] == null ? 0 : toInt(json['pk_id']),
      camp: json['camp'] == null ? 0 : toInt(json['camp']),
      camp1Uid:
          json['camp1_uid'] == null ? '' : dynamicToString(json['camp1_uid']),
      camp2Uid:
          json['camp2_uid'] == null ? '' : dynamicToString(json['camp2_uid']),
      endTime: json['end_time'] == null ? 0 : toInt(json['end_time']),
    );

Map<String, dynamic> _$GameEmpireInfoToJson(GameEmpireInfo instance) =>
    <String, dynamic>{
      'pk_id': instance.pkId,
      'camp': instance.camp,
      'camp1_uid': instance.camp1Uid,
      'camp2_uid': instance.camp2Uid,
      'end_time': instance.endTime,
    };

GameCenterCfg _$GameCenterCfgFromJson(Map<String, dynamic> json) =>
    GameCenterCfg(
      isOpen: json['is_open'] == null ? false : toBool(json['is_open']),
      deeplink:
          json['deeplink'] == null ? '' : dynamicToString(json['deeplink']),
    );

Map<String, dynamic> _$GameCenterCfgToJson(GameCenterCfg instance) =>
    <String, dynamic>{
      'is_open': instance.isOpen,
      'deeplink': instance.deeplink,
    };
