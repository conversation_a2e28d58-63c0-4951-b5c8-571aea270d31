import 'dart:async';

import 'package:service/common/statistics/msg_list_statistics.g.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/account/account_service.dart';
import 'package:service/modules/live/api/live_api.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/notification/handler/i_notification_handler.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';

import '../abs_room_service.dart';
import '../model/online_user_list_model.dart';
import '../model/user_live_status.dart';

mixin RoomUserMixin on AbsRoomService {
  final _tag = "RoomUserMixin";

  int _updateTime = 0;

  final _timeOutMilliseconds = 300000;

  /// 各个房间用户信息缓存
  final _roomUserCache =
      <String, LruMap<String, Map<int, RoomUserInfoModel>>>{};

  late _RoomUsersUpdateHandler _roomUserHandler;

  /// 当前房间用户列表 只显示15个
  List<RoomUserInfoModel>? _roomUserList;

  int _totalInRoom = 0;

  final Preferences _accountPref = Preferences.newInstance('account');

  /// 在房状态
  final LruMap<String, UserLiveStatusModel> _statusCache = LruMap(500);
  Set<String> listUidStatusToReq = {};
  bool _statusReqComplete = true;

  /// 当前的房间用户信息是否正在请求中
  final _roomUserInfoRequesting = <String>{};

  Future<List<RoomUserInfoModel>?> getRoomUserList() async {
    if (_roomUserList != null) return _roomUserList;
    await fetchUserList();
    return _roomUserList;
  }

  Future<List<RoomUserInfoModel>?> fetchUserList() async {
    final roomId = roomService.getCurrentRoomId();
    if (roomId?.isNotEmpty == true) {
      final resp = await liveApi.getRoomUserList(roomId: roomId!);

      if (resp.isSuccess) {
        micSeatService.setCurrentMicSeat(
            resp.data?.timestamp ?? 0, resp.data?.micUser,
            otherMicSeat: resp.data?.otherMicList);
        _updateOnlineUser(resp.data?.timestamp ?? 0, resp.data?.roomUser,
            resp.data?.total ?? 0);
      }
    }
    return null;
  }

  Future<FlatHttpResponse<RoomUserInfoModel>> getRoomUserCard(String uid,
      {bool forceUpdate = false, bool needRefresh = true}) async {
    String? roomId = roomService.getCurrentRoomId();
    if (roomId?.isNotEmpty == true) {
      final requestKey = "$roomId-$uid";
      if (!forceUpdate) {
        /// 该房间用户信息正在请求中，等待1s后再取缓存，避免重复请求
        if (_roomUserInfoRequesting.contains(requestKey)) {
          Log.d(_tag, "Wait for room user req end: $requestKey");
          await Future.delayed(Duration(seconds: 1));
        }

        final cache = _getUserCache(roomId!, uid, needFresh: needRefresh);
        if (cache != null) {
          return FlatHttpResponse(1, null, data: cache);
        }
      }

      Log.d(_tag, "Post getRoomUser req: $requestKey");
      _roomUserInfoRequesting.add(requestKey);

      final resp = await liveApi.getRoomUser(
        roomId: roomId!,
        uid: uid,
        clubId: roomService.fansService.getFansClubInfo()?.id,
      );
      if (resp.isSuccess && resp.data != null) {
        _setCache(roomId, [resp.data!]);
      }

      _roomUserInfoRequesting.remove(requestKey);

      return resp;
    }

    return FlatHttpResponse(-1, LocaleStrings.instance.defaultError);
  }

  void updateRoomUser(RoomUserInfoModel roomUser) {
    if (roomService.isInLive()) {
      String roomId = roomService.getCurrentRoomId()!;
      _setCache(roomId, [roomUser]);

      rxUtil.send(RoomEvent.updateUserInfo, roomUser);

      ///更新观众席信息
      final list = _roomUserList;
      if (list != null) {
        for (int index = 0; index < list.length; index++) {
          if (list[index].uid == roomUser.uid) {
            list[index] = roomUser;
            break;
          }
        }
      }
    }
  }

  void setup() {
    _roomUserHandler = _RoomUsersUpdateHandler(_onRoomUsersUpdate);
    notificationService.addHandler(_roomUserHandler);
  }

  void onJoinRoom(String roomId) {
    final uid = accountService.getAccountInfo()?.uid;
    if (uid != null) {
      getRoomUserCard(uid, forceUpdate: true);
    }

  }

  void cleanUser() {
    _roomUserList = null;
    _roomUserCache.clear();
    _totalInRoom = 0;
  }

  void _onRoomUsersUpdate(int time, PbRoomUsersUpdateNotice notice) {
    final list = <RoomUserInfoModel>[];
    for (var element in notice.user) {
      list.add(RoomUserInfoModel.fromPb(element));
    }
    _updateOnlineUser(time, list, notice.total.toInt());
  }

  void _updateOnlineUser(
      int time, List<RoomUserInfoModel>? onLineUser, int total) {
    if (time > _updateTime) {
      _updateTime = time;
      _totalInRoom = total.toInt();
      rxUtil.send(RoomEvent.updateOnLineNum, _totalInRoom);
      _roomUserList = onLineUser;
      rxUtil.send(RoomEvent.updateOnLineUsers, _roomUserList);

      /// 设置缓存
      final roomId = roomService.getCurrentRoomId();
      if (roomId != null) {
        _setCache(roomId, onLineUser);
      }
    }
  }

  void _setCache(String roomId, List<RoomUserInfoModel>? users) {
    if (users?.isNotEmpty == true) {
      int time = DateTime.now().millisecondsSinceEpoch;
      var lruMap = _roomUserCache[roomId];
      if (lruMap == null) {
        lruMap = LruMap<String, Map<int, RoomUserInfoModel>>(500);
        _roomUserCache[roomId] = lruMap;
      }
      for (var item in users!) {
        if (item.uid?.isNotEmpty == true) {
          lruMap.put(item.uid!, {time: item});
        }
      }
    }
  }

  /// 获取用户信息缓存
  /// [needFresh] 需要刷新 5分钟
  RoomUserInfoModel? _getUserCache(String roomId, String uid,
      {bool needFresh = true}) {
    final data = _roomUserCache[roomId]?.get(uid);
    if (data != null && data.keys.isNotEmpty) {
      if (needFresh) {
        int current = DateTime.now().millisecondsSinceEpoch;
        if ((current - data.keys.first) < _timeOutMilliseconds) {
          return data.values.first;
        }
      } else {
        return data.values.first;
      }
    }
    return null;
  }

  /// 获取房间在线用户列表
  /// [page] 页数
  /// [limit]
  @override
  Future<FlatHttpResponse<OnlineUserListModel>?> getRoomUserListByPage(
      {required int page, int limit = 50, String? keyword}) async {
    final roomId = roomService.getCurrentRoomId();
    var clubId = roomService.fansService.getFansClubInfo()?.id;
    if (roomId?.isNotEmpty == true) {
      final resp = await liveApi.getRoomUserListByPage(
        keyword: keyword,
        roomId: roomId!,
        page: page.toString(),
        limit: limit.toString(),
        clubId: clubId,
      );
      return resp;
    }
    return null;
  }

  @override
  Future<List<RoomUserInfoModel>> getOnlineAdminUsers() async {
    final roomId = roomService.getCurrentRoomId();
    if (roomId?.isEmpty ?? true) return [];
    final resp = await liveApi.getOnlineAdminUsers(roomId: roomId!);
    return resp.data?.roomUser ?? [];
  }

  @override
  Future<List<RoomUserInfoModel>> getAllAdminUsers() async {
    final roomId = roomService.getCurrentRoomId();
    if (roomId?.isEmpty ?? true) return [];
    final resp = await liveApi.getAllAdminUsers(roomId: roomId!);
    return resp.data?.roomUser ?? [];
  }

  @override
  Future<FlatHttpResponse> setRoomAdmin({required String targetId}) async {
    final roomId = roomService.getCurrentRoomId();
    return await liveApi.setRoomAdmin(roomId: roomId ?? "", fuid: targetId);
  }

  @override
  Future<FlatHttpResponse> removeRoomAdmin({required String targetId}) async {
    final roomId = roomService.getCurrentRoomId();
    return await liveApi.removeRoomAdmin(roomId: roomId ?? "", fuid: targetId);
  }

  @override
  int getTotalInRoom() {
    return _totalInRoom;
  }

  @override
  Future<FlatHttpResponse<UserLiveStatusModel>> getUserInRoom(
      String targetUid) async {
    final resp = await liveApi.getUserInRoom(targetUid: targetUid);
    return resp;
  }

  @override
  Future<UserLiveStatusModel?> getUserRoomStatus(String targetUid) async {
    UserLiveStatusModel? status = _statusCache[targetUid];
    if (status?.isExpired ?? true) {
      _updateUserStatus([targetUid]);
    }
    return status;
  }

  void _updateUserStatus(List<String> uidList) async {
    listUidStatusToReq.addAll(uidList);
    if (!_statusReqComplete || listUidStatusToReq.isEmpty) return;

    _statusReqComplete = false;
    await Future.delayed(Duration(milliseconds: 2000), () async {
      var reqList = <String>[];

      reqList = listUidStatusToReq.toList().sublist(
          0, listUidStatusToReq.length > 100 ? 100 : listUidStatusToReq.length);

      if (reqList.isNotEmpty) {
        listUidStatusToReq.removeAll(reqList);
        final resp =
            await liveApi.getUserRoomStatus(uidList: reqList.join(','));
        final list = resp.data?.list;
        if (list != null) {
          for (final status in list) {
            updateUserRoomStatus(status);
          }
        }
      }
    }).then((value) => _statusReqComplete = true);

    /// 如果列表中还有带请求，则继续
    if (listUidStatusToReq.isNotEmpty && _statusReqComplete) {
      _updateUserStatus([]);
    }
  }

  /// 更新用户状态
  @override
  void updateUserRoomStatus(UserLiveStatusModel status) {
    _statusCache[status.uid ?? ""] = status;
    rxUtil.send(RoomUserEvent.inRoomStatus, status);
  }

  @override
  Future<FlatHttpResponse<RoomRecoUsers>> getRoomRecoUsers(
      {int page = 1, required String source, int? size}) async {
    var user = await userService.getCurrentUserInfo();
    if (user == null && accountService.hadLogin()) {
      user = await userService.getUserInfo(
          accountService.getAccountInfo()?.uid ?? "",
          fromServer: true);
    }
    var sex = user?.sexStr ??
        (_accountPref.getString(AccountService.kGender) == "m"
            ? sexMale
            : sexFemale);
    DateTime startTime = DateTime.now();
    final resp = await liveApi.getRoomRecoUsers(
        page: '$page', source: source, sex: sex, size: '${size ?? 20}');

    String endTime =
        DateTime.now().difference(startTime).inMilliseconds.toString();

    MsgListStatistics.reportUserGuideRequestEnd(
        act: source,
        onTime: endTime,
        result: resp.isSuccess ? "succ" : "fail",
        reason: '${resp.code}');
    return resp;
  }

  @override
  void blockUserUpdateStatus(
      {required String targetUid, required bool cancel}) {
    if (cancel) {
      _statusCache.remove(targetUid);
      getUserRoomStatus(targetUid);
    } else {
      updateUserRoomStatus(UserLiveStatusModel()
        ..uid = targetUid
        ..status = false);
    }
  }

}

class _RoomUsersUpdateHandler implements INotificationHandler {
  final void Function(int time, PbRoomUsersUpdateNotice notice)? callback;

  _RoomUsersUpdateHandler(this.callback);

  @override
  PbPushEvent event() {
    return PbPushEvent.PbPushEvent_ROOM_USERS_UPDATE;
  }

  @override
  void onHandle(PbBizPush push, List<int> data, bool isPush,
      {Map<String, dynamic>? extra}){
    PbRoomUsersUpdateNotice notice = PbRoomUsersUpdateNotice.fromBuffer(data);
    int time = push.timestamp.toInt();
    callback?.call(time, notice);
  }
}