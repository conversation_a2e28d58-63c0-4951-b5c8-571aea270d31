import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';

import '../abs_room_service.dart';
import '../event/event.dart';

mixin RoomRewardAmountMixin on AbsRoomService {
  bool enableRoomRewardFeature = false;

  int rewardAmountStage = 0;

  int curRewardTimeSec = 0;
  int totalRewardTimeRequireSec = 0;

  int curAmount = 0;
  int nextStageAmount = 0;

  void initRewardAmountConfig() {
    _reset();
    enableRoomRewardFeature = remoteConfigService.config.roomOwnerRewardIncentiveSwitch;
    if (enableRoomRewardFeature) {
      var rewardConfig = getCurrentRoom()!.rewardAmountConfig;
      rewardAmountStage = rewardConfig?.stage ?? 0;
      if (rewardConfig?.partyDurInfo != null) {
        curRewardTimeSec = rewardConfig!.partyDurInfo!.timeCur;
        totalRewardTimeRequireSec = rewardConfig.partyDurInfo!.timeRequire;
      }
      if (rewardConfig?.rewardAmountInfo != null) {
        curAmount = rewardConfig!.rewardAmountInfo!.curAmount;
        nextStageAmount = rewardConfig.rewardAmountInfo!.nextStageAmount;
      }
    }
    rxUtil.send(RoomEvent.roomOwnerReward, 1);
  }

  void onPartyDurationChange({required int cur, required int total}) {
    curRewardTimeSec = cur;
    totalRewardTimeRequireSec = total;
    rxUtil.send(RoomEvent.roomOwnerReward, 2);
  }

  void onRewardAmountChange({required WeekRewardAmountInfo? amountInfo}) {
    if (amountInfo?.needShow == true) {
      rewardAmountStage = 2;
      curAmount = amountInfo!.curAmount;
      nextStageAmount = amountInfo.nextStageAmount;
      rxUtil.send(RoomEvent.roomOwnerReward, 3);
    }
  }

  String getRewardLog() {
    return 'enableRoomRewardFeature=$enableRoomRewardFeature,rewardAmountStage:$rewardAmountStage, curRewardTimeSec:$curRewardTimeSec, totalRewardTimeRequireSec:$totalRewardTimeRequireSec, curAmount:$curAmount, nextStageAmount:$nextStageAmount';
  }

  void releaseRewardAmount() {
    _reset();
  }

  void _reset() {
    enableRoomRewardFeature = false;
    rewardAmountStage = 0;
    curRewardTimeSec = 0;
    totalRewardTimeRequireSec = 0;
    curAmount = 0;
    nextStageAmount = 0;
  }
}
