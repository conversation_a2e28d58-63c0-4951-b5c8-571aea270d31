import 'dart:async';
import 'package:fixnum/fixnum.dart' as $fixnum;

import 'package:get/get.dart';
import 'package:service/modules/cocos_game/cocos_game_const.dart';
import 'package:service/modules/cocos_game/model/empire_config_model.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/pb/net/pb_game.pb.dart';
import 'package:service/service.dart';

import '../model/room_info_model.dart';

class RoomCocosGameService extends GetxController {
  RoomGameEmpireMatchInfo? _matchInfo;

  RoomGameEmpireMatchInfo? get matchInfo => _matchInfo;

  Timer? _matchTimeOut;

  /// 进房匹配信息
  void onJoinRoomGameMatchInfo(GameEmpireInfo? gameEmpireInfo) {
    if (gameEmpireInfo == null || gameEmpireInfo.isValid() != true || _isTimeOut(gameEmpireInfo.endTime)) {
      matchInfo = null;
      _cancelMatchTimeOut();
    } else {
      matchInfo = RoomGameEmpireMatchInfo(
        matchId: gameEmpireInfo.pkId,
        camp: gameEmpireInfo.camp,
        camp1Uid: gameEmpireInfo.camp1Uid,
        camp2Uid: gameEmpireInfo.camp2Uid,
        endTime: $fixnum.Int64(gameEmpireInfo.endTime),
      );

      _tryStartTimeoutTimer(matchInfo);
    }
  }

  /// Push 匹配
  set matchInfo(RoomGameEmpireMatchInfo? value) {
    _matchInfo = value;
    _tryStartTimeoutTimer(value);
  }

  void _tryStartTimeoutTimer(RoomGameEmpireMatchInfo? value) {
    if (value == null) {
      _cancelMatchTimeOut();
    } else {
      int dur = value.endTime.toInt() - DateTime.now().millisecondsSinceEpoch ~/ 1000;
      Log.d(cocosTag, 'cal timeout,match id ${value.matchId} end time ${value.endTime.toInt()}, dur: ${dur}s');
      if (dur > 0) {
        _cancelMatchTimeOut();
        _matchTimeOut = Timer(
          Duration(seconds: dur + 10),
          () {
            Log.d(cocosTag, 'match time out, cancel match ${value.matchId}');
            if (matchInfo?.matchId == value.matchId) {
              matchInfo = null;
              rxUtil.send(RoomGameEmpireEvent.timeout, 0);
            }
          },
        );
      } else {
        _cancelMatchTimeOut();
        _matchInfo = null;
      }
    }
  }

  _cancelMatchTimeOut() {
    _matchTimeOut?.cancel();
    _matchTimeOut = null;
  }

  void handleGameResult(RoomGameEmpireResultInfo resultInfo) {
    if (resultInfo.pkId == matchInfo?.matchId) {
      matchInfo = null;
      _cancelMatchTimeOut();
    }
  }

  bool get hasGaming => matchInfo != null;

  /// 游戏说明的Url
  Rxn<H5PageInfo> cocosH5Info = Rxn<H5PageInfo>();

  void loadConfig() async {
    var config = await cocosGameService.getCachedEmpireConfig();
    cocosH5Info.value = config?.data?.h5PageInfo;
  }

  @override
  void onClose() {
    _cancelMatchTimeOut();
    super.onClose();
  }
}

bool _isTimeOut(int? endTime) {
  return (endTime ?? 0) - (DateTime.now().millisecondsSinceEpoch ~/ 1000) <= 0;
}
