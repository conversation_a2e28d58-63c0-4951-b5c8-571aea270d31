import 'dart:async';

import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/model/list_resp.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/account/const/events.dart';
import 'package:service/modules/live/api/live_api.dart';
import 'package:service/modules/live/chat/model/invite_share_msg_content.dart';
import 'package:service/modules/live/chat/model/notice_msg_content.dart';
import 'package:service/modules/live/event/handler/room_event_handler.dart';
import 'package:service/modules/live/media/handler/music_player_manager.dart';
import 'package:service/modules/live/plugins/red_pack/red_pack_msg_handler.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/handler/add_admin_handler.dart';
import 'package:service/modules/live/room/handler/auto_take_mic_handler.dart';
import 'package:service/modules/live/room/handler/enter_room_handler.dart';
import 'package:service/modules/live/room/handler/exit_room_handler.dart';
import 'package:service/modules/live/room/handler/kick_room_handler.dart';
import 'package:service/modules/live/room/handler/remove_admin_handler.dart';
import 'package:service/modules/live/room/handler/room_info_update_handler.dart';
import 'package:service/modules/live/room/handler/room_red_point_update_handler.dart';
import 'package:service/modules/live/room/handler/room_video_handler.dart';
import 'package:service/modules/live/room/handler/room_will_close_tips_handler.dart';
import 'package:service/modules/live/room/handler/user_charm_wealth_change_handler.dart';
import 'package:service/modules/live/room/interface/black_list_mixin.dart';
import 'package:service/modules/live/room/interface/room_statistics_mixin.dart';
import 'package:service/modules/live/room/join_room_code.dart';
import 'package:service/modules/live/room/model/activity_model.dart';
import 'package:service/modules/live/room/model/bonus_model.dart';
import 'package:service/modules/live/room/model/cross_room_pk_record.dart';
import 'package:service/modules/live/room/model/cross_room_pk_record_list.dart';
import 'package:service/modules/live/room/model/online_user_list_model.dart';
import 'package:service/modules/live/room/model/room_country_list.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';
import 'package:service/modules/live/room/model/room_level.dart';
import 'package:service/modules/live/room/model/room_member_tab_config.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';
import 'package:service/modules/live/room/model/room_tag_model.dart';
import 'package:service/modules/live/room/model/room_theme_model.dart';
import 'package:service/modules/mall/model/mall_tabs.dart';
import 'package:service/modules/match/model/match_no_action_model.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/task/model/task_model.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/local/pb_local.pbenum.dart';
import 'package:service/pb/local/pb_local_req.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/connectivity_util.dart';
import 'package:service/utils/value_parsers.dart';

import '../../../common/statistics/const/const.dart';
import '../../family/const/events.dart';
import '../../family/handler/lucky_bag_msg_handler.dart';
import '../../family/model/family_info_model.dart';
import '../game/model/search_result.dart';
import '../room_fans/room_fans_service.dart';
import 'abs_room_service.dart';
import 'const/enums.dart';
import 'controller/room_cocos_game_service.dart';
import 'controller/room_sign_in_controller.dart';
import 'handler/ban_room_handler.dart';
import 'handler/enter_room_car_handler.dart';
import 'handler/general_float_screen_handler.dart';
import 'handler/global_float_screen_handler.dart';
import 'handler/level_upgrade_handler.dart';
import 'handler/party_dur_change_handler.dart';
import 'handler/punish_silent_handler.dart';
import 'handler/rank_top_change_handler.dart';
import 'handler/room_activity_progress_handler.dart';
import 'handler/room_fans_draw_count_change_handler.dart';
import 'handler/room_fans_level_handler.dart';
import 'handler/room_game_empire_handler.dart';
import 'handler/room_giftwall_light_handler.dart';
import 'handler/room_task_handler.dart';
import 'handler/room_userinfo_update_handler.dart';
import 'interface/game_invite_mixin.dart';
import 'interface/room_punish_interface.dart';
import 'interface/room_rank_interface.dart';
import 'interface/room_reward_amount_mixin.dart';
import 'interface/room_setting_interface.dart';
import 'interface/room_user_mixin.dart';
import 'interface/room_video_mixin.dart';
import 'model/join_room_resp.dart';
import 'model/leave_room_resp.dart';
import 'model/ranking_model.dart';
import 'model/room_common_config.dart';
import 'model/room_gift_gallery_rank.dart';
import 'model/room_list_model.dart';
import 'model/room_mode_list_model.dart';
import 'model/room_name_list_model.dart';
import 'model/room_stay_time_model.dart';
import 'model/room_user_info_model.dart';
import 'model/user_live_status.dart';

@Service()
class RoomService extends AbsRoomService
    with
        RoomUserMixin,
        BlackListMixin,
        RoomStatisticsMixin,
        RoomVideoMixin,
        RoomSettingInterface,
        RoomPunishInterface,
        RoomRankInterface,
        RoomRewardAmountMixin,
        GameInviteMixin {
  JoinRoomResp? _currentRoom;
  FamilyInfoModel? _familyInfo;

  final String _tag = "RoomService";

  /// 缓存房间tag
  RoomTypeTagList? tagList;

  /// 提醒房主邀请停留1min的用户上麦
  Timer? _stay1minTimer;

  /// 提醒停留2min的用户去关注房主
  Timer? _stay2minTimer;

  int _joinRoomTime = 0;

  /// 房间是否缩小化
  bool _isRoomMinimum = false;

  bool _engineCreated = false;

  /// 是否已经加入频道，小窗进入不用再走
  bool _joinedRoomChannel = false;

  FlatHttpResponse<RoomLevelDetail>? _currentRoomLevel;

  /// 我的全部房间 包括家族房间
  MyRoomGoodIdInfo? _myRoomGoodIdInfo;

  /// 间隔上报停留时长的Timer
  Timer? _stayReportTimer;

  int _stayReportInterval = kDefaultReportRoomStayTimeInterval;

  RoomSignInController get signInController => Get.find<RoomSignInController>(tag: _getXTag());

  RoomFansService get fansService => Get.find<RoomFansService>(tag: _getXTag());

  RoomCocosGameService get cocosGameService => Get.find<RoomCocosGameService>(tag: _getXTag());

  final preferences = Preferences.newInstance(spLive);

  List<RoomTagModel>? _tagList;

  RoomService() {
    /// 启动关联
    micSeatService;
    imRoomService;
    mediaService;
    roomChatService;
    gameService;
    roomReportService;
    turntableGameService;
    newGameService;

    /// 初始化room user mixin
    setup();

    /// 加handler
    notificationService.addHandler(AddAdminHandler());
    notificationService.addHandler(RoomEventHandler());
    notificationService.addHandler(AutoTakeMicHandler());
    notificationService.addHandler(RemoveAdminHandler());

    notificationService.addHandler(KickRoomHandler());
    notificationService.addHandler(RoomInfoUpdateHandler());
    notificationService.addHandler(RoomUserInfoUpdateHandler());
    notificationService.addHandler(BanRoomHandler());
    notificationService.addHandler(EnterRoomHandler());
    notificationService.addHandler(EnterRoomCarHandler());
    notificationService.addHandler(ExitRoomHandler());
    notificationService.addHandler(RoomLevelUpgradeHandler());
    notificationService.addHandler(RoomRedPointUpdateHandler());
    notificationService.addHandler(RoomVideoHandler());
    notificationService.addHandler(RoomTaskHandler());
    notificationService.addHandler(RoomActivityProgressHandler());
    notificationService.addHandler(PunishSilentHandler());
    notificationService.addHandler(RankTopChangeHandler());
    notificationService.addHandler(PartyDurChangeHandler());
    notificationService.addHandler(RoomFansLevelHandler());
    notificationService.addHandler(RoomFansDrawCountChangeHandler());
    notificationService.addHandler(UserCharmWealthChangeHandler());
    notificationService.addHandler(RedPackMsgHandler());
    notificationService.addHandler(LuckyBagMsgHandler());
    notificationService.addHandler(GeneralFloatScreenHandler());
    notificationService.addHandler(GlobalFloatScreenHandler());
    notificationService.addHandler(RoomGiftWallLightHandler());
    notificationService.addHandler(RoomGameEmpireHandler());
    notificationService.addHandler(RoomWillCloseTipsHandler());

    /// 监听事件
    rxUtil.observer<String>(AccountEvent.beforeLogout).listen(_onLogout);
    final rxMerge = Rx.merge([
      rxUtil.observer<String>(RoomEvent.imRoomLeave),
      rxUtil.observer<String>(RoomEvent.mediaLeave),
    ]);
    rxMerge.bufferTime(Duration(milliseconds: 500)).listen(_onRoomDisconnect);
    rxUtil.observer(RoomUserOptEvent.initBlackList).listen((_) {
      muteBlackList();
    });

    rxUtil
        .observer<ConnectivityStatus>(DeviceEvent.connectivityChange)
        .throttleTime(Duration(milliseconds: 500), trailing: true, leading: false)
        .listen(_updateCurrentRoomModel);

    rxUtil.observer<String>(UserRelationEvent.follow).listen(_onFollowUser);
    rxUtil.observer<String>(UserRelationEvent.unFollow).listen(_onUnFollowUser);

    ///注册自己信息更新通知
    rxUtil.observer<List<String>>(UserStatusEvent.updateUsers).listen(_handleUpdateUsers);

    rxUtil.observer<int>(RoomEvent.roomLevelUpdate).listen((_) {
      _fetchRoomInfo();
    });

    rxUtil.observer<PbRoomUser>(RoomUserEvent.addAdmin).listen((_) {
      _currentRoom?.userType = RoomUserType.ADMINER;
      MusicPlayerManager.instance().setEnablePlayer(true);
    });
    rxUtil.observer<PbRoomUser>(RoomUserEvent.removeAdmin).listen((_) {
      _currentRoom?.userType = RoomUserType.MEMBER;
      MusicPlayerManager.instance().stopMusic();
      MusicPlayerManager.instance().setEnablePlayer(false);
    });

    rxUtil.observer<String>(LocalEvent.change).listen((_) {
      _tagList = null;
      getRoomTagList();
    });
  }

  void _onRoomDisconnect(List<String> roomId) {
    if (roomId.isEmpty) return;
    if (isInLive()) {
      for (var element in roomId) {
        if (element == getCurrentRoomId()) {
          leaveRoom(element);
          rxUtil.send(RoomEvent.roomDisconnect, element);
          return;
        }
      }
    }
  }

  void _onLogout(String uid) {
    Log.i(_tag, "_onLogout");
    if (isInLive()) {
      leaveRoom(getCurrentRoomId()!);
    }
    _familyInfo = null;
  }

  Future<FlatHttpResponse<RoomInfoModel>> joinRoom(String roomId, {String pwd = '', String? from}) async {
    /// 已经在同个房间 排除家族房间
    if (isInLive() &&
        (_currentRoom?.roomId == roomId ||
            (roomId.isEmpty &&
                _currentRoom?.isFamilyRoom != true &&
                _currentRoom?.roomOwner == accountService.getAccountInfo()?.uid))) {
      return FlatHttpResponse(1, '',
          data: _currentRoom
            ?..joinNewRoom = false
            ..start = false);
    }

    /// http进房
    var httpResp = await liveApi.joinRoom(roomId: roomId, pwd: pwd);
    if (httpResp.isSuccess && httpResp.data?.roomId?.isNotEmpty == true) {
      /// 能够成功进入另外一个房间，则需要先退房
      if (isInLive()) {
        await reportLeaveRoom(from: from, exitType: '3');
        /*final leave = */
        await leaveRoom(_currentRoom!.roomId!);
        _currentRoom = null;
        _currentRoomLevel = null;

        /// 等UI退出，重进房间
        await Future.delayed(Duration(milliseconds: 200));
        /*if (leave?.isLast == true) {
        _lastAdminSwitchRoomCallback?.call(roomId, leave);
        return FlatHttpResponse(JoinRoomCode.lastAdminExit, '');
      }*/
      }

      _currentRoom = httpResp.data!;

      /// 遇到未知的房间模式时，设置为默认的Chat模式
      if (RoomMode.isUnknown(_currentRoom?.mode)) {
        _currentRoom!.mode = RoomMode.CHAT;
      }

      if (from == StatisticPageFrom.gameMatch) {
        _currentRoom?.joinFromMatching = true;
      }
      roomFactoryService().onFactoryJoinRoom(); // 创建服务&调用进房
      _initController();
      cocosGameService.onJoinRoomGameMatchInfo(_currentRoom?.gameEmpireInfo);
    }
    return httpResp;
  }

  Future<FlatHttpResponse> joinRoomChannel() async {
    if (_currentRoom?.roomId?.isNotEmpty == true) {
      var retSuccessResp = FlatHttpResponse(1, null);
      if (_joinedRoomChannel) {
        return retSuccessResp;
      }
      String roomId = _currentRoom!.roomId!;

      /// 信令聊天室
      int rtmCode = await imRoomService.joinRoom(roomId);
      if (rtmCode != 1) {
        /// 进房失败
        await leaveRoom(roomId, needMedia: false, needSignal: false);
        return FlatHttpResponse(JoinRoomCode.imRoomError + rtmCode, LocaleStrings.instance.defaultError);
      }

      if (!_engineCreated) {
        var createCode = await mediaService.createEngine();
        if (createCode != 1) {
          /// 进房失败
          await leaveRoom(roomId, needMedia: false, needSignal: false);
          return FlatHttpResponse(JoinRoomCode.mediaRoomError + createCode, LocaleStrings.instance.defaultError);
        }
      }
      _engineCreated = true;
      await mediaService.setConfig({
        'parameters':
            '"{\"rtc.enable_xdump\": false}","{\"rtc.enable_xdump_file\": false}","{\"rtc.enable_xdump_upload\": false}"',
        'channelProfile': _currentRoom?.channelProfile ?? '1',
        'audioProfile': _currentRoom?.audioProfile ?? '0',
        'audioScenario': _currentRoom?.audioScenario ?? '0'
      });

      // 先关闭本地录音权限申请
      await mediaService.enableLocalAudio(false);

      int rtcCode = await mediaService.joinRoom(roomId, _currentRoom?.agoraToken ?? '');

      if (rtcCode != 1) {
        /// 进房失败
        await leaveRoom(roomId, needMedia: false, needSignal: true);
        return FlatHttpResponse(JoinRoomCode.mediaRoomError + rtcCode, LocaleStrings.instance.defaultError);
      }
      punishUserSilent(silentTime: _currentRoom?.silentTimeRemaining ?? 0);
      _joinRoomTime = DateTime.now().millisecondsSinceEpoch;
      rxUtil.send(RoomEvent.joinRoom, roomId);
      _stayReportInterval = remoteConfigService.config.reportRoomStayTimeInterval;
      _startRoomStayReportTimer();
      initRewardAmountConfig();
      fansService.initFansModule();
      onJoinRoom(roomId);
      joinRoomNotify();
      MusicPlayerManager.instance().setEnablePlayer(getCurrentRoom()?.isOwnerOrAdmin == true);

      /// 初始化黑名单列表
      unawaited(roomService.initBlackList());

      updateUserRoomStatus(UserLiveStatusModel()
        ..uid = accountService.getAccountInfo()?.uid
        ..status = true);

      roomGuideService.joinRoom();
      matchService.cancelDelayNoActionTimer(action: MatchNoAction.enterRoom);
      _joinedRoomChannel = true;

      return retSuccessResp;
    }

    /// 进房失败
    return FlatHttpResponse(0, 'room id is empty');
  }

  @override
  String getRoomMode() {
    return _currentRoom?.mode ?? RoomMode.CHAT;
  }

  @override
  bool isRoomChannel() {
    return _joinedRoomChannel;
  }

  bool isInLive() {
    return (_currentRoom?.roomId?.isNotEmpty == true) && (_currentRoom?.isOpen == true);
  }

  RoomInfoModel? getCurrentRoom() {
    return _currentRoom;
  }

  Future<RoomUserInfoModel?> getCurrentUserInfo({bool forceUpdate = false}) async {
    final uid = getCurrentUid();
    if (uid?.isNotEmpty == true) {
      return (await getRoomUserCard(uid!, forceUpdate: forceUpdate, needRefresh: false)).data;
    }
    return null;
  }

  String? getCurrentRoomId() {
    return _currentRoom?.roomId;
  }

  String? getCurrentUid() {
    return accountService.getAccountInfo()?.uid;
  }

  Future<bool> isFamilyMember({bool refresh = false}) async {
    if (_familyInfo == null || refresh) {
      _familyInfo = (await familyService.getMyFamilyInfo()).data;
    }
    return _familyInfo?.id == getCurrentRoomId();
  }

  bool isRoomOwner() {
    if (getCurrentUid() != null && getCurrentRoom()?.roomOwner != null) {
      return getCurrentUid() == getCurrentRoom()?.roomOwner;
    } else {
      return false;
    }
  }

  Future leaveRoom(String roomId,
      {bool needSignal = true,
      bool needMedia = true,
      bool close = false,
      Function(FlatHttpResponse<LeaveRoomResp> resp)? leaveRoomCallback}) async {
    Log.i(_tag, "leaveRoom $roomId");
    rxUtil.send(RoomEvent.beforeLeaveRoom, roomId);
    _cancelRoomStayReportTimer();

    /// 业务退房
    liveApi.leaveRoom(roomId: roomId, act: close ? 'close_room' : 'quit_room').then((resp) {
      if (close == true && resp.isSuccess) {
        rxUtil.send(RoomEvent.closeRoom, roomId);
      }
      return leaveRoomCallback?.call(resp);
    });

    roomFactoryService().onFactoryLeaveRoom();
    _releaseController();

    _currentRoom = null;
    _currentRoomLevel = null;
    onlineTopOneUser = null;
    _joinRoomTime = 0;
    releasePunishUserSilent();
    releaseBgMusic();
    _joinedRoomChannel = false;
    roomGuideService.leaveRoom();
    _stay1minTimer?.cancel();
    _stay2minTimer?.cancel();
    cleanUser();
    releaseRewardAmount();
    _tagList = null;

    updateUserRoomStatus(UserLiveStatusModel()
      ..uid = accountService.getAccountInfo()?.uid
      ..status = false);

    if (needSignal) {
      /// 信令退房，如果退登陆了，不需要await，rtm logout和leave同时会阻塞回调
      if (accountService.hadLogin()) {
        await imRoomService.leaveRoom(roomId);
      } else {
        imRoomService.leaveRoom(roomId);
      }
    }

    if (needMedia) {
      /// 媒体退房
      await mediaService.leaveRoom();
    }

    MusicPlayerManager.instance().stopMusic();
    MusicPlayerManager.instance().setEnablePlayer(false);

    mediaService.stopAllEffect();

    rxUtil.send(RoomEvent.leaveRoom, roomId);

    channelMsgService.send(PbLocalMsgType.PbLocalMsgType_ROOM_NOTIFICATION_BAR,
        req: PbLocalRoomNotificationReq(type: 0));

    Log.i(_tag, "leaveRoom success $roomId");
  }

  /// 首次进房
  void joinRoomNotify() async {
    /// 通知栏
    if (_currentRoom?.isOpen == true) {
      channelMsgService.send(PbLocalMsgType.PbLocalMsgType_ROOM_NOTIFICATION_BAR,
          req: PbLocalRoomNotificationReq(
              roomId: _currentRoom?.roomId ?? "",
              title: LocaleStrings.instance.inChatRoom(_currentRoom?.name ?? ""),
              type: 1));
    }

    var roomUser = await roomService.getCurrentUserInfo();
    bool isManager = (roomUser?.isOwner ?? false) || (roomUser?.isAdmin ?? false);

    /// 社区规则
    // roomChatService.addMsg([
    //   NoticeMsgContent(
    //       isRules: true,
    //       content: roomService.getCurrentRoom()?.welcome,
    //       isManager: isManager)
    // ]);

    /// 公告
    roomChatService.addMsg([
      NoticeMsgContent(
          isRules: false, content: roomService.getCurrentRoom()?.announce, isManager: roomUser?.isOwner ?? false)
    ]);

    UserInfo? userInfo = await roomUser?.baseInfo;
    roomChatService.sendEnterRoomMsg(isWelcome: false, atUser: userInfo);

    /// 如果是临时房，没有欢迎语和关注信息
    if (_currentRoom?.isTempRoom ?? true) return;

    final String roomId = _currentRoom!.roomId!;

    _stay1minTimer?.cancel();
    _stay1minTimer = Timer(Duration(minutes: 1), () {
      if (accountService.isSuperAdmin()) return;
      _addRemindOwner(roomUser, roomId);
    });

    _stay2minTimer?.cancel();
    _stay2minTimer = Timer(Duration(minutes: 2), () {
      if (accountService.isSuperAdmin()) return;
      _addFollowOwner(roomUser, roomId);
    });

    /// 超级管理员不显示分享弹窗后关注房间信息
    if (accountService.isSuperAdmin()) return;

    if (!isManager) {
      fansService.delayShowTipDialog();
    }

    /// 房主管理员分享引导，普通用户欢迎语
    if (isManager) {
      roomChatService.addMsg([InviteShareMsgContent()]);
    } else if (roomUser?.isMember != true) {
      /// 游客关注房间信息
      // roomChatService.addMsg([
      //   ChatFollowMsgContent(content: LocaleStrings.instance.followRoomContent)
      // ]);
    }
  }

  void _addRemindOwner(RoomUserInfoModel? roomUser, String roomId) async {
    if (roomId != _currentRoom?.roomId) return;
    if (roomUser?.isOwner == true) return;
    roomChatService.sendRemindOwnerInviteMsg();
  }

  void _addFollowOwner(RoomUserInfoModel? roomUser, String roomId) async {
    if (roomId != _currentRoom?.roomId) return;
    if (roomUser?.isOwner == true || _currentRoom?.hasFollow == true) return;
    roomChatService.addFollowOwnerMsg();
  }

  /// 将黑名单列表的用户mute音频
  void muteBlackList() {
    roomService.getUserBlackList().forEach((uid) => mediaService.mute(true, uid));
  }

  @override
  Future<List<RoomThemeBackpack>?> getRoomThemeList() async {
    final response = await liveApi.getRoomThemeList(roomId: getCurrentRoomId()!);
    return response.data?.roomThemeList;
  }

  @override
  Future<FlatHttpResponse<EditRoomResult>> editRoomInfo({
    String? roomName,
    String? announce,
    String? roomCover,
    String? tagId,
    String? tagName,
    String? micPermission,
    String? roomMode,
    String? cpStep,
    String? enterPermission,
    String? micMode,
    String? fee,
    String? gameMode,
  }) async {
    if (getCurrentRoomId() == null) {
      return FlatHttpResponse(-1, LocaleStrings.instance.tryAgain);
    }
    FlatHttpResponse<EditRoomResult> response = await liveApi.editRoomInfo(
        roomId: getCurrentRoomId()!,
        roomName: roomName,
        announce: announce,
        roomCover: roomCover,
        tagId: tagId,
        micPermission: micPermission,
        roomMode: roomMode,
        cpStep: cpStep,
        enterPermission: enterPermission,
        micMode: micMode,
        fee: fee,
        gameMode: gameMode);
    bool result = response.isSuccess;

    /// 如果编辑成功并且是公告相关的，需要发送一条全局pb，用作公屏显示
    if (result) {
      if (announce?.isNotEmpty == true) {
        roomChatService.sendAnnounce(text: announce ?? "");
      }
      if (roomName?.isNotEmpty == true) {
        roomChatService.sendRoomNameMsg(text: roomName ?? '');
      }
    }
    if (result) {
      if (tagId?.isNotEmpty == true) {
        _currentRoom?.roomTagId = tagId;
        _currentRoom?.roomTag = tagName;
      }
    }
    return response;
  }

  Future<FlatHttpResponse> editRoomBg({String? bagId, String? bgId, String? vipImg}) async {
    FlatHttpResponse response =
        await liveApi.editRoomTheme(roomId: getCurrentRoomId()!, id: bgId, bagId: bagId, vipImg: vipImg);

    if (response.isSuccess) {
      reportEventChangeRoomBg(bgNumber: bgId!);
    }

    return response;
  }

  @override
  Future<FlatHttpResponse> uploadRoomBg(String url) async {
    return await liveApi.updateRoomBg(sourceUrl: url);
  }

  @override
  Future<List<RoomTagModel>?> getRoomTagList() async {
    if (_tagList != null) return _tagList;
    FlatHttpResponse<RoomTypeTagList> response = await liveApi.getRoomTagList(roomId: getCurrentRoomId() ?? '');
    _tagList = response.data?.tagList;
    return _tagList;
  }

  @override
  Future<FlatHttpResponse> followerRoom({String? roomId, String? act, String? from}) async {
    reportRoomFollow(true, act: act, from: from);

    /// 超级管理员不能关注房间
    if (accountService.isSuperAdmin()) {
      return FlatHttpResponse(-1, LocaleStrings.instance.youAreSuperAdmin);
    }
    final _roomId = roomId ?? getCurrentRoomId();
    if (_roomId?.isEmpty ?? true) {
      return FlatHttpResponse(-1, LocaleStrings.instance.tryAgain);
    }
    final resp = await liveApi.followerRoom(roomId: _roomId ?? "");
    if (resp.isSuccess) {
      roomReportService.recordFollowRoom();
    }
    return resp;
  }

  @override
  Future<FlatHttpResponse> removeFollowerRoom({String? roomId, String? act, String? from}) async {
    reportRoomFollow(false, act: act, from: from);

    /// 超级管理员不能关注房间
    if (accountService.isSuperAdmin()) {
      return FlatHttpResponse(-1, LocaleStrings.instance.youAreSuperAdmin);
    }
    return await liveApi.removeFollowerRoom(roomId: roomId ?? getCurrentRoomId() ?? "");
  }

  @override
  Future<FlatHttpResponse<RoomListModel>> getFollowerRoomList({String startId = ''}) async {
    return await liveApi.followRoomList(startId: startId);
  }

  @override
  Future<OnlineUserListModel?> getFollowerList({required int page, required int size}) async {
    if (getCurrentRoomId() == null) return null;
    FlatHttpResponse<OnlineUserListModel> response =
        await liveApi.getRoomFollowerList(roomId: getCurrentRoomId()!, page: page.toString(), size: size.toString());
    return response.data;
  }

  @override
  Future<bool> editRoomPw({required bool lock, String? pw}) async {
    if (lock) {
      FlatHttpResponse response = await liveApi.editRoomPw(roomId: getCurrentRoomId()!, act: 'add', pw: pw);
      return response.isSuccess;
    } else {
      FlatHttpResponse response = await liveApi.editRoomPw(roomId: getCurrentRoomId()!, act: 'remove', pw: pw);
      return response.isSuccess;
    }
  }

  @override
  void updateRoomInfo({PbRoomInfoUpdateNotice? pb, RoomInfoModel? roomInfo}) async {
    RoomInfoModel? updateRoomInfo = _getRoom(pb: pb, newRoomInfo: roomInfo);
    if (!isInLive() || _currentRoom == null || updateRoomInfo == null) return;

    /// 更新公告
    if (updateRoomInfo.announce?.isNotEmpty == true && updateRoomInfo.announce != _currentRoom?.announce) {
      rxUtil.send(RoomEvent.roomAnnounceUpdate, updateRoomInfo.announce);
    }

    final _preRoomMode = _currentRoom!.mode;

    /// 遇到未知的房间模式时，设置为默认的Chat模式
    if (RoomMode.isUnknown(updateRoomInfo.mode)) {
      updateRoomInfo.mode = RoomMode.CHAT;
    }

    /// 更新其他信息
    _currentRoom!
      ..name = dynamicToStringNick(updateRoomInfo.name)
      ..status = updateRoomInfo.status
      ..cover = updateRoomInfo.cover
      ..announce = updateRoomInfo.announce
      ..hasPw = updateRoomInfo.hasPw
      ..roomOwner = updateRoomInfo.roomOwner
      ..mode = updateRoomInfo.mode
      ..micPermission = updateRoomInfo.micPermission
      ..roomTheme = updateRoomInfo.roomTheme
      ..cpStep = updateRoomInfo.cpStep
      ..showIdLevel = updateRoomInfo.showIdLevel
      ..showId = updateRoomInfo.showId
      ..micMode = updateRoomInfo.micMode
      ..enterPermission = updateRoomInfo.enterPermission
      ..gameConfig = updateRoomInfo.gameConfig
      ..roomTagId = updateRoomInfo.roomTagId
      ..password = updateRoomInfo.password
    ;
    // pb 中没有以下字段，需要判空 否则会被覆盖
    if (pb == null) {
      _currentRoom?.isFollowed = updateRoomInfo.isFollowed;
      _currentRoom?.h5GameList = updateRoomInfo.h5GameList;
    }
    if (updateRoomInfo.roomModeList != null) {
      _currentRoom?.roomModeList = updateRoomInfo.roomModeList;
    }
    rxUtil.send(RoomEvent.roomInfoUpdate, _currentRoom);

    if (_preRoomMode != _currentRoom!.mode) {
      rxUtil.send(RoomEvent.roomModeUpdate, _currentRoom);
      Log.d(_tag, ' switch room mode $_preRoomMode -> ${_currentRoom!.mode}');
      if (_preRoomMode != null && _currentRoom!.mode != null) {
        roomFactoryService().onFactoryChangeRoomMode(_preRoomMode, _currentRoom!.mode!);
      }
    }

    /// 关播
    if (updateRoomInfo.isClose) {
      await leaveRoom(_currentRoom!.roomId!);
    }

    ///关闭房间的原因，status=close时有效，0-房主关闭房间 1-房间静默被系统关闭 2-房主被ban号
    if (pb?.closeReason == 1) {
      showAlertDialog(
        content: LocaleStrings.instance.roomClosedDueToProlonged,
        showCancel: false,
        confirmText: LocaleStrings.instance.ok,
      );
    } else if (pb?.closeReason == 2) {
      toast(LocaleStrings.instance.forceClosedTips);
    }
  }

  @override
  bool mergeRoomInfo({
    int? roomUserCount,
    FansClubInfo? fansClubInfo,
    FansClubShow? fansClubShow,
  }) {
    bool changed = _currentRoom!.merge(
      roomUserCount: roomUserCount,
      fansClubInfo: fansClubInfo,
      fansClubShow: fansClubShow,
    );
    return changed;
  }

  RoomInfoModel? _getRoom({RoomInfoModel? newRoomInfo, PbRoomInfoUpdateNotice? pb}) {
    if (newRoomInfo != null) return newRoomInfo;
    if (pb != null) {
      return RoomInfoModel()
        ..micMode = pb.micMode
        ..announce = pb.roomAnnounce
        ..name = dynamicToStringNick(pb.roomName)
        ..status = pb.status
        ..cover = pb.roomCover
        ..announce = pb.roomAnnounce
        ..hasPw = pb.hasRoomPass_13
        ..roomOwner = pb.roomOwner
        ..mode = pb.liveMode
        ..micPermission = pb.microphonePermission
        ..status = pb.status
        ..roomTheme = RoomThemeModel.fromPb(pb.theme)
        ..showId = pb.showId
        ..showIdLevel = pb.showLevel
        ..cpStep = pb.cpStep
        ..enterPermission = toInt(pb.enterPermission)
        ..gameConfig = GameConfig.fromPb(pb.gameConf)
        ..roomTagId = pb.roomTagId
        ..password = pb.roomPass
      ;
    }
    return null;
  }

  @override
  Future<FlatHttpResponse<ListResp<RoomUserInfoModel>>> getRoomBlackList({int page = 0}) {
    return liveApi.roomBackList(roomId: getCurrentRoomId()!, page: '$page');
  }

  @override
  Future<FlatHttpResponse> kickRoom({required String uid}) {
    return liveApi.kickoutRoom(roomId: getCurrentRoomId()!, uid: uid);
  }

  /// 拉黑
  Future<FlatHttpResponse> roomBlack({required String uid}) {
    return liveApi.roomBlack(roomId: getCurrentRoomId()!, uid: uid);
  }

  @override
  Future<FlatHttpResponse> removeRoomBlackList({required String uid}) {
    return liveApi.delRoomBlackList(roomId: getCurrentRoomId()!, uid: uid);
  }

  @override
  Future<RoomTypeTagList?> getRoomTypeTagList() async {
    if (tagList != null) return tagList;
    final res = await liveApi.getRoomTypeTagList();
    if (res.isSuccess) {
      tagList = res.data;
      return res.data;
    }
    return null;
  }

  @override
  Future<RoomListModel?> getRoomListByType(
      {required String tagCode, String? tagId, String listVer = "", String size = "20", String startId = "0"}) async {
    final res =
        await liveApi.roomListByType(act: tagCode, size: size, startId: startId, listVer: listVer, tagId: tagId);

    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  @override
  Future<RoomListModel?> getRecommendRoomList() async {
    // final report = RecommendRoomRequestReport();
    final res = await liveApi.recommendRoomList();
    // report.reqEndReport(res);
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  RoomInfoModel? _myRoomCache;

  @override
  Future<FlatHttpResponse<RoomInfoModel?>> getMyRoomInfo() async {
    final resp = await liveApi.getMyRoomInfo();
    if (resp.isSuccess) {
      _myRoomCache = resp.data;
    }
    return resp;
  }

  @override
  Future<RoomInfoModel?> getMyRoomInfoCache() async {
    if (_myRoomCache == null) {
      await getMyRoomInfo();
    }
    return _myRoomCache;
  }

  @override
  Future<RoomNameListModel?> getRandomRoomName() async {
    final res = await liveApi.getRandomRoomName();
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  @override
  Future<FlatHttpResponse<RoomInfoModel>> createOrResetRoom(
      {required String mode, String? roomName, String? tagId, bool create = true, String? roomId}) async {
    final user = await userService.getCurrentUserInfo();
    final res = await liveApi.createOrResetRoom(
        roomId: roomId,
        roomName: roomName,
        tagId: tagId,
        mode: mode,
        create: create ? "1" : "",
        userName: user?.nickname);
    if (res.isSuccess && res.data?.roomId?.isNotEmpty == true) {
      rxUtil.send<RoomInfoModel>(RoomEvent.createRoom, res.data!);
    }

    return res;
  }

  @override
  Future<List<RoomListItem>?> getRecommendRooms(String roomId, {String? size}) async {
    final resp = await liveApi.roomRecommendList(roomId: roomId, size: size ?? "4");
    if (resp.isSuccess && resp.data?.list?.isNotEmpty == true) {
      return resp.data?.list;
    }
    return null;
  }

  @override
  Future<String?> refreshToken() async {
    if (isInLive() && getCurrentRoomId()?.isNotEmpty == true) {
      final resp = await liveApi.refreshAgoraToken(roomId: getCurrentRoomId());
      if (resp.isSuccess) {
        return resp.data?.token;
      }
    }
    return null;
  }

  @override
  Future<FlatHttpResponse<JoinRoomResp>> matchGame({required String gameType, String? fee, String? gameMode}) async {
    return liveApi.ludoMatch(gameType: gameType, fee: fee, gameMode: gameMode);
  }

  @override
  Future<FlatHttpResponse<JoinRoomResp>> roomMatch({required String matchType}) {
    return liveApi.roomMatch(matchType: matchType);
  }

  @override
  Future<RoomListItem?> searchRoomById({required String roomId, String? act}) async {
    final res = await liveApi.searchRoomById(roomId: roomId, act: act);
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  @override
  Future<FlatHttpResponse<TaskRewardsModel>> getNewHandReward() async {
    final res = await liveApi.newHandReward();
    return res;
  }

  @override
  int getInRoomTime() {
    return DateTime.now().millisecondsSinceEpoch - _joinRoomTime;
  }

  @override
  Future<FlatHttpResponse> blockRoom(String roomId) async {
    return await liveApi.blockRoom(roomId: roomId);
  }

  @override
  Future<FlatHttpResponse> cancelBlockRoom(String roomId) async {
    final res = await liveApi.cancelBlockRoom(roomId: roomId);
    if (res.isSuccess && _currentRoom != null) {
      _currentRoom?.blackRoom = 0;
      rxUtil.send(RoomEvent.roomInfoUpdate, _currentRoom);
    }
    return res;
  }

  @override
  Future<RoomListModel?> getBlockRoomList({String size = "20", required String page}) async {
    final res = await liveApi.getBlockRoomList(page: page, size: size);
    return res.data;
  }

  @override
  Future<FlatHttpResponse<RankingModel>> getRoomTotalRank() async {
    return await liveApi.getRoomTotalRank();
  }

  @override
  Future<FlatHttpResponse<RankingModel>> getRoomRankCoins() async {
    return await liveApi.getRoomRankCoins(roomId: getCurrentRoomId() ?? '');
  }

  @override
  Future<FlatHttpResponse<RoomLevelDetail>> getRoomLevelDetail(
      {required String roomId, required String action, bool? cache}) async {
    if (cache == true && _currentRoomLevel != null) {
      return _currentRoomLevel!;
    }
    return await liveApi.roomLevel(roomId: roomId, action: action);
  }

  @override
  Future<FlatHttpResponse<BonusModel>> getBonusInfo() async {
    return await liveApi.getBonusInfo(roomId: roomService.getCurrentRoomId() ?? '');
  }

  Future<FlatHttpResponse<RoomCountryList>> getCountryList() async {
    return await liveApi.getCountryList();
  }

  Future<RoomListModel?> getCouRoomList({required String country, required int size, String? startId}) async {
    final res = await liveApi.getCouRoomList(startId: startId ?? '0', country: country, size: size.toString());
    return res.data;
  }

  @override
  bool isRoomMinimum() {
    return _isRoomMinimum;
  }

  @override
  void setRoomMinimum(bool minimum) {
    _isRoomMinimum = minimum;
  }

  @override
  int getJoinRoomTime() {
    return _joinRoomTime;
  }

  @override
  void updateCpRoomStep(String step, {bool updateMic = true}) async {
    if (isInLive() && _currentRoom != null) {
      _currentRoom!.cpStep = step;

      rxUtil.send(RoomEvent.roomInfoUpdate, _currentRoom);

      if (updateMic) {
        rxUtil.send(RoomMicSeatEvent.update, await micSeatService.fetchMicSeat());
      }
    }
  }

  void _fetchRoomInfo() async {
    final resp = await liveApi.getCurrentRoomInfo(roomId: getCurrentRoom()?.roomId ?? "");
    if (resp.isSuccess && resp.data != null) {
      updateRoomInfo(roomInfo: resp.data);
    }
  }

  void _updateCurrentRoomModel(ConnectivityStatus status) {
    if (status != ConnectivityStatus.none && roomService.isInLive()) {
      _fetchRoomInfo();
    }
  }

  Future<MyRoomGoodIdInfo?> getMyAllRoomInfo({bool fromServer = false}) async {
    if (_myRoomGoodIdInfo != null && !fromServer) return _myRoomGoodIdInfo;
    final res = await liveApi.getMyAllRoomInfo();
    _myRoomGoodIdInfo = res.data;
    return _myRoomGoodIdInfo;
  }

  @override
  Future<FlatHttpResponse<ActivityListResp>> activityProgressOption() async {
    return await liveApi.activityProgressOption(roomId: _currentRoom?.roomId ?? "");
  }

  @override
  Future<FlatHttpResponse> setActivityProgressOption(String codeList) async {
    return await liveApi.setActivityProgressOption(roomId: _currentRoom?.roomId ?? "", codeList: codeList);
  }

  @override
  Future<FlatHttpResponse<ActivityListResp>> getActivityProgressList() async {
    return await liveApi.activityProgressList(roomId: _currentRoom?.roomId ?? "");
  }

  @override
  Future<CrossRoomPKRecordList?> getCrossRoomPKRecords() async {
    final resp = await liveApi.getCrossRoomPKRecords(roomId: getCurrentRoom()?.roomId ?? "");
    return resp.isSuccess ? resp.data : null;
  }

  @override
  Future<CrossRoomPKRecord?> getCrossRoomPKRecord(String gameId) async {
    final resp = await liveApi.getCrossRoomPKResult(roomId: getCurrentRoom()?.roomId ?? "", gameId: gameId);
    return resp.isSuccess ? resp.data : null;
  }

  @override
  Future<FlatHttpResponse<RoomMemberTabConfig>> roomUserTabConfig({String? act}) async {
    return await liveApi.roomUserTabConfig(roomId: getCurrentRoom()?.roomId ?? "", act: act);
  }

  Future<FlatHttpResponse<RoomGiftGalleryRankListModel>> giftWallRank() async {
    return await liveApi.giftWallRank();
  }

  _onFollowUser(String targetUid) {
    if (targetUid == _currentRoom?.roomOwner) {
      _currentRoom?.hasFollow = true;
    }
  }

  _onUnFollowUser(String targetUid) {
    if (targetUid == _currentRoom?.roomOwner) {
      _currentRoom?.hasFollow = false;
    }
  }

  _handleUpdateUsers(List<String> uids) {
    var uid = _currentRoom?.roomOwner;
    if (uid != null && uids.contains(uid)) {
      updateRoomOwnerInfo();
    }
  }

  void updateRoomOwnerInfo() {
    var uid = _currentRoom?.roomOwner;
    if (uid == null) {
      return;
    }
    UserInfo? userInfo = userService.getUserInfoInCache(uid);
    if (userInfo == null) return;
    bool changed = false;
    if (userInfo.avatarCode != _currentRoom!.roomOwnerAvatarCode) {
      _currentRoom!.roomOwnerAvatarCode = userInfo.avatarCode!;
      changed = true;
    }
    if (userInfo.avatar != _currentRoom!.roomOwnerAvatar) {
      _currentRoom!.roomOwnerAvatar = userInfo.avatar!;
      changed = true;
    }
    if (changed) {
      rxUtil.send(RoomEvent.roomInfoUpdate, _currentRoom);
    }
  }

  Future<FlatHttpResponse<RoomStayTimeModel>> roomStayTimeReport() async {
    return liveApi.roomStayTimeReport(roomId: getCurrentRoomId()!);
  }

  Future<FlatHttpResponse<EditRoomResult>> changeRoomMode({required String roomId, required String roomMode}) async {
    return await liveApi.editRoomInfo(
      roomId: roomId,
      roomMode: roomMode,
    );
  }

  Future<FlatHttpResponse<RoomCommonConfig>> commonConfig({
    required String roomId,
  }) {
    return liveApi.commonConfig(roomId: roomId);
  }

  /// 开启上报房间停留时长Timer
  void _startRoomStayReportTimer() {
    if (_stayReportTimer != null && _stayReportTimer?.isActive == true) {
      return;
    }
    _stayReportTimer?.cancel();
    _stayReportTimer = null;
    if (_stayReportInterval > 0) {
      _stayReportTimer = Timer.periodic(Duration(seconds: _stayReportInterval), (timer) async {
        var resp = await roomStayTimeReport();
        if (resp.isSuccess) {
          var interval = resp.data?.interval;
          if (interval != null && interval > 0) {
            if (_stayReportInterval != interval) {
              _stayReportInterval = interval;
              timer.cancel();
              _stayReportTimer = null;
              _startRoomStayReportTimer();
            }
          } else {
            _stayReportInterval = 0;
            timer.cancel();
            _stayReportTimer = null;
          }
        } else {
          Log.w(_tag, 'RoomStayReportTimer error ${resp.msg}');
        }
      });
    }
  }

  /// 关闭上报房间停留时长Timer
  void _cancelRoomStayReportTimer() {
    _stayReportTimer?.cancel();
    _stayReportTimer = null;
  }

  String _getXTag() {
    return '_${getCurrentRoomId() ?? ''}';
  }

  void _initController() {
    Get.lazyPut<RoomSignInController>(RoomSignInController.new, tag: _getXTag());
    Get.lazyPut<RoomFansService>(RoomFansService.new, tag: _getXTag());
    Get.lazyPut<RoomCocosGameService>(RoomCocosGameService.new, tag: _getXTag());
  }

  void _releaseController() {
    Get.delete<RoomSignInController>(tag: _getXTag());
    Get.delete<RoomFansService>(tag: _getXTag());
    Get.delete<RoomCocosGameService>(tag: _getXTag());
  }

  @override
  bool get isLudoGameMode => getRoomMode() == RoomMode.LUDO_GAME;

  /// 确认房间活跃
  @override
  Future<FlatHttpResponse> keepAlive() {
    return liveApi.keepAlive(roomId: roomService.getCurrentRoomId() ?? '');
  }

  @override
  void updateRoomModeGameMiniStatus({required bool isMini}) {
    if (isInLive() && _currentRoom != null) {
      if (_currentRoom?.gameMini == isMini) return;
      _currentRoom?.gameMini = isMini;
      rxUtil.send(RoomOperationEvent.roomModeGameMini, isMini);
    }
  }

  @override
  Future<FlatHttpResponse<RoomModeList>> getRoomModeList() async {
    return await liveApi.getRoomModeList(roomId: _currentRoom?.roomId ?? "");
  }

  @override
  Future<OnlineUserListModel?> getInviteListByPage({required int page, int limit = 20}) async {
    final roomId = roomService.getCurrentRoomId();
    if (roomId?.isNotEmpty == true) {
      final resp = await liveApi.getInviteListByPage(roomId: roomId!, page: page.toString(), limit: limit.toString());
      return resp.data;
    }
    return null;
  }

  @override
  Future<FlatHttpResponse<SearchResult>> search({required String keyword, String? opt}) async {
    return liveApi.search(keyword: keyword, opt: opt);
  }

  /// 房间列表
  @override
  Future<FlatHttpResponse<RoomPartyListEntity>> roomListByPage({
    required String tab,
    int page = 1,
  }) {
    return liveApi.roomListByPage(tab: tab, page: '$page');
  }

  /// following 房间列表
  @override
  Future<FlatHttpResponse<RoomPartyListEntity>> roomFollowingListByPage({int page = 1}) {
    return liveApi.roomFollowingListByPage(page: '$page');
  }

  /// 我的房间列表
  @override
  Future<FlatHttpResponse<MyRoomListEntity>> myRooms() {
    return liveApi.myRooms();
  }
}
