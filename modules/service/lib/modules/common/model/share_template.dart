import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/app_info.dart';
import 'package:service/l10n/intl_localizations.dart';
import 'package:service/modules/live/event/model/event_model.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/utils/deep_link.dart';

/// 分享内容模板
abstract class ShareTemplate {
  final Moment? moment;

  ///对方uid 用于好友关系操作：关注/举报/私聊
  final String? targetUid;

  ///链接：分享深度链接
  final String? deppLink;

  ///链接：分享外链
  final String? link;

  ///内容
  final String? content;

  ///image
  final String? image;

  ///icon
  final String? icon;


  ShareTemplate._(
      {this.moment,
      this.targetUid,
      this.deppLink,
      this.link,
      this.content,
      this.image,
      this.icon,});

  String getLogo() {
    return AppInfo.instance.logo;
  }
}

class MomentShare extends ShareTemplate {
  MomentShare({required Moment moment})
      : super._(
            moment: moment,
            targetUid: moment.userInfo?.uid,
            content: (moment.text?.isNotEmpty ?? false)
                ? moment.text
                : LocaleStrings.instance.shareContent,
            icon: (moment.medias?.isNotEmpty ?? false)
                ? moment.medias![0].thumb
                : "",
            image: (moment.medias?.isNotEmpty ?? false)
                ? moment.medias![0].thumb
                : "",
            link:
                "${AppConfig.instance.webBaseUrl}?${DeepLink.deepLinkAction}${moment.getDeepLink()}",
            deppLink: moment.getDeepLink());
}

class QuizShare extends ShareTemplate {
  QuizShare(
      {String? image,
      required String title,
      String? deepLink,
      String? link,
      String? icon})
      : super._(
            image: image,
            content: title,
            deppLink: deepLink,
            link: link,
            icon: icon);
}

class CommandShare extends ShareTemplate {
  CommandShare(
      {required String content,
      String? deepLink,
      String? link,
      String? icon,
      String? image})
      : super._(
            content: content,
            deppLink: deepLink,
            link: link,
            icon: icon,
            image: image);
}

class ImageShare extends ShareTemplate {
  ImageShare({required String image}) : super._(image: image);
}

class AppShare extends ShareTemplate {
  AppShare({required String content, String? link})
      : super._(
          content: content,
          link: link,
        );
}

class LiveRoomShare extends ShareTemplate {
  /// 内部分享给朋友显示的内容
  final String imContent;

  LiveRoomShare({
    required String content,
    required this.imContent,
    String? deepLink,
    String? link,
    String? icon,
    String? image,
  }) : super._(
            content: content,
            deppLink: deepLink,
            link: link,
            icon: icon,
            image: image);
}

/// 活动分享
class LiveEventShare extends ShareTemplate {
  /// 内部分享给朋友显示的内容
  final String imContent;

  LiveEventShare({
    required String content,
    required this.imContent,
    required RoomEventInfo eventInfo,
    String? deepLink,
  }) : super._(content: content, deppLink: deepLink,);
}


/// 家族分享
class FamilyShare extends ShareTemplate {
  /// 内部分享给朋友显示的内容
  final String imContent;

  FamilyShare({
    required String content,
    required this.imContent,
    String? deepLink,
  }) : super._(content: content, deppLink: deepLink,);
}

/// 特殊活动分享
class LiveEventTagShare extends ShareTemplate {
  final String title;

  LiveEventTagShare({
    required String content,
    required this.title,
    required String image,
    String? deepLink,
    String? link,
  }) : super._(content: content, deppLink: deepLink, image: image, link: link);
}