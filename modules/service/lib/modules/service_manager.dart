import 'package:get/get.dart';
import 'package:service/service.dart';

import 'account/const/events.dart';
import 'im/chat_list_service.dart';
import 'user/service/user_agency_service.dart';

export 'task/income_task_service.dart';
export 'cocos_game/cocos_game_service.dart';

const _tag = 'ServiceManager';

/// 服务管理器，存放全局形式的服务
/// 目前是Getx的实例管理
/// 服务分为用户级别和全局级别
class ServiceManager {
  ServiceManager._();

  static ServiceManager inst = ServiceManager._();

  String? _currentUid;

  String get uidTag => 'uid_$_currentUid';

  void init() {
    rxUtil.observer<String>(AccountEvent.loginSuccess).listen((value) {
      Log.d(_tag, '  --- loginSuccess ---');
      initTag();
    });

    rxUtil.observer<String>(AccountEvent.logoutSuccess).listen((value) {
      Log.d(_tag, '  --- logoutSuccess ---');
      initTag();
    });
  }

  void initTag() {
    var uid = accountService.currentUidOfCache();
    Log.d(_tag, '  initTag uid=$uid, _currentUid=$_currentUid');
    if (uid != _currentUid) {
      Log.d(_tag, '  uid diff,re-create userLevel controller');
      _deleteUserLevel();
      _currentUid = uid;
      _putUserLevel();
    }
  }

  /// 注册所有服务
  void registerController() {
    _putUserLevel();
    Get.lazyPut<CocosGameService>(CocosGameService.new);
  }

  /// 反注册所有服务
  void unRegisterController() {
    _deleteUserLevel();
  }

  /// 注册用户级别服务
  void _putUserLevel() {
    if (_currentUid?.isNotEmpty == true) {
      String getXTag = uidTag;
      Get.lazyPut<IncomeTaskService>(IncomeTaskService.new, tag: getXTag);
      Get.lazyPut<UserAgencyService>(UserAgencyService.new, tag: getXTag);
      Get.lazyPut<ChatListService>(ChatListService.new, tag: getXTag);
    }
  }

  /// 销毁用户级别服务
  void _deleteUserLevel() {
    if (_currentUid?.isNotEmpty == true) {
      String getXTag = uidTag;
      Get.delete<IncomeTaskService>(tag: getXTag);
      Get.delete<UserAgencyService>(tag: getXTag);
      Get.delete<ChatListService>(tag: getXTag);
    }
  }
}

/// 个人收益服务
IncomeTaskService get incomeTaskService {
  assert(ServiceManager.inst._currentUid?.isNotEmpty == true);
  return Get.find<IncomeTaskService>(tag: ServiceManager.inst.uidTag);
}

/// 用户公会服务
UserAgencyService get agencyService {
  assert(ServiceManager.inst._currentUid?.isNotEmpty == true);
  return Get.find<UserAgencyService>(tag: ServiceManager.inst.uidTag);
}

/// 消息列表服务
ChatListService get chatListService {
  assert(ServiceManager.inst._currentUid?.isNotEmpty == true);
  return Get.find<ChatListService>(tag: ServiceManager.inst.uidTag);
}

CocosGameService get cocosGameService => Get.find<CocosGameService>();
