import 'dart:async';
import 'dart:convert';

import 'package:protobuf/protobuf.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/modules/account/const/events.dart';
import 'package:service/modules/channel_msg/handler/i_channel_msg_handler.dart';
import 'package:service/modules/live/api/live_api.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/pb/local/pb_local.pb.dart';
import 'package:service/pb/local/pb_local_req.pb.dart';
import 'package:service/pb/local/pb_local_resp.pb.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/service.dart';

import '../abs_im_room_service.dart';

mixin RtmType {
  static const none = 0;
  static const joinRoom = 1;
  static const leaveRoom = 2;
  static const login = 3;
  static const logout = 4;
  static const sendMsg = 5;
  static const receivedMsg = 6;
  static const disconnect = 7;
}

@Service()
class ImRoomService extends AbsImRoomService implements IChannelMsgHandler {
  final _tag = 'ImRoomService';

  /// 公共房间id
  final _commonRoomId = '9999';
  bool _hadJoinCommonRoom = false;

  /// 转盘游戏房间id
  final _turntableRoomId = '19999';
  bool _hadJoinTurntableRoom = false;

  bool _hadLogin = false;

  /// 登录中
  bool _inLogin = false;

  ImRoomService() {
    rxUtil.observer<String>(AccountEvent.loginSuccess).listen(_onLoginSuccess);
    rxUtil
        .observer<String>(AccountEvent.logoutSuccess)
        .listen(_onLogoutSuccess);

    channelMsgService.addHandler(this);
  }

  @override
  String getCommonRoomId() {
    return _commonRoomId;
  }

  @override
  String getTurntableRoomId() {
    return _turntableRoomId;
  }

  @override
  void login() {
    if (_hadLogin || _inLogin) return;
    _rtmLogin();
  }

  @override
  void joinCommonRoom() {
    if (_hadJoinCommonRoom) return;
    unawaited(Future.delayed(Duration(seconds: 8), _joinCommonRoom));
  }

  @override
  void joinTurntableRoom({int? delaySeconds}) {
    if (_hadJoinTurntableRoom) return;
    Future.delayed(Duration(seconds: delaySeconds ?? 4), _joinTurntableRoom);
  }

  @override
  Future<int> joinRoom(String roomId) async {
    ///进房前都检查加入一下公共房间
    joinCommonRoom();
    joinTurntableRoom();

    final req = PbLocalRtmReq()
      ..type = RtmType.joinRoom
      ..params.addAll({
        "room_id": roomId,
      });
    final resp = await channelMsgService.send(PbLocalMsgType.PbLocalMsgType_RTM,
        req: req, resp: PbLocalRtmResp.create());

    Log.i(_tag, "joinRoom id = $roomId, code = ${resp?.code}");
    return resp?.code ?? -1;
  }

  Future<bool> leaveRoom(String roomId) async {
    final req = PbLocalRtmReq()
      ..type = RtmType.leaveRoom
      ..params.addAll({
        "room_id": roomId,
      });
    final resp = await channelMsgService.send(PbLocalMsgType.PbLocalMsgType_RTM,
        req: req, resp: PbLocalRtmResp.create());
    Log.i(_tag, "leaveRoom id = $roomId, code = ${resp?.code}");
    if (resp?.code == 1) {
      return true;
    }
    return false;
  }

  void _onLoginSuccess(String uid) {
    _rtmLogin();
  }

  Future<bool> _rtmLogin() async {
    _inLogin = true;
    final tokenResp = await liveApi.getRtmToken();
    if (tokenResp.isSuccess && tokenResp.data?.token?.isNotEmpty == true) {
      final uid = accountService.currentUid();
      if (uid != null && uid.isNotEmpty) {
        final req = PbLocalRtmReq()
          ..type = RtmType.login
          ..params.addAll({"uid": uid, "token": tokenResp.data?.token ?? ''});
        final resp = await channelMsgService.send(
            PbLocalMsgType.PbLocalMsgType_RTM,
            req: req,
            resp: PbLocalRtmResp.create());

        Log.i(_tag, "login code = ${resp?.code}");
        final isSuccess = resp?.code == 1;
        if (isSuccess) {
          joinCommonRoom();
          _hadLogin = true;
        }
        _inLogin = false;
        return isSuccess;
      }
    }
    _inLogin = false;
    return false;
  }

  void _onLogoutSuccess(String uid) {
    _inLogin = false;
    _hadLogin = false;
    final req = PbLocalRtmReq()..type = RtmType.logout;
    channelMsgService
        .send(PbLocalMsgType.PbLocalMsgType_RTM,
            req: req, resp: PbLocalRtmResp.create())
        .then((value) {
      Log.i(_tag, "logout code = ${value?.code}");
    });
  }

  @override
  Future<bool> sendRoomMsg(String roomId, PbBizPush pb,
      {String? roomMode}) async {
    pb.fromUid = accountService.currentUid() ?? "";
    final content = base64Encode(pb.writeToBuffer());
    if (content.isEmpty) return false;
    final res = await liveApi.sendRtmMsg(
        roomId: roomId, origin: content, roomMode: roomMode);
    return res.isSuccess;
    // final req = PbLocalRtmReq()
    //   ..type = RtmType.sendMsg
    //   ..params.addAll({"room_id": roomId, "text": content});
    // channelMsgService.send(PbLocalMsgType.PbLocalMsgType_RTM,
    //     req: req, resp: PbLocalRtmResp.create());
  }

  @override
  Future<GeneratedMessage?> onChanelMsgHandle({List<int>? data}) async {
    if (data != null) {
      int time = DateTime.now().millisecondsSinceEpoch;
      final req = PbLocalRtmReq.fromBuffer(data);

      switch (req.type) {
        case RtmType.disconnect:
          return _onDisconnect(req.params);
        case RtmType.receivedMsg:
          return _onReceivedMsg(req.params, time);
      }
    }

    return null;
  }

  Future<void> _joinCommonRoom() async {
    final req = PbLocalRtmReq()
      ..type = RtmType.joinRoom
      ..params.addAll({
        "room_id": _commonRoomId,
      });
    final resp = await channelMsgService.send(PbLocalMsgType.PbLocalMsgType_RTM,
        req: req, resp: PbLocalRtmResp.create());
    Log.i(_tag, "joinRoom id = $_commonRoomId, code = ${resp?.code}");

    if (resp?.code == 1) {
      _hadJoinCommonRoom = true;
    }
  }

  /// 加入转盘游戏房间
  Future<void> _joinTurntableRoom() async {
    final req = PbLocalRtmReq()
      ..type = RtmType.joinRoom
      ..params.addAll({
        "room_id": _turntableRoomId,
      });
    final resp = await channelMsgService.send(PbLocalMsgType.PbLocalMsgType_RTM,
        req: req, resp: PbLocalRtmResp.create());
    Log.i(_tag,
        "_joinTurntableRoom id = $_turntableRoomId, code = ${resp?.code}");

    if (resp?.code == 1) {
      _hadJoinTurntableRoom = true;
    }
  }

  Future<PbLocalRtmResp?> _onReceivedMsg(Map<String, String> params, int time) {
    Log.i(_tag, "_onReceivedMsg");
    final msg = params["msg"];
    if (msg?.isNotEmpty == true) {
      notificationService.onReceived(msg,
          isRoom: true, time: time, from: SignalingType.rtm);
    }
    return Future.value(PbLocalRtmResp.create()..code = 1);
  }

  Future<PbLocalRtmResp?> _onDisconnect(Map<String, String> params) {
    _hadJoinCommonRoom = false;
    if (roomService.isInLive()) {
      rxUtil.send(RoomEvent.imRoomLeave, roomService.getCurrentRoomId() ?? '');
    }
    return Future.value(PbLocalRtmResp.create()..code = 1);
  }

  @override
  PbLocalMsgType type() {
    return PbLocalMsgType.PbLocalMsgType_RTM;
  }
}
