import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/modules/bottle/model/bottle_detail.dart';
import 'package:service/modules/im/abs_im_msg_service.dart';
import 'package:service/modules/im/handler/chat_handler.dart';
import 'package:service/modules/im/model/activity_msg_content.dart';
import 'package:service/modules/im/model/audio_msg_content.dart';
import 'package:service/modules/im/model/bottle_msg_content.dart';
import 'package:service/modules/im/model/cp_match_msg_content.dart';
import 'package:service/modules/im/model/cp_msg_content.dart';
import 'package:service/modules/im/model/empty_msg_content.dart';
import 'package:service/modules/im/model/icebreaker_msg_content.dart';
import 'package:service/modules/im/model/im_gift_message_content.dart';
import 'package:service/modules/im/model/image_msg_content.dart';
import 'package:service/modules/im/model/local_msg_content.dart';
import 'package:service/modules/im/model/msg.dart';
import 'package:service/modules/im/model/msg_content.dart';
import 'package:service/modules/im/model/pb_msg_content.dart';
import 'package:service/modules/im/model/punch_msg_content.dart';
import 'package:service/modules/im/model/refer_multimedia_msg_content.dart';
import 'package:service/modules/im/model/reference_msg_content.dart';
import 'package:service/modules/im/model/system_msg_content.dart';
import 'package:service/modules/im/model/text_msg_content.dart';
import 'package:service/modules/im/model/video_msg_content.dart';
import 'package:service/modules/im/model/vip_msg_content.dart';
import 'package:service/modules/im/model/voice_match_msg_content.dart';
import 'package:service/modules/im/rong_cloud/convert/const_converter.dart';
import 'package:service/modules/im/rong_cloud/model/activity_message.dart';
import 'package:service/modules/im/rong_cloud/model/bottle_message.dart';
import 'package:service/modules/im/rong_cloud/model/custom_audio_message.dart';
import 'package:service/modules/im/rong_cloud/model/custom_image_message.dart';
import 'package:service/modules/im/rong_cloud/model/custom_pb_message.dart';
import 'package:service/modules/im/rong_cloud/model/custom_video_message.dart';
import 'package:service/modules/im/rong_cloud/model/empty_message.dart';
import 'package:service/modules/im/rong_cloud/model/icebreaker_message.dart';
import 'package:service/modules/im/rong_cloud/model/local_message.dart';
import 'package:service/modules/im/rong_cloud/model/refer_multimedia_message.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/pb/net/pb_system.pbserver.dart';
import 'package:service/service.dart';

/// 融云Message => 业务Msg
Future<Msg?> convertRCMessage(RCIMIWMessage? message) async {
  if (message != null) {
    final msgId = message.messageId ?? -1;
    // 1vs1 Private msg filter
    if (message.conversationType == RCIMIWConversationType.private && message.expansion != null) {
      String? showUid = message.expansion?[expansionShowUid];
      if (showUid?.trim().isNotEmpty == true &&
          accountService.currentUid()?.trim().isNotEmpty == true &&
          showUid != accountService.currentUid()) {
        Log.d('chat', 'convertRCMessage filter msg,messageUId=${message.messageUId}');
        return null;
      }
    }

    final content = await convertRCMessageContent(
      message,
      targetId: message.targetId,
      messageId: message.messageId,
    );

    /// 统一添加附属字段
    content?.starlightInfo = message.extraInfo?.starlightInfo;

    return Msg(
      id: msgId,
      targetId: message.targetId ?? '',
      chatType: getChatType(message.conversationType),
      direction: getMsgDirection(message.direction),
      senderUid: message.senderUserId ?? '',
      status: getMsgStatus(message.sentStatus),
      receiveStatus: getMsgReceiveStatus(message.receivedStatus),
      sentTime: message.sentTime ?? 0,
      content: content,
      msgUid: message.messageUId ?? '',
      // canIncludeExpansion: message.canIncludeExpansion,
      expansionDic: message.expansion,
      extra: message.extra,
      versionCode: message.extraInfo?.versionCode,
    );
  } else {
    return null;
  }
}

/// 融云MessageContent => 业务MsgContent
/// [targetId] 必传的情况：1、需要判断是否违规消息、是否取消隐藏；2、通知消息类型
/// [cancelHidden] 是否风险消息且用户点击了取消隐藏
///
Future<MsgContent?> convertRCMessageContent(RCIMIWMessage? content,
    {bool? cancelHidden, String? targetId, int? messageId}) async {
  MsgContent? msgContent;
  if (content is RCIMIWTextMessage) {
    msgContent = _convertRCCustomMessage(content, targetId)..atUserMap = content.extraInfo?.atUserMap;
  } else if (content is RCIMIWImageMessage) {
    msgContent = ImageMsgContent(
      localPath: _handlePathForMsgContent(content.local),
      imageUrl: content.remote,
      // thumbUrl: content.mThumbUri,
      thumbData:
          content.thumbnailBase64String?.isNotEmpty == true ? base64.decode(content.thumbnailBase64String!) : null,
      width: content.width,
      height: content.height,
    );
  } else if (content is RCIMIWVoiceMessage) {
    msgContent = AudioMsgContent(
      localPath: _handlePathForMsgContent(content.local),
      remoteUrl: content.remote,
      duration: content.duration ?? 0,
    );
  } else if (content is RCIMIWSightMessage) {
    final localPath = _handlePathForMsgContent(content.local);
    Uint8List? thumbData;
    if (localPath != null) {
      thumbData = await commonService.getVideoThumbnailData(localPath);
    } else {
      thumbData =
          content.thumbnailBase64String?.isNotEmpty == true ? base64.decode(content.thumbnailBase64String!) : null;
    }
    msgContent = VideoMsgContent(
      localPath: localPath,
      remoteUrl: content.remote,
      // thumbUri: content.mThumbUri,
      thumbData: thumbData,
      duration: content.duration ?? 0,
      size: content.size ?? 0,
      width: content.width,
      height: content.height,
    );
  } else if (content is RCIMIWReferenceMessage) {
    final msg = await convertRCMessageContent(content.referenceMessage);
    msgContent = ReferenceMsgContent(
        fromSelf: content.direction == RCIMIWMessageDirection.send,
        content: content.text ?? '',
        referMsgUserId: content.referenceMessage?.senderUserId,
        referMsg: msg,
        mentionedInfo: content.mentionedInfo,
        referMsgId: content.referenceMessage?.messageId,
        referMsgUId: content.referenceMessage?.messageUId)
      ..atUserMap = content.extraInfo?.atUserMap;
  }
  if (msgContent != null && content != null) {
    msgContent
      ..destructAfterView = (content.extraInfo?.isDestructMessage ?? false)
      ..alert = content.extraInfo?.alert ?? false
      ..pushData = content.extraInfo?.pushData
      ..serverType = content.extraInfo?.serverType;
    if (msgContent.alert) {
      if (messageId != null) {
        final cancelHidden = await getService<AbsIMMsgService>()?.isMsgUnhidden(messageId);
        msgContent.cancelHidden = cancelHidden == true;
      } else if (cancelHidden != null) {
        msgContent.cancelHidden = cancelHidden;
      }
    }
  }
  return msgContent ?? _unknownMsgContent();
}

MsgContent _convertRCCustomMessage(RCIMIWTextMessage text, String? targetId) {
  switch (text.type) {
    case CustomImageMessage.msgType:
      final content = CustomImageMessage.fromText(text);
      final thumbData = content.thumbnailBase64String != null ? base64Decode(content.thumbnailBase64String!) : null;
      return ImageMsgContent(
        localPath: _handlePathForMsgContent(content.localPath),
        imageUrl: content.imageUrl,
        thumbUrl: content.thumbUrl,
        thumbData: thumbData,
        width: content.width,
        height: content.height,
      );
    case CustomAudioMessage.msgType:
      final content = CustomAudioMessage.fromText(text);
      return AudioMsgContent(
        localPath: _handlePathForMsgContent(content.localPath),
        remoteUrl: content.remoteUrl,
        duration: content.duration,
      );
    case CustomVideoMessage.msgType:
      final content = CustomVideoMessage.fromText(text);
      return VideoMsgContent(
        localPath: _handlePathForMsgContent(content.localPath),
        remoteUrl: content.remoteUrl,
        thumbUri: content.thumbUrl,
        duration: content.duration,
        size: content.size,
        width: content.width,
        height: content.height,
      );
    case IcebreakerMessage.msgType:
      final content = IcebreakerMessage.fromText(text);
      return IcebreakerMsgContent(
          matching: content.matching ?? '',
          info: content.info ?? [],
          pbImChatSafeMode: content.pbImChatSafeMode,
          winkStatus: content.winkStatus,
          version: content.version);
    case RefMultiMediaMsgMessage.msgType:
      final content = RefMultiMediaMsgMessage.fromText(text);
      return RefMultiMediaMsgContent(
        fromSelf: content.direction == RCIMIWMessageDirection.send,
        mediaType: content.mediaType ?? '',
        source: content.source ?? '',
        momentId: content.momentId ?? '',
        userId: content.userId,
        userName: content.userName,
        avatarUrl: content.avatarUrl,
        avatarCode: content.avatarCode,
        mediaUrl: content.mediaUrl,
        desText: content.desText,
        content: content.content ?? '',
        duration: content.duration,
      );
    case BottleMessage.msgType:
      final content = BottleMessage.fromText(text);
      return BottleMsgContent(bottleDetail: content.bottleDetail ?? BottleDetail());
    case ActivityMessage.msgType:
      final content = ActivityMessage.fromText(text);
      return ActivityMsgContent(
        title: content.titleString,
        coverUrl: content.coverUrl ?? '',
        text: content.textString,
        link: content.link ?? '',
        id: content.id ?? '',
        activityId: content.activityId ?? '',
      );
    case CustomPbMessage.msgType:
      final content = CustomPbMessage.fromText(text);
      final pbContent = PbMsgContent(content.imMsg);
      if (pbContent.im?.type == PbCustomImType.PbCustomImType_SYSTEM) {
        return _parseSystemMsgContent(pbContent, targetId);
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_VOICE_MATCH) {
        return VoiceMatchMsgContent(content: pbContent);
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_CP) {
        return CpMsgContent(content: pbContent);
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_CP_MATCH) {
        return CpMatchMsgContent(content: pbContent);
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_VIP_LEVEL_UPGRADE) {
        return VipUpgradeMsgContent(content: pbContent);
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_PUNCH_MSG) {
        var result = _parsePunchMsgContent(pbContent, targetId);
        return result;
      } else if (pbContent.im?.type == PbCustomImType.PbCustomImType_PRIVATE_GIFT_MSG) {
        var result = _parsePrivateChatGiftMsg(pbContent, targetId);
        return result;
      } else {
        return pbContent.support ? pbContent : _unknownMsgContent();
      }
    case LocalMessage.msgType:
      return LocalMsgContent(text: text.text);
    case EmptyMessage.msgType:
      return EmptyMsgContent();
    default:
      {
        if (text.type?.isNotEmpty == true) {
          /// 不支持
          return _unknownMsgContent();
        } else {
          return TextMsgContent(
              text.direction == RCIMIWMessageDirection.send, text.text ?? '', null, text.mentionedInfo);
        }
      }
  }
}

/// 不支持消息
MsgContent _unknownMsgContent() {
  final data =
      PbImSystem(type: PbSystemMsgType.PbSystemMsgType_UNKNOWN, content: LocaleStrings.instance.msgDescUnknown);
  return SystemMsgContent(data);
}

SystemMsgContent _parseSystemMsgContent(PbMsgContent content, String? targetId) {
  final data = PbImSystem.fromBuffer(content.im!.data);
  switch (data.type) {
    case PbSystemMsgType.PbSystemMsgType_BE_BLOCKED:
      return BeBlockMsgContent();
    case PbSystemMsgType.PbSystemMsgType_POST_MOMENT:
    // return PostMomentMsgContent();
    case PbSystemMsgType.PbSystemMsgType_SCREENSHOT:
      return ScreenCaptureMsgContent();
    case PbSystemMsgType.PbSystemMsgType_SEND_GIFT:
      return SendGiftTipMsgContent();
    case PbSystemMsgType.PbSystemMsgType_GUIDE_UNCOVER_IDENTITY:
      return GuideIdentityMsgContent(targetId);
    case PbSystemMsgType.PbSystemMsgType_UNCOVER_IDENTITY:
      return UncoverIdentityMsgContent();
    case PbSystemMsgType.PbSystemMsgType_BE_CAPTURED:
      return ScreenBeCapturedMsgContent(data: data);
    case PbSystemMsgType.PbSystemMsgType_FOLLOW:
    case PbSystemMsgType.PbSystemMsgType_BE_FOLLOWED:
      var msg = _parseSystemMsgContentWithUserInfo(data, targetId);
      return msg;
    case PbSystemMsgType.PbSystemMsgType_CHANGE_MODE_TIPS:
      return ModeTipsMsgContent(targetId);
    case PbSystemMsgType.PbSystemMsgType_COMPLETE_PROFILE_TIPS:
      return CompleteBioTipMsgContent();
    case PbSystemMsgType.PbSystemMsgType_INTIMACY_UPDATE:
      return IntimacyUpdateMsgContent(content: data.content);
    case PbSystemMsgType.PbSystemMsgType_GUIDE_NO_MONEY_TASK:
      return FreeMoneyTaskGuideMsgContent();
    default:
      return SystemMsgContent(data);
  }
}

SystemMsgContent _parseSystemMsgContentWithUserInfo(PbImSystem data, String? targetId) {
  switch (data.type) {
    case PbSystemMsgType.PbSystemMsgType_FOLLOW:
      return FollowMsgContent(targetId);
    case PbSystemMsgType.PbSystemMsgType_BE_FOLLOWED:
      return BeFollowedMsgContent(targetId);
    default:
      return SystemMsgContent(data);
  }
}

MsgContent _parsePunchMsgContent(PbMsgContent content, String? targetId) {
  final data = PbImPunchMsg.fromBuffer(content.im!.data);
  switch (data.punchType) {
    case punchTypeGameResult:
    case punchTypeJoinGame:
    case punchTypeQuestion:
      return PunchMsgContent(content: content);
  }
  return _unknownMsgContent();
}

MsgContent _parsePrivateChatGiftMsg(PbMsgContent content, String? targetId) {
  final data = PbImGiftMsg.fromBuffer(content.im!.data);
  return ImGiftMessageContent.fromPb(data, content: content);
}

/// 业务Msg => 融云Message
RCIMIWMessage convertMsg(Msg msg) {
  final message = msg.content != null ? convertMsgContent(msg.content!) : RCIMIWUnknownMessage.fromJson({});
  message.messageId = msg.id;
  message.targetId = msg.targetId;
  message.conversationType = getRCConversationType(msg.chatType);
  message.direction = getRCMessageDirection(msg.direction);
  message.senderUserId = msg.senderUid;
  message.sentStatus = getRCSentStatus(msg.status);
  message.receivedStatus = getRCReceiveStatus(msg.receiveStatus);
  message.sentTime = msg.sentTime;
  // message.content = msg.content != null ? convertMsgContent(msg.content!) : null;
  message.messageUId = msg.msgUid;
  return message;

  // return Message()
  //   ..messageId = msg.id
  //   ..targetId = msg.targetId
  //   ..conversationType = getRCConversationType(msg.chatType)
  //   ..messageDirection = getRCMessageDirection(msg.direction)
  //   ..senderUserId = msg.senderUid
  //   ..sentStatus = getRCSentStatus(msg.status)
  //   ..receivedStatus = getRCReceiveStatus(msg.receiveStatus)
  //   ..sentTime = msg.sentTime
  //   ..content = msg.content != null ? convertMsgContent(msg.content!) : null
  //   ..messageUId = msg.msgUid
  //   ..objectName = _getObjName(msg.content);
}

// String? _getObjName(MsgContent? content) {
//   if (content == null) return null;
//   switch (content.runtimeType) {
//     case ImageMsgContent:
//       return ImageMessage.objectName;
//     case AudioMsgContent:
//       return VoiceMessage.objectName;
//     case VideoMsgContent:
//       return SightMessage.objectName;
//     case ReferenceMsgContent:
//       return ReferenceMessage.objectName;
//     default:
//       return TextMessage.objectName;
//   }
// }

/// 业务MessageContent => 融云MessageContent
///
/// [useRCStorage] 是否使用融云服务存储保存多媒体内容
RCIMIWMessage convertMsgContent(
  MsgContent content, {
  bool useRCStorage = true,
  RCIMIWConversationType conversationType = RCIMIWConversationType.private,
}) {
  RCIMIWMessage messageContent;
  if (content is ReferenceMsgContent) {
    final rMessage = content.referMsg != null ? convertMsgContent(content.referMsg!) : null;
    rMessage?.messageId = content.referMsgId;
    rMessage?.messageUId = content.referMsgUId;
    rMessage?.senderUserId = content.referMsgUserId;
    messageContent = RCIMIWReferenceMessage.create(
      text: content.content,
      referenceMessage: rMessage,
    );
  } else if (content is ActivityMsgContent) {
    messageContent = ActivityMessage(coverUrl: content.coverUrl, text: content.text, link: content.link);
  } else if (content is IcebreakerMsgContent) {
    messageContent = IcebreakerMessage(
        matching: content.matching,
        info: content.info,
        pbImChatSafeMode: content.pbImChatSafeMode,
        winkStatus: content.winkStatus,
        version: content.version);
  } else if (content is RefMultiMediaMsgContent) {
    messageContent = RefMultiMediaMsgMessage(
      mediaType: content.mediaType,
      source: content.source,
      momentId: content.momentId,
      userId: content.userId,
      userName: content.userName,
      avatarUrl: content.avatarUrl,
      avatarCode: content.avatarCode,
      mediaUrl: content.mediaUrl,
      desText: content.desText,
      content: content.content,
      duration: content.duration,
    );
  } else if (content is BottleMsgContent) {
    messageContent = BottleMessage(bottleDetail: content.bottleDetail);
  } else if (content is TextMsgContent) {
    messageContent = RCIMIWTextMessage.create(text: content.content, type: content.extraType)
      ..mentionedInfo = content.mentionedInfo;
    // ..forceSend = content.forceSend;
  } else if (content is ImageMsgContent) {
    final localPath = _handlePathForRCMessageContent(content.localPath);
    final thumbData = (content.thumbData?.isNotEmpty == true) ? base64Encode(content.thumbData!) : null;
    messageContent = useRCStorage
        ? RCIMIWImageMessage.create(
            local: localPath,
            remote: content.imageUrl,
            thumbnailBase64String: thumbData,
            width: content.width,
            height: content.height)
        : CustomImageMessage(
            localPath: localPath,
            imageUrl: content.imageUrl,
            thumbUrl: content.thumbUrl,
            thumbnailBase64String: thumbData,
            width: content.width,
            height: content.height,
          );
  } else if (content is AudioMsgContent) {
    final localPath = _handlePathForRCMessageContent(content.localPath);
    messageContent = useRCStorage
        ? RCIMIWVoiceMessage.create(local: localPath, remote: content.remoteUrl, duration: content.duration)
        : CustomAudioMessage(
            localPath: localPath,
            remoteUrl: content.remoteUrl,
            duration: content.duration,
          );
  } else if (content is VideoMsgContent) {
    final localPath = _handlePathForRCMessageContent(content.localPath);
    messageContent = useRCStorage
        ? RCIMIWSightMessage.create(
            local: localPath,
            remote: content.remoteUrl,
            thumbnailBase64String: (content.thumbData?.isNotEmpty == true) ? base64Encode(content.thumbData!) : null,
            size: content.size,
            duration: content.duration,
            width: content.width,
            height: content.height)
        : CustomVideoMessage(
            localPath: localPath,
            remoteUrl: content.remoteUrl,
            thumbUrl: content.thumbUri,
            duration: content.duration,
            size: content.size,
            width: content.width,
            height: content.height,
          );
  } else if (content is PbMsgContent) {
    messageContent = CustomPbMessage(imMsg: content.im);
  } else if (content is LocalMsgContent) {
    messageContent = LocalMessage(text: content.text);
  } else if (content is EmptyMsgContent) {
    messageContent = EmptyMessage();
  } else {
    messageContent = EmptyMessage();
  }
  final extraInfo = RCIMMessageExtraInfo(
    isDestructMessage: content.destructAfterView,
    alert: content.alert,
    serverType: content.serverType,
    atUserMap: content.atUserMap,
  )..forceSend = content.forceSend;
  return messageContent
    ..conversationType = conversationType
    ..extraInfo = extraInfo;
}

const _fileProto = 'file://';

/// MsgContent => MessageContent
String _handlePathForRCMessageContent(String? localPath) {
  if (localPath == null) return '';
  if (Platform.isAndroid && !localPath.startsWith(_fileProto)) {
    localPath = '$_fileProto$localPath';
  }
  return localPath;
}

/// MessageContent => MsgContent
String? _handlePathForMsgContent(String? localPath) {
  if (localPath == null) return null;
  if (Platform.isAndroid && localPath.startsWith(_fileProto)) {
    localPath = localPath.replaceFirst(_fileProto, '');
  }
  return localPath;
}

PbBizPush? getMsgPushPbData(Msg? msg) {
  PbBizPush? push;
  if (msg?.content?.pushData?.isNotEmpty == true) {
    try {
      push = PbBizPush.fromBuffer(base64Decode(msg!.content!.pushData!));
    } on Exception catch (e) {
      Log.i('msgConverter', "catch Exception $e");
      buglyLog(msg: 'getMsgPushPbData err->${e.toString()}');
    }
  }
  return push;
}
