// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'family_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FamilyConfigModel _$FamilyConfigModelFromJson(Map<String, dynamic> json) =>
    FamilyConfigModel(
      coolingOffDays: dynamicToString(json['cooling_off_days']),
      level: (json['level'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            e == null
                ? null
                : FamilyLevelConfig.fromJson(e as Map<String, dynamic>)),
      ),
      rankTabs: (json['rank_tabs'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      settingEntrance: json['setting_entrance'] == null
          ? null
          : FamilySettingEntranceConfig.fromJson(
              json['setting_entrance'] as Map<String, dynamic>),
      luckyBag: json['lucky_bag'] == null
          ? null
          : FamilyLuckyBagConfig.fromJson(
              json['lucky_bag'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FamilyConfigModelToJson(FamilyConfigModel instance) =>
    <String, dynamic>{
      'cooling_off_days': instance.coolingOffDays,
      'level': instance.level,
      'rank_tabs': instance.rankTabs,
      'setting_entrance': instance.settingEntrance,
      'lucky_bag': instance.luckyBag,
    };

FamilyLevelConfig _$FamilyLevelConfigFromJson(Map<String, dynamic> json) =>
    FamilyLevelConfig(
      badge: dynamicToString(json['badge']),
      badgeBg: dynamicToString(json['badge_bg']),
      icon: dynamicToString(json['icon']),
      bgColors: (json['bg_colors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FamilyLevelConfigToJson(FamilyLevelConfig instance) =>
    <String, dynamic>{
      'badge': instance.badge,
      'badge_bg': instance.badgeBg,
      'icon': instance.icon,
      'bg_colors': instance.bgColors,
    };

FamilySettingEntranceConfig _$FamilySettingEntranceConfigFromJson(
        Map<String, dynamic> json) =>
    FamilySettingEntranceConfig()
      ..url = dynamicToString(json['url'])
      ..icon = dynamicToString(json['icon']);

Map<String, dynamic> _$FamilySettingEntranceConfigToJson(
        FamilySettingEntranceConfig instance) =>
    <String, dynamic>{
      'url': instance.url,
      'icon': instance.icon,
    };

FamilyLuckyBagConfig _$FamilyLuckyBagConfigFromJson(
        Map<String, dynamic> json) =>
    FamilyLuckyBagConfig()
      ..minDiamond = toInt(json['min_diamond'])
      ..minMember = toInt(json['min_member']);

Map<String, dynamic> _$FamilyLuckyBagConfigToJson(
        FamilyLuckyBagConfig instance) =>
    <String, dynamic>{
      'min_diamond': instance.minDiamond,
      'min_member': instance.minMember,
    };
