import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:service/utils/value_parsers.dart';

part 'family_config_model.g.dart';

/// 家族 云控配置
@JsonSerializable()
class FamilyConfigModel {
  @JsonKey(name: "cooling_off_days", fromJson: dynamicToString)
  final String? coolingOffDays;

  @JsonKey(name: "level")
  final Map<String, FamilyLevelConfig?>? level;

  @JsonKey(name: "rank_tabs")
  final List<String>? rankTabs;

  @JsonKey(name: "setting_entrance")
  final FamilySettingEntranceConfig? settingEntrance;

  @Json<PERSON>ey(name: "lucky_bag")
  final FamilyLuckyBagConfig? luckyBag;

  FamilyConfigModel({
    this.coolingOffDays,
    this.level,
    this.rankTabs,
    this.settingEntrance,
    this.luckyBag,
  });

  factory FamilyConfigModel.fromJson(Map<String, dynamic> json) => _$FamilyConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyConfigModelToJson(this);

  FamilyLevelConfig? infoByLevel(int lv) {
    if (level == null) return null;
    if (level?.isEmpty ?? true) return null;

    if (level!.containsKey('$lv') == false) return null;

    return level!['$lv'];
  }
}

/// 家族等级 云控配置
@JsonSerializable()
class FamilyLevelConfig {
  /// 徽章icon
  @JsonKey(name: "badge", fromJson: dynamicToString)
  String? badge;

  /// 徽章背景
  @JsonKey(name: "badge_bg", fromJson: dynamicToString)
  String? badgeBg;

  /// 等级icon
  @JsonKey(name: "icon", fromJson: dynamicToString)
  String? icon;

  /// 名称背景渐变色
  @JsonKey(name: "bg_colors")
  List<String>? bgColors;

  FamilyLevelConfig({
    this.badge,
    this.badgeBg,
    this.icon,
    this.bgColors,
  });

  factory FamilyLevelConfig.fromJson(Map<String, dynamic> json) {
    return _$FamilyLevelConfigFromJson(json);
  }

  Map<String, dynamic> toJson() => _$FamilyLevelConfigToJson(this);
}

@JsonSerializable()
class FamilySettingEntranceConfig {
  @JsonKey(name: "url", fromJson: dynamicToString)
  String? url;

  @JsonKey(name: "icon", fromJson: dynamicToString)
  String? icon;

  FamilySettingEntranceConfig();

  factory FamilySettingEntranceConfig.fromJson(Map<String, dynamic> json) =>
      _$FamilySettingEntranceConfigFromJson(json);

  Map<String, dynamic> toJson() => _$FamilySettingEntranceConfigToJson(this);
}

@JsonSerializable()
class FamilyLuckyBagConfig {

  @JsonKey(name: "min_diamond", fromJson: toInt)
  int? minDiamond;
  
  @JsonKey(name: "min_member", fromJson: toInt)
  int? minMember;

  FamilyLuckyBagConfig();

  factory FamilyLuckyBagConfig.fromJson(Map<String, dynamic> json) => _$FamilyLuckyBagConfigFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyLuckyBagConfigToJson(this);
}