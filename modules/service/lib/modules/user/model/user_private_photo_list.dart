

import 'package:service/global/model/safe_convert.dart';

class UserPrivatePhotoList {
  final List<UserPrivatePhotoItem>? list;
  final bool canView;

  UserPrivatePhotoList({
    required this.list,
    this.canView = false,
  });

  factory UserPrivatePhotoList.fromJson(Map<String, dynamic>? json) => UserPrivatePhotoList(
    list: asNT<List>(json, 'list')?.map((e) => UserPrivatePhotoItem.fromJson(e)).toList(),
    canView: asT<bool>(json, 'can_view'),
  );

  Map<String, dynamic> toJson() => {
    'list': list?.map((e) => e.toJson()).toList(),
    "can_view": canView
  };
}

class  UserPrivatePhotoItem {
  final int id;
  final String photoUrl;

  UserPrivatePhotoItem({
    this.id = 0,
    this.photoUrl = "",
  });

  factory UserPrivatePhotoItem.fromJson(Map<String, dynamic>? json) => UserPrivatePhotoItem(
    id: asT<int>(json, 'id'),
    photoUrl: asT<String>(json, 'photo_url'),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'photo_url': photoUrl,
  };
}

