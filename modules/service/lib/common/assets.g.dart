/// 该类代码由ResourceScanner脚本自动生成
///
/// 请勿手动修改！！！
class Assets {
  Assets._();
  
  static const String aachCStop = 'assets/aach/c_stop.aac';
  static const String aachCallRing = 'assets/aach/call_ring.aac';
  static const String aachCpFailed = 'assets/aach/cp_failed.mp3';
  static const String aachCpHasMatch = 'assets/aach/cp_has_match.mp3';
  static const String aachFateBell = 'assets/aach/fate_bell.mp3';
  static const String aachTruthWheelRotate = 'assets/aach/truth_wheel_rotate.mp3';
  static const String aachTruthWheelSelected = 'assets/aach/truth_wheel_selected.mp3';
  static const String aachTruthWheelStart = 'assets/aach/truth_wheel_start.mp3';
  static const String confighSensitive = 'assets/configh/sensitive.json';
  static const String countryhCCode = 'assets/countryh/c_code.json';
  static const String feedFeedFollow = 'assets/feed/feed_follow.json';
  static const String gameEmp = 'assets/game/emp.zip';
  static const String htmlPrivacyEn = 'assets/html/privacy_en.html';
  static const String htmlPrivacyId = 'assets/html/privacy_id.html';
  static const String htmlTermsEn = 'assets/html/terms_en.html';
  static const String htmlTermsId = 'assets/html/terms_id.html';
  static const String liveAvatarRipple = 'assets/live/avatar_ripple.json';
  static const String liveCocosGameBanner = 'assets/live/cocos_game_banner.json';
  static const String liveUserInRoom = 'assets/live/user_in_room.json';
  static const String regionhRCodeE = 'assets/regionh/r_code_e.json';
  static const String voiceVoiceVerifyPlayFour = 'assets/voice/voice_verify_play_four.json';
  static const String voiceVoiceVerifyPlayer = 'assets/voice/voice_verify_player.json';
}
