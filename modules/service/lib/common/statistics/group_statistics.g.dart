import 'package:feature_flat_base/feature_flat_base.dart';

class GroupStatistics {
  GroupStatistics._();

  /// 群聊内点击开宝箱玩法入口
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  static void reportMagicboxEntranceClick({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊开宝箱任务入口展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  static void reportMagicboxEntranceImp({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [page] 上报当前在哪个tab，切换一次上报一次，open=开宝箱页面；send=赠送页面。
  /// [familyId]
  static void reportMagicboxImp({
    String? uid,
    String? gender,
    String? groupId,
    String? page,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'page': page ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法展示索取钥匙信息或者赠送钥匙信息时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] request=索取列表 / send=赠送列表
  static void reportMagicboxMessageImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_message_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击索取消息的赠送钥匙按钮
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  /// [groupId]
  /// [toUid]
  /// [toGender]
  static void reportMagicboxMessageSend({
    String? uid,
    String? gender,
    String? familyId,
    String? groupId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_message_send',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'group_id': groupId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法点击开启宝箱时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  /// [groupId]
  /// [content] 上报宝箱的奖励包id
  static void reportMagicboxOpenClick({
    String? uid,
    String? gender,
    String? familyId,
    String? groupId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_open_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'group_id': groupId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法界面点击索取钥匙时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  static void reportMagicboxRequestClick({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_request_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法接口请求结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  /// [groupId]
  /// [type] 请求类型，send=赠送，request=索取，open=开宝箱
  /// [result] succ/fail
  /// [reason] 失败原因，上报错误码
  /// [duration] 请求时长
  /// [content] 如是开启宝箱，上报宝箱的奖励包id
  static void reportMagicboxRequestEnd({
    String? uid,
    String? gender,
    String? familyId,
    String? groupId,
    String? type,
    String? result,
    String? reason,
    String? duration,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'group_id': groupId ?? '',
        'type': type ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法点击赠送钥匙给他人时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  static void reportMagicboxSendClick({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_send_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊开宝箱玩法点击
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，上报当前点击按钮状态为1=领取奖励或0=跳转deeplink
  /// [content] 上报点击的action code
  /// [familyId]
  /// [groupId]
  static void reportMagicboxTaskClick({
    String? uid,
    String? gender,
    String? status,
    String? content,
    String? familyId,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_task_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'content': content ?? '',
        'family_id': familyId ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法点击用户列表索取或者赠送钥匙时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  /// [toUid] 如果是索取群聊则上报group，索取人则上报对方的uid
  /// [toGender]
  /// [type] request=索取列表 / send=赠送列表
  static void reportMagicboxUserlistClick({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    String? toUid,
    String? toGender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_userlist_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 开宝箱玩法点击索取/赠送查看用户列表时
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [familyId]
  /// [type] request=索取列表 / send=赠送列表
  static void reportMagicboxUserlistImp({
    String? uid,
    String? gender,
    String? groupId,
    String? familyId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'magicbox_userlist_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'family_id': familyId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊AT消息发送成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] AT所有人则报ALL
  /// [groupId]
  static void reportMsgAtSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_at_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊内点击顶部在房用户头像进入房间
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId] 点击进入的语音房ID
  /// [groupId]
  static void reportMsgGroupEnterRoom({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_group_enter_room',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊消息面板展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [from] 打开来源，chat_list聊天会话列表/push推送；进入消息列表的方式/ room房间内打开
  static void reportMsgListGroupImp({
    String? uid,
    String? gender,
    String? groupId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_group_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击开金库玩法入口
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [status] 当前是否可开奖，0=不可领奖，1=可领奖时
  /// [from] screen=公屏信息，entrance=常驻入口
  static void reportVaultEntranceClick({
    String? uid,
    String? gender,
    String? groupId,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 开金库玩法入口展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  static void reportVaultEntranceImp({
    String? uid,
    String? gender,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 开金库点击瓜分奖励
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [num] 上报当前可瓜分的百分比
  static void reportVaultGetRewards({
    String? uid,
    String? gender,
    String? groupId,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_get_rewards',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 点击瓜分奖励结果弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [num] 上报当前可瓜分的百分比
  /// [content] 上报瓜分到的钻石数量
  static void reportVaultGetRewardsResult({
    String? uid,
    String? gender,
    String? groupId,
    String? num,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_get_rewards_result',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'num': num ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊金库主界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [status] 当前是否可开奖，0=不可领奖，1=可领奖时
  static void reportVaultImp({
    String? uid,
    String? gender,
    String? groupId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 群聊金库任务点击时上报
  ///
  /// [uid]
  /// [gender]
  /// [groupId]
  /// [status] 0/1，上报当前点击按钮状态为1=领取奖励或0=跳转deeplink
  /// [content] 上报点击的action code
  static void reportVaultTaskGet({
    String? uid,
    String? gender,
    String? groupId,
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vault_task_get',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'group_id': groupId ?? '',
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }
}
