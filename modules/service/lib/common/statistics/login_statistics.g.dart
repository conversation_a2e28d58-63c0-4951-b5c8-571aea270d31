import 'package:feature_flat_base/feature_flat_base.dart';

class LoginStatistics {
  LoginStatistics._();

  /// 用户进入主界面时检测是否为多开
  ///
  /// [status] 0=非使用多开用户，1=使用多开用户
  static void reportDuaApp({
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'dua_app',
      params: {
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 访问winky按钮点击时上报
  ///
  /// [duration] 新手引导页面的停留时长，单位s
  static void reportIntroAccessClick({
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_access_click',
      params: {
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 访问winky按钮展示时上报
  static void reportIntroAccessImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_access_imp',
      priority: priority,
    );
  }

  /// 选择Avatar控件展示时上报
  static void reportIntroAvatarImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_avatar_imp',
      priority: priority,
    );
  }

  /// 提交选择的Avatar内容时上报
  ///
  /// [content] 选择的Avatar标识
  static void reportIntroAvatarSubmit({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_avatar_submit',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 选择生日控件展示时上报
  static void reportIntroBirthdayImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_birthday_imp',
      priority: priority,
    );
  }

  /// 提交生日时上报
  ///
  /// [content] dd-mm-yyyy
  static void reportIntroBirthdaySubmit({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_birthday_submit',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 性别控件展示时上报
  static void reportIntroGenderImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_gender_imp',
      priority: priority,
    );
  }

  /// 选择性别确认弹窗点击时上报
  ///
  /// [act] ok/cannel
  static void reportIntroGenderPopClick({
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_gender_pop_click',
      params: {
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 提交性别时上报
  ///
  /// [content] female/male，用户所选择的性别
  static void reportIntroGenderSubmit({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_gender_submit',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 输入name控件展示时上报
  static void reportIntroNameImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_name_imp',
      priority: priority,
    );
  }

  /// 提交name时上报
  ///
  /// [result] 提交结果succ/fail
  /// [content] 提交的name内容
  static void reportIntroNameSubmit({
    String? result,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_name_submit',
      params: {
        'result': result ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 新手流程页面展示时上报
  ///
  /// [status] 0：从验证流程开始，1：已验证，从完善个人资料开始
  static void reportIntroPageImp({
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_page_imp',
      params: {
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 提交个人信息返回结果时上报
  ///
  /// [result] succ/fail
  static void reportIntroRegisterCallback({
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_register_callback',
      params: {
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 回答问题有结果后上报
  ///
  /// [step] 数字，当前是第几题
  /// [content] 问题序号
  /// [result] right/wrong
  static void reportIntroVerifyAnswer({
    String? step,
    String? content,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_verify_answer',
      params: {
        'step': step ?? '',
        'content': content ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 有验证结果时上报
  ///
  /// [type] InviteCode：邀请码，AnswerTest：答题验证
  /// [result] succ/fail
  /// [duration] 验证耗时，从最后一次点击选择验证方式开始计时，单位s
  static void reportIntroVerifyCallback({
    String? type,
    String? result,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_verify_callback',
      params: {
        'type': type ?? '',
        'result': result ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 点击验证方式时上报
  ///
  /// [type] InviteCode：邀请码AnswerTest：答题验证
  static void reportIntroVerifyClick({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_verify_click',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 展示验证方式时上报
  static void reportIntroVerifyImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_verify_imp',
      priority: priority,
    );
  }

  /// 展示问题题目时上报
  ///
  /// [step] 数字，当前是第几题
  /// [content] 问题序号
  static void reportIntroVerifyQuestionImp({
    String? step,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intro_verify_question_imp',
      params: {
        'step': step ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 注册登录请求结束时
  ///
  /// [type] facebook/google/apple/phone
  /// [result] succ/fail
  /// [reason] 失败原因
  /// [onTime] 得到结果耗时：单位毫秒
  static void reportLogginRequestEnd({
    String? type,
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'loggin_request_end',
      params: {
        'type': type ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 注册登录发起请求
  ///
  /// [type] facebook/google/apple/phone
  static void reportLogginRequestStart({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'loggin_request_start',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 登录-请求第三方登录授权结束时
  ///
  /// [type] facebook/google/apple
  /// [result] succ/fail
  /// [reason] 失败原因
  /// [duration] 得到结果耗时：单位s
  /// [from] 上报来源，homepage=主页，phonelogin=otp验证码页
  static void reportLogin3rdAuthEnd({
    String? type,
    String? result,
    String? reason,
    String? duration,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_3rd_auth_end',
      params: {
        'type': type ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 登录-请求第三方登录授权开始时
  ///
  /// [type] facebook/google/apple/phoneLogin
  /// [from] 上报来源，homepage=主页，phonelogin=otp验证码页
  static void reportLogin3rdAuthStart({
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_3rd_auth_start',
      params: {
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 登录页面上选择某个登录方式时
  ///
  /// [type] facebook/google/apple/phoneLogin
  /// [status] agreed同意条款/non_agreed未同意条款
  /// [from] 上报来源，homepage=主页，phonelogin=otp验证码页
  static void reportLoginPageClick({
    String? type,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_page_click',
      params: {
        'type': type ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 登录页面展示
  static void reportLoginPageImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_page_imp',
      priority: priority,
    );
  }

  /// 验证码输入界面停留时长
  ///
  /// [duration] 记录时长
  /// [status] 0/1，上报验证码是否登陆成功，0不成功，1成功
  static void reportLoginPhoneDuration({
    String? duration,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_phone_duration',
      params: {
        'duration': duration ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 手机账号登陆点击Next请求验证码
  ///
  /// [result] succ/fail 记录是否成功进入到下一步页面
  /// [content] 用户手机号码记录
  static void reportLoginPhoneNext({
    String? result,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_phone_next',
      params: {
        'result': result ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 请求验证码
  ///
  /// [result] 是否请求成功；Succ/fail
  /// [type] 用户国家，记录用户手机MCC code
  /// [onTime] 得到结果耗时：单位毫秒
  static void reportLoginPhoneOtp({
    String? result,
    String? type,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_phone_otp',
      params: {
        'result': result ?? '',
        'type': type ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 登陆界面点击勾选隐私政策/用户协议选项
  ///
  /// [status] 上报点击后的状态，0=不同意协议，1=勾选同意协议。
  static void reportLoginPpClick({
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_pp_click',
      params: {
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 用户政策与隐私协议同意弹窗展示
  ///
  /// [from] facebook/google/apple/phoneLogin
  static void reportLoginPpPopout({
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_pp_popout',
      params: {
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击用户政策与隐私协议弹窗内按钮
  ///
  /// [from] facebook/google/apple/phoneLogin
  /// [type] agree=同意按钮，cancel=取消按钮
  static void reportLoginPpPopoutClick({
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_pp_popout_click',
      params: {
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 登陆失败挽留弹窗点击对应的选项时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报点击的选项，fb/google/apple/off，其中off=关闭弹窗
  static void reportLoginRetainClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_retain_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 登陆挽留弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报因为什么登录失败原因引起的挽留弹窗，fb / google/ phone/apple
  static void reportLoginRetainPopout({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'login_retain_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 新用户安装后启动过app且完成了账号注册，当该用户在本地时间第二天冷启动app时，进行打点，满足条件的用户只上报一次（用于次留优化，需要上报到FB）
  static void reportNdauAppOpen2d({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ndau_app_open_2d',
      priority: priority,
    );
  }

  /// 账号注册成功时（必须信息完善）
  ///
  /// [type] Facebook/google/apple/phoneLogin，用户登录方式
  /// [content] female/male，用户所选择的性别
  /// [status] 0=非使用多开用户，1=使用多开用户
  /// [name] 用户昵称
  /// [birthday] 生日 dd-mm-yyyy
  /// [avatar] 选择的Avatar标识
  static void reportRegisterSucc({
    String? type,
    String? content,
    String? status,
    String? name,
    String? birthday,
    String? avatar,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'register_succ',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'status': status ?? '',
        'name': name ?? '',
        'birthday': birthday ?? '',
        'avatar': avatar ?? '',
      },
      priority: priority,
    );
  }
}
