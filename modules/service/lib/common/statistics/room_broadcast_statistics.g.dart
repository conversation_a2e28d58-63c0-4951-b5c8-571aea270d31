import 'package:feature_flat_base/feature_flat_base.dart';

class RoomBroadcastStatistics {
  RoomBroadcastStatistics._();

  /// 全服广播点击跳转
  ///
  /// [uid]
  /// [gender]
  /// [fromUid] 中奖者uid
  /// [url] 活动链接
  /// [roomId] 曝光房间id
  static void reportRoomBroadcastClick({
    String? uid,
    String? gender,
    String? fromUid,
    String? url,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_broadcast_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from_uid': fromUid ?? '',
        'url': url ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 全服广播曝光
  ///
  /// [uid]
  /// [gender]
  /// [fromUid] 中奖者uid
  /// [url] 活动链接
  /// [roomId] 曝光房间id
  static void reportRoomBroadcastImp({
    String? uid,
    String? gender,
    String? fromUid,
    String? url,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_broadcast_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from_uid': fromUid ?? '',
        'url': url ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }
}
