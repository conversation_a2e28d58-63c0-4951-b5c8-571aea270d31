import 'package:feature_flat_base/feature_flat_base.dart';

class VerifyStatistics {
  VerifyStatistics._();

  /// 声音认证-审核失败结果展示页点击去认证
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyFailedGo({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_failed_go',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-审核失败结果弹窗展示。
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyFailedImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_failed_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-全屏引导弹窗点击去认证
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [from] 注册完成后/次日冷启动（尚未认证的用户）/老版本更新后，0/1/2 代替。
  /// [status] 0/1，0=正常，1=失败，判断是失败的页面还是正常页面
  static void reportVerifyFullguideGo({
    String? uid,
    String? gender,
    String? from,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_fullguide_go',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-全屏引导弹窗出现
  ///
  /// [uid] 当前弹窗的用户id
  /// [gender] 当前弹窗的用户性别
  /// [from] 注册完成后/次日冷启动（尚未认证的用户），0/1/代替。
  static void reportVerifyFullguideImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_fullguide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-全屏引导被用户跳过时
  ///
  /// [gender]
  /// [from] 注册完成后/次日冷启动（尚未认证的用户），0/1/代替。
  /// [status] 0/1，0=正常，1=失败，判断是失败的页面还是正常页面
  static void reportVerifyFullguideSkip({
    String? gender,
    String? from,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_fullguide_skip',
      params: {
        'gender': gender ?? '',
        'from': from ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 点击弹窗中去认证按钮
  ///
  /// [from] 展示弹窗来源，faker=点击假用户出现引导弹窗，chat=退出聊天出现认证引导。
  /// [uid]
  /// [gender]
  static void reportVerifyGuidePopoutClick({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_guide_popout_click',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证弹窗展示曝光
  ///
  /// [from] 展示弹窗来源，faker=点击假用户出现引导弹窗，chat=退出聊天出现认证引导。
  /// [uid]
  /// [gender]
  static void reportVerifyGuidePopoutImp({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_guide_popout_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-半屏引导弹窗点击认证
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [from] 使用声音速配功能向右滑时/ IM聊天时点击对方声音 /点击速配时；用0 /1 /2代替。
  static void reportVerifyHalfguideGo({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_halfguide_go',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-半屏引导弹窗出现
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [from] 使用声音速配功能向右滑时/ IM聊天时点击对方声音 /点击速配时；用0 /1 /2代替。
  static void reportVerifyHalfguideImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_halfguide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-自己从bio处点击声音认证编辑按钮
  ///
  /// [status] 当前按钮状态是审核中/审核通过，0/1上报，0为审核中，1为审核通过。
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyMybioClick({
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_mybio_click',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 引导发送声音认证声音的界面展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify / verify_mybio_click/ voicequick_verify_click/ verify_record_result_edit，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入/个人bio处点击进入/声音认证界面右上角认证按钮/录制结果重新编辑。
  /// [duration] 上报当前音频录音时长
  static void reportVerifyPostGuideImp({
    String? uid,
    String? gender,
    String? from,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_post_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 引导发送声音post点击分享
  ///
  /// [uid]
  /// [gender]
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify / verify_mybio_click/ voicequick_verify_click/ verify_record_result_edit，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入/个人bio处点击进入/声音认证界面右上角认证按钮/录制结果重新编辑。
  static void reportVerifyPostGuideShare({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_post_guide_share',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-认证流程点击开始录制
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [content] 上报当前页面的认证文案内容
  static void reportVerifyRecordClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-录制流程已录制结束后点击删除录音
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordDelete({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_delete',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-记录认证流程的时长
  ///
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入。
  /// [duration] 上报时长
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [status] 是否录制完成退出，0代表未录制完成退出，1代表已录制完成退出。
  static void reportVerifyRecordDuration({
    String? from,
    String? duration,
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_duration',
      params: {
        'from': from ?? '',
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-认证流程已录制完声音界面（包含手动暂停和超过60s自动暂停）
  ///
  /// [content] 上报录制的问题内容
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [duration] 录音时长
  /// [type] 录制暂停方式：manual=手动点击；timeout=超过60s自动暂停other=其他方式导致的暂停，比如录制过程中来电等
  /// [status] 声音状态上报，0/1，0无异常提示出现，1上报声音太小提示
  static void reportVerifyRecordEnd({
    String? content,
    String? uid,
    String? gender,
    String? duration,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_end',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-未完成认证过程，中途点击左上角退出按钮
  ///
  /// [status] 0/1，0代表未录制声音退出，1代表已录制声音退出。
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordExit({
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_exit',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-未完成认证中途退出二次确认弹窗展示
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [status] 报0/1代表弹窗展示位置，0代表未录制声音，1代表已录制声音。
  static void reportVerifyRecordExitpopout({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_exitpopout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 退出认证界面二次弹窗点击按钮
  ///
  /// [type] 上报点击的按钮类型，exit=点击退出按钮，continue=继续动作
  /// [uid]
  /// [gender]
  /// [status] 0/1，0代表未录制声音退出，1代表已录制声音退出。
  static void reportVerifyRecordExitpopoutClick({
    String? type,
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_exitpopout_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-新手引导展示点击
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordGuideClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_guide_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-认证界面展示
  ///
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify / verify_mybio_click/ voicequick_verify_click/ verify_record_result_edit/faker/chat，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入/个人bio处点击进入/声音认证界面右上角认证按钮/录制结果重新编辑/ 假用户弹窗/ 聊天引导弹窗
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [content] 上报当前界面认证内容
  static void reportVerifyRecordImp({
    String? from,
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-录制完成提交后审核中和审核成功的界面（同已录制提交后点击主动入口进入此界面）
  ///
  /// [status] 上报0/1 ，代表审核中/审核完成状态。
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordResult({
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_result',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-录制完成后点击再编辑
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordResultEdit({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_result_edit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-录音结束后点击提交
  ///
  /// [duration] 录音时长
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [content] 上报问题内容
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify / verify_mybio_click/ voicequick_verify_click/ verify_record_result_edit，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入/个人bio处点击进入/声音认证界面右上角认证按钮/录制结果重新编辑。
  static void reportVerifyRecordSubmit({
    String? duration,
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_submit',
      params: {
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-录音结果提交
  ///
  /// [result] succ/fail
  /// [reason] 失败原因
  /// [duration] 上报录音时长
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [content] 上报所选的分组，public=公共可见；friends=朋友可见；myself=自己可见。
  /// [from] 认证来源，报verify_fullscreenguide_goverify/ verify_halfscreenguide_goverify/ verify_failed_goverify / verify_mybio_click/ voicequick_verify_click/ verify_record_result_edit，分别是 全屏弹窗/半屏弹窗/审核失败弹窗页进入/个人bio处点击进入/声音认证界面右上角认证按钮/录制结果重新编辑。
  static void reportVerifyRecordSubmitEnd({
    String? result,
    String? reason,
    String? duration,
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_submit_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-认证流程中点击更换题目
  ///
  /// [content] 上报被更换的题目id
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVerifyRecordSwitch({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_record_switch',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音认证-点击他人认证声音（IM信息处/BIO处）
  ///
  /// [duration] 上报对方音频时长
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 对方id
  /// [toGender] 对方性别
  static void reportVerifyUseraudioplay({
    String? duration,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'verify_useraudioplay',
      params: {
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-功能使用时长
  ///
  /// [duration] 上报时长
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVoicequickDuration({
    String? duration,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_duration',
      params: {
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-新手引导点击
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVoicequickGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音速配-点击首页入口进入声音速配
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVoicequickHomeClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_home_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-点击进入后界面展示，每个卡片上报一次。
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 当前被匹配方id
  /// [toGender] 当前被匹配方性别
  static void reportVoicequickImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-右滑或点击喜欢
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 被匹配方id
  /// [toGender] 被匹配方性别
  /// [duration] 当前被匹配方的音频时长
  static void reportVoicequickLike({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_like',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-左滑或点击不喜欢按钮
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 被匹配方uid
  /// [toGender] 被匹配方性别
  /// [duration] 上报当前被匹配方音频时长
  static void reportVoicequickPass({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_pass',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-点击播放暂停按钮
  ///
  /// [status] 上报点击按钮后状态，0/1代表，0代表播放，1代表暂停
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 被匹配方id
  /// [toGender] 被匹配方性别
  /// [duration] 上报当前被匹配方音频时长
  static void reportVoicequickPlaypauseClick({
    String? status,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_playpause_click',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-匹配接口请求结束时
  ///
  /// [type] match 匹配信息，本地自然日刷新like  喜欢卡片dislike 不喜欢卡片
  /// [onTime] 耗时，毫秒。
  /// [reason] 失败原因
  /// [result] succ/fail
  static void reportVoicequickRequestEnd({
    String? type,
    String? onTime,
    String? reason,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_request_end',
      params: {
        'type': type ?? '',
        'on_time': onTime ?? '',
        'reason': reason ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-匹配接口请求
  ///
  /// [type] match 匹配信息，本地自然日刷新like  喜欢卡片dislike 不喜欢卡片
  static void reportVoicequickRequestStart({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_request_start',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-成功匹配弹窗出现
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 被匹配方用户id
  /// [toGender] 被匹配方用户性别
  static void reportVoicequickSuccImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_succ_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-匹配成功弹窗后，输入内容点击发送
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [toUid] 被匹配方id
  /// [toGender] 被配方性别
  static void reportVoicequickSuccSendmsg({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_succ_sendmsg',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配点击unlockmore增加次数按钮
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，是否有余额增加次数，0余额不足，1成功增加。
  static void reportVoicequickUnlockmoreClick({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_unlockmore_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-次数用完界面提示
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVoicequickUsedupImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_usedup_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音卡片速配-点击页面右上角认证按钮进入声音认证流程
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVoicequickVerifyClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voicequick_verify_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
