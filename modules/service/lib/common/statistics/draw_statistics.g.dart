import 'package:feature_flat_base/feature_flat_base.dart';

class DrawStatistics {
  DrawStatistics._();

  /// 签到转盘点击转动
  ///
  /// [uid]
  /// [gender]
  static void reportDrawClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜参赛者回答成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  /// [status] 当前是否答对，0=答错，1=答对
  static void reportDrawGuessAnswerSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_answer_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜作画者笔触结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  static void reportDrawGuessDraw({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_draw',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜作画者画图阶段展示上报	
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  /// [type] 三种身份视角上报，painter=作画者 / joiner=参赛者 / guest=观众
  static void reportDrawGuessDrawImp({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_draw_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  static void reportDrawGuessEndImp({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_end_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜新手引导展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  /// [content] 上报引导的种类，painter=作画者/ joiner=参赛者
  static void reportDrawGuessGuideImp({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜页面展示时上报，用于统计游戏覆盖渗透。
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报进入房间的上一级来源，客户端定义路由。
  static void reportDrawGuessImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜阶段结束时展示弹窗上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  /// [result] 正确和错误各自上播啊，正确=true/ 错误=false
  /// [num] 上报增加的分数
  static void reportDrawGuessResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    String? result,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
        'result': result ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜选择词界面点击选择
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId]
  /// [content] 上报所选择的词内容，上报词的全称；如果选择更换词上报Change
  static void reportDrawGuessSelectWordsClick({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_select_words_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 你画我猜选择词界面展示上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roundId] 上报当前轮次的唯一id
  static void reportDrawGuessSelectWordsImp({
    String? uid,
    String? gender,
    String? roomId,
    String? roundId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_guess_select_words_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'round_id': roundId ?? '',
      },
      priority: priority,
    );
  }

  /// 签到转盘展示
  ///
  /// [uid]
  /// [gender]
  /// [content] 当前签到的天数（弹窗页面展示的天数
  /// [from] 签到展出来源，auto=自动弹出，manual=手动点击
  static void reportDrawImp({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 签到转盘结果展示
  ///
  /// [content] 上报展示出来的奖励包id
  /// [uid]
  /// [gender]
  static void reportDrawResultImp({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_result_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
