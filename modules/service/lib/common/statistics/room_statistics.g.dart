import 'package:feature_flat_base/feature_flat_base.dart';

class RoomStatistics {
  RoomStatistics._();

  /// 相亲房气泡动画展示时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 用1和2代表当前气泡播放样式，1=初始化阶段，2=宣布阶段
  static void reportBlinddateAnimeImp({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_anime_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// host在第二轮点击麦位下方时宣布按钮时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid]
  /// [toGender]
  static void reportBlinddateAnnounceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_announce_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 相亲房第二阶段宣布结果弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被宣布的人的uid
  /// [toGender] 被宣布的人的性别
  /// [roomId]
  static void reportBlinddateAnnouncePopoutImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_announce_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 在相亲房中点击麦位下方取消选择他人时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid]
  /// [toGender]
  static void reportBlinddateCancelClick({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_cancel_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 相亲房入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前房间类型
  static void reportBlinddateEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 相亲房入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前房间类型
  static void reportBlinddateEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 进入相亲房，界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportBlinddateImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 相亲房互相pick中，匹配成功弹窗展示时
  ///
  /// [uid]
  /// [toUid] 当前匹配中的另一方uid
  /// [gender]
  /// [toGender]
  /// [roomId]
  /// [fuid] 当前匹配中的一方uid
  /// [fgender] 当前匹配中的一方uid
  static void reportBlinddateMatchPopoutImp({
    String? uid,
    String? toUid,
    String? gender,
    String? toGender,
    String? roomId,
    String? fuid,
    String? fgender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_match_popout_imp',
      params: {
        'uid': uid ?? '',
        'to_uid': toUid ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'fuid': fuid ?? '',
        'fgender': fgender ?? '',
      },
      priority: priority,
    );
  }

  /// host点击operation中按钮时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] 点击对应按钮上报，announce=进入下一轮宣布阶段，new round=新一轮游戏。
  /// [status] 上报当前是在什么阶段，pick=选择阶段，announce=宣布阶段
  static void reportBlinddateOperationClick({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_operation_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 在相亲房中点击麦位下方选择他人时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId]
  /// [status] 当前用户身份，host=主持人，joiner=其他麦上用户
  static void reportBlinddatePickClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_pick_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 拉黑房间成功时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房
  static void reportBlockRoomSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'block_room_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 魅力值计数器创建界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportCalculatorCreateImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'calculator_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 魅力值计数器创建完成时上报
  ///
  /// [uid]
  /// [gender]
  /// [duration] 上报创建的持续时长
  /// [roomId]
  static void reportCalculatorCreateSucc({
    String? uid,
    String? gender,
    String? duration,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'calculator_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 魅力值计数器入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportCalculatorEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'calculator_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 魅力值计数器入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportCalculatorEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'calculator_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 魅力值计数器单次结束，展示结果弹窗时上报
  ///
  /// [uid]
  /// [gender]
  /// [duartion] 上报当次的时长
  /// [cost] 上报当次产生的钻石流水数
  /// [num] 上报当次参与的人数
  static void reportCalculatorResultImp({
    String? uid,
    String? gender,
    String? duartion,
    String? cost,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'calculator_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duartion': duartion ?? '',
        'cost': cost ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏界面曝光时上报
  ///
  /// [uid]
  /// [roomId]
  /// [roomMode] 房间模板//普通聊天：chat；真心话：truth_dare；相亲：blind_date; ludo:ludo
  static void reportClashEmpireExposed({
    String? uid,
    String? roomId,
    String? roomMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'Clash_empire_exposed',
      params: {
        'uid': uid ?? '',
        'room_id': roomId ?? '',
        'room_mode': roomMode ?? '',
      },
      priority: priority,
    );
  }

  /// cp任务展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportCoupleTaskImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_task_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房列表主动点击转盘入口
  ///
  /// [uid]
  /// [gender]
  static void reportDrawListClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'draw_list_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 切换房间背景时上报
  ///
  /// [ownerUid]
  /// [roomId]
  /// [bgNumber]
  static void reportEventChangeRoombg({
    String? ownerUid,
    String? roomId,
    String? bgNumber,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_change_roombg',
      params: {
        'owner_uid': ownerUid ?? '',
        'room_id': roomId ?? '',
        'bg_number': bgNumber ?? '',
      },
      priority: priority,
    );
  }

  /// 房间在信息流曝光时上报
  ///
  /// [roomId ] 房间ID
  /// [uid] 用户ID
  /// [ownerId] 房主ID
  /// [refer] //曝光来源live_roomlist; 房间信息流
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportEventRoomExposure({
    String? roomId ,
    String? uid,
    String? ownerId,
    String? refer,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_room_exposure',
      params: {
        'room_id ': roomId  ?? '',
        'uid': uid ?? '',
        'owner_id': ownerId ?? '',
        'refer': refer ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 公屏发言上报
  ///
  /// [roomId]
  /// [uid]
  /// [ownerUid]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportEventRoomPublicChat({
    String? roomId,
    String? uid,
    String? ownerUid,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_room_public_chat',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'owner_uid': ownerUid ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 退出粉丝团
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [ownerUid]
  static void reportExitRoomClub({
    String? uid,
    String? gender,
    String? roomId,
    String? ownerUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'exit_room_club',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏中心的游戏点击
  ///
  /// [uid]
  /// [gameId] 1004=slots;1016=aero;1022=fishing;1029=fruit;1073=big_battle
  static void reportGameCenterClick({
    String? uid,
    String? gameId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_center_click',
      params: {
        'uid': uid ?? '',
        'game_id': gameId ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏中心页面展示时上报
  ///
  /// [uid]
  /// [pageRefer] //页面展示room_icon=语音房icongame_expore=游戏中心抽屉页面user_page=用户个人主页进入
  /// [gender] 用户性别
  static void reportGameCenterExpore({
    String? uid,
    String? pageRefer,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_center_expore',
      params: {
        'uid': uid ?? '',
        'page_refer': pageRefer ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间收到大礼物数据入队，排队播放
  ///
  /// [roomId] 房间id
  /// [content] 礼物id
  /// [type] 展示的礼物类型
  /// [status] 是否可以添加，如果是礼物或者座驾，判断是否有缓存，有缓存就可以添加。0不可以1可以
  static void reportGiftAddQueue({
    String? roomId,
    String? content,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_add_queue',
      params: {
        'room_id': roomId ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 点击大礼物广播跳转对应的房间
  ///
  /// [uid]
  /// [gender]
  /// [page] 上报大礼物广播的展示位置，home=一级页面，room=房间内
  /// [roomId] 跳转的房间id
  /// [type] 根据后端数据返回对应字段，不再自定义
  /// [time] 飘屏的下发时间，当作飘屏id
  static void reportGiftBroadcastClick({
    String? uid,
    String? gender,
    String? page,
    String? roomId,
    String? type,
    String? time,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_broadcast_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'time': time ?? '',
      },
      priority: priority,
    );
  }

  /// 广播展示
  ///
  /// [uid]
  /// [gender]
  /// [page] 上报大礼物广播的展示位置，home=一级页面，room=房间内
  /// [type] 根据后端数据返回对应字段，不自定义
  /// [time] 下发时间，当作id
  /// [roomId] 曝光时所在的房间，没有显示空
  static void reportGiftBroadcastImp({
    String? uid,
    String? gender,
    String? page,
    String? type,
    String? time,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_broadcast_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
        'type': type ?? '',
        'time': time ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物读取缓存失败
  ///
  /// [roomId] 房间id
  /// [content] 礼物id
  /// [type] 播放的礼物类型
  static void reportGiftCacheFailure({
    String? roomId,
    String? content,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_cache_failure',
      params: {
        'room_id': roomId ?? '',
        'content': content ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物下载成功后的回调，在这回调去插入 播放队列
  ///
  /// [roomId] 房间ID
  /// [content] 礼物id
  /// [type] 礼物类型
  /// [status] 下载的状态，0失败1成功
  static void reportGiftDownloadComplete({
    String? roomId,
    String? content,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_download_complete',
      params: {
        'room_id': roomId ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物播放完毕，或代码触发完毕
  ///
  /// [roomId]
  /// [status] 0/1。0代表动画执行完毕触发，1代表代码主动触发
  static void reportGiftPlayComplete({
    String? roomId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_play_complete',
      params: {
        'room_id': roomId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端礼物数据出队
  ///
  /// [roomId] 房间id
  /// [content] 礼物id
  /// [status] 上个svga动画状态
  /// [type] 礼物类型：座驾、礼物、全屏
  static void reportGiftPollQueue({
    String? roomId,
    String? content,
    String? status,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_poll_queue',
      params: {
        'room_id': roomId ?? '',
        'content': content ?? '',
        'status': status ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间内收到大礼物pb上报（app不在后台）
  ///
  /// [roomId] 房间id
  /// [content] 礼物id
  /// [level] 礼物等级
  static void reportGiftReceive({
    String? roomId,
    String? content,
    String? level,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_receive',
      params: {
        'room_id': roomId ?? '',
        'content': content ?? '',
        'level': level ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物墙页面曝光
  ///
  /// [uid]
  /// [gender]
  static void reportGiftWallExport({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_wall_export',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物点亮时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [giftId]
  /// [giftNum] 送礼个数
  /// [count] 送出价值钻石数
  /// [refer] light_icon=点亮按钮 gift_list 普通送礼
  /// [unknown]
  static void reportGiftWallLight({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? giftId,
    String? giftNum,
    String? count,
    String? refer,
    String? unknown,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_wall_light',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'gift_id': giftId ?? '',
        'gift_num': giftNum ?? '',
        'count': count ?? '',
        'refer': refer ?? '',
        'unknown': unknown ?? '',
      },
      priority: priority,
    );
  }

  /// 首页点击语音房入口
  ///
  /// [uid]
  /// [gender]
  static void reportHomeVoicepartyClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_voiceparty_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户加入粉丝团
  ///
  /// [uid] 用户ID
  /// [gender]
  /// [roomId]
  /// [ownerUid]
  static void reportJoinRoomClub({
    String? uid,
    String? gender,
    String? roomId,
    String? ownerUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'join_room_club',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨创建界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报入口的展示来源，room=房间内，group=群聊内
  /// [groupId]
  static void reportLuckybagCreateImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨创建成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前发出的红包雨类型，gift=礼物红包，room=普通房间钻石红包，world=世界红包
  /// [cost] 上报当前红包雨的实际消耗钻石价值
  /// [num] 上报当前红包雨可接收的人数（数字）
  /// [giftNum] 上报如是礼物红包雨时，当前发出的礼物个数
  /// [from] 上报入口的展示来源，room=房间内，group=群聊内
  /// [groupId]
  static void reportLuckybagCreateSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? cost,
    String? num,
    String? giftNum,
    String? from,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'cost': cost ?? '',
        'num': num ?? '',
        'gift_num': giftNum ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨详情页展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报红包雨类型，gift=礼物红包，room=普通房间钻石红包，world=世界红包
  /// [groupId]
  static void reportLuckybagDetailsImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击红包雨在Function内的入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报入口的展示来源，room=房间内，group=群聊内
  /// [groupId]
  static void reportLuckybagEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨在Function内入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报入口的展示来源，room=房间内，group=群聊内
  /// [groupId]
  static void reportLuckybagEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击红包雨领取按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 红包界面上一级来源，auto=自动弹出，screen=公屏手动点击，suspend=悬浮icon，group=群聊
  /// [type] 上报当前发出的红包雨类型，gift=礼物红包，room=普通房间钻石红包，world=世界红包
  /// [num] 上报当前红包雨可接收的人数（数字）
  /// [giftNum] 上报如是礼物红包雨时，当前发出的礼物个数
  /// [groupId]
  static void reportLuckybagGetClick({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    String? type,
    String? num,
    String? giftNum,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_get_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'type': type ?? '',
        'num': num ?? '',
        'gift_num': giftNum ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨领取界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 红包界面上一级来源，auto=自动弹出，screen=公屏手动点击，suspend=悬浮icon，group=群聊内
  /// [type] 上报当前发出的红包雨类型，gift=礼物红包，room=普通房间钻石红包，world=世界红包
  /// [num] 上报当前红包雨可接收的人数（数字）
  /// [giftNum] 上报如是礼物红包雨时，当前发出的礼物个数
  /// [groupId]
  static void reportLuckybagGetImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    String? type,
    String? num,
    String? giftNum,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_get_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'type': type ?? '',
        'num': num ?? '',
        'gift_num': giftNum ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨领取结果展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报领取的红包雨类型，gift=礼物红包，room=普通房间钻石红包，world=世界红包
  /// [from] 红包界面上一级来源，auto=自动弹出，screen=公屏手动点击，suspend=悬浮icon，group=群聊内
  /// [num] 上报当前领取到的钻石数
  /// [giftNum] 上报当前领取到的礼物个数
  /// [status] 上报当前红包的状态，get=成功领取，expired=已过期，empty=红包被领取完
  /// [groupId]
  static void reportLuckybagGetResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? from,
    String? num,
    String? giftNum,
    String? status,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_get_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'num': num ?? '',
        'gift_num': giftNum ?? '',
        'status': status ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 红包雨接口请求结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [reason] 失败原因
  /// [onTime] 请求时长
  /// [result] succ/fail
  /// [act] list 红包雨列表数据（礼物/钻石/数量）send 发送红包newest  可领取红包detail 红包详情pick 领取红包
  /// [from] 上报请求的来源，room=房间内，group=群聊内
  static void reportLuckybagRequestEnd({
    String? uid,
    String? gender,
    String? reason,
    String? onTime,
    String? result,
    String? act,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckybag_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'result': result ?? '',
        'act': act ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// ludo游戏结束时上报
  ///
  /// [duration]
  /// [roomId]
  /// [ownerUid]
  /// [joinUid]
  static void reportLudoEnd({
    String? duration,
    String? roomId,
    String? ownerUid,
    String? joinUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ludo_end',
      params: {
        'duration': duration ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
        'join_uid': joinUid ?? '',
      },
      priority: priority,
    );
  }

  /// 麦位数量更改成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报更改后的麦位数量，如果是8+2的形式则上报8+2.
  static void reportMicNumsChangeSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mic_nums_change_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 点击改变麦位不符合要求提示弹窗出现时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportMicNumsRestrictImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mic_nums_restrict_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击下一个时上报
  static void reportNextOne({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'next_one',
      priority: priority,
    );
  }

  /// 总榜单入口点击
  ///
  /// [uid]
  /// [gender]
  /// [type] type=上报类型，Gift sent=送礼，Gift received=收礼，Intimacy=亲密度，Gift gallery=礼物馆
  static void reportRankingEntranceClick({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ranking_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 榜单展示（标签类型展示只上报一次，切换标签类型再上报一次）
  ///
  /// [type] 上报当前排行榜的类型，sent=送礼榜，receive=收礼榜，room=房间收礼榜，family=家族收礼榜
  /// [uid]
  /// [gender]
  static void reportRankingImp({
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ranking_imp',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// tab点击时上报
  ///
  /// [rooTabClick]
  /// [uid]
  static void reportRooTabClick({
    String? rooTabClick,
    String? uid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roo_tab_click',
      params: {
        'roo_tab_click': rooTabClick ?? '',
        'uid': uid ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内点击活动入口
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报房间类型
  /// [roomId]
  /// [content] 上报点击的活动id
  /// [bannerType] activity=活动模块，recharge=充值模块
  static void reportRoomActivityClick({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    String? content,
    String? bannerType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_activity_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
        'banner_type': bannerType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内活动入口展示（一次房间session内只展示上报一次
  ///
  /// [type] 上报房间类型
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] 上报展示的活动id
  /// [bannerType] activity=活动模块，recharge=充值模块
  static void reportRoomActivityImp({
    String? type,
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    String? bannerType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_activity_imp',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
        'banner_type': bannerType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内H5拉起页面弹窗展示，切换一个session时上报一次
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报banner id
  /// [roomId]
  /// [bannerType] activity=活动模块，recharge=充值模块
  static void reportRoomActivityPopoutImp({
    String? uid,
    String? gender,
    String? content,
    String? roomId,
    String? bannerType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_activity_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
        'banner_type': bannerType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音公屏进入的弹窗按钮点击
  ///
  /// [type] save=保存修改，cancel=取消弹窗
  /// [uid]
  /// [gender]
  /// [status] 上报当前查看者的身份，owner=房主，admin=管理员，member=关注者，visitor=游客
  /// [roomId]
  static void reportRoomAnnouncementClick({
    String? type,
    String? uid,
    String? gender,
    String? status,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_announcement_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房公告面板展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 上报当前查看者的身份，owner=房主，admin=管理员，member=关注者，visitor=游客
  /// [roomId]
  /// [from] 上报公告展示，setting=设置，screen=公屏进入
  static void reportRoomAnnouncementImp({
    String? uid,
    String? gender,
    String? status,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_announcement_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 被邀请弹窗展示
  ///
  /// [uid] 触发方id
  /// [gender] 触发方性别
  /// [toUid] 发出邀请方的id
  /// [toGender] 发出邀请方的性别
  /// [roomId] 房间id
  /// [type] 房间类别，chat/game
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  /// [roomType] 上报当前是普通房间还是家族房间，normal=普通房间，family=家族房间
  /// [gameName] 如果是游戏房上报游戏名称
  /// [status ] 上报0和1，0=接受，1=拒绝
  static void reportRoomBeInvitedPopout({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? type,
    String? from,
    String? roomType,
    String? gameName,
    String? status ,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_be_invited_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'room_type': roomType ?? '',
        'game_name': gameName ?? '',
        'status ': status  ?? '',
      },
      priority: priority,
    );
  }

  /// 被邀请弹窗点击动作
  ///
  /// [uid] 触发方id
  /// [gender] 触发方性别
  /// [toUid] 发出邀请人的id
  /// [toGender] 发情邀请人的性别
  /// [type] 房间类型上报，chat/game
  /// [status] 上报0和10=接受，1=拒绝
  /// [roomId] 房间id
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  /// [roomType] 上报当前是普通房间还是家族房间，normal=普通房间，family=家族房间
  /// [gameName] 如果是游戏房上报游戏名称
  static void reportRoomBeInvitedPopoutClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? type,
    String? status,
    String? roomId,
    String? from,
    String? roomType,
    String? gameName,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_be_invited_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'room_type': roomType ?? '',
        'game_name': gameName ?? '',
      },
      priority: priority,
    );
  }

  /// 点击弹窗的关闭按钮
  ///
  /// [uid] 触发方id
  /// [gender] 触发方性别
  /// [roomId] 房间id
  /// [toUid] 发出邀请方的id
  /// [toGender] 发出邀请方的性别
  /// [type] 房间类型，chat/game
  /// [gameName] 游戏名称
  static void reportRoomBeInvitedPopoutClose({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    String? type,
    String? gameName,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_be_invited_popout_close',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
        'game_name': gameName ?? '',
      },
      priority: priority,
    );
  }

  /// 房间最后一位权限者关播时资产累计展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报展示者身份，0=房主，1=管理员
  /// [roomId]
  static void reportRoomCloseAsset({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_close_asset',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房间关播页面点击动作
  ///
  /// [uid]
  /// [gender]
  /// [type] room=点击推荐列表的房间，exit=退出房间
  /// [roomId]
  static void reportRoomCloseClick({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_close_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房间关播告知&推荐房间页面
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [status] 上报当前房间是否已关注，0=未关注，1=已关注
  /// [type] 上报房间类型
  static void reportRoomCloseImp({
    String? roomId,
    String? uid,
    String? gender,
    String? status,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_close_imp',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击房间贡献榜单打开榜单时上报
  ///
  /// [roomId]
  static void reportRoomContributionRankImp({
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_contribution_rank_imp',
      params: {
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房界面点击右上角创建
  ///
  /// [uid]
  /// [gender]
  static void reportRoomCreateClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 房间创建成功时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报后端所返回的房间类型
  /// [from] 增加创建房间成功时来源，list=列表创建，mine=我的房间管理页面创建完成，im=私聊im创建成功时上报
  /// [roomType] live=直播房间，voice=语音房间
  static void reportRoomCreateEnd({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? from,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房点击创建时异常情况出现（不满足创建条件等
  ///
  /// [reason] 上报错误原因，verify=没有声音认证，后段错误码=数值没有满足，other=其他原因
  /// [uid]
  /// [gender]
  static void reportRoomCreateError({
    String? reason,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_error',
      params: {
        'reason': reason ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建房间页面点击创建房间
  ///
  /// [uid]
  /// [gender]
  static void reportRoomCreateFullClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_full_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房创建全屏页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportRoomCreateFullImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_full_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 选择完标签与名字后点击创建按钮
  ///
  /// [type] 上报上一界面所选择的类型，chat/ game
  /// [content] 上报所选择的语音房标签，上报标签名
  /// [uid]
  /// [gender]
  /// [status] 上报当前房间是否有名字，0=无名字为默认，1=有名字
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，其他方式上报other
  static void reportRoomCreateTagClick({
    String? type,
    String? content,
    String? uid,
    String? gender,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_tag_click',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 房间类型标签选择界面展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报上一界面所选择的类型，chat/ game
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，其他方式上报other
  static void reportRoomCreateTagImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_tag_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 创建语音房流程点击类型选择
  ///
  /// [type] chat=语音房，game=游戏房
  /// [uid]
  /// [gender]
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，其他方式上报other
  static void reportRoomCreateTypeClick({
    String? type,
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_type_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击创建房间选择类型界面展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，其他方式上报other
  static void reportRoomCreateTypeImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_create_type_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击发送麦上大表情
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [status] 游戏类型上报，ludo=飞行棋，knife=飞刀
  /// [type] 当前进入的房间类型，chat/game/temporarygame
  static void reportRoomEmojiSend({
    String? roomId,
    String? uid,
    String? gender,
    String? status,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_emoji_send',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 进入房间页面加载完毕上报
  ///
  /// [refer] 进房来源//daily_task：签到create_room_page：创建房间tab_live_roomlist：live-tab房间列表friends_playing ：live-好友在玩message_card：IM消息-分享房间卡片user_info_page ：个人主页进入  search：搜索home：首页人的推荐exchange_room：切换房型h5page_link:站外h5分享链接reco_list：动态推荐follow_list：动态关注chat_list:私聊列表im_notification 站内消息推送qmatch_room_chat 首页语聊房匹配入口qmatch_room_tod  首页真心话大冒险匹配入口change_another_room  通过更换房间按钮进入房间home_room_invitation_pop 首页邀请进房弹窗
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  /// [result] succ/fail
  /// [roomId] 房间id
  /// [reason] 失败原因
  /// [uid]
  /// [ownerUid] 房主ID
  /// [isMyRoom] 是否是我的房间 1= 是， 0 = 不是
  /// [agencyId] 加入的机构id，没有传0
  static void reportRoomEntered({
    String? refer,
    String? mode,
    String? result,
    String? roomId,
    String? reason,
    String? uid,
    String? ownerUid,
    String? isMyRoom,
    String? agencyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_entered',
      params: {
        'refer': refer ?? '',
        'mode': mode ?? '',
        'result': result ?? '',
        'room_id': roomId ?? '',
        'reason': reason ?? '',
        'uid': uid ?? '',
        'owner_uid': ownerUid ?? '',
        'is_my_room': isMyRoom ?? '',
        'agency_id': agencyId ?? '',
      },
      priority: priority,
    );
  }

  /// 上报用户跟随进房成功时
  ///
  /// [from] 上报用户跟随进房的来源，开发定义
  /// [uid]
  /// [gender]
  /// [roomId] 进的房间id
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportRoomEnteredFollow({
    String? from,
    String? uid,
    String? gender,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_entered_follow',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内进场特效展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] 展示的特效id
  static void reportRoomEntereffectImp({
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_entereffect_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房入口展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 展示入口位置，home=首页，chat=聊天页
  static void reportRoomEntranceImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 用户退出房间成功时
  ///
  /// [uid]
  /// [gender]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  /// [timeDistance] 上报在房间内的时长，用s表示
  /// [exitType] //上报退出房间的方式1=直接关闭2=被踢出房间3=切换到其他房间退出
  /// [roomId]
  /// [refer] 进房来源//create_room_page：创建房间tab_live_roomlist：live-tab房间列表friends_playing ：live-好友在玩message_card：IM消息-分享房间卡片user_info_page ：个人主页进入  search：搜索home：首页人的推荐exchange_room：切换房型h5page_link:站外h5分享链接reco_list：动态推荐follow_list：动态关注chat_list:私聊列表im_notification 站内消息推送
  /// [isMyRoom] 是否是我的房间 1= 是， 2 = 不是
  /// [internetErr] //网络波动时长累计，单位s小于等于bad等级则开始累积
  static void reportRoomExit({
    String? uid,
    String? gender,
    String? mode,
    String? timeDistance,
    String? exitType,
    String? roomId,
    String? refer,
    String? isMyRoom,
    String? internetErr,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_exit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'mode': mode ?? '',
        'time_distance': timeDistance ?? '',
        'exit_type': exitType ?? '',
        'room_id': roomId ?? '',
        'refer': refer ?? '',
        'is_my_room': isMyRoom ?? '',
        'internet_err': internetErr ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房退出房间二次确认弹窗出现
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [style] 上报退出房间的方式，0=直接关闭，1=最小化跳转其他房间关闭，2=被踢出房间，3=切换到其他房间退出
  /// [type] 根据后端返回字段上报房间类型
  /// [content] 上报当前房间标签
  static void reportRoomExitPopout({
    String? uid,
    String? gender,
    String? roomId,
    String? style,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_exit_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'style': style ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 探索国家列表页点击进入房间
  ///
  /// [uid]
  /// [gender]
  /// [roomId] 点击进入的room_id
  /// [page] 上报当前切换到的国家列表缩写
  static void reportRoomExploreCouClick({
    String? uid,
    String? gender,
    String? roomId,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_explore_cou_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 探索页国家房间展示（每切换一次国家上报一次）
  ///
  /// [uid]
  /// [gender]
  /// [page] 上报当前切换到的国家列表缩写
  static void reportRoomExploreCouImp({
    String? uid,
    String? gender,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_explore_cou_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 点击关注按钮
  ///
  /// [uid] 点击关注的用户
  /// [gender] 点击关注的用户性别
  /// [roomId] 关注的房间ID
  /// [roomType] 房间类型，normal=个人语音房，family=家族语音房
  static void reportRoomFollowClick({
    String? uid,
    String? gender,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_follow_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 关注房间引导弹窗出现
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 根据后端给予的字段，上报房间的类型
  /// [from] 以上均增加增加from上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，，explore=探索页国家列表进入
  static void reportRoomFollowPopout({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_follow_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 关注房间引导弹窗点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] 上报弹窗的点击动作，follow=关注房间；cancel=取消弹窗
  /// [type] 根据后端给予的字段，上报房间的类型
  /// [from] 以上均增加增加from上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，，explore=探索页国家列表进入
  static void reportRoomFollowPopoutClick({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_follow_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内点击切换游戏
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 上报对应的房间类型，返回后端字段
  static void reportRoomFunctionChange({
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_function_change',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房点击function里面的游戏（h5）时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] 上报点击的游戏 id
  static void reportRoomFunctionClick({
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_function_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房底部功能栏点击展开曝光
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  static void reportRoomFunctionImp({
    String? roomId,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_function_imp',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏点击动作上报
  ///
  /// [type] 返回后端游戏类型房间
  /// [content] 上报点击的动作类型，join=加入游戏，ready=准备游戏，cancelready=取消准备，start=开始游戏，quit=退出游戏
  /// [roomId]
  /// [uid]
  /// [gender]
  static void reportRoomGameAction({
    String? type,
    String? content,
    String? roomId,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_action',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏结束时上报（正常结束，或游戏中断
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 加入的房间类型上报，根据后端所返回的字段进行区分，分为正常游戏房与游戏房Ludo/Knife/Dominoes三种游戏
  /// [duration] 记录当局游戏时长
  static void reportRoomGameEnd({
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_end',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房错误上报
  ///
  /// [reason] 上报错误码
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 加入的房间类型上报，根据后端所返回的字段进行区分，分为正常游戏房与游戏房Ludo/Knife/Dominoes三种游戏
  static void reportRoomGameError({
    String? reason,
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_error',
      params: {
        'reason': reason ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房加载结束时
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [reason] 失败原因错误码
  /// [duration] 加载时长
  /// [result] succ/fail
  /// [type] 返回后端游戏类型房间
  static void reportRoomGameLoadingEnd({
    String? roomId,
    String? uid,
    String? gender,
    String? reason,
    String? duration,
    String? result,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_loading_end',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'result': result ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏界面加载开始
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 返回后端游戏类型房间
  static void reportRoomGameLoadingStart({
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_loading_start',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击列表快速游戏匹配
  ///
  /// [uid]
  /// [gender]
  /// [from] roomlist=房间列表快速匹配，homepage=首页点击快速匹配
  /// [type] Ludo/Knife/Dominoes三种游戏名称代表
  static void reportRoomGameMatchClick({
    String? uid,
    String? gender,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_match_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏快速匹配结束时
  ///
  /// [result] 是否成功匹配，0=匹配进房间，1=自己创建了临时房间
  /// [uid]
  /// [gender]
  /// [duration] 匹配过程耗时
  /// [reason] 失败原因错误码上报
  /// [roomId]
  /// [from] roomlist=房间列表快速匹配，homepage=首页点击快速匹配
  /// [type] 返回后端游戏类型房间
  static void reportRoomGameMatchEnd({
    String? result,
    String? uid,
    String? gender,
    String? duration,
    String? reason,
    String? roomId,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_match_end',
      params: {
        'result': result ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'reason': reason ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏快速匹配开始
  ///
  /// [uid]
  /// [gender]
  /// [from] roomlist=房间列表快速匹配，homepage=首页点击快速匹配
  /// [type] 返回后端游戏类型房间
  static void reportRoomGameMatchStart({
    String? uid,
    String? gender,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_match_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏匹配过程中点击取消
  ///
  /// [uid]
  /// [gender]
  /// [type] Ludo/Knife/Dominoes三种游戏名称代表，返回后端定义字段
  /// [from] roomlist=房间列表快速匹配，homepage=首页点击快速匹配，event=event匹配
  static void reportRoomGameMatchingCancel({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_matching_cancel',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏匹配过程展示
  ///
  /// [uid]
  /// [gender]
  /// [type] Ludo/Knife/Dominoes三种游戏名称代表，返回后端定义字段
  /// [from] roomlist=房间列表快速匹配，homepage=首页点击快速匹配，event=event匹配
  static void reportRoomGameMatchingImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_matching_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏结果页点击动作上报
  ///
  /// [content] 动作上报，close=关闭游戏界面，again=再加入一次游戏
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 加入的房间类型上报，根据后端所返回的字段进行区分，分为正常游戏房与游戏房Ludo/Knife/Dominoes三种游戏
  static void reportRoomGameResultAction({
    String? content,
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_result_action',
      params: {
        'content': content ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏结果页展示
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 加入的房间类型上报，根据后端所返回的字段进行区分，分为正常游戏房与游戏房Ludo/Knife/Dominoes三种游戏
  static void reportRoomGameResultImp({
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_result_imp',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房中游戏开始时
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [num] 游戏参加人数上报，用数字代替
  /// [type] 加入的房间类型上报，根据后端所返回的字段进行区分，分为正常游戏房与游戏房Ludo/Knife/Dominoes三种游戏
  static void reportRoomGameStart({
    String? roomId,
    String? uid,
    String? gender,
    String? num,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_game_start',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 房间内点击公屏上回礼功能
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid]
  /// [toGender]
  /// [type] 上报当前点击的礼物等级
  static void reportRoomGiftBack({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_gift_back',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内点击礼物面板入口（公屏页面右下角入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportRoomGiftBoardClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_gift_board_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 房间送礼物引导弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roomMode] 根据后端给予的字段，上报房间的类型
  static void reportRoomGiftGuidePopout({
    String? uid,
    String? gender,
    String? roomId,
    String? roomMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_gift_guide_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'room_mode': roomMode ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在房间内点击im入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，其他方式上报other
  static void reportRoomImClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_im_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内点击发送图片入口
  static void reportRoomImageEntranceClick({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_image_entrance_click',
      priority: priority,
    );
  }

  /// 语音房发送图片入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前房间类型（返回后端字段
  static void reportRoomImageEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_image_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内图片发送成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报对应的房间类型（后端返回字段
  static void reportRoomImageSendSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_image_send_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 首页进房邀请弹窗点击时
  ///
  /// [type] //触发方式pushstay
  /// [autherId] 被曝光用户的uid
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [distance] 双方的距离(km)，上报数值，保留小数点后1位// 例如 3.6
  /// [roomId]
  /// [clickType] 点击acceptclose
  static void reportRoomInvitationPopClick({
    String? type,
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? distance,
    String? roomId,
    String? clickType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invitation_pop_click',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'distance': distance ?? '',
        'room_id': roomId ?? '',
        'click_type': clickType ?? '',
      },
      priority: priority,
    );
  }

  /// 首页进房邀请弹窗展示时
  ///
  /// [type] //触发方式pushstay
  /// [autherId] 被曝光用户的uid
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [distance] 双方的距离(km)，上报数值，保留小数点后1位// 例如 3.6
  /// [roomId] 房间id
  static void reportRoomInvitationPopImp({
    String? type,
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? distance,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invitation_pop_imp',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'distance': distance ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 邀请上麦引导弹窗出现
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [type] 根据后端给予的字段，上报房间的类型
  /// [status] 上报当前弹窗的类型，1=普通上麦，2=发出上麦申请
  /// [from] 以上均增加增加from上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  static void reportRoomInvitationPopout({
    String? roomId,
    String? uid,
    String? gender,
    String? type,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invitation_popout',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 邀请上麦引导弹窗点击
  ///
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [act] 上报弹窗的点击动作，take=点击上麦；cancel=取消弹窗
  /// [type] 根据后端给予的字段，上报房间的类型
  /// [status] 上报当前弹窗的类型，1=普通上麦，2=发出上麦申请
  /// [from] 以上均增加增加from上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，，explore=探索页国家列表进入
  static void reportRoomInvitationPopoutClick({
    String? roomId,
    String? uid,
    String? gender,
    String? act,
    String? type,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invitation_popout_click',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房用户邀请上麦时
  ///
  /// [type] 上报房间类型，chat/game
  /// [roomId] 上报房间id
  /// [uid] 发出邀请方的id
  /// [gender] 发出邀请方的性别
  /// [toUid] 被邀请用户id
  /// [toGender] 被邀请用户性别
  /// [act] 上报邀请来源，0=资料卡入口，1=进场提示邀请入口
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  /// [roomType] 上报当前是普通房间还是家族房间，normal=普通房间，family=家族房间
  static void reportRoomInvitedMic({
    String? type,
    String? roomId,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? act,
    String? from,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invited_mic',
      params: {
        'type': type ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'act': act ?? '',
        'from': from ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端收到邀请上麦请求时
  ///
  /// [roomId]
  /// [uid] 发出方id
  /// [gender]
  /// [toUid] 接收方id
  /// [toGender]
  /// [type] 上报房间类型
  static void reportRoomInvitedMicRequest({
    String? roomId,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_invited_mic_request',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户最小化房间
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报房间类型，chat/ game
  /// [roomId]
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  static void reportRoomKeep({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_keep',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房用户下麦时
  ///
  /// [status] 上报下麦方式，1=自己下麦，2=被踢下麦
  /// [roomId] 上报房间id
  /// [toUid] 上报踢该用户下麦的uid
  /// [uid]
  /// [gender]
  /// [duration] 上报用户在麦上时长
  /// [refer] 进房来源//create_room_page：创建房间tab_live_roomlist：live-tab房间列表friends_playing ：live-好友在玩message_card：IM消息-分享房间卡片user_info_page ：个人主页进入  search：搜索home：首页人的推荐exchange_room：切换房型h5page_link:站外h5分享链接reco_list：动态推荐follow_list：动态关注chat_list:私聊列表im_notification 站内消息推送
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  /// [isMyRoom] 是否是我的房间 1= 是， 2 = 不是
  static void reportRoomLeaveMic({
    String? status,
    String? roomId,
    String? toUid,
    String? uid,
    String? gender,
    String? duration,
    String? refer,
    String? mode,
    String? isMyRoom,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_leave_mic',
      params: {
        'status': status ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'refer': refer ?? '',
        'mode': mode ?? '',
        'is_my_room': isMyRoom ?? '',
      },
      priority: priority,
    );
  }

  /// 房主弹出房间升级界面时上报
  ///
  /// [roomId]
  /// [roomLevel] 当前房间升级的等级
  static void reportRoomLevelUpgradeImp({
    String? roomId,
    String? roomLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_level_upgrade_imp',
      params: {
        'room_id': roomId ?? '',
        'room_level': roomLevel ?? '',
      },
      priority: priority,
    );
  }

  /// 列表点击进入语音房
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前点击进入的语音房标签
  /// [from] 上报进入房间来源
  static void reportRoomListClick({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_list_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 房间列表展示（单个房间标签类型展示只上报一次，切换标签类型再上报一次）
  ///
  /// [content] 上报当前展示页面标签名字
  /// [uid]
  /// [gender]
  /// [from] 上报进入来源，home=首页入口，chat=聊天列表页入口，push=推送进入，tab=点击一级页面tabbar icon
  /// [type] 上报当前页面，voice/ live
  static void reportRoomListImp({
    String? content,
    String? uid,
    String? gender,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_list_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房推荐位置展示
  ///
  /// [uid]
  /// [gender]
  static void reportRoomListRecImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_list_rec_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房列表推荐位置接口请求结束
  ///
  /// [uid]
  /// [gender]
  /// [result] succ/fail
  /// [onTime] 接口请求时间
  /// [reason] 失败原因错误码
  /// [type] 上报当前页面，voice/ live
  static void reportRoomListRecRequestEnd({
    String? uid,
    String? gender,
    String? result,
    String? onTime,
    String? reason,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_list_rec_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'on_time': onTime ?? '',
        'reason': reason ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房列表推荐位置接口请求开始
  ///
  /// [uid]
  /// [gender]
  static void reportRoomListRecRequestStart({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_list_rec_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 房间上锁成功时
  ///
  /// [roomId] 上报房间id
  /// [uid]
  /// [gender]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportRoomLockEnd({
    String? roomId,
    String? uid,
    String? gender,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_lock_end',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房进行锁麦动作
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 房间类型上报，chat/game
  static void reportRoomLockMic({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_lock_mic',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房手动点击右上角处分享按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportRoomManualShareClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_manual_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内点击对其他用户进行闭麦操作
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [type] 根据后端给予的字段，上报房间的类型
  static void reportRoomMicClose({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_mic_close',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 创建房间的入口曝光时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 房间模式
  static void reportRoomModeImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_mode_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 打开房间更多页面
  ///
  /// [uid] 用户uid
  /// [gender] 用户性别
  static void reportRoomMoreImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_more_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 房间公屏消息发送结束时（这里仅指用户主动发送的消息，不包括客户端自己发送的系统消息）
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报内容类型，dice=骰子，text=文字，sticker=表情包
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入，explore=探索页国家列表进入
  /// [roomType] 上报当前是普通房间还是家族房间，normal=普通房间，family=家族房间，live=直播房间
  /// [roomId]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportRoomMsgSendEnd({
    String? uid,
    String? gender,
    String? content,
    String? from,
    String? roomType,
    String? roomId,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_msg_send_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'room_type': roomType ?? '',
        'room_id': roomId ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房音乐功能点击功能
  ///
  /// [content] 上报点击功能点，add=添加音乐，play=播放音乐，pause=暂停音乐，next=播放下一首，select=选择某一首歌，pre=点击播放上一首歌，cancel=退出弹窗，search=搜索，delete=删除
  /// [type] 上报房间类型，chat/ game/TMO/Truth&Dare
  /// [roomId] 上报房间id
  /// [status] 麦状态 1： 开麦，0 未开麦
  static void reportRoomMusicClick({
    String? content,
    String? type,
    String? roomId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_music_click',
      params: {
        'content': content ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房中点击音乐功能
  ///
  /// [roomId] 上报房间id
  /// [type] 上报房间类型，chat/game/TMO/Truth&Dare
  static void reportRoomMusicImp({
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_music_imp',
      params: {
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 主播 开麦\闭麦
  ///
  /// [content] 1 开麦，0 闭麦
  /// [uid] uid
  /// [status] 判断闭麦时是否在放音乐，0/1，0=没有播放音乐，1=在播放音乐
  /// [roomId]
  /// [type]
  /// [style] 是否闭麦时状态有勾选闭麦播放，0=闭麦播报音乐，1=无闭麦播报音乐
  /// [liveId] 直播id
  static void reportRoomMuteMic({
    String? content,
    String? uid,
    String? status,
    String? roomId,
    String? type,
    String? style,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_mute_mic',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'style': style ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房新人礼包点击接收礼包
  ///
  /// [from] 展示新手礼包弹窗位置，list=列表页展示，room=房间内展示
  /// [uid]
  /// [gender]
  /// [type] 上报当前礼包的item id
  /// [page] 上报当前点击的路由位置
  static void reportRoomNewgiftClick({
    String? from,
    String? uid,
    String? gender,
    String? type,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_newgift_click',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房新手礼包获取弹窗展示
  ///
  /// [from] 展示新手礼包弹窗位置，list=列表页展示，room=房间内展示
  /// [uid]
  /// [gender]
  /// [type] 上报当前礼包的item id
  /// [page] 上报当前页面
  static void reportRoomNewgiftImp({
    String? from,
    String? uid,
    String? gender,
    String? type,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_newgift_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房内提示弹窗
  ///
  /// [status] 上报弹窗类型，kickout=踢出房间，leave=被踢下麦
  /// [uid]
  /// [gender]
  /// [type] 上报房间类型
  /// [roomId]
  static void reportRoomNoticeImp({
    String? status,
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_notice_imp',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 打开房间资料界面
  ///
  /// [uid] 用户uid
  /// [gender] 用户性别
  static void reportRoomProfileImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_profile_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 活动进度banner点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [bannerId]
  static void reportRoomProgressBannerClick({
    String? uid,
    String? gender,
    String? roomId,
    String? bannerId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_progress_banner_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'banner_id': bannerId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动进度入口展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 展示banner id
  /// [roomId]
  static void reportRoomProgressImp({
    String? uid,
    String? gender,
    String? content,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_progress_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动进度入口设置弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoomProgressSettingImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_progress_setting_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动进度设置弹窗点击save时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] 当前save开启的活动进度item id
  static void reportRoomProgressSettingSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_progress_setting_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房邀请弹窗点击动作
  ///
  /// [type] 区分匹配弹窗的类别，chat=语音房邀请匹配，game=游戏房邀请匹配，返回后端定义字段
  /// [act] join=加入房间，cancel=取消弹窗
  /// [uid] 接收方id
  /// [gender] 接收方性别
  /// [roomId] 邀请房间的roomid
  /// [duration] 弹窗展示到点击时停留时长，单位s
  static void reportRoomQmatchReceiveClick({
    String? type,
    String? act,
    String? uid,
    String? gender,
    String? roomId,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_qmatch_receive_click',
      params: {
        'type': type ?? '',
        'act': act ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配弹窗成功展示时
  ///
  /// [type] 区分匹配弹窗的类别，chat=语音房邀请匹配，game=游戏房邀请匹配，返回后端定义字段
  /// [uid] 接收方的id
  /// [gender] 接收方的性别
  /// [roomId] 邀请房间的roomid
  static void reportRoomQmatchReceiveImp({
    String? type,
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_qmatch_receive_imp',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房快速匹配接口请求结束时
  ///
  /// [result] succ/ fail
  /// [reason] 上报具体错误码
  /// [type] chat=语音房邀请匹配，game=游戏房邀请匹配，后端返回对应子段
  /// [uid]
  /// [gender]
  /// [roomId] 发出邀请的房间ID
  /// [duration] 请求时长
  static void reportRoomQmatchRequestEnd({
    String? result,
    String? reason,
    String? type,
    String? uid,
    String? gender,
    String? roomId,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_qmatch_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 房间内榜单展示
  ///
  /// [uid]
  /// [gender]
  static void reportRoomRankingImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_ranking_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 与我相关房间列表界面操作
  ///
  /// [uid]
  /// [gender]
  /// [act] 操作动作，gofind=最近进入房间列表跳转至explore房间列表，follow=关注单个推荐的房间，followall=一键关注所有推荐的房间
  static void reportRoomRelatedAction({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_related_action',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 与我相关房间列表界面展示（单个房间标签类型展示只上报一次，切换标签类型再上报一次）
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报切换到的表情，recently=最近去过的房间，following=我关注的房间，managed=我管理的房间
  static void reportRoomRelatedImp({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_related_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房设置管理员
  ///
  /// [uid] 设置人id
  /// [gender] 设置人性别
  /// [toUid] 被设置人id
  /// [toGender] 被设置人性别
  /// [roomId] 房间id
  /// [status] 上报设置动作，0=取消管理员，1=设为管理员
  /// [type] 房间类型上报，chat/game
  /// [roomType] live=直播房间，voice=语音房
  static void reportRoomSetAdmin({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? status,
    String? type,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_set_admin',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'type': type ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 点击房间设置内选项
  ///
  /// [content] 上报点击修改的设置名称，cover=封面，name=名称，tag=标签，announcement=公告，theme=房间背景，lock=上锁，permission=麦克风权限，blocklist=黑名单列表查看，cancel=取消弹窗，unlock=解锁
  /// [uid]
  /// [gender]
  /// [type] 上报房间类型
  /// [roomId]
  static void reportRoomSettingClick({
    String? content,
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_setting_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房设置页展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前房间类型
  /// [roomId]
  static void reportRoomSettingImp({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_setting_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击私聊中语音房邀请信息，单次对话只上报一次
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [status] 当前房间开关状态，0=已关闭，1=开播中
  /// [type] 上报邀请的房间类型，chat/game
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportRoomShareChatClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? status,
    String? type,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_share_chat_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房邀请信息在私聊中展示，单次对话只上报一次
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [status] 当前房间开关状态，0=已关闭，1=开播中
  /// [type] 上报邀请的房间类型，chat/game
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportRoomShareChatImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? status,
    String? type,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_share_chat_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房点击分享
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [act] //分享场景区分 friends, facebook, instagram, whatsapp, snapchat, system (更多)
  /// [roomId]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportRoomShareClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? act,
    String? roomId,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'act': act ?? '',
        'room_id': roomId ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房分享弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报分享弹窗点击来源，screen=公屏，more=右上角退出房间旁，auto=创建房间自动拉起
  /// [type] 上报房间类型
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportRoomShareImp({
    String? uid,
    String? gender,
    String? from,
    String? type,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_share_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房用户点击上麦
  ///
  /// [status] 上报上麦方式，1=自己上麦，2=邀请上麦，3=申请上麦
  /// [roomId] 房间id
  /// [uid]
  /// [gender]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  /// [ownerUid] 房主id
  /// [isMyRoom] 是否是我的房间 1= 是， 2 = 不是
  static void reportRoomTakeMic({
    String? status,
    String? roomId,
    String? uid,
    String? gender,
    String? mode,
    String? ownerUid,
    String? isMyRoom,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic',
      params: {
        'status': status ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'mode': mode ?? '',
        'owner_uid': ownerUid ?? '',
        'is_my_room': isMyRoom ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房房主和管理员点击接受请求上麦
  ///
  /// [type] 上报房间类型
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 上报点击接受的来源，展示上麦请求展示位置，screen=公屏，list=上麦申请列表
  static void reportRoomTakeMicRequestAllow({
    String? type,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_request_allow',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房房主和管理员点击清空上麦请求
  ///
  /// [type] 上报房间类型
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoomTakeMicRequestEmpty({
    String? type,
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_request_empty',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 申请上麦列表展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoomTakeMicRequestImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_request_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房房主和管理员收到申请上麦请求时
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportRoomTakeMicRequestReceive({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_request_receive',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户发出请求上麦动作时
  ///
  /// [type] 上报当前房间类型
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报点击来源，position=点击麦位，button=点击上麦区域位置
  static void reportRoomTakeMicRequestSend({
    String? type,
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_request_send',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 用户上麦成功时
  ///
  /// [type] 上报房间类型
  /// [status] 上报上麦方式，0=自己上麦，1=邀请上麦，2=申请上麦，3=引导弹窗上麦
  /// [roomId]
  /// [uid]
  /// [gender]
  /// [from] 上报进入房间来源，followlist=关注的房间列表，managedlist=管理的房间列表，all=全部房间列表，taglist=标签房间列表，chat=私聊邀请进入，notification=开播通知进入，push=push进入，match=房间匹配弹窗，其他方式上报other，recently=最近进入的房间，ranking=排行榜进入,explore=探索页国家列表进入
  /// [frameUrl] 上麦时，佩戴的头像框url
  static void reportRoomTakeMicSucc({
    String? type,
    String? status,
    String? roomId,
    String? uid,
    String? gender,
    String? from,
    String? frameUrl,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_take_mic_succ',
      params: {
        'type': type ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'frame_url': frameUrl ?? '',
      },
      priority: priority,
    );
  }

  /// 上报用户在对应模式持续时长
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 根据后端返回字段上报房间模式名称
  /// [duration ] 以s上报具体时长
  static void reportRoomTypeDuration({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? duration ,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_type_duration',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'duration ': duration  ?? '',
      },
      priority: priority,
    );
  }

  /// 房间解锁成功时
  ///
  /// [roomId] 房间id
  /// [uid]
  /// [gender]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportRoomUnlockEnd({
    String? roomId,
    String? uid,
    String? gender,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_unlock_end',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房点击解锁麦位
  ///
  /// [uid]
  /// [gender]
  /// [roomId] 上报当前房间id
  /// [type] 房间类型上报，chat/game
  static void reportRoomUnlockMic({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_unlock_mic',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户点击操作资料卡上按钮
  ///
  /// [content] chat=聊天，follow=关注，gift=发送礼物，admin=设为/取消admin，leavemic=踢麦，leaveroom=踢出房，@=艾特用户，report=举报，info=跳转他人资料页
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId]
  /// [type] 房间类型上报，chat/game
  static void reportRoomUserInfoClick({
    String? content,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_user_info_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 房间内用户资料展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 房间类型上报，chat/game
  /// [toUid]
  /// [toGender]
  static void reportRoomUserInfoImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_user_info_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音房欢迎新人进房消息点击动作
  ///
  /// [act] invite=邀请上麦，welcome=发送欢迎语
  /// [status] 操作者当前身份，owner=房主，admin=管理员，member=关注者，visitor=游客。
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportRoomWelcomeClick({
    String? act,
    String? status,
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_welcome_click',
      params: {
        'act': act ?? '',
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击房间的小窗
  ///
  /// [roomId]
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportRoomWindowClick({
    String? roomId,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_window_click',
      params: {
        'room_id': roomId ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk点击取消匹配时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkMatchingCancel({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_matching_cancel',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 钻石/星光值待领取入口曝光时上报
  static void reportStarLightGet({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'star_light_get',
      priority: priority,
    );
  }

  /// 充值结果展示
  ///
  /// [content] 用户钻石余额
  /// [uid]
  /// [gender]
  /// [type] 上报充值类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，diamonds=正常渠道充值，discountgift=折扣礼物，bigbonus=大额充值弹窗
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  /// [amout] 当前充值钻石数
  static void reportTopupResultImp({
    String? content,
    String? uid,
    String? gender,
    String? type,
    String? couponId,
    String? amout,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_result_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
        'amout': amout ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险进入气泡动画展示时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTruthAnimeImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_anime_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户点击真心话大冒险屏幕界面操作按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] join=加入游戏，start=开始游戏
  static void reportTruthClick({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险倒计时开始时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTruthCountdownImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_countdown_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTruthEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险游戏开始
  ///
  /// [roomId] 房间ID
  /// [uid] 游戏开始时，所有麦上用户上报一次
  /// [hostUid] 游戏控制人uid
  /// [ownerUid] 房主uid
  static void reportTruthGameStart({
    String? roomId,
    String? uid,
    String? hostUid,
    String? ownerUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_game_start',
      params: {
        'room_id': roomId ?? '',
        'uid': uid ?? '',
        'host_uid': hostUid ?? '',
        'owner_uid': ownerUid ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险游戏界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTruthImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 被惩罚者选择结果弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid] 上报被惩罚者的uid
  /// [toGender] 上报被惩罚者的性别
  static void reportTruthPunishmentImp({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_punishment_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 被惩罚者选择惩罚手段时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 上报当前用户选择惩罚是真心话还是大冒险，truth / dare
  /// [toUid] 上报被惩罚者的uid
  /// [toGender] 上报被惩罚者的性别
  static void reportTruthPunishmentSelecrt({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_punishment_selecrt',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 真心话大冒险结果弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] 上报当前惩罚结果文案
  /// [toUid] 受到惩罚的用户uid
  /// [toGender] 受到惩罚的用户性别
  static void reportTruthResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'truth_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 解除房间拉黑成功时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房
  static void reportUnblockRoomSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'unblock_room_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 上传房间背景图片入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportUploadRoombackgroundClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_roombackground_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 上传房间背景入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportUploadRoombackgroundImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_roombackground_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 上传房间背景成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportUploadRoombackgroundSucc({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_roombackground_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 上报用实际观看视频的时长
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [duration] 以s上报
  /// [content] 上报用户具体的视频内容
  /// [type] 1 youtube id, 2 点播视频 id ，3 点播url
  static void reportVideoPlayingDuration({
    String? uid,
    String? gender,
    String? roomId,
    String? duration,
    String? content,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_playing_duration',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'duration': duration ?? '',
        'content': content ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击H5页面游戏时上报，需要报告入口来源
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [gameId] 游戏 id
  /// [from] screen公屏，casual games
  static void reportWebGameClick({
    String? uid,
    String? gender,
    String? gameId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'web_game_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'game_id': gameId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
