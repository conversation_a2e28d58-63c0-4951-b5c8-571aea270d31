import 'package:feature_flat_base/feature_flat_base.dart';

class UnoStatistics {
  UnoStatistics._();

  /// 在大厅中点击了Uno入口按钮时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  static void reportHomepageUnoClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'homepage_uno_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// Uno模式选择界面展示时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [from] 上报进入房间的上一级来源，客户端定义路由，匹配入口，房间入口，好友分享入口，好友邀请入口
  static void reportUnoImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// Uno游戏界面加载结束时时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [result] succ/fail，成功/失败
  /// [reason] 失败原因错误码
  /// [duration] 加载时长
  static void reportUnoLoadingImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? result,
    String? reason,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_loading_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// Uno游戏界面加载开始时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  static void reportUnoLoadingStart({
    String? gameroomId,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_loading_start',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配页面结束展示时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [result] succ/fail，匹配成功/失败
  /// [reason] 失败原因错误码
  /// [duration] match匹配时长，单位s
  static void reportUnoMatchImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? result,
    String? reason,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_match_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// Uno结算界面，点击再来一局
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  static void reportUnoPlayAgain({
    String? gameroomId,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_play_again',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// Uno开始游戏时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [duration] start，开始时长，单位s
  static void reportUnoStart({
    String? gameroomId,
    String? uid,
    String? gender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_start',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// Uno模式选择界面，点击start按钮时上报
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [content] 入场费
  static void reportUnoStartClick({
    String? gameroomId,
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'uno_start_click',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }
}
