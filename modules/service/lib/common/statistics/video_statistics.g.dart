import 'package:feature_flat_base/feature_flat_base.dart';

class VideoStatistics {
  VideoStatistics._();

  /// 一起看视频模式点击关闭视频
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 上报播放的来源，reclist=推荐列表，search=搜索播放的视频，cloud=网盘播放的视频，live=直播视频
  /// [duration] 当前观看视频的时长，单位s
  static void reportVideoCloseClick({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_close_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 成功关闭一起看视频模式下上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoCloseSucc({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_close_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击一起看视频界面入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频在function入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 操作视频播放动作
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] play=播放视频，pause=暂停播放，last=上一集，next=下一集，fullscreen=全屏，search=重新拉起播放视频
  /// [status] 上报播放的来源，reclist=推荐列表，search=搜索播放的视频，cloud=网盘播放的视频，live=直播视频
  /// [content] 上报播放的视频url或视频id
  static void reportVideoPlayAction({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_play_action',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频模式下功能使用持续时长，在关闭房间模式或退出房间时上报，以s上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [duration] 上报时长，以s上报
  static void reportVideoPlayDuration({
    String? uid,
    String? gender,
    String? roomId,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_play_duration',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频播放失败展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [code] 上报错误码
  /// [reason] 如有错误详情时上报错误详情
  /// [status] 上报播放的来源，reclist=推荐列表，search=搜索播放的视频，cloud=网盘播放的视频，live=直播视频
  /// [content] 上报播放的视频url或视频id
  static void reportVideoPlayErrorImp({
    String? uid,
    String? gender,
    String? roomId,
    String? code,
    String? reason,
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_play_error_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'code': code ?? '',
        'reason': reason ?? '',
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频正处于等待视频界面选择时展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoPlayPendingImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_play_pending_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频 ，视频成功播放时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 上报成功播放的来源，reclist=推荐列表，search=搜索播放的视频，cloud=网盘播放的视频，live=直播视频
  /// [content] 上报播放的视频url或视频id
  static void reportVideoPlaying({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_playing',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频推荐列表展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoReclistImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_reclist_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频推荐列表点击视频播放
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoReclistPlay({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_reclist_play',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 一起看视频在推荐列表界面搜索视频成功
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVideoReclistSearchSucc({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'video_reclist_search_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }
}
