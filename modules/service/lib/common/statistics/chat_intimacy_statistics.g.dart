import 'package:feature_flat_base/feature_flat_base.dart';

class ChatIntimacyStatistics {
  ChatIntimacyStatistics._();

  /// 产生聊天亲密度时上报
  ///
  /// [gender] 自己的性别
  /// [toUid] 对方的uid
  /// [toGender] 对方的性别
  /// [coinChg] 当前增加的亲密度值
  /// [coinChgBefore] 变更后的亲密度值
  /// [coin] 钻石消耗
  /// [type] send_msg 发消息chat_gift 私聊送礼
  static void reportChatIntimacyFlow({
    String? gender,
    String? toUid,
    String? toGender,
    String? coinChg,
    String? coinChgBefore,
    String? coin,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_intimacy_flow',
      params: {
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'coin_chg': coinChg ?? '',
        'coin_chg_before': coinChgBefore ?? '',
        'coin': coin ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
