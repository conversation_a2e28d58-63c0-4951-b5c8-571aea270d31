import 'package:feature_flat_base/feature_flat_base.dart';

class RtcStatistics {
  RtcStatistics._();

  /// 声网进入频道的打点上报，仅以joinChannel的返回值判断
  ///
  /// [from] voice_match：语音匹配，im_call：im打电话进入语音房有其他打点，此处忽略
  /// [status] 0：失败，1：成功
  /// [roomId] 频道id
  /// [code] 进房返回状态码
  static void reportRtcJoinChannel({
    String? from,
    String? status,
    String? roomId,
    String? code,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rtc_join_channel',
      params: {
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'code': code ?? '',
      },
      priority: priority,
    );
  }
}
