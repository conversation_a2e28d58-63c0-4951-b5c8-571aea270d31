import 'package:feature_flat_base/feature_flat_base.dart';

class MsgListStatistics {
  MsgListStatistics._();

  /// 接通中的音频通话被挂断时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [callId] 本次音频通话对应的ID 
  /// [duration] 本次通话时长，单位s
  /// [type] sender_send发起方挂断/receiver_end接听芳挂断/auto超时等情况引起自动挂断；挂断方式
  static void reportChatCallVoiceEnd({
    String? matchType,
    String? toUid,
    String? toGender,
    String? callId,
    String? duration,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_end',
      params: {
        'match_type': matchType ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'call_id': callId ?? '',
        'duration': duration ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在前台接到音频通话时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [micAuth] 0/1；麦克风权限，0无权限，1有权限
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceReceive({
    String? matchType,
    String? toUid,
    String? toGender,
    String? micAuth,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_receive',
      params: {
        'match_type': matchType ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'mic_auth': micAuth ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户点击接听音频通话
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [micAuth] 0/1；麦克风权限，0无权限，1有权限
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceReceiverAnswer({
    String? matchType,
    String? toUid,
    String? toGender,
    String? micAuth,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_receiver_answer',
      params: {
        'match_type': matchType ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'mic_auth': micAuth ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户拒绝接听音频通话
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [micAuth] 0/1；麦克风权限，0无权限，1有权限
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceReceiverReject({
    String? matchType,
    String? toUid,
    String? toGender,
    String? micAuth,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_receiver_reject',
      params: {
        'match_type': matchType ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'mic_auth': micAuth ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户发起音频通话
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceSend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_send',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 等待接听时，音频通话发起方点击cancel
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [duration] 从发起到cancel时等待时长，单位s
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceSenderCancel({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? duration,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_sender_cancel',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'duration': duration ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 音频通话一直未接听超过30s自动挂断时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [callId] 本次音频通话对应的ID
  static void reportChatCallVoiceTimeout({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_call_voice_timeout',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 会话创建-当用户在一个会话中第一次发出一个消息时（只记录主动方）
  ///
  /// [matchType] fate_bell_list 恋爱铃列表fate_bell_pop 恋爱铃弹窗wink wink列表home 首页reco_list 推荐动态moment_detail 动态详情me_visitor 我的访客列表push  推送share_chat 分享弹窗me_follower 我的粉丝follow_list 关注动态列表me_following  我的关注的人列表userpage_chat 个人主页点聊天chat 会话列表theme_aggregate 主题聚合页room_profile房间跳转个人主页room_mini_profile房间个人卡片other 为空/user/info 他人个人资料页
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [roomId] match_type=room_profile、room_mini_profile时，上报room_id
  /// [ownerUid] 上报房间ID时带上房主ID
  /// [agencyId] 自己是否加入机构  0=未加入，加入的上报机构id
  /// [toAgencyId] 对方是否加入机构  0=未加入，加入的上报机构id
  static void reportChatCreate({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? roomId,
    String? ownerUid,
    String? agencyId,
    String? toAgencyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_create',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
        'agency_id': agencyId ?? '',
        'to_agency_id': toAgencyId ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天内/ profile引导进房横幅状态点击进入房间
  ///
  /// [type] 区分是聊天内或者profile内展示，chat=聊天，profile=个人资料内
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId] 对方对应的room_id
  static void reportChatGuideRoomClick({
    String? type,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_guide_room_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天内/ profile引导进房横幅状态展示
  ///
  /// [type] 区分是聊天内或者profile内展示，chat=聊天，profile=个人资料内
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportChatGuideRoomImp({
    String? type,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_guide_room_imp',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击对应助手消息时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 点击的助手id
  static void reportChatlistAssistantClick({
    String? uid,
    String? gender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chatlist_assistant_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天列表对应的助手消息展示时上报，只上报助手类型的消息展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 助手id，比如附近的人，声音匹配卡片
  static void reportChatlistAssistantImp({
    String? uid,
    String? gender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chatlist_assistant_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在app内收到IM消息时（冷启动时一次性上报离线时的所有消息数量，打一个点，数量上报在子参；使用app期间内每收到一次消息上报一次
  ///
  /// [content] 收到的消息数量，上报数字
  /// [type] 上报类型：app_launch=打开app一次性上报所有离线收到的消息；in_app=app内收到的消息
  /// [toUid] 发送消息的id 
  /// [toGender] 发送消息的性别
  static void reportImMsgReceive({
    String? content,
    String? type,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'im_msg_receive',
      params: {
        'content': content ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 会话中自动发送破冰消息
  ///
  /// [type] 上报发送的消息类型，text/pb(表情包)
  /// [result] succ/fail
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  static void reportMsgAutoSend({
    String? type,
    String? result,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? matchType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_auto_send',
      params: {
        'type': type ?? '',
        'result': result ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'match_type': matchType ?? '',
      },
      priority: priority,
    );
  }

  /// 小助手信息点击跳转带url链接的内容
  ///
  /// [content] 上报所跳转的message id
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportMsgListAssisantJump({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_assisant_jump',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小助手界面曝光展示
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报小助手当前页面的文本信息id
  static void reportMsgListAssistantImp({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_assistant_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击发送语音消息入口
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [micAuth] 0/1；点击时是否有麦克风权限，0无权限，1有权限
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId] 群组id
  static void reportMsgListAudio({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? micAuth,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'mic_auth': micAuth ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-语音消息取消录制时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [duration] 语音消息时长，单位s
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListAudioCancel({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? duration,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio_cancel',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'duration': duration ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-语音消息开始录制
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [groupId]
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  static void reportMsgListAudioRecord({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    String? groupId,
    String? userType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio_record',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'group_id': groupId ?? '',
        'user_type': userType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-语音消息发送时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [duration] 语音消息时长，单位s
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListAudioSend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? duration,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio_send',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'duration': duration ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-语音消息过长的toast提示出现时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [groupId]
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  static void reportMsgListAudioToolong({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    String? groupId,
    String? userType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio_toolong',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'group_id': groupId ?? '',
        'user_type': userType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-发送语音消息过短的toast提示出现时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListAudioTooshort({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_audio_tooshort',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击聊天对象头像
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [isMsgHistory] 0/1；展示时是否已有聊天记录（破冰信息不算），0无聊天记录，1有聊天记录
  static void reportMsgListAvatar({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? isMsgHistory,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_avatar',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'is_msg_history': isMsgHistory ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击音视频通话入口
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [micAuth] 0/1；是否有麦克风权限
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListCall({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? micAuth,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_call',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'mic_auth': micAuth ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 破冰消息-客户端请求结束时
  ///
  /// [result] succ/fail；请求结果
  /// [content] 破冰引导数量，仅成功时上报
  /// [reason] 失败原因，仅失败时上报
  /// [onTime] 请求耗时，单位毫秒
  static void reportMsgListChatguideRequestEnd({
    String? result,
    String? content,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_chatguide_request_end',
      params: {
        'result': result ?? '',
        'content': content ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天消息列表停留时长，离开/切换页面/app切到后台时上报
  ///
  /// [from] qmatch_succ手动匹配成功/qmatch_receive被匹配弹窗/userpage_chat用户主页聊天入口/moment_item_more_chat动态更多弹窗聊天入口/chat_list聊天会话列表/push推送；进入消息列表的方式/ voice_match 声音问题匹配/ verify_profile 声音卡片速配 / bottle 漂流瓶
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [duration] 页面停留时长，单位s
  /// [groupId] 群组id
  static void reportMsgListDuration({
    String? from,
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? duration,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_duration',
      params: {
        'from': from ?? '',
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'duration': duration ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// IM内文字彩蛋展示时
  ///
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] to_gender
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] 触发彩蛋的文字
  static void reportMsgListEastereggImp({
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_easteregg_imp',
      params: {
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击emoji入口
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [groupId] 群组id
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  static void reportMsgListEmoji({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? groupId,
    String? userType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_emoji',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
        'user_type': userType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户左滑/点击左上角返回按钮退出页面
  ///
  /// [from] qmatch_succ手动匹配成功/qmatch_receive被匹配弹窗/userpage_chat用户主页聊天入口/moment_item_more_chat动态更多弹窗聊天入口/chat_list聊天会话列表/push推送；进入消息列表的方式/ voice_match 声音问题匹配/ verify_profile 声音卡片速配/ bottle 漂流瓶
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [isMsgHistory] 0/1；展示时是否已有聊天记录（破冰信息不算），0无聊天记录，1有聊天记录
  /// [chatType] 上报退出时当前聊天模式，报safe/free
  /// [groupId] 群组id
  static void reportMsgListExit({
    String? from,
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? isMsgHistory,
    String? chatType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_exit',
      params: {
        'from': from ?? '',
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'is_msg_history': isMsgHistory ?? '',
        'chat_type': chatType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-打招呼引导内容点击时
  ///
  /// [from] qmatch_succ手动匹配成功/qmatch_receive被匹配弹窗/userpage_chat用户主页聊天入口/moment_item_more_chat动态更多弹窗聊天入口/chat_list聊天会话列表/push推送；进入消息列表的方式/ voice_match 声音问题匹配/ verify_profile 声音卡片速配
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] 用户选择的打招呼的信息内容
  static void reportMsgListGreetingClick({
    String? from,
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_greeting_click',
      params: {
        'from': from ?? '',
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-打招呼引导展示时
  ///
  /// [from] qmatch_succ手动匹配成功/qmatch_receive被匹配弹窗/userpage_chat用户主页聊天入口/moment_item_more_chat动态更多弹窗聊天入口/chat_list聊天会话列表/push推送；进入消息列表的方式/ voice_match 声音问题匹配/ verify_profile 声音卡片速配
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportMsgListGreetingImp({
    String? from,
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_greeting_imp',
      params: {
        'from': from ?? '',
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 与聊天对象的消息列表展示时
  ///
  /// [from] qmatch_succ手动匹配成功/qmatch_receive被匹配弹窗/userpage_chat用户主页聊天入口/moment_item_more_chat动态更多弹窗聊天入口/chat_list聊天会话列表/push推送；进入消息列表的方式/ voice_match 声音问题匹配/ verify_profile 声音卡片速配 / bottle 漂流瓶/ moment_quick moment主页点击聊天
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [isMsgHistory] 0/1；展示时是否已有聊天记录（破冰信息不算），0无聊天记录，1有聊天记录
  /// [chatType] 上报当前聊天模式，报safe/free
  /// [groupId] 聊天的群组id
  static void reportMsgListImp({
    String? from,
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? isMsgHistory,
    String? chatType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_imp',
      params: {
        'from': from ?? '',
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'is_msg_history': isMsgHistory ?? '',
        'chat_type': chatType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击发送媒体文件入口
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [photoAuth] 0/1；点击时是否有相册/外部存储权限，0无权限，1有权限
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListMedia({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? photoAuth,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'photo_auth': photoAuth ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-普通图片/视频消息预览页停留时长
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] msg_id；对应消息的ID
  /// [type] image/video；预览的媒体文件类型
  /// [duration] 停留时长，单位s
  static void reportMsgListMediaPreviewDuration({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    String? type,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_preview_duration',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-普通图片/视频消息预览页展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] msg_id；对应消息的ID
  /// [type] image/video；预览的媒体文件类型
  static void reportMsgListMediaPreviewImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_preview_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户相册媒体文件选择页面展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] bf_guide阅后即焚引导本次不出现/af_guide阅后即焚引导本次出现
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListMediaSelectImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_select_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户相册媒体文件选择预览页面展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [isSelfDelete] 0/1；是否勾选阅后即焚，0未选择，1选择
  /// [from] bf_guide阅后即焚引导本次不出现/af_guide阅后即焚引导本次出现
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListMediaSelectPreviewImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? isSelfDelete,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_select_preview_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'is_self_delete': isSelfDelete ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户相册媒体文件选择预览页面，用户点击发送时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；点击完成时选择的文件类型
  /// [numImage] 选择的图片数量，没有则为空
  /// [size] 上传的媒体文件的大小，单位Mb
  /// [isSelfDelete] 0/1；是否勾选阅后即焚，0未选择，1选择
  /// [from] bf_guide阅后即焚引导本次不出现/af_guide阅后即焚引导本次出现
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListMediaSelectPreviewSend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? numImage,
    String? size,
    String? isSelfDelete,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_select_preview_send',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'num_image': numImage ?? '',
        'size': size ?? '',
        'is_self_delete': isSelfDelete ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户相册媒体文件选择页面，用户点击发送时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；点击完成时选择的文件类型
  /// [numImage] 选择的图片数量，没有则为空
  /// [size] 上传的媒体文件的大小，单位Mb
  /// [isSelfDelete] 0/1；是否勾选阅后即焚，0未选择，1选择
  /// [from] bf_guide阅后即焚引导本次不出现/af_guide阅后即焚引导本次出现
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgListMediaSelectSend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? numImage,
    String? size,
    String? isSelfDelete,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_select_send',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'num_image': numImage ?? '',
        'size': size ?? '',
        'is_self_delete': isSelfDelete ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-列表上的阅后即焚消息点击
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；发送的消息类型
  /// [content] msg_id，对应消息的ID
  /// [from] self_send自己的消息/to_send对方发送的消息；点击查看的消息由谁发送
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListMediaSelfDeleteClick({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? content,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_self_delete_click',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端下载单个阅后即焚文件结束时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；消息类型
  /// [content] msg_id，对应消息的ID
  /// [from] self_send自己的消息/to_send对方发送的消息；点击查看的消息由谁发送
  /// [size] 文件大小，单位Mb
  /// [result] succ/fail；下载结果
  /// [reason] 失败原因
  /// [duration] 耗时，单位s
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListMediaSelfDeleteDownloadEnd({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? content,
    String? from,
    String? size,
    String? result,
    String? reason,
    String? duration,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_self_delete_download_end',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'size': size ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-阅后即焚预览页停留时长
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；消息类型
  /// [content] msg_id，对应消息的ID
  /// [from] self_send自己的消息/to_send对方发送的消息；点击查看的消息由谁发送
  /// [duration] 停留时长，单位s
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListMediaSelfDeletePirevidewDuration({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? content,
    String? from,
    String? duration,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_self_delete_pirevidew_duration',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'duration': duration ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-阅后即焚预览引导页展示时（有引导长按的动画的页面）
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；消息类型
  /// [content] msg_id，对应消息的ID
  /// [from] self_send自己的消息/to_send对方发送的消息；点击查看的消息由谁发送
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListMediaSelfDeletePirevidewGuide({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? content,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_self_delete_pirevidew_guide',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-阅后即焚预览页展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] image/video；消息类型
  /// [content] msg_id，对应消息的ID
  /// [from] self_send自己的消息/to_send对方发送的消息；点击查看的消息由谁发送
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListMediaSelfDeletePirevidewImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? content,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_media_self_delete_pirevidew_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 当前聊天模式系统消息提示曝光
  ///
  /// [type] 当前聊天模式系统提示，上报safe/free
  /// [uid]
  /// [gender]
  /// [toUid] 聊天对方id
  /// [toGender] 聊天对方性别
  static void reportMsgListModeTips({
    String? type,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_mode_tips',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-长按消息的更多操作上产生点击时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [act] reply/copy/report/delete；点击操作
  /// [content] msg_id；对应消息的ID
  static void reportMsgListMsgMoreAct({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? act,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_msg_more_act',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'act': act ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-长按消息的更多操作展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] msg_id；对应消息的ID
  static void reportMsgListMsgMoreImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_msg_more_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 会话消息列表上点击follow时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [isMsgHistory] 0/1；展示时是否已有聊天记录（破冰信息不算），0无聊天记录，1有聊天记录
  static void reportMsgListNaviFollow({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowed,
    String? isMsgHistory,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_navi_follow',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_followed': isFollowed ?? '',
        'is_msg_history': isMsgHistory ?? '',
      },
      priority: priority,
    );
  }

  /// 会话消息列表上点击更多入口时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportMsgListNaviMore({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_navi_more',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户进行截图时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListScreenshot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_screenshot',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-截图弹窗展示时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListScreenshotPopoutImp({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_screenshot_popout_imp',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-消息设置页点击设置项时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [act] avatar/remark/follow/unfollow/pin/unpin/clear_chat/report/block/unblock/delete
  static void reportMsgListSettingAct({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_setting_act',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端收到消息风险提醒时（风险信息接收方）
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysRiskReceiverGot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_risk_receiver_got',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端收到风险消息引导举报提醒时（接收方解锁风险消息后）
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysRiskReceiverGuideGot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_risk_receiver_guide_got',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-风险消息引导举报提醒上，用户点击举报
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysRiskReceiverGuideReport({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_risk_receiver_guide_report',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户点击解锁风险消息
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysRiskReceiverUnlock({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_risk_receiver_unlock',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端收到社区规范提醒时（风险信息发送方收到社区规范提醒）
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysRiskSenderGot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_risk_sender_got',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端收到截图提醒时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysScreenshotGot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_screenshot_got',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-客户端收到对方用户违规提醒（给对方发消息，对方被封号场景下）
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  static void reportMsgListSysUserViolateGot({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? from,
    String? chatType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_sys_user_violate_got',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户点击文本消息发送
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [content] 发送内容的字符数
  /// [from] im=正常im，bottle=漂流瓶
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId] 群组id
  static void reportMsgListTextSend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? content,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_text_send',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表破冰信息上查看主页按钮点击时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isMsgHistory] 0/1；展示时是否已有聊天记录（破冰信息不算），0无聊天记录，1有聊天记录
  static void reportMsgListViewProfile({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isMsgHistory,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_view_profile',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_msg_history': isMsgHistory ?? '',
      },
      priority: priority,
    );
  }

  /// 变更模式二级页面曝光
  ///
  /// [type] 上报当前的聊天模式，safe/free
  /// [uid]
  /// [gender]
  /// [from] 区分是点击消息还是按钮进入二级页面，上报im=点击消息，按钮=button
  static void reportMsgModeChangeImp({
    String? type,
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_change_imp',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 变更模式申请二次确认弹窗点击接受
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 上报展示的位置，im=聊天消息列表，change=变更模式页
  static void reportMsgModeInvitationAccept({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_invitation_accept',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击忽略变更模式申请按钮
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 上报展示的位置，im=聊天消息列表，change=变更模式页
  static void reportMsgModeInvitationIgnore({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_invitation_ignore',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 变更聊天模式申请展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 上报展示的位置，im=聊天消息列表，change=变更模式页
  static void reportMsgModeInvitationImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_invitation_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 变更聊天模式功能弹窗展示
  ///
  /// [from] 上报当前弹窗出现的类型，notime=当天没有变更次数，confirm=二次确认弹窗，expired=请求已过期弹窗
  /// [uid]
  /// [gender]
  static void reportMsgModePopoutImp({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_popout_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求聊天模式接口结束
  ///
  /// [duration] 上报请求时间
  /// [status] 当次是否请求成功
  /// [reason] 失败错误码
  /// [type] 上报请求的action，check=查询聊天模式 start=开启自由 ignore=忽略 accept=接受
  /// [uid]
  /// [gender]
  static void reportMsgModeRequestEnd({
    String? duration,
    String? status,
    String? reason,
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_request_end',
      params: {
        'duration': duration ?? '',
        'status': status ?? '',
        'reason': reason ?? '',
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求聊天模式接口
  ///
  /// [type] 上报请求的action，check=查询聊天模式 start=开启自由 ignore=忽略 accept=接受
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportMsgModeRequestStart({
    String? type,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_request_start',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 变更模式界面点击下方操作按钮
  ///
  /// [status] 上报当前点击的按钮状态，enable=直接开启，invite=邀请开启，withdraw=撤销申请，ignore=拒绝申请，accept=接受申请
  /// [uid]
  /// [gender]
  /// [type] 上报当前选中的聊天模式，safe/free
  /// [from] 区分是点击消息还是按钮进入二级页面，上报im=点击消息，按钮=button
  static void reportMsgModeSelectClick({
    String? status,
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_select_click',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 变更模式界面选中单个模式时
  ///
  /// [type] 上报当前选中的聊天模式，safe/free
  /// [from] 区分是点击消息还是按钮进入二级页面，上报im=点击消息，按钮=button
  /// [uid]
  /// [gender]
  /// [status] 上报当前选中模式的状态，enabled=开启中，close=未开启，request=已发出去申请待对方处理，pending=待当前用户处理。
  static void reportMsgModeSelectImp({
    String? type,
    String? from,
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_mode_select_imp',
      params: {
        'type': type ?? '',
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 一级页面消息提示出现后，点击进入对应页面
  ///
  /// [uid]
  /// [gender]
  /// [page] home/moment/live/chat/mine
  /// [type] 返回当前的消息类型
  static void reportMsgNotifyClick({
    String? uid,
    String? gender,
    String? page,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_notify_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 一级界面顶部聊天消息展示
  ///
  /// [uid]
  /// [gender]
  /// [page] 上报当前消息展示的界面，home/moment/live/chat/mine
  /// [type] 返回当前的消息类型
  static void reportMsgNotifyImp({
    String? uid,
    String? gender,
    String? page,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_notify_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 提醒女性用户回复的提示展示，单个对话只上报一次。
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportMsgReplyTips({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_reply_tips',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-用户点击重新发送某个消息时
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] text/audio/image/video；发送的消息类型
  /// [isSelfDelete] 0/1；是否阅后即焚
  /// [size] 发送消息的大小，单位Mb（文本消息可不上报）
  /// [content] msg_id，对应消息的ID
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前的聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  static void reportMsgResend({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? isSelfDelete,
    String? size,
    String? content,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_resend',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'is_self_delete': isSelfDelete ?? '',
        'size': size ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-单个消息发送结束时（这里仅指用户主动发送的消息，不包括客户端自己发送的系统消息）
  ///
  /// [matchType] fate_bell_list 恋爱铃列表fate_bell_pop 恋爱铃弹窗wink wink列表home 首页reco_list 推荐动态moment_detail 动态详情me_visitor 我的访客列表push  推送share_chat 分享弹窗me_follower 我的粉丝follow_list 关注动态列表me_following  我的关注的人列表userpage_chat 个人主页点聊天chat 会话列表theme_aggregate 主题聚合页room_profile房间个人主页room_mini_profile房间个人卡片hq_user 匹配高质量用户other 为空
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [type] text/audio/image/video/stickers/gifts/autoicebreak；发送的消息类型
  /// [isSelfDelete] 0/1；是否阅后即焚
  /// [size] 发送消息的大小，单位Mb（文本消息可不上报）
  /// [result] succ/fail；消息发送结果
  /// [reason] 失败原因
  /// [duration] 发送耗时，单位s
  /// [content] msg_id，对应消息的ID
  /// [from] 正常im对话=im，漂流瓶对话=bottle
  /// [chatType] 上报当前聊天模式，safe/free
  /// [userType] 普通用户还是群组，普通用户=0，群组=1
  /// [groupId]
  /// [roomId] match_type=room_profile、room_mini_profile时，上报room_id
  /// [ownerUid] 有room_id上报时，带上房主id
  /// [agencyId] 自己是否加入机构  0=未加入，加入的上报机构id
  /// [toAgencyId] 对方是否加入机构  0=未加入，加入的上报机构id
  static void reportMsgSendEnd({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? type,
    String? isSelfDelete,
    String? size,
    String? result,
    String? reason,
    String? duration,
    String? content,
    String? from,
    String? chatType,
    String? userType,
    String? groupId,
    String? roomId,
    String? ownerUid,
    String? agencyId,
    String? toAgencyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_send_end',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'type': type ?? '',
        'is_self_delete': isSelfDelete ?? '',
        'size': size ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'chat_type': chatType ?? '',
        'user_type': userType ?? '',
        'group_id': groupId ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
        'agency_id': agencyId ?? '',
        'to_agency_id': toAgencyId ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天列表用户引导进房点击
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId] 点击进入对应的room id
  /// [from] chat_list=外部列表，group=群聊内
  /// [groupId] type非group则不上报
  static void reportMsgUserGuideClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? from,
    String? groupId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_user_guide_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
        'group_id': groupId ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天列表引导进房用户展示
  ///
  /// [uid]
  /// [gender]
  static void reportMsgUserGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_user_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 引导用户进房推荐请求结束时
  ///
  /// [uid]
  /// [gender]
  /// [act] 请求推荐位置：home / profile
  /// [onTime] 请求时长
  /// [result] succ / fail
  /// [reason] 接口失败原因
  static void reportUserGuideRequestEnd({
    String? uid,
    String? gender,
    String? act,
    String? onTime,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'user_guide_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'on_time': onTime ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }
}
