import 'package:feature_flat_base/feature_flat_base.dart';

class EventStatistics {
  EventStatistics._();

  /// 活动订阅时出现日历弹窗点击添加到系统日历
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  static void reportEventCalendarPopoutClick({
    String? uid,
    String? gender,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_calendar_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动订阅时出现订阅系统日历弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  static void reportEventCalendarPopoutImp({
    String? uid,
    String? gender,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_calendar_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动成功取消时上报
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  static void reportEventCancelSucc({
    String? uid,
    String? gender,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_cancel_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 上报点击活动在设置页，More页中的Mine，friends，Hot页，私聊，moment，房间公屏展示，进入对应活动的详情页
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [page] 上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=explore页，splash=开屏，popup=主页弹窗，portal=悬浮球
  static void reportEventClick({
    String? uid,
    String? gender,
    String? eventId,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 活动创建流程选择封面界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateCoverImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_cover_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 活动创建流程选择封面界面点击保存创建
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateCoverSave({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_cover_save',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动填写描述，名字页展示
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateDesImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_des_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动填写描述，名字页完成点击下一步
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateDesNext({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_des_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 活动创建入口点击
  ///
  /// [uid]
  /// [gender]
  /// [from] 展示创建入口的来源，explore=创建页，setting=设置页，all=全部活动列表页面
  static void reportEventCreateEntranceClick({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动入口展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 展示创建入口的来源，explore=创建页，setting=设置页，all=全部活动列表页面
  static void reportEventCreateEntranceImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动流程选择房间，活动标签页展示
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateRoomImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_room_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动流程选择房间，活动标签页点击下一步
  ///
  /// [uid]
  /// [gender]
  /// [roomId] 上报所挑选的房间id
  /// [tag] 如有event tag则上报，无则不报
  static void reportEventCreateRoomNext({
    String? uid,
    String? gender,
    String? roomId,
    String? tag,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_room_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'tag': tag ?? '',
      },
      priority: priority,
    );
  }

  /// 活动创建成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [roomId] 创建活动的房间id
  static void reportEventCreateSucc({
    String? uid,
    String? gender,
    String? eventId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动选择时间界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateTimeImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_time_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建活动流程选择时间界面完成点击下一步
  ///
  /// [uid]
  /// [gender]
  static void reportEventCreateTimeNext({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_create_time_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 活动详情内点击底部操作区域
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [act] subscribe=订阅，unsubscribe=取消订阅，Live=直接进入房间
  /// [from] 上报活动详情的进入来源，上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=直接在一级explore页进入
  static void reportEventDetailsAction({
    String? uid,
    String? gender,
    String? eventId,
    String? act,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_details_action',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'act': act ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 活动详情页点击进入房间
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [roomId] 上报进入的room_id
  /// [from] 上报活动详情的进入来源，上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=直接在一级explore页进入
  static void reportEventDetailsEnterroom({
    String? uid,
    String? gender,
    String? eventId,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_details_enterroom',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 活动详情展示
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [from] 上报活动详情的进入来源，上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=直接在一级explore页进入
  static void reportEventDetailsImp({
    String? uid,
    String? gender,
    String? eventId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 在explore页面点击event活动
  ///
  /// [uid]
  /// [gender]
  static void reportEventExploreClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_explore_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 活动在explore页展示
  ///
  /// [uid]
  /// [gender]
  static void reportEventExploreImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_explore_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 上报活动在设置页，More页中的Mine，friends，Hot页，私聊，moment，房间公屏展示
  ///
  /// [uid]
  /// [gender]
  /// [page] page=上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=explore页，splash=开屏，popup=主页弹窗，portal=悬浮球
  /// [eventId]
  static void reportEventImp({
    String? uid,
    String? gender,
    String? page,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动点击分享
  ///
  /// [uid]
  /// [gender]
  /// [act] inapp/fb/ins/whatsapp/more/moment
  /// [eventId]
  static void reportEventShareClick({
    String? uid,
    String? gender,
    String? act,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 活动分享弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [from] 上报活动详情的进入来源，上报活动的展示来源，setting=设置页，Mine=活动二级列表Mine页展示，Friends=活动二级列表firends页展示，Hot=活动二级列表Hot页展示，screen=公屏，chat=私聊，moment=moment页，explore=直接在一级explore页进入
  static void reportEventShareImp({
    String? uid,
    String? gender,
    String? eventId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_share_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
