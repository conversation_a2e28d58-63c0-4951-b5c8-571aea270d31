import 'package:feature_flat_base/feature_flat_base.dart';

class RoomHomeStatistics {
  RoomHomeStatistics._();

  /// 打开所有房间活动界
  ///
  /// [uid] 打开活动页面的用户uid
  /// [gender] 打开活动页面的用户性别
  static void reportRoomEventAllImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_all_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击允许日历授权按钮
  ///
  /// [uid] 点击允许日历授权按钮的用户uid
  /// [gender] 点击允许日历授权按钮的用户性别
  static void reportRoomEventCalendarAddClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_calendar_add_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 日历授权弹窗，点击勾选按钮
  ///
  /// [uid] 日历授权弹窗，点击勾选按钮的用户uid
  /// [gender] 日历授权弹窗，点击勾选按钮的用户性别
  static void reportRoomEventCalendarCheck({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_calendar_check',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 弹出日历授权弹窗
  ///
  /// [uid] 弹出日历授权弹窗的用户uid
  /// [gender] 弹出日历授权弹窗的用户性别
  static void reportRoomEventCalendarPopup({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_calendar_popup',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击创建房间活动按钮
  ///
  /// [uid] 点击创建活动按钮的用户uid
  /// [gender] 点击创建活动按钮的用户性别
  /// [result] succ/fail 创建成功/创建失败
  static void reportRoomEventCreateClick({
    String? uid,
    String? gender,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_create_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 打开创建活动界面
  ///
  /// [uid] 打开创建活动页面的用户uid
  /// [gender] 打开创建活动页面的用户性别
  static void reportRoomEventCreateImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 打开活动详情界面
  ///
  /// [uid] 打开活动详情界面的用户uid
  /// [gender] 打开活动详情界面的用户性别
  /// [roomEventId] 活动id
  /// [roomId] 活动房间ID
  static void reportRoomEventDetailsImp({
    String? uid,
    String? gender,
    String? roomEventId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_event_id': roomEventId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击活动详情分享按钮
  ///
  /// [uid] 点击活动详情分享按钮的用户uid
  /// [gender] 点击活动详情分享按钮的用户性别
  /// [roomEventId] 活动id
  /// [roomId] 活动房间ID
  static void reportRoomEventDetailsShareClick({
    String? uid,
    String? gender,
    String? roomEventId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_details_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_event_id': roomEventId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击订阅按钮
  ///
  /// [uid] 点击订阅按钮的用户uid
  /// [gender] 点击订阅按钮的用户性别
  /// [roomEventId] 活动id
  /// [roomId] 活动房间ID
  static void reportRoomEventDetailsSubscribe({
    String? uid,
    String? gender,
    String? roomEventId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_details_subscribe',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_event_id': roomEventId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 打开我的活动界面
  ///
  /// [uid] 打开我的活动页面的用户uid
  /// [gender] 打开我的活动页面的用户性别
  static void reportRoomEventMineImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_mine_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 打开订阅的活动界面
  ///
  /// [uid] 打开订阅的活动页面的用户uid
  /// [gender] 打开订阅的活动页面的用户性别
  static void reportRoomEventSubscribedImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_event_subscribed_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击房间推荐页面的hot，game ，chat 页签
  ///
  /// [uid] 用户uid
  /// [gender] 用户性别
  /// [content] content=hot/game/chat
  static void reportRoomLiveListClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_live_list_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 打开房间推荐页面的hot，game ，chat 页签
  ///
  /// [uid] 用户uid
  /// [gender] 用户性别
  /// [content] content=hot/game/chat
  static void reportRoomLiveListImp({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_live_list_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }
}
