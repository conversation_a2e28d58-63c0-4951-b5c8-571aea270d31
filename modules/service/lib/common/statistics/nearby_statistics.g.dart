import 'package:feature_flat_base/feature_flat_base.dart';

class NearbyStatistics {
  NearbyStatistics._();

  /// lbs新手引导展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配主界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报上一级进入的页面来源，客户端定义参数。
  static void reportLbsIntroImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_intro_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配介绍页面点击next按钮
  ///
  /// [uid]
  /// [gender]
  static void reportLbsIntroNext({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_intro_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配主界面点击左滑右滑，喜欢或者不喜欢时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [status] like=喜欢，unlike=不喜欢
  static void reportLbsMainAction({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_main_action',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配主界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 上一个进入来源，客户端定义路由。
  static void reportLbsMainImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_main_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配没有照片拦截弹窗时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsNophotoRestrictImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_nophoto_restrict_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs上传照片页展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsPhotoUploadImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_photo_upload_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配上传图片页面点击下一步时上报
  ///
  /// [uid]
  /// [gender]
  /// [num] 上传的照片数量，0/1/2
  static void reportLbsPhotoUploadNext({
    String? uid,
    String? gender,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_photo_upload_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// lbs上传照片成功时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsPhotoUploadSucc({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_photo_upload_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配profile页调整账户状态时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报点击后的效果，enable=开启，disabled=关闭
  static void reportLbsProfileEditDisabled({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_edit_disabled',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配编辑界面时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsProfileEditImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_edit_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配编辑profile成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [num] 上报编辑成功时的照片张数。
  static void reportLbsProfileEditSucc({
    String? uid,
    String? gender,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_edit_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// lbs选择profile页面时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsProfileImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// bio完善度不够拦截弹窗展示，点击里面act
  ///
  /// [uid]
  /// [gender]
  /// [act] continue=继续匹配，go=去完善
  static void reportLbsProfileRestrictClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_restrict_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// bio完善度不高拦截弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsProfileRestrictImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_profile_restrict_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配接口请求结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [duration] 请求时长
  /// [result] succ / fail
  /// [reason] 失败原因
  static void reportLbsRequestEnd({
    String? uid,
    String? gender,
    String? duration,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// lbs接口请求开始时上报
  static void reportLbsRequestStart({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_request_start',
      priority: priority,
    );
  }

  /// 设置修改完成后上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 所选择的性别，all / male/ female
  static void reportLbsSettingsChange({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_settings_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// lbs匹配设置页面展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLbsSettingsImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lbs_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 同城推荐点击用户进入他的profile内
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前的城市名
  /// [page] 上报当前页面，home=首页展示，chatlist=消息页进入的全屏页面
  static void reportNearbyClick({
    String? uid,
    String? gender,
    String? content,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nearby_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 同城推荐设置修改完成后上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前的城市名
  /// [type] 所选择的性别，all / male/ female
  /// [page] 上报当前页面，home=首页展示，chatlist=消息页进入的全屏页面
  static void reportNearbyFilterChange({
    String? uid,
    String? gender,
    String? content,
    String? type,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nearby_filter_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 同城推荐设置页展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [page] 上报当前页面，home=首页展示，chatlist=消息页进入的全屏页面
  static void reportNearbyFilterImp({
    String? uid,
    String? gender,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nearby_filter_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 同城推荐列表展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否是有用户展示状态，0=无用户，1=有用户，2=读到COU没有读到城市的空状态
  /// [content] 上报当前的城市名
  /// [page] 上报当前页面，home=首页展示，chatlist=消息页进入的全屏页面
  static void reportNearbyImp({
    String? uid,
    String? gender,
    String? status,
    String? content,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nearby_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'content': content ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }
}
