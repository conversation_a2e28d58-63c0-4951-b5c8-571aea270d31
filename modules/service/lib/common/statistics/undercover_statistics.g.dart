import 'package:feature_flat_base/feature_flat_base.dart';

class UndercoverStatistics {
  UndercoverStatistics._();

  /// 谁是卧底描述阶段展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当次参加游戏的人数
  /// [content] 上报描述的词
  /// [round] 上报当前在第几轮，数字表示
  /// [status] normal=正常描述；pk=平票描述
  static void reportUndercoverDesImp({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    String? content,
    String? round,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_des_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
        'content': content ?? '',
        'round': round ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底页面展示时上报，用于统计游戏覆盖渗透。
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报进入房间的上一级来源，客户端定义路由。
  static void reportUndercoverImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底用户被淘汰时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid] 被淘汰的用户id
  /// [toGender] 被淘汰的用户性别
  /// [round] 上报当前在第几轮，数字表示
  /// [num] 上报当次参加游戏的人数
  static void reportUndercoverOutImp({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    String? round,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_out_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'round': round ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底轮到自己描述阶段点击pass按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当次参加游戏的人数
  /// [content] 上报用户领到的词
  /// [round] 上报当前在第几轮，数字表示
  static void reportUndercoverPassClick({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    String? content,
    String? round,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_pass_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
        'content': content ?? '',
        'round': round ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底游戏结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 上报胜利方，0=卧底，1=平民
  /// [num] 上报当次参加游戏的人数
  static void reportUndercoverResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底游戏开始告知提示展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当次参加游戏的人数
  static void reportUndercoverStartImp({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_start_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底中平票时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportUndercoverTieImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_tie_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底点击投票按钮时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被投票的用户数
  /// [toGender]
  /// [roomId]
  /// [round] 上报当前在第几轮，数字表示
  static void reportUndercoverVoteClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? round,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_vote_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'round': round ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底投票阶段展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当次参加游戏的人数
  /// [round] 上报当前在第几轮，数字表示
  static void reportUndercoverVotingImp({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    String? round,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_voting_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
        'round': round ?? '',
      },
      priority: priority,
    );
  }

  /// 谁是卧底游戏过程中展示用户词语是什么的界面
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当次参加游戏的人数
  /// [content] 上报用户领到的词
  static void reportUndercoverWordsImp({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'undercover_words_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }
}
