import 'package:feature_flat_base/feature_flat_base.dart';

class GameStatistics {
  GameStatistics._();

  /// 勾选/取消自动开始时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [status] 上报操作0和1，0取消自动，1选择自动
  static void reportGameAutoStartClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_auto_start_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房间发送大表情
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [gameId] 上报游戏房间id
  /// [type] 上报游戏类型
  static void reportGameEmojiSend({
    String? uid,
    String? gender,
    String? gameId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_emoji_send',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'game_id': gameId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 确认游戏结束弹框,操作时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [status] 上报操作0和1，0取消自动，1选择自动
  static void reportGameEndClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_end_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 弹出游戏结束确定弹框时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  static void reportGameEndImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_end_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
      },
      priority: priority,
    );
  }

  /// 收到游戏结束确认弹框时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  static void reportGameEndResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_end_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房邀请到房间点击用户列表邀请时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [shareType] fb/ins/whatsapp/other
  static void reportGameInviteRoomShareClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? shareType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_room_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'share_type': shareType ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房邀请到房间点击用户列表邀请时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [toUid] 被邀请用户的ID
  /// [toGender] 被邀请用户的性别
  static void reportGameInviteRoomUserClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_room_user_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 打开游戏房间邀请界面时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  static void reportGameInviteRoomUserImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_room_user_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
      },
      priority: priority,
    );
  }

  /// 收到游戏座位邀请弹框时上报
  ///
  /// [uid] 触发方id
  /// [gender] 触发方性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [toUid] 被邀请用户的ID
  /// [toGender] 被邀请用户的性别
  static void reportGameInviteSeatInvitePopup({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_seat_invite_popup',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 收到游戏座位邀请弹框,操作时上报
  ///
  /// [uid] 触发方id
  /// [gender] 触发方性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [toUid] 被邀请用户的ID
  /// [toGender] 被邀请用户的性别
  /// [status] 上报操作0和1，0拒绝，1接受
  static void reportGameInviteSeatInvitePopupClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? toUid,
    String? toGender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_seat_invite_popup_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏房邀请到游戏位置点击用户列表邀请时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [toUid] 被邀请用户的ID
  /// [toGender] 被邀请用户的性别
  static void reportGameInviteSeatUserClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_seat_user_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 打开游戏房邀请到游戏位置界面时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  static void reportGameInviteSeatUserImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_invite_seat_user_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
      },
      priority: priority,
    );
  }

  /// 点击game主页下的蹦迪入口时上报
  ///
  /// [id] 用户id 
  /// [gender] 性别
  static void reportGameJumpyClick({
    String? id,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_jumpy_click',
      params: {
        'id': id ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 确认踢出界面弹框,操作时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [uidRole] 上报1和2，1为房主，2为队长，上报操作人的身份
  /// [toUid] 被踢出用户的ID
  /// [toGender] 被踢出用户的性别
  /// [status] 上报操作0和1，0取消，1确定
  /// [result] succ/fail
  /// [reason] 失败原因
  static void reportGameKickOutClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? uidRole,
    String? toUid,
    String? toGender,
    String? status,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_kick_out_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'uid_role': uidRole ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 弹出确认踢出界面时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [uidRole] 上报1和2，1为房主，2为队长，上报操作人的身份
  /// [toUid] 被踢出用户的ID
  /// [toGender] 被踢出用户的性别
  static void reportGameKickOutImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? uidRole,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_kick_out_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'uid_role': uidRole ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 结算界面点击分享按钮时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [gameRank] MPV/1/2/3/4,分享的海报排名
  /// [shareType] fb/ins/whatsapp/moment/friends/save
  static void reportGameOverShareClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? gameRank,
    String? shareType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_over_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'game_rank': gameRank ?? '',
        'share_type': shareType ?? '',
      },
      priority: priority,
    );
  }

  /// 结算界面，打开结算分享时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  static void reportGameOverShareImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_over_share_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
      },
      priority: priority,
    );
  }

  /// 点击确认转让弹框时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [uidRole] 上报1和2，1为房主，2为队长，上报操作人的身份
  /// [toUid] 被转让用户的ID
  /// [toGender] 被转让用户的性别
  /// [status] 上报操作0和1，0取消，1确定
  /// [result] succ/fail
  /// [reason] 失败原因
  static void reportGameTransferCaptainClick({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? uidRole,
    String? toUid,
    String? toGender,
    String? status,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_transfer_captain_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'uid_role': uidRole ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 弹出转让队长确定弹框时上报
  ///
  /// [uid] 操作用户id
  /// [gender] 操作性别
  /// [roomId] 上报房间id
  /// [gameId] 上报游戏id
  /// [gameMode] 0/1/2, 0为冒险模式，1为组合挑战，2为快速模式
  /// [uidRole] 上报1和2，1为房主，2为队长，上报操作人的身份
  /// [toUid] 被转让用户的ID
  /// [toGender] 被转让用户的性别
  static void reportGameTransferCaptainImp({
    String? uid,
    String? gender,
    String? roomId,
    String? gameId,
    String? gameMode,
    String? uidRole,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_transfer_captain_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'game_id': gameId ?? '',
        'game_mode': gameMode ?? '',
        'uid_role': uidRole ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击game主页下的真心话入口时上报
  ///
  /// [id] 用户id 
  /// [gender] 性别
  static void reportGameTruthClick({
    String? id,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_truth_click',
      params: {
        'id': id ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
