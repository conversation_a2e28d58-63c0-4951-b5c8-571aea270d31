import 'package:feature_flat_base/feature_flat_base.dart';

class MomentDetailStatistics {
  MomentDetailStatistics._();

  /// 动态详情-点击commen入口
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [place] button动态上的评论按钮/comment_bar动态详情页底栏的评论入口；点击的入口
  static void reportMomentDetailComment({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? place,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'place': place ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-评论部分-点击评论item
  ///
  /// [postId] 动态的ID
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口打开的动态详情页
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  static void reportMomentDetailCommentItem({
    String? postId,
    String? from,
    String? fromCommentId,
    String? fromCommentUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_item',
      params: {
        'post_id': postId ?? '',
        'from': from ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-评论部分-长按评论出现的更多操作弹窗-点击copy
  ///
  /// [postId] 动态的ID
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  static void reportMomentDetailCommentMoreCopy({
    String? postId,
    String? fromCommentId,
    String? fromCommentUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_more_copy',
      params: {
        'post_id': postId ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-评论部分-长按评论出现的更多操作弹窗-点击reply
  ///
  /// [postId] 动态的ID
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  static void reportMomentDetailCommentMoreReply({
    String? postId,
    String? fromCommentId,
    String? fromCommentUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_more_reply',
      params: {
        'post_id': postId ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-评论部分-长按评论出现的更多操作弹窗-点击report
  ///
  /// [postId] 动态的ID
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  static void reportMomentDetailCommentMoreReport({
    String? postId,
    String? fromCommentId,
    String? fromCommentUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_more_report',
      params: {
        'post_id': postId ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-评论部分-点击评论reply
  ///
  /// [postId] 动态的ID
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口打开的动态详情页
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  static void reportMomentDetailCommentReply({
    String? postId,
    String? from,
    String? fromCommentId,
    String? fromCommentUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_reply',
      params: {
        'post_id': postId ?? '',
        'from': from ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击发送评论
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  /// [type] 评论内容类型：text/sticker
  static void reportMomentDetailCommentSend({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? fromCommentId,
    String? fromCommentUid,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_send',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-发送评论结束
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [fromCommentId] 被评论的comment_id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id）
  /// [fromCommentUid] 被评论comment_id的用户id（区分对作品评论还是对评论进行评论，如果是作品，就是空，如果是评论，就是这条评论的id））
  /// [result] succ发送成功/fail发送失败
  /// [commentId] 发送成功的评论ID，仅发送成功上报
  /// [reason] 失败原因，仅发送失败上报
  /// [onTime] 从点击发送到有结果消耗时长，单位毫秒
  /// [type] 评论内容类型：text/sticker
  static void reportMomentDetailCommentSendEnd({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? fromCommentId,
    String? fromCommentUid,
    String? result,
    String? commentId,
    String? reason,
    String? onTime,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_comment_send_end',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'from_comment_id': fromCommentId ?? '',
        'from_comment_uid': fromCommentUid ?? '',
        'result': result ?? '',
        'comment_id': commentId ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击动态内容
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [act] image/video/quiz/op_link/text_more/text_less，用户点击的动态部分
  static void reportMomentDetailContentClick({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_content_click',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-离开
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [duration] 在对应列表的停留时长，单位s
  /// [themeId] 主题id
  static void reportMomentDetailDuration({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? duration,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_duration',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'duration': duration ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击follow
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [type] head：头像上的关注按钮，title：标题栏吸顶时的关注按钮
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailFollow({
    String? postId,
    String? autherId,
    String? autherGender,
    String? type,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_follow',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'type': type ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-展示
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  /// [themeId] 主题id
  static void reportMomentDetailImp({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_imp',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击中插引导
  ///
  /// [type] profile=个人主页引导talk=聊天引导
  /// [toUid ] 发布者的uid
  /// [toGender ] 发布者的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from]  reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailInsertClick({
    String? type,
    String? toUid ,
    String? toGender ,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_insert_click',
      params: {
        'type': type ?? '',
        'to_uid ': toUid  ?? '',
        'to_gender ': toGender  ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击like
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailLike({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_like',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击more入口
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailMore({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_more',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击主题
  ///
  /// [autherId] 动态发布用户的ID
  /// [postId] 动态id
  /// [themeId] 主题id
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailTheme({
    String? autherId,
    String? postId,
    String? themeId,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_theme',
      params: {
        'auther_id': autherId ?? '',
        'post_id': postId ?? '',
        'theme_id': themeId ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击unlike
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [from] reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/ topic_detail话题详情；从什么入口打开的动态详情页
  static void reportMomentDetailUnlike({
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_unlike',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情-点击头像处的关注按钮
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  static void reportTabMomentItemFollow({
    String? postId,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_follow',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }
}
