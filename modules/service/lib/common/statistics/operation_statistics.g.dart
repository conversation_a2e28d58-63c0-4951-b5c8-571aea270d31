import 'package:feature_flat_base/feature_flat_base.dart';

class OperationStatistics {
  OperationStatistics._();

  /// 商业化促销弹窗点击时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 展示出现的弹窗id
  /// [from] 上传客户端对应的location code
  static void reportAdPopoutClick({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ad_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 商业化促销弹窗点击关闭时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 展示出现的弹窗id
  /// [from] 上传客户端对应的location code
  static void reportAdPopoutClose({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ad_popout_close',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 商业化促销弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 展示出现的弹窗id
  /// [from] 上传客户端对应的location code
  static void reportAdPopoutImp({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ad_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// banner资源下载结束时（每个素材下载结果上报一次）
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  /// [content] 下载资源的内容ID
  static void reportBannerCreativeDownloadEnd({
    String? result,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'banner_creative_download_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 点击banner
  ///
  /// [from] from=区分banner展示来源：tab_live_roomlist=房间列表；wallet=充值落地页live_room=房间内展示；recharge_dialog=半屏充值页；store=商店；familyChat=家族群聊；family=家族banner
  /// [activityId] 活动ID
  /// [activityName] 活动名字
  static void reportBannerMomentClick({
    String? from,
    String? activityId,
    String? activityName,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'banner_moment_click',
      params: {
        'from': from ?? '',
        'activity_id': activityId ?? '',
        'activity_name': activityName ?? '',
      },
      priority: priority,
    );
  }

  /// Banner展示时（每个素材展示出来一次上报一次，一个session只上报一次）
  ///
  /// [content] banner item id
  /// [from] from=区分banner展示来源，moment=moment页；tab_live_roomlist=房间列表；wallet=充值页，live_room=房间内展示；family=家族banner，group=群组；PK=PKbanner; recharge_dialog=半屏充值页；store=商店；familyChat=家族群聊
  /// [activityName] 活动名字
  static void reportBannerMomentImp({
    String? content,
    String? from,
    String? activityName,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'banner_moment_imp',
      params: {
        'content': content ?? '',
        'from': from ?? '',
        'activity_name': activityName ?? '',
      },
      priority: priority,
    );
  }

  /// banner接口请求结束时
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  static void reportBannerRequestEnd({
    String? result,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'banner_request_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 悬浮窗下载接口请求结束
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  /// [content] 对应的内容ID
  static void reportFloatCreativeDownloadEnd({
    String? result,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'float_creative_download_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 用户点击悬浮窗
  ///
  /// [content] 点击的悬浮窗内容 item id
  /// [gender] 用户性别
  static void reportFloatImClick({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'float_im_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 悬浮窗在IM的展示
  ///
  /// [content] 展示的内容item id
  /// [gender] 用户性别
  static void reportFloatImImp({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'float_im_imp',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 悬浮窗接口请求结束时
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  static void reportFloatRequestEnd({
    String? result,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'float_request_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 点击闪屏广告
  ///
  /// [content] 上报点击的闪屏广告item id
  /// [gender] 用户性别
  static void reportSplashClick({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'splash_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 闪屏广告资源下载结束时
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  /// [content] 内容item ID
  static void reportSplashCreativeDownloadEnd({
    String? result,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'splash_creative_download_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 闪屏广告展示
  ///
  /// [content] 展示开屏广告item id
  /// [gender] 用户性别
  static void reportSplashImp({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'splash_imp',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 闪屏广告接口请求结束时
  ///
  /// [result] 成功、超时、失败，上报具体错误码
  /// [onTime] 请求结果耗时
  static void reportSplashRequestEnd({
    String? result,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'splash_request_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 闪屏用户手动点击skip
  ///
  /// [content] 展示开屏广告item id
  /// [gender] 用户性别
  static void reportSplashSkip({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'splash_skip',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
