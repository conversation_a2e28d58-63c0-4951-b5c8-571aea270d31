import 'package:feature_flat_base/feature_flat_base.dart';

class QmatchStatistics {
  QmatchStatistics._();

  /// 用户点击首页的快速匹配入口时（手动匹配）
  ///
  /// [status] 0/1；0无匹配次数，1有匹配次数
  static void reportHomeQmatchClick({
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_qmatch_click',
      params: {
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 突破限制页点击ALL
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，0使用免费次数，1花费金币匹配。
  static void reportQmatchFilterAll({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_filter_all',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 突破限制页点击选择女性匹配
  ///
  /// [uid]
  /// [gender]
  static void reportQmatchFilterFemale({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_filter_female',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 快速匹配 突破限制页曝光
  ///
  /// [uid]
  /// [gender]
  static void reportQmatchFilterImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_filter_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 突破限制页点击选择男性
  ///
  /// [uid]
  /// [gender]
  static void reportQmatchFilterMale({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_filter_male',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 突破限制达到当日限制弹窗展示
  ///
  /// [uid]
  /// [gender]
  static void reportQmatchFilterRestrictionPopout({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_filter_restriction_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 手动匹配检测到匹配次数不足出现的弹窗展示时
  static void reportQmatchNochancePopoutImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_nochance_popout_imp',
      priority: priority,
    );
  }

  /// 手动速配页面上点击取消时
  ///
  /// [duration] 本次匹配从开始到用户取消消耗时长，单位s；匹配开始为用户感知到的开始，不是接口实际开始访问
  /// [status] await_request/process_request；用户取消时速配接口访问状态，客户端等待向服务端发请求的2s内/已发请求等待响应
  static void reportQmatchPageCancel({
    String? duration,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_page_cancel',
      params: {
        'duration': duration ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 手动匹配失败时
  ///
  /// [duration] 本次匹配从开始到失败消耗时长，单位s；匹配开始为用户感知到的开始，不是接口实际开始访问
  static void reportQmatchPageFail({
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_page_fail',
      params: {
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 手动速配页面展示时
  static void reportQmatchPageImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_page_imp',
      priority: priority,
    );
  }

  /// 手动匹配页面点击重试时
  static void reportQmatchPageRetry({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_page_retry',
      priority: priority,
    );
  }

  /// 手动匹配成功时
  ///
  /// [duration] 本次匹配从开始到匹配成功消耗时长，单位s；匹配开始为用户感知到的开始，不是接口实际开始访问
  /// [toUid] 被匹配的用户的ID
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  static void reportQmatchPageSucc({
    String? duration,
    String? toUid,
    String? isOnline,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_page_succ',
      params: {
        'duration': duration ?? '',
        'to_uid': toUid ?? '',
        'is_online': isOnline ?? '',
      },
      priority: priority,
    );
  }

  /// 被成功匹配弹窗上点击chat按钮
  ///
  /// [type] auto_self/auto_user/manual_user其区分被匹配的方式，自己的客户端自动匹配/其他用户客户端自动匹配/其他用户手动匹配（若不能区分，则直接用auto/matched，自动匹配/被匹配）
  /// [from] is_guide/no_guide；是否展示新手引导
  /// [toUid] 被匹配的用户的ID
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  /// [duration] 弹窗展示到点击时停留时长，单位s
  /// [matchType] 根据后端返回的匹配类型字段展示是什么类型的匹配
  /// [page] 展示客户端的页面路由
  static void reportQmatchReceivePopoutChat({
    String? type,
    String? from,
    String? toUid,
    String? isOnline,
    String? duration,
    String? matchType,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_receive_popout_chat',
      params: {
        'type': type ?? '',
        'from': from ?? '',
        'to_uid': toUid ?? '',
        'is_online': isOnline ?? '',
        'duration': duration ?? '',
        'match_type': matchType ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 被成功匹配弹窗上点击关闭按钮
  ///
  /// [type] auto_self/auto_user/manual_user其区分被匹配的方式，自己的客户端自动匹配/其他用户客户端自动匹配/其他用户手动匹配（若不能区分，则直接用auto/matched，自动匹配/被匹配）
  /// [from] is_guide/no_guide；是否展示新手引导
  /// [toUid] 被匹配的用户的ID
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  /// [duration] 弹窗展示到点击时停留时长，单位s
  /// [matchType] 根据后端返回的匹配类型字段展示是什么类型的匹配
  /// [page] 上传客户端当前页面路由
  static void reportQmatchReceivePopoutClose({
    String? type,
    String? from,
    String? toUid,
    String? isOnline,
    String? duration,
    String? matchType,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_receive_popout_close',
      params: {
        'type': type ?? '',
        'from': from ?? '',
        'to_uid': toUid ?? '',
        'is_online': isOnline ?? '',
        'duration': duration ?? '',
        'match_type': matchType ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 被成功匹配弹窗展示时
  ///
  /// [type] auto_self/auto_user/manual_user其区分被匹配的方式，自己的客户端自动匹配/其他用户客户端自动匹配/其他用户手动匹配（若不能区分，则直接用auto/matched，自动匹配/被匹配）
  /// [from] is_guide/no_guide；是否展示新手引导
  /// [toUid] 被匹配的用户的ID
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  /// [matchType] 根据后端返回的匹配类型字段展示是什么类型的匹配
  /// [page] 当前页面的客户端路由
  static void reportQmatchReceivePopoutImp({
    String? type,
    String? from,
    String? toUid,
    String? isOnline,
    String? matchType,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_receive_popout_imp',
      params: {
        'type': type ?? '',
        'from': from ?? '',
        'to_uid': toUid ?? '',
        'is_online': isOnline ?? '',
        'match_type': matchType ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端向服务端发起速配请求结束时（自动匹配&手动匹配）
  ///
  /// [type] auto/manual；匹配方式，自动匹配/用户手动匹配
  /// [result] succ/fail
  /// [reason] 失败时上报具体失败错误码
  /// [onTime] 从开始请求匹配到结束的耗时（不包含手动匹配时前2s的等待时间），单位毫秒
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线，仅匹配成功上报
  /// [toUid] 被匹配的用户的ID，仅匹配成功上报
  /// [page] all/female/male，当前速配请求用户性别。
  static void reportQmatchRequestEnd({
    String? type,
    String? result,
    String? reason,
    String? onTime,
    String? isOnline,
    String? toUid,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_request_end',
      params: {
        'type': type ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端向服务端发起速配请求开始（自动匹配&手动匹配发出请求时）
  ///
  /// [type] auto/manual；匹配方式，自动匹配/用户手动匹配
  /// [page] all/female/male，当前速配请求用户性别。
  static void reportQmatchRequestStart({
    String? type,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_request_start',
      params: {
        'type': type ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 快速匹配中点击交友铃开关
  ///
  /// [uid]
  /// [gender]
  /// [status] 更改后的交友铃开关状态，0=关闭，1=开启
  static void reportQmatchSwitchChange({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_switch_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 快速匹配点击交友铃开关界面展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前交友铃开关状态，0=关闭，1=开启
  /// [from] 上报来源，find=快速匹配入口，setting=设置入口
  static void reportQmatchSwitchImp({
    String? uid,
    String? gender,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_switch_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
