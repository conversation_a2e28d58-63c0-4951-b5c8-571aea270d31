import 'package:feature_flat_base/feature_flat_base.dart';

class LevelStatistics {
  LevelStatistics._();

  /// 等级页展示
  ///
  /// [uid]
  /// [gender]
  /// [num] 上报当前的用户等级
  /// [type] active=活跃等级，charm=魅力等级，wealth=财富等级
  static void reportLevelImp({
    String? uid,
    String? gender,
    String? num,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'level_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户等级升级弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报弹窗的来源，homepage=一级界面，room=房间内
  /// [num] 上报当前升级到的等级数
  /// [type] active=活跃等级，charm=魅力等级，wealth=财富等级
  static void reportLevelUpgradeImp({
    String? uid,
    String? gender,
    String? from,
    String? num,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'level_upgrade_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'num': num ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
