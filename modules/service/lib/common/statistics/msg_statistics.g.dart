import 'package:feature_flat_base/feature_flat_base.dart';

class MsgStatistics {
  MsgStatistics._();

  /// 融云消息点击时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [pbType] 上报下发的pb类型
  /// [channelType] 上报下发的channal类型
  /// [type] 区分 家族群聊=familygroup，漂流瓶=bottle，用户消息=person，小助手=assistant，普通群聊=normalgroup
  static void reportRongcloudMessageClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? pbType,
    String? channelType,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rongcloud_message_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'pb_type': pbType ?? '',
        'channel_type': channelType ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
