import 'package:feature_flat_base/feature_flat_base.dart';

class AvatarStatistics {
  AvatarStatistics._();

  /// 点击编辑头像弹窗按钮
  ///
  /// [type] 上报不同的action，change=原更改头像，photo=照片转头像，history=历史头像，cancel=取消弹窗
  /// [uid]
  /// [gender]
  static void reportAvatarEditClick({
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'avatar_edit_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 编辑头像弹窗展示
  ///
  /// [uid]
  /// [gender]
  static void reportAvatarEditPopout({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'avatar_edit_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 历史头像中点击更换历史头像
  ///
  /// [uid]
  /// [gender]
  static void reportAvatarHistoryChange({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'avatar_history_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 二次确认弹窗点击确定删除历史头像
  ///
  /// [uid]
  /// [gender]
  static void reportAvatarHistoryDelete({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'avatar_history_delete',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 历史头像界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportAvatarHistoryImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'avatar_history_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像界面点击某一滤镜
  ///
  /// [content] 上报点击的滤镜名，Film/Cartoon/Painting
  /// [uid]
  /// [gender]
  static void reportPhotoToClick({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 退出照片转头像功能上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报退出的节点，select=选择照片页，filter=选择滤镜页，bg=选择背景页，share=引导分享页
  static void reportPhotoToExit({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_exit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportPhotoToImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像接口请求结束时
  ///
  /// [uid]
  /// [gender]
  /// [reason] 失败原因
  /// [result] succ/fail
  /// [onTime] 得到结果耗时：单位毫秒
  /// [size] 上传的图片的大小，单位Mb，没有则为空
  static void reportPhotoToRequestEnd({
    String? uid,
    String? gender,
    String? reason,
    String? result,
    String? onTime,
    String? size,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'reason': reason ?? '',
        'result': result ?? '',
        'on_time': onTime ?? '',
        'size': size ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像接口请求开始
  ///
  /// [uid]
  /// [gender]
  static void reportPhotoToRequestStart({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像选择背景页展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前的滤镜名
  static void reportPhotoToResultBg({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_result_bg',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像选择背景页点击保存
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前的滤镜名
  static void reportPhotoToResultBgSave({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_result_bg_save',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像结果页
  ///
  /// [uid]
  /// [gender]
  static void reportPhotoToResultFilter({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_result_filter',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像结果页点击下一步
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前所选择的滤镜名
  static void reportPhotoToResultFilterNext({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_result_filter_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像中选择某一照片提交
  ///
  /// [uid]
  /// [gender]
  static void reportPhotoToSelectPic({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_select_pic',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像引导分享页点击分享
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报分享出去的滤镜名
  static void reportPhotoToShareClick({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 照片转头像引导分享页展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报分享出去的滤镜名
  static void reportPhotoToShareImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'photo_to_share_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
