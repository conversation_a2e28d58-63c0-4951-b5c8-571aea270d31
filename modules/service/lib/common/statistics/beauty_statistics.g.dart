import 'package:feature_flat_base/feature_flat_base.dart';

class BeautyStatistics {
  BeautyStatistics._();

  /// 是否开启美颜组
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否有开启美颜，0=未开启美颜，1=已开启美颜
  /// [content] 上报当前的设备等级
  /// [type] page=页面，dialog=弹窗内修改
  static void reportBeautyStatusCheck({
    String? uid,
    String? gender,
    String? status,
    String? content,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'beauty_status_check',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'content': content ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
