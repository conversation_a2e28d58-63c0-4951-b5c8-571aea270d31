import 'package:feature_flat_base/feature_flat_base.dart';

class PremiumStatistics {
  PremiumStatistics._();

  /// 点击爵位入口时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] mine=个人profile常驻入口；gift=礼物面板入口半屏入口
  static void reportPremiumEntranceClick({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位入口展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] mine=个人profile常驻入口；gift=礼物面板入口半屏入口
  static void reportPremiumEntranceImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位功能界面点击开通
  ///
  /// [uid]
  /// [gender]
  /// [level] 上报当前该用户点击激活的等级，1-5表示
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportPremiumMainActivateClick({
    String? uid,
    String? gender,
    String? level,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_main_activate_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'level': level ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位开通成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [level] 上报当前该用户激活的等级，1-5表示
  /// [type] 上报当前开通是正常开通还是续订，正常开通=1，续订=0
  /// [num] 上报当前开通所用的钻石数
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportPremiumMainActivateSucc({
    String? uid,
    String? gender,
    String? level,
    String? type,
    String? num,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_main_activate_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'level': level ?? '',
        'type': type ?? '',
        'num': num ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位二次购买确认弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [level] 上报当前该用户点击激活的等级，1-5表示
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportPremiumMainDoublePopout({
    String? uid,
    String? gender,
    String? level,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_main_double_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'level': level ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位主界面每天领取钻石成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [level] 上报当前该用户激活的等级，1-5表示
  /// [num] 上报当前用户所领取到的钻石数
  static void reportPremiumMainGetDiamonds({
    String? uid,
    String? gender,
    String? level,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_main_get_diamonds',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'level': level ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位主界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报进入来源，mine=个人profile常驻入口；gift=礼物面板入口半屏入口；popout=促销弹窗
  /// [status] 上报当前用户的爵位等级，用0/1/2/3/4/5代表，0=没有爵位，1-5代表爵位的不同等级
  static void reportPremiumMainImp({
    String? uid,
    String? gender,
    String? from,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'premium_main_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 防跟随进房设置界面展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportPreventFollowImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'prevent_follow_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 阻止跟随进房上报时成功上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 阻挡者id
  /// [toGender] 阻挡者性别
  static void reportPreventFollowResultImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'prevent_follow_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 爵位功能防跟随进房状态更换成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 更换后的状态上报，open=开启，off=关闭
  static void reportPreventFollowSwitch({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'prevent_follow_switch',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
