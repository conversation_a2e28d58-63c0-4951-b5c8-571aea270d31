import 'package:feature_flat_base/feature_flat_base.dart';

class IcebreakerStatistics {
  IcebreakerStatistics._();

  /// 破冰问题点击更换问题
  ///
  /// [content] 上报更换掉的问题内容
  /// [gender]
  /// [toGender]
  /// [toUid]
  static void reportIcebreakerChange({
    String? content,
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'icebreaker_change',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 破冰问题点击发送问题
  ///
  /// [content] 上报发送的问题内容id
  /// [duration] 上报破冰问题从展示出到发送出的时长
  /// [gender]
  /// [toGender]
  /// [toUid]
  static void reportIcebreakerClick({
    String? content,
    String? duration,
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'icebreaker_click',
      params: {
        'content': content ?? '',
        'duration': duration ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 破冰问题在IM信息页的展示，每个问题展示上报一次
  ///
  /// [content] 上报问题内容id
  /// [gender]
  /// [toGender]
  /// [toUid]
  static void reportIcebreakerImp({
    String? content,
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'icebreaker_imp',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 破冰问题点击更换问题
  ///
  /// [content] 上报需要被更换的第一个问题内容id
  /// [page] 上报需要被更换的第二个问题内容id
  /// [gender]
  /// [toGender]
  /// [toUid]
  static void reportIcebreakerQuestionChange({
    String? content,
    String? page,
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'icebreaker_question_change',
      params: {
        'content': content ?? '',
        'page': page ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 破冰问题在IM信息页的展示
  ///
  /// [content] 上报第一个问题内容id
  /// [page] 上报第二个问题内容id
  /// [gender]
  /// [toGender]
  /// [toUid]
  static void reportIcebreakerQuestionImp({
    String? content,
    String? page,
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'icebreaker_question_imp',
      params: {
        'content': content ?? '',
        'page': page ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }
}
