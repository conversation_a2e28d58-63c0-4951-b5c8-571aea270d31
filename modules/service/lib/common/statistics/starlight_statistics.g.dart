import 'package:feature_flat_base/feature_flat_base.dart';

class StarlightStatistics {
  StarlightStatistics._();

  /// 产生星光流水时上报
  ///
  /// [coinChg] 当前订单金额
  /// [coinChgBefore] 变更后的余额
  /// [source] submit_agency 主播提交工会dissolution_agency 工会解散（将主播的星光值提交至工会）room_gifts 房间收礼获得星光值exchange_diamond-兑换钻石exit_submit_agency 用户主动退出工会(将星光值提交至工会)kick_out_submit_agency 用户被踢出工会(将星光值提交至工会)task_receive _{task_id}  任务获取chat_gifts 聊天收礼chat_receive 聊天获得stage_rewards 周奖励illegal_deduction -- 违规扣除system_provisioning-- 系统发放settlement_other 运营后台结算_other
  /// [accountType] personal  个人星光账户agency 机构星光账户
  static void reportServerStarlightFlow({
    String? coinChg,
    String? coinChgBefore,
    String? source,
    String? accountType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'server_starlight_flow',
      params: {
        'coin_chg': coinChg ?? '',
        'coin_chg_before': coinChgBefore ?? '',
        'source': source ?? '',
        'account_type': accountType ?? '',
      },
      priority: priority,
    );
  }

  /// 点击聊天获取星光值引导气泡
  static void reportTabStarlightGuideClick({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_starlight_guide_click',
      priority: priority,
    );
  }

  /// 聊天获取星光值引导气泡曝光（每次程序启动时上报一次）
  static void reportTabStarlightGuideView({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_starlight_guide_view',
      priority: priority,
    );
  }
}
