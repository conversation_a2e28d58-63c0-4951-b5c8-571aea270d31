import 'package:feature_flat_base/feature_flat_base.dart';

class ImStatistics {
  ImStatistics._();

  /// im创建房间入口点击
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportImCreateRoomEntranceClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'im_create_room_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-用户表情管理接口请求结束时
  ///
  /// [type] my_emojis 获取个人emojis信息（包括收藏、集合、推荐类目）update 集合——更新信息add_emojis 收藏表情——添加表情delete 收藏表情——删除forward 收藏表情——移动到最前
  /// [onTime] 耗时，毫秒
  /// [result] succ/fail
  /// [reason] 失败原因
  static void reportStickersActionRequestEnd({
    String? type,
    String? onTime,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_action_request_end',
      params: {
        'type': type ?? '',
        'on_time': onTime ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-用户表情管理请求
  ///
  /// [type] my_emojis 获取个人emojis信息（包括收藏、集合、推荐类目）update 集合——更新信息add_emojis 收藏表情——添加表情delete 收藏表情——删除forward 收藏表情——移动到最前
  static void reportStickersActionRequestStart({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_action_request_start',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-IM聊天添加表情包动作
  ///
  /// [uid] 添加人uid
  /// [gender] 添加人性别
  /// [content] 上报添加的表情包item id（如有）
  static void reportStickersAddImpage({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_add_impage',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击表情包预览右上角添加操作点击
  ///
  /// [uid] 添加人uid
  /// [gender] 添加人性别
  /// [content] 上报添加的表情包item id（如有）
  /// [from] 上报来源，区分来自IM信息或是精选集合管理，报impage/collection
  static void reportStickersAddSecondarypage({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_add_secondarypage',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-文字联想表情展示
  ///
  /// [content] 上报联想出来的表情包item id
  /// [uid] 发起人id
  /// [gender] 发起人性别
  static void reportStickersAssociateImp({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_associate_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击类别推荐里的不同分类，点击时上报
  ///
  /// [content] 上报点击的content id
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportStickersCategoryClassifyClick({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_category_classify_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-类目推荐界面展示
  ///
  /// [uid] 发起人id
  /// [gender] 发起人性别
  /// [content] 上报展示出来推荐类目id
  static void reportStickersCategoryClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_category_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击类别推荐入口
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportStickersCategoryEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_category_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击集合
  ///
  /// [content] 上报点击的集合id
  /// [uid] 点击人的id
  /// [gender] 点击人的性别
  static void reportStickersCollectionClick({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_collection_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击预览时右上角拉起点击下载
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  /// [content] 上报下载的表情包item id
  static void reportStickersDownloadSecondarypage({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_download_secondarypage',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-收藏界面的点击
  ///
  /// [status] 0/1，0代表空状态无表情包，1代表已收藏表情包
  /// [uid] 发起人id
  /// [gender] 发起人性别
  static void reportStickersFavouriteClick({
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_favourite_click',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-首次聊天表情包框推荐展示
  ///
  /// [uid] 触发人id
  /// [gender] 触发人性别
  static void reportStickersFirstrecommendImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_firstrecommend_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击新手引导
  ///
  /// [uid] 点击人的id
  /// [gender] 点击人性别
  static void reportStickersGuideClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_guide_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-长按拉起表情包管理面板点击删除表情包
  ///
  /// [content] 上报被删除的表情包item id（如有）
  /// [uid] 点击人的id
  /// [gender] 点击人的性别
  static void reportStickersManageDelete({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_manage_delete',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-长按拉起表情包管理面板点击移动最前
  ///
  /// [content] 上报被移动到最前的表情包item id （如有）
  /// [uid] 发起人的id
  /// [gender] 发起人的性别
  static void reportStickersManageMove({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_manage_move',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-管理我的集合删除集合（二次确认点击yes）
  ///
  /// [content] 上报删除的集合id
  /// [uid] 删除人id
  /// [gender] 删除人性别
  static void reportStickersMycollectionDelete({
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_mycollection_delete',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-集合管理中我的集合展示
  ///
  /// [uid] 展示人id
  /// [gender] 展示人性别
  static void reportStickersMycollectionImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_mycollection_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-搜索界面点击
  ///
  /// [uid] 发起人id
  /// [gender] 发起人性别
  static void reportStickersSearchClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_search_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-搜索界面点击More stickers进入表情管理界面
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportStickersSearchMore({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_search_more',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-请求搜索接口
  ///
  /// [result] succ/fail
  /// [content] 上报搜索输入的内容
  /// [status] 上报搜索出来的内容数量
  static void reportStickersSearchRequestEnd({
    String? result,
    String? content,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_search_request_end',
      params: {
        'result': result ?? '',
        'content': content ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-搜索展现的界面
  ///
  /// [content] 带搜索内容文字上报
  static void reportStickersSearchResult({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_search_result',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-精选集合点击添加下载上报结果
  ///
  /// [result] succ/fail
  /// [content] 上报已下载添加的集合id
  static void reportStickersSelectcollectionAddResult({
    String? result,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_selectcollection_add_result',
      params: {
        'result': result ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-精选集合点击下载添加集合
  ///
  /// [content] 上报下载的集合 id
  static void reportStickersSelectcollectionAddStart({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_selectcollection_add_start',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-集合管理中精选集合界面展示
  ///
  /// [uid] 当前用户id
  /// [gender] 当前用户性别
  static void reportStickersSelectcollectionImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_selectcollection_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 表情包-点击发送表情包（搜索/集合/收藏/类别）发出的表情
  ///
  /// [content] 上报发出的表情包item id （如有）
  /// [from] 报发送的来源，搜索/集合/收藏/类别/文字联想/首次聊天推荐，stickers_search_imp/ stickers_collection_click / 	stickers_favourite_imp / 	stickers_category_classify_click/ stickers_associate_imp/ stickers_firstrecommend_imp
  /// [gender ] 发送人性别
  /// [toUid] 接收人id
  /// [toGender] 接收人性别
  static void reportStickersSend({
    String? content,
    String? from,
    String? gender ,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'stickers_send',
      params: {
        'content': content ?? '',
        'from': from ?? '',
        'gender ': gender  ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }
}
