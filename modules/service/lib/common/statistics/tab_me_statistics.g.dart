import 'package:feature_flat_base/feature_flat_base.dart';

class TabMeStatistics {
  TabMeStatistics._();

  /// “我的“页面-点击about me
  static void reportMeAboutmeClick({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_aboutme_click',
      priority: priority,
    );
  }

  /// mine-about me页面展示时
  static void reportMeAboutmeImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_aboutme_imp',
      priority: priority,
    );
  }

  /// “我的“页面-点击页面顶部个人头像
  static void reportMeAvatarClick({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_avatar_click',
      priority: priority,
    );
  }

  /// 用户粉丝列表点击follow
  static void reportMeFollowerFollow({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_follower_follow',
      priority: priority,
    );
  }

  /// 用户粉丝列表展示时
  static void reportMeFollowerImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_follower_imp',
      priority: priority,
    );
  }

  /// 用户粉丝列表点击头像
  static void reportMeFollowerProfile({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_follower_profile',
      priority: priority,
    );
  }

  /// 用户关注列表展示时
  static void reportMeFollowingImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_following_imp',
      priority: priority,
    );
  }

  /// 用户关注列表上点击头像
  static void reportMeFollowingProfile({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_following_profile',
      priority: priority,
    );
  }

  /// “我的“页面-点击页面兴趣入口
  ///
  /// [from] bf_guide引导前/af_guide出现新手引导后
  static void reportMeInterestClick({
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_interest_click',
      params: {
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 用户访客列表点击follow
  static void reportMeVisitorFollow({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_visitor_follow',
      priority: priority,
    );
  }

  /// 用户访客列表展示时
  static void reportMeVisitorImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_visitor_imp',
      priority: priority,
    );
  }

  /// 用户访客列表点击头像
  static void reportMeVisitorProfile({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'me_visitor_profile',
      priority: priority,
    );
  }

  /// 我的账号上点击头像/昵称编辑入口进入Bio编辑页时
  ///
  /// [type] 0=未完成bio信息，有进度展示；1=已完成bio信息
  static void reportTabMeAvatarEdit({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_avatar_edit',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// Me tab停留时长，离开/app切到后台/切换页面时上报
  ///
  /// [duration] 页面停留时长，单位s
  static void reportTabMeDuration({
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_duration',
      params: {
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// “我的“页面-点击f follower
  ///
  /// [isNotified] dot红点提醒/none无提醒；点击时是否有红点提醒
  static void reportTabMeFollower({
    String? isNotified,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_follower',
      params: {
        'is_notified': isNotified ?? '',
      },
      priority: priority,
    );
  }

  /// “我的“页面-点击f following
  ///
  /// [isNotified] dot红点提醒/none无提醒；点击时是否有红点提醒
  static void reportTabMeFollowing({
    String? isNotified,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_following',
      params: {
        'is_notified': isNotified ?? '',
      },
      priority: priority,
    );
  }

  /// Me tab展示时，每次使用app过程中仅记录第一次展示
  static void reportTabMeImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_imp',
      priority: priority,
    );
  }

  /// 动态推荐列表-切换
  ///
  /// [content] theme/moment，用户点击要切换的列表
  static void reportTabMeListClick({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_list_click',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// “我的“页面-点击发布动态入口
  ///
  /// [refer] mine_theme_keepposting 个人tab主题引导发布mine_theme_bottom_guide 个人tab主题底部引导发布mine_theme_idle_guide 个人tab主题空态发布引导mine_moments_idle_guide 个人tab动态空态发布引导
  static void reportTabMeMomentSend({
    String? refer,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_moment_send',
      params: {
        'refer': refer ?? '',
      },
      priority: priority,
    );
  }

  /// “我的“页面-点击设置入口
  static void reportTabMeSetting({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_setting',
      priority: priority,
    );
  }

  /// “我的“页面-点击f visitor
  ///
  /// [isNotified] dot红点提醒/none无提醒；点击时是否有红点提醒
  static void reportTabMeVisitor({
    String? isNotified,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_me_visitor',
      params: {
        'is_notified': isNotified ?? '',
      },
      priority: priority,
    );
  }
}
