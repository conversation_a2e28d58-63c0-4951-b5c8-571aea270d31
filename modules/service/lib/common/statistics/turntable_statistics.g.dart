import 'package:feature_flat_base/feature_flat_base.dart';

class TurntableStatistics {
  TurntableStatistics._();

  /// 转盘关闭二次确认弹窗的点击确认关闭时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableCloseClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_close_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘的编辑入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableEditClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_edit_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘最小化按钮点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableMinimizeClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_minimize_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘结果飘屏曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableResultImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘规则弹窗曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 点击入口来源；board/turntable；编辑面板/大转盘内
  static void reportTurntableRulesImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_rules_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘侧边栏入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableSideEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_side_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击转转盘
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTurntableSpinClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_spin_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘打开点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [userNum] 指定参与者人数
  /// [optionNum] 选项个数
  static void reportTurntableTurnOnClick({
    String? uid,
    String? gender,
    String? roomId,
    String? userNum,
    String? optionNum,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_turn_on_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'user_num': userNum ?? '',
        'option_num': optionNum ?? '',
      },
      priority: priority,
    );
  }

  /// 转盘更新点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [optionNum] 选项个数
  static void reportTurntableUpdateClick({
    String? uid,
    String? gender,
    String? roomId,
    String? optionNum,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'turntable_update_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'option_num': optionNum ?? '',
      },
      priority: priority,
    );
  }
}
