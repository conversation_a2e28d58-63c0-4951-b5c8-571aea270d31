import 'package:feature_flat_base/feature_flat_base.dart';

class TitleStatistics {
  TitleStatistics._();

  /// Profile页面查看铭牌说明页展示
  ///
  /// [uid]
  /// [gender]
  static void reportTitleImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 铭牌背包内的佩戴动作
  ///
  /// [status] 上报当前点击的佩戴按钮状态，wear=尚未佩戴，点击佩戴；wearing=已佩戴，点击卸下
  /// [content] 上报当前操作的铭牌item id
  /// [uid]
  /// [gender]
  static void reportTitleWearAction({
    String? status,
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_wear_action',
      params: {
        'status': status ?? '',
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 铭牌佩戴页背包展示
  ///
  /// [uid]
  /// [gender]
  static void reportTitleWearBackpackImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_wear_backpack_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 铭牌佩戴页点击佩戴铭牌入口
  ///
  /// [uid]
  /// [gender]
  static void reportTitleWearEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_wear_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 铭牌佩戴页展示
  ///
  /// [uid]
  /// [gender]
  static void reportTitleWearImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_wear_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 铭牌佩戴页，预览页展示（从Mine页点击title进入的页面
  ///
  /// [uid]
  /// [gender]
  static void reportTitleWearPreviewImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'title_wear_preview_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
