import 'package:feature_flat_base/feature_flat_base.dart';

class H5Statistics {
  H5Statistics._();

  /// 房主激励页面曝光时上报
  ///
  /// [roomId] 房间id
  /// [isMeet] 是否满足条件 0=不满足，1=满足
  static void reportH5IncentiveOwnerPageImp({
    String? roomId,
    String? isMeet,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'h5_incentive_owner_page_imp',
      params: {
        'room_id': roomId ?? '',
        'is_meet': isMeet ?? '',
      },
      priority: priority,
    );
  }
}
