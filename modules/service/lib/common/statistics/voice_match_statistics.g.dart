import 'package:feature_flat_base/feature_flat_base.dart';

class VoiceMatchStatistics {
  VoiceMatchStatistics._();

  /// 双方都公开身份
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchBothShowIdentityImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_both_show_identity_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配中点击取消按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  static void reportVoiceMatchCancelClick({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_cancel_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击钻石余额
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchDiamondClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_diamond_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 钻石充值面板曝光
  ///
  /// [uid]
  /// [gender]
  /// [from] 来源；all/female/male/entrance；点击all/female/male/钻石余额弹出
  static void reportVoiceMatchDiamondShow({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_diamond_show',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 通话结束页面曝光
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [status] 对方是否公开身份；0/1；公开/未公开
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchDoneImp({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? status,
    String? toGender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_done_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 在通话结束页面点击对方profile
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchDoneProfileClick({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_done_profile_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击语音匹配入口
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音匹配入口曝光
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 连接失败
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [toUid] 对方uid
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchFailImp({
    String? uid,
    String? gender,
    String? type,
    String? toUid,
    String? toGender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_fail_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击免费匹配按钮（仅男用户）
  ///
  /// [uid]
  /// [gender]
  /// [status] 按钮状态；match/task；还有免费机会/去做任务
  /// [from] 点击页面；ready/done；待匹配页面/通话结束页面
  static void reportVoiceMatchFreeMatchClick({
    String? uid,
    String? gender,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_free_match_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 免费次数全部用完后提示文案曝光
  ///
  /// [uid]
  /// [gender]
  /// [from] 点击页面；ready/done；待匹配页面/通话结束页面
  static void reportVoiceMatchFreeMatchDoneImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_free_match_done_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 免费匹配按钮（仅男用户）曝光
  ///
  /// [uid]
  /// [gender]
  /// [status] 按钮状态；match/task；还有免费机会/去做任务
  static void reportVoiceMatchFreeMatchImp({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_free_match_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 点击挂断按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchHangupClick({
    String? uid,
    String? gender,
    String? type,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_hangup_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击挂断二次确认弹窗的确认按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；female/whitelist；普通女用户/白名单女用户
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchHangupConfirmClick({
    String? uid,
    String? gender,
    String? type,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_hangup_confirm_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 挂断二次确认弹窗曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；female/whitelist；普通女用户/白名单女用户
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchHangupConfirmImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_hangup_confirm_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击公开身份
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchIdentityClick({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_identity_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 通话中页面曝光
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [toGender] 对方性别
  /// [from] from=匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchIncallImp({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? toGender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_incall_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 通话中成功接通
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchIncallSuccImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_incall_succ_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击匹配按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 按钮曝光；all_free/all_paid/female/male；all免费/all付费/female/male
  /// [status] 用户身份；normal/host；普通用户/白名单女用户
  static void reportVoiceMatchMatchClick({
    String? uid,
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_match_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配按钮变为次数用尽后状态曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female；男用户/普通女用户
  /// [from] 点击页面；ready/done；待匹配页面/通话结束页面
  static void reportVoiceMatchMatchDoneImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_match_done_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配超时页面曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  static void reportVoiceMatchMatchFailImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_match_fail_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配按钮曝光（仍有匹配次数）
  ///
  /// [uid]
  /// [gender]
  /// [type] 按钮曝光；all_free/all_paid/female/male；all免费/all付费/female/male
  /// [status] 用户身份；normal/host；普通用户/白名单女用户
  static void reportVoiceMatchMatchImp({
    String? uid,
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_match_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配中页面曝光
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchMatchingImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_matching_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击麦克风按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [status] 按钮状态；on/off；点击后打开/点击后关闭
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchMicrophoneClick({
    String? uid,
    String? gender,
    String? type,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_microphone_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 接收到打电话pb
  ///
  /// [uid]
  /// [gender]
  /// [type] 匹配类型
  /// [content] pb内容
  /// [callId]
  static void reportVoiceMatchPbReceive({
    String? uid,
    String? gender,
    String? type,
    String? content,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_pb_receive',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 拒绝pb
  ///
  /// [uid]
  /// [gender]
  /// [type] 匹配类型
  /// [content] pb内容
  /// [callId]
  static void reportVoiceMatchPbReject({
    String? uid,
    String? gender,
    String? type,
    String? content,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_pb_reject',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击被动弹窗（接通/挂断）
  ///
  /// [uid]
  /// [gender]
  /// [type] 弹窗类型；0/1/2；普通被动匹配弹窗/静默匹配弹窗/陪聊任务弹窗
  /// [status] 是否接通；0/1；点击挂断/点击接通
  static void reportVoiceMatchPopoutClick({
    String? uid,
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 被动弹窗曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 弹窗类型；0/1/2；普通被动匹配弹窗/静默匹配弹窗/陪聊任务弹窗
  static void reportVoiceMatchPopoutImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击对方头像进入profile
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [toUid] 对方uid
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchProfileClick({
    String? uid,
    String? gender,
    String? type,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_profile_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 语音匹配待匹配页面曝光
  ///
  /// [uid]
  /// [gender]
  /// [status] 用户身份；normal/host；普通用户/白名单女用户
  static void reportVoiceMatchReadyImp({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_ready_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 成功充值钻石
  ///
  /// [uid]
  /// [gender]
  /// [type] 充值金额；上报充值包的code
  static void reportVoiceMatchRechargeSucc({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_recharge_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 转接到录音通话页面
  ///
  /// [uid]
  /// [gender]
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchRecordImp({
    String? uid,
    String? gender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_record_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击举报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [from] 点击页面；incall/done；通话中/通话结束
  /// [toGender] 对方性别
  static void reportVoiceMatchReportClick({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? from,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_report_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 提交举报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [from] 点击页面；incall/done；通话中/通话结束
  /// [toGender] 对方性别
  static void reportVoiceMatchReportSubmit({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? from,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_report_submit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'from': from ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击语音评价
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 对方uid
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [status] 点击按钮；0/1；差评/好评
  /// [toGender] 对方性别
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  /// [callId]
  static void reportVoiceMatchReviewClick({
    String? uid,
    String? gender,
    String? toUid,
    String? type,
    String? status,
    String? toGender,
    String? from,
    String? callId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_review_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'call_id': callId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击规则按钮
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchRuleClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_rule_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击设置
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchSettingClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_setting_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击扬声器按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  /// [status] 按钮状态；speaker/earphone；点击后变为扬声器/点击后变为听筒
  /// [from] 匹配类型；0/1/2/3；主动匹配/被动匹配/静默匹配/陪聊匹配
  static void reportVoiceMatchSpeakerClick({
    String? uid,
    String? gender,
    String? type,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_speaker_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 被动弹窗开关被关闭
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  static void reportVoiceMatchSwitchOff({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_switch_off',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 被动弹窗开关被打开
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；male/female/whitelist；男用户/普通女用户/白名单女用户
  static void reportVoiceMatchSwitchOn({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_switch_on',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 陪聊任务入口点击
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；female/whitelist；普通女用户/白名单女用户
  static void reportVoiceMatchTaskEntranceClick({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_task_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 陪聊任务入口曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 用户类型；female/whitelist；普通女用户/白名单女用户
  static void reportVoiceMatchTaskEntranceImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_task_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击用户验证按钮
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchVerifyClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_verify_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户验证面板曝光
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceMatchVerifyImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_match_verify_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
