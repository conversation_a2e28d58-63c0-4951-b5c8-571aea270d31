import 'package:feature_flat_base/feature_flat_base.dart';

class CandyStatistics {
  CandyStatistics._();

  /// candy模式选择界面展示
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [from] 上报进入房间的上一级来源，客户端定义路由，匹配入口，创建私人房入口，好友分享入口，好友邀请入口
  static void reportCandyImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// candy游戏界面加载结束时
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [type] 返回后端游戏类型房间，匹配房，私人房
  /// [result] succ/fail，成功/失败
  /// [reason] 失败原因错误码
  /// [duration] 加载时长
  static void reportCandyLoadingImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? type,
    String? result,
    String? reason,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_loading_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// candy游戏界面加载开始
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [type] 返回后端游戏类型房间，匹配房，私人房
  static void reportCandyLoadingStart({
    String? gameroomId,
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_loading_start',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配页面结束展示
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [result] succ/fail，匹配成功/失败
  /// [reason] 失败原因错误码
  /// [duration] match匹配时长，单位s
  static void reportCandyMatchImp({
    String? gameroomId,
    String? uid,
    String? gender,
    String? result,
    String? reason,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_match_imp',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// candy结算界面，点击再来一局
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [result] succ/fail，playagain成功/失败
  /// [reason] 失败原因错误码
  static void reportCandyPlayAgain({
    String? gameroomId,
    String? uid,
    String? gender,
    String? result,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_play_again',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// candy开始游戏
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [duration] start，开始时长，单位s
  static void reportCandyStart({
    String? gameroomId,
    String? uid,
    String? gender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_start',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// candy模式选择界面，点击start按钮
  ///
  /// [gameroomId] 游戏房间id
  /// [uid] 用户id
  /// [gender] 性别
  /// [content] 入场费
  static void reportCandyStartClick({
    String? gameroomId,
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'candy_start_click',
      params: {
        'gameroom_id': gameroomId ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 在大厅中点击了candy入口按钮
  ///
  /// [uid] 用户id
  /// [gender] 性别
  static void reportHomepageCandyClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'homepage_candy_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
