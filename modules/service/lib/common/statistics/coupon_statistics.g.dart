import 'package:feature_flat_base/feature_flat_base.dart';

class CouponStatistics {
  CouponStatistics._();

  /// 优惠券点击去试用按钮
  ///
  /// [uid]
  /// [gender]
  /// [type] 不同类型，all / recharge / premium / store
  /// [couponId] 上报优惠券id
  static void reportCouponGoUse({
    String? uid,
    String? gender,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'coupon_go_use',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 优惠券使用详情展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 不同类型，all / recharge / premium / store
  /// [couponId] 上报优惠券id
  static void reportCouponGuideImp({
    String? uid,
    String? gender,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'coupon_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 优惠券页面展示时上报，切换一次类型上报一次
  ///
  /// [uid]
  /// [gender]
  /// [type] 不同类型，all / recharge / premium / store
  /// [status] 0=空状态，1=存在优惠券
  static void reportCouponImp({
    String? uid,
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'coupon_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }
}
