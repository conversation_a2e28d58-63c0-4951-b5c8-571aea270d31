import 'package:feature_flat_base/feature_flat_base.dart';

class QuizStatistics {
  QuizStatistics._();

  /// 首页quiz入口点击时
  static void reportHomeQuizClick({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_quiz_click',
      priority: priority,
    );
  }

  /// quiz答题详情页上点击导航栏分享按钮时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetaiShare({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detai_share',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz答题详情页提交所有答案时（最后一题完成）
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/register注册流程；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailAnswer({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_answer',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz答题详情页上点击答案选项时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailAnswerClick({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_answer_click',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz答题详情页的停留时长，离开/切换页面/app切到后台时上报
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/register注册流程；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID 
  /// [duration] 页面停留时长，单位s
  static void reportQuizDetailDuration({
    String? from,
    String? content,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_duration',
      params: {
        'from': from ?? '',
        'content': content ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// quiz答题详情页展示时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享/register注册流程；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailImp({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_imp',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz答题详情页上点击返回上一提时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailPrevious({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_previous',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz详情请求结束时
  ///
  /// [result] succ/fail
  /// [reason] 失败code
  /// [onTime] 单位毫秒
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz测试结果页展示时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；本次从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailResultImp({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_imp',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz测试结果页上点击推荐用户头像时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；本次从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID 
  /// [toUid] 被查看的用户ID
  static void reportQuizDetailResultProfile({
    String? from,
    String? content,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_profile',
      params: {
        'from': from ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 请求测试结果结束时
  ///
  /// [content] quiz的内容ID
  /// [result] succ/fail
  /// [reason] 失败code
  /// [onTime] 单位毫秒
  static void reportQuizDetailResultRequestEnd({
    String? content,
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_request_end',
      params: {
        'content': content ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// quiz测试结果页上点击测试结果下的retry时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；本次从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  static void reportQuizDetailResultRetry({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_retry',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz测试结果页上点击分享时
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；本次从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  /// [act] navi_bar/result_card；点击的入口，导航栏分享/结果卡片下的分享
  static void reportQuizDetailResultShare({
    String? from,
    String? content,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_share',
      params: {
        'from': from ?? '',
        'content': content ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// quiz测试结果页上点击更多测试推荐列表的内容
  ///
  /// [from] quiz_list测试列表/quiz_result_reco测试结果页推荐列表/quiz_result_retry测试结果页重试/quiz_my_result我的测试结果卡入口/moment通过动态进入/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；本次从什么入口打开的quiz答题详情页
  /// [content] 当前quiz对应的内容ID
  /// [act] 点击的quiz对应的内容ID
  static void reportQuizDetailResultTest({
    String? from,
    String? content,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_detail_result_test',
      params: {
        'from': from ?? '',
        'content': content ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// quiz列表展示时
  ///
  /// [from] home首页入口/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的quiz模块
  /// [type] quiz_list测试题列表/quiz_my_result我的测试结果列表；当前所在列表
  static void reportQuizImp({
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_imp',
      params: {
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// quiz列表停留时长，离开/切换页面/app切到后台时上报
  ///
  /// [duration] 页面停留时长，单位s
  static void reportQuizListDuration({
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_list_duration',
      params: {
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// quiz列表请求结束
  ///
  /// [result] succ/fail
  /// [reason] 失败code
  /// [onTime] 请求耗时，单位浩渺
  static void reportQuizListRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_list_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// quiz列表页上点击某个测试时
  ///
  /// [from] home首页入口/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的quiz模块
  /// [act] 点击的测试对应的ID
  /// [status] 0=未测试过1=已测试过
  static void reportQuizListTest({
    String? from,
    String? act,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_list_test',
      params: {
        'from': from ?? '',
        'act': act ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 我的测试结果卡上点击retry时
  ///
  /// [from] home首页入口/quiz_my_result我的测试结果卡/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的
  /// [act] 点击的测试对应的ID
  static void reportQuizMyResultRetry({
    String? from,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_my_result_retry',
      params: {
        'from': from ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 我的测试结果卡上点击share时
  ///
  /// [from] home首页入口/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的
  /// [act] 点击的测试对应的ID
  static void reportQuizMyResultShare({
    String? from,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_my_result_share',
      params: {
        'from': from ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// quiz列表导航栏点击share
  ///
  /// [from] home首页入口/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的模块
  /// [type] quiz_list测试题列表/quiz_my_result我的测试结果列表；当前所在列表
  static void reportQuizNaviShare({
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_navi_share',
      params: {
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 性格测试入口点击
  ///
  /// [from] home首页入口/push推送/share_chat端内聊天分享/share_sns端外动态链接分享；从什么入口进入的quiz模块
  /// [status] 0=未测试过1=已测试过
  static void reportQuizPersonalityClick({
    String? from,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_personality_click',
      params: {
        'from': from ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// quiz分享弹窗展示时
  ///
  /// [page] quiz_list测试列表/quiz_my_result我的测试结果/quiz_detail测试答题详情页/quiz_detail_result测试结果页；在什么页面上出现的分享弹窗
  /// [type] quiz_list/quiz/quiz_result；分享的内容类别，测试列表/单个测试/测试结果
  /// [content] 本次分享的测试对应的ID，分享quiz_list则不上报
  static void reportQuizSharePopoutImp({
    String? page,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_share_popout_imp',
      params: {
        'page': page ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz分享弹窗上点击分享给某个app内聊天对象时
  ///
  /// [page] quiz_list测试列表/quiz_my_result我的测试结果/quiz_detail测试答题详情页/quiz_detail_result测试结果页；在什么页面上出现的分享弹窗
  /// [type] quiz_list/quiz/quiz_result；分享的内容类别，测试列表/单个测试/测试结果
  /// [content] 本次分享的测试对应的ID，分享quiz_list则不上报
  static void reportQuizSharePopoutShareFriends({
    String? page,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_share_popout_share_friends',
      params: {
        'page': page ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz分享弹窗上点击分享到app内动态时
  ///
  /// [page] quiz_list测试列表/quiz_my_result我的测试结果/quiz_detail测试答题详情页/quiz_detail_result测试结果页；在什么页面上出现的分享弹窗
  /// [type] quiz_result；分享的内容类别，这里只有测试结果可以分享到动态
  /// [content] 本次分享的测试对应的ID，分享quiz_list则不上报
  static void reportQuizSharePopoutShareMoment({
    String? page,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_share_popout_share_moment',
      params: {
        'page': page ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// quiz分享弹窗上点击分享到端外时
  ///
  /// [page] quiz_list测试列表/quiz_my_result我的测试结果/quiz_detail测试答题详情页/quiz_detail_result测试结果页；在什么页面上出现的分享弹窗
  /// [type] quiz_list/quiz/quiz_result；分享的内容类别，测试列表/单个测试/测试结果
  /// [content] 本次分享的测试对应的ID，分享quiz_list则不上报
  /// [act] fb/ins/whatsapp/more
  static void reportQuizSharePopoutShareSns({
    String? page,
    String? type,
    String? content,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'quiz_share_popout_share_sns',
      params: {
        'page': page ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }
}
