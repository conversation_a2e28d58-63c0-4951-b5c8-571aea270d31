import 'package:feature_flat_base/feature_flat_base.dart';

class MomentThemeStatistics {
  MomentThemeStatistics._();

  /// 动态-主题详情页曝光展示
  ///
  /// [from] moment_list/moment_detail/moment_preview/topic_detail，更多弹窗出现的位置，动态列表/动态详情/动态预览/用户主页/话题详情//1.01.00 将用户主页（userpage）拆分成两个来源.userpage_moment/userpage_theme
  /// [themeId] 主题id
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  static void reportMomentThemeImp({
    String? from,
    String? themeId,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_imp',
      params: {
        'from': from ?? '',
        'theme_id': themeId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-单个动态-评论
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [themeId] 主题id
  static void reportMomentThemeItemComment({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_item_comment',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-单个动态-内容
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [themeId] 主题id
  static void reportMomentThemeItemContentClick({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_item_content_click',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-单个动态-点赞
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [themeId] 主题id
  static void reportMomentThemeItemLike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_item_like',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-单个动态-更多
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [postId] 动态id
  /// [themeId] 主题id
  static void reportMomentThemeItemMore({
    String? type,
    String? autherId,
    String? autherGender,
    String? style,
    String? postId,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_item_more',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'post_id': postId ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-单个动态-取消点赞
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [themeId] 主题id
  static void reportMomentThemeItemUnlike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_item_unlike',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-更多-点击编辑主题
  ///
  /// [themeId] 主题id
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  static void reportMomentThemeMoreEdit({
    String? themeId,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_more_edit',
      params: {
        'theme_id': themeId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 动态-主题详情页-更多-点击举报主题
  ///
  /// [themeId] 主题id
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  static void reportMomentThemeMoreReport({
    String? themeId,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_theme_more_report',
      params: {
        'theme_id': themeId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }
}
