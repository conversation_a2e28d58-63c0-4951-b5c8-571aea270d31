import 'package:feature_flat_base/feature_flat_base.dart';

class AgencyStatistics {
  AgencyStatistics._();

  /// 用户在客户端申请加入机构时
  ///
  /// [agencyId] 申请加入的机构id
  /// [apkChannel] 机构专包上报机构id
  static void reportApplyJoinAgency({
    String? agencyId,
    String? apkChannel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'apply_join_agency',
      params: {
        'agency_id': agencyId ?? '',
        'apk_channel': apkChannel ?? '',
      },
      priority: priority,
    );
  }

  /// 用户退出机构时
  ///
  /// [agencyId] 机构id
  static void reportDismissAgency({
    String? agencyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'dismiss_agency',
      params: {
        'agency_id': agencyId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户正式成功加入机构时
  ///
  /// [agencyId] 加入的机构id
  static void reportJoinedAgency({
    String? agencyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'joined_agency',
      params: {
        'agency_id': agencyId ?? '',
      },
      priority: priority,
    );
  }
}
