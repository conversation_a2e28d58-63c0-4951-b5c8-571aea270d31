import 'package:feature_flat_base/feature_flat_base.dart';

class MallStatistics {
  MallStatistics._();

  /// 背包界面展示
  ///
  /// [content] 上报当前展示的界面，avatar=头像，bubble=气泡，gift=礼物
  /// [uid]
  /// [gender]
  /// [from] 上报进入的来源，mall=商城进入，bio=bio页进入
  static void reportBackpackImp({
    String? content,
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'backpack_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 背包界面预览弹窗展示
  ///
  /// [content] 上报当前预览的背包物品id
  /// [status] 上报当前佩戴状态，wear=尚未佩戴；wearing=已佩戴，礼物预览不上报。
  /// [uid]
  /// [gender]
  static void reportBackpackPreviewImp({
    String? content,
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'backpack_preview_imp',
      params: {
        'content': content ?? '',
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 背包中点击佩戴动作
  ///
  /// [status] 上报当前点击的佩戴按钮状态，wear=尚未佩戴，点击佩戴；wearing=已佩戴，点击卸下
  /// [content] 上报当前操作的背包物品id
  /// [from] 上报当前佩戴动作的来源，main=背包界面佩戴，preview=预览界面佩戴
  /// [uid]
  /// [gender]
  static void reportBackpackWearClick({
    String? status,
    String? content,
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'backpack_wear_click',
      params: {
        'status': status ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送朋友商城道具列表界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportMallGiftListImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_gift_list_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送朋友商城道具列表界面点击赠送
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [content] 商品id
  static void reportMallGiftListSend({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_gift_list_send',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 商城界面曝光展示
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前展示的商城界面，avatar=头像，bubble=气泡，effect=进场特效，theme=房间背景，ring=戒指页
  /// [from] 上报进入商城的来源，bio=bio页，backpck=背包页
  static void reportMallImp({
    String? uid,
    String? gender,
    String? content,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 商城中点击商品item
  ///
  /// [type] 上报点击的位置，preview=点击缩略图进入预览，buy=点击购买
  /// [uid]
  /// [gender]
  /// [content] 上报当前点击的商品id
  static void reportMallItemClick({
    String? type,
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_item_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 商城道具预览购买弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前的商品id
  /// [type] 上报当前展示的商品类型，gold=金币商品，diamond=钻石礼物
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportMallItemPopout({
    String? uid,
    String? gender,
    String? content,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_item_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 商城预览弹窗点击购买
  ///
  /// [content] 上报购买的商品id
  /// [uid]
  /// [gender]
  /// [status] 上报购买的结果，succ/fail
  /// [reason] 购买失败时返回错误状态码
  /// [type] 上报当前购买的商品类型，gold=金币商品，diamond=钻石礼物
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportMallItemPopoutBuy({
    String? content,
    String? uid,
    String? gender,
    String? status,
    String? reason,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_item_popout_buy',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'reason': reason ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 商城预览弹窗点击赠送
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报赠送的商品id
  /// [type] 上报当前购买的商品类型，gold=金币商品，diamond=钻石礼物
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportMallItemPopoutGift({
    String? uid,
    String? gender,
    String? content,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_item_popout_gift',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }
}
