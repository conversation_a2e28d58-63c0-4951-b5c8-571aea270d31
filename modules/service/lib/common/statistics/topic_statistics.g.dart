import 'package:feature_flat_base/feature_flat_base.dart';

class TopicStatistics {
  TopicStatistics._();

  /// 动态编辑页-点击topic入口
  ///
  /// [gender] 用户性别
  static void reportPostDetailTopic({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_detail_topic',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 动态编辑页-点击移除已添加话题
  ///
  /// [gender] 用户性别
  /// [content] 上报已选择的topic item id
  static void reportPostDetailTopicRemove({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_detail_topic_remove',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 编辑post，选择topic界面展示
  ///
  /// [gender] 用户性别
  static void reportPostDetailTopicSelectImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_detail_topic_select_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-搜索空状态展示
  ///
  /// [gender] 用户性别
  static void reportPostTopicSearchBlank({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_topic_search_blank',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-搜索空状态点击contribute
  ///
  /// [gender] 用户性别
  static void reportPostTopicSearchContribute({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_topic_search_contribute',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 编辑post，topic选择后点击Confirm
  ///
  /// [content] 上报已选择的topic 数量，0/1/2/3/4/5
  /// [gender] 用户性别
  static void reportPostTopicSelectConfirm({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_topic_select_confirm',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 编辑post，用户点击topic搜索
  ///
  /// [content] 搜索内容上报
  /// [gender] 用户性别
  static void reportPostTopicSelectSearch({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_topic_select_search',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-详情页点击关注
  ///
  /// [content] 上报关注的话题标签item id
  /// [gender] 用户性别
  /// [from] 区分进入页面的流量来源：topic_recommend=moment列表话题推荐，push=推送，splash=闪屏，float=悬浮球，banner=运营banner，other=端内其他的位置share=端外分享调起
  static void reportTopicDetailFollow({
    String? content,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_follow',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-topic 详情页展示
  ///
  /// [content] 该详情页hashtag item id
  /// [gender] 用户性别
  /// [from] 区分进入页面的流量来源：topic_recommend=moment列表话题推荐，push=推送，splash=闪屏，float=悬浮球，banner=运营banner，other=端内其他的位置share=端外分享调起share_chat=聊天分享进入reco_list=动态推荐列表follow_list=动态关注列表userpage=用户主页topic_detail=topic详情页进入
  static void reportTopicDetailImp({
    String? content,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_imp',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-点击列表评论入口
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemComment({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_comment',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-单个动态内容点击进入详情时
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemContentClick({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_content_click',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-单个动态展示时
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemImp({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_imp',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-动态点赞
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemLike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_like',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-单个动态点击更多
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemMore({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_more',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-点击动态发布者profile
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemProfile({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_profile',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情列表-取消点赞
  ///
  /// [type] 在哪个列表展示；hor=热门，new=最新
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [page] post发布时带有的topic数量，没有topic则上报0
  static void reportTopicDetailMomentItemUnlike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_moment_item_unlike',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-详情页点击发送post按钮
  ///
  /// [content] 上报当前该详情页topic item id
  /// [gender] 用户性别
  /// [from] 区分进入页面的流量来源：topic_recommend=moment列表话题推荐，push=推送，splash=闪屏，float=悬浮球，banner=运营banner，other=端内其他的位置share=端外分享调起
  static void reportTopicDetailPost({
    String? content,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_post',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-详情页分享端内用户
  ///
  /// [content] 上报分享的topic item id
  /// [gender] 用户性别
  /// [toUid] 分享到的端内用户id
  /// [toGender] 分享到的端内用户性别
  static void reportTopicDetailShareApp({
    String? content,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_share_app',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签- topic详情页点击分享按钮
  ///
  /// [content] 上报分享的topic item id
  /// [gender] 用户性别
  /// [from] 区分进入页面的流量来源：topic_recommend=moment列表话题推荐，push=推送，splash=闪屏，float=悬浮球，banner=运营banner，other=端内其他的位置share=端外分享调起
  static void reportTopicDetailShareClick({
    String? content,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_share_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-分享topic详情页到端外
  ///
  /// [content] 上报分享出去topic item id
  /// [gender] 用户性别
  /// [act] fb/ins/whatsapp/more
  static void reportTopicDetailShareSns({
    String? content,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_share_sns',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 话题详情hot/new列表切换点击时
  ///
  /// [content] 该详情页hashtag item id
  /// [gender] 用户性别
  /// [from] 区分进入页面的流量来源：topic_recommend=moment列表话题推荐，push=推送，splash=闪屏，float=悬浮球，banner=运营banner，other=端内其他的位置share=端外分享调起
  static void reportTopicDetailTabClick({
    String? content,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_detail_tab_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-点击moments 推荐的hashtag
  ///
  /// [content] 上报点击的hashtag item id
  /// [gender] 用户性别
  static void reportTopicRecommendClick({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_recommend_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-推荐标签展示（每个话题展示一次打点一次，一个session内一个话题只记录一次展示，同banner展示打点逻辑）
  ///
  /// [content ] 上报展示出来的hashtag item id
  /// [gender] 用户性别
  static void reportTopicRecommendImp({
    String? content ,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_recommend_imp',
      params: {
        'content ': content  ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-动态标签接口请求结束
  ///
  /// [result] succ/fail
  /// [onTime] 耗时
  /// [type] all 全部reco 动态顶部推荐search_name 模糊查询search_id 单个id查询follow 关注unfollow 取消关注
  static void reportTopicRequestEnd({
    String? result,
    String? onTime,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_request_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 话题标签-请求动态标签接口
  ///
  /// [type] 查询类型all 全部reco 动态顶部推荐search_name 模糊查询search_id 单个id查询follow 关注unfollow 取消关注
  static void reportTopicRequestStart({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topic_request_start',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
