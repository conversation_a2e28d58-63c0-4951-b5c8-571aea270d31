import 'package:feature_flat_base/feature_flat_base.dart';

class UserpageStatistics {
  UserpageStatistics._();

  /// 用户主页bio部分点击展开更多时
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageBioMore({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_bio_more',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页，点击chat按钮
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表/search搜索id
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageChat({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_chat',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页停留时长，离开/切换页面/app切到后台时上报
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表/search搜索id
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  /// [duration]
  static void reportUserpageDuration({
    String? from,
    String? autherId,
    String? autherGender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_duration',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页，点击follow按钮
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表/search搜索id
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageFollow({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_follow',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页展示时
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表/search搜索id
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageImp({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_imp',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-列表-切换
  ///
  /// [content] theme/moment，用户点击要切换的列表
  static void reportUserpageListClick({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_list_click',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-用户动态-点击comment
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  static void reportUserpageMomentItemComment({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_comment',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-用户动态-点击动态内容部分
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  /// [act] text/image/video/quiz/op_link/text_more/text_less，用户点击的动态部分
  static void reportUserpageMomentItemContentClick({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_content_click',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-单个动态展示时
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  static void reportUserpageMomentItemImp({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_imp',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-用户动态-点击like
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  static void reportUserpageMomentItemLike({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_like',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-用户动态-点击more
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  static void reportUserpageMomentItemMore({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_more',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-用户动态-点击unlike
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接 
  static void reportUserpageMomentItemUnlike({
    String? from,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_moment_item_unlike',
      params: {
        'from': from ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页点击更多入口时
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMore({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-更多弹窗，点击block
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMoreBlock({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more_block',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-更多弹窗，点击remark
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMoreRemark({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more_remark',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-更多弹窗，点击举报
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMoreReport({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more_report',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-更多弹窗，点击unblock
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMoreUnblock({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more_unblock',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-更多弹窗，点击unfollow
  ///
  /// [from] push推送/home_user_reco首页用户推荐/quiz_detail_result测试结果页用户推荐/moment动态列表或动态详情/chat聊天/me_follower我的关注列表/me_following我的粉丝列表/me_visitor我的访客列表
  /// [autherId] 用户主页对应用户的ID
  /// [autherGender] 用户主页对应用户的性别
  static void reportUserpageMoreUnfollow({
    String? from,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_more_unfollow',
      params: {
        'from': from ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求用户主页结束时
  ///
  /// [toUid] 主页用户的ID
  /// [result] 请求结果
  /// [reason] 失败原因，记录code
  /// [onTime] 结果消耗时长，单位毫秒
  static void reportUserpageRequestEnd({
    String? toUid,
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_request_end',
      params: {
        'to_uid': toUid ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主页-主题列表-点击动态
  ///
  /// [postId] 动态的ID
  /// [themeId] 主题id
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/op_link，动态内容类型，text为纯文字，sin_image为包含单图，multi_image为包涵多图video为包含视频，quiz为带有quiz分享链接，op_link为运营配置链接
  static void reportUserpageThemeItemClick({
    String? postId,
    String? themeId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'userpage_theme_item_click',
      params: {
        'post_id': postId ?? '',
        'theme_id': themeId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }
}
