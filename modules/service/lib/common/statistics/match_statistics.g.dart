import 'package:feature_flat_base/feature_flat_base.dart';

class MatchStatistics {
  MatchStatistics._();

  /// event匹配超时上报
  ///
  /// [uid]
  /// [gender]
  /// [eventId]
  /// [from] 进入来源，上报page根据客户端定义参数。
  static void reportEventMatchExpired({
    String? uid,
    String? gender,
    String? eventId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_match_expired',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'event_id': eventId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// event匹配弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 进入来源，上报page根据客户端定义参数。
  /// [roomId] 邀请房间的roomid
  /// [eventId] 上报当前匹配的event id
  static void reportEventMatchImp({
    String? uid,
    String? gender,
    String? from,
    String? roomId,
    String? eventId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'event_match_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'room_id': roomId ?? '',
        'event_id': eventId ?? '',
      },
      priority: priority,
    );
  }

  /// 主动发起匹配有结果时上报
  ///
  /// [result] succ/fail
  /// [reason] 失败原因////客户端没有接收到服务端返回的结果 = -1// 用户今日的匹配次数已经用完 = 1// 主动匹配在冷却时间内 = 2// 本次从池子中未匹配到合适用户 = 3
  /// [autherId] 被曝光用户的uid
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [distance] 双方的距离(km)，上报数值，保留小数点后1位// 例如 3.6 未知报0
  static void reportFatebellMatchEnd({
    String? result,
    String? reason,
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? distance,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fatebell_match_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'distance': distance ?? '',
      },
      priority: priority,
    );
  }

  /// 点击Fate Bell弹窗聊天按钮时上报
  ///
  /// [autherId] 被曝光用户的uid
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [distance] 双方的距离(km)，上报数值，保留小数点后1位// 例如 3.6
  /// [type] 触发方式//0=被动方，1=主动方
  static void reportFatebellPopClick({
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? distance,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fatebell_pop_click',
      params: {
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'distance': distance ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// Fate Bell弹窗曝光时上报
  ///
  /// [autherId] 被曝光用户的uid
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [distance] 双方的距离(km)，上报数值，保留小数点后1位// 例如 3.6，未知报0
  /// [type] 触发方式//0=被动方，1=主动方
  static void reportFatebellPopImp({
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? distance,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fatebell_pop_imp',
      params: {
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'distance': distance ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 主动打电话匹配弹窗点击去打电话
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceProactiveMatchClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_proactive_match_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 语音主动匹配弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportVoiceProactiveMatchImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_proactive_match_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
