import 'package:feature_flat_base/feature_flat_base.dart';

class AssetStatistics {
  AssetStatistics._();

  /// 分组可见弹窗拉起
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报弹窗来源：verify=声音认证（录制提交/结果页均报此）；moment=发送moment时；bio=个人bio页更改权限时。
  static void reportAssetPermissionPopout({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'asset_permission_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 分组结果提交时上报（声音认证结果提交/Moment发送时/结果页&bio更改分组时）
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报弹窗来源：verify=声音认证（录制提交/结果页均报此）；moment=发送moment时；bio=个人bio页更改权限时。
  /// [content] 上报所选的分组，public=公共可见；friends=朋友可见；myself=自己可见。
  static void reportAssetPermissionSubmit({
    String? uid,
    String? gender,
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'asset_permission_submit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// coin收入
  ///
  /// [incomeType] 获得方式//sign_in 签到task_{type}_{任务id}  完成任务奖励recharge 充值
  /// [incomeMoney] 收入金额，单位coin
  /// [fromUid] 来源用户的uid//1=来自系统发放
  static void reportCoinIncome({
    String? incomeType,
    String? incomeMoney,
    String? fromUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'coin_income',
      params: {
        'income_type': incomeType ?? '',
        'income_money': incomeMoney ?? '',
        'from_uid': fromUid ?? '',
      },
      priority: priority,
    );
  }

  /// coin消费
  ///
  /// [consumeType] 消费场景//chat_block_pop = 私聊拦截送礼
  /// [consumeMoney] 消费的金额，单位coin
  /// [giftId] 礼物id
  /// [giftNum] 礼物数量
  /// [toUid] 目标用户uid
  /// [toUserGender] 目标用户的性别
  /// [giftType] 0:普通礼物 1:随机礼物
  /// [giftTag] 文本
  static void reportCoinPayout({
    String? consumeType,
    String? consumeMoney,
    String? giftId,
    String? giftNum,
    String? toUid,
    String? toUserGender,
    String? giftType,
    String? giftTag,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'coin_payout',
      params: {
        'consume_type': consumeType ?? '',
        'consume_money': consumeMoney ?? '',
        'gift_id': giftId ?? '',
        'gift_num': giftNum ?? '',
        'to_uid': toUid ?? '',
        'to_user_gender': toUserGender ?? '',
        'gift_type': giftType ?? '',
        'gift_tag': giftTag ?? '',
      },
      priority: priority,
    );
  }

  /// 钻石折换金币弹窗点击动作
  ///
  /// [uid]
  /// [gender]
  /// [num] 上报所兑换的钻石数
  /// [type] 上报点击的按钮，cancel=取消弹窗，exchange=确定兑换
  static void reportExchangeClick({
    String? uid,
    String? gender,
    String? num,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'exchange_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 钻石折换金币弹窗展示
  ///
  /// [uid]
  /// [gender]
  static void reportExchangeImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'exchange_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 选择金币兑换档位时上报
  ///
  /// [content] 上报兑换档位，1任意兑换，500兑换，1000兑换，10000兑换，50000兑换，100000兑换
  static void reportGoldsChangeClick({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'golds_change_click',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 打开金币兑换界面时上报
  ///
  /// [from] 上报进入打开golds的入口来源，mine我的界面，live_room房间礼物面板，chat文聊礼物面板
  static void reportGoldsPageImp({
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'golds_page_imp',
      params: {
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 打开income界面时上报
  ///
  /// [userRole] 用户角色，0普通用户，1签约主播
  /// [from] 上报进入打开income的入口来源，chat聊天界面，mine我的界面
  static void reportIncomePageImp({
    String? userRole,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'income_page_imp',
      params: {
        'user_role': userRole ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 余额不足弹窗出现
  ///
  /// [from] 上报弹窗的来源，bottle=捞瓶子，gift=发送礼物，mall=商城购买，qmatch=快速匹配突破，vquiz=声音quiz突破，vmatch=声音卡片突破，game=加入游戏时，gamematch=游戏匹配时
  /// [uid]
  /// [gender]
  static void reportNomoneyPopout({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nomoney_popout',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 余额不足弹窗点击跳转钱包页
  ///
  /// [from] 上报弹窗的来源，bottle=捞瓶子，gift=发送礼物，mall=商城购买，qmatch=快速匹配突破，vquiz=声音quiz突破，vmatch=声音卡片突破，game=加入游戏时，gamematch=游戏匹配时
  /// [uid]
  /// [gender]
  static void reportNomoneyPopoutClick({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'nomoney_popout_click',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 钱包-宝石页点击页面按钮
  ///
  /// [uid]
  /// [gender]
  /// [act] transfer=转账入口点击，exchange=折换钻石入口
  static void reportWalletGemsClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_gems_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 宝石流水页展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前用户的身份，分为主播和普通用户，主播=anchor，普通用户=normal
  static void reportWalletGemsDetailsImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_gems_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 宝石折换钻石半屏页面展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前用户的身份，分为主播和普通用户，主播=anchor，普通用户=normal
  static void reportWalletGemsExchangeImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_gems_exchange_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 宝石折换钻石成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前用户的身份，分为主播和普通用户，主播=anchor，普通用户=normal
  /// [num] 上报当次折换的钻石数
  static void reportWalletGemsExchangeSucc({
    String? uid,
    String? gender,
    String? type,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_gems_exchange_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 钱包页-宝石详情展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前用户的身份，分为主播和普通用户，主播=anchor，普通用户=normal
  static void reportWalletGemsImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_gems_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// wallet页曝光时上报
  static void reportWalletPageImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_page_imp',
      priority: priority,
    );
  }
}
