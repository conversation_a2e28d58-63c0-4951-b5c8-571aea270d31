import 'package:feature_flat_base/feature_flat_base.dart';

class ConfigStatistics {
  ConfigStatistics._();

  /// 云控配置下载结束时
  ///
  /// [result] succ/ fail
  /// [content] 云控配置id
  /// [reason] 上报失败原因错误码
  /// [onTime] 请求耗时
  static void reportConfigDownloadEnd({
    String? result,
    String? content,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'config_download_end',
      params: {
        'result': result ?? '',
        'content': content ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 云控配置请求接口结束时
  ///
  /// [result] succ / fail
  /// [reason] 失败时上报错误码
  /// [onTime] 请求耗时
  static void reportConfigRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'config_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }
}
