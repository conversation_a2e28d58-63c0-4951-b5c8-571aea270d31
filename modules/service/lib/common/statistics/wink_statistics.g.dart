import 'package:feature_flat_base/feature_flat_base.dart';

class WinkStatistics {
  WinkStatistics._();

  /// wink列表曝光时上报
  ///
  /// [list] 0=Who wink at you / 1=Who i had wink
  static void reportWinkPageImp({
    String? list,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wink_page_imp',
      params: {
        'list': list ?? '',
      },
      priority: priority,
    );
  }
}
