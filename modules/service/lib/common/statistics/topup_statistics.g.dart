import 'package:feature_flat_base/feature_flat_base.dart';

class TopupStatistics {
  TopupStatistics._();

  /// h5展示查询订单结果页曝光时上报
  ///
  /// [state] 1=支付中，2=成功，3=失败
  /// [orderNo] 订单id（业务）
  static void reportH5OrderResultPageImp({
    String? state,
    String? orderNo,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'h5_order_result_page_imp',
      params: {
        'state': state ?? '',
        'order_no': orderNo ?? '',
      },
      priority: priority,
    );
  }

  /// h5收到支付订单信息跳转quarkpay时上报
  ///
  /// [orderNo] 订单id（业务）
  /// [payType] google_play/OVO/DANA/LINKAJA/SHOPEEPAY/BANK_TRANSFER
  static void reportH5PayGotoQuarkpay({
    String? orderNo,
    String? payType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'h5_pay_goto_quarkpay',
      params: {
        'order_no': orderNo ?? '',
        'pay_type': payType ?? '',
      },
      priority: priority,
    );
  }

  /// H5收银台页面曝光时上报
  ///
  /// [goodId] 充值档位30钻石=166钻石=2340钻石=3750钻石=42050钻石=56999钻石=6
  /// [gender] 性别
  /// [from] 上报充值页面的来源，wallet=钱包全屏充值页，live_room=房间
  static void reportH5PayPageImp({
    String? goodId,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'h5_pay_page_imp',
      params: {
        'good_id': goodId ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// h5发起支付时上报
  ///
  /// [goodId] 充值档位30钻石=166钻石=2340钻石=3750钻石=42050钻石=56999钻石=6
  /// [gender] 性别
  /// [price] 上报对应的美金金额： 0.49
  /// [from] 上报充值页面的来源，wallet=钱包全屏充值页，live_room=房间
  /// [payType] google_play/OVO/DANA/LINKAJA/SHOPEEPAY/BANK_TRANSFER
  static void reportH5PayRequestStart({
    String? goodId,
    String? gender,
    String? price,
    String? from,
    String? payType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'h5_pay_request_start',
      params: {
        'good_id': goodId ?? '',
        'gender': gender ?? '',
        'price': price ?? '',
        'from': from ?? '',
        'pay_type': payType ?? '',
      },
      priority: priority,
    );
  }

  /// 华为支付引导出现时上报
  ///
  /// [uid]
  /// [gender]
  static void reportHuaweipayGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'huaweipay_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求充值方式结束时
  ///
  /// [result] succ/ fail
  /// [reason] 失败原因
  /// [gotoType] google_play/quark_pay_h5
  static void reportPayTypeRequestEnd({
    String? result,
    String? reason,
    String? gotoType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pay_type_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'goto_type': gotoType ?? '',
      },
      priority: priority,
    );
  }

  /// 钱包页切换成功支付方式时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报成功切换的支付方式，gp / huawei
  static void reportPaymethodSwitchSucc({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'paymethod_switch_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 充值货币点击充值
  ///
  /// [content] 上报点击的充值档位，用美元数标识：0.99/ 8.99/ 43.99/ 99.99/ 199.99 
  /// [uid]
  /// [gender]
  /// [from] 上报来源，上报充值页面的来源，wallet=钱包全屏充值页，gift=送礼余额不足拉起的半屏弹窗，mall=商城余额不足拉起的半屏弹窗
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportTopupClick({
    String? content,
    String? uid,
    String? gender,
    String? from,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_click',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 充值界面展示
  ///
  /// [from] 上报充值页面的来源wallet=钱包全屏充值页，room_gift=送礼余额不足拉起的半屏弹窗,send_msg=聊天页面发送消息余额不足时弹出,chat_gift=聊天页面送礼余额不足时弹出,room_fruit=果盘点钻石余额时弹出,fruit_choose=选择水果余额不足时弹出
  /// [uid]
  /// [gender] 性别
  /// [result] 上报sku是否加载成功，0=加载失败，1=加载成功
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportTopupImp({
    String? from,
    String? uid,
    String? gender,
    String? result,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 充值界面上报加载结果
  ///
  /// [from] 上报充值页面的来源，wallet=钱包全屏充值页，gift=送礼余额不足拉起的半屏弹窗，mall=商城余额不足拉起的半屏弹窗，上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起
  /// [uid]
  /// [gender]
  /// [result] 上报sku是否加载成功，0=加载失败，1=加载成功
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportTopupLoadResult({
    String? from,
    String? uid,
    String? gender,
    String? result,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_load_result',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 充值接口请求结束时
  ///
  /// [result] succ/ fail / other，other=异常错误上报
  /// [reason] 失败原因
  /// [onTime] 得到结果耗时：单位毫秒
  /// [from] 上报充值页面的来源，wallet=钱包全屏充值页，gift=送礼余额不足拉起的半屏弹窗，mall=商城余额不足拉起的半屏弹窗，上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起，bet=菠菜关闭，mall=离开商城，vip=关闭vip页面，giftclose=关闭礼物面板时，rechargeclose=未充值成功时
  /// [content] 上报点击的充值档位，用美元数标识：0.99/ 8.99/ 43.99/ 99.99/ 199.99 
  /// [uid]
  /// [gender]
  /// [type] 上报充值类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，diamonds=正常渠道充值，discountgift=折扣礼物，bigbonus=大额充值弹窗
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportTopupRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    String? from,
    String? content,
    String? uid,
    String? gender,
    String? type,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'from': from ?? '',
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 开始请求充值接口时
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报充值页面的来源，wallet=钱包全屏充值页，room=送礼余额不足拉起的半屏弹窗,h5=h5收银台
  /// [content] 上报点击的充值档位，用美元数标识：0.99/ 8.99/ 43.99/ 99.99/ 199.99 
  /// [type] 上报充值类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，diamonds=正常渠道充值，discountgift=折扣礼物，bigbonus=大额充值弹窗
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  /// [goodId] 充值档位30钻石=166钻石=2340钻石=3750钻石=42050钻石=56999钻石=6
  static void reportTopupRequestStart({
    String? uid,
    String? gender,
    String? from,
    String? content,
    String? type,
    String? couponId,
    String? goodId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'topup_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'coupon_id': couponId ?? '',
        'good_id': goodId ?? '',
      },
      priority: priority,
    );
  }
}
