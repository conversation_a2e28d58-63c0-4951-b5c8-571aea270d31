import 'package:feature_flat_base/feature_flat_base.dart';

class BioStatistics {
  BioStatistics._();

  /// bio信息全部填写完成时上报
  ///
  /// [type] bio页面信息类型；country=用户国家about=个人描述sportsmusicfoodshowsbookstravel
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  static void reportBioComplete({
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_complete',
      params: {
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 某个bio信息编辑页面展示时
  ///
  /// [type] bio页面信息类型；country=用户国家about=个人描述sportsmusicfoodshowsbookstravel
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  static void reportBioDetailEditImp({
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_detail_edit_imp',
      params: {
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 某个bio信息编辑页面点击OK保存时
  ///
  /// [type] bio页面信息类型；country=用户国家about=个人描述sportsmusicfoodshowsbookstravel
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  static void reportBioDetailEditSave({
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_detail_edit_save',
      params: {
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 某个bio信息编辑页面点击skip时
  ///
  /// [type] bio页面信息类型；country=用户国家about=个人描述sportsmusicfoodshowsbookstravel
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  static void reportBioDetailEditSkip({
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_detail_edit_skip',
      params: {
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 引导填写资料页点击去完成bio
  ///
  /// [from] 上报来源，bio_guide=个人资料点击聊天引导，qmatch=快速匹配引导进入
  /// [uid]
  /// [gender]
  static void reportBioDetailGuideClick({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_detail_guide_click',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 引导填写资料引导展示
  ///
  /// [from] 上报来源，bio_guide=个人资料点击聊天引导，qmatch=快速匹配引导进入
  /// [uid]
  /// [gender]
  static void reportBioDetailGuideImp({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_detail_guide_imp',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击bio处功能入口
  ///
  /// [type] 根据后端或者云控配置的英文名上报
  /// [uid]
  /// [gender]
  static void reportBioEntranceClick({
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_entrance_click',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 引导分享身份卡片界面展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  static void reportBioIdcardImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_idcard_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 引导分享身份卡片界面点击分享
  ///
  /// [from] 进入编辑页面的场景tab_me=个人账号qmatch=速配引导场景qmatch_fail=速配失败场景bio_guide=个人资料点击聊天引导，chat_guide=聊天内引导进入
  /// [uid]
  /// [gender]
  static void reportBioIdcardShare({
    String? from,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bio_idcard_share',
      params: {
        'from': from ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
