import 'package:feature_flat_base/feature_flat_base.dart';

class HomeUserRecoStatistics {
  HomeUserRecoStatistics._();

  /// 首页推荐用户模块展示时
  ///
  /// [content] 展示的在线用户数量
  static void reportHomeUserRecoImp({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_reco_imp',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 首页推荐用户模块点击头像时
  ///
  /// [toUid] 被查看的用户ID
  static void reportHomeUserRecoProfile({
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_reco_profile',
      params: {
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 首页推荐用户模块点击refresh重试按钮
  static void reportHomeUserRecoRefresh({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_reco_refresh',
      priority: priority,
    );
  }

  /// 首页用户推荐接口请求结束
  ///
  /// [result] succ/fail
  /// [reason] 失败时上报具体失败错误码
  /// [onTime] 得到结果的耗时，单位毫秒
  /// [content] 接口返回的用户数量
  static void reportHomeUserRecoRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_reco_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 首页用户推荐接口请求开始，客户端发起请求时
  static void reportHomeUserRecoRequestStart({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_reco_request_start',
      priority: priority,
    );
  }
}
