import 'package:feature_flat_base/feature_flat_base.dart';

class VipStatistics {
  VipStatistics._();

  /// 处理好友申请界面点击动作上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [act]  accept=接受对方的申请，ignore=拒绝对方的申请，setting=跳转到Setting，friends=跳转到快速匹配界面
  static void reportAddFriendsAct({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'add_friends_act',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在Profile页点击添加好友按钮
  ///
  /// [uid]
  /// [gender]
  /// [act] add=添加好友，cancel=取消添加好友
  /// [toUid] 接收申请的用户uid
  /// [toGender] 接收申请的用户性别
  static void reportAddFriendsClick({
    String? uid,
    String? gender,
    String? act,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'add_friends_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// MSGLIST 顶部+号进去的二级页面，点击查看添加好友的入口时上报。
  ///
  /// [uid]
  /// [gender]
  static void reportAddFriendsEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'add_friends_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击MSGLIST 顶部+号进去的二级页面，如果有添加好友入口展示时上报。
  ///
  /// [uid]
  /// [gender]
  static void reportAddFriendsEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'add_friends_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 处理好友申请界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportAddFriendsImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'add_friends_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击上传真实头像入口
  ///
  /// [uid]
  /// [gender]
  /// [vipLevel] 上报当前用户的vip等级，以数字0-5共6个等级。
  static void reportUploadAvatarEntranceClick({
    String? uid,
    String? gender,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_avatar_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }

  /// 上传真实头像入口展示
  ///
  /// [uid]
  /// [gender]
  /// [vipLevel] 上报当前用户的vip等级，以数字0-5共6个等级。
  static void reportUploadAvatarEntranceImp({
    String? uid,
    String? gender,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_avatar_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }

  /// 上传真实头像成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [vipLevel] 上报当前用户的vip等级，以数字0-5共6个等级。
  static void reportUploadAvatarSucc({
    String? uid,
    String? gender,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_avatar_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }

  /// 上传个人卡片背景页入口点击
  ///
  /// [uid]
  /// [gender]
  static void reportUploadProfileEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_profile_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 上传个人卡片背景页入口展示
  ///
  /// [uid]
  /// [gender]
  static void reportUploadProfileEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_profile_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 上传个人卡片背景成功时上报
  ///
  /// [uid]
  /// [gender]
  static void reportUploadProfileSucc({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'upload_profile_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// VIP细则查看上报，在页面内每切换一次上报一次
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报对应细则的页面名称
  /// [vipLevel] 上报当前查看的页面属于哪个等级，vip 1-5共5个等级
  static void reportVipDetailsImp({
    String? uid,
    String? gender,
    String? content,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }

  /// vip权益页面展示
  ///
  /// [uid]
  /// [gender]
  /// [vipLevel] 上报当前vip等级，0-5共6个等级
  /// [from] 上报进入来源，msg=小助手消息进入，bio=mine页自己点击进入，minicard=他人mini资料卡页
  static void reportVipImp({
    String? uid,
    String? gender,
    String? vipLevel,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'vip_level': vipLevel ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// VIP设置页入口展示
  ///
  /// [uid]
  /// [gender]
  static void reportVipSettingsEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 添加好友设置页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportVipSettingsFriends({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_friends',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 添加好友设置页面开启添加好友
  ///
  /// [uid]
  /// [gender]
  /// [act] open=开启，close=关闭
  static void reportVipSettingsFriendsOpen({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_friends_open',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// VIP设置页内部展示
  ///
  /// [uid]
  /// [gender]
  static void reportVipSettingsImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 神秘访客设置页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportVipSettingsMysteriousImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_mysterious_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 神秘访客功能成功开启时上报
  ///
  /// [uid]
  /// [gender]
  /// [act] open=开启，close=关闭
  static void reportVipSettingsMysteriousOpen({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_settings_mysterious_open',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// VIP权益展示页点击对应的权益icon进入细则查看
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报点击对应细则的页面名称
  /// [vipLevel] 上报当前查看的页面属于哪个等级，vip 1-5共5个等级
  static void reportVipShowClick({
    String? uid,
    String? gender,
    String? content,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_show_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }

  /// vip升级弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [vipLevel] 上报升到的vip等级，0-5
  static void reportVipUpgradePopoutImp({
    String? uid,
    String? gender,
    String? vipLevel,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vip_upgrade_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'vip_level': vipLevel ?? '',
      },
      priority: priority,
    );
  }
}
