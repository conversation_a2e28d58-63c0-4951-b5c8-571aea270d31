import 'package:feature_flat_base/feature_flat_base.dart';

class MomentStatistics {
  MomentStatistics._();

  /// 冷启动时moment引导气泡曝光展示
  ///
  /// [uid]
  /// [gender]
  static void reportMomentBubbleGuide({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_bubble_guide',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 动态详情请求结束时
  ///
  /// [result] succ成功/fail失败
  /// [reason] 失败原因，仅发送失败上报code
  /// [onTime] 结果消耗时长，单位毫秒
  /// [postId] 动态的ID
  static void reportMomentDetailRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    String? postId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_detail_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'post_id': postId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态信箱展示时
  static void reportMomentInboxImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_inbox_imp',
      priority: priority,
    );
  }

  /// 动态信箱页面，点击信息item时
  ///
  /// [status] read/unread；点击的信息书是否已读
  /// [content] 信息ID
  static void reportMomentInboxItem({
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_inbox_item',
      params: {
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 动态相关的页面点击头像
  ///
  /// [type] reco_list推荐列表follow_list关注列表moment_detail动态详情moment_detail_comment动态详情评论moment_theme主题详情页
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  static void reportMomentItemProfile({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_item_profile',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 点击moment中的链接跳转
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [postId] 点击的post id
  /// [url] 上报点击的url链接
  static void reportMomentLinkClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? postId,
    String? url,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_link_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'post_id': postId ?? '',
        'url': url ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表请求结束时
  ///
  /// [content] reco_list/follow_list，请求的列表
  /// [result] succ成功/fail失败
  /// [reason] 失败原因，仅发送失败上报code
  /// [onTime] 结果消耗时长，单位毫秒
  static void reportMomentListRequestEnd({
    String? content,
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_list_request_end',
      params: {
        'content': content ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-离开
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [duration] 在对应列表的停留时长，单位s
  /// [scrollUp] 整个停留过程中上滑次数
  /// [scrollDown] 整个停留过程中下滑次数
  static void reportTabMomentDuration({
    String? type,
    String? duration,
    String? scrollUp,
    String? scrollDown,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_duration',
      params: {
        'type': type ?? '',
        'duration': duration ?? '',
        'scroll_up': scrollUp ?? '',
        'scroll_down': scrollDown ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-展示，每次使用app过程中仅记录第一次展示
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  static void reportTabMomentImp({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_imp',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-信箱
  ///
  /// [isNotified] notify有角标提醒/none无提醒，点击时信箱的状态
  static void reportTabMomentInboxClick({
    String? isNotified,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_inbox_click',
      params: {
        'is_notified': isNotified ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态-评论
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  static void reportTabMomentItemComment({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_comment',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态-内容
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  static void reportTabMomentItemContentClick({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_content_click',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [themeId] 主题id
  static void reportTabMomentItemImp({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_imp',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态-点赞
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  static void reportTabMomentItemLike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_like',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态-更多
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  /// [postId]
  static void reportTabMomentItemMore({
    String? type,
    String? autherId,
    String? autherGender,
    String? style,
    String? postId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_more',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
        'post_id': postId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐页点击"talk to"
  ///
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  static void reportTabMomentItemTalk({
    String? postId,
    String? autherId,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_talk',
      params: {
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }

  /// 动态列表-单个动态-点击主题
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [autherId] 动态发布用户的ID
  /// [postId] 动态id
  /// [themeId] 主题id
  static void reportTabMomentItemTheme({
    String? type,
    String? autherId,
    String? postId,
    String? themeId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_theme',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'post_id': postId ?? '',
        'theme_id': themeId ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-单个动态-取消点赞
  ///
  /// [type] reco_list推荐列表/follow_list关注列表
  /// [postId] 动态的ID
  /// [autherId] 动态发布用户的ID
  /// [autherGender] 动态发布用户的性别
  /// [style] text/sin_image/multi_image/video/quiz/voice，动态内容类型
  static void reportTabMomentItemUnlike({
    String? type,
    String? postId,
    String? autherId,
    String? autherGender,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_item_unlike',
      params: {
        'type': type ?? '',
        'post_id': postId ?? '',
        'auther_id': autherId ?? '',
        'auther_gender': autherGender ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-切换
  ///
  /// [content] reco_list/follow_list，用户点击要切换的列表
  static void reportTabMomentListClick({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_list_click',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 动态推荐列表-发布
  ///
  /// [type] reco_list推荐列表/follow_list关注列表，在哪个列表进行的点击
  static void reportTabMomentSendClick({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_moment_send_click',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
