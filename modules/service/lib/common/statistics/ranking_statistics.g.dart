import 'package:feature_flat_base/feature_flat_base.dart';

class RankingStatistics {
  RankingStatistics._();

  /// 全站榜单页面曝光时上报
  ///
  /// [uid]
  /// [gender]
  static void reportRankPageShow({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rank_page_show',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 排行榜接口请求结束时上报
  ///
  /// [type] 区分榜单类型，inroom=房间内榜单，sent=送礼榜单，received=收礼榜单，room=房间收礼榜单，cp=cp榜单
  /// [uid]
  /// [gender]
  /// [onTime] 请求时长
  /// [result] succ/fail
  /// [reason] 接口失败原因
  /// [rankingType] day/week/month
  static void reportRankingRequestEnd({
    String? type,
    String? uid,
    String? gender,
    String? onTime,
    String? result,
    String? reason,
    String? rankingType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ranking_request_end',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'on_time': onTime ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'ranking_type': rankingType ?? '',
      },
      priority: priority,
    );
  }

  /// 排行榜接口请求开始
  ///
  /// [uid]
  /// [gender]
  /// [type] 区分榜单类型，inroom=房间内榜单，sent=送礼榜单，received=收礼榜单，room=房间收礼榜单，cp=cp榜单
  /// [rankingType] day/week/month
  static void reportRankingRequestStart({
    String? uid,
    String? gender,
    String? type,
    String? rankingType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'ranking_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'ranking_type': rankingType ?? '',
      },
      priority: priority,
    );
  }
}
