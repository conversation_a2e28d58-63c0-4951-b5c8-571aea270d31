import 'package:feature_flat_base/feature_flat_base.dart';

class PullStatistics {
  PullStatistics._();

  /// pull api请求结束时
  ///
  /// [result] succ/fail
  /// [onTime] 请求耗时，毫秒
  /// [content] 请求成功时，上报pull id
  static void reportApiPullEnd({
    String? result,
    String? onTime,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'api_pull_end',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// pull api请求开始
  static void reportApiPullStart({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'api_pull_start',
      priority: priority,
    );
  }

  /// pull消息被点击时
  ///
  /// [content] pull id
  static void reportPullClick({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pull_click',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// pull推送在客户端成功发送到通知栏展示时，每一条pull发送成功上报一次
  ///
  /// [content] pull id
  static void reportPullImp({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pull_imp',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端获取pull成功后，设置本地闹钟成功，每一条pull消息设置成功上报一次
  ///
  /// [content] pull id
  static void reportPullLocalSetSucc({
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pull_local_set_succ',
      params: {
        'content': content ?? '',
      },
      priority: priority,
    );
  }
}
