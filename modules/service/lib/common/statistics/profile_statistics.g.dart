import 'package:feature_flat_base/feature_flat_base.dart';

class ProfileStatistics {
  ProfileStatistics._();

  /// 用户Profile进场特效内页展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportEntereffectWallImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'entereffect_wall_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击查看隐私相册未解锁时的提示弹窗-曝光
  ///
  /// [toUid] 被查看用户的uid
  static void reportPrivatePhotosLockPop({
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'private_photos_lock_pop',
      params: {
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 点击查看隐私相册未解锁时的提示弹窗-点击
  ///
  /// [toUid] 被查看用户的uid
  /// [type] gift / chat
  static void reportPrivatePhotosLockPopClick({
    String? toUid,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'private_photos_lock_pop_click',
      params: {
        'to_uid': toUid ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 隐私相册上传照片
  ///
  /// [num] 上传图片数量
  /// [result] 0=失败（有一张失败就报失败），1=成功（全部成功才算成功）
  /// [errorCode] 失败原因
  static void reportPrivatePhotosUpload({
    String? num,
    String? result,
    String? errorCode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'private_photos_upload',
      params: {
        'num': num ?? '',
        'result': result ?? '',
        'error_code': errorCode ?? '',
      },
      priority: priority,
    );
  }

  /// 点击查看隐私相册
  ///
  /// [toUid] 被查看用户的uid
  /// [isUnlock] 是否解锁    0=未解锁，1=已解锁
  static void reportPrivatePhotosView({
    String? toUid,
    String? isUnlock,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'private_photos_view',
      params: {
        'to_uid': toUid ?? '',
        'is_unlock': isUnlock ?? '',
      },
      priority: priority,
    );
  }

  /// 支持者榜单页面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被查看者uid
  /// [toGender] 被查看者性别
  static void reportSupporterDetailsImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'supporter_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 个人主页曝光来源
  ///
  /// [type] 来源标识//首页： homefeed 推荐： reco_listfeed 关注： follow_listfeed 详情： moment_detail粉丝列表： me_follower访客列表：me_visitor关注列表：me_following主题：theme_aggregate聊天详情： chat
  /// [autherId] 主页归属人的uid
  /// [autherAge] 主页归属人的年龄
  /// [autherGender] 主页归属人的性别
  static void reportUserProfileExport({
    String? type,
    String? autherId,
    String? autherAge,
    String? autherGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'user_profile_export',
      params: {
        'type': type ?? '',
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
      },
      priority: priority,
    );
  }
}
