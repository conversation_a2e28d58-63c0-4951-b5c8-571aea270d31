import 'package:feature_flat_base/feature_flat_base.dart';

class LuckywheelStatistics {
  LuckywheelStatistics._();

  /// 幸运转盘主界面操作点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] close=管理员/房主关闭转盘，minimize=最小化转盘，start=开启游戏，join=加入游戏
  static void reportLuckywheelClick({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘创建页面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLuckywheelCreateImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘创建成功时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [cost] 上报当次幸运转盘的入场费
  /// [status] 上报创建者是否有加入 0=无加入，1=已加入
  static void reportLuckywheelCreateSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? cost,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'cost': cost ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 点击幸运转盘入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报入口展示，function=功能栏内，screen=公屏入口，sidebar=侧边栏
  static void reportLuckywheelEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘入口展示，包括公屏右侧入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [from] 上报入口展示，function=功能栏内，screen=公屏入口，sidebar=侧边栏
  static void reportLuckywheelEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘游戏结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当局参与人数
  /// [status] 上报关闭方式，auto=自动超时关闭，manual=手动点击关闭，close=游戏结束关闭
  static void reportLuckywheelGamingEnd({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_gaming_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘被淘汰展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid] 被淘汰者id
  /// [toGender] 被淘汰者性别
  static void reportLuckywheelGamingOut({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_gaming_out',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘游戏开始时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [cost] 上报当次幸运转盘的入场费
  /// [num] 上报当次游戏参与人数
  static void reportLuckywheelGamingStart({
    String? uid,
    String? gender,
    String? roomId,
    String? cost,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_gaming_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'cost': cost ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [cost] 上报当次幸运转盘的入场费
  /// [status] 上报弹出方式，screen=手动点击公屏消息点开，sidebar=点击公屏右下方角标，auto=自动弹出，closeauto=游戏结束后自动弹出，function=点击功能入口
  static void reportLuckywheelImp({
    String? uid,
    String? gender,
    String? roomId,
    String? cost,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'cost': cost ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘用户加入成功时上报（付费成功）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [cost] 上报当次幸运转盘的入场费
  static void reportLuckywheelJoinSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? cost,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_join_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'cost': cost ?? '',
      },
      priority: priority,
    );
  }

  /// 幸运转盘结果展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 上报当前胜利者所获得的钻石数
  /// [toUid] 胜利者id
  /// [toGender] 胜利者性别
  static void reportLuckywheelResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'luckywheel_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }
}
