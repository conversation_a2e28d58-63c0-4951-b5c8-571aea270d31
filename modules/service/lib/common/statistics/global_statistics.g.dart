import 'package:feature_flat_base/feature_flat_base.dart';

class GlobalStatistics {
  GlobalStatistics._();

  /// 用户被动退出登录时
  ///
  /// [reason] 退出登录的原因上报
  static void reportLogoutAuto({
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'logout_auto',
      params: {
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主动操作退出灯登录时
  static void reportLogoutUser({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'logout_user',
      priority: priority,
    );
  }

  /// 用户退出粉丝团
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [ownerUid]
  static void reportOwnerUid({
    String? uid,
    String? gender,
    String? roomId,
    String? ownerUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'owner_uid',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'owner_uid': ownerUid ?? '',
      },
      priority: priority,
    );
  }
}
