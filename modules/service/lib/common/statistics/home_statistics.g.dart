import 'package:feature_flat_base/feature_flat_base.dart';

class HomeStatistics {
  HomeStatistics._();

  /// 首页各房间类型入口曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 入口名称；根据云控配置数据返回
  static void reportHomeEntranceImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 上报首页function功能的点击
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报具体点击的function名，返回云控所配置的英文名。
  static void reportHomeFunctionClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_function_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在首页nearby列表曝光
  ///
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [reasonState] 设置状态曝光，有状态报状态ID，活跃=1000，新注册=1001
  /// [autherId] 被曝光用户的uid
  /// [lastActiveTime] 秒的时间戳
  /// [impType] lbs/living
  static void reportHomeNearbyUserClick({
    String? autherAge,
    String? autherGender,
    String? reasonState,
    String? autherId,
    String? lastActiveTime,
    String? impType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_nearby_user_click',
      params: {
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'reason_state': reasonState ?? '',
        'auther_id': autherId ?? '',
        'last_active_time': lastActiveTime ?? '',
        'imp_type': impType ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在首页nearby列表曝光
  ///
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [reasonState] 设置状态曝光，有状态报状态ID，活跃=1000，新注册=1001
  /// [autherId] 被曝光用户的uid
  /// [lastActiveTime] 秒的时间戳
  /// [impType] lbs/living
  static void reportHomeNearbyUserImp({
    String? autherAge,
    String? autherGender,
    String? reasonState,
    String? autherId,
    String? lastActiveTime,
    String? impType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_nearby_user_imp',
      params: {
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'reason_state': reasonState ?? '',
        'auther_id': autherId ?? '',
        'last_active_time': lastActiveTime ?? '',
        'imp_type': impType ?? '',
      },
      priority: priority,
    );
  }

  /// 首页nearby列表空态按钮点击
  ///
  /// [type] lbs=定位 /  living=所在地
  static void reportHomeNearbyVacantClick({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_nearby_vacant_click',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 首页nearby列表空态曝光
  static void reportHomeNearbyVacantImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_nearby_vacant_imp',
      priority: priority,
    );
  }

  /// 状态设置
  ///
  /// [stateId] 状态id
  static void reportHomeStateSetting({
    String? stateId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_state_setting',
      params: {
        'state_id': stateId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击首页滑动到底部的按钮
  ///
  /// [type] 按钮类型discover，update
  static void reportHomeUserFootClick({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_foot_click',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 首页用户引导进房位置点击
  ///
  /// [uid]
  /// [gender]
  /// [act] profile=点击进入查看profile，room=点击进入房间
  /// [roomId] 点击进房的room id
  /// [toUid]
  /// [toGender]
  static void reportHomeUserGuideClick({
    String? uid,
    String? gender,
    String? act,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_guide_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 首页用户引导进房展示
  ///
  /// [uid]
  /// [gender]
  static void reportHomeUserGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户在主页曝光
  ///
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [reasonState] 设置状态曝光，有状态报状态ID，活跃=1000，新注册=1001
  /// [autherId] 被曝光用户的uid
  /// [lastActiveTime] 秒的时间戳
  static void reportHomeUserImp({
    String? autherAge,
    String? autherGender,
    String? reasonState,
    String? autherId,
    String? lastActiveTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_imp',
      params: {
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'reason_state': reasonState ?? '',
        'auther_id': autherId ?? '',
        'last_active_time': lastActiveTime ?? '',
      },
      priority: priority,
    );
  }

  /// 首页点击用户状态时上报
  ///
  /// [autherAge] 被曝光用户的年龄
  /// [autherGender] 被曝光用户的性别
  /// [reasonState] 设置状态曝光，有状态报状态ID，活跃=1000，新注册=1001
  /// [autherId] 被曝光用户的uid
  /// [lastActiveTime] 秒的时间戳
  /// [clickType] user / chat/join
  /// [from] for you=首页推荐，online=首页在线，following=首页关注的用户
  static void reportHomeUserStateClick({
    String? autherAge,
    String? autherGender,
    String? reasonState,
    String? autherId,
    String? lastActiveTime,
    String? clickType,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_user_state_click',
      params: {
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'reason_state': reasonState ?? '',
        'auther_id': autherId ?? '',
        'last_active_time': lastActiveTime ?? '',
        'click_type': clickType ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配入口点击
  ///
  /// [type] 被曝光的入口：fatebell=恋爱铃room_chat=语聊房room_tod=真心话大冒险change_room_tips=换房提示按钮
  /// [status] free / coin / diamond
  static void reportQmatchClick({
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_click',
      params: {
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 服务端收到主动恋爱铃匹配请求时
  ///
  /// [location] //位置信息lbs=定位living=所在地unable=无可使用的位置信息
  /// [result] succ/fail
  /// [reason] not_sufficient_funds:余额不足no_match_to_user：本次未匹配到用户
  /// [passiveLocation] //被匹配方位置信息lbs=定位living=所在地unable=无可使用的位置信息
  /// [autherId] 被匹配方的uid
  /// [autherAge] 被匹配的用户的年龄
  /// [autherGender] 被匹配用户的性别
  /// [status] free / coin / diamond
  static void reportQmatchFatebellRequest({
    String? location,
    String? result,
    String? reason,
    String? passiveLocation,
    String? autherId,
    String? autherAge,
    String? autherGender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_fatebell_request',
      params: {
        'location': location ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'passive_location': passiveLocation ?? '',
        'auther_id': autherId ?? '',
        'auther_age': autherAge ?? '',
        'auther_gender': autherGender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配入口曝光
  ///
  /// [type] 被曝光的入口：fatebell=恋爱铃room_chat=语聊房room_tod=真心话大冒险change_room_tips=换房提示按钮
  /// [status] free / coin / diamond
  static void reportQmatchImp({
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_imp',
      params: {
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配有结果时上报
  ///
  /// [type] 被曝光的入口：fatebell=恋爱铃room_chat=语聊房room_tod=真心话大冒险change_room_tips=换房提示按钮
  /// [status] free / coin / diamond
  /// [result] succ/fail
  /// [reason] 失败原因timeout 匹配超时cancel 手动取消not_sufficient_funds:余额不足enter_myroom：进入自己的房间error code
  /// [roomId] 匹配的房间id
  static void reportQmatchRequest({
    String? type,
    String? status,
    String? result,
    String? reason,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'qmatch_request',
      params: {
        'type': type ?? '',
        'status': status ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// Home tab停留时长，离开/app切到后台/切换页面时上报
  ///
  /// [duration] 页面停留时长，单位s
  /// [rotate] 首页在线用户球体转动次数
  static void reportTabHomeDuration({
    String? duration,
    String? rotate,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_home_duration',
      params: {
        'duration': duration ?? '',
        'rotate': rotate ?? '',
      },
      priority: priority,
    );
  }

  /// Home tab展示时，每次使用app过程中仅记录第一次展示
  static void reportTabHomeImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'tab_home_imp',
      priority: priority,
    );
  }
}
