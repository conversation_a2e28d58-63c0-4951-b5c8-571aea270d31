import 'package:feature_flat_base/feature_flat_base.dart';

class HomeRoomRecoStatistics {
  HomeRoomRecoStatistics._();

  /// 首页推荐房间模块点击头像/昵称
  ///
  /// [from] 上报来源，首页推荐进入
  /// [content] 上报当前点击进入的语音房标签
  /// [toUid] 被点击的用户ID
  static void reportHomeRoomRecoProfile({
    String? from,
    String? content,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_room_reco_profile',
      params: {
        'from': from ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }
}
