import 'package:feature_flat_base/feature_flat_base.dart';

class PushStatistics {
  PushStatistics._();

  /// 运营push被点击时-运营push统一打点
  ///
  /// [serType] 智能/人工
  /// [messageId] 消息ID
  /// [logExt] 透传字段
  /// [notifyBegin] 展示开始时间
  /// [notifyEnd] 展示结束时间
  /// [sendType] 推送方式：push/pull
  /// [title] 消息标题
  /// [itemId] 内容id
  /// [deeplink] 拉起的url
  static void reportFleetsPushClick({
    String? serType,
    String? messageId,
    String? logExt,
    String? notifyBegin,
    String? notifyEnd,
    String? sendType,
    String? title,
    String? itemId,
    String? deeplink,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fleets_push_click',
      params: {
        'ser_type': serType ?? '',
        'message_id': messageId ?? '',
        'log_ext': logExt ?? '',
        'notify_begin': notifyBegin ?? '',
        'notify_end': notifyEnd ?? '',
        'send_type': sendType ?? '',
        'title': title ?? '',
        'item_id': itemId ?? '',
        'deeplink': deeplink ?? '',
      },
      priority: priority,
    );
  }

  /// 运营推送到达时-运营推送统一打点
  ///
  /// [serType] 智能/人工
  /// [messageId] 消息ID
  /// [logExt] 透传字段
  /// [notifyBegin] 展示开始时间
  /// [notifyEnd] 展示结束时间
  /// [sendType] 推送方式：push/pull
  /// [title] 消息标题
  /// [itemId] 内容id
  /// [deeplink] 拉起的url
  static void reportFleetsPushReach({
    String? serType,
    String? messageId,
    String? logExt,
    String? notifyBegin,
    String? notifyEnd,
    String? sendType,
    String? title,
    String? itemId,
    String? deeplink,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fleets_push_reach',
      params: {
        'ser_type': serType ?? '',
        'message_id': messageId ?? '',
        'log_ext': logExt ?? '',
        'notify_begin': notifyBegin ?? '',
        'notify_end': notifyEnd ?? '',
        'send_type': sendType ?? '',
        'title': title ?? '',
        'item_id': itemId ?? '',
        'deeplink': deeplink ?? '',
      },
      priority: priority,
    );
  }

  /// 运营push展示时-运营push统一打点
  ///
  /// [serType] 智能/人工
  /// [messageId] 消息ID
  /// [logExt] 透传字段
  /// [notifyBegin] 展示开始时间
  /// [notifyEnd] 展示结束时间
  /// [sendType] 推送方式：push/pull
  /// [title] 消息标题
  /// [itemId] 内容id
  /// [deeplink] 拉起的url
  static void reportFleetsPushShow({
    String? serType,
    String? messageId,
    String? logExt,
    String? notifyBegin,
    String? notifyEnd,
    String? sendType,
    String? title,
    String? itemId,
    String? deeplink,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'fleets_push_show',
      params: {
        'ser_type': serType ?? '',
        'message_id': messageId ?? '',
        'log_ext': logExt ?? '',
        'notify_begin': notifyBegin ?? '',
        'notify_end': notifyEnd ?? '',
        'send_type': sendType ?? '',
        'title': title ?? '',
        'item_id': itemId ?? '',
        'deeplink': deeplink ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端点击推送时
  ///
  /// [type] msg聊天消息/call音视频通话/follow关注消息/moment社区互动消息/match匹配消息/visitor访客消息/mate_online互关用户上线消息/op_activity运营消息/fate_bell恋爱铃
  /// [pushId] 该推送的ID，仅运营推送上报
  /// [jumpTo] userpage/quiz/moment/link；运营push跳转类型，仅运营推送上报
  /// [content] 运营push跳转页面对应的ID/地址，仅运营推送上报
  /// [pbType] 上报后端所返回的pb_type字段
  static void reportPushClick({
    String? type,
    String? pushId,
    String? jumpTo,
    String? content,
    String? pbType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'push_click',
      params: {
        'type': type ?? '',
        'push_id': pushId ?? '',
        'jump_to': jumpTo ?? '',
        'content': content ?? '',
        'pb_type': pbType ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端接收到推送时上报（安卓）
  ///
  /// [type] msg聊天消息/call音视频通话/follow关注消息/moment社区互动消息/match匹配消息/visitor访客消息/mate_online互关用户上线消息/op_activity运营消息
  /// [pushId] 该推送的ID，仅运营推送上报
  /// [jumpTo] userpage/quiz/moment/link；运营push跳转类型，仅运营推送上报
  /// [content] 运营push跳转页面对应的ID/地址，仅运营推送上报
  static void reportPushImp({
    String? type,
    String? pushId,
    String? jumpTo,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'push_imp',
      params: {
        'type': type ?? '',
        'push_id': pushId ?? '',
        'jump_to': jumpTo ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 客户端注册厂商推送通道
  ///
  /// [regPushType] 厂商通道类型 华为:HW / 小米:MI / oppo:OPPO
  /// [regResult] 注册结果 成功 success /失败 结果返回码
  static void reportPushRegToken({
    String? regPushType,
    String? regResult,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'push_reg_token',
      params: {
        'reg_push_type': regPushType ?? '',
        'reg_result': regResult ?? '',
      },
      priority: priority,
    );
  }

  /// 服务端向用户推送消息时上报
  ///
  /// [pushType] 推送类型
  /// [pushId] 推送内容id
  static void reportPushSend({
    String? pushType,
    String? pushId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'push_send',
      params: {
        'push_type': pushType ?? '',
        'push_id': pushId ?? '',
      },
      priority: priority,
    );
  }
}
