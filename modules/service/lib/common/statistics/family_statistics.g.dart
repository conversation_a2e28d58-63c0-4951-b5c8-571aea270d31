import 'package:feature_flat_base/feature_flat_base.dart';

class FamilyStatistics {
  FamilyStatistics._();

  /// 家族审核人员操作申请者信息时
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 申请人的id
  /// [toGender] 申请人的性别
  /// [act] reject=拒绝加入，accept=同意加入
  /// [familyId] 当前操作的家族id
  static void reportFamilyAduitClick({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? act,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_aduit_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'act': act ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 审核家族申请页面展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportFamilyAduitImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_aduit_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 申请加入家族不满足条件失败时上报
  ///
  /// [uid]
  /// [gender]
  /// [reason] 上报失败的文本信息
  /// [familyId] 加入失败的家族id
  static void reportFamilyApplyjoinFail({
    String? uid,
    String? gender,
    String? reason,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_applyjoin_fail',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'reason': reason ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 申请加入家族成功弹窗时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 上报加入的家族id
  /// [from] 上报加入的上一级页面，search=搜索页面，square=广场页，details=家族详情页
  static void reportFamilyApplyjoinSucc({
    String? uid,
    String? gender,
    String? familyId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_applyjoin_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 打开家族助手消息展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报消息类型数字，9：收到邀请，8: 拒绝邀请, 1: 加入家族申请, 4: 创建家族审核通过，创建家族成功, 10: 通过家族审核加入家族, 3: 拒绝家族申请, 7: 逐出家族, 13: 任命职务-副族长, 14: 任命职务-长老, 12: 家族升级, 11: 家族解散,  5: 创建家族被拒绝
  static void reportFamilyAssistantImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_assistant_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 家族详情点击修改家族信息时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilyDetailsEdit({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_edit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族详情页展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 上报家族id
  /// [from] 开发自定路由，square=广场页，moment/im按照旧参数。
  static void reportFamilyDetailsImp({
    String? uid,
    String? gender,
    String? familyId,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 家族详情页点击邀请其他人加入家族入口
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilyDetailsInviteClick({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_invite_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族邀请他人加入家族发出邀请成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [familyId]
  static void reportFamilyDetailsInviteSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_invite_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族成员列表展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilyDetailsMemberlistImp({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_memberlist_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族详情页点击进入家族房间
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  /// [roomId]
  static void reportFamilyDetailsRoomEnter({
    String? uid,
    String? gender,
    String? familyId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_room_enter',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族详情页点击分享家族入口时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilyDetailsShareClick({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 分享家族详情成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 分享到的用户id，如果是moment则上报为moment
  /// [toGender]
  /// [familyId]
  static void reportFamilyDetailsShareSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_details_share_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 用户点击房间列表页家族入口进入家族广场
  ///
  /// [uid]
  /// [gender]
  static void reportFamilyEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户加入家族成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 上报用户成功加入的家族id
  /// [from] 通过的节点，audit=审核成功加入，invite=邀请确认成功加入，free=自动通过
  /// [fuid] 加入者id
  /// [fgender] 加入者的性别
  static void reportFamilyJoinSucc({
    String? uid,
    String? gender,
    String? familyId,
    String? from,
    String? fuid,
    String? fgender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_join_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'from': from ?? '',
        'fuid': fuid ?? '',
        'fgender': fgender ?? '',
      },
      priority: priority,
    );
  }

  /// 用户主动退出家族成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilySettingsExitSucc({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_settings_exit_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族设置页展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilySettingsImp({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族设置管理人员页面上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  static void reportFamilySettingsManageImp({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_settings_manage_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族设置页管理人员设置角色成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [act] setadmin=设置成副家族长，setmaster=设置成长老。
  /// [status] 上报设置动作，0=取消身份，1=设为身份
  /// [familyId]
  static void reportFamilySettingsManageSet({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? act,
    String? status,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_settings_manage_set',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'act': act ?? '',
        'status': status ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族管理中剔除某一用户成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [familyId]
  /// [toUid]
  /// [toGender]
  static void reportFamilySettingsRemoveSucc({
    String? uid,
    String? gender,
    String? familyId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_settings_remove_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 家族广场点击某一家族查看详情
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 上报点击的家族id
  /// [content] 上报点击的列表，rec=推荐列表，all=全部列表，new=新家族
  static void reportFamilySquareClick({
    String? uid,
    String? gender,
    String? familyId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 家族广场点击创建家族入口
  ///
  /// [uid]
  /// [gender]
  static void reportFamilySquareCreateClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_create_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建家族前置提示页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportFamilySquareCreateFirst({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_create_first',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建家族填写详细信息页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportFamilySquareCreateImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 家族创建递交申请成功时上报
  ///
  /// [uid]
  /// [gender]
  static void reportFamilySquareCreateSucc({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 家族广场页点击进入自己的家族详情
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 家族id
  static void reportFamilySquareEntermy({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_entermy',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }

  /// 家族广场展示时上报，每切换一次page上报一次pv
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报来源，= mine页的常驻入口进入，roomlist=房间列表页进入，assistant=小助手
  /// [page] 上报当前家族广场在什么页面，room=家族房间页，list=家族列表页
  static void reportFamilySquareImp({
    String? uid,
    String? gender,
    String? from,
    String? page,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'page': page ?? '',
      },
      priority: priority,
    );
  }

  /// 家族广场点击加入家族
  ///
  /// [uid]
  /// [gender]
  /// [familyId] 上报选择加入的家族
  static void reportFamilySquareJoin({
    String? uid,
    String? gender,
    String? familyId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'family_square_join',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'family_id': familyId ?? '',
      },
      priority: priority,
    );
  }
}
