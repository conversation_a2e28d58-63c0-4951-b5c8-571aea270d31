import 'package:feature_flat_base/feature_flat_base.dart';

class InviteGameStatistics {
  InviteGameStatistics._();

  /// 游戏邀请弹框点击
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [act] 区分是接受还是关闭，accept接受，close关闭
  /// [type] 游戏类型，ludo，domino，candy，baloot，bombcat
  /// [unknown] [object Object]
  static void reportInviteGamePopupClick({
    String? uid,
    String? gender,
    String? act,
    String? type,
    String? unknown,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'invite_game_popup_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'type': type ?? '',
        'unknown': unknown ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏邀请弹框展示
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [duration] 显示时长
  /// [type] 游戏类型，ludo，domino，candy，baloot，bombcat
  static void reportInviteGamePopupImp({
    String? uid,
    String? gender,
    String? duration,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'invite_game_popup_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 弃用：统一使用room_entered打点，from为invite_game_popup_result
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [type] 0/1 1新用户，0老用户区分
  /// [gameroomid] 进入的游戏房间id
  /// [result] succ/fail，跳转到游戏房成功/失败
  /// [reason] 失败原因
  /// [duration] 得到结果耗时：单位毫秒
  static void reportInviteGamePopupResult({
    String? uid,
    String? gender,
    String? type,
    String? gameroomid,
    String? result,
    String? reason,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'invite_game_popup_result',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'gameroomid': gameroomid ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }
}
