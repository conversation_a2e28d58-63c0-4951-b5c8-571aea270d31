import 'package:feature_flat_base/feature_flat_base.dart';

class VoiceAdjustStatistics {
  VoiceAdjustStatistics._();

  /// 音控台耳返打开时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVoiceAdjustFeedback({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_adjust_feedback',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 音控台曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportVoiceAdjustImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_adjust_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 音控台点击混响效果时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 混响类型；什么类型上报什么名称
  static void reportVoiceAdjustReverbClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_adjust_reverb_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 音控台音量变化时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 音量类型；music/voice；音乐音量/耳返音量
  static void reportVoiceAdjustVolumeChange({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'voice_adjust_volume_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
