import 'package:feature_flat_base/feature_flat_base.dart';

class TreasureboxStatistics {
  TreasureboxStatistics._();

  /// 房间内，宝箱任务入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [result] 上报当前宝箱的等级，1/2/3/4
  static void reportTreasureBoxEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'treasure_box_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 房间内，宝箱任务入口展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [result] 上报当前宝箱的等级，1/2/3/4
  static void reportTreasureBoxEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'treasure_box_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 房间内，宝箱任务半屏弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [num] 上报当前宝箱的等级，用1/2/3/4表示
  static void reportTreasureBoxImp({
    String? uid,
    String? gender,
    String? roomId,
    String? num,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'treasure_box_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'num': num ?? '',
      },
      priority: priority,
    );
  }

  /// 房间任务，宝箱结果弹窗弹窗出展示时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [content] 上报后端返回的奖励，gift/diamonds/coins
  /// [stauts] 当前是否有礼物状态，0/1表示，0=无礼物，1=有礼物
  /// [result] 上报当前宝箱的等级，1/2/3/4
  static void reportTreasureBoxResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? content,
    String? stauts,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'treasure_box_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'stauts': stauts ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 房间任务，宝箱倒计时展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前房间类型
  /// [num] 上报当前展示时从第几秒开始倒计时
  /// [result] 上报当前宝箱的等级，1/2/3/4
  static void reportTreasureBoxResultTimeout({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? num,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'treasure_box_result_timeout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'num': num ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }
}
