import 'package:feature_flat_base/feature_flat_base.dart';

class PkStatistics {
  PkStatistics._();

  /// 点击function内pk入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间类型
  static void reportPkEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// pk过程中点击弹窗上面投票或者support按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间类型
  /// [act] vote=点击投票，support=拉起礼物面板
  /// [pkType] 上报当前PK的类型，vote=投票pk，gift=礼物pk
  /// [status] 当前PK是否是掠夺模式，0=非掠夺模式，1=掠夺模式
  /// [num] 当前pk的人数，用数字来表示
  /// [giftType] 上报当前的礼物类型
  /// [giftId] 上报当前的礼物id
  static void reportPkGamingClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? act,
    String? pkType,
    String? status,
    String? num,
    String? giftType,
    String? giftId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_gaming_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'act': act ?? '',
        'pk_type': pkType ?? '',
        'status': status ?? '',
        'num': num ?? '',
        'gift_type': giftType ?? '',
        'gift_id': giftId ?? '',
      },
      priority: priority,
    );
  }

  /// PK弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [pkType] 上报当前PK的类型，vote=投票pk，gift=礼物pk
  /// [status] 当前PK是否是掠夺模式，0=非掠夺模式，1=掠夺模式
  /// [num] 当前参与PK的人数，用数字进行表示
  /// [giftType] 上报当前的礼物类型
  /// [giftId] 上报当前的礼物id
  static void reportPkGamingImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? pkType,
    String? status,
    String? num,
    String? giftType,
    String? giftId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_gaming_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'pk_type': pkType ?? '',
        'status': status ?? '',
        'num': num ?? '',
        'gift_type': giftType ?? '',
        'gift_id': giftId ?? '',
      },
      priority: priority,
    );
  }

  /// pk弹窗点击最小化
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间类型
  /// [pkType] 上报当前PK的类型，vote=投票pk，gift=礼物pk
  /// [status] 当前PK是否是掠夺模式，0=非掠夺模式，1=掠夺模式
  /// [num] 当前参与PK的人数，用数字进行表示
  /// [giftType] 上报当前的礼物类型
  /// [giftId] 上报当前的礼物id
  static void reportPkGamingMin({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? pkType,
    String? status,
    String? num,
    String? giftType,
    String? giftId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_gaming_min',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'pk_type': pkType ?? '',
        'status': status ?? '',
        'num': num ?? '',
        'gift_type': giftType ?? '',
        'gift_id': giftId ?? '',
      },
      priority: priority,
    );
  }

  /// PK结果展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  /// [pkType] 上报当前PK的类型，vote=投票pk，gift=礼物pk
  /// [status] 当前PK是否是掠夺模式，0=非掠夺模式，1=掠夺模式
  /// [num] 当前参与PK的人数，用数字进行表示
  /// [toUid] 胜利者id
  /// [toGender] 胜利者性别
  /// [giftType] 上报当前礼物类型
  /// [giftId] 上报当前礼物id
  static void reportPkResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? pkType,
    String? status,
    String? num,
    String? toUid,
    String? toGender,
    String? giftType,
    String? giftId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'pk_type': pkType ?? '',
        'status': status ?? '',
        'num': num ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'gift_type': giftType ?? '',
        'gift_id': giftId ?? '',
      },
      priority: priority,
    );
  }

  /// pk设置中掠夺模式说明弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间模式
  static void reportPkSettingsBonusImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_settings_bonus_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// PK设置创建成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间类型
  /// [pkType] 上报当前PK的类型，vote=投票pk,gift=礼物pk,
  /// [status] 当前PK是否是掠夺模式，0=非掠夺模式，1=掠夺模式
  /// [num] 当前参与PK的人数，用数字进行表示
  /// [giftType] 上报当前的礼物类型
  /// [giftId] 上报当前的礼物id
  static void reportPkSettingsCreateSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? pkType,
    String? status,
    String? num,
    String? giftType,
    String? giftId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_settings_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'pk_type': pkType ?? '',
        'status': status ?? '',
        'num': num ?? '',
        'gift_type': giftType ?? '',
        'gift_id': giftId ?? '',
      },
      priority: priority,
    );
  }

  /// pk设置页面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前的房间类型
  static void reportPkSettingsImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'pk_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK点击云控配置的按钮入口跳转H5时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkConfigClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_config_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房间PK入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK功能展示时上报（无论在哪个节点，一个房间只报一次，统计功能覆盖
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk接收到pk邀请弹窗时点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toRoomId]
  /// [act] accept=接受pk申请，reject=拒绝pk申请
  static void reportRoompkInvitePopoutClick({
    String? uid,
    String? gender,
    String? roomId,
    String? toRoomId,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_invite_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_room_id': toRoomId ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK接收到Pk邀请弹窗时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toRoomId]
  static void reportRoompkInvitePopoutImp({
    String? uid,
    String? gender,
    String? roomId,
    String? toRoomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_invite_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_room_id': toRoomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk发出邀请成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toRoomId]
  /// [list] 从哪个推荐列表邀请的；follow/influencer；我关注的房间/主播在播房间
  static void reportRoompkInviteSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? toRoomId,
    String? list,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_invite_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_room_id': toRoomId ?? '',
        'list': list ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK邀请对决前置页（未发出邀请时页面上报）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [list] 曝光的推荐列表；follow/influencer；我关注的房间/主播在播房间
  static void reportRoompkInvitesearchImp({
    String? uid,
    String? gender,
    String? roomId,
    String? list,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_invitesearch_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'list': list ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk匹配失败展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkMatchfailedImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_matchfailed_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK匹配失败点击重试按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkMatchfailedRetry({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_matchfailed_retry',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK匹配中展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkMatchingImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_matching_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk点击选择pk方式按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [act] match=匹配对决，invite=邀请对决
  static void reportRoompkModeSelectClick({
    String? uid,
    String? gender,
    String? roomId,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_mode_select_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK选择匹配/邀请初始化界面展示
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkModeSelectImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_mode_select_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk合流音量引导气泡曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkPopoutImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk对战记录入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkRecordClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_record_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk对战记录入口筛选时间点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkRecordTimeClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_record_time_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk对战记录入口筛选时间确认点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [time] 场次日期
  static void reportRoompkRecordTimeConfirm({
    String? uid,
    String? gender,
    String? roomId,
    String? time,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_record_time_confirm',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'time': time ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房PK结束时结果页面上报	
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toRoomId]
  /// [result] victory 胜利/ defeat 失败/draw 平局
  static void reportRoompkResultImp({
    String? uid,
    String? gender,
    String? roomId,
    String? toRoomId,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_result_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_room_id': toRoomId ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk设置入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoompkSettingClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_setting_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// PK成功进入对决时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toRoomId] 对方房间id
  /// [type] PK进入方式，invite=邀请对决，match=匹配进入对决
  static void reportRoompkSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? toRoomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_room_id': toRoomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 跨房pk合流音量开光点击（打开开关上报on，关闭开关上报off）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 开关状态；on/off；打开/关闭
  static void reportRoompkSwitchClick({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'roompk_switch_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }
}
