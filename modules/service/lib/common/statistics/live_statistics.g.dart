import 'package:feature_flat_base/feature_flat_base.dart';

class LiveStatistics {
  LiveStatistics._();

  /// 点击领取奖励
  ///
  /// [tagId] 任务id
  static void reportBtnStreamerDailyTasksGet({
    String? tagId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'btn_streamer_daily_tasks_get',
      params: {
        'tag_id': tagId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击关播页面内的按钮（或入口）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [type] 点击按钮；avatar/follow/close/room；直播间头像/关注/关闭页面
  /// [toRoomId] 点击推荐房间id
  static void reportLiveCloseClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? type,
    String? toRoomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_close_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'type': type ?? '',
        'to_room_id': toRoomId ?? '',
      },
      priority: priority,
    );
  }

  /// 关播页面（用户视角）曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveCloseImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_close_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间链接状态上报
  ///
  /// [uid]
  /// [gender]
  /// [c1] 该用户的上行网络质量
  /// [c2] 该用户的下行网络质量
  /// [roomType] 上报后端所返回的房间类型
  static void reportLiveConnectionStatus({
    String? uid,
    String? gender,
    String? c1,
    String? c2,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_connection_status',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'c1': c1 ?? '',
        'c2': c2 ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 选择国家后点击提交
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [country] 提交的国家，上报国家简称
  static void reportLiveCounrtySave({
    String? uid,
    String? gender,
    String? roomId,
    String? country,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_counrty_save',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'country': country ?? '',
      },
      priority: priority,
    );
  }

  /// 直播开启成功后倒计时开始321时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveCountdownImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_countdown_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 选择国家入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveCountryEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_country_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 选择国家入口曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveCountryEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_country_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 成功修改直播间封面时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveCoverChange({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_cover_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播时点击美颜入口
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateBeautyClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beauty_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播设置美颜弹窗上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateBeautyImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beauty_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播美颜设置完成时上报，关闭美颜设置弹窗
  ///
  /// [uid]
  /// [gender]
  /// [c1] 美颜参数
  /// [c2] 美型参数
  /// [c3] 滤镜参数
  /// [c4] 美妆参数
  static void reportLiveCreateBeautySetSucc({
    String? uid,
    String? gender,
    String? c1,
    String? c2,
    String? c3,
    String? c4,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beauty_set_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'c1': c1 ?? '',
        'c2': c2 ?? '',
        'c3': c3 ?? '',
        'c4': c4 ?? '',
      },
      priority: priority,
    );
  }

  /// 直播美颜组件下载结束时上报
  ///
  /// [uid]
  /// [gender]
  /// [result] succ/fail
  /// [duration] 下载整耗时
  /// [reason] 失败原因code上报
  static void reportLiveCreateBeautyloadEnd({
    String? uid,
    String? gender,
    String? result,
    String? duration,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beautyload_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'duration': duration ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 直播美颜组件点击重新加载时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateBeautyloadReload({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beautyload_reload',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 直播美颜组件开始下载时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateBeautyloadStart({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_beautyload_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播被拦截弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 不被满足的条件code上报，或者上报不满足的文案内容
  static void reportLiveCreateConditionImp({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_condition_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 直播选择内容弹窗点击直播内容时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报所选择的直播内容
  static void reportLiveCreateContentClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_content_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播选择直播内容弹窗展示
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateContentImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_content_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播流程中点击开启直播
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateEnable({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_enable',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播入口点击
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 直播创建页面入口展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播点击功能栏位置功能
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报点击的function名字
  static void reportLiveCreateFucntionsClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_fucntions_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播页面展示时上报，
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否能开播，0=被condition拦截，1=没有授予麦克风权限，2=没有授予相机权限，3=正常开播
  /// [country] 上报开播时选择的国家；未选国家上报为空
  /// [mode] 房间模式；public/password/only_friends/only_my_follower/only_room_follower；公开房/密码房/仅好友可见/仅我的关注者可见/仅房间关注者可见
  static void reportLiveCreateImp({
    String? uid,
    String? gender,
    String? status,
    String? country,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'country': country ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播时直播间介绍设置页面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否有开启直播介绍，0=无开启，1=开启
  static void reportLiveCreateIntroImp({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_intro_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间介绍开启，点击save时候上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateIntroOpen({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_intro_open',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播选择完成对应发言权限时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报选择后的结果：all / friends/ fans
  static void reportLiveCreatePermissionClick({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_permission_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间权限展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreatePermissionImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_permission_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播时点击设置里的内容时上报
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报点击的item 名字，intro=介绍功能，permission=权限设置
  static void reportLiveCreateSettingsClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_settings_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播设置展示时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveCreateSettingsImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [country] 主播选择的国家；没有选的上报default
  /// [mode] public/password/only_friend/only_my_follower/only_room_follower；公开房/密码房/仅好友可见/仅我的关注者可见/仅房间关注者可见
  static void reportLiveCreateSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? country,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_create_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'country': country ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 主播点击decoration内的function
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] beauty=美颜设置，stickers=遮盖贴纸设置，paster=图文贴纸设置，intro=直播间介绍设置
  static void reportLiveDecorationClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_decoration_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 主播视角装饰弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveDecorationImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_decoration_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播编辑房间名成功时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveEditNameSucc({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_edit_name_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击下播结算页的返回按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveEndBackClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_end_back_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击下播结算页的视频直播中心入口
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveEndCenterClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_end_center_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击下播结算页的反馈按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveEndFeedbackClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_end_feedback_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 下播结算页曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [duration] 开播时长，单位为秒
  static void reportLiveEndImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_end_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 点击下播结算页的更多按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveEndMoreClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_end_more_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击直播间内的关注关注主播
  ///
  /// [touid] 主播的uid
  /// [pageFrom] info/minicard/screen：左上角/主播资料卡/公屏
  static void reportLiveFollow({
    String? touid,
    String? pageFrom,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_follow',
      params: {
        'touid': touid ?? '',
        'page_from': pageFrom ?? '',
      },
      priority: priority,
    );
  }

  /// 直播界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 主播的id上报
  /// [toGender]
  /// [roomId]
  /// [type] 当前的身份类型，streamer=主播，audience=观众
  static void reportLiveImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 点击直播间左上角主播信息
  ///
  /// [touid] 点击的主播uid
  static void reportLiveInfoClick({
    String? touid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_info_click',
      params: {
        'touid': touid ?? '',
      },
      priority: priority,
    );
  }

  /// 点击离开直播间弹窗内的按钮（或入口）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [type] 按钮点击；room/follow/leave/close；推荐房间/关注离开/离开/关闭弹窗
  /// [toRoomId] 点击推荐房间id
  static void reportLiveLeaveClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? type,
    String? toRoomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_leave_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'type': type ?? '',
        'to_room_id': toRoomId ?? '',
      },
      priority: priority,
    );
  }

  /// 离开直播间弹窗曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [followLeave] 是否有“关注并离开”的按钮；0/1；无/有
  /// [recommendRoom] 是否有推荐房间展示；0/1；无/有
  /// [toRoomId] 被推荐房间id
  static void reportLiveLeaveImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? followLeave,
    String? recommendRoom,
    String? toRoomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_leave_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'follow_leave': followLeave ?? '',
        'recommend_room': recommendRoom ?? '',
        'to_room_id': toRoomId ?? '',
      },
      priority: priority,
    );
  }

  /// 直播单次点赞
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [guide] 是否在引导动画期间点赞；0/1；否/是
  /// [vibration] 是否触发震动；0/1；否/是
  static void reportLiveLike({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? guide,
    String? vibration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_like',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'guide': guide ?? '',
        'vibration': vibration ?? '',
      },
      priority: priority,
    );
  }

  /// 点赞作弊（在一个直播间只上报一次）
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveLikeCheat({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_like_cheat',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点赞引导气泡曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveLikeGuideImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_like_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点赞震动开关
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [status] 开关状态；0/1；点击关闭/点击打开
  static void reportLiveLikeVibrationSwitch({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_like_vibration_switch',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 直播连续点赞
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [times] 点赞次数
  /// [vibration] 是否触发震动；0/1；否/是
  static void reportLiveManyLike({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? times,
    String? vibration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_many_like',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'times': times ?? '',
        'vibration': vibration ?? '',
      },
      priority: priority,
    );
  }

  /// 点击直播房间可见模式面板的模式
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [mode] 点击模式；public/password/only；公开房/密码房/指定房
  static void reportLiveModeClick({
    String? uid,
    String? gender,
    String? roomId,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_mode_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 直播房间可见模式面板曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveModeImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_mode_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 权限者对其他用户进行禁言操作时上报
  ///
  /// [uid]
  /// [gender]
  /// [toGender]
  /// [toUid]
  /// [status] 0/1，0=进行禁言，1=取消禁言
  /// [roomId]
  static void reportLiveMuteSucc({
    String? uid,
    String? gender,
    String? toGender,
    String? toUid,
    String? status,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_mute_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击直播间指定房模式面板的模式
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [onlyMode] 指定人群；friend/my_follower/room_follower；仅好友/仅我的关注者/仅房间关注者
  static void reportLiveOnlyModeClick({
    String? uid,
    String? gender,
    String? roomId,
    String? onlyMode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_only_mode_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'only_mode': onlyMode ?? '',
      },
      priority: priority,
    );
  }

  /// 修改密码二次确认弹窗点击确认
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId] 直播中时上报
  /// [status] 弹窗曝光时，直播状态；0/1；直播前/直播中
  /// [mention] 提交时是否勾选了不再提醒；0/1；否/是
  static void reportLivePasswordChangeClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? status,
    String? mention,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_password_change_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'status': status ?? '',
        'mention': mention ?? '',
      },
      priority: priority,
    );
  }

  /// 修改密码二次确认弹窗曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId] 直播中时上报
  /// [status] 弹窗曝光时，直播状态；0/1；直播前/直播中
  static void reportLivePasswordChangeImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_password_change_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 编辑房间密码弹窗曝光时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 弹窗曝光时，直播状态；0/1；直播前/直播中
  /// [liveId] 直播中时上报
  static void reportLivePasswordSettingImp({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_password_setting_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 提交密码
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId] 直播中时上报
  /// [status] 弹窗曝光时，直播状态；0/1；直播前/直播中
  /// [change] 密码是否变更；0/1；否/是
  static void reportLivePasswordSubmit({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? status,
    String? change,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_password_submit',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'status': status ?? '',
        'change': change ?? '',
      },
      priority: priority,
    );
  }

  /// 选择具体的贴纸
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前选择展示类型，text=可编辑的文字贴纸，image=不可编辑的图片贴纸
  /// [content] 上报选择的贴纸id
  static void reportLivePasterClick({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_paster_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 直播间贴纸删除成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报当前弹窗的展示类型，text=可编辑的文字贴纸，image=不可编辑的图片贴纸
  /// [roomId]
  /// [content] 上报选择的贴纸id
  static void reportLivePasterDeleteSucc({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_paster_delete_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 直播房主设置图文贴纸弹窗展示时上报，切换一次类型上报一次
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报当前弹窗的展示类型，text=可编辑的文字贴纸，image=不可编辑的图片贴纸
  static void reportLivePasterImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_paster_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 火力值描述页入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLivePopularityFaqClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_popularity_faq_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 分享海报入口点击
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLivePosterEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_poster_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 分享海报入口曝光
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLivePosterEntranceImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_poster_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 成功生成分享海报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLivePosterImp({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_poster_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 成功保存海报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLivePosterSaveSucc({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_poster_save_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击分享/保存海报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [type] 分享或保存；fb/ins/whatsapp/save
  static void reportLivePosterShare({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_poster_share',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 直播中天维度排行榜弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveRankingImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_ranking_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 直播中主播进行设置页面点击功能时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [content] admin （设置管理员）/ mute and block （禁言与拉黑）/ permissions（说话权限设置）/ filtered word（屏蔽词）
  static void reportLiveSettingsClick({
    String? uid,
    String? gender,
    String? roomId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_settings_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 直播中主播进行设置页面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveSettingsImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_settings_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击分享房间到直播间
  ///
  /// [type] live/voice：区分直播间/语音房
  static void reportLiveShareMomentClick({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_share_moment_click',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 发送分享房间的moment
  ///
  /// [type] live/voice
  static void reportLiveShareMomentPost({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_share_moment_post',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 主播回到前台时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 主播id
  /// [toGender] 主播性别
  /// [duration] 离去的时长上报
  /// [roomId]
  static void reportLiveStreamerLeaveEnd({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? duration,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_streamer_leave_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'duration': duration ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 主播开始不在前台时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [roomId]
  static void reportLiveStreamerLeaveStart({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_streamer_leave_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 点击房间内任务入口
  ///
  /// [roomId]
  static void reportLiveTaskClick({
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_task_click',
      params: {
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 成功修改直播间标题时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  static void reportLiveTitleChange({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_title_change',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
      },
      priority: priority,
    );
  }

  /// 创建直播上传直播封面成功时上报
  ///
  /// [uid]
  /// [gender]
  static void reportLiveUploadCoverSucc({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_upload_cover_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 主播被警告弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportLiveWarningImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'live_warning_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 任务页面曝光
  static void reportPageStreamerDailyTasks({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'page_streamer_daily_tasks',
      priority: priority,
    );
  }

  /// 用户主动退出直播间时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [liveId]
  /// [type] follow/leave/close/minimize；在离开弹窗点击关注离开/在离开弹窗点击直接离开/用户点击关闭直接离开/最小化
  /// [duration] 用户在直播间停留时间，在用户离开直播间（点击关注离开或直接离开）时上报，单位为秒
  static void reportUserLeaveLive({
    String? uid,
    String? gender,
    String? roomId,
    String? liveId,
    String? type,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'user_leave_live',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'live_id': liveId ?? '',
        'type': type ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }
}
