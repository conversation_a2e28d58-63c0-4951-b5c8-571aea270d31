import 'package:feature_flat_base/feature_flat_base.dart';

class BlinddateStatistics {
  BlinddateStatistics._();

  /// CP房活动页引导展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [from] 首页进入=home，banner进入=banner
  static void reportBlinddateEventguideImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'blinddate_eventguide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
