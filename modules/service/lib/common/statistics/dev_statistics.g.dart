import 'package:feature_flat_base/feature_flat_base.dart';

class DevStatistics {
  DevStatistics._();

  /// app 启动打点
  ///
  /// [duration] 时长
  static void reportAppStartup({
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'app_startup',
      params: {
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏行为
  ///
  /// [content] 游戏加载行为
  /// [gameId] 游戏id
  static void reportGameAction({
    String? content,
    String? gameId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_action',
      params: {
        'content': content ?? '',
        'game_id': gameId ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏下载完成
  ///
  /// [gameId] 游戏id
  /// [roomId] 房间号
  /// [result] 1成功，其他失败
  /// [content] 失败原因
  /// [duration] 时长
  static void reportGameDownloadEnd({
    String? gameId,
    String? roomId,
    String? result,
    String? content,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_download_end',
      params: {
        'game_id': gameId ?? '',
        'room_id': roomId ?? '',
        'result': result ?? '',
        'content': content ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏下载开始
  ///
  /// [gameId] 游戏id
  /// [roomId] 房间号
  static void reportGameDownloadStart({
    String? gameId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_download_start',
      params: {
        'game_id': gameId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏异常，显示reload
  ///
  /// [content] 异常内容
  /// [gameId] 游戏id
  static void reportGameError({
    String? content,
    String? gameId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_error',
      params: {
        'content': content ?? '',
        'game_id': gameId ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏fee匹配点击
  ///
  /// [result] 1成功，其他失败
  /// [gameId] 游戏id
  /// [content] 失败原因
  static void reportGameFeeMatchClick({
    String? result,
    String? gameId,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_fee_match_click',
      params: {
        'result': result ?? '',
        'game_id': gameId ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏init完成
  ///
  /// [gameId] 游戏id
  /// [roomId] 房间号
  /// [result] 1成功，其他失败
  /// [content] 失败原因
  /// [duration] 时长
  static void reportGameInitEnd({
    String? gameId,
    String? roomId,
    String? result,
    String? content,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_init_end',
      params: {
        'game_id': gameId ?? '',
        'room_id': roomId ?? '',
        'result': result ?? '',
        'content': content ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 游戏init开始
  ///
  /// [gameId] 游戏id
  /// [roomId] 房间号
  static void reportGameInitStart({
    String? gameId,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_init_start',
      params: {
        'game_id': gameId ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// http请求码
  ///
  /// [result] code
  /// [onTime] 请求时间
  static void reportHttpReqCode({
    String? result,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'http_req_code',
      params: {
        'result': result ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 信令耗时 采样20%
  ///
  /// [duration] 客户端到达时间（修正服务端时间）- 服务端发送时间 
  /// [content] 信令类型
  /// [type] rtm/rongcloud
  static void reportImSignalingAction({
    String? duration,
    String? content,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'im_signaling_action',
      params: {
        'duration': duration ?? '',
        'content': content ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 融云链接状态打点
  ///
  /// [result] 链接码
  static void reportRongImConnect({
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rong_im_connect',
      params: {
        'result': result ?? '',
      },
      priority: priority,
    );
  }
}
