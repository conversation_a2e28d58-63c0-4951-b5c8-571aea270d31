import 'package:feature_flat_base/feature_flat_base.dart';

class BottleStatistics {
  BottleStatistics._();

  /// 漂流瓶入口点击
  ///
  /// [uid]
  /// [gender]
  /// [from] 区分入口点击来源，homepage=首页点击/ msglist=聊天页顶部点击
  static void reportBottleEntranceClick({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 捞瓶子动作点击确认付费突破
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，是否有余额捞取，0余额不足，1成功付费。
  static void reportBottleFishBreakConfirm({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_break_confirm',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶主界面点击捞取瓶子
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前用户是否可捞取瓶子，0/1/2表示，0表示完全没有可捞瓶子数量（包括突破），1表示有免费捞瓶子数量。2表示有可突破捞瓶子数量。
  static void reportBottleFishClick({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶捞取失败界面展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，上报该次次数性质，0为免费捞取次数失败，1为突破捞取次数失败。
  /// [reason] 上报错误原因，命中无瓶子=nobottle，捞失败=noshot
  static void reportBottleFishFail({
    String? uid,
    String? gender,
    String? status,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_fail',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 捞瓶子动作出现的弹窗
  ///
  /// [type] 上报捞瓶子动作时弹窗类型，break=突破确认弹窗；throw=需扔瓶子弹窗；notime=没有次数弹窗。
  /// [uid]
  /// [gender]
  static void reportBottleFishPopout({
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_popout',
      params: {
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 捞瓶子接口请求结束时
  ///
  /// [result] succ/fail/other，succ=成功捞取到瓶子，fail=捞取失败，other=异常错误上报
  /// [reason] 失败原因
  /// [onTime] 得到结果耗时：单位毫秒
  static void reportBottleFishRequestEnd({
    String? result,
    String? reason,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_request_end',
      params: {
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 捞取瓶子接口请求开始
  static void reportBottleFishRequestStart({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_request_start',
      priority: priority,
    );
  }

  /// 捞取成功后卡片信息界面曝光展示
  ///
  /// [content] 上报当前捞取到的瓶子类型，text=文字瓶子，audio=声音瓶子
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportBottleFishSuccImp({
    String? content,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_succ_imp',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 捞取成功后卡片信息界面点击回复
  ///
  /// [content] 上报当前的瓶子类型，text=文字瓶子，audio=声音瓶子
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportBottleFishSuccReply({
    String? content,
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_succ_reply',
      params: {
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 捞取成功后卡片信息界面点击扔回海里
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报当前的瓶子类型，text=文字瓶子，audio=声音瓶子
  /// [toUid] 被捞取到的一方id
  /// [toGender] 被捞取到的一方性别
  static void reportBottleFishSuccThrow({
    String? uid,
    String? gender,
    String? content,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_fish_succ_throw',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶引导展示曝光
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报引导出现节点，throw=扔瓶子引导，fish=捞瓶子引导，setting=设置气泡引导，rule=规则页展示
  static void reportBottleGuideImp({
    String? uid,
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶加载页加载结束时
  ///
  /// [uid]
  /// [gender]
  /// [duration] 上报当次加载时长
  /// [status] 上报当次加载是否成功，succ/fail
  /// [reason] 失败原因
  static void reportBottleLoadingEnd({
    String? uid,
    String? gender,
    String? duration,
    String? status,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_loading_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'status': status ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶主界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportBottleMainImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_main_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶选择删除对话
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被删除一方id
  /// [toGender] 被删除一方性别
  static void reportBottleMsgDelete({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_msg_delete',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶消息对话主动点击揭开身份
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 发起来源，popout=固定弹窗按钮，system=点击系统消息提示
  static void reportBottleMsgUncover({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_msg_uncover',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击接受揭开身份按钮
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportBottleMsgUncoverAccept({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_msg_uncover_accept',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 揭开身份提示选择忽略
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportBottleMsgUncoverIgnored({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_msg_uncover_ignored',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶揭开身份成功界面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportBottleMsgUncoverSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_msg_uncover_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶主界面点击扔瓶子
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前用户是否可扔瓶子，0/1表示，0表示没有可扔瓶子数量，1表示有可扔瓶子数量。
  static void reportBottleThrowClick({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 录制界面二次确认弹窗确认退出
  ///
  /// [status] 0/1代表是否有录制/输入内容。
  /// [uid]
  /// [gender]
  static void reportBottleThrowExitConfirm({
    String? status,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_exit_confirm',
      params: {
        'status': status ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 漂流瓶录制瓶子界面展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报进入的来源，main=漂流瓶主界面，popout=前置判断弹窗。
  static void reportBottleThrowImp({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 扔瓶子界面选择删除录音
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报哪个节点删除，record=录制完成后没有提交即删除，finish=提交后删除
  static void reportBottleThrowRecordDelete({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_record_delete',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 扔瓶子录制结束（包含手动暂停和超过30s自动暂停）
  ///
  /// [duration] 上报音频时长
  /// [gender]
  /// [type] 录制暂停方式：manual=手动点击；timeout=超过30s自动暂停other=其他方式导致的暂停，比如录制过程中来电等
  /// [status] 声音状态上报，0/1，0无异常提示出现，1上报声音太小提示
  static void reportBottleThrowRecordEnd({
    String? duration,
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_record_end',
      params: {
        'duration': duration ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 扔瓶子界面点击开始录制声音
  ///
  /// [uid]
  /// [gender]
  static void reportBottleThrowRecordStart({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_record_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 扔瓶子录制界面点击提交
  ///
  /// [duration] 音频时长
  /// [uid]
  /// [gender]
  static void reportBottleThrowRecordSubmit({
    String? duration,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_record_submit',
      params: {
        'duration': duration ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 扔瓶子界面点击右上方发送瓶子
  ///
  /// [type] 上报当前发送出去的瓶子类型，例如mood/joke/sing/truth or dare
  /// [content] 上报当前内容类型，text=文字瓶子，audio=声音瓶子
  /// [uid]
  /// [gender]
  /// [duration] 上报音频时长，非音频时上报为0
  /// [from] 上报进入的来源，main=漂流瓶主界面，popout=前置判断弹窗。
  static void reportBottleThrowSend({
    String? type,
    String? content,
    String? uid,
    String? gender,
    String? duration,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'bottle_throw_send',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
