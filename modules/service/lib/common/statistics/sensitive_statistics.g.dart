import 'package:feature_flat_base/feature_flat_base.dart';

class SensitiveStatistics {
  SensitiveStatistics._();

  /// 服务端校验用户提交的内容触发了敏感词时上报
  ///
  /// [refer] 校验内容触发的场景//user_nikename 用户昵称文本user_bio 用户个人简介文本moment_content 动态内容文本moment_theme 动态主题文本moment_title_content 动态title文本chat_message 聊天文本消息
  /// [sensitiveWord] 命中的敏感词
  /// [content] 提交校验的完整内容
  /// [sensitiveLevel] 敏感词等级
  /// [type] 命中的敏感词类型//Sexually Explicit/Religiously/Illegal/Political/Spam/Violence/Privacy
  static void reportSensitiveWordReviewResult({
    String? refer,
    String? sensitiveWord,
    String? content,
    String? sensitiveLevel,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'sensitive_word_review_result',
      params: {
        'refer': refer ?? '',
        'sensitive_word': sensitiveWord ?? '',
        'content': content ?? '',
        'sensitive_level': sensitiveLevel ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }
}
