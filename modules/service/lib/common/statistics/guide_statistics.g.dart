import 'package:feature_flat_base/feature_flat_base.dart';

class GuideStatistics {
  GuideStatistics._();

  /// 社区规范说明弹窗展示时
  static void reportCommunityGuideImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'community_guide_imp',
      priority: priority,
    );
  }

  /// 消息列表，点击音视频通话引导互相关注的弹窗上点击follow时
  ///
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportFollowGuideCallFollow({
    String? isFollowing,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'follow_guide_call_follow',
      params: {
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表，点击音视频通话引导互相关注的弹窗出现时
  ///
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportFollowGuideCallImp({
    String? isFollowing,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'follow_guide_call_imp',
      params: {
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天会话的消息屏上，关注用户引导点击follow时
  ///
  /// [from] msg_20/followed；触发引导展示的场景，未关注对方且给对方发送的聊天信息超过20条/未关注对方且对方关注你后
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportFollowGuideMsgListFollow({
    String? from,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'follow_guide_msg_list_follow',
      params: {
        'from': from ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天会话的消息屏上，关注用户引导展示时
  ///
  /// [from] msg_20/followed；触发引导展示的场景，未关注对方且给对方发送的聊天信息超过20条/未关注对方且对方关注你后
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  static void reportFollowGuideMsgListImp({
    String? from,
    String? isFollowed,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'follow_guide_msg_list_imp',
      params: {
        'from': from ?? '',
        'is_followed': isFollowed ?? '',
      },
      priority: priority,
    );
  }

  /// 用户第一次未产生互动离开聊天会话页面，提醒用户会放弃匹配的弹窗上产生点击时
  ///
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  /// [act] stay/leave；弹窗上的点击，继续聊天/放弃匹配
  static void reportLeaveMatchPopoutAct({
    String? isOnline,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'leave_match_popout_act',
      params: {
        'is_online': isOnline ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 用户第一次未产生互动离开聊天会话页面，提醒用户会放弃匹配的弹窗展示时
  ///
  /// [isOnline] 0/1；被匹配对象在线状态，0不在线，1在线
  static void reportLeaveMatchPopoutImp({
    String? isOnline,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'leave_match_popout_imp',
      params: {
        'is_online': isOnline ?? '',
      },
      priority: priority,
    );
  }

  /// 引导发送Moment弹窗展示曝光
  ///
  /// [uid]
  /// [gender]
  /// [from] 触发引导展示的场景 like/chat/view_moment/comment
  static void reportMomentGuidePopout({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_guide_popout',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 引导发送Moment弹窗点击Go to post按钮
  ///
  /// [uid]
  /// [gender]
  /// [from] 触发引导展示的场景 like/chat/view_moment/comment
  static void reportMomentGuidePopoutPost({
    String? uid,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'moment_guide_popout_post',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 聊天会话的消息屏上，发布动态引导展示时
  static void reportPostGuideMsgListImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_guide_msg_list_imp',
      priority: priority,
    );
  }

  /// 聊天会话的消息屏上，动态引导上点击post按钮时
  static void reportPostGuideMsgListPost({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'post_guide_msg_list_post',
      priority: priority,
    );
  }

  /// 评分引导弹窗关闭时
  ///
  /// [from] view_userpage/chat/quiz/view_moment/follow/comment/like_moment；触发引导展示的场景
  /// [type] close/feedback/gp；弹窗被关闭的方式
  /// [content] 1/2/3/4/5；弹窗关闭时用户所选的评分，没有则不上报
  static void reportRatingGuideClose({
    String? from,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rating_guide_close',
      params: {
        'from': from ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 评分引导弹窗展示时
  ///
  /// [from] view_userpage/chat/quiz/view_moment/follow/comment/like_moment；触发引导展示的场景
  static void reportRatingGuideImp({
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rating_guide_imp',
      params: {
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 评分引导弹窗用户选择评星时
  ///
  /// [from] view_userpage/chat/quiz/view_moment/follow/comment/like_moment；触发引导展示的场景
  /// [content] 1/2/3/4/5；用户所选的评分
  static void reportRatingGuideRate({
    String? from,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'rating_guide_rate',
      params: {
        'from': from ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 首次在聊天会话页面进行截图的截图弹窗提醒展示
  static void reportScreenshotPopoutImp({
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'screenshot_popout_imp',
      priority: priority,
    );
  }

  /// sensitive_alert_popout弹窗上按钮产生点击时（V1.02.00 男用户发送敏感信息提醒）
  ///
  /// [type] photo照片/account社交媒体账号/porn黄暴
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [act] clear_msg=清空聊天信息keep_send=选择继续发送原信息
  static void reportSensitiveAlertClick({
    String? type,
    String? isOnline,
    String? toUid,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'sensitive_alert_click',
      params: {
        'type': type ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// sensitive_alert_popout这个弹窗展示时（V1.02.00 男用户发送敏感信息提醒）
  ///
  /// [type] photo照片/account社交媒体账号/porn黄暴
  /// [toUid] 聊天用户的ID
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  static void reportSensitiveAlertShow({
    String? type,
    String? toUid,
    String? isOnline,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'sensitive_alert_show',
      params: {
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'is_online': isOnline ?? '',
      },
      priority: priority,
    );
  }

  /// 未互关的男用户给女用户的会话在关系初期时，点击发送信息时，文字信息中包含敏感关键词时上报
  ///
  /// [toUid] 聊天用户的ID
  /// [type] photo照片/account社交媒体账号/porn黄暴，敏感词的类别
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  static void reportSensitiveAlertTrigger({
    String? toUid,
    String? type,
    String? isOnline,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'sensitive_alert_trigger',
      params: {
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'is_online': isOnline ?? '',
      },
      priority: priority,
    );
  }
}
