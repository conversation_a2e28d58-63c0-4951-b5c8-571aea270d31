import 'package:feature_flat_base/feature_flat_base.dart';

class IntimacyStatistics {
  IntimacyStatistics._();

  /// 亲密值增长提示展示
  ///
  /// [uid]
  /// [gender]
  /// [num] 上报当前增长的亲密值数值
  /// [toGender]
  /// [toUid]
  static void reportIntimacyIncreaseImp({
    String? uid,
    String? gender,
    String? num,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intimacy_increase_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 亲密值流水界面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportIntimacyOthersDetailsImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intimacy_others_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 查看我与他人的亲密值二级详细页面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [num] 上报我与当前该人的亲密值数字
  /// [from] 上报进入该页面来源，chat=私聊中，profile=资料页
  static void reportIntimacyOthersImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? num,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'intimacy_others_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'num': num ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
