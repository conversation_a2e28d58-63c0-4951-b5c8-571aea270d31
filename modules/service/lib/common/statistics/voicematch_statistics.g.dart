import 'package:feature_flat_base/feature_flat_base.dart';

class VoicematchStatistics {
  VoicematchStatistics._();

  /// 在Chat tab 点击声音匹配入口进入
  ///
  /// [gender] 发起人性别
  /// [status] 当天是否有剩余次数：0=没有，1=有
  static void reportChatVmatchClick({
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'chat_vmatch_click',
      params: {
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 首页点击声音匹配入口
  ///
  /// [gender] 发起人性别
  /// [status] 当天是否有剩余次数：0=没有，1=有
  static void reportHomeVmatchClick({
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'home_vmatch_click',
      params: {
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// bio处点击声音录制入口
  ///
  /// [gender] 录制人性别
  static void reportVmatchBioRecord({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_bio_record',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配次数用完倒计时页面展示
  ///
  /// [gender] 发起方性别
  static void reportVmatchCountdownImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_countdown_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 记录声音匹配流程时长
  ///
  /// [duration] 声音流程的停留时长，单位s
  /// [status] 0/1/2，0后退销毁退出，1跳转聊天退出，2跳转录制退出
  /// [from] home_vmatch_click / chat_vmatch_click ，记录从哪里进入
  /// [gender]
  static void reportVmatchDuration({
    String? duration,
    String? status,
    String? from,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_duration',
      params: {
        'duration': duration ?? '',
        'status': status ?? '',
        'from': from ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配预加载界面被关闭时
  ///
  /// [duration] 页面停留时长，秒计算
  /// [gender] 发起人性别
  /// [status] 1=接口返回成功且第一个音频文件预加载成功；2=接口返回成功，第一个音频文件预加载失败或超时；3=接口返回失败或超时4=接口尚未返回
  /// [type] 关闭方式：enter=进入声音匹配界面；exit=用户手动离开
  static void reportVmatchLoadingDuration({
    String? duration,
    String? gender,
    String? status,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_loading_duration',
      params: {
        'duration': duration ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面点击选择单个答案时
  ///
  /// [result] 0/1，正确或者错误，0为错误，1为正确
  /// [gender] 点击答案用户性别
  /// [status] 1st=当前回答该用户第一个问题；2nd=当前回答该用户第二个问题
  /// [content] 上报内容id
  /// [toGender]
  /// [toUid]
  static void reportVmatchPageAnswerClick({
    String? result,
    String? gender,
    String? status,
    String? content,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_answer_click',
      params: {
        'result': result ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'content': content ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面用户手动点击播放声音问题音频
  ///
  /// [gender] 点击用户性别
  /// [toGender]
  /// [toUid]
  static void reportVmatchPageAudioplayClick({
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_audioplay_click',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面点击退出
  ///
  /// [status] 上报退出时已答多少题，0/1/2
  /// [id] 退出的用户id
  /// [duration] 记录用户功能使用时长
  static void reportVmatchPageExit({
    String? status,
    String? id,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_exit',
      params: {
        'status': status ?? '',
        'id': id ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面展示时
  ///
  /// [from] Home页进入报Home_vmatch_click，Chat进入报Chat_vmatch_click
  /// [gender] 发起匹配的用户性别
  /// [toGender] 当前页面匹配的用户性别
  /// [toUid] 被匹配人id
  /// [status] 0/1，匹配页面展示时，当前页面的音频素材是否加载完毕：0未加载完成；1=加载完毕
  static void reportVmatchPageImp({
    String? from,
    String? gender,
    String? toGender,
    String? toUid,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_imp',
      params: {
        'from': from ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面点击录制问题入口
  ///
  /// [gender] 点击录制用户性别
  static void reportVmatchPageRecord({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_record',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配界面点击Skip跳过
  ///
  /// [gender] 发起匹配的用户性别
  /// [toGender]
  /// [toUid]
  /// [status] skip时是否播放过被匹配用户的音频：0=未播放；1=已播放
  /// [from] skip时位于第几道题目：1st=第一道；2nd=第二道
  static void reportVmatchPageSkip({
    String? gender,
    String? toGender,
    String? toUid,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_skip',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-发起匹配的用户看到的被匹配方曝光数
  ///
  /// [gender] 发起方性别
  /// [toGender] 被匹配方性别
  /// [uid] 发起方id
  /// [toUid] 被匹配方id
  static void reportVmatchPageUserimp({
    String? gender,
    String? toGender,
    String? uid,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_page_userimp',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'uid': uid ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-单个问题的录制音频点击播放音频
  ///
  /// [gender] 录制人性别
  /// [content] 当前问题的ID
  static void reportVmatchQuestionRecordAudioplay({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_audioplay',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-录制问题界面更改问题
  ///
  /// [gender] 性别
  /// [content] 上报当前要更改的问题id
  static void reportVmatchQuestionRecordChange({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_change',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 单个问题录制界面点击开始录制
  ///
  /// [content] 上报问题内容id
  /// [gender] 录制人性别
  static void reportVmatchQuestionRecordClick({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-单个问题的录制音频点击删除
  ///
  /// [gender] 录制人性别
  /// [content] 当前问题的ID
  static void reportVmatchQuestionRecordDelete({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_delete',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-单个问题录制结束时（包含手动暂停和超过60s自动暂停）
  ///
  /// [duration] 音频时长
  /// [gender] 录制人性别
  /// [status] 声音状态上报，0/1，0无异常提示出现，1上报声音太小提示
  /// [type] 录制暂停方式：manual=手动点击；timeout=超过60s自动暂停other=其他方式导致的暂停，比如录制过程中来电等
  /// [content] 当前所录制的问题ID
  static void reportVmatchQuestionRecordEnd({
    String? duration,
    String? gender,
    String? status,
    String? type,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_end',
      params: {
        'duration': duration ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'type': type ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-单个问题录制音频提交
  ///
  /// [gender] 录制人性别
  /// [duration] 音频时长
  /// [content] 提交内容id
  static void reportVmatchQuestionRecordSubmit({
    String? gender,
    String? duration,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_question_record_submit',
      params: {
        'gender': gender ?? '',
        'duration': duration ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 单个问题录制界面选择答案点击
  ///
  /// [content] 上报问题内容id
  /// [gender] 录制人性别
  static void reportVmatchRecordAnswerClick({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_answer_click',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-单个问题录制，选择答案页面展示时
  ///
  /// [content] 上报问题内容id
  /// [gender] 录制人性别
  static void reportVmatchRecordAnswerImp({
    String? content,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_answer_imp',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 记录用户录制声音流程时长
  ///
  /// [duration] 录制流程时长，单位s
  /// [status] 0/1，0代表中途退出，1代表最后完成退出
  /// [from] vmatch_page_record / vmatch_result_succ_record / vmatch_bio_record ，来源入口（主动点击匹配页面入口，结果页跳转进入， bio直接进入）
  /// [gender]
  static void reportVmatchRecordDuration({
    String? duration,
    String? status,
    String? from,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_duration',
      params: {
        'duration': duration ?? '',
        'status': status ?? '',
        'from': from ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-录制过程中退出弹窗确认
  ///
  /// [gender] 退出确认方性别
  /// [status] 确认退出时，当前已录制声音个数，0，1，2
  static void reportVmatchRecordExitConfirm({
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_exit_confirm',
      params: {
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 录制过程退出出现的二次确认弹窗
  ///
  /// [gender] 录制人性别
  /// [status] 弹窗展示时，当前已录制声音个数，0，1，2
  static void reportVmatchRecordExitPopout({
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_exit_popout',
      params: {
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-录制问题界面展示
  ///
  /// [gender] 录制人性别
  /// [content] 当前展示的问题的ID
  static void reportVmatchRecordQuestionImp({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_question_imp',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-两道问题录制结束后点击重新编辑
  ///
  /// [gender] 录制人性别
  static void reportVmatchRecordResultEdit({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_result_edit',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-两道问题录制结束后结果页展示时
  ///
  /// [gender] 录制人性别
  static void reportVmatchRecordResultImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_result_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 两道问题录制结束后点击提交
  ///
  /// [gender] 录制人性别
  /// [type] 提交时BGM对应的ID
  /// [status] BGM是否是默认选择的：default=默认随机的BGMmanual=手动改动的BGM
  static void reportVmatchRecordResultSubmit({
    String? gender,
    String? type,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_result_submit',
      params: {
        'gender': gender ?? '',
        'type': type ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配-两道问题录制结束页面上手动切换BGM
  ///
  /// [gender] 录制人性别
  /// [type] 要切换时BGM对应的ID
  static void reportVmatchRecordResultSwitch({
    String? gender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_result_switch',
      params: {
        'gender': gender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 开始录制前提示界面
  ///
  /// [from] 来源，报Vmatch_page_record/Vmatch_result_succ_record/Bio_record_entrance 三者。对应：匹配主界面进入，匹配结果解锁聊天进入，bio个人主页进入。
  /// [gender] 当前用户性别
  static void reportVmatchRecordStart({
    String? from,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_start',
      params: {
        'from': from ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 录制主提示界面record my question按钮点击
  ///
  /// [gender] 录制用户性别
  /// [from] 进入页面的来源，报Vmatch_page_record/Vmatch_result_succ_record/Bio_record_entrance 三者。对应：匹配主界面进入，匹配结果解锁聊天进入，bio个人主页进入。
  static void reportVmatchRecordStartClick({
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_record_start_click',
      params: {
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配接口请求失败/超时
  ///
  /// [type] info/match/done/upload/record/match_succ  info:信息（问题和背景音乐），match:匹配用户，done （选一个答案，扣除次数），upload:获取上传单个问题录音地址，record:录音完成,match_succ 成功聊天（预设不用
  /// [onTime] 请求耗时，单位毫秒
  /// [reason] 失败错误码
  static void reportVmatchRequestFail({
    String? type,
    String? onTime,
    String? reason,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_request_fail',
      params: {
        'type': type ?? '',
        'on_time': onTime ?? '',
        'reason': reason ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配接口开始请求
  ///
  /// [type] info/match/done/upload/record/match_succ  info:信息（问题和背景音乐），match:匹配用户，done （选一个答案，扣除次数），upload:获取上传单个问题录音地址，record:录音完成,match_succ 成功聊天（预设不用
  static void reportVmatchRequestStart({
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_request_start',
      params: {
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配接口请求成功时
  ///
  /// [type] info/match/done/upload/record/match_succ  info:信息（问题和背景音乐），match:匹配用户，done （选一个答案，扣除次数），upload:获取上传单个问题录音地址，record:录音完成,match_succ 成功聊天（预设不用
  /// [onTime] 请求耗时，单位毫秒
  static void reportVmatchRequestSucc({
    String? type,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_request_succ',
      params: {
        'type': type ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配失败结果展示
  ///
  /// [gender] 发起方性别
  /// [toGender] 被匹配方性别
  /// [toUid] 被匹配方id
  static void reportVmatchResultFail({
    String? gender,
    String? toGender,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_fail',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配失败选择继续匹配
  ///
  /// [gender] 点击用户性别
  static void reportVmatchResultFailContinue({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_fail_continue',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配成功结果展示
  ///
  /// [gender] 发起方性别
  /// [toUid] 被匹配方id
  /// [toGender] 被匹配方性别
  static void reportVmatchResultSucc({
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_succ',
      params: {
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配成功点击去聊天
  ///
  /// [gender] 发起方性别
  /// [toGender] 被匹配方性别
  /// [id] 发起方id
  /// [toUid] 被匹配方id
  static void reportVmatchResultSuccChat({
    String? gender,
    String? toGender,
    String? id,
    String? toUid,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_succ_chat',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'id': id ?? '',
        'to_uid': toUid ?? '',
      },
      priority: priority,
    );
  }

  /// 匹配成功点击继续匹配
  ///
  /// [gender] 发起方性别
  static void reportVmatchResultSuccContinue({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_succ_continue',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音匹配成功后弹窗，点击去录制声音
  ///
  /// [uid] 用户id
  /// [gender] 用户性别
  static void reportVmatchResultSuccRecord({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_result_succ_record',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 声音问题匹配点击unlockmore增加次数按钮
  ///
  /// [uid]
  /// [gender]
  /// [status] 0/1，是否有余额增加次数，0余额不足，1成功增加。
  static void reportVmatchUnlockmoreClick({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'vmatch_unlockmore_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }
}
