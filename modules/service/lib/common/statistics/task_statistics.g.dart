import 'package:feature_flat_base/feature_flat_base.dart';

class TaskStatistics {
  TaskStatistics._();

  /// 房间内清屏功能使用成功时上报（成功清除公屏），有一位触发时上报一次
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoomCleanChatSucc({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_clean_chat_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房间任务页点击任务栏右侧按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [status] 0/1，上报当前点击按钮状态为1=领取奖励或0=跳转deeplink
  /// [content] 上报点击的action code
  static void reportRoomTaskClick({
    String? uid,
    String? gender,
    String? roomId,
    String? status,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_task_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'status': status ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 房间任务引导展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportRoomTaskGuideImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_task_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房间任务半屏页展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId] 当前所在的room id
  static void reportRoomTaskImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'room_task_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 任务列表曝光时上报
  ///
  /// [from] 上报打开来源，mine 我的主页，主页来源，上报打开来源，mine 我的主页，主页来源，more 更多任务弹框来源，男性用户message来源
  static void reportTaskPageImp({
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'task_page_imp',
      params: {
        'from': from ?? '',
      },
      priority: priority,
    );
  }
}
