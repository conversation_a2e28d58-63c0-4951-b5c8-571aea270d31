import 'package:feature_flat_base/feature_flat_base.dart';

class CoupleStatistics {
  CoupleStatistics._();

  /// 上报在CP中心页面的点击动作
  ///
  /// [uid]
  /// [gender]
  /// [act] propose=求婚入口，divorce=离婚入口，ranking=排行榜头像
  static void reportCoupleCenterClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_center_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// CP中心点击右上角进入规则页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportCoupleCenterGuidelineImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_center_guideline_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// CP中心的页面展示
  ///
  /// [uid]
  /// [gender]
  static void reportCoupleCenterImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_center_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// cp系统在房间all列表的入口点击
  ///
  /// [uid]
  /// [gender]
  static void reportCoupleEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// cp系统在房间all列表的入口展示上报
  ///
  /// [uid]
  /// [gender]
  static void reportCoupleEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击cp任务按钮时上报
  ///
  /// [status] 0/1，上报当前点击按钮状态为1=领取奖励或0=跳转deeplink
  /// [content] 上报点击的action code
  /// [uid]
  /// [gender]
  static void reportCoupleTaskClick({
    String? status,
    String? content,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'couple_task_click',
      params: {
        'status': status ?? '',
        'content': content ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// cp查询接口请求结束时
  ///
  /// [uid]
  /// [gender]
  /// [act] info 查询对方信息｜ list 查询cp列表 ｜ love_zone 爱心小窝 |  intimacy_detail 亲密度流水 ｜ daily_change 每日亲密度变化 | divorce_info 离婚信息 | gift_history 礼物历史记录 | wihes 祝福值
  /// [reason] 失败原因
  /// [result] succ/fail
  /// [onTime] 接口请求时长
  static void reportCpsearchRequestEnd({
    String? uid,
    String? gender,
    String? act,
    String? reason,
    String? result,
    String? onTime,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'cpsearch_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'reason': reason ?? '',
        'result': result ?? '',
        'on_time': onTime ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚界面收到协议离婚方界面点击
  ///
  /// [uid]
  /// [gender]
  /// [act]  reject=拒绝，accept=接受
  /// [toUid]
  /// [toGender]
  static void reportDivorceActionClick({
    String? uid,
    String? gender,
    String? act,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_action_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚主界面点击动作
  ///
  /// [uid]
  /// [gender]
  /// [act] propose=点击去求婚，Compulsory=强制离婚，Consensual=协议离婚
  static void reportDivorceClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚主界面展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否为已结婚状态，0=无结婚，1=已结婚，2=等待对方处理离婚，3=等待我处理离婚的状态
  static void reportDivorceImp({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚接口请求结束时
  ///
  /// [uid]
  /// [gender]
  /// [act] divorce_agreement 离婚协议 ｜ divorce_force 强制离婚 | divorce_pass 同意 ｜ divorce_reject 拒绝离婚
  /// [onTime] 接口请求时间
  /// [reason] 失败原因
  /// [result] succ/fail
  static void reportDivorceRequestEnd({
    String? uid,
    String? gender,
    String? act,
    String? onTime,
    String? reason,
    String? result,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'on_time': onTime ?? '',
        'reason': reason ?? '',
        'result': result ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚接口请求开始时
  ///
  /// [uid]
  /// [gender]
  /// [act] divorce_agreement 离婚协议 ｜ divorce_force 强制离婚 | divorce_pass 同意 ｜ divorce_reject 拒绝离婚
  static void reportDivorceRequestStart({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚二次确认弹窗点击
  ///
  /// [uid]
  /// [gender]
  /// [type] 弹窗不同的类型，Compulsory=强制离婚，Consensual=协议离婚
  /// [toUid]
  /// [toGender]
  static void reportDivorceSecondClick({
    String? uid,
    String? gender,
    String? type,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_second_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚二次确认弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 弹窗不同的类型，Compulsory=强制离婚，Consensual=协议离婚
  /// [toUid]
  /// [toGender]
  static void reportDivorceSecondImp({
    String? uid,
    String? gender,
    String? type,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_second_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 离婚成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [type] 上报离婚的方式，弹窗不同的类型，Compulsory=强制离婚，Consensual=协议离婚
  static void reportDivorceSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'divorce_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝入口点击（Profile页）
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneEntranceClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝入口展示（Profile页）
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneEntranceImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝主界面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 查看的uid小窝
  /// [toGender] 查看的uid性别
  /// [status] 当前小窝是否是已婚状态，0=没有cp，1=已有cp
  static void reportLovezoneImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝中戒指盒界面点击动作
  ///
  /// [uid]
  /// [gender]
  /// [act] wear=更换佩戴的戒指，add=跳转至新增新的戒指页面
  static void reportLovezoneRingBoxClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_ring_box_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝中戒指盒界面展示
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneRingBoxImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_ring_box_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝戒指入口点击
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneRingClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_ring_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝中戒指盒新增戒指页点击佩戴新的戒指
  ///
  /// [uid]
  /// [gender]
  /// [content] 新戒指的商品id
  static void reportLovezoneRingWearClick({
    String? uid,
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_ring_wear_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝中戒指盒新增戒指页展示
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneRingWearImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_ring_wear_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 小窝中点击送礼icon增加祝福指数
  ///
  /// [uid]
  /// [gender]
  static void reportLovezoneSendGiftClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'lovezone_send_gift_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 有CP的profile展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  static void reportProfileCoupleImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'profile_couple_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚最后一步选择完信函后需花费多少钻石二次确认弹窗点击购买
  ///
  /// [uid]
  /// [gender]
  /// [cost] 上报所需要花费的钻石额度
  /// [content] 上报所选择的信函商品id
  /// [toUid]
  /// [toGender]
  static void reportProposeCostClick({
    String? uid,
    String? gender,
    String? cost,
    String? content,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_cost_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'cost': cost ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚最后一步选择完信函后需花费多少钻石二次确认弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [cost] 上报所需要花费的钻石额度
  /// [content] 上报所选择的信函商品id
  /// [toUid]
  /// [toGender]
  static void reportProposeCostImp({
    String? uid,
    String? gender,
    String? cost,
    String? content,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_cost_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'cost': cost ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚点击查看求婚函详情页展示
  ///
  /// [uid]
  /// [gender]
  /// [stauts] 当前信函状态，waiting=等待对方处理，reject=已拒绝，accept=已答应求婚，invalid=求婚函已失效
  /// [from] 求婚函来源，propose=求婚等待界面，message=消息处，witness=助手进入
  /// [toUid]
  /// [toGender]
  static void reportProposeDetailsImp({
    String? uid,
    String? gender,
    String? stauts,
    String? from,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_details_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'stauts': stauts ?? '',
        'from': from ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 被求婚者点击处理求婚函
  ///
  /// [uid]
  /// [gender]
  /// [act] reject=拒绝求婚，accept=接受求婚
  /// [from] 求婚函来源，message=消息处，witness=助手进入
  /// [toUid]
  /// [toGender]
  static void reportProposeDetailsImpClick({
    String? uid,
    String? gender,
    String? act,
    String? from,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_details_imp_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
        'from': from ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚主界面点击去求婚按钮
  ///
  /// [uid]
  /// [gender]
  static void reportProposeGo({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_go',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚主界面展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前是否为已结婚状态，0=无结婚，1=已结婚，2=求婚中
  /// [from] 上报求婚页面来源，center=cp中心，intimacy_info=亲密值详情界面，divorce=离婚空状态
  static void reportProposeImp({
    String? uid,
    String? gender,
    String? status,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第四步选择信函界面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被求婚者uid
  /// [toGender] 被求婚者性别
  static void reportProposeLetterImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_letter_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第四步选择信函界面选择完信函后点击下一步
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报所选择的信函商品id
  /// [toUid]
  /// [toGender]
  static void reportProposeLetterNext({
    String? uid,
    String? gender,
    String? content,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_letter_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求发起结婚接口结束时
  ///
  /// [uid]
  /// [gender]
  /// [onTime] 接口请求时间
  /// [result] succ /fail
  /// [reason] 失败原因
  /// [ringId] 上报戒指id
  /// [letterId] 上报信函id
  /// [toUid]
  /// [toGender]
  static void reportProposeRequestEnd({
    String? uid,
    String? gender,
    String? onTime,
    String? result,
    String? reason,
    String? ringId,
    String? letterId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_request_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'on_time': onTime ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'ring_id': ringId ?? '',
        'letter_id': letterId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 请求发起结婚接口开始时
  ///
  /// [uid]
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [ringId] 上报戒指id
  /// [letterId] 上报信函id
  static void reportProposeRequestStart({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? ringId,
    String? letterId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_request_start',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'ring_id': ringId ?? '',
        'letter_id': letterId ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第二步选择戒指时提示无戒指展示
  ///
  /// [uid]
  /// [gender]
  static void reportProposeRingNoImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_ring_no_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第二步选择戒指时提示无戒时点击跳转商店
  ///
  /// [uid]
  /// [gender]
  static void reportProposeRingNoStore({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_ring_no_store',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第二步有戒指，选择戒指时展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被求婚对象的id
  /// [toGender] 被求婚对象的性别
  static void reportProposeRingSelectImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_ring_select_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第二步有戒指，选择戒指点击下一步
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被求婚对象的id
  /// [toGender] 被求婚对象的性别
  /// [content] 上报所选择的戒指商品id
  static void reportProposeRingSelectNext({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_ring_select_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第一步选择求婚对象展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 当前有无可发出求婚的对象，0=无求婚对象，1=有可求婚对象
  static void reportProposeSelectFirstImp({
    String? uid,
    String? gender,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_select_first_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第一步选择求婚对象点击下一步
  ///
  /// [uid]
  /// [gender]
  static void reportProposeSelectFirstNext({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_select_first_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚步骤全部走完成功发出求婚申请
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被求婚者id
  /// [toGender] 被求婚者性别
  /// [content] 上报所求婚的戒指商品id
  static void reportProposeSucc({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_succ',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第三步填写誓言界面展示
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被求婚者uid
  /// [toGender] 被求婚者性别
  static void reportProposeVowImp({
    String? uid,
    String? gender,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_vow_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 求婚第三步填写完誓言点击下一步
  ///
  /// [uid]
  /// [gender]
  static void reportProposeVowNext({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'propose_vow_next',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
