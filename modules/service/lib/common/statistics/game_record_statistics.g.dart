import 'package:feature_flat_base/feature_flat_base.dart';

class GameRecordStatistics {
  GameRecordStatistics._();

  /// 看过我的停留时长，离开/app切到后台/切换页面时上报
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [duration] 页面停留时长，单位s
  static void reportGameRecordDuration({
    String? uid,
    String? gender,
    String? duration,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_record_duration',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'duration': duration ?? '',
      },
      priority: priority,
    );
  }

  /// 看过我的页面展示
  ///
  /// [uid] 用户id
  /// [gender] 性别
  static void reportGameRecordImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_record_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 战绩分享页面，点击分享按钮
  ///
  /// [uid] 用户id
  /// [gender] 性别
  /// [act] inapp/fb/ins/save
  static void reportGameRecordShareClick({
    String? uid,
    String? gender,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_record_share_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 战绩分享页面展示
  ///
  /// [uid] 用户id
  /// [gender] 性别
  static void reportGameRecordShareImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'game_record_share_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
