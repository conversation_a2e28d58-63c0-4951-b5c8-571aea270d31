import 'package:feature_flat_base/feature_flat_base.dart';

class NotifyStatistics {
  NotifyStatistics._();

  /// 本地leave通知检查当天不再设置
  ///
  /// [ignore] 是否忽略
  static void reportCheckLocalLeaveNotifyIgonre({
    String? ignore,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'check_local_leave_notify_igonre',
      params: {
        'ignore': ignore ?? '',
      },
      priority: priority,
    );
  }

  /// 分类型本地通知发送成功
  ///
  /// [type] 设置的通知类型，on_leave/daily
  /// [content] 上报通知内容文案（只上报content部分）
  /// [activeTime] 预设的触发时间
  /// [messageId] 用于追踪通知链路的id
  static void reportLocalNotifySentSucc({
    String? type,
    String? content,
    String? activeTime,
    String? messageId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'local_notify_sent_succ',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'active_time': activeTime ?? '',
        'message_id': messageId ?? '',
      },
      priority: priority,
    );
  }

  /// 分类型本地闹钟配置取消（一种类型设置取消就上报一次）
  ///
  /// [type] 设置的通知类型，on_leave/daily/never
  /// [content] 上报通知内容文案（只上报content部分）
  /// [messageId] 用于追踪通知链路的id
  static void reportLocalNotifySetCancel({
    String? type,
    String? content,
    String? messageId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'local_notify_set_cancel',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'message_id': messageId ?? '',
      },
      priority: priority,
    );
  }

  /// 分类型本地闹钟配置完成（一种类型设置完成就上报一次）
  ///
  /// [type] 设置的通知类型，on_leave/daily
  /// [content] 上报通知内容文案（只上报content部分）
  /// [activeTime] 预设的触发时间
  /// [messageId] 用于追踪通知链路的id
  static void reportLocalNotifySetSucc({
    String? type,
    String? content,
    String? activeTime,
    String? messageId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'local_notify_set_succ',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'active_time': activeTime ?? '',
        'message_id': messageId ?? '',
      },
      priority: priority,
    );
  }

  /// 分类型本地通知点击
  ///
  /// [type] 设置的通知类型，on_leave/daily
  /// [content] 上报通知内容文案（只上报content部分）
  /// [activeTime] 预设的触发时间
  /// [messageId] 用于追踪通知链路的id
  static void reportLocalNotifyUserClick({
    String? type,
    String? content,
    String? activeTime,
    String? messageId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'local_notify_user_click',
      params: {
        'type': type ?? '',
        'content': content ?? '',
        'active_time': activeTime ?? '',
        'message_id': messageId ?? '',
      },
      priority: priority,
    );
  }
}
