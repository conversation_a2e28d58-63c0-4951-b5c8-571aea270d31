import 'package:feature_flat_base/feature_flat_base.dart';

class GiftStatistics {
  GiftStatistics._();

  /// 用户当前魅力值/财富值展示界面曝光
  ///
  /// [gender] 当前查看的用户性别
  /// [content] charisma=魅力值contribute=贡献值
  static void reportAssetdataImp({
    String? gender,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'assetdata_imp',
      params: {
        'gender': gender ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 折扣礼物购买弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报具体的房间类型，后端字段
  static void reportDiscountGiftImp({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'discount_gift_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 折扣礼物弹窗点击购买折扣礼物
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportDiscountGiftPurchase({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'discount_gift_purchase',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 折扣礼物购买成功结果弹窗展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [type] 上报房间类型
  static void reportDiscountGiftPurchaseResult({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'discount_gift_purchase_result',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送头像框礼物面板展示
  ///
  /// [uid]
  /// [gender]
  /// [status] 赠送头像面板出现的场景：im=IM内发送礼物；room=语音房
  /// [roomId]
  /// [toUid]
  /// [toGender]
  static void reportGiftAvatarBoardImp({
    String? uid,
    String? gender,
    String? status,
    String? roomId,
    String? toUid,
    String? toGender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_avatar_board_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击礼物面板上配置的banner
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报对应的礼物id
  /// [roomId]
  /// [type] 返回服务端该礼物类型
  static void reportGiftBannerClick({
    String? uid,
    String? gender,
    String? content,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_banner_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物配置banner展示时
  ///
  /// [uid]
  /// [gender]
  /// [content] 上报对应的礼物id
  /// [roomId]
  static void reportGiftBannerImp({
    String? uid,
    String? gender,
    String? content,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_banner_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送成功或关闭礼物面板，礼物面板停留时长
  ///
  /// [status] 面板关闭时的状态：0=未赠送礼物；1=已赠送礼物
  /// [duration] 时长
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；lovezone=小窝；group=群组
  /// [toGender]
  /// [toUid]
  /// [gender]
  /// [roomId]
  /// [situation] 特殊场景调起，礼物顺序不同；normal/pk/room_pk；常规时段/房内pk时段/跨房pk时段
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftBoardDuration({
    String? status,
    String? duration,
    String? from,
    String? toGender,
    String? toUid,
    String? gender,
    String? roomId,
    String? situation,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_duration',
      params: {
        'status': status ?? '',
        'duration': duration ?? '',
        'from': from ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'situation': situation ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物面板曝光展示，礼物和背包每切换一次上报一次
  ///
  /// [gender] 用户性别
  /// [style] 上报从礼物面板还是背包，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房;lovezone=小窝；group=群组
  /// [toGender]
  /// [toUid]
  /// [roomId]
  /// [situation] 特殊场景调起，礼物顺序不同；normal/pk/room_pk；常规时段/房内pk时段/跨房pk时段
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftBoardImp({
    String? gender,
    String? style,
    String? from,
    String? toGender,
    String? toUid,
    String? roomId,
    String? situation,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_imp',
      params: {
        'gender': gender ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'room_id': roomId ?? '',
        'situation': situation ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 选定礼物数量后上报，完成一次选择上报一次
  ///
  /// [num] 所选的礼物数量
  /// [gender] 用户性别
  /// [toGender] 赠送对方的性别
  /// [toUid] 赠送对方的ID
  /// [content] 当前选中的礼物的ID
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；lovezone=小窝；group=群组
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  /// [style] 上报从礼物面板还是背包发出，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  /// [situation] 特殊场景调起，礼物顺序不同；normal/pk/room_pk；常规时段/房内pk时段/跨房pk时段
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftBoardNum({
    String? num,
    String? gender,
    String? toGender,
    String? toUid,
    String? content,
    String? from,
    String? status,
    String? roomId,
    String? style,
    String? situation,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_num',
      params: {
        'num': num ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'style': style ?? '',
        'situation': situation ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物面板中单个礼物商品曝光时，每次礼物面板展示时同一个礼物展示只上报一次（例如用户本次打开礼物面板后不断切tab，某个礼物展示了多次只记录一次，若本次面板展示了3个不同的礼物，则每一个礼物单独报一次展示）
  ///
  /// [gender]
  /// [toGender]
  /// [toUid]
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；group=群组
  /// [content] 展示的礼物ID
  /// [roomId]
  /// [style] 上报从礼物面板还是背包发出，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  static void reportGiftBoardProductImp({
    String? gender,
    String? toGender,
    String? toUid,
    String? from,
    String? content,
    String? roomId,
    String? style,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_product_imp',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'from': from ?? '',
        'content': content ?? '',
        'room_id': roomId ?? '',
        'style': style ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物面板，选中单个礼物时
  ///
  /// [content] 上报该次发送的礼物id
  /// [gender]
  /// [toGender]
  /// [toUid]
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [cost] 当次送礼所消耗的金币数/钻石数
  /// [style] 上报从礼物面板还是背包发出，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；group=群组
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  static void reportGiftBoardProductSelect({
    String? content,
    String? gender,
    String? toGender,
    String? toUid,
    String? type,
    String? cost,
    String? style,
    String? from,
    String? status,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_product_select',
      params: {
        'content': content ?? '',
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'cost': cost ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物面板接口请求结束
  ///
  /// [page] 接口内容：shop 商店backpack 背包
  /// [result] succ/fail
  /// [reason] 失败原因
  /// [onTime] 得到结果耗时：单位毫秒
  /// [gender] 用户性别
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；group=群组
  static void reportGiftBoardRequestEnd({
    String? page,
    String? result,
    String? reason,
    String? onTime,
    String? gender,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_request_end',
      params: {
        'page': page ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物面板接口请求
  ///
  /// [page] 接口内容：shop 商店backpack 背包
  /// [gender] 用户性别
  static void reportGiftBoardRequestStart({
    String? page,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_request_start',
      params: {
        'page': page ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 点击礼物面板上的赠送按钮
  ///
  /// [num] 上报该次礼物发送的数量
  /// [content] 上报该次发送的礼物id
  /// [gender] 用户性别
  /// [toUid] 对方id
  /// [toGender] 对方性别
  /// [cost] 当次送礼所消耗的金币数/钻石数
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [style] 上报从礼物面板还是背包发出，0/1/2/3表示，0代表背包，1代表礼物面板，2代表头像框面板，3代表房间送礼引导弹窗
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；lovezone=小窝；group=群组；giftback=公屏回礼
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [act] 区分是连击赠送还是礼物面板赠送，combo=连击赠送，board=点击赠送
  /// [roomId]
  /// [situation] 特殊场景调起，礼物顺序不同；normal/pk/room_pk；常规时段/房内pk时段/跨房pk时段
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftBoardSend({
    String? num,
    String? content,
    String? gender,
    String? toUid,
    String? toGender,
    String? cost,
    String? type,
    String? style,
    String? from,
    String? status,
    String? act,
    String? roomId,
    String? situation,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_board_send',
      params: {
        'num': num ?? '',
        'content': content ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'cost': cost ?? '',
        'type': type ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'act': act ?? '',
        'room_id': roomId ?? '',
        'situation': situation ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 充值礼包入口点击
  ///
  /// [from] 上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起，discountgift=房间里折扣礼包入口
  /// [type] 上报礼包类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，discountgift=折扣礼物，
  /// [uid]
  /// [gender]
  static void reportGiftBonusEntranceClick({
    String? from,
    String? type,
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_bonus_entrance_click',
      params: {
        'from': from ?? '',
        'type': type ?? '',
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 充值礼包入口展示
  ///
  /// [uid]
  /// [gender]
  /// [from] 上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起，discountgift=房间里折扣礼包入口
  /// [type] 上报礼包类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，discountgift=折扣礼物
  static void reportGiftBonusEntranceImp({
    String? uid,
    String? gender,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_bonus_entrance_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 充值礼包弹窗点击充值
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报礼包类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，discountgift=折扣礼物，bigbonus=大额充值弹窗
  /// [from] 上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起，bet=菠菜关闭，mall=离开商城，vip=关闭vip页面，giftclose=关闭礼物面板时，rechargeclose=未充值成功时
  static void reportGiftBonusPopoutClick({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_bonus_popout_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 充值礼包弹窗展示
  ///
  /// [uid]
  /// [gender]
  /// [type] 上报礼包类型，first=新手礼包，silence=沉默礼包，broke=破产礼包，discountgift=折扣礼物，bigbonus=大额充值弹窗
  /// [from] 上报触发来源，giftboard=礼物面板手动点击，auto=充值页自动弹出，rechargepage=全屏充值页手动拉起，halfpage=半屏充值页手动拉起，bet=菠菜关闭，mall=离开商城，vip=关闭vip页面，giftclose=关闭礼物面板时，rechargeclose=未充值成功时
  static void reportGiftBonusPopoutImp({
    String? uid,
    String? gender,
    String? type,
    String? from,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_bonus_popout_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'from': from ?? '',
      },
      priority: priority,
    );
  }

  /// 点击IM对话中出现的礼物引导
  ///
  /// [uid]
  /// [gender]
  static void reportGiftImGuideClick({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_im_guide_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// IM中出现礼物引导曝光
  ///
  /// [uid]
  /// [gender]
  static void reportGiftImGuideImp({
    String? uid,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_im_guide_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 发送礼物货币不足时弹窗提示
  ///
  /// [cost] 当次花费的礼物货币总额
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [content] 当前礼物id
  /// [gender]
  /// [toUid]
  /// [toGender]
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；group=群组
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  /// [style] 上报从礼物面板还是背包发出，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftNomoneyPopout({
    String? cost,
    String? type,
    String? content,
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    String? status,
    String? roomId,
    String? style,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_nomoney_popout',
      params: {
        'cost': cost ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'style': style ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 离线礼物获取提示弹窗
  ///
  /// [num] 上报获得的礼物数量
  /// [gender] 用户性别
  static void reportGiftNotifyPopout({
    String? num,
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_notify_popout',
      params: {
        'num': num ?? '',
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物播放上报
  ///
  /// [uid] 播放方用户id
  /// [gender] 播放方用户性别
  /// [type] auto/manual，自动播放还是手动点击播放。
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  static void reportGiftPlay({
    String? uid,
    String? gender,
    String? type,
    String? roomId,
    String? roomType,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_play',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'type': type ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
      },
      priority: priority,
    );
  }

  /// 发送礼物二次确认弹窗展示时
  ///
  /// [gender]
  /// [toGender]
  /// [toUid]
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [cost] 当次花费的礼物货币总额
  /// [content] 当前礼物的id
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话
  /// [roomId]
  static void reportGiftSendConfirmPopoutImp({
    String? gender,
    String? toGender,
    String? toUid,
    String? type,
    String? cost,
    String? content,
    String? from,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_send_confirm_popout_imp',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'type': type ?? '',
        'cost': cost ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 发送礼物二次确认弹窗-确认发送
  ///
  /// [cost] 当次花费的礼物货币总额
  /// [gender] 用户性别
  /// [toUid] 对方id
  /// [toGender] 对方性别
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [content] 当前礼物的id
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  static void reportGiftSendConfirmPopoutSend({
    String? cost,
    String? gender,
    String? toUid,
    String? toGender,
    String? type,
    String? content,
    String? from,
    String? status,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_send_confirm_popout_send',
      params: {
        'cost': cost ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'type': type ?? '',
        'content': content ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送礼物开始请求借口时
  ///
  /// [page] 请求借口内容：buy_send 购买send 发送
  /// [gender] 用户性别
  /// [num] 上报该次礼物发送的数量
  /// [content] 上报该次发送的礼物
  /// [toUid]
  /// [toGender]
  /// [cost] 当次送礼所消耗的金币数/钻石数
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [style] 上报从礼物面板还是背包发出，0/1/2/3表示，0代表背包，1代表礼物面板，2代表头像框面板，3代表房间送礼引导弹窗
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；lovezone=小窝；group=群组giftback=公屏回礼
  /// [result] succ/fail
  /// [reason] 失败原因
  /// [onTime] 得到结果耗时：单位毫秒
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  /// [mode] 房间模板//普通聊天：chat；真心话：truth_dare
  static void reportGiftSendRequestEnd({
    String? page,
    String? gender,
    String? num,
    String? content,
    String? toUid,
    String? toGender,
    String? cost,
    String? type,
    String? style,
    String? from,
    String? result,
    String? reason,
    String? onTime,
    String? status,
    String? roomId,
    String? roomType,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_send_request_end',
      params: {
        'page': page ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'cost': cost ?? '',
        'type': type ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'on_time': onTime ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 赠送礼物开始请求借口时
  ///
  /// [page] 请求借口内容：buy_send 购买send 发送
  /// [gender] 用户性别
  /// [num] 上报该次礼物发送的数量
  /// [content] 上报该次发送的礼物
  /// [toUid]
  /// [toGender]
  /// [cost] 当次送礼所消耗的金币数/钻石数
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [style] 上报从礼物面板还是背包发出，0/1表示，0代表背包，1代表礼物面板，2代表头像框面板
  /// [from] 礼物面板出现的场景：unlock_im=非匹配场景解锁对话；im=IM内发送礼物；unlock_high_im=非匹配场景高价值礼物解锁对话；room=语音房；lovezone=小窝；group=群组
  /// [status] mic=送全麦，room=送全房，single=指定某一人
  /// [roomId]
  /// [roomType] live=直播房间，voice=语音房房间
  /// [mode] 房间模板// 普通聊天：chat；真心话：truth_dare
  static void reportGiftSendRequestStart({
    String? page,
    String? gender,
    String? num,
    String? content,
    String? toUid,
    String? toGender,
    String? cost,
    String? type,
    String? style,
    String? from,
    String? status,
    String? roomId,
    String? roomType,
    String? mode,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_send_request_start',
      params: {
        'page': page ?? '',
        'gender': gender ?? '',
        'num': num ?? '',
        'content': content ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'cost': cost ?? '',
        'type': type ?? '',
        'style': style ?? '',
        'from': from ?? '',
        'status': status ?? '',
        'room_id': roomId ?? '',
        'room_type': roomType ?? '',
        'mode': mode ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物-退出送礼解锁聊天界面
  ///
  /// [gender] 用户性别
  /// [toUid]
  /// [toGender]
  /// [from] moment动态/home_reco首页用户推荐/room语音房/ranking 榜单/search 搜索/；，通过什么方式开启的对话
  /// [type] 当前面板是高价值解锁还是低价值，0=低价值，1=高价值
  static void reportGiftUnlockExit({
    String? gender,
    String? toUid,
    String? toGender,
    String? from,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_unlock_exit',
      params: {
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'from': from ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 礼物墙页展示
  ///
  /// [gender] 当前查看的用户性别
  /// [toGender] 对方用户性别，查看自己的则不上报
  /// [toUid] 对方用户ID，查看自己的则不上报
  /// [content] 展示的礼物墙内容：sent=送出的礼物received=收到的礼物
  static void reportGiftWallImp({
    String? gender,
    String? toGender,
    String? toUid,
    String? content,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'gift_wall_imp',
      params: {
        'gender': gender ?? '',
        'to_gender': toGender ?? '',
        'to_uid': toUid ?? '',
        'content': content ?? '',
      },
      priority: priority,
    );
  }

  /// 商城赠送接口请求结束时
  ///
  /// [uid]
  /// [gender]
  /// [result] succ/ fail
  /// [reason] 失败原因错误码
  /// [content] 上报赠送的商品id
  /// [type] 上报当前所送礼物类型（分金币型和钻石型），golds/diamond
  /// [cost] 当次送礼所消耗的金币数/钻石数
  /// [onTime] 得到结果耗时：单位毫秒
  /// [from] 展示商城物品从何处送出，room=房间内，im=私聊内，store=商城
  /// [couponId] 有选择优惠券时上报优惠券id，没有优惠券选择不上报
  static void reportMallGiftListSendEnd({
    String? uid,
    String? gender,
    String? result,
    String? reason,
    String? content,
    String? type,
    String? cost,
    String? onTime,
    String? from,
    String? couponId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'mall_gift_list_send_end',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'result': result ?? '',
        'reason': reason ?? '',
        'content': content ?? '',
        'type': type ?? '',
        'cost': cost ?? '',
        'on_time': onTime ?? '',
        'from': from ?? '',
        'coupon_id': couponId ?? '',
      },
      priority: priority,
    );
  }

  /// 消息列表-点击礼物入口
  ///
  /// [matchType] qmatch/other；通过什么方式匹配的用户
  /// [isOnline] 0/1；聊天对象在线状态，0不在线，1在线
  /// [toUid] 聊天用户的ID
  /// [toGender] 聊天用户的性别
  /// [isFollowing] 0/1；是否关注了对方，0未关注对方，1已关注对方
  /// [isFollowed] 0/1；是否被对方关注，0未被关注，1已被关注
  /// [micAuth] 0/1；点击时是否有麦克风权限，0无权限，1有权限
  static void reportMsgListGift({
    String? matchType,
    String? isOnline,
    String? toUid,
    String? toGender,
    String? isFollowing,
    String? isFollowed,
    String? micAuth,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'msg_list_gift',
      params: {
        'match_type': matchType ?? '',
        'is_online': isOnline ?? '',
        'to_uid': toUid ?? '',
        'to_gender': toGender ?? '',
        'is_following': isFollowing ?? '',
        'is_followed': isFollowed ?? '',
        'mic_auth': micAuth ?? '',
      },
      priority: priority,
    );
  }

  /// 收礼魅力值增长提示弹窗出现
  ///
  /// [gender] 用户性别
  static void reportValueCharismaNotifyImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'value_charisma_notify_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 送礼财富值增长提示弹窗出现
  ///
  /// [gender] 用户性别
  static void reportValueContributeNotifyImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'value_contribute_notify_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }

  /// 金币详情页展示
  ///
  /// [gender] 用户性别
  static void reportWalletGoldsImp({
    String? gender,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'wallet_golds_imp',
      params: {
        'gender': gender ?? '',
      },
      priority: priority,
    );
  }
}
