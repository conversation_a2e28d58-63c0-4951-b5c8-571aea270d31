import 'package:feature_flat_base/feature_flat_base.dart';

class TalentStatistics {
  TalentStatistics._();

  /// 成功通过申请上选手位消息时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [toUid] 申请者uid
  /// [identity] 操作者身份；host/owner/admin；主持人/房主/房管
  static void reportTalentApplyAgree({
    String? uid,
    String? gender,
    String? roomId,
    String? toUid,
    String? identity,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_apply_agree',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'to_uid': toUid ?? '',
        'identity': identity ?? '',
      },
      priority: priority,
    );
  }

  /// 清空申请者列表时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [identity] 操作者身份；host/owner/admin；主持人/房主/房管
  static void reportTalentApplyClear({
    String? uid,
    String? gender,
    String? roomId,
    String? identity,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_apply_clear',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'identity': identity ?? '',
      },
      priority: priority,
    );
  }

  /// 点击爆灯按钮时
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被赠送者uid
  /// [roomId]
  /// [identity] 操作者身份；user/guest；除评委外其余用户/评委
  static void reportTalentBoomClick({
    String? uid,
    String? gender,
    String? toUid,
    String? roomId,
    String? identity,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_boom_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'room_id': roomId ?? '',
        'identity': identity ?? '',
      },
      priority: priority,
    );
  }

  /// 清空累计计分板时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [identity] 操作者身份；host/owner/admin；主持人/房主/房管
  static void reportTalentClearPoint({
    String? uid,
    String? gender,
    String? roomId,
    String? identity,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_clear_point',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'identity': identity ?? '',
      },
      priority: priority,
    );
  }

  /// 创建才艺房
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentEntranceClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_entrance_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 才艺房faq曝光时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentFaqImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_faq_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 选手抢麦成功时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentGrabClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_grab_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 进入才艺房，界面展示时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 选手名单曝光时上报
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [identity] 操作者身份；host/owner/admin/user；主持人/房主/房管/其余用户
  /// [tab] 分页；performer/apply；选手名单/申请上选手位名单
  static void reportTalentListImp({
    String? uid,
    String? gender,
    String? roomId,
    String? identity,
    String? tab,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_list_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'identity': identity ?? '',
        'tab': tab ?? '',
      },
      priority: priority,
    );
  }

  /// 点击操控台流程控制按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [identity] 操作者身份；host/owner/admin；主持人/房主/房管
  /// [type] 玩法模式；grab/specify；抢麦模式/指定表演者模式
  /// [time] 表演时间；多少时长就报多少，数值为秒
  /// [performerUid] 指定表演者uid
  /// [act] 按钮；start/skip/end；开始（或再次开始）/跳过/结束
  static void reportTalentOperationClick({
    String? uid,
    String? gender,
    String? roomId,
    String? identity,
    String? type,
    String? time,
    String? performerUid,
    String? act,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_operation_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'identity': identity ?? '',
        'type': type ?? '',
        'time': time ?? '',
        'performer_uid': performerUid ?? '',
        'act': act ?? '',
      },
      priority: priority,
    );
  }

  /// 用户进入选手名单时上报
  ///
  /// [uid] 上选手位用户uid
  /// [gender]
  /// [roomId]
  /// [type] 进入方式；invite/apply；通过邀请/申请通过
  static void reportTalentPerformerListIn({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_performer_list_in',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 用户离开选手名单时上报
  ///
  /// [uid] 上选手位用户uid
  /// [gender]
  /// [roomId]
  /// [type] 离开方式；leave/kick/other；主动离开/被踢出/其他方式
  static void reportTalentPerformerListOut({
    String? uid,
    String? gender,
    String? roomId,
    String? type,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_performer_list_out',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'type': type ?? '',
      },
      priority: priority,
    );
  }

  /// 累计计分板曝光时
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentPointImp({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_point_imp',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房主或房管点击选手座位
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [number] 选手位序号
  static void reportTalentSeatClick({
    String? uid,
    String? gender,
    String? roomId,
    String? number,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_seat_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'number': number ?? '',
      },
      priority: priority,
    );
  }

  /// 点击快捷送礼按钮时
  ///
  /// [uid]
  /// [gender]
  /// [toUid] 被赠送者uid
  /// [roomId]
  /// [giftId]
  /// [status] 送出状态；succ/charge；成功送出/调起充值面板
  static void reportTalentSendFastGift({
    String? uid,
    String? gender,
    String? toUid,
    String? roomId,
    String? giftId,
    String? status,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_send_fast_gift',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'to_uid': toUid ?? '',
        'room_id': roomId ?? '',
        'gift_id': giftId ?? '',
        'status': status ?? '',
      },
      priority: priority,
    );
  }

  /// 成功从其他房间模式切换成才艺房
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  static void reportTalentSwitchClick({
    String? uid,
    String? gender,
    String? roomId,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_switch_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
      },
      priority: priority,
    );
  }

  /// 房管或房主点击选手作为后，再点击上位按钮
  ///
  /// [uid]
  /// [gender]
  /// [roomId]
  /// [number] 选手位序号
  static void reportTalentTakeSeatClick({
    String? uid,
    String? gender,
    String? roomId,
    String? number,
    StatisticsPriority priority = StatisticsPriority.normal,
  }) {
    getService<AbsStatisticsService>()?.report(
      'talent_take_seat_click',
      params: {
        'uid': uid ?? '',
        'gender': gender ?? '',
        'room_id': roomId ?? '',
        'number': number ?? '',
      },
      priority: priority,
    );
  }
}
