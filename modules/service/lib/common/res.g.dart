/// 该类代码由ResourceScanner脚本自动生成
///
/// 请勿手动修改！！！
class Res {
  Res._();
  
  static const String agencyAgencyDefault = 'res/agency/agency_default.webp';
  static const String broadcastBgBcGame = 'res/broadcast/bg_bc_game.png';
  static const String broadcastBgBcLuckyBag = 'res/broadcast/bg_bc_lucky_bag.webp';
  static const String broadcastBgGift = 'res/broadcast/bg_gift.png';
  static const String broadcastBgRedBag = 'res/broadcast/bg_red_bag.png';
  static const String broadcastIcBcLuckyBagFrame = 'res/broadcast/ic_bc_lucky_bag_frame.webp';
  static const String chatAdd = 'res/chat/add.png';
  static const String chatAddSticker = 'res/chat/add_sticker.png';
  static const String chatAllFriendType = 'res/chat/all_friend_type.png';
  static const String chatArrowDown = 'res/chat/arrow_down.webp';
  static const String chatArrowDownNext = 'res/chat/arrow_down_next.webp';
  static const String chatAudio = 'res/chat/audio.png';
  static const String chatBeautifulLiveId = 'res/chat/beautiful_live_id.webp';
  static const String chatBgGiftGuide = 'res/chat/bg_gift_guide.png';
  static const String chatBgMagicBox = 'res/chat/bg_magic_box.webp';
  static const String chatBgMagicBoxBonus = 'res/chat/bg_magic_box_bonus.webp';
  static const String chatBgMagicBoxOpen = 'res/chat/bg_magic_box_open.webp';
  static const String chatBgMagicBoxOpened = 'res/chat/bg_magic_box_opened.webp';
  static const String chatBgMagicBoxTaskBottom = 'res/chat/bg_magic_box_task_bottom.webp';
  static const String chatBgMagicBoxTaskTop = 'res/chat/bg_magic_box_task_top.webp';
  static const String chatBgMagicRequestKey = 'res/chat/bg_magic_request_key.webp';
  static const String chatBgPage = 'res/chat/bg_page.png';
  static const String chatBgRewardMsgItem = 'res/chat/bg_reward_msg_item.webp';
  static const String chatBgRewardMsgLeftTime = 'res/chat/bg_reward_msg_left_time.webp';
  static const String chatBgSendGiftGuide = 'res/chat/bg_send_gift_guide.webp';
  static const String chatBlindboxRight = 'res/chat/blindbox_right.webp';
  static const String chatCallPopup = 'res/chat/call_popup.png';
  static const String chatChatBarEmoji = 'res/chat/chat_bar_emoji.png';
  static const String chatChatBarFeatureLock = 'res/chat/chat_bar_feature_lock.webp';
  static const String chatChatBarGift = 'res/chat/chat_bar_gift.webp';
  static const String chatChatBarPhoto = 'res/chat/chat_bar_photo.png';
  static const String chatChatBarPunchOff = 'res/chat/chat_bar_punch_off.webp';
  static const String chatChatBarPunchOn = 'res/chat/chat_bar_punch_on.webp';
  static const String chatChatBarSend = 'res/chat/chat_bar_send.webp';
  static const String chatChatBarVoice = 'res/chat/chat_bar_voice.png';
  static const String chatChatDetailFollowFamale = 'res/chat/chat_detail_follow_famale.webp';
  static const String chatChatDetailFollowMale = 'res/chat/chat_detail_follow_male.webp';
  static const String chatChatDetailRead = 'res/chat/chat_detail_read.webp';
  static const String chatChatDetailUnread = 'res/chat/chat_detail_unread.webp';
  static const String chatChatFamilyTag = 'res/chat/chat_family_tag.webp';
  static const String chatChatFollowIcon = 'res/chat/chat_follow_icon.webp';
  static const String chatChatQuickPunch = 'res/chat/chat_quick_punch.webp';
  static const String chatChatQuickWink = 'res/chat/chat_quick_wink.webp';
  static const String chatChatRight = 'res/chat/chat_right.png';
  static const String chatChatVideoDestructLeft = 'res/chat/chat_video_destruct_left.webp';
  static const String chatChatVideoDestructRight = 'res/chat/chat_video_destruct_right.webp';
  static const String chatCopy = 'res/chat/copy.webp';
  static const String chatCpMatchSucc = 'res/chat/cp_match_succ.webp';
  static const String chatFailed = 'res/chat/failed.png';
  static const String chatFiredLeft = 'res/chat/fired_left.webp';
  static const String chatFiredRight = 'res/chat/fired_right.webp';
  static const String chatFollow = 'res/chat/follow.png';
  static const String chatFree = 'res/chat/free.webp';
  static const String chatFreeModeImg = 'res/chat/free_mode_img.webp';
  static const String chatFriendTypeTick = 'res/chat/friend_type_tick.png';
  static const String chatGuideLine = 'res/chat/guide_line.webp';
  static const String chatHearting2 = 'res/chat/hearting2.png';
  static const String chatHidden = 'res/chat/hidden.webp';
  static const String chatHide = 'res/chat/hide.png';
  static const String chatIcIncomeTask = 'res/chat/ic_income_task.webp';
  static const String chatIcRechange = 'res/chat/ic_rechange.webp';
  static const String chatIcebreakMsgSendIcon = 'res/chat/icebreak_msg_send_icon.svg';
  static const String chatIconAll = 'res/chat/icon_all.webp';
  static const String chatIconAm = 'res/chat/icon_am.webp';
  static const String chatIconAmEntrance = 'res/chat/icon_am_entrance.webp';
  static const String chatIconAnniversaryDelete = 'res/chat/icon_anniversary_delete.webp';
  static const String chatIconCircularLoading = 'res/chat/icon_circular_loading.webp';
  static const String chatIconContactsRightArrow = 'res/chat/icon_contacts_right_arrow.webp';
  static const String chatIconExpand = 'res/chat/icon_expand.png';
  static const String chatIconFollowAdd = 'res/chat/icon_follow_add.webp';
  static const String chatIconFollowGuide = 'res/chat/icon_follow_guide.webp';
  static const String chatIconFreeGift = 'res/chat/icon_free_gift.webp';
  static const String chatIconFriendRequest = 'res/chat/icon_friend_request.webp';
  static const String chatIconGroupAnnounce = 'res/chat/icon_group_announce.webp';
  static const String chatIconGuideArrow = 'res/chat/icon_guide_arrow.webp';
  static const String chatIconIceAbout = 'res/chat/icon_ice_about.webp';
  static const String chatIconIceLocation = 'res/chat/icon_ice_location.webp';
  static const String chatIconMagicBoxEntrance = 'res/chat/icon_magic_box_entrance.webp';
  static const String chatIconMagicBoxKey = 'res/chat/icon_magic_box_key.webp';
  static const String chatIconRule = 'res/chat/icon_rule.webp';
  static const String chatIconSayHi = 'res/chat/icon_say_hi.webp';
  static const String chatIconSelectAll = 'res/chat/icon_select_all.webp';
  static const String chatIconSetting = 'res/chat/icon_setting.png';
  static const String chatIconThemeSelect = 'res/chat/icon_theme_select.webp';
  static const String chatIconTispCoin = 'res/chat/icon_tisp_coin.webp';
  static const String chatIconTispDiamond = 'res/chat/icon_tisp_diamond.webp';
  static const String chatIconTispIntimacy = 'res/chat/icon_tisp_intimacy.webp';
  static const String chatImgAmTitle = 'res/chat/img_am_title.webp';
  static const String chatImgGuideFate = 'res/chat/img_guide_fate.webp';
  static const String chatImgGuideLine = 'res/chat/img_guide_line.webp';
  static const String chatImgGuideMsg = 'res/chat/img_guide_msg.webp';
  static const String chatIntimacyImMsg = 'res/chat/intimacy_im_msg.webp';
  static const String chatIntimacyInfoBg = 'res/chat/intimacy_info_bg.webp';
  static const String chatIntimacyInfoLove = 'res/chat/intimacy_info_love.webp';
  static const String chatIntimacyLock = 'res/chat/intimacy_lock.webp';
  static const String chatIntimacyLove = 'res/chat/intimacy_love.webp';
  static const String chatIntimacyLoveSmall = 'res/chat/intimacy_love_small.webp';
  static const String chatLeave = 'res/chat/leave.webp';
  static const String chatLock = 'res/chat/lock.png';
  static const String chatMsgItemPunchBg = 'res/chat/msg_item_punch_bg.webp';
  static const String chatMsgTypeMore = 'res/chat/msg_type_more.webp';
  static const String chatMsgWelcome = 'res/chat/msg_welcome.webp';
  static const String chatMute = 'res/chat/mute.png';
  static const String chatMyFriendType = 'res/chat/my_friend_type.webp';
  static const String chatPicLeft = 'res/chat/pic_left.webp';
  static const String chatPicRight = 'res/chat/pic_right.webp';
  static const String chatPlayGift = 'res/chat/play_gift.png';
  static const String chatPolice = 'res/chat/police.webp';
  static const String chatPunchHand = 'res/chat/punch_hand.webp';
  static const String chatPunchLeftBu = 'res/chat/punch_left_bu.webp';
  static const String chatPunchLeftJiandao = 'res/chat/punch_left_jiandao.webp';
  static const String chatPunchLeftQuan = 'res/chat/punch_left_quan.webp';
  static const String chatPunchRightBu = 'res/chat/punch_right_bu.webp';
  static const String chatPunchRightJiandao = 'res/chat/punch_right_jiandao.webp';
  static const String chatPunchRightQuan = 'res/chat/punch_right_quan.webp';
  static const String chatReply = 'res/chat/reply.webp';
  static const String chatReport = 'res/chat/report.webp';
  static const String chatRewardMsgBorder = 'res/chat/reward_msg_border.webp';
  static const String chatRoomCpSuccess = 'res/chat/room_cp_success.webp';
  static const String chatSafe = 'res/chat/safe.webp';
  static const String chatSafeModeImg = 'res/chat/safe_mode_img.webp';
  static const String chatScreenshot = 'res/chat/screenshot.webp';
  static const String chatSearchBlack = 'res/chat/search_black.png';
  static const String chatSector = 'res/chat/sector.png';
  static const String chatSendGift = 'res/chat/send_gift.png';
  static const String chatSendWhite = 'res/chat/send_white.webp';
  static const String chatSendWink = 'res/chat/send_wink.gif';
  static const String chatSensitiveAlertHeader = 'res/chat/sensitive_alert_header.webp';
  static const String chatStarlight = 'res/chat/starlight.webp';
  static const String chatStarlightGreetFemaleIcon = 'res/chat/starlight_greet_female_icon.svg';
  static const String chatStickerAdd = 'res/chat/sticker_add.png';
  static const String chatStickerDelete = 'res/chat/sticker_delete.webp';
  static const String chatStickerDeleteDisable = 'res/chat/sticker_delete_disable.png';
  static const String chatStickerEmoji = 'res/chat/sticker_emoji.png';
  static const String chatStickerFavorite = 'res/chat/sticker_favorite.png';
  static const String chatUncoverIdentity = 'res/chat/uncover_identity.webp';
  static const String chatUncoverSuc = 'res/chat/uncover_suc.png';
  static const String chatUp = 'res/chat/up.png';
  static const String chatVideo = 'res/chat/video.png';
  static const String chatVocieImg = 'res/chat/vocie_img.webp';
  static const String chatVoice = 'res/chat/voice.png';
  static const String chatWait = 'res/chat/wait.webp';
  static const String cocosGameGamePk = 'res/cocos_game/game_pk.webp';
  static const String commonAboutQuotes = 'res/common/about_quotes.webp';
  static const String commonAdd = 'res/common/add.png';
  static const String commonAdd2 = 'res/common/add2.webp';
  static const String commonAdd3 = 'res/common/add3.webp';
  static const String commonAdd4 = 'res/common/add4.webp';
  static const String commonAppName = 'res/common/app_name.webp';
  static const String commonAppNameWhite = 'res/common/app_name_white.webp';
  static const String commonArrowDown = 'res/common/arrow_down.png';
  static const String commonArrowForward = 'res/common/arrow_forward.webp';
  static const String commonArrowForward2 = 'res/common/arrow_forward2.webp';
  static const String commonArrowForwardW1H2 = 'res/common/arrow_forward_w1_h2.svg';
  static const String commonArrowRight = 'res/common/arrow_right.webp';
  static const String commonArrowRightSvg = 'res/common/arrow_right_svg.svg';
  static const String commonArrowRightWhite = 'res/common/arrow_right_white.webp';
  static const String commonAssetsArrow = 'res/common/assets_arrow.png';
  static const String commonAssistanceVerifyMar = 'res/common/assistance_verify_mar.webp';
  static const String commonAttention = 'res/common/attention.webp';
  static const String commonBack = 'res/common/back.webp';
  static const String commonBack0 = 'res/common/back0.webp';
  static const String commonBack2 = 'res/common/back2.webp';
  static const String commonBackIconCircle = 'res/common/back_icon_circle.svg';
  static const String commonBanner = 'res/common/banner.webp';
  static const String commonBeautifulId = 'res/common/beautiful_id.webp';
  static const String commonBgAvatar = 'res/common/bg_avatar.png';
  static const String commonBgHashtagDetailsHeader = 'res/common/bg_hashtag_details_header.webp';
  static const String commonBgVerifyDialogFemale = 'res/common/bg_verify_dialog_female.png';
  static const String commonBgVerifyDialogMale = 'res/common/bg_verify_dialog_male.png';
  static const String commonBgVoiceVerifyGuide = 'res/common/bg_voice_verify_guide.webp';
  static const String commonCameraReport = 'res/common/camera_report.png';
  static const String commonCheckNormal = 'res/common/check_normal.webp';
  static const String commonCheckSelected = 'res/common/check_selected.webp';
  static const String commonChecked = 'res/common/checked.png';
  static const String commonChecked2 = 'res/common/checked2.webp';
  static const String commonChecked3 = 'res/common/checked3.webp';
  static const String commonCheckedSvg = 'res/common/checked_svg.svg';
  static const String commonClock = 'res/common/clock.png';
  static const String commonClock2 = 'res/common/clock2.webp';
  static const String commonClose = 'res/common/close.png';
  static const String commonClose10 = 'res/common/close10.png';
  static const String commonClose11 = 'res/common/close11.png';
  static const String commonClose12 = 'res/common/close12.png';
  static const String commonClose13 = 'res/common/close13.webp';
  static const String commonClose3 = 'res/common/close3.png';
  static const String commonClose4 = 'res/common/close4.webp';
  static const String commonClose5 = 'res/common/close5.png';
  static const String commonClose7 = 'res/common/close7.webp';
  static const String commonClose8 = 'res/common/close8.png';
  static const String commonClose9 = 'res/common/close9.png';
  static const String commonCloseGrey = 'res/common/close_grey.webp';
  static const String commonCloseSearch = 'res/common/close_search.webp';
  static const String commonCloseSvg = 'res/common/close_svg.svg';
  static const String commonColorfulTextLogo = 'res/common/colorful_text_logo.webp';
  static const String commonCopy2 = 'res/common/copy2.webp';
  static const String commonDelete = 'res/common/delete.webp';
  static const String commonDelete2 = 'res/common/delete2.webp';
  static const String commonDelete3 = 'res/common/delete3.png';
  static const String commonDelete4 = 'res/common/delete4.png';
  static const String commonDelete5 = 'res/common/delete5.webp';
  static const String commonDown = 'res/common/down.webp';
  static const String commonEmoji = 'res/common/emoji.png';
  static const String commonExit = 'res/common/exit.webp';
  static const String commonFollow = 'res/common/follow.webp';
  static const String commonFollowBlue = 'res/common/follow_blue.webp';
  static const String commonFollowed = 'res/common/followed.webp';
  static const String commonGift = 'res/common/gift.webp';
  static const String commonHelp = 'res/common/help.png';
  static const String commonHelp2 = 'res/common/help2.webp';
  static const String commonHelp3 = 'res/common/help3.webp';
  static const String commonHelpSvg = 'res/common/help_svg.svg';
  static const String commonIcCheckNo = 'res/common/ic_check_no.svg';
  static const String commonIcChecked = 'res/common/ic_checked.svg';
  static const String commonIcSelectRight = 'res/common/ic_select_right.webp';
  static const String commonIconCamera = 'res/common/icon_camera.webp';
  static const String commonIconJoin = 'res/common/icon_join.webp';
  static const String commonIconsAllRight = 'res/common/icons_all_right.png';
  static const String commonImNewMsg = 'res/common/im_new_msg.webp';
  static const String commonImVerifyAnd = 'res/common/im_verify_and.webp';
  static const String commonImgFemale = 'res/common/img_female.webp';
  static const String commonImgMale = 'res/common/img_male.png';
  static const String commonLiveVerifyTop = 'res/common/live_verify_top.webp';
  static const String commonLoading = 'res/common/loading.png';
  static const String commonLocation = 'res/common/location.webp';
  static const String commonLogo = 'res/common/logo.webp';
  static const String commonLogo3d = 'res/common/logo_3d.webp';
  static const String commonLogoFacebook = 'res/common/logo_facebook.webp';
  static const String commonLogoGoogle = 'res/common/logo_google.webp';
  static const String commonLogoIns = 'res/common/logo_ins.webp';
  static const String commonLogoWhatsapp = 'res/common/logo_whatsapp.webp';
  static const String commonMoment = 'res/common/moment.png';
  static const String commonMomentTag = 'res/common/moment_tag.webp';
  static const String commonMore = 'res/common/more.png';
  static const String commonMore2 = 'res/common/more2.png';
  static const String commonMore3 = 'res/common/more3.png';
  static const String commonMore4 = 'res/common/more4.png';
  static const String commonMore5 = 'res/common/more5.png';
  static const String commonMore6 = 'res/common/more6.webp';
  static const String commonNotification = 'res/common/notification.png';
  static const String commonNotification2 = 'res/common/notification2.webp';
  static const String commonPause = 'res/common/pause.webp';
  static const String commonPause2 = 'res/common/pause2.png';
  static const String commonPlaint = 'res/common/plaint.svg';
  static const String commonPlay = 'res/common/play.webp';
  static const String commonPlay2 = 'res/common/play2.png';
  static const String commonRecommendValue = 'res/common/recommend_value.webp';
  static const String commonRecord2 = 'res/common/record2.webp';
  static const String commonRedDot = 'res/common/red_dot.webp';
  static const String commonRefresh = 'res/common/refresh.png';
  static const String commonRefresh2 = 'res/common/refresh2.png';
  static const String commonRefreshWhite = 'res/common/refresh_white.webp';
  static const String commonReport3 = 'res/common/report3.webp';
  static const String commonRight2 = 'res/common/right2.webp';
  static const String commonRight3 = 'res/common/right3.png';
  static const String commonRoomRankCup = 'res/common/room_rank_cup.webp';
  static const String commonSafe = 'res/common/safe.png';
  static const String commonSearch = 'res/common/search.png';
  static const String commonSelected = 'res/common/selected.png';
  static const String commonShare = 'res/common/share.png';
  static const String commonShareGuideBg = 'res/common/share_guide_bg.webp';
  static const String commonSharePreviewEye = 'res/common/share_preview_eye.png';
  static const String commonSmallRight = 'res/common/small_right.webp';
  static const String commonStarChecked = 'res/common/star_checked.png';
  static const String commonStarUnCheck = 'res/common/star_un_check.png';
  static const String commonSubmit = 'res/common/submit.webp';
  static const String commonTime = 'res/common/time.png';
  static const String commonTitleVoiceVerifyGuide = 'res/common/title_voice_verify_guide.webp';
  static const String commonTop = 'res/common/top.png';
  static const String commonTriangle = 'res/common/triangle.png';
  static const String commonUnselect = 'res/common/unselect.png';
  static const String commonUpgrade = 'res/common/upgrade.webp';
  static const String commonUserIntroEdit = 'res/common/user_intro_edit.webp';
  static const String commonVerifyPagePlaceholderBg = 'res/common/verify_page_placeholder_bg.webp';
  static const String commonVerifyPagePlaceholderBgFemale = 'res/common/verify_page_placeholder_bg_female.webp';
  static const String commonVisibleFriends = 'res/common/visible_friends.webp';
  static const String commonVisibleFriendsGray = 'res/common/visible_friends_gray.webp';
  static const String commonVisibleMyselfGray = 'res/common/visible_myself_gray.webp';
  static const String commonVisibleMyselfy = 'res/common/visible_myselfy.webp';
  static const String commonVisiblePublic = 'res/common/visible_public.webp';
  static const String commonVisiblePublicGray = 'res/common/visible_public_gray.webp';
  static const String commonVoiceChange = 'res/common/voice_change.png';
  static const String commonVoiceReviewPass = 'res/common/voice_review_pass.png';
  static const String commonVoiceShareConfirm = 'res/common/voice_share_confirm.webp';
  static const String commonVoiceVerifyEdit = 'res/common/voice_verify_edit.webp';
  static const String commonVoiceVerifyMark = 'res/common/voice_verify_mark.webp';
  static const String commonVoiceVerifyMarkWhite = 'res/common/voice_verify_mark_white.webp';
  static const String commonVoiceVerifyPause = 'res/common/voice_verify_pause.webp';
  static const String commonVoiceVerifyPlay = 'res/common/voice_verify_play.webp';
  static const String commonVoiceVerifyRhythm = 'res/common/voice_verify_rhythm.png';
  static const String emptyBgContactNull = 'res/empty/bg_contact_null.png';
  static const String emptyBgImLongNull = 'res/empty/bg_im_long_null.png';
  static const String emptyBgImNull = 'res/empty/bg_im_null.png';
  static const String emptyBgMomentNull = 'res/empty/bg_moment_null.webp';
  static const String emptyBgRoomNull = 'res/empty/bg_room_null.webp';
  static const String emptyEmptyBox = 'res/empty/empty_box.webp';
  static const String emptyEmptyBoxLightTheme = 'res/empty/empty_box_light_theme.webp';
  static const String emptyEmptyChat = 'res/empty/empty_chat.webp';
  static const String emptyEmptyFeed = 'res/empty/empty_feed.webp';
  static const String emptyEmptyList = 'res/empty/empty_list.webp';
  static const String emptyEmptyNetwork = 'res/empty/empty_network.webp';
  static const String emptyEmptyNotification = 'res/empty/empty_notification.webp';
  static const String emptyEmptySearch = 'res/empty/empty_search.webp';
  static const String emptyEmptyUser = 'res/empty/empty_user.webp';
  static const String emptyEmptyUserPost = 'res/empty/empty_user_post.webp';
  static const String emptyEmptyVisitor = 'res/empty/empty_visitor.webp';
  static const String emptyImgError = 'res/empty/img_error.png';
  static const String emptyImgError2 = 'res/empty/img_error2.png';
  static const String emptyMusicNoResult = 'res/empty/music_no_result.png';
  static const String emptyRoomMemberListEmpty = 'res/empty/room_member_list_empty.webp';
  static const String emptyVoiceVerfiyFail = 'res/empty/voice_verfiy_fail.png';
  static const String familyBgCreateNav = 'res/family/bg_create_nav.webp';
  static const String familyBgFamilyLevel = 'res/family/bg_family_level.webp';
  static const String familyBgFamilyLevelName = 'res/family/bg_family_level_name.webp';
  static const String familyBgFamilyPrivilege = 'res/family/bg_family_privilege.webp';
  static const String familyBgFamilyTreasury = 'res/family/bg_family_treasury.webp';
  static const String familyBgFamilyTreasuryGold = 'res/family/bg_family_treasury_gold.png';
  static const String familyBgPickerSelected = 'res/family/bg_picker_selected.webp';
  static const String familyBgRankLevel = 'res/family/bg_rank_level.webp';
  static const String familyIcDiamond = 'res/family/ic_diamond.webp';
  static const String familyIcFamilyLevelHeader = 'res/family/ic_family_level_header.webp';
  static const String familyIcFamilyLevelNext = 'res/family/ic_family_level_next.webp';
  static const String familyIcFamilyPrivilegeHeader = 'res/family/ic_family_privilege_header.webp';
  static const String familyIcFamilyTreasuryGold = 'res/family/ic_family_treasury_gold.webp';
  static const String familyIcLuckyBagBody = 'res/family/ic_lucky_bag_body.webp';
  static const String familyIcLuckyBagCrown = 'res/family/ic_lucky_bag_crown.webp';
  static const String familyIcLuckyBagCrownDetail = 'res/family/ic_lucky_bag_crown_detail.webp';
  static const String familyIcLuckyBagOpen = 'res/family/ic_lucky_bag_open.webp';
  static const String familyIcLuckyBagRushAvailable = 'res/family/ic_lucky_bag_rush_available.webp';
  static const String familyIcLuckyBagRushUnavailable = 'res/family/ic_lucky_bag_rush_unavailable.webp';
  static const String familyIcLuckyBagTriangle = 'res/family/ic_lucky_bag_triangle.png';
  static const String familyIconCamera = 'res/family/icon_camera.webp';
  static const String familyIconCantShare = 'res/family/icon_cant_share.webp';
  static const String familyIconChatGroup = 'res/family/icon_chat_group.webp';
  static const String familyIconCopy = 'res/family/icon_copy.webp';
  static const String familyIconCreate = 'res/family/icon_create.webp';
  static const String familyIconCreateArrow = 'res/family/icon_create_arrow.webp';
  static const String familyIconDefaultCover = 'res/family/icon_default_cover.webp';
  static const String familyIconGold = 'res/family/icon_gold.webp';
  static const String familyIconGoldArrow = 'res/family/icon_gold_arrow.webp';
  static const String familyIconListMember = 'res/family/icon_list_member.webp';
  static const String familyIconListMemberPurple = 'res/family/icon_list_member_purple.webp';
  static const String familyIconManagerMenu = 'res/family/icon_manager_menu.webp';
  static const String familyIconRankCoin = 'res/family/icon_rank_coin.webp';
  static const String familyIconRoleMaster = 'res/family/icon_role_master.webp';
  static const String familyIconRolePatriarch = 'res/family/icon_role_patriarch.webp';
  static const String familyIconRoleVicePatriarch = 'res/family/icon_role_vicePatriarch.webp';
  static const String familyIconSearch = 'res/family/icon_search.webp';
  static const String familyIconSetting = 'res/family/icon_setting.webp';
  static const String familyIconSloganArrow = 'res/family/icon_slogan_arrow.webp';
  static const String familyIconTips = 'res/family/icon_tips.webp';
  static const String familyImgCreate = 'res/family/img_create.webp';
  static const String familyImgLuckyBagResultGot = 'res/family/img_lucky_bag_result_got.webp';
  static const String familyImgLuckyBagResultTitle = 'res/family/img_lucky_bag_result_title.webp';
  static const String familyLuckyBag = 'res/family/lucky_bag.webp';
  static const String familyLuckyBagAbout = 'res/family/lucky_bag_about.webp';
  static const String familyLuckyBagTime = 'res/family/lucky_bag_time.webp';
  static const String familyLuckyBagTriangle = 'res/family/lucky_bag_triangle.webp';
  static const String feedIconComment = 'res/feed/icon_comment.webp';
  static const String feedIconHasPraise = 'res/feed/icon_has_praise.webp';
  static const String feedIconMsg = 'res/feed/icon_msg.webp';
  static const String feedIconTag = 'res/feed/icon_tag.webp';
  static const String feedIconUnPraise = 'res/feed/icon_un_praise.webp';
  static const String feedMore = 'res/feed/more.webp';
  static const String feedPlayVoicePower = 'res/feed/play_voice_power.webp';
  static const String feedPublishBack = 'res/feed/publish_back.webp';
  static const String feedPublishEnter = 'res/feed/publish_enter.webp';
  static const String feedPublishFeedAdd = 'res/feed/publish_feed_add.webp';
  static const String feedPublishFeedPostDisable = 'res/feed/publish_feed_post_disable.webp';
  static const String feedPublishFeedPostEnable = 'res/feed/publish_feed_post_enable.webp';
  static const String feedPublishPermissionArrow = 'res/feed/publish_permission_arrow.webp';
  static const String feedPublishThemeSelectedIndicator = 'res/feed/publish_theme_selected_indicator.webp';
  static const String feedTabbarIconPost = 'res/feed/tabbar_icon_post.png';
  static const String feedThemePlaceholder = 'res/feed/theme_placeholder.jpg';
  static const String feedThemeTitleDelete = 'res/feed/theme_title_delete.webp';
  static const String financeBgLargePayment = 'res/finance/bg_large_payment.webp';
  static const String financeBgLimitRechargeD = 'res/finance/bg_limit_recharge_d.webp';
  static const String financeBgRechargeReward = 'res/finance/bg_recharge_reward.png';
  static const String financeBigGold = 'res/finance/big_gold.webp';
  static const String financeBtnLarge = 'res/finance/btn_large.webp';
  static const String financeBtnLimitBanner = 'res/finance/btn_limit_banner.webp';
  static const String financeBtnLimitRecharge = 'res/finance/btn_limit_recharge.webp';
  static const String financeCloseLarge = 'res/finance/close_large.webp';
  static const String financeDiamond = 'res/finance/diamond.webp';
  static const String financeExchange = 'res/finance/exchange.svg';
  static const String financeGem = 'res/finance/gem.webp';
  static const String financeGiftP = 'res/finance/gift_p.webp';
  static const String financeGiftT = 'res/finance/gift_t.webp';
  static const String financeGoldCoin = 'res/finance/gold_coin.webp';
  static const String financeIcGooglePay = 'res/finance/ic_google_pay.webp';
  static const String financeIcHuaweiPay = 'res/finance/ic_huawei_pay.png';
  static const String financeIconChatCoin = 'res/finance/icon_chat_coin.webp';
  static const String financeIconExchangeCoin = 'res/finance/icon_exchange_coin.webp';
  static const String financeIconExchangeDiamond = 'res/finance/icon_exchange_diamond.webp';
  static const String financeIconExchangeEdit = 'res/finance/icon_exchange_edit.webp';
  static const String financeIconLimitClose = 'res/finance/icon_limit_close.webp';
  static const String financeIconRechargeReward = 'res/finance/icon_recharge_reward.webp';
  static const String financeLottieRechargeSuccess = 'res/finance/lottie_recharge_success.json';
  static const String financeQa = 'res/finance/qa.webp';
  static const String financeRechargeAdd = 'res/finance/recharge_add.webp';
  static const String financeRechargeShine = 'res/finance/recharge_shine.png';
  static const String financeSecondGuide = 'res/finance/second_guide.webp';
  static const String financeStarlight = 'res/finance/starlight.webp';
  static const String financeTitleLargePay = 'res/finance/title_large_pay.webp';
  static const String financeTitleLimitRecharge = 'res/finance/title_limit_recharge.webp';
  static const String gameIcHelp = 'res/game/ic_help.webp';
  static const String gameIcMusicTurnOff = 'res/game/ic_music_turn_off.webp';
  static const String gameIcMusicTurnOn = 'res/game/ic_music_turn_on.webp';
  static const String gameIcSetting = 'res/game/ic_setting.webp';
  static const String gameIconCoinBox = 'res/game/icon_coin_box.webp';
  static const String gameIconGameShareMvp = 'res/game/icon_game_share_mvp.webp';
  static const String gameIconResultShare = 'res/game/icon_result_share.webp';
  static const String gameImgGameShare1Frame = 'res/game/img_game_share_1_frame.webp';
  static const String gameImgGameShare2Frame = 'res/game/img_game_share_2_frame.webp';
  static const String gameImgGameShare3Frame = 'res/game/img_game_share_3_frame.webp';
  static const String gameImgGameShare4Frame = 'res/game/img_game_share_4_frame.webp';
  static const String gameImgGameShareMvpFrame = 'res/game/img_game_share_mvp_frame.webp';
  static const String gameImgGameShareName1 = 'res/game/img_game_share_name1.webp';
  static const String gameImgGameShareName2 = 'res/game/img_game_share_name2.webp';
  static const String gameImgGameShareName3 = 'res/game/img_game_share_name3.webp';
  static const String gameImgGameShareName4 = 'res/game/img_game_share_name4.webp';
  static const String gameShareQrCode = 'res/game/share_qr_code.webp';
  static const String gameCenterBannerGreen = 'res/game_center/banner_green.webp';
  static const String gameCenterBannerPurple = 'res/game_center/banner_purple.webp';
  static const String gameCenterBannerRed = 'res/game_center/banner_red.webp';
  static const String gameCenterBannerYellow = 'res/game_center/banner_yellow.webp';
  static const String gameCenterBgMain = 'res/game_center/bg_main.webp';
  static const String gameCenterBgRoomDialog = 'res/game_center/bg_room_dialog.webp';
  static const String gameCenterBgTaskList = 'res/game_center/bg_task_list.webp';
  static const String gameCenterDrawBg = 'res/game_center/draw_bg.webp';
  static const String gameCenterFreeBubbleEn = 'res/game_center/free_bubble_en.webp';
  static const String gameCenterFreeBubbleId = 'res/game_center/free_bubble_id.webp';
  static const String gameCenterIcRoomEntry = 'res/game_center/ic_room_entry.webp';
  static const String gameCenterIntergrations = 'res/game_center/intergrations.webp';
  static const String gameCenterJactpotBg = 'res/game_center/jactpot_bg.webp';
  static const String gameCenterPoints = 'res/game_center/points.webp';
  static const String gameCenterPointsSmall = 'res/game_center/points_small.webp';
  static const String gameCenterRecordBg = 'res/game_center/record_bg.webp';
  static const String gameCenterTaskBg = 'res/game_center/task_bg.webp';
  static const String gameCenterTop123Bg = 'res/game_center/top_123_bg.webp';
  static const String giftBlindBoxBg = 'res/gift/blind_box_bg.webp';
  static const String giftBlindBoxImgAr = 'res/gift/blind_box_img_ar.webp';
  static const String giftBlindBoxImgEg = 'res/gift/blind_box_img_eg.webp';
  static const String giftBlindGiftBg = 'res/gift/blind_gift_bg.png';
  static const String giftBlindboxRight = 'res/gift/blindbox_right.webp';
  static const String giftCharisma = 'res/gift/charisma.webp';
  static const String giftDecorationBottomCenter = 'res/gift/decoration_bottom_center.webp';
  static const String giftDecorationBottomCenter2 = 'res/gift/decoration_bottom_center_2.webp';
  static const String giftDecorationBottomLeft = 'res/gift/decoration_bottom_left.webp';
  static const String giftDecorationBottomLeft2 = 'res/gift/decoration_bottom_left_2.webp';
  static const String giftDecorationBottomRight = 'res/gift/decoration_bottom_right.webp';
  static const String giftDecorationBottomRight2 = 'res/gift/decoration_bottom_right_2.webp';
  static const String giftDecorationTopCenter = 'res/gift/decoration_top_center.webp';
  static const String giftDecorationTopLeft = 'res/gift/decoration_top_left.webp';
  static const String giftDecorationTopRight = 'res/gift/decoration_top_right.png';
  static const String giftDecorationTopRight2 = 'res/gift/decoration_top_right_2.png';
  static const String giftDecorationTopRight3 = 'res/gift/decoration_top_right_3.webp';
  static const String giftGiftEnter = 'res/gift/gift_enter.png';
  static const String giftGiftTagDescArrow = 'res/gift/gift_tag_desc_arrow.webp';
  static const String giftGiftUserSelected = 'res/gift/gift_user_selected.webp';
  static const String giftGuide = 'res/gift/guide.webp';
  static const String giftWealth = 'res/gift/wealth.webp';
  static const String guideHand = 'res/guide/hand.webp';
  static const String homeBgCandy = 'res/home/<USER>';
  static const String homeBgCandy1 = 'res/home/<USER>';
  static const String homeBgDomino = 'res/home/<USER>';
  static const String homeBgDomino1 = 'res/home/<USER>';
  static const String homeBgLodo1 = 'res/home/<USER>';
  static const String homeBgLodu = 'res/home/<USER>';
  static const String homeBgMatch = 'res/home/<USER>';
  static const String homeBgMatch1 = 'res/home/<USER>';
  static const String homeBgMatche = 'res/home/<USER>';
  static const String homeBgPage = 'res/home/<USER>';
  static const String homeBgTruth = 'res/home/<USER>';
  static const String homeBgTruth1 = 'res/home/<USER>';
  static const String homeBgUno = 'res/home/<USER>';
  static const String homeBgUno1 = 'res/home/<USER>';
  static const String homeBgVoice = 'res/home/<USER>';
  static const String homeBgVoice1 = 'res/home/<USER>';
  static const String homeCharmWealthLevelBg = 'res/home/<USER>';
  static const String homeIcGps = 'res/home/<USER>';
  static const String homeIcInGame = 'res/home/<USER>';
  static const String homeIconBlindDate = 'res/home/<USER>';
  static const String homeIconChat = 'res/home/<USER>';
  static const String homeIconGame = 'res/home/<USER>';
  static const String homeIconGameCandy = 'res/home/<USER>';
  static const String homeIconGameDomino = 'res/home/<USER>';
  static const String homeIconGameLudo = 'res/home/<USER>';
  static const String homeIconGameUno = 'res/home/<USER>';
  static const String homeIconLogo = 'res/home/<USER>';
  static const String homeIconMatchArrow = 'res/home/<USER>';
  static const String homeIconRoomJoin = 'res/home/<USER>';
  static const String homeIconTruthDare = 'res/home/<USER>';
  static const String homeInAppReview = 'res/home/<USER>';
  static const String homeLoadingClose = 'res/home/<USER>';
  static const String homeNearbyNoSettingBg = 'res/home/<USER>';
  static const String homePackageEntrance = 'res/home/<USER>';
  static const String homeReviewGreyStar = 'res/home/<USER>';
  static const String homeReviewLightStar = 'res/home/<USER>';
  static const String homeStarLeft = 'res/home/<USER>';
  static const String homeStarRight = 'res/home/<USER>';
  static const String iconHDiamond = 'res/icon_h/diamond.webp';
  static const String iconHFriends = 'res/icon_h/friends.webp';
  static const String iconHGoldStar = 'res/icon_h/gold_star.webp';
  static const String iconHMomentH = 'res/icon_h/moment_h.webp';
  static const String iconHRefreshH = 'res/icon_h/refresh_h.png';
  static const String iconHRight2H = 'res/icon_h/right2_h.webp';
  static const String iconHScore = 'res/icon_h/score.webp';
  static const String iconHShareFacebook = 'res/icon_h/share_facebook.png';
  static const String iconHShareInstagram = 'res/icon_h/share_instagram.webp';
  static const String iconHShareWhatsapp = 'res/icon_h/share_whatsapp.webp';
  static const String intimacyAniAvatarEmpty = 'res/intimacy/ani_avatar_empty.webp';
  static const String intimacyIconLevelUp = 'res/intimacy/icon_level_up.webp';
  static const String intimacyIconUnlock = 'res/intimacy/icon_unlock.webp';
  static const String intimacyTop1AvatarEmpty = 'res/intimacy/top1_avatar_empty.webp';
  static const String inviteRoomClose = 'res/invite_room_close.webp';
  static const String liveAwardCountDown0 = 'res/live/award/count_down_0.webp';
  static const String liveAwardCountDown1 = 'res/live/award/count_down_1.webp';
  static const String liveAwardCountDown2 = 'res/live/award/count_down_2.webp';
  static const String liveAwardCountDown3 = 'res/live/award/count_down_3.webp';
  static const String liveAwardCountDown4 = 'res/live/award/count_down_4.webp';
  static const String liveAwardCountDown5 = 'res/live/award/count_down_5.webp';
  static const String liveAwardCountDown6 = 'res/live/award/count_down_6.webp';
  static const String liveAwardCountDown7 = 'res/live/award/count_down_7.webp';
  static const String liveAwardCountDown8 = 'res/live/award/count_down_8.webp';
  static const String liveAwardCountDown9 = 'res/live/award/count_down_9.webp';
  static const String liveBgChatRoomInvite = 'res/live/bg_chat_room_invite.webp';
  static const String liveBgGameRoomInvite = 'res/live/bg_game_room_invite.webp';
  static const String liveCandy = 'res/live/candy.webp';
  static const String liveDomino = 'res/live/domino.webp';
  static const String liveEventBgFamily = 'res/live/event/bg_family.webp';
  static const String liveEventBgGiftTop1 = 'res/live/event/bg_gift_top1.webp';
  static const String liveEventBgGiftTop2 = 'res/live/event/bg_gift_top2.webp';
  static const String liveEventBgGiftTop3 = 'res/live/event/bg_gift_top3.webp';
  static const String liveEventBgSubscribers = 'res/live/event/bg_subscribers.webp';
  static const String liveEventIconBannerAdd = 'res/live/event/icon_banner_add.webp';
  static const String liveEventIconClock = 'res/live/event/icon_clock.webp';
  static const String liveEventIconCreate = 'res/live/event/icon_create.webp';
  static const String liveEventIconDelete = 'res/live/event/icon_delete.webp';
  static const String liveEventIconDetailRoom = 'res/live/event/icon_detail_room.webp';
  static const String liveEventIconEdit = 'res/live/event/icon_edit.webp';
  static const String liveEventIconGiftCrown = 'res/live/event/icon_gift_crown.webp';
  static const String liveEventIconGiftHeart = 'res/live/event/icon_gift_heart.webp';
  static const String liveEventIconGiftTips = 'res/live/event/icon_gift_tips.webp';
  static const String liveEventIconHot = 'res/live/event/icon_hot.webp';
  static const String liveEventIconRoomArrow = 'res/live/event/icon_room_arrow.webp';
  static const String liveGameAddFriend = 'res/live/game/add_friend.webp';
  static const String liveGameBgCandy = 'res/live/game/bg_candy.webp';
  static const String liveGameBgCandyRule = 'res/live/game/bg_candy_rule.webp';
  static const String liveGameBgInviteTop = 'res/live/game/bg_invite_top.webp';
  static const String liveGameBgUno = 'res/live/game/bg_uno.webp';
  static const String liveGameBgUnoRule = 'res/live/game/bg_uno_rule.webp';
  static const String liveGameBtnClose = 'res/live/game/btn_close.webp';
  static const String liveGameBtnGreen = 'res/live/game/btn_green.webp';
  static const String liveGameBtnUnoClose = 'res/live/game/btn_uno_close.webp';
  static const String liveGameBtnYellow = 'res/live/game/btn_yellow.webp';
  static const String liveGameGameFeeDialogBg = 'res/live/game/game_fee_dialog_bg.webp';
  static const String liveGameIcInvite = 'res/live/game/ic_invite.webp';
  static const String liveGameIcJoin = 'res/live/game/ic_join.webp';
  static const String liveGameIcLock = 'res/live/game/ic_lock.webp';
  static const String liveGameIcPlayerLock = 'res/live/game/ic_player_lock.webp';
  static const String liveGameIcUnlock = 'res/live/game/ic_unlock.webp';
  static const String liveGameIcWhiteArrow = 'res/live/game/ic_white_arrow.webp';
  static const String liveGameIconGreenCheckBox = 'res/live/game/icon_green_check_box.png';
  static const String liveGameIconKickOut = 'res/live/game/icon_kick_out.webp';
  static const String liveGameIconPlayerAdd = 'res/live/game/icon_player_add.webp';
  static const String liveGameIconPlayerUnknown = 'res/live/game/icon_player_unknown.webp';
  static const String liveGameImgGift = 'res/live/game/img_gift.webp';
  static const String liveGameImgLoadFront = 'res/live/game/img_load_front.png';
  static const String liveGameImgMedal01 = 'res/live/game/img_medal_01.webp';
  static const String liveGameImgMedal02 = 'res/live/game/img_medal_02.webp';
  static const String liveGameImgMedal03 = 'res/live/game/img_medal_03.webp';
  static const String liveGameImgRanking01 = 'res/live/game/img_ranking_01.webp';
  static const String liveGameImgRanking02 = 'res/live/game/img_ranking_02.webp';
  static const String liveGameImgRanking03 = 'res/live/game/img_ranking_03.webp';
  static const String liveGameImgRanking04 = 'res/live/game/img_ranking_04.webp';
  static const String liveGameImgRankingG01 = 'res/live/game/img_ranking_g_01.png';
  static const String liveGameImgRankingG02 = 'res/live/game/img_ranking_g_02.png';
  static const String liveGameImgRankingG03 = 'res/live/game/img_ranking_g_03.png';
  static const String liveGameImgRankingG04 = 'res/live/game/img_ranking_g_04.png';
  static const String liveGameLudoLogo = 'res/live/game/ludo_logo.webp';
  static const String liveGameLudoNormal = 'res/live/game/ludo_normal.webp';
  static const String liveGameLudoNormalBg = 'res/live/game/ludo_normal_bg.webp';
  static const String liveGameLudoQuickly = 'res/live/game/ludo_quickly.webp';
  static const String liveGameResultTop = 'res/live/game_result_top.webp';
  static const String liveIcThemeSelect = 'res/live/ic_theme_select.webp';
  static const String liveIconCaptain = 'res/live/icon_captain.png';
  static const String liveLudo = 'res/live/ludo.webp';
  static const String liveLudoCancelBtn = 'res/live/ludo_cancel_btn.webp';
  static const String liveLudoCancelText = 'res/live/ludo_cancel_text.png';
  static const String liveMusicIcAdd = 'res/live/music/ic_add.webp';
  static const String liveMusicIcAddNet = 'res/live/music/ic_add_net.webp';
  static const String liveMusicIcDownArrow = 'res/live/music/ic_down_arrow.webp';
  static const String liveMusicIcNext = 'res/live/music/ic_next.webp';
  static const String liveMusicIcPause = 'res/live/music/ic_pause.webp';
  static const String liveMusicIcPlay = 'res/live/music/ic_play.webp';
  static const String liveMusicIcPre = 'res/live/music/ic_pre.webp';
  static const String liveMusicIcRocker = 'res/live/music/ic_rocker.webp';
  static const String liveMusicIconArrawBorder = 'res/live/music/icon_arraw_border.webp';
  static const String liveMusicIconArrawBorderA = 'res/live/music/icon_arraw_border_a.png';
  static const String liveMusicIconCopy = 'res/live/music/icon_copy.webp';
  static const String liveMusicIconFplayerClose = 'res/live/music/icon_fplayer_close.webp';
  static const String liveMusicIconFplayerList = 'res/live/music/icon_fplayer_list.webp';
  static const String liveMusicIconFplayerNext = 'res/live/music/icon_fplayer_next.webp';
  static const String liveMusicIconFplayerPlay = 'res/live/music/icon_fplayer_play.webp';
  static const String liveMusicIconFplayerStop = 'res/live/music/icon_fplayer_stop.webp';
  static const String liveMusicIconPlayInLoop = 'res/live/music/icon_play_in_loop.webp';
  static const String liveMusicIconPlayInOrder = 'res/live/music/icon_play_in_order.webp';
  static const String liveMusicIconPlayShuffle = 'res/live/music/icon_play_shuffle.webp';
  static const String liveMusicIconSearchEmptyMusic = 'res/live/music/icon_search_empty_music.webp';
  static const String liveMusicIconVol0 = 'res/live/music/icon_vol0.webp';
  static const String liveMusicIconVol1 = 'res/live/music/icon_vol1.webp';
  static const String liveMusicIconVol2 = 'res/live/music/icon_vol2.webp';
  static const String liveMusicIconWifiServer = 'res/live/music/icon_wifi_server.webp';
  static const String liveMusicImgMusic = 'res/live/music/img_music.webp';
  static const String liveMusicImgPlayingDisk = 'res/live/music/img_playing_disk.webp';
  static const String liveMusicNoWifiBg = 'res/live/music/no_wifi_bg.webp';
  static const String liveMusicWifiUploadBg = 'res/live/music/wifi_upload_bg.webp';
  static const String liveSvgaBombcatMvp = 'res/live/svga/bombcat_mvp.svga';
  static const String liveUno = 'res/live/uno.webp';
  static const String loginBg = 'res/login/bg.webp';
  static const String loginChecked = 'res/login/checked.png';
  static const String loginIconFeedback = 'res/login/icon_feedback.webp';
  static const String loginIconId = 'res/login/icon_id.webp';
  static const String loginIconLoginButtonPhone = 'res/login/icon_login_button_phone.webp';
  static const String loginLoginBlackName = 'res/login/login_black_name.webp';
  static const String loginLogoApple = 'res/login/logo_apple.webp';
  static const String loginRegAnsTest = 'res/login/reg_ans_test.webp';
  static const String loginRegMail = 'res/login/reg_mail.webp';
  static const String loginRegRight = 'res/login/reg_right.png';
  static const String loginRegUserCardBg = 'res/login/reg_user_card_bg.webp';
  static const String loginRegWrong = 'res/login/reg_wrong.png';
  static const String loginStateDown = 'res/login/state_down.webp';
  static const String loginThirdLoginBoxLine = 'res/login/third_login_box_line.webp';
  static const String loginUnchecked = 'res/login/unchecked.png';
  static const String mallBgAvatar = 'res/mall/bg_avatar.webp';
  static const String mallBgCar = 'res/mall/bg_car.webp';
  static const String mallBgChat = 'res/mall/bg_chat.webp';
  static const String mallBgDiscount = 'res/mall/bg_discount.webp';
  static const String mallBgHomePageEffect = 'res/mall/bg_home_page_effect.webp';
  static const String mallBgHomepage = 'res/mall/bg_homepage.webp';
  static const String mallBgHot = 'res/mall/bg_hot.webp';
  static const String mallBgMicSeatPreview = 'res/mall/bg_mic_seat_preview.webp';
  static const String mallBgProps = 'res/mall/bg_props.webp';
  static const String mallBgRide = 'res/mall/bg_ride.webp';
  static const String mallBgRing = 'res/mall/bg_ring.webp';
  static const String mallBgSkin = 'res/mall/bg_skin.webp';
  static const String mallBgTheme = 'res/mall/bg_theme.webp';
  static const String mallIconAvatar = 'res/mall/icon_avatar.webp';
  static const String mallIconBackpack = 'res/mall/icon_backpack.webp';
  static const String mallIconBagShop = 'res/mall/icon_bag_shop.webp';
  static const String mallIconCar = 'res/mall/icon_car.webp';
  static const String mallIconChat = 'res/mall/icon_chat.webp';
  static const String mallIconClock = 'res/mall/icon_clock.webp';
  static const String mallIconCountLeft = 'res/mall/icon_count_left.webp';
  static const String mallIconHot = 'res/mall/icon_hot.webp';
  static const String mallIconPlay = 'res/mall/icon_play.webp';
  static const String mallIconProps = 'res/mall/icon_props.webp';
  static const String mallIconPropsAdd = 'res/mall/icon_props_add.webp';
  static const String mallIconPropsReduce = 'res/mall/icon_props_reduce.webp';
  static const String mallIconRide = 'res/mall/icon_ride.webp';
  static const String mallIconRightIndicator = 'res/mall/icon_right_indicator.webp';
  static const String mallIconRing = 'res/mall/icon_ring.webp';
  static const String mallIconSkin = 'res/mall/icon_skin.webp';
  static const String mallIconTheme = 'res/mall/icon_theme.webp';
  static const String mallImgNavBg = 'res/mall/img_nav_bg.webp';
  static const String matchFatebellRingLottie = 'res/match/fatebell_ring_lottie.json';
  static const String matchMatchBg = 'res/match/match_bg.webp';
  static const String matchMatchBg2 = 'res/match/match_bg_2.webp';
  static const String matchMatchDegreeBg = 'res/match/match_degree_bg.webp';
  static const String matchMatchRing = 'res/match/match_ring.webp';
  static const String matchMatchRoomBg = 'res/match/match_room_bg.webp';
  static const String profileAboutMe = 'res/profile/about_me.webp';
  static const String profileAddFriend = 'res/profile/add_friend.webp';
  static const String profileAddedFriend = 'res/profile/added_friend.webp';
  static const String profileAvatarBan = 'res/profile/avatar_ban.webp';
  static const String profileBgBeFollow = 'res/profile/bg_be_follow.webp';
  static const String profileBgInfo = 'res/profile/bg_info.webp';
  static const String profileBgInvitationScreenshot = 'res/profile/bg_invitation_screenshot.webp';
  static const String profileBgInvitationStamp = 'res/profile/bg_invitation_stamp.webp';
  static const String profileBgPage = 'res/profile/bg_page.webp';
  static const String profileBgStateInfo = 'res/profile/bg_state_info.png';
  static const String profileChatPreDialogBg = 'res/profile/chat_pre_dialog_bg.webp';
  static const String profileCopy = 'res/profile/copy.webp';
  static const String profileCopySvg = 'res/profile/copy_svg.svg';
  static const String profileDefaultAvatar = 'res/profile/default_avatar.webp';
  static const String profileEdit = 'res/profile/edit.webp';
  static const String profileExchange = 'res/profile/exchange.webp';
  static const String profileFemale = 'res/profile/female.webp';
  static const String profileIcAboutEdit = 'res/profile/ic_about_edit.webp';
  static const String profileIcFemaleRound = 'res/profile/ic_female_round.webp';
  static const String profileIcMaleRound = 'res/profile/ic_male_round.webp';
  static const String profileIcScreenshot = 'res/profile/ic_screenshot.webp';
  static const String profileIcToChat = 'res/profile/ic_to_chat.webp';
  static const String profileIcWinkStatus = 'res/profile/ic_wink_status.webp';
  static const String profileIconChat = 'res/profile/icon_chat.webp';
  static const String profileIconChatBlue = 'res/profile/icon_chat_blue.webp';
  static const String profileIconFemale = 'res/profile/icon_female.webp';
  static const String profileIconInfoBadge = 'res/profile/icon_info_badge.png';
  static const String profileIconInfoMoment = 'res/profile/icon_info_moment.webp';
  static const String profileIconInfoPrivatePhoto = 'res/profile/icon_info_private_photo.webp';
  static const String profileIconLocation = 'res/profile/icon_location.webp';
  static const String profileIconMale = 'res/profile/icon_male.webp';
  static const String profileIconMenuActivity = 'res/profile/icon_menu_activity.webp';
  static const String profileIconMenuBadge = 'res/profile/icon_menu_badge.webp';
  static const String profileIconMenuBag = 'res/profile/icon_menu_bag.webp';
  static const String profileIconMenuFamily = 'res/profile/icon_menu_family.webp';
  static const String profileIconMenuFeedback = 'res/profile/icon_menu_feedback.webp';
  static const String profileIconMenuIncome = 'res/profile/icon_menu_income.webp';
  static const String profileIconMenuRecharge = 'res/profile/icon_menu_recharge.webp';
  static const String profileIconMenuStore = 'res/profile/icon_menu_store.webp';
  static const String profileIconMineHeaderAdd = 'res/profile/icon_mine_header_add.webp';
  static const String profileIconStar = 'res/profile/icon_star.webp';
  static const String profileInRoomStatusBg = 'res/profile/in_room_status_bg.webp';
  static const String profileInvitationAnywhere = 'res/profile/invitation_anywhere.webp';
  static const String profileInvitationAnywhereId = 'res/profile/invitation_anywhere_id.webp';
  static const String profileInvitationFly = 'res/profile/invitation_fly.webp';
  static const String profileInvitationFlyLine = 'res/profile/invitation_fly_line.webp';
  static const String profileInvitationLogo = 'res/profile/invitation_logo.webp';
  static const String profileInvitationQrCode = 'res/profile/invitation_qr_code.webp';
  static const String profileInvitationWinker = 'res/profile/invitation_winker.webp';
  static const String profileMale = 'res/profile/male.webp';
  static const String profileMineInfoIntimacyRankIc = 'res/profile/mine_info_intimacy_rank_ic.webp';
  static const String profileMineInfoLevelIc = 'res/profile/mine_info_level_ic.webp';
  static const String profileMineInfoMatchHqUser = 'res/profile/mine_info_match_hq_user.webp';
  static const String profileMineInfoMomentsIc = 'res/profile/mine_info_moments_ic.webp';
  static const String profileMineInfoPrivateGift = 'res/profile/mine_info_private_gift.webp';
  static const String profileMineInfoPrivatePhoto = 'res/profile/mine_info_private_photo.webp';
  static const String profileMineInfoSettingsIc = 'res/profile/mine_info_settings_ic.webp';
  static const String profileMineInfoVisitorIc = 'res/profile/mine_info_visitor_ic.webp';
  static const String profilePostTipJournal = 'res/profile/post_tip_journal.webp';
  static const String profilePostTipLocation = 'res/profile/post_tip_location.webp';
  static const String profilePostTipPet = 'res/profile/post_tip_pet.webp';
  static const String profilePrivatePhotoAdd = 'res/profile/private_photo_add.webp';
  static const String profilePrivatePhotoAddDisable = 'res/profile/private_photo_add_disable.webp';
  static const String profilePrivatePhotoAlertIcon = 'res/profile/private_photo_alert_icon.webp';
  static const String profileSetFemale = 'res/profile/set_female.png';
  static const String profileSetMale = 'res/profile/set_male.png';
  static const String profileUserPostTip = 'res/profile/user_post_tip.webp';
  static const String roomActionMenuArrow = 'res/room/action_menu_arrow.webp';
  static const String roomApplyMicAccept = 'res/room/apply_mic_accept.png';
  static const String roomApplyMicReject = 'res/room/apply_mic_reject.png';
  static const String roomBarEmoji = 'res/room/bar_emoji.webp';
  static const String roomBarMicDisable = 'res/room/bar_mic_disable.png';
  static const String roomBarMicNormal = 'res/room/bar_mic_normal.webp';
  static const String roomBarNoMic = 'res/room/bar_no_mic.webp';
  static const String roomBgFamilyEntrance = 'res/room/bg_family_entrance.webp';
  static const String roomBgMusicBgMusicDisk = 'res/room/bg_music/bg_music_disk.webp';
  static const String roomBgMusicIcMusicDrivingLever = 'res/room/bg_music/ic_music_driving_lever.webp';
  static const String roomBgOfficialRoomBg = 'res/room/bg_official_room_bg.webp';
  static const String roomBgPage = 'res/room/bg_page.webp';
  static const String roomBgProfileCover = 'res/room/bg_profile_cover.webp';
  static const String roomBgRankFamily = 'res/room/bg_rank_family.webp';
  static const String roomBgRankGiftGallery = 'res/room/bg_rank_gift_gallery.webp';
  static const String roomBgRankGiftReceived = 'res/room/bg_rank_gift_received.webp';
  static const String roomBgRankGiftSend = 'res/room/bg_rank_gift_send.webp';
  static const String roomBgRankIntimacy = 'res/room/bg_rank_intimacy.webp';
  static const String roomBgRankTop = 'res/room/bg_rank_top.webp';
  static const String roomBlindBox = 'res/room/blind_box.webp';
  static const String roomBlindDateGameResultHasMatch = 'res/room/blind_date/game_result_has_match.webp';
  static const String roomBlindDateGameResultNoMatch = 'res/room/blind_date/game_result_no_match.webp';
  static const String roomBlindDateIcCpLove = 'res/room/blind_date/ic_cp_love.webp';
  static const String roomBlindDateIcNoCp = 'res/room/blind_date/ic_no_cp.png';
  static const String roomBlindDateMicSeatBg = 'res/room/blind_date/mic_seat_bg.webp';
  static const String roomBlindDateSelected = 'res/room/blind_date/selected.png';
  static const String roomCharmWealthUpgradeBg = 'res/room/charm_wealth_upgrade_bg.webp';
  static const String roomComboBg = 'res/room/combo_bg.webp';
  static const String roomComboBtnBg = 'res/room/combo_btn_bg.webp';
  static const String roomDefaultBg = 'res/room/default_bg.webp';
  static const String roomFansGroupDrawResultBg = 'res/room/fans_group/draw_result_bg.webp';
  static const String roomFansGroupFansCardBig = 'res/room/fans_group/fans_card_big.webp';
  static const String roomFansGroupFansLevelUpBg = 'res/room/fans_group/fans_level_up_bg.webp';
  static const String roomFansGroupFansLotteryAdd = 'res/room/fans_group/fans_lottery_add.webp';
  static const String roomFansGroupFansLotteryGiftBig = 'res/room/fans_group/fans_lottery_gift_big.webp';
  static const String roomFansGroupFansLotteryGiftSmall = 'res/room/fans_group/fans_lottery_gift_small.webp';
  static const String roomFansGroupFansLotteryPop = 'res/room/fans_group/fans_lottery_pop.webp';
  static const String roomFansGroupFansRewardBrightness = 'res/room/fans_group/fans_reward_brightness.webp';
  static const String roomFansGroupIcTopFans = 'res/room/fans_group/ic_top_fans.webp';
  static const String roomFansGroupRoomFansClubChurchBg = 'res/room/fans_group/room_fans_club_church_bg.webp';
  static const String roomFansGroupRoomFansClubChurchRibbon = 'res/room/fans_group/room_fans_club_church_ribbon.webp';
  static const String roomFansGroupRoomUserFans = 'res/room/fans_group/room_user_fans.webp';
  static const String roomFansGroupTriangleBottom = 'res/room/fans_group/triangle_bottom.webp';
  static const String roomFansGroupTriangleBottomHelpDialog = 'res/room/fans_group/triangle_bottom_help_dialog.webp';
  static const String roomGift = 'res/room/gift.webp';
  static const String roomGiftBarrageBg1 = 'res/room/gift_barrage_bg_1.webp';
  static const String roomGiftBarrageBg2 = 'res/room/gift_barrage_bg_2.webp';
  static const String roomGiftBarrageBg3 = 'res/room/gift_barrage_bg_3.webp';
  static const String roomGiftCountEight = 'res/room/gift_count_eight.webp';
  static const String roomGiftCountFive = 'res/room/gift_count_five.webp';
  static const String roomGiftCountFour = 'res/room/gift_count_four.webp';
  static const String roomGiftCountNine = 'res/room/gift_count_nine.webp';
  static const String roomGiftCountOne = 'res/room/gift_count_one.webp';
  static const String roomGiftCountSeven = 'res/room/gift_count_seven.webp';
  static const String roomGiftCountSix = 'res/room/gift_count_six.webp';
  static const String roomGiftCountThree = 'res/room/gift_count_three.webp';
  static const String roomGiftCountTwo = 'res/room/gift_count_two.webp';
  static const String roomGiftCountX = 'res/room/gift_count_x.webp';
  static const String roomGiftCountZero = 'res/room/gift_count_zero.webp';
  static const String roomGiftGalleryArrow = 'res/room/gift_gallery/arrow.webp';
  static const String roomGiftGalleryBg = 'res/room/gift_gallery/bg.webp';
  static const String roomGiftGalleryDefaultAvatar = 'res/room/gift_gallery/default_avatar.webp';
  static const String roomGiftGalleryIntroBg = 'res/room/gift_gallery/intro_bg.webp';
  static const String roomGiftGalleryItemArrow = 'res/room/gift_gallery/item_arrow.png';
  static const String roomGiftGalleryRank1 = 'res/room/gift_gallery/rank_1.webp';
  static const String roomGiftGalleryRankBg = 'res/room/gift_gallery/rank_bg.webp';
  static const String roomGiftGallerySectionLeft = 'res/room/gift_gallery/section_left.webp';
  static const String roomGiftGallerySectionRight = 'res/room/gift_gallery/section_right.webp';
  static const String roomGiftGoodWear = 'res/room/gift_good_wear.webp';
  static const String roomGiftNewerGuide = 'res/room/gift_newer_guide.webp';
  static const String roomGiftPanelDiamondArrow = 'res/room/gift_panel/diamond_arrow.webp';
  static const String roomGiftPanelGiftSendBtnBg = 'res/room/gift_panel/gift_send_btn_bg.webp';
  static const String roomGiftPanelIconAllUser = 'res/room/gift_panel/icon_all_user.webp';
  static const String roomGiftPanelSelectedUserArrow = 'res/room/gift_panel/selected_user_arrow.webp';
  static const String roomGiftPanelSendUserBg = 'res/room/gift_panel/send_user_bg.webp';
  static const String roomGiftPanelSendUserBg1 = 'res/room/gift_panel/send_user_bg_1.webp';
  static const String roomGiftPanelSendUserBg2 = 'res/room/gift_panel/send_user_bg_2.webp';
  static const String roomGiftPanelSendUserBg3 = 'res/room/gift_panel/send_user_bg_3.webp';
  static const String roomIcEmotion = 'res/room/ic_emotion.webp';
  static const String roomIcOnline = 'res/room/ic_online.webp';
  static const String roomIcVipEmotion = 'res/room/ic_vip_emotion.webp';
  static const String roomIconBottomBarGift = 'res/room/icon_bottom_bar_gift.webp';
  static const String roomIconBottomBarMore = 'res/room/icon_bottom_bar_more.webp';
  static const String roomIconConnection = 'res/room/icon_connection.webp';
  static const String roomIconCreateRoomAdd = 'res/room/icon_create_room_add.webp';
  static const String roomIconItemChat = 'res/room/icon_item_chat.webp';
  static const String roomIconLeve1 = 'res/room/icon_leve1.webp';
  static const String roomIconLeve2 = 'res/room/icon_leve2.webp';
  static const String roomIconLeve3 = 'res/room/icon_leve3.webp';
  static const String roomIconLeve4 = 'res/room/icon_leve4.webp';
  static const String roomIconLeve5 = 'res/room/icon_leve5.webp';
  static const String roomIconMicSeatApply = 'res/room/icon_mic_seat_apply.webp';
  static const String roomIconMicSeatMine = 'res/room/icon_mic_seat_mine.webp';
  static const String roomIconMoreClose = 'res/room/icon_more_close.webp';
  static const String roomIconMsgClose = 'res/room/icon_msg_close.webp';
  static const String roomIconMsgEntrance = 'res/room/icon_msg_entrance.webp';
  static const String roomIconMyRoomHome = 'res/room/icon_my_room_home.webp';
  static const String roomIconMyroomHomeBg = 'res/room/icon_myroom_home_bg.webp';
  static const String roomIconPartyCreate = 'res/room/icon_party_create.webp';
  static const String roomIconProfileAccess = 'res/room/icon_profile_access.webp';
  static const String roomIconProfileArrow = 'res/room/icon_profile_arrow.webp';
  static const String roomIconProfileBg = 'res/room/icon_profile_bg.webp';
  static const String roomIconProfileCamera = 'res/room/icon_profile_camera.webp';
  static const String roomIconProfileCopy = 'res/room/icon_profile_copy.webp';
  static const String roomIconProfileEdit = 'res/room/icon_profile_edit.webp';
  static const String roomIconProfileLevelTips = 'res/room/icon_profile_level_tips.webp';
  static const String roomIconProfileLock = 'res/room/icon_profile_lock.webp';
  static const String roomIconProfileMic = 'res/room/icon_profile_mic.webp';
  static const String roomIconProfileTag = 'res/room/icon_profile_tag.webp';
  static const String roomIconRankIntimacy = 'res/room/icon_rank_intimacy.webp';
  static const String roomIconSendGiftGuideMessage = 'res/room/icon_send_gift_guide_message.webp';
  static const String roomIconTypeCandy = 'res/room/icon_type_candy.webp';
  static const String roomIconTypeChat = 'res/room/icon_type_chat.webp';
  static const String roomIconTypeClean = 'res/room/icon_type_clean.webp';
  static const String roomIconTypeDomino = 'res/room/icon_type_domino.webp';
  static const String roomIconTypeLuckBag = 'res/room/icon_type_luck_bag.webp';
  static const String roomIconTypeLudo = 'res/room/icon_type_ludo.webp';
  static const String roomIconTypeMusic = 'res/room/icon_type_music.webp';
  static const String roomIconTypeReward = 'res/room/icon_type_reward.webp';
  static const String roomIconTypeTmo = 'res/room/icon_type_tmo.webp';
  static const String roomIconTypeTruthDare = 'res/room/icon_type_truthDare.webp';
  static const String roomIconTypeUno = 'res/room/icon_type_uno.webp';
  static const String roomIconUnlockRoomAdmin = 'res/room/icon_unlock_room_admin.webp';
  static const String roomIconUnlockRoomBg = 'res/room/icon_unlock_room_bg.webp';
  static const String roomIconUnlockRoomTmo = 'res/room/icon_unlock_room_tmo.webp';
  static const String roomIconUnlockRoomTmoLock = 'res/room/icon_unlock_room_tmo_lock.webp';
  static const String roomIconUpgradeArrow = 'res/room/icon_upgrade_arrow.webp';
  static const String roomIconWelcomeNew = 'res/room/icon_welcome_new.webp';
  static const String roomImgCreateRoom = 'res/room/img_create_room.webp';
  static const String roomImgUpgradeTop = 'res/room/img_upgrade_top.webp';
  static const String roomIntroduction = 'res/room/introduction.webp';
  static const String roomInviteMicAcceptIcon = 'res/room/invite_mic_accept_icon.webp';
  static const String roomInviteMicBg = 'res/room/invite_mic_bg.webp';
  static const String roomMicSeatMoreRightArrow = 'res/room/mic_seat_more_right_arrow.webp';
  static const String roomMicSeatPlaceholder = 'res/room/mic_seat_placeholder.webp';
  static const String roomMicStatusRequestIcon = 'res/room/mic_status_request_icon.webp';
  static const String roomMoreWhite = 'res/room/more_white.webp';
  static const String roomMsgEdit = 'res/room/msg_edit.png';
  static const String roomMsgInviteMic = 'res/room/msg_invite_mic.webp';
  static const String roomMsgWelcomeHand = 'res/room/msg_welcome_hand.webp';
  static const String roomPartyCreateRoom = 'res/room/party_create_room.webp';
  static const String roomPeople = 'res/room/people.webp';
  static const String roomPlayCenterPk = 'res/room/play_center/pk.png';
  static const String roomPlayCenterRoompk = 'res/room/play_center/roompk.webp';
  static const String roomProfileCardAdmin = 'res/room/profile_card_admin.webp';
  static const String roomProfileCardBlock = 'res/room/profile_card_block.webp';
  static const String roomProfileCardCaptain = 'res/room/profile_card_captain.webp';
  static const String roomProfileCardCloseMic = 'res/room/profile_card_closeMic.webp';
  static const String roomProfileCardDownMic = 'res/room/profile_card_downMic.webp';
  static const String roomProfileCardKickout = 'res/room/profile_card_kickout.webp';
  static const String roomProfileCardMore = 'res/room/profile_card_more.webp';
  static const String roomProfileCardOpenMic = 'res/room/profile_card_openMic.webp';
  static const String roomProfileCardReport = 'res/room/profile_card_report.webp';
  static const String roomProfileCardUpMic = 'res/room/profile_card_upMic.webp';
  static const String roomRankGiftFloatScreenBg = 'res/room/rank/gift_float_screen_bg.webp';
  static const String roomRankIcRoomRank = 'res/room/rank/ic_room_rank.webp';
  static const String roomRankRankTop123Bg = 'res/room/rank/rank_top123_bg.webp';
  static const String roomRankRankTopCrown = 'res/room/rank/rank_top_crown.webp';
  static const String roomRedPackIcMsgRedPack = 'res/room/red_pack/ic_msg_red_pack.webp';
  static const String roomRedPackRedPackBg = 'res/room/red_pack/red_pack_bg.webp';
  static const String roomRedPackRedPackBtnWait = 'res/room/red_pack/red_pack_btn_wait.webp';
  static const String roomRedPackRedPackClose = 'res/room/red_pack/red_pack_close.webp';
  static const String roomRedPackRedPackGet = 'res/room/red_pack/red_pack_get.webp';
  static const String roomRedPackRedPackHot = 'res/room/red_pack/red_pack_hot.png';
  static const String roomRedPackRedPackJoinBtnBg = 'res/room/red_pack/red_pack_join_btn_bg.webp';
  static const String roomRedPackRedPackJoinFansBg = 'res/room/red_pack/red_pack_join_fans_bg.webp';
  static const String roomRedPackRedPackSmallGetEntry = 'res/room/red_pack/red_pack_small_get_entry.webp';
  static const String roomRedPackRedPackSmallSumEntry = 'res/room/red_pack/red_pack_small_sum_entry.webp';
  static const String roomRewardGoodBg = 'res/room/reward_good_bg.webp';
  static const String roomSearch = 'res/room/search.webp';
  static const String roomSendGiftGuideBg = 'res/room/send_gift_guide_bg.webp';
  static const String roomSettingsIcAccessPermissonLock = 'res/room/settings/ic_access_permisson_lock.webp';
  static const String roomSettingsIcBg = 'res/room/settings/ic_bg.webp';
  static const String roomSettingsIcDownWeight = 'res/room/settings/ic_down_weight.png';
  static const String roomSettingsIcExit = 'res/room/settings/ic_exit.png';
  static const String roomSettingsIcLock = 'res/room/settings/ic_lock.webp';
  static const String roomSettingsIcMinimize = 'res/room/settings/ic_minimize.png';
  static const String roomSettingsIcReport = 'res/room/settings/ic_report.webp';
  static const String roomSettingsIcSoundMute = 'res/room/settings/ic_sound_mute.webp';
  static const String roomSettingsIcSoundUnmute = 'res/room/settings/ic_sound_unmute.webp';
  static const String roomSettingsIconLock = 'res/room/settings/icon_lock.webp';
  static const String roomShare = 'res/room/share.webp';
  static const String roomShareCheck = 'res/room/share_check.webp';
  static const String roomSignGiftBg = 'res/room/sign_gift_bg.webp';
  static const String roomStickerStickerCountdown = 'res/room/sticker/sticker_countdown.png';
  static const String roomStickerStickerDice1 = 'res/room/sticker/sticker_dice_1.png';
  static const String roomStickerStickerDice2 = 'res/room/sticker/sticker_dice_2.png';
  static const String roomStickerStickerDice3 = 'res/room/sticker/sticker_dice_3.png';
  static const String roomStickerStickerDice4 = 'res/room/sticker/sticker_dice_4.png';
  static const String roomStickerStickerDice5 = 'res/room/sticker/sticker_dice_5.png';
  static const String roomStickerStickerDice6 = 'res/room/sticker/sticker_dice_6.png';
  static const String roomStickerStickerMora1 = 'res/room/sticker/sticker_mora_1.png';
  static const String roomStickerStickerMora2 = 'res/room/sticker/sticker_mora_2.png';
  static const String roomStickerStickerMora3 = 'res/room/sticker/sticker_mora_3.png';
  static const String roomStickerStickerPumpMic = 'res/room/sticker/sticker_pump_mic.png';
  static const String roomStickerStickerPumpMic1 = 'res/room/sticker/sticker_pump_mic_1.png';
  static const String roomStickerStickerPumpMic10 = 'res/room/sticker/sticker_pump_mic_10.png';
  static const String roomStickerStickerPumpMic2 = 'res/room/sticker/sticker_pump_mic_2.png';
  static const String roomStickerStickerPumpMic3 = 'res/room/sticker/sticker_pump_mic_3.png';
  static const String roomStickerStickerPumpMic4 = 'res/room/sticker/sticker_pump_mic_4.png';
  static const String roomStickerStickerPumpMic5 = 'res/room/sticker/sticker_pump_mic_5.png';
  static const String roomStickerStickerPumpMic6 = 'res/room/sticker/sticker_pump_mic_6.png';
  static const String roomStickerStickerPumpMic7 = 'res/room/sticker/sticker_pump_mic_7.png';
  static const String roomStickerStickerPumpMic8 = 'res/room/sticker/sticker_pump_mic_8.png';
  static const String roomStickerStickerPumpMic9 = 'res/room/sticker/sticker_pump_mic_9.png';
  static const String roomSudGameGameStatusIn = 'res/room/sud_game/game_status_in.png';
  static const String roomSvgaRoomHotRocket = 'res/room/svga/room_hot_rocket.svga';
  static const String roomSvgaStickerCountDown = 'res/room/svga/sticker_count_down.svga';
  static const String roomSvgaStickerDice = 'res/room/svga/sticker_dice.svga';
  static const String roomSvgaStickerMora = 'res/room/svga/sticker_mora.svga';
  static const String roomSvgaStickerPumpMic = 'res/room/svga/sticker_pump_mic.svga';
  static const String roomSwitchRoomIcon = 'res/room/switch_room_icon.webp';
  static const String roomTruthDareArrow = 'res/room/truth_dare/arrow.webp';
  static const String roomTruthDareAvatarFrame = 'res/room/truth_dare/avatar_frame.webp';
  static const String roomTruthDareGameIntro1 = 'res/room/truth_dare/game_intro_1.webp';
  static const String roomTruthDareGameIntro2 = 'res/room/truth_dare/game_intro_2.webp';
  static const String roomTruthDareGameIntro3En = 'res/room/truth_dare/game_intro_3_en.webp';
  static const String roomTruthDareGameIntro3Id = 'res/room/truth_dare/game_intro_3_id.webp';
  static const String roomTruthDareGameIntroTitleEn = 'res/room/truth_dare/game_intro_title_en.webp';
  static const String roomTruthDareGameIntroTitleId = 'res/room/truth_dare/game_intro_title_id.webp';
  static const String roomTruthDareLightSelector = 'res/room/truth_dare/light_selector.webp';
  static const String roomTruthDareLightSelectorMask = 'res/room/truth_dare/light_selector_mask.webp';
  static const String roomTruthDareMicSeatBg = 'res/room/truth_dare/mic_seat_bg.webp';
  static const String roomTruthDareOngoing = 'res/room/truth_dare/ongoing.webp';
  static const String roomTruthDareStart = 'res/room/truth_dare/start.webp';
  static const String roomTruthDareTopicPanel = 'res/room/truth_dare/topic_panel.webp';
  static const String roomTruthDareWheelBg = 'res/room/truth_dare/wheel_bg.webp';
  static const String roomTurntableGameBetBtnNormal = 'res/room/turntable_game/bet_btn_normal.webp';
  static const String roomTurntableGameBetBtnPressed = 'res/room/turntable_game/bet_btn_pressed.webp';
  static const String roomTurntableGameBgGameDestop = 'res/room/turntable_game/bg_game_destop.webp';
  static const String roomTurntableGameBgGameDestopBottom = 'res/room/turntable_game/bg_game_destop_bottom.webp';
  static const String roomTurntableGameBtnRanking = 'res/room/turntable_game/btn_ranking.webp';
  static const String roomTurntableGameClockNum0 = 'res/room/turntable_game/clock_num_0.webp';
  static const String roomTurntableGameClockNum1 = 'res/room/turntable_game/clock_num_1.webp';
  static const String roomTurntableGameClockNum2 = 'res/room/turntable_game/clock_num_2.webp';
  static const String roomTurntableGameClockNum3 = 'res/room/turntable_game/clock_num_3.webp';
  static const String roomTurntableGameClockNum4 = 'res/room/turntable_game/clock_num_4.webp';
  static const String roomTurntableGameClockNum5 = 'res/room/turntable_game/clock_num_5.webp';
  static const String roomTurntableGameClockNum6 = 'res/room/turntable_game/clock_num_6.webp';
  static const String roomTurntableGameClockNum7 = 'res/room/turntable_game/clock_num_7.webp';
  static const String roomTurntableGameClockNum8 = 'res/room/turntable_game/clock_num_8.webp';
  static const String roomTurntableGameClockNum9 = 'res/room/turntable_game/clock_num_9.webp';
  static const String roomTurntableGameClockNumEmpty = 'res/room/turntable_game/clock_num_empty.webp';
  static const String roomTurntableGameFruitMatchine = 'res/room/turntable_game/fruit_matchine.webp';
  static const String roomTurntableGameIcRankStar = 'res/room/turntable_game/ic_rank_star.webp';
  static const String roomTurntableGameIconHand = 'res/room/turntable_game/icon_hand.webp';
  static const String roomTurntableGameIconHistory = 'res/room/turntable_game/icon_history.webp';
  static const String roomTurntableGameIconQa = 'res/room/turntable_game/icon_qa.webp';
  static const String roomTurntableGameRankTop3Bg = 'res/room/turntable_game/rank_top3_bg.webp';
  static const String roomTurntableGameResultLight = 'res/room/turntable_game/result_light.webp';
  static const String roomTurntableGameResultTop3Bg = 'res/room/turntable_game/result_top3_bg.webp';
  static const String roomTurntableGameRoundBgLamp1 = 'res/room/turntable_game/round_bg_lamp1.webp';
  static const String roomTurntableGameRoundBgLamp2 = 'res/room/turntable_game/round_bg_lamp2.webp';
  static const String roomTurntableGameRoundBtnFrame = 'res/room/turntable_game/round_btn_frame.webp';
  static const String roomTurntableGameRoundCenterItemBg = 'res/room/turntable_game/round_center_item_bg.webp';
  static const String roomTurntableGameWin = 'res/room/turntable_game/win.webp';
  static const String roomTypeBlindDateSel = 'res/room/type_blind_date_sel.webp';
  static const String roomTypeChatSel = 'res/room/type_chat_sel.webp';
  static const String roomTypeCreateBlindDate = 'res/room/type_create_blind_date.webp';
  static const String roomTypeCreateCandy = 'res/room/type_create_candy.webp';
  static const String roomTypeCreateCandySel = 'res/room/type_create_candy_sel.webp';
  static const String roomTypeCreateChat = 'res/room/type_create_chat.webp';
  static const String roomTypeCreateDomino = 'res/room/type_create_domino.webp';
  static const String roomTypeCreateDominoSel = 'res/room/type_create_domino_sel.webp';
  static const String roomTypeCreateLudo = 'res/room/type_create_ludo.webp';
  static const String roomTypeCreateLudoSel = 'res/room/type_create_ludo_sel.webp';
  static const String roomTypeCreateTruthDare = 'res/room/type_create_truth_dare.webp';
  static const String roomTypeCreateUno = 'res/room/type_create_uno.webp';
  static const String roomTypeCreateUnoSel = 'res/room/type_create_uno_sel.webp';
  static const String roomTypeTruthDareSel = 'res/room/type_truth_dare_sel.webp';
  static const String roomUserCardLocation = 'res/room/user_card_location.webp';
  static const String roomUserMicClose = 'res/room/user_mic_close.webp';
  static const String roomUserMicOpen = 'res/room/user_mic_open.webp';
  static const String settingAbout = 'res/setting/about.webp';
  static const String settingAccount = 'res/setting/account.webp';
  static const String settingBgImNotificationSwitch = 'res/setting/bg_im_notification_switch.webp';
  static const String settingBlockedList = 'res/setting/blocked_list.webp';
  static const String settingClearcache = 'res/setting/clearcache.webp';
  static const String settingFateBellMessage = 'res/setting/fate_bell_message.webp';
  static const String settingFb = 'res/setting/fb.webp';
  static const String settingGlobal = 'res/setting/global.webp';
  static const String settingHelp = 'res/setting/help.webp';
  static const String settingInfo = 'res/setting/info.webp';
  static const String settingMessageNotification = 'res/setting/message_notification.webp';
  static const String settingNotification = 'res/setting/notification.webp';
  static const String settingPrivacy = 'res/setting/privacy.webp';
  static const String settingShare = 'res/setting/share.webp';
  static const String settingUpgrade = 'res/setting/upgrade.webp';
  static const String shareBlock = 'res/share/block.png';
  static const String shareChat = 'res/share/chat.png';
  static const String shareDelete = 'res/share/delete.png';
  static const String shareFollow = 'res/share/follow.png';
  static const String shareLogoShareFriends = 'res/share/logo_share_friends.webp';
  static const String shareLogoShareLink = 'res/share/logo_share_link.webp';
  static const String shareMore = 'res/share/more.webp';
  static const String shareMoveTheme = 'res/share/move_theme.png';
  static const String shareReport = 'res/share/report.png';
  static const String svgaAudioPlayingLeft = 'res/svga/audio_playing_left.svga';
  static const String svgaAudioPlayingRight = 'res/svga/audio_playing_right.svga';
  static const String svgaAvatarRippleBlue = 'res/svga/avatar_ripple_blue.svga';
  static const String svgaBarChat = 'res/svga/bar_chat.svga';
  static const String svgaBarHome = 'res/svga/bar_home.svga';
  static const String svgaBarMine = 'res/svga/bar_mine.svga';
  static const String svgaCandyLogo = 'res/svga/candy_logo.svga';
  static const String svgaChatGiftBox = 'res/svga/chat_gift_box.svga';
  static const String svgaDominoLogo = 'res/svga/domino_logo.svga';
  static const String svgaFruitMachineEntryDiamond = 'res/svga/fruit_machine_entry_diamond.svga';
  static const String svgaGameMicrophone = 'res/svga/game_microphone.svga';
  static const String svgaGameSeated = 'res/svga/game_seated.svga';
  static const String svgaGlow = 'res/svga/glow.svga';
  static const String svgaGoodPersonalId = 'res/svga/good_personal_id.svga';
  static const String svgaGoodRoomId = 'res/svga/good_room_id.svga';
  static const String svgaHomeMatchPop = 'res/svga/home_match_pop.svga';
  static const String svgaIconLoading = 'res/svga/icon_loading.svga';
  static const String svgaLuckyBagGotDiamond = 'res/svga/lucky_bag_got_diamond.svga';
  static const String svgaLudoLogo = 'res/svga/ludo_logo.svga';
  static const String svgaMatchBg = 'res/svga/match_bg.svga';
  static const String svgaMatchTitle = 'res/svga/match_title.svga';
  static const String svgaMedalGet01 = 'res/svga/medal_get_01.svga';
  static const String svgaMedalGet02 = 'res/svga/medal_get_02.svga';
  static const String svgaMedalShow = 'res/svga/medal_show.svga';
  static const String svgaRoomMsg = 'res/svga/room_msg.svga';
  static const String svgaRoomSignInGift = 'res/svga/room_sign_in_gift.svga';
  static const String svgaTkBtnChat = 'res/svga/tk_btn_chat.svga';
  static const String svgaTkBtnGame = 'res/svga/tk_btn_game.svga';
  static const String svgaUncoverSuc = 'res/svga/uncover_suc.svga';
  static const String svgaUnoLogo = 'res/svga/uno_logo.svga';
  static const String svgaVoicePlaying = 'res/svga/voice_playing.svga';
  static const String tabbarIconBgTop = 'res/tabbar/icon_bg_top.webp';
  static const String tabbarIconChatSelected = 'res/tabbar/icon_chat_selected.webp';
  static const String tabbarIconChatUnselected = 'res/tabbar/icon_chat_unselected.webp';
  static const String tabbarIconHomeSelected = 'res/tabbar/icon_home_selected.webp';
  static const String tabbarIconHomeUnselected = 'res/tabbar/icon_home_unselected.webp';
  static const String tabbarIconMineSelected = 'res/tabbar/icon_mine_selected.webp';
  static const String tabbarIconMineUnselected = 'res/tabbar/icon_mine_unselected.webp';
  static const String taskBgNewbieDate = 'res/task/bg_newbie_date.webp';
  static const String taskBgNewbieGradient = 'res/task/bg_newbie_gradient.webp';
  static const String taskBgNewbieRecevied = 'res/task/bg_newbie_recevied.webp';
  static const String taskBgNewbieTitle = 'res/task/bg_newbie_title.webp';
  static const String taskBgPage = 'res/task/bg_page.webp';
  static const String taskBgRewardSucc = 'res/task/bg_reward_succ.webp';
  static const String taskBgSignInHeader = 'res/task/bg_sign_in_header.webp';
  static const String taskBluetoothIcon = 'res/task/bluetooth_icon.png';
  static const String taskCompleteOverlayBg = 'res/task/complete_overlay_bg.webp';
  static const String taskCompleteOverlayContentBg = 'res/task/complete_overlay_content_bg.webp';
  static const String taskCompleteOverlayDiamond = 'res/task/complete_overlay_diamond.webp';
  static const String taskCompleteOverlayNum = 'res/task/complete_overlay_num.webp';
  static const String taskCompleteOverlayStar = 'res/task/complete_overlay_star.webp';
  static const String taskIconCoin0 = 'res/task/icon_coin_0.webp';
  static const String taskIconCoin1 = 'res/task/icon_coin_1.webp';
  static const String taskIconCoin2 = 'res/task/icon_coin_2.webp';
  static const String taskIconCoin3 = 'res/task/icon_coin_3.webp';
  static const String taskIconCoin4 = 'res/task/icon_coin_4.webp';
  static const String taskIconCoin5 = 'res/task/icon_coin_5.webp';
  static const String taskIconCoin6 = 'res/task/icon_coin_6.webp';
  static const String taskIconCoin7 = 'res/task/icon_coin_7.webp';
  static const String taskIconCoin8 = 'res/task/icon_coin_8.webp';
  static const String taskIconCoin9 = 'res/task/icon_coin_9.webp';
  static const String taskIconCoinAdd = 'res/task/icon_coin_add.webp';
  static const String taskIconCoinDot = 'res/task/icon_coin_dot.webp';
  static const String taskIconCoinK = 'res/task/icon_coin_k.webp';
  static const String taskIconNewbieClose = 'res/task/icon_newbie_close.webp';
  static const String taskIconSignInCheck = 'res/task/icon_sign_in_check.webp';
  static const String taskIconStar0 = 'res/task/icon_star_0.webp';
  static const String taskIconStar1 = 'res/task/icon_star_1.webp';
  static const String taskIconStar2 = 'res/task/icon_star_2.webp';
  static const String taskIconStar3 = 'res/task/icon_star_3.webp';
  static const String taskIconStar4 = 'res/task/icon_star_4.webp';
  static const String taskIconStar5 = 'res/task/icon_star_5.webp';
  static const String taskIconStar6 = 'res/task/icon_star_6.webp';
  static const String taskIconStar7 = 'res/task/icon_star_7.webp';
  static const String taskIconStar8 = 'res/task/icon_star_8.webp';
  static const String taskIconStar9 = 'res/task/icon_star_9.webp';
  static const String taskIconStarAdd = 'res/task/icon_star_add.webp';
  static const String taskIconStarDot = 'res/task/icon_star_dot.webp';
  static const String taskIconStarK = 'res/task/icon_star_k.webp';
  static const String taskImgDailyTask = 'res/task/img_daily_task.webp';
  static const String taskImgNewbieHeader = 'res/task/img_newbie_header.webp';
  static const String taskImgSignInAccept = 'res/task/img_sign_in_accept.webp';
  static const String taskSignInClose = 'res/task/sign_in_close.webp';
  static const String taskSignInRewardCoin = 'res/task/sign_in_reward_coin.webp';
  static const String taskSuspendedBkg = 'res/task/suspended_bkg.png';
  static const String taskSuspendedGift = 'res/task/suspended_gift.png';
  static const String taskTaskExp = 'res/task/task_exp.webp';
  static const String titleAdd = 'res/title/add.webp';
  static const String titleEffectPreviewBg = 'res/title/effect_preview_bg.webp';
  static const String titleExpand = 'res/title/expand.webp';
  static const String upgradeChecking = 'res/upgrade/checking.png';
  static const String upgradeHeader = 'res/upgrade/header.png';
  static const String userHBadgeAg = 'res/user_h/badge_ag.webp';
  static const String userHBadgeAgUnselected = 'res/user_h/badge_ag_unselected.webp';
  static const String userHBadgeAu = 'res/user_h/badge_au.webp';
  static const String userHBadgeAuUnselected = 'res/user_h/badge_au_unselected.webp';
  static const String userHBadgeCu = 'res/user_h/badge_cu.webp';
  static const String userHBadgeCuUnselected = 'res/user_h/badge_cu_unselected.webp';
  static const String userHBadgeDetailClose = 'res/user_h/badge_detail_close.webp';
  static const String userHBadgeDia = 'res/user_h/badge_dia.webp';
  static const String userHBadgeDiaUnselected = 'res/user_h/badge_dia_unselected.webp';
  static const String userHBgBadgeInfoRank = 'res/user_h/bg_badge_info_rank.webp';
  static const String userHBgBadgeNav = 'res/user_h/bg_badge_nav.jpg';
  static const String userHBgBadgeRanking = 'res/user_h/bg_badge_ranking.webp';
  static const String userHBgBadgeRanking1 = 'res/user_h/bg_badge_ranking1.webp';
  static const String userHBgBadgeRanking2 = 'res/user_h/bg_badge_ranking2.webp';
  static const String userHBgBadgeRanking3 = 'res/user_h/bg_badge_ranking3.webp';
  static const String userHBgBadgeRule = 'res/user_h/bg_badge_rule.webp';
  static const String userHBgBadgeState = 'res/user_h/bg_badge_state.webp';
  static const String userHBgBadgeWear = 'res/user_h/bg_badge_wear.webp';
  static const String userHBgGradient = 'res/user_h/bg_gradient.webp';
  static const String userHEmptyBadge = 'res/user_h/empty_badge.webp';
  static const String userHIcOfficial = 'res/user_h/ic_official.webp';
  static const String userHIconBadgeEmpty = 'res/user_h/icon_badge_empty.webp';
  static const String userHIconBadgeRankTips = 'res/user_h/icon_badge_rank_tips.webp';
  static const String userHIconBadgeRankTop1 = 'res/user_h/icon_badge_rank_top1.webp';
  static const String userHIconBadgeRankTop2 = 'res/user_h/icon_badge_rank_top2.webp';
  static const String userHIconBadgeRankTop3 = 'res/user_h/icon_badge_rank_top3.webp';
  static const String userHIconLeft = 'res/user_h/icon_left.webp';
  static const String userHIconRight = 'res/user_h/icon_right.webp';
  static const String userHIconSaveGameRecord = 'res/user_h/icon_save_game_record.webp';
  static const String userHIconStar = 'res/user_h/icon_star.webp';
  static const String userHIconStarWhite = 'res/user_h/icon_star_white.webp';
  static const String userHIconTitleEdit = 'res/user_h/icon_title_edit.webp';
  static const String userHImgBadgeChatInRoom = 'res/user_h/img_badge_chat_in_room.webp';
  static const String userHImgBadgeInfo = 'res/user_h/img_badge_info.webp';
  static const String userHImgBadgeMiniCard = 'res/user_h/img_badge_mini_card.webp';
  static const String userHImgBadgeUserListRoom = 'res/user_h/img_badge_user_list_room.webp';
  static const String walletIconDetail = 'res/wallet/icon_detail.webp';
  static const String walletIconDetailArraw = 'res/wallet/icon_detail_arraw.webp';
  static const String walletIconExchangeSucCoin = 'res/wallet/icon_exchange_suc_coin.webp';
}
