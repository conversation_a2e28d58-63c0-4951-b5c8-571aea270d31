import 'package:flutter/material.dart';
import 'package:service/service.dart';

/// 需要鉴权
const R_AUTH = '/auth';

const R_MAIN = '/main';

const R_APP_UPGRADE = '/app_upgrade';

/// 通用透明网页
const R_COMMON_WEB_PAGE = '/common_web_page';

const R_LIVE_COMMON_WEB_DIALOG = "$R_LIVE/common_web_dialog";

/// 高度比例
const P_HEIGHT_RATIO = 'height_ratio';

/// 最小高度
const P_MIN_HEIGHT = 'min_height';

const P_IMAGE_FILE = "game_record_share_image";

/// -------------------------- 用户相关 -----------------------------------
const R_USER = '/user';

/// 注册新用户的新手引导
const R_USER_REG_QA = '$R_USER/reg_qa';

/// 用户注册邀请码页面
const R_USER_REG_INVITATION_CODE = '$R_USER/invitation_code';

///用户信息
const R_USER_INFO = '$R_USER/info';

///用户关于
const R_ABOUT = '$R_USER/about';

///用户昵称
const R_USER_NAME = '$R_USER/name';

///用户生日
const R_USER_BIRTH = '$R_USER/birthday';

///性别
const R_USER_SEX = '$R_USER/sex';

///简况:昵称/出生日期
const R_USER_PROFILE = '$R_USER/profile';

///头像
const R_USER_AVATAR = '$R_USER/avatar';

/// 照片转头像主页
const R_USER_PHOTO_AVATAR = '$R_USER/photo_avatar';

/// 照片转头像结果
const R_USER_PHOTO_AVATAR_RESULT = '$R_USER/photo_avatar_result';

/// 头像历史
const R_USER_AVATAR_HISTORY = '$R_USER/avatar_history';

///兴趣爱好
const R_USER_PASSIONS = '$R_USER/passions';

///修改昵称
const R_USER_EDIT_NAME = '$R_USER/edit_name';

///关注列表/粉丝列表/访客列表
const R_USER_FRIENDS = '$R_USER/friends';

///问题
const R_USER_QUESTION = '$R_USER/question';

/// 钱包: 路由下标，钻石：0，金币：1，宝石：2
const R_USER_WALLET = '$R_USER/wallet';

/// 钱包-金币页面
const R_USER_WALLET_GOLD = '$R_USER/gold';

/// 钱包-收益页面
const R_USER_INCOME = '$R_USER/income';

/// 钱包-收益任务页面
const R_USER_INCOME_TASK = '$R_USER/income_task';

/// 钱包流水
const R_USER_WALLET_RECORD = '$R_USER/wallet_record';

/// 座驾墙
const R_USER_CAR_WALL = '$R_USER/car_wall';

/// 礼物墙
const R_USER_GIFT_WALL = '$R_USER/gift_wall';

/// 资产说明
const R_USER_ASSET_DATA = '$R_USER/asset_data';

/// bio身份卡片
const R_USER_BIO_CARD = '$R_USER/bio_card';

/// 用户等级
const R_USER_LEVEL = '$R_USER/level';

/// 名牌列表
const R_USER_TITLE_LIST = '$R_USER/title_list';

/// 名牌编辑
const R_USER_TITLE_EDIT = '$R_USER/title_edit';

/// 勋章
const R_USER_BADGE = '$R_USER/badge';

/// 勋章排行榜
const R_BADGE_RANKING = '$R_USER/badge_ranking';

/// 勋章佩戴
const R_USER_BADGE_WEAR = '$R_USER/badge_wear';

/// 勋章说明页
const R_USER_BADGE_ABOUT = '$R_USER/badge_about';

const R_USER_MINI_CARD = '$R_USER/user_card';

/// 支持者排行榜
const R_USER_SUPPORTER = '$R_USER/supporter';

///推荐关注
const R_USER_RECOMMEND_FOLLOW = '$R_USER/recommend_follow';

const R_USER_MEPAGE = '$R_USER/me_page';

/// 申请加入公会
const R_APPLY_AGENCY = '$R_USER/apply_agency';

const R_USER_PRIVATE_PHOTO = '$R_USER/private_photo';

/// -------------------------- 登录注册 -----------------------------------

const R_SPLASH = '/';

/// 登陆
const R_LOGIN = '/login';
const R_LOGIN_CS = '/login/cs';
const R_LOGIN_PHONE = '/login/phone';
const R_LOGIN_VERIFY_PHONE = '/login/verify_phone';
const R_LOGIN_SELECT_COUNTRY = '/login/select_country';

/// -------------------------- 广场动态 -----------------------------------
const R_MOMENTS = '/moments';

///动态详情
const R_MOMENTS_DETAIL = '$R_MOMENTS/detail';

///发布动态
const R_MOMENTS_PUBLISH = '$R_MOMENTS/publish';

///动态信箱
const R_MOMENTS_NOTICES = '$R_MOMENTS/notices';
const R_MOMENTS_MEDIAS = '$R_MOMENTS/medias';

/// 话题搜索
const R_HASHTAG_SEARCH = '$R_MOMENTS/hashtag_search';

/// 话题详情
const R_HASHTAG_DETAIL = '$R_MOMENTS/hashtag_detail';

const R_MOMENTS_CREATE_CUSTOM_THEME = '$R_MOMENTS/create_custom_theme';

/// -------------------------- IM ---------------------------------------
/// 聊天页面
const R_CHAT = '/chat';

/// 会话列表
const R_CHAT_LIST = '$R_CHAT/list';

/// 会话设置
const R_CHAT_SETTINGS = '$R_CHAT/settings';

/// 图片预览页
const R_CHAT_PREVIEW_IMAGE = '$R_CHAT/preview_image';

/// 视频预览页
const R_CHAT_PREVIEW_VIDEO = '$R_CHAT/preview_video';

/// 联系人、会话搜索
const R_CHAT_SEARCH = '$R_CHAT/search';

/// 联系人搜索
const R_CHAT_SEARCH_CONTACT = '$R_CHAT/search_contact';

/// 会话搜索
const R_CHAT_SEARCH_CHAT = '$R_CHAT/search_chat';

/// 好友申请
const R_CHAT_FRIENDS_REQUEST = '$R_CHAT/friends_request';

/// 联系人列表
const R_CONTACT_LIST = '$R_CHAT/contact_list';

/// 问题盒子答案
const R_QUESTION_BOX_ANSWER = '$R_CHAT/box_answer';

/// 表情详情
const R_STICKER_DETAIL = '$R_CHAT/sticker_detail';

/// 表情集合
const R_STICKER_COLLECTIONS = '$R_CHAT/sticker_collections';

/// 集合详情
const R_COLLECTION_DETAIL = '$R_CHAT/collection_detail';

/// 安全模式页面
const R_CHAT_MODE = '$R_CHAT/chat_mode';

/// 红包
const R_CHAT_MODE_LUCKY_BAG = "$R_CHAT/lucky_bg";

/// 开金库
const R_CHAT_TREASURE_DIALOG = "$R_CHAT/treasure_dialog";
const R_CHAT_TREASURE_REWARD_DIALOG = "$R_CHAT/treasure_reward_dialog";
const R_CHAT_TREASURE_RANKING = "$R_CHAT/treasure_ranking";

/// 宝箱
const R_CHAT_MAGIC_BOX_DIALOG = "$R_CHAT/magic_box_dialog";

/// -------------------------- 设置页面 -----------------------------------
const R_SETTINGS = '/settings';
const R_SETTINGS_LANGUAGE = '$R_SETTINGS/lan';
const R_SETTINGS_ACCOUNT = '$R_SETTINGS/account';
const R_SETTINGS_BLOCKED = '$R_SETTINGS/blocked';
const R_SETTINGS_ABOUT = '$R_SETTINGS/about';
const R_SETTINGS_DELETE_ACCOUNT = '$R_SETTINGS/delete_account';
const R_SETTINGS_MESSAGE_NOTIFICATION = '$R_SETTINGS/message_notification';
const R_SETTINGS_VIP = '$R_SETTINGS/vip_setting';
const R_SETTINGS_VIP_CHAT = '$R_SETTINGS/vip_chat_setting';
const R_SETTINGS_VIP_MYSTERIOUS = '$R_SETTINGS/vip_mysterious_setting';
const R_SETTINGS_PREMIUM = '$R_SETTINGS/premium_setting';
const R_SETTINGS_FATE_BELL = '$R_SETTINGS/fate_bell_setting';
///删除账号界面
///删除账号确认页面，需要填写理由
const R_SETTINGS_DELETE_CONFIRM = "$R_SETTINGS/delete_confirm";

///删除账号资产确认页面
const R_SETTINGS_DELETE_CONFIRM_ASSETS = "$R_SETTINGS/delete_confirm_assets";
const R_SETTINGS_GUIDE_LANGUAGE = '$R_SETTINGS/guide_lan';
const R_SETTINGS_REPORT = '$R_SETTINGS/report';

const R_SETTINGS_REPORT_USER = '$R_SETTINGS/report_user';

///完善资料页面
const R_SETTINGS_EDIT_PROFILE = '$R_SETTINGS/edit_profile';

/// xfile资源预览界面
const R_XFILE_PREVIEW_PAGE = '/xfile_preview_page';

/// -------------------------- 漂流瓶 ----------------------------------------
const R_BOTTLE = '/bottle';
const R_WISH_BOTTLE = '$R_BOTTLE/wish_bottle_page';
const R_BOTTLE_PROGRESS = '$R_BOTTLE/bottle_progress';
const R_BOTTLE_SETTING = '$R_BOTTLE/setting';
const R_BOTTLE_CHATS = '$R_BOTTLE/chats';
const R_BOTTLE_THROW = '$R_BOTTLE/throw';

/// --------------------------- H5 ----------------------------------------
const R_WEB = '/web';
const R_WEB_DIALOG = '/web_dialog';

/// -------------------------- 原生注册 ------------------------------------
/// 原生页面
const R_CHAT_VIDEO = '/chat/video';
const R_CHAT_AUDIO = '/chat/audio';

/// -------------------------- 其他 ----------------------------------------
/// 测试页面
const R_TEST = '/test';

/// 测试游戏
const R_TEST_GAME = '$R_TEST/game';

/// 彩蛋页面
const R_EASTER_EGG = '/easter_egg';

/// 商业化折扣弹窗
const R_COMMERCIAL_DISCOUNT_DIALOG = "commercial/discount_dialog";

/// 活动
const R_ACTIVITY_LIST = '/activity/list';

/// -------------------------- 声音认证 -----------------------------------------
const R_CERTIFY = '/verify';
const R_VOICE_VERIFY = '/voice/verify';
const R_VOICE_VERIFY_POPUP = '/voice/verify_popup';
const R_VOICE_REVIEW = '/voice/review';
const R_VOICE_DATING = '/voice/dating';
const R_VOICE_SHARE = '/voice/share';
const R_VOICE_VERIFY_INTERCEPT_POPUP = '/voice/verify_intercept_popup';
/// -------------------------- 匹配 -----------------------------------------
const R_MATCH = '/match';
const R_CHOOSE_MATCH = '/choose_match';
const R_SET_MATCH_MODE = '$R_MATCH/set_match_mode';
const R_MATCH_ROOM_DIALOG = '/match_room_dialog';

/// -------------------------- 声音匹配 -----------------------------------------
const R_VOICE_MATCH = '/voice/match';
const R_VOICE_RECORD_START = '/voice/record_start';
const R_VOICE_RECORD = '/voice/record';
const R_VOICE_BACKGROUND = '/voice/background';
const R_VOICE_MATCH_PROGRESS = '/voice/match_progress';

/// -------------------------- 语音匹配 -----------------------------------------
const R_VOICE_CALL = '/voice_call';
const R_VOICE_MATCH_INITIATE = '$R_VOICE_CALL/match_initiate';
const R_VOICE_MATCH_SETTING = '$R_VOICE_CALL/match_setting';
const R_VOICE_MATCH_RULE = '$R_VOICE_CALL/match_rule';
const R_VOICE_MATCHING = '$R_VOICE_CALL/matching';
const R_VOICE_MATCH_CALLING = '$R_VOICE_CALL/match_calling';

/// -------------------------- 任务 -----------------------------------------
const R_TASK_LIST = '/task/list';

/// -------------------------- FAQ -----------------------------------------
const R_FAQ = '/faq';
const R_FEEDBACK = '/feedback';
const R_FEEDBACK_DETAIL = '$R_FEEDBACK/detail';

/// -------------------------- 商城 -----------------------------------------
const R_MALL = '/mall';
const R_MALL_BACKPACK = '$R_MALL/backpack';
const R_MALL_SEND_FRIEND = '$R_MALL/send';
const R_MALL_SEARCH_ID = '$R_MALL/search_id';
const R_MALL_PREVIEW_GOODS = '$R_MALL/preview_goods';
const R_MALL_TYPE_GOODS_LIST = '/mall_type_goods_list';

/// -------------------------- 充值 -----------------------------------------
const R_RECHARGE_SHEET = '/recharge_sheet';

/// -------------------------- 优惠券 -----------------------------------------
const R_COUPON = '/coupon';
const R_COUPON_BAG = '$R_COUPON/bag';
const R_COUPON_RECORDS = '$R_COUPON/records';

/// -------------------------- 直播 -----------------------------------------
const R_LIVE = '/live';

/// -------------------------- vip -----------------------------------------
const R_VIP = '/vip';
const R_VIP_RECHARGE = '$R_VIP/recharge';
const R_VIP_RULE = '$R_VIP/rule';

/// 用与路由跳转
const R_LIVE_ROME_JOIN = '$R_LIVE/room_join';


/// ------- bs 游戏------
const R_BS_GAME = "/bs_game";
const R_BS_GAME_DIALOG = "/bs_game_dialog";
const R_BS_GAME_TURNTABLE = "/bs_game/turntable";

/// 此路由不允许push！！！！！

const R_LIVE_LIVE_ROME = '$R_LIVE/live_room';
const R_LIVE_ROOM_SETTING = '$R_LIVE/room_setting';
const R_LIVE_PWD = '$R_LIVE/room_password';
const R_LIVE_BOTTOM_INPUT_DIALOG = '$R_LIVE/bottom_input_dialog';
const R_LIVE_ROOM_SEARCH = '$R_LIVE/room_search';
/// 上麦类型选择Dialog
const R_LIVE_MIC_TYPE = '$R_LIVE/room_mic_type';

/// 图片预览页
const R_LIVE_PREVIEW_IMAGE = '$R_LIVE/preview_image';
const R_LIVE_VIDEO_FULL_SCREEN = '$R_LIVE/video_full_screen';

/// 房间等级
const R_LIVE_ROOM_LEVEL = '$R_LIVE/room_level';

/// 房间等级
const R_LIVE_ROOM_TRUTH_GAME_INTRODUCTION = '$R_LIVE/truth_game_introduction';

/// 获取到物品弹窗
const R_LIVE_ROOM_REWARD_PROBABILITY_Gift_DIALOG = '$R_LIVE/reward_probability_gift_dialog';

const R_LIVE_ROOM_SEND_GIFT_GUIDE_DIALOG = '$R_LIVE/send_gift_guide_dialog';

/// -------------------------- 活动 -----------------------------------------
const R_EVENT = '/event';

const R_EVENT_LIST = '$R_EVENT/list';
const R_EVENT_CREATE = '$R_EVENT/create';
// const R_EVENT_CREATE_FIRST = '$R_EVENT_CREATE/first';
// const R_EVENT_CREATE_SECOND = '$R_EVENT_CREATE/second';
// const R_EVENT_CREATE_THIRD = '$R_EVENT_CREATE/third';
// const R_EVENT_CREATE_FOURTH = '$R_EVENT_CREATE/fourth';
// const R_EVENT_CREATE_SELECT = '$R_EVENT_CREATE/select';
/// 规则说明页
const R_EVENT_RULE = '$R_EVENT/rule';

/// --------------------------主题聚合 -----------------------------------------
const R_THEME = '/theme';
const R_THEME_LIST = '$R_THEME/list';


/// 详情
const R_EVENT_DETAIL = '$R_EVENT/detail';

/// 详情修改
const R_EVENT_EDIT = '$R_EVENT/edit';

/// tag活动列表
// const R_EVENT_TAG_DETAIL = '$R_EVENT/tag_detail';

/// -------------------------- 蒙面匹配 -----------------------------------------
const R_MASKED_MATCH = '/masked_match';

/// -------------------------- CP系统 -----------------------------------------
const R_CP = '/cp';

/// 亲密值详情
const R_CP_INTIMACY_INFO = '$R_CP/intimacy_info';

/// 亲密值流水
const R_CP_INTIMACY_TURNOVER = '$R_CP/intimacy_turnover';

/// 小窝
const R_CP_LOVE_ZONE = '$R_CP/love_zone';

/// 小窝说明
const R_CP_LOVE_ZONE_EXPLAIN = '$R_CP/love_zone_explain';

/// 小窝纪念日
const R_CP_ANNIVERSARY = '$R_CP/anniversary';

/// 小窝纪念日
const R_CP_EDIT_ANNIVERSARY = '$R_CP/edit_anniversary';

const R_CP_ANNIVERSARY_DETAIL = '$R_CP/anniversary_detail';

/// 戒指盒
const R_CP_RING_BOX = '$R_CP/ring_box';

/// 添加戒指盒
const R_CP_ADD_RING = '$R_CP/add_ring';

/// 求婚
const R_CP_PROPOSAL = '$R_CP/proposal';

/// 教堂
const R_CP_CHURCH = '$R_CP/church';

/// 教堂说明页
const R_CP_CHURCH_EXPLAIN = '$R_CP_CHURCH/explain';

/// 消息
const R_CP_MSG = '$R_CP/msg';

/// 选择求婚对象
const R_CP_SELECT_SUITOR = '$R_CP_PROPOSAL/select_suitor';

/// 选择求婚戒指
const R_CP_SELECT_RING = '$R_CP_PROPOSAL/select_ring';

/// 求婚誓言
const R_CP_PROPOSAL_VOWS = '$R_CP_PROPOSAL/proposal_vows';

/// 选择求婚函
const R_CP_PROPOSAL_LETTER = '$R_CP_PROPOSAL/proposal_letter';

/// 求婚函详情
const R_CP_PROPOSAL_DETAIL = '$R_CP/proposal_detail';

/// 离婚
const R_CP_DIVORCE = '$R_CP/divorce';

/// 红包雨详情
const R_EVENT_LUCKY_BAG_DETAIL = '$R_EVENT/lucky_bag_detail';

/// -------------------------- 亲密度 -----------------------------------------
const R_INTIMATE = '/intimate';

/// 亲密度排行，参数
/// [P_ID] 查看谁的排行
const R_INTIMATE_RANKING = '$R_INTIMATE/ranking';
const R_INTIMATE_ANIMATION = '$R_INTIMATE/animation';

/// dialog
const R_LIVE_ROOM_INFO_DIALOG = "$R_LIVE/room_info_dialog";
const R_LIVE_ROOM_ONLINE_DIALOG = "$R_LIVE/room_online_dialog";
const R_LIVE_ROOM_GIFT = "$R_LIVE/gift";
const R_LIVE_ROOM_MUSIC = "$R_LIVE/music";
const R_LIVE_ROOM_MUSIC_LOCAL = "$R_LIVE/music/local";
const R_LIVE_ROOM_INVITE_MIC = "$R_LIVE/invite_mic";
const R_LIVE_ROOM_EMOTION = "$R_LIVE/emotion";
const R_LIVE_ROOM_MORE = "$R_LIVE/more";
const R_LIVE_ROOM_SHARE = "$R_LIVE/share";
const R_LIVE_ROOM_MENU = "$R_LIVE/menu";
const R_LIVE_GAME_EXIT = "$R_LIVE/game_exit";
const R_LIVE_MIC_OPERATE = "$R_LIVE/mic_operate";
const R_LIVE_ROOM_CREATE_DIALOG = "$R_LIVE/create_dialog";
const R_LIVE_ROOM_EDIT_DIALOG = "$R_LIVE/edit_room_dialog";
const R_LIVE_ROOM_RANK_DIALOG = "$R_LIVE/rank_dialog";
const R_CHAT_GIFT = "$R_CHAT/gift";
const R_LIVE_MIC_APPLY_LIST = "$R_LIVE/mic_apply";
const R_LIVE_ROOM_ACTIVITY_DIALOG = "$R_LIVE/activity_dialog";
const R_LIVE_ROOM_TURNTABLE_DIALOG = "$R_LIVE/turntable_dialog";
const R_LIVE_ROOM_LUCKY_WHEEL = "$R_LIVE/lucky_wheel_dialog";
const R_LIVE_ROOM_LUCKY_WHEEL_START = "$R_LIVE/lucky_wheel_start_dialog";
const R_LIVE_ROOM_LUCKY_WHEEL_RULE = "$R_LIVE/lucky_wheel_rule_dialog";
const R_LIVE_ROOM_LUCKY_WHEEL_RESULT = "$R_LIVE/lucky_wheel_result_dialog";
const R_LIVE_ROOM_GIFT_AWARD_RESULT_DIALOG = "$R_LIVE/gift_award_result_dialog";
const R_LIVE_ROOM_TURNTABLE_GAME = "$R_LIVE/turntable_game";
const R_LIVE_ROOM_UPLOAD_MUSIC = "$R_LIVE/music/wifiUpload";
const R_REQUEST_PERMISSION = '/request/permission';
const R_LIVE_ROOM_CALCULATOR = "$R_LIVE/calculator";
const R_LIVE_ROOM_LUCKY_BAG = "$R_LIVE/lucky_bg";
const R_LIVE_ROOM_LUCKY_BAG_INSTRUCTION = "$R_LIVE/lucky_bg_instruction";
const R_LUCKY_BAG_RECEIVE = "lucky_bg_receive";
const R_LIVE_ROOM_CP_MATCH = "$R_LIVE/cp_match";
const R_LIVE_ROOM_CP_MATCH_SUC = "$R_LIVE/cp_match_suc";
const R_LIVE_ROOM_CP_MATCH_RULE = "$R_LIVE/cp_match_rule";
const R_LIVE_ROOM_PK_PERFORM = "$R_LIVE/pk/perform_pk";
const R_LIVE_ROOM_RECHARGE_DISCOUNT = "$R_LIVE/recharge_discount";
const R_LIVE_ROOM_PK_RESULT = "$R_LIVE/pk/result";
const R_LIVE_ROOM_PK_INVITE = "$R_LIVE/pk/invite";
const R_LIVE_ROOM_PK_INVITATION = "$R_LIVE/pk/invitation";
const R_LIVE_ROOM_PK_INVITATION_REJECT = "$R_LIVE/pk/invitation/reject";
const R_FAMILY_MEMBER_SETTING_OPERATION = "$R_FAMILY/member_setting_opr";
const R_SHARE_GUIDE_DIALOG = "/share_guide";
const R_SELECT_DURATION_DIALOG = "$R_LIVE/duration_dialog";
const R_SELECT_PK_RESULT_DIALOG = "$R_LIVE/pk_result_dialog";
const R_SELECT_PK_RECORD_DIALOG = "$R_LIVE/pk_record_picker";
const R_LIVE_ROOM_TASK_DIALOG = "$R_LIVE/room_task_dialog";
const R_LIVE_ROOM_EVENT_SHARE = "$R_LIVE/event_share";
const R_LIVE_ROOM_TALENT_CONTESTANT = "$R_LIVE/talent_contestant_manage";
const R_LIVE_ROOM_CONTESTANT_OPERATE = "$R_LIVE/talent_contestant_operate";
const R_LIVE_ROOM_CONTESTANT_INVITE = "$R_LIVE/talent_contestant_invite";
const R_LIVE_ROOM_TALENT_RULE = "$R_LIVE/talent_rule";
const R_CP_ANNIVERSARY_SHARE = "$R_CP/anniversary_share";
const R_LIVE_ROOM_CUSTOM_TURNTABLE_SET = "$R_LIVE/custom_turntable/set";
const R_LIVE_ROOM_CUSTOM_TURNTABLE_FAQ = "$R_LIVE/custom_turntable/faq";
const R_LIVE_ROOM_BACKGROUND_MUSIC = "$R_LIVE/background_music";
const R_LIVE_ROOM_MEMBER_LIST = '$R_LIVE/member_list_dialog';
const R_LIVE_ROOM_GIFT_SELECT_USER_LIST = '$R_LIVE/gift_select_user_dialog';
const R_LIVE_ROOM_FANS_TASK = '$R_LIVE/fans_task_dialog';
const R_LIVE_ROOM_FANS_DETAIL_DIALOG = '$R_LIVE/fans_club_detail_dialog';
const R_LIVE_ROOM_REWARD_H5_DIALOG = '$R_LIVE/fan_club_reward_h5_dialog';
const R_LIVE_ROOM_RED_PACK_DIALOG = '$R_LIVE/red_pack_dialog';
const R_LIVE_ROOM_JOIN_FANS_GUIDE_DIALOG = '$R_LIVE/join_fans_guide_dialog';
const R_LIVE_ROOM_FANS_META_LIST = '$R_LIVE/fans_meta_list';
const R_LIVE_ROOM_FANS_NAME_EDIT_DIALOG = '$R_LIVE/fans_name_edit_dialog';
const R_LIVE_ROOM_SIGN_IN_REWARD_DIALOG = "$R_LIVE/sign_in_reward_dialog";
const R_LIVE_ROOM_COCOS_GAME = "$R_LIVE/cocos_game";
const R_LIVE_ROOM_GAME_CENTER_DIALOG = "$R_LIVE/game_center_dialog";
const R_LIVE_ROOM_WILL_CLOSE_DIALOG = "$R_LIVE/will_close_dialog";

const R_COCOAS_GAME_RESULT_DIALOG = "cocoas_game_result_dialog";

const R_VIP_UPGRADE = "$R_VIP/upgrade";
const R_VIP_SETTLED = "$R_VIP/settled";
const R_LIVE_DRAW_GUESS_ROUND_RESULT = "$R_LIVE/draw_guess_round";
const R_LIVE_SELECT_ACTIVITY_DIALOG = "$R_LIVE/select_activity_dialog";

const R_RECHARGE_DIALOG = "/recharge_dialog";
const R_STARLIGHT_EXCHANGE_DIALOG = "/starlight_exchange_dialog";

/// 首页发状态列表
const R_HOME_STATE_LIST ='/home_state_list';
/// 签到面板
const R_DAILY_REWARD_DIALOG ='/daily_reward_dialog';

/// 相亲房玩法说明
const R_BLIND_DATE_INTRO_DIALOG ='$R_LIVE/blind_date_intro_dialog';

/// 房间魅力值财富值等级提升弹框
const R_CHARM_WEALTH_UPDATE_DIALOG ='$R_LIVE/r_charm_wealth_update_dialog';

const R_GIFT_GALLERY_LIST_DIALOG = '$R_LIVE/r_gift_gallery_list_dialog';

const R_GIFT_GALLERY_DETAIL_DIALOG = '$R_LIVE/r_gift_gallery_detail_dialog';
const R_GIFT_GALLERY_RULE_DIALOG = '$R_LIVE/r_gift_gallery_rule_dialog';

const R_GIFT_GALLERY_RANK_DIALOG = '$R_LIVE/r_gift_gallery_rank_dialog';

/// -------------------------- 家族 -----------------------------------------
const R_FAMILY = '/family';
const R_FAMILY_CREATE = "$R_FAMILY/create";
const R_FAMILY_HOME = "$R_FAMILY/home";
const R_FAMILY_SEARCH = "$R_FAMILY/search";
const R_FAMILY_DETAIL = "$R_FAMILY/detail";
const R_FAMILY_SETTING = "$R_FAMILY/setting";
const R_FAMILY_MEMBERS = "$R_FAMILY/members";
const R_FAMILY_REQUEST = "$R_FAMILY/request";
const R_FAMILY_TREASURY = "$R_FAMILY/treasury";
const R_FAMILY_TASK = "$R_FAMILY/task";
const R_FAMILY_LEVEL = "$R_FAMILY/level";
const R_FAMILY_MANAGEMENT = "$R_FAMILY/management";
const R_FAMILY_LUCKY_BAG = "$R_FAMILY/lucky_bag";
const R_FAMILY_SEND_LUCKY_BAG = "$R_FAMILY/send_lucky_bag";
const R_FAMILY_LUCKY_BAG_RECORD = "$R_FAMILY/lucky_bag_record";
const R_FAMILY_LUCKY_BAG_DETAIL = "$R_FAMILY/lucky_bag_detail";

/// -------------------------- 游戏中心 -----------------------------------------
const R_GAME_CENTER = '/game_center';
const R_GAME_TASK_LIST_DIALOG = '$R_GAME_CENTER/task_list_dialog';

/// -------------------------- 测试 -----------------------------------------
const R_QUIZ = '/quiz';
const R_QUIZ_GUIDE = '/quiz/guide';
const R_QUIZ_LIST = '/quiz/list';
const R_QUIZ_NATURE = '/quiz/nature';

/// -------------------------- 爵位 -----------------------------------------
const R_PREMIUM = '/premium';
const R_PREMIUM_INFO = '$R_PREMIUM/info';
const R_PREMIUM_INFO_DIALOG = '$R_PREMIUM/info_dialog';

/// -------------------------- 附近的人 -----------------------------------------
const R_NEARBY = '/nearby';

/// -------------------------- 首页 -----------------------------------------
const R_HOME = '/home';
const R_HOME_WINK_LIST = '$R_HOME/wink_list';

/// -------------------------- 首页 -----------------------------------------
/// 全站排行榜
const R_RANK = '/rank';

/// -------------------------- 分享弹窗 -----------------------------------------
const R_SHARE_DIALOG = '/dialog/share';


/// -------------------------- 路由参数key -----------------------------------
/// 路由参数 必须都是string
const P_ID = 'id';
const P_UID = 'uid';
const P_TYPE = 'type';
const P_TARGET_ID = 'target_id';
const P_NEED_VERIFY = 'need_verify'; // '0', '1', 任务跳转前是否需要请求接口，1代表需要
const P_ROOM_ID = 'room_id';
const P_URL = 'url';
const P_CONTENT = 'content';
const P_IS_KICK_OUT = 'is_kick_out'; //'0'，'1'
const P_UNLOCK_TIME = 'unlock_time'; // 解封时间戳(秒)，-1代表永久封禁
const P_IS_MATCH = 'is_match'; //'0'，'1'
const P_RECOMMEND_TYPE = 'recommend_type'; //1主播端匹配
const P_INDEX = 'index'; //'1','2','3','4'
const P_TAB_NAME = 'tab_name';
const P_LIVE_LIST_INDEX = 'room_list_index'; // 房间列表下标
const P_MOMENT_ID = 'moment_id';
const P_PAGE = 'page';
const P_KEYWORD = 'keyword'; // 搜索关键字
const P_COUNTRY_CODE = 'country_code'; //地区手机区号
const P_PHONE = 'phone'; //手机号
const P_GROUP = 'group';
const P_VOICE_QUESTION_LIST = 'voice_question_list'; //声音问题信息列表，包括背景音乐，题库等全部信息
const P_SUBJECT_LIST = 'subject_list'; //问题列表
const P_VOICE_INFO_LIST = 'voice_info_list'; //声音问题列表
const P_HAS_RECORD = 'has_record'; //存在录音，已录音
const P_RECORD_FROM = 'record_from';
const P_RECORD_CACHE_INDEX = 'record_cache_index'; //bgm缓存下标
const P_IS_FREE_MATCH = 'is_free_match'; //匹配是否免费
const P_IS_BOTTLE_CHAT = 'is_bottle_chat'; //是否是漂流瓶聊天
const P_HIDE_TOP = 'hide_top'; //是否隐藏chatlist顶部widget
const P_IS_FREE_MODE = 'is_free_mode'; //是否是自由聊天模式
const P_IS_SEARCH_BY_ID = 'is_search_by_id'; //是否根据id搜索联系人
const P_NEED_AUTO_SEND_MSG = 'need_auto_send_msg'; //是否需要自动发送打招呼消息
const P_IS_BUY = 'is_buy'; // 是否购买，0未购买，1购买
const P_IS_MANAGE = 'is_manage';
const P_FAMILY_ROLE = 'family_role';
const P_USER_INFO = 'user_info';

const P_QUESTION_ID = 'question_id';
const P_IS_NATURE = 'is_nature'; //quiz 是否

const P_VOICE_VERIFY_MODEL = 'voice_verify_model'; //跳转声音认证审核结果携带参数
const P_IS_EDIT = 'is_edit'; //声音认证编辑跳转
const P_IS_ERROR = 'is_error'; //声音认证界面是否显示失败界面
const P_UNLOCK_GIFT = 'unlock_gift'; //送礼解锁
const P_PRIVATE = 'private'; // 只显示私聊
const P_HAS_OPR = 'has_opr'; // 支持操作
const P_CONVERSATION_TYPE = "conversation_type"; //会话类型
const P_MSG_UID = "msg_uid"; //消息uid
const P_MSG_TIME = "msg_time"; //消息时间
const P_HASH_TAGS = 'hashtags';
const P_IS_CREATE = 'is_create'; //是否创建
const P_SHOW_REFRESH = 'show_refresh'; //是否支持刷新
const P_IS_VIDEO = 'is_video'; //视频功能
const P_IS_CHECK = 'is_check';
const P_CHECK_PRE_CHAT = 'check_pre_chat'; // 私聊页面检测是否可以聊天
const P_CHAT_IS_CUSTOMER_LIST = 'is_customer_list'; /// 是否是 account manager

const P_LEVEL = 'level'; //等级

const P_IS_ROOM = 'is_room';

/// 聊天
/// 聊天引用数据
const P_CHAT_REFER_DATA = 'chat_refer_data';
const P_ROOM_OWNER_ID = "room_owner_id";

/// 资产数据
const P_WEALTH = 'wealth';
const P_CHARISMA = 'charisma';

/// 用于传复杂对象，必须是可空参数
const P_DATA = 'data';

/// 漂流瓶加载页传递数据
const P_BOTTLE_RESOURCE = 'bottle_resource';
const P_BOTTLE_NUM = 'bottle_num';
const P_BOTTLE_TYPE = 'bottle_type';
const P_BOTTLE_DATA = 'bottle_data';

/// 用于打点统计
/// 从那个页面进入
const P_STATISTIC_FROM = 'from';
const P_STATISTIC_DURATION = 'duration'; //打点页面时长

/// 性格测试是否已完成
const P_STATISTIC_STATUS = 'status';

const P_STATISTIC_MATCH_TYPE = 'matchType';

const _tag = 'RouterUrl';

const P_HASHTAG_ID = 'hashtag_id';

/// 语音房密码
const P_ROOM_PWD = 'room_pwd';

const P_ROOM_START = 'room_start';

const P_ROUTE_DIALOG = 'dialog';

const P_HIDE_TITLE = 'hide_title';

const P_WEB_BG_COLOR = 'p_web_bg_color';

const P_ROOM_MODE = 'room_mode';

/// 房间活动tag
const P_EVENT_TAG_ID = 'tag_id';
const P_EVENT_TAG_NAME = 'tag';
const P_EVENT_TAG_ICON = 'tag_icon';
const P_EVENT_NAME = 'event_name';

const P_FAMILY_COVER = 'family_cover';
const P_FAMILY_NAME = 'family_name';
const P_FAMILY_ANNOUNCE = 'family_announce';
const P_FAMILY_ID = 'family_id';
const P_LUCKY_BAG_ID = 'lucky_bag_id';

const P_GIFT_ID = 'gift_id';

/// 使用优惠券
const P_COUPON_BAG_ID = 'coupon_bag_id';
const P_COUPON_ID = 'coupon_id';
const P_COUPON_AMOUNT = 'coupon_amount';

///主题id
const P_THEME_ID = 'theme_id';
const P_THEME_NAME = 'theme_name';


const P_SOURCE = 'source';

// 部分页面会跳转到指定目标页，此标记表明使用正常的返回
const P_BACK_NORMAL = 'back_normal';

const P_USER_STATE = 'user_state';

const P_SWITCH_ROOM_BANNER = 'switch_room_banner';

/// 房间加载签到相关
const P_ROOM_SIGN_IN = 'room_sign_in';

/// 打开礼物面板
const P_ROOM_OPEN_GIFT = 'open_gift';
/// 打开水果机
const P_ROOM_FRUIT_GAME = 'open_fruit_game';
/// 打开BsGame游戏ID
const P_ROOM_BS_GAME_ID = 'bs_game_id';
/// 打开  bc 游戏 id
const P_ROOM_BC_GAME_ID = 'open_bc_game_id';

/// 排行榜相关 (魅力、财富、cp)
const P_RANK_TYPE = 'rank_type';

/// 排行榜相关-子类型 （本周、上周）
const P_RANK_SUB_TYPE = 'sub_type';

/// 页面返回值
const P_RESULT_CODE = 'code';

/// 阻止切换工会
const P_DISABLE_SWITCH_AGENCY = 'disable_switch_agency';

const P_SHOW_GIFT_PANEL = 'show_gift_panel';

const P_BACKGROUND = 'background'; //背景色

const P_TITLE_BAR_COLOR = 'title_bar_color'; //标题栏颜色
const P_TITLE_COLOR = 'title_color'; //标题文字颜色
const P_BACK_ICON_COLOR = 'back_icon_color'; //返回按钮颜色

const P_STATUS = 'status'; // 状态

/// 是否隐藏appBar

///开播

/// 格式化url，加入多语言占位
/// 格式化url，加入多语言占位
String formatWebUrl(String url) {
  Log.i(_tag, 'Original url: $url');
  final formatted = url.replaceFirst('{lan}', Intl.defaultLocale ?? 'en');
  Log.i(_tag, 'Formatted url: $formatted');
  return formatted;
}

/// 弹窗类型
RouteSettings dialogRouteSettings(String url) {
  return RouteSettings(name: url, arguments: P_ROUTE_DIALOG);
}

/// 上层页面路由名称
String topPageName() {
  /// 过滤弹窗类型
  for (var item in FLRouter.routeObserver.getRoutes().reversed) {
    var name = item.settings.name;
    if (name != null && item.settings.arguments != P_ROUTE_DIALOG) {
      return name;
    }
  }
  return R_MAIN;
}