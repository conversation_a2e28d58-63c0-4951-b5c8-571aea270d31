{"appName": "<PERSON><PERSON>", "pingName": "Ping!", "@@locale": "en", "msgDescImage": "[Photo]", "msgDescAudio": "[Voice]", "msgDescVideo": "[Video]", "tabHome": "Home", "tabMoments": "Moments", "tabChat": "Cha<PERSON>", "tabMine": "Mine", "pin": "<PERSON>n", "unpin": "Unpin", "delete": "Delete", "send": "Send", "yesterday": "Yesterday", "match": "Match", "copy": "Copy", "cancel": "Cancel", "sureDelete": "Are you sure you want to delete it?", "callError": "Call error", "defaultError": "Our server is busy  now, please try later", "defaultMatchError": "Your soulmate is not here yet, but this is not the end. Try again later!", "iAm": "I am", "firstName": "First name", "myNameIs": "My name is", "myBirthIs": "My birthday is", "switchAvatar": "Change an avatar", "female": "Female", "male": "Male", "setAvatar": "Set avatar", "tipSetAvatar": "Select your avatar", "setUpProfile": "Set up profile", "tipCompleteInfo": "Complete my profile", "yourName": "Your Name", "next": "Next", "typing": "Typing...", "recording": "Recording...", "following": "Following", "followers": "Followers", "visitors": "Visitors", "copySuccess": "<PERSON><PERSON>d successfully", "follow": "Follow", "chat": "Cha<PERSON>", "editName": "Edit name", "save": "Save", "waitingFirstStep": "Waiting for your first step", "read": "Read", "describeYourself": "Describe yourself", "name": "Name", "callInviteVideo": "invites you to a video call", "callInviteAudio": "invites you to a audio call", "tipFollowBack": "Follow back to become friends.", "tipHasFollowing": "You are following this user.", "unFollowConfirm": "Are you sure you want to unfollow this user?", "unFollow": "Unfollow", "unFollow2": "Unfollow", "@unFollow2": {"description": "Accurate translation copy in some languages"}, "report": "Report", "reply": "Reply", "block": "Block", "hintNetError": "Connection failed.", "itsMatch": "It's a match!", "friendshipMatching": "Friendship matching", "findFriends": "Find friends", "personalityTests": "Personality tests", "settings": "Settings", "account": "Account", "avatar": "Avatar", "nickname": "Nickname", "birthday": "Birthday", "interests": "Interests", "notifications": "Notifications", "allNotifications": "All notifications", "onlyMessageMoment": "Only message, moments & interaction alerts", "onlyMessage": "Only message alerts", "noNotifications": "No notifications", "blockedList": "Blocked list", "unblock": "Unblock", "appLanguage": "Language", "helpAndFeedback": "Help & feedback", "shareApp": "Share app", "aboutUs": "About us", "privacyPolicy": "Privacy policy", "termOfUse": "Terms of use", "logoutAlert": "Once log out, you'll no longer receive any new messages.", "logout": "Log out", "auto": "Auto", "forYou": "For you", "moment": "Moment", "comments": "Comments", "saySomething": "Talk to {sex} about this moment", "@saySomething": {"type": "string", "placeholders": {"sex": {}}}, "post": "Post", "tipPublishInput": "What's fun?", "guidePublishInput": "Introduce yourself in simple words...", "canceled": "Canceled", "declined": "Declined", "callFailed": "Call failed", "callDeclined": "Call declined", "callCancelled": "Call cancelled by caller", "durationWith": "Duration: {time}", "@durationWith": {"type": "string", "placeholders": {"time": {}}}, "timeLeftToday": "{num} times left today", "@timeLeftToday": {"type": "string", "placeholders": {"num": {}}}, "timesNum": "{num,plural, =0{{num} time}=1{{num} time}=2{{num} times}few{{num} times}other{{num} times}}", "@timesNum": {"type": "string", "placeholders": {"num": {}}}, "holdToSpeak": "Hold to speak", "swipeUpToCancel": "Swipe up to cancel", "recordingWillStop": "Recording will stop in", "imCallInstruction": "Follow each other to start voice calling", "ok": "Ok", "audioCall": "Audio Call", "videoCall": "Video Call", "personalityTestsDetail": "Meet friends matching your interests", "matchFriends": "Find friends", "findingPerfectMatchTitle": "Finding your perfect match", "matchFailed": "Match failed", "close": "Close", "matchSuccess": "Match successfully", "facebook": "Facebook", "instagram": "Instagram", "whatsApp": "WhatsApp", "snapchat": "Snapchat", "message": "Message", "more": "More", "followSuccess": "Followed successfully", "chatNow": "Chat now", "noticeTitle": "Moments notifications", "noticeLike": "Liked your post", "noticeFollow": "started following you", "matesOnlineNow": "Active now", "tapAgainToExit": "Tap back again to exit", "skip": "<PERSON><PERSON>", "loginSlogan": "Right place to meet right person", "loginAgree": "By log in, you agree to", "loginFailed": "<PERSON><PERSON> failed, please try another way.", "signInApple": "Sign in with Apple", "signInFacebook": "Sign in with Facebook", "signInFaceGoogle": "Sign in with Google", "agreeAlert": "Please agree with Privacy Policy and Terms of use to login", "passionate": "Interests", "emptyMoment": "There are no posts yet.", "checkNetWork": "Please check your network connection and try again", "tryAgain": "Try again", "noFollowing": "No Following here", "noFollowers": "No Followers here", "noVisitors": "No Visitors here", "msgDescVideoCall": "[Video call]", "msgDescAudioCall": "[Audio call]", "msgDescShare": "[Share]", "msgDescUnknown": "You received a new message that unsupported in the current version, please update.", "msgDigestUnknown": "You received an unsupported message.", "msgDescActivity": "[Activity]", "emptyNotice": "No notification yet", "mightContainRisk": "This message might contain unfriendly or risky content", "dontMind": "If you don’t mind, ", "tapToView": "tap here to view hidden message", "feltOffended": "Felt offended? ", "tapToReport": "Tap here to report", "receiveNewMsg": "You received a new message", "chatSettings": "Chat settings", "alias": "<PERSON><PERSON>", "pinChat": "Pin chat", "clearChatHistory": "Clear chat history", "myResults": "My results", "test": "Test", "undone": "Undone", "noContentHere": "No content here", "setRemarks": "Set Remarks", "remarks": "Remarks", "deleted": "Deleted", "tapToSee": "Tap to see", "destructMsgHint": "Long hold the screen\nCan only check once", "selfDelete": "Auto-delete after viewing", "previous": "Previous", "done": "Done", "mediasOverSize": "Size of a single video cannot exceed 20 MB", "emptyComments": "No one has commented yet", "pictureNotExist": "This picture does not exist", "userMatchingYourResult": "Users matching your result", "share": "Share", "shared": "Shared", "otherPopularTests": "Other popular tests", "harassment": "Harassment", "vulgarOrOffensive": "vulgar or offensive language", "violence": "violence", "stalking": "stalking", "sexualHarassment": "sexual harassment", "inappropriatePhotos": "inappropriate photos", "spam": "Spam", "askingNumber": "the user is asking my number", "advertising": "the user is advertising", "suspiciousLink": "this user sent me suspicious link", "inappropriateName": "inappropriate nickname", "askedForMoney": "this user asked me for money", "promotingOtherApps": "promoting other apps", "hateSpeech": "Hate speech", "politicsRelated": "politics related", "religionRelated": "religion related", "racism": "racism", "illegal": "Illegal", "smokeDrugsDrinking": "smoke,drugs,drinking", "terrorism": "terrorism", "bloodyViolence": "bloody violence", "other": "Other", "description": "Description", "pleaseDescribe": "Please describe the problem you are experiencing...", "pleaseSelectReason": "Please select the reason for reporting\nWe won't tell this user", "thanksReachingOut": "Thanks for reaching out about your experience", "blockHint": "Your safety is our priority. We will handle it for you promptly. To avoid further disturbance, you may also block this user.", "blockUser": "Block user", "checkDetails": "Check details", "shareToFriends": "Share to friends", "momentNoExist": "The post has been deleted", "sendingMoment": "Sending moment…", "postSuccess": "Post successfully", "confirm": "Confirm", "noFirstStep": "You didn’t make the first step", "willLoseMatch": "You will lose this match if you leave now.", "leaveChat": "Leave chat", "keepStaying": "Stay", "dontMiss": "Don't miss a single message!", "quizShare": "Hi~<PERSON><PERSON>! Take personality tests now to find friends match your results!", "shareSuccess": "Shared successfully", "shareFail": "Failed to share, please try again", "sentButReject": "This message is successfully sent but rejected by the receiver", "checkUpdate": "Check for update", "postMoment": "High quality moments can get you more replies from other mates, <h>", "postNow": "add a new post now!", "turnOnToKnowMessages": "Turn on notifications to know new messages and matches.", "notifyMe": "Notify me", "turnOnNotifications": "Turn on notifications to know new messages", "goToSetNotifications": "Go to Settings-Winker-Notifications-Allow notifications.", "allow": "Allow", "fakeUserMale": "<PERSON><PERSON>", "fakeUserFemale": "<PERSON>", "fakeMsgMale": "Hello", "fakeMsgFemale": "Hello", "enableForMsgMale": "Enable notification to receive his message", "enableForMsgFemale": "Enable notification to receive her message", "enableForMoment": "Enable notification to receive updates for moments and comments", "greeting1": "Hi", "greeting2": "Hello Hello~", "greeting3": "Nice to meet you", "communityRules": "Community rules", "rulesIntro": "Following behaviours will lead to permanent account ban.", "rules1": "❌ Asking for personal information", "rules2": "❌ Harassing or bullying other users", "rules3": "❌ Posting sexual content", "rules4": "❌ Posting religious or political content", "rules5": "❌ Using abusive inappropriate language", "iUnderstand": "I understand", "destructHint": "By selecting this, photo or video can only be viewed once , after which it automatically self-destructs.", "sendFeedback": "Send a feedback", "feedback": "<PERSON><PERSON><PERSON>", "myFeedback": "My feedback", "appProblems": "App problems", "suggestion": "Suggestion", "others": "Others", "screenshot": "Screenshot", "optional": "Optional", "submit": "Submit", "submitSuccess": "Submitted successfully", "failedToSubmit": "Failed to submit, please try again", "waitingReply": "Waiting for reply", "replied": "Replied", "problemDesc": "Problem description", "processingRequest": "We are processing your request", "userGuide": "User guide", "evaluateContent": "If you like <PERSON><PERSON>, please give us a 5 star, thanks~", "helpUsImprove": "Help us improve", "checkingUpgrade": "Checking for latest version, please wait...", "latestVersion": "Your current app is the latest version.", "versionOutOfDate": "Your current app version is out of date, please get the latest version to enjoy new features.", "newVersion": "New Version :", "install": "Install", "updateNow": "Update now", "youTookScreenshot": "You took a screenshot of chat", "userTookScreenshot": "{username} took a screenshot", "@userTookScreenshot": {"type": "string", "placeholders": {"username": {}}}, "msgDescSystem": "[Notification]", "chatShouldStayPrivate": "Hey, that's not cool.\n<PERSON><PERSON> should stay private", "sentMsgOnceTakeScreen": "A message will be sent to user once you take screen of the chat.", "gotIt": "Got it", "micAccessBanned": "Mic access not enabled", "storageAccessBanned": "Storage access not enabled", "enableMicAccess": "Please go to \"Setting<PERSON>\" > \"Winker\" and enable microphone access.", "enableStorageAccess": "Please go to \"Setting<PERSON>\" > \"Winker\" and enable storage access.", "goToSettings": "Go to settings", "failedToSend": "Failed to send", "yes": "Yes", "onlyPublishOneMoment": "You can only post one moment at a time,  are you sure you want to send the new moment?", "sexGuideText": "Once gender is selected, it cannot be modified. Please choose carefully.", "less": "Less", "matchGuide": "Tap to start chatting with new friend", "beRestrictedFor": "You account has been restricted because of violating community rules.", "bePermanentlyRestricted": "Your account has been blocked permanently for violating community rules", "iGotIt": "I got it", "msgTooShort": "Message too short", "resendMsg": "Resend this message?", "resend": "Resend", "year": "Year", "month": "Month", "day": "Day", "matchCountEmptyTitle": "You used up all chances today", "matchCountEmptyDesc": "Please come tomorrow to meet new friends", "discardPublishEdit": "If you discard now,you'll lose this post", "discardPost": "Discard Post", "keepEditing": "Keep Editing", "fillAccountInfo": "Please fill in account info", "selectYourAvatar": "Please select your avatar", "tipPassions": "It's the perfect opportunity to show a little more about yourself.", "selectLeastInterest": "Please select at least one interest", "enablePhotoAccess": "Please allow <PERSON><PERSON> to access your device's photos in \"Settings> Privacy > Photos", "editNickNameAlert": "You can edit nickname every 24 hours, are you sure you want to change it?", "canNotEditName": "You can edit nickname every 24 hours, please try again later", "canNotEditAvatar": "You can update avatar every 24 hours, please try again tomorrow", "blockSuccessfully": "Block successfully", "unblockSuccessfully": "Unblock successfully", "blockConfirm": "Are you sure you want to block this user?", "addPassionTip": "Add your interest tags to attract more like-minded mates", "saveSuccessfully": "Saved successfully", "saveFailed": "Save failed", "about": "About", "currentVersion": "Current Version:", "copyright": "Copyright © 2023 All rights reserved", "showYourCharm": "Make the first move to show your charm!", "confirmToClear": "Confirm to clear all the chat history?", "clear": "Clear", "cleared": "Cleared", "inCallNotice": "Line busy, please try later", "loading": "Loading...", "refreshComplete": "Refreshed", "refreshFailed": "Refresh failed", "followGuide": "Follow {username} to unlock more features!", "@followGuide": {"type": "text", "placeholders": {"username": {}}}, "beFollowedGuide": "Follow {username} to unlock more features!", "@beFollowedGuide": {"type": "text", "placeholders": {"username": {}}}, "edit": "Edit", "gif": "Gif", "heicNotSupported": "Unsupported HEIC file type.", "loadFailed": "Loading Failed", "original": "Original", "preview": "Preview", "select": "Select", "emptyList": "Empty list", "unSupportedAssetType": "Unsupported HEIC file type.", "unableToAccessAll": "Unable to access all files on the device", "viewingLimitedAssetsTip": "Only view files and albums accessible to app.", "changeAccessibleLimitedAssets": "Update limited access files list", "accessAllTip": "<PERSON><PERSON> can only access some files on the device. Go to system settings and allow app to access all files on the device.", "goToSystemSettings": "Go to system settings", "accessLimitedAssets": "Continue with limited access", "accessiblePathName": "Accessible files", "shareContent": "Hey~ Never miss such awesome moment.", "shareAppContent": "Winker~Free voice call & quizzes with people around the world!", "friends": "Friends", "kickOut": "Your account logged in on another device.", "hourAgo": "{time,plural, =0{{time} hour}=1{{time} hour}=2{{time} hours}few{{time} hours}other{{time} hours}} ago", "@hourAgo": {"type": "text", "placeholders": {"time": {}}}, "minuteAgo": "{time,plural, =0{{time} minute}=1{{time} minute}=2{{time} minutes}few{{time} minutes}other{{time} minutes}} ago", "@minuteAgo": {"type": "string", "placeholders": {"time": {}}}, "albumNotEnabledTitle": "Album access not enabled", "albumNotEnabledContent": "<PERSON><PERSON> can't access photos. Allow permission for <PERSON><PERSON> to access photos in device settings.", "goToMoments": "Go to moments to find interesting mates!", "postMoreMoments": "Post more moments to get more attention!", "followSomeInterest": "Follow some interesting mates now!", "go": "Go", "postMoments": "Post moment", "contacts": "Contacts", "search": "Search", "chatHistory": "Chats history", "noResult": "No results found", "chats": "Chats", "updatingNow": "Update now", "sendSomethingNew": "Send something new", "keepSending": "Keep sending", "completeAccount": "Complete your account info to attract more like-minded mates", "livingIn": "Living in", "aboutMe": "About me", "personalInfo": "Personal info", "starSign": "Zodiac sign", "completePercent": "{percent}% complete", "@completePercent": {"type": "string", "placeholders": {"percent": {}}}, "region": "Region", "selected": "Selected", "doNotBeShy": "Don't be shy!", "writeABio": "Write a bio to introduce yourself", "mute": "Mute", "unmute": "Unmute", "muteNotifications": "Mute notifications", "newMessages": "{amount} new messages", "@newMessages": {"type": "string", "placeholders": {"amount": {}}}, "stopEditProfile": "Stop editing your profile?", "completeYourProfile": "Almost done! Complete your profile to find people most suitable for you!", "notYet": "Not yet", "viewingProfile": "Viewing profile", "characterTitle": "Your personality type", "todayForCast": "Forcast for today", "todayBestMatch": "Today's best match", "generalPrediction": "General prediction", "luck": "Luck", "money": "Money", "mood": "<PERSON><PERSON>", "energy": "Energy", "luckyNumber": "Lucky number", "luckySign": "Lucky sign", "takeTheTest": "Take the test", "completeProfileBubble": "Complete your account info to attract more like-minded mates", "completeToFindFriends": "Complete all account info will help you find friends.", "selectUpTen": "You can select up to 10 tags", "recentlyUsed": "Recently used", "allStickers": "All stickers", "viewProfile": "View profile", "resendCode": "Resend", "resendCodeIn": "Resend after", "welcomeToAlo": "Welcome to Winker", "youBastExperience": "Your Place, Your Mates, Your Best Experience.", "invalidMobileNumber": "Invalid mobile number", "phoneNumber": "Phone number", "signInWithPhoneNumber": "Sign in with Mobile", "mobileNumberConfirmation": "Mobile number confirmation", "enterSmsCodeSentTo": "Send a verification code to the following number:", "moreWaysToLogIn": "More ways to login>", "blockedThisUser": "You blocked this user", "blockedByThisUser": "You are blocked by this user", "selectCountry": "Select country", "voiceTest": "Voice test", "chooseAnswerTip": "Choose an answer best fits you", "recordQuestions": "Record my questions", "recordYourQuestion": "Record your question", "recordQueTips": "Record your questions to unlock the chat,make a match and you will get new friends!", "pleaseRecord": "Please click the record button first, and then read the above words aloud.", "clickToPlay": "Click to play", "playing": "Playing", "clickToRecord": "Click to record", "resendCodeMax": "You have reached the maximum requests, please try again tomorrow.", "speakTooFast": "Hey, be patient~Grab a coffee and wait till you get a reply!", "notMatchForHer": "She is not a match for you today, let's chat tomorrow!", "notMatchForHim": "He is not a match for you today, let's chat tomorrow!", "voiceRecording": "Recording", "pleaseChooseAnswer": "Please choose your answer", "editQuestions": "Edit questions", "typeAMessage": "Type a message", "makeFirstStarted": "Click to quickly initiate a chat", "change": "Change", "congrats": "Congrats!", "youMatchedWith": "You have been matched with ", "notMatchWith": "Sorry, you have not match with ", "continueMatch": "Keep matching", "goToChat": "Go to chat", "recordUnlockChat": "Unlock the chat", "pleaseTryAgain": "Network problem please try again", "no": "No", "askAQuestion": "Ask a question for you both to answer", "changeQuestion": "Change a question", "yourQuestion": "Your question:", "typeAnswer": "Type your answer{content}", "@typeAnswer": {"type": "string", "placeholders": {"content": {}}}, "illegalContent": "Your answer have the illegal content, please follow the community rules.", "unlockHerAnswer": "Answer the question to unlock her answers", "unlockHisAnswer": "Answer the question to unlock his answers", "answerAreRevealed": "Answers are revealed when you both answer", "playWithFriend": "Click and play with your friend!", "playQuestionWithYou": "{nick} wants to play question game with you.", "@playQuestionWithYou": {"type": "string", "placeholders": {"nick": {}}}, "sendYouQuestion": "{nick} sends you a question.", "@sendYouQuestion": {"type": "string", "placeholders": {"nick": {}}}, "askAnotherQuestion": "Tap to ask another question", "countDownTimeTips": "Your match times have been used up. The match times will be refreshed at the end of the countdown.", "matchedImTips": "Match you from the voice test.", "msgDescQuestionBox": "[Question game]", "startQuestionGame": "{nick} started the question game.", "@startQuestionGame": {"type": "string", "placeholders": {"nick": {}}}, "exitRecordingTips": "Discard now? Your audio note is not saved.", "yourVoiceLow": "Your voice is too low, please speak loudly.", "notVoiceInput": "There is no voice input, please record your question in a quiet environment.", "clearCache": "Clear cache", "clearSuccess": "<PERSON><PERSON> cleared", "calculating": "Calculating", "tryVoiceMatching": "Try voice test, you can find more friends in Winker", "matchedYouByVoice": "{nick} matched you from the voice test function.", "@matchedYouByVoice": {"type": "string", "placeholders": {"nick": {}}}, "theVoiceMatchFindTips": "Find friends who share the same tastes with you from voice test.", "listenToTheVoiceQuestion": "Listen to the question and select your answer", "cherishTheChance": "Cherish the chance to choose carefully", "sorryNotFoundVoiceMatch": "Sorry, we can't find a perfect user for you now, please try again later.", "times": "times", "myVoiceVerification": "My voice verification", "needToVerifyVoice": "<PERSON><PERSON> does its best to ensure you a healthy real social environment so please verify your voice now so we can get to know your real identity!", "yourVoiceVerificationFailed": "Your voice verification failed, please try to record again. ", "verifyVoiceBeforeMatching": "You haven't verified your voice yet. Please go and verify your voice now to unlock our features!", "verifyVoiceBeforeListening": "You haven't verified your voice yet. Please verify your voice before listening. ", "pleaseReadTheWords": "Read the text below:", "goToVerify": "Go to verify", "voiceVerification": "Verification", "clickToRecordVoice": "Click to record your voice", "recordAtLeastSeconds": "Record at least {second} seconds", "verificationInReview": "Your verification is under review", "verificationPassed": "Verification passed", "voiceSeconds": "{seconds}s", "msgDescExpression": "[sticker]", "micPermission": "\"Winker\" would like to access your Microphone and collect your voice data to enable voice message, identity verification only when the app is in use.", "storagePermission": "\"Winker\" would like to collect read and write storage data to enable sending pictures in messages only when the app is in use.", "searchSticker": "Click it! You can search for any sticker you want.", "favoriteSticker": "We have added the sticker feature.\nYou can add stickers to your favorites now.", "manageSticker": "More stickers can be managed and added in \"Collection management\".", "noneFavorites": "You haven't added any stickers to your favorites yet.", "add": "Add", "addedSuccess": "Added successfully", "addToFavorite": "Add to favorite", "download": "Download", "voiceDatingTimeUsedUp": "No time left today, please try again tomorrow.", "voiceDatingNoPeople": "Your perfect match is not here right now, please try again later.", "ooops": "Ooops..", "moveFront": "Move to front", "downloadSuccess": "Downloaded successfully", "collections": "Collections", "myCollection": "My collection", "collectionDetail": "Collection details", "searchStickers": "Search stickers", "verifyProfile": "Verify profile", "verifyFailed": "Verify failed", "voiceVerifyMatchCount": "{count} times left today", "waitForApproval": "Please wait for approval.", "submitSuccessfully": "Submit successfully", "iKnow": "I Know", "sayHiToYourMate": "Say hi to your mate!", "moreStickers": "More stickers", "addToMyCollection": "Add to my collection", "firstSelectYourFavourite": "First, select your favourite text to read with your voice. You can change the words.", "secondClickToRead": "Second, click it to read the text with your voice. ", "swipeRightToLike": "SWIPE RIGHT TO LIKE", "swipeLeftToPass": "SWIPE LEFT TO PASS", "playAndPause": "PLAY AND PAUSE", "matchLikeEachOther": "You will have a match only if you both like each other.Try it!", "passNoOneKnow": "If you don't like them, simply pass. No one will know.", "justListenSwipeLike": "Just listen the voice from other user, swipe right if you liked it.", "selectAll": "Select all", "deleteNum": "Delete ({num})", "@deleteNum": {"type": "string", "placeholders": {"num": {}}}, "deleteCollectionConfirm": "Stickers can't be recovered once deleted. Delete now?", "noneMyCollections": "You did not add any collections.", "stickerLimit": "Stickers reached limit", "answerQuestionGame": "{nick} answered the question game.", "@answerQuestionGame": {"type": "string", "placeholders": {"nick": {}}}, "callMatch": "Call match", "voiceMatch": "Voice match", "noContent": "No result, please try a different keyword.", "personalityTest": "Personality Test", "quizGuideText": "Take the test to find your ideal mate based on your result", "testNow": "Test now", "greatStart": "Great start", "theyLikeYouBackUnlockChat": "If they like you back you can unlock the chat.", "notBeUsed": "Not be used at the same time", "recordVoiceGuidContent": "Click it to read the words by your voice", "deleteSureContent": "Are you sure you want to delete?", "addTopic": "Add Topic", "searchHashtag": "Search topic", "noMoreTopic": "Sorry, no content", "recommendTopic": "Recommend topic", "cancelPickTopic": "Exit recording will delete what you recorded", "contribute": "Contribute", "proposeTopic": "Recommend your topic to us", "hottest": "Hottest", "latest": "Latest", "hashTag": "Hashtag", "favouriteHashtag": "Click here to choose your favourite hashtag! ", "postVoiceMoment": "You can post a voice note in moments now!", "signInRewards": "Daily rewards", "signContent": "Sign in for 7 days to get a surprise", "signIn": "Sign in", "getForFree": "Get for free", "signedInToday": "Signed in today", "signedInNumDay": "You have signed in for {num} days.", "@signedInNumDay": {"type": "string", "placeholders": {"num": {}}}, "goldsCanSendGift": "Golds can be used to send gifts.", "giftsCanSendFriends": "Send gifts to the friends you like.", "backpack": "Backpack", "gift": "Gift", "backpackEmpty": "Your backpack is empty.", "earnGolds": "Earn golds", "hours": "{hour} hours", "@hours": {"type": "string", "placeholders": {"hour": {}}}, "msgDescGift": "[Gift]", "quantityShortage": "Gifts shortage", "iSentYou": "I sent you", "sentYouGift": "Sent you a gift", "lackOfCoins": "Lack of golds! please finish more tasks", "spendCoinsToSendGift": "Would you spend {coin} golds to send this gift?", "@spendCoinsToSendGift": {"type": "string", "placeholders": {"coin": {}}}, "sendSuccessfully": "<PERSON><PERSON> successfully", "usesOfGolds": "Uses of Golds", "wayToGetGolds": "Way to Get Golds", "goldsUses1": "1. Golds can be used to send gifts.", "goldsUses2": "2. Golds can be used to increase match times.", "goldGet1": "1. Golds can be gained by signing in.", "goldGet2": "2. Golds can be gained by doing tasks and milestones.", "myGolds": "My Golds", "details": "Details", "detail": "Detail", "unlockChat": "Send gift to unlock the chat", "sendAGift": "Please send a gift to show your appreciation.", "notNow": "Not now", "tryIt": "Try it", "makeGoodImpression": "Send gifts to make a good impression.", "sendToChat": "Send to chat", "charisma": "Charisma", "wealth": "Contribution", "receive": "Receive", "assetData": "Asset data", "assetDataTitle": "How to get contribution or charisma?", "assetDataWealth": "Contribution: You can get contribution points by sending gifts or items to others:\n1 gold = 1 point \n1 diamond = 10 points", "assetDataCharisma": "Charisma: You can get charisma points when receiving gifts or items from other users:\n1 gold = 1 point \n1 diamond = 10 points \nIf you send gifts to yourself, you will only get contribution points, not charisma points.", "giftUpperLimit": "This gift has reached the upper limit of sending today", "youGetAGift": "You received a gift.", "youGetTwoGifts": "You received two gifts.", "youGotNumGifts": "You received {num,plural, =0{{num} gift}=1{{num} gift}=2{{num} gifts}few{{num} gifts}other{{num} gifts}}.", "@youGotNumGifts": {"type": "string", "placeholders": {"num": {}}}, "golds": "Golds", "signInFailed": "Sign in failed, please retry", "profile": "Profile", "dailyTasks": "Daily tasks", "milestones": "Milestones", "get": "Get", "youHaveRewards": "Tap to get rewards", "tasks": "Tasks", "quizProvided": "Quiz provided by third party content providers.", "signInBackpack": "Task rewards have been sent to backpack.", "youHaveGot": "You have got", "getMoreRewards": "Get more rewards", "giftSendGuid": "Click it to send a gift to show your appreciation!", "deleteAccount": "Delete account", "areYouSureToDeleteAccount": "Are you sure you want to delete your account?", "letUsKnowTheReasonYouLeave": "Please let us know the reason you are leaving.", "iDontLikeWinker": "I don't like <PERSON><PERSON>", "privacyProblem": "Privacy problem", "somethingIsBroken": "Something is broken", "typeTourSuggestionsToUs": "Type your suggestions to us...", "accountDataCantBeRecovered": "Account data can not be recovered after deleted.", "accountDeleted": "Account deleted", "yourAccountWasDeleted": "Your Winker account was deleted.", "visibleToPublic": "Visible to public", "visibleToFriends": "Visible to friends", "visibleToMyself": "Visible to myself", "public": "Public", "myself": "Myself", "private": "Private", "noPermissionToListenVoice": "You have no permission to listen the voice verification.", "permission": "Permission", "continueToDelete": "Continue to delete", "requestTimeout": "Request time-out, please try again.", "okay": "Okay", "thisAccountHadDeleted": "This account has been deleted", "thisUserHadBanned": "This user has been banned", "clickToGetTheFree": "Click it to get the free rewards.", "matchFilter": "Select match type", "yourBalance": "Your balance :", "all": "All", "numGolds": "{num,plural, =0{{num} gold}=1{{num} gold}=2{{num} golds}few{{num} golds}other{{num} golds}}", "@numGolds": {"type": "string", "placeholders": {"num": {}}}, "timesFree": "{num,plural, =0{{num} time}=1{{num} time}=2{{num} times}few{{num} times}other{{num} times}} free", "@timesFree": {"type": "string", "placeholders": {"num": {}}}, "filterTimesToday": "You tried too much today, please come back tomorrow", "numGoldsCountTimes": "Spend {num,plural, =0{{num} gold}=1{{num} gold}=2{{num} golds}few{{num} golds}other{{num} golds}} to increase {count,plural, =0{{count} time}=1{{count} time}=2{{count} times}few{{count} times}other{{count} times}}.", "@numGoldsCountTimes": {"type": "string", "placeholders": {"num": {}, "count": {}}}, "unlockMore": "Unlock more", "tooFastAndTakeARest": "You are too fast, please take a rest...", "welcomeToCompleteSpace": "Welcome to complete anonymous space.", "youWillHaveANewId": "You will have a new identity that include avatar and nickname.", "youCanThrowIntoSea": "No one will know you, just bottle up what you want to say and write and throw it into the sea.", "throwTheBottle": "Throw a bottle", "fishTheBottle": "Fish a bottle", "myBottle": "My bottles", "bottleAvatarLabel": "Set an avatar for this anonymous space.", "bottleSettingDesc": "Mystery letter is a complete anonymous space. The avatar and nickname you set can only be displayed here.", "bottleAvatar": "Bottle avatar", "bottleNickname": "<PERSON>ttle nickname", "notOriginal": "This content is not original", "notOriginalHint": "Please let us know the original creator with proof if possible so we can proceed to verify faster.", "reportSuccess": "Reported successfully", "unCoverIdentityAlert": "Confirm to send uncover Win<PERSON> profile invitation？", "unCoverIdentityMsg": "Invite the other side to uncover <PERSON><PERSON> profile?", "unCoverIdentitySystemTip": "Uncover Winker profile from each other?{str}", "@unCoverIdentitySystemTip": {"type": "string", "placeholders": {"str": {}}}, "accept": "Accept", "accepted": "Accepted", "ignored": "Ignored", "ignore": "Ignore", "yseWithExclamation": "Yes!", "msgDescUncoverIdentity": "[Uncover Winker profile]", "receivedBottleFrom": "You received a bottle from ", "receivedBottle": "You received a bottle", "throwAway": "Throw away", "sent": "<PERSON><PERSON>", "selectTheTopic": "Select the topic", "congratsNoExclamation": "Congrats", "throwBottleInOcean": "Click it, put what you want to say in a bottle, and throw it into the sea, someone may pick it up.", "pickBottleAndTalk": "Click it, pick the bottle up and talk with them.", "clickChangeBottleAvatar": "Click it to change the avatar and nickname.", "youCantSendBottle": "You ran out of your times today, please come back tomorrow!", "exitNoSaveInfo": "Exit will not save existing information", "haveUncovered": "Both of you have uncovered your <PERSON><PERSON> profile from Mystery letter.", "uncoverLimit": "You can only uncover once a day.", "deleteUser": "Delete User", "hasClosedConversation": "The other side has ended this conversation.", "deleteBottleConfirm": "The other side will not receive your messages once you delete this chat.", "triedTooMuchAndComeTomorrow": "You tried too much today, please come back tomorrow.", "whetherSpendGoldsToFish": "You have used up free times for today, wanna spend {coins} to fish one more time?", "@whetherSpendGoldsToFish": {"type": "string", "placeholders": {"coins": {}}}, "xGolds": "{coins} golds", "@xGolds": {"type": "string", "placeholders": {"coins": {}}}, "fishFailedTryAgain": "Fish failed, try again.", "sendBottleFirstTitle": "To continue, send your today's bottle first.", "sendBottleFirstContent": "You haven't sent a bottle today, please send a bottle first.", "yourGoldReturnedBalance": "Fish failed, your golds have been returned to your balance.", "fishFailed": "Fish failed", "tryBottle": "Try mystery letter, you can find more friends in Winker", "iGetIt": "I get it", "uncoverIdentity": "Uncover profile", "mysteryLetter": "Mystery letter", "mysteryLetterImTitle": "Mystery Letter", "bottleFrom": "A bottle from ", "realPersonVerified": "Real person verified", "receivedABottle": "Received a bottle", "shareYourMomentTitle": "Share your moment", "shareYourMomentContent": "Your stories would gain more likes and friends.", "GoToPost": "Go to post", "shareHalama": "Share Winker to your mate", "shareHaContent": "Share the happiness of <PERSON><PERSON> with your friends.", "shareMomentTodayContent": "Your stories would gain more likes and friends.", "guidePostMoment": "Click here to post today story to others!", "safeMode": "Safe mode activated, your information is under our protection.", "chatMode": "Chat mode", "nowMode": "NOW", "freeMode": "Free mode", "freeModeContent": "Free mode not restrict sensitive content.", "safeModeContentToast": "Safe mode activated, also can change to free mode here!", "safeModeContent": "Safe mode protect your information.", "youSentTheRequest": "You‘ve sent the request, please wait for approval.", "safeModeName": "Safe mode", "inviteToEnable": "Invite to enable", "enabled": "Enabled", "enable": "Enable", "cancelRequest": "Cancel request", "waitingForOtherSideAccept": "Waiting for acceptance", "invitationHasSent": "Invitation has been sent.", "switchMode": "Want to switch it to this mode?", "sendChatModeRequest": "{name} sent a request to change your chat mode.", "@sendChatModeRequest": {"type": "string", "placeholders": {"name": {}}}, "safeModeDetailContent": "Under safe mode we will protect your privacy and secure your personal information from the other users.", "freeModeDetailContent": "Under free mode you may express yourself freely, but the other side shall accept your invitation to switch on the same mode. ", "changeChatMode": "{name} Change chat mode", "@changeChatMode": {"type": "string", "placeholders": {"name": {}}}, "msgDescChatMode": "[Change chat mode]", "withdrawRequest": "The other side withdrew the request.", "requestTooMuch": "Request failed. You can send invitations to this user twice per day.", "youCanUnlockTips": "You can unlock restriction by <h>", "changeChatModeTips": "change chat mode", "changeSuccessfully": "Change successfully", "mall": "Mall", "purchase": "Purchase", "earnGold": "Earn gold >", "avatarFrame": "Avatar frame", "daysTimeout": "{num,plural, =0{{num} Day}=1{{num} Day}=2{{num} Days}few{{num} Days}other{{num} Days}}", "@daysTimeout": {"type": "string", "placeholders": {"num": {}}}, "hoursTimeout": "{num,plural, =0{{num} Hour}=1{{num} Hour}=2{{num} Hours}few{{num} Hours}other{{num} Hours}}", "@hoursTimeout": {"type": "string", "placeholders": {"num": {}}}, "secondsTimeout": "{num,plural, =0{{num} Second}=1{{num} Second}=2{{num} Seconds}few{{num} Seconds}other{{num} Seconds}}", "@secondsTimeout": {"type": "string", "placeholders": {"num": {}}}, "chatFrame": "Chat frame", "coinsAndDay": "{coins} / {day,plural, =0{{day}Day}=1{{day}Day}=2{{day}Days}few{{day}Days}other{{day}Days}}", "@coinsAndDay": {"type": "string", "placeholders": {"day": {}, "coins": {}}}, "purchaseSuccessfully": "Purchase successfully", "searchByUserId": "Search by user ID", "searchByUserNickname": "Search by user nickname", "wear": "Wear", "wearing": "Wearing", "successfully": "Successfully", "wallet": "Wallet", "store": "Store", "bag": "Backpack", "task": "Task", "niceToMeetYou": "Hi! Nice to meet you!", "wouldYouLikeToShareVoice": "Would you like to share to moments to make others hear your voice?", "shareToMoment": "Share to moment", "titleVoiceVerifyGuide": "How to get more replies?", "contentVoiceVerifyGuide": "Verify your voice to ensure you are a real person.\n Passing the verification to get priority for the match.", "ensuresSafety": "Winker ensures all users are in safety and real people environment.", "getMoreInWinker": "Can get more exposure and recommendation in Winker.", "protectYourInfo": "We will protect your privacy and personal information.", "moreVerifiedUser": "More verified user", "passingVerification": "Passing the verification to chat with her~", "VerifyUnlockMatch": "Verify your voice to unlock match features!", "VerifyUnlockCreate": "Verify your voice to unlock chat room features!", "VerifyBeforeListening": "Verify your voice before listening!", "verifyToUnlock": "Verify to unlock", "verificationToGet": "Verification to get", "verificationProtectYou": "Verification can protect you", "discardNow": "Discard now?", "protectYourPrivacy": "Don't worry we ensure to protect your privacy and information.", "morePriority": "One step away from getting more priority to chat with others.", "groupYourInfo": "You can choose your verification audio to be invisible. Are you sure exit?", "passGetMatch": "Passing the verification can get priority for the match. Are you sure exit?", "continueText": "Continue", "exit": "Exit", "thirdLoginCancel": "Your account info will only be used for log in.  We will ensure all user in a private and safe environment.", "thirdLoginNetworkError": "Network error, please try again.", "otherLoginWays": "Other login ways", "verificationCodeError": "Verification code error", "tryAnotherWay": "Would you like to try another way?", "agreeToTermAndPolicy": "Agree to Terms of use and Privacy policy", "agreeTo": "Agree to ", "and": " and ", "pleaseReadAndAgree": "Please read and agree", "agreePrivacyContent": "Your info will only be used for create account. We never leak user privacy.", "agreeAndContinue": "Agree and continue", "verifyProfileSecurely": "Verify profile securely", "editAvatar": "Edit Avatar", "newText": "New", "changeAvatar": "Change Avatar", "photoToAvatar": "Photo to Avatar", "avatarHistory": "Avatar history", "film": "Film", "filmDesc": "Want to take yourself back to the Middle Ages?", "cartoon": "Cartoon", "cartoonDesc": "Become cartoon superhero and digital art in 5 seconds!", "painting": "Painting", "paintingDesc": "Turn your selfie into pretty painting!", "free": "Free", "fourteenCentury": "14th Century", "nineteenCentury": "19th Century", "twentyOneCentury": "21st Century", "anime": "Anime", "baby": "Baby", "digitalArt": "Digital Art", "natural": "Natural", "beauty": "Beauty", "fantasy": "Fantasy", "saveAvatar": "Save avatar", "editPhoto": "Edit photo", "selectFilter": "Select filter", "selectBackground": "Select background", "askShareToMoment": "Would you like to share to moment？", "shareMomentPhotoToAvatar": "I turned my photo into this profile photo provided by <PERSON><PERSON> to change my avatar!", "deeplinkPhotoToAvatar": "Photo to avatar in Winker", "timesUsedUp": "Times used up", "uploadTimeUsedUpDesc": "Your upload times used up today, come back tomorrow!", "selectSinglePhoto": "Select a single photo", "uploadClearPhoto": "Please upload clear upper body photos, otherwise conversion may fail.", "faceNotDetected": "Face are not detected. Please try again.", "picturesTips": "Pictures are more touching!", "completeProfile": "Complete profile", "completeProfileContentMatch": "Get more priority and conversation opportunities by completing your profile.", "completeProfileContentGiftChat": "Make other people reply to you easily by completing your profile!", "youHaveInterests": "You have three interests in common!", "heLiving": "He living in: your nearby!", "sheLiving": "She living in: your nearby!", "weHaveTheInterests": "We have the same interests!", "canWeGetKnow": "Can we get to know?", "avatarPreview": "Avatar preview", "history": "History", "setToAvatar": "Set to avatar", "deleteHistory": "Delete history？", "userContent": "User content", "reportUserTitle": "Report User", "blockUserTitle": "Block User", "niceToMeetYouInBioGuide": "Hi, <PERSON> to meet you!", "completedProfile": "You have completed the profile", "completeYourProfileInChat": "Complete your profile get reply easily!", "camera": "Camera", "cameraPermission": "\"Winker\" would like to access your Camera and collect your photo data to enable upload photo, camera functions only when the app is in use.", "cameraAccessBanned": "Camera access not enabled", "enableCameraAccess": "Please go to \"Setting<PERSON>\" > \"Winker\" and enable camera access.", "completePercentInUserCard": "Profile complete {percent}%", "@completePercentInUserCard": {"type": "string", "placeholders": {"percent": {}}}, "selectYourCountry": "Select your country", "maleShareGuideContent": "Find intimate friendship!", "femaleShareGuideContent": "Safe and privacy friendship!", "shareToYouFriends": "Share to you friends", "bioCardTitle": "Congrats！\nYou got Winker ID Card！", "bioCardContent": "Would you like to share to moment make others know you more", "idCardTitle": "Winker ID Card", "giveUpCard": "Whether to give up your id card?", "recreateAgain": "You can recreate by completing the profile again.", "selectYourAppLan": "Select your app language", "multipleFace": "Several faces have been detected. \nSelect one to continue.", "diamonds": "Diamonds", "exchange": "Exchange", "goStore": "Go store", "myBalance": "My Balance", "recharge": "Recharge", "contactUs": "Contact us", "diamondsBalance": "Diamonds Balance", "exchangeHint": "Number of Diamonds", "minExchangeDiamond": "Minimum 10 Diamonds", "exchangeSuccessfully": "Exchange successfully", "rechargeSucceeds": "Recharge successfully", "purchasingWait": "Purchasing...Please wait", "insufficientDiamonds": "Insufficient diamonds, recharge now!", "roomTypeTips": "Type...", "keep": "Keep", "enteredTheRoom": "Entered the room", "atWho": "@{name} ", "@atWho": {"type": "string", "placeholders": {"name": {}}}, "roomId": "ID:{id}", "@roomId": {"type": "string", "placeholders": {"id": {}}}, "tag": "Tag", "announcement": "Announcement", "follower": "Follower", "country": "Country", "theme": "Theme", "lock": "Lock", "mode": "Mode", "roomMicPermission": "Mic permission", "blockLost": "Block list", "password": "Password:{pw}", "@password": {"type": "string", "placeholders": {"pw": {}}}, "roomName": "Room name", "roomAnnounceWordCount": "{count}/1000", "@roomAnnounceWordCount": {"type": "string", "placeholders": {"count": {}}}, "onlineUserCount": "Online User : {count}", "@onlineUserCount": {"type": "string", "placeholders": {"count": {}}}, "everyone": "Everyone", "invitedOnly": "Invited only", "editSuccessfully": "Edit successfully", "owner": "Owner", "sendGifts": "Send Gifts", "admin": "Admin", "leave": "Leave", "hide": "<PERSON>de", "unlock": "Unlock", "roomKickOut": "Kick out", "takeTheMic": "Take the Mic", "leaveTheMic": "Leave the Mic", "lockTheMic": "Lock the Mic", "unlockTheMic": "Unlock the Mic", "welcomeJoinRoom": " , welcome to the room! Don't hesitate to grab the mic and follow us!", "allOnMic": "All on Mic", "allInRoom": "All in room", "pleaseEnterRoomPw": "Please enter a 4-digit password", "roomMode": "Room Mode", "invitation": "Invite mic", "explore": "Explore", "managed": "Managed", "official": "Official", "music": "Music", "sureUnlockRoom": "Sure to unlock your room", "sure": "Sure", "game": "Game", "create": "Create", "chooseChatTopic": "Choose chat topic", "createMyRoom": "Create my room", "random": "Random", "combo": "Combo", "sureKickOutRoom": "Sure to kick this user out of room?he/she will not able to enter your room in 24h", "blockUserPermanently": "Block this user permanently", "inviteYouToTakeMic": "{who} invites you to take the mic", "@inviteYouToTakeMic": {"type": "string", "placeholders": {"who": {}}}, "notice": "Notice", "youHaveKickedOutRoom": "You have been kicked out of the room", "youHaveKickedOutMic": "You will leave the mic now. Come later!", "roomFunction": "Room function", "myMusic": "Play Music", "myLocalMusic": "My local music", "searchForSongs": "Search for songs", "uploadMusicToYourPhone": "No music available, please upload music to your phone first", "scanning": "Scanning...", "totalMusic": "Total: {count}", "@totalMusic": {"type": "string", "placeholders": {"count": {}}}, "pauseMusicWhileTuringOffMic": "Pause music while turning off the microphone", "musicFileNotExist": "Music file does not exist", "playError": "Play error", "exitTheRoom": "Exit the room", "receivedGifts": "Gifts", "highCaseTimes": "Duration", "joined": "Joined", "mins": "mins", "recommendOtherRoom": "Recommend other rooms for you", "takeMicFirst": "Please take the mic first", "confirmDeleteSong": "Confirm to delete this song?", "hostColsedTheRoom": "Host closed the room", "messages": "Messages", "NoPeopleOnMic": "No people on mic", "sendLowercase": "send", "giftNumUpLimit": "Maximum {count}", "@giftNumUpLimit": {"type": "string", "placeholders": {"count": {}}}, "sendUser": "send", "balance": "Balance", "restore": "Rest<PERSON>", "upgradeYourWinkerVersion": "Please upgrade your Winker version", "enterTheRoom": "Enter the room", "enterThePassword": "Enter the password", "noPeopleInRoom": "No people in room", "greaterThanCount": "Charisma or Contribution must be greater than {count}", "@greaterThanCount": {"type": "string", "placeholders": {"count": {}}}, "unqualifiedUser": "Unqualified user", "createRoomGuideFirst": "You can change your room name and cover anytime, select your favorite tag!", "createRoomGuideSecond": "Invite your friends to join party!", "replyToUnlockMore": "Reply to conversations to unlock more!", "voiceParty": "Voice Party", "inviteYouJoinRoom": "Invite you join {name}", "@inviteYouJoinRoom": {"type": "string", "placeholders": {"name": {}}}, "room": "Room", "notification": "Notification", "roomIsOpening": "Your followed room {nick} is opening!", "@roomIsOpening": {"type": "string", "placeholders": {"nick": {}}}, "userHavingParty": "{nick} is opening a party!", "@userHavingParty": {"type": "string", "placeholders": {"nick": {}}}, "searchByRoomId": "Search by room ID", "canNotEnterRoom": "You can't enter the chat room", "roomIsClosed": "The current room is not online, please come back later", "shareYourRoom": "Invite your friends to chat!", "youAreSetAdmin": "You are set as an administrator", "youHadRevokedAdmin": "You had revoked admin", "noMoreRooms": "No more rooms", "takeMicToJoinGame": "Take the mic first to join the game", "gameBalance": "Balance:", "entryFees": "Entry fees:", "liveRoomShareContentAndPwd": "We have funny conversation in here! Come to Winker to join 「{name}」{roomId}! The room password is {pwd}", "@liveRoomShareContentAndPwd": {"type": "string", "placeholders": {"name": {}, "roomId": {}, "pwd": {}}}, "liveRoomShareContent": "We have funny conversation in here! Come to Winker to join 「{name}」{roomId}!", "@liveRoomShareContent": {"type": "string", "placeholders": {"name": {}, "roomId": {}}}, "checkDetailInfo": "Check detail info", "gameRoom": "Game room", "chatRoom": "Chat room", "pleaseExitRoom": "Please exit the room first", "matching": "Matching", "matchingPeoplePlayWith": "Matching people to play with", "editWithoutPermission": "Edit without permission", "roomHasBanned": "The room has been banned", "followThisRoom": "Follow this room", "ready": "Ready", "playerPosition": "Player {pos}", "@playerPosition": {"type": "string", "placeholders": {"pos": {}}}, "msgNew": "New", "gameOver": "Game Over", "playAgain": "Play again", "gameCharge": "Charge 10% commission", "roomLostConnect": "The room is disconnected", "inputCannotBeNull": "The input cannot be empty", "playerNotReady": "Players not ready or not enough", "gameError": "Game error, error code:{code}", "@gameError": {"type": "string", "placeholders": {"code": {}}}, "callInRoomError": "Calls are not supported in the room", "matchInRoomError": "Game matching is not supported in the room", "reload": "Reload", "exitRoomWillLeaveGame": "Exiting the room will leave the game", "exitRoomWillCauseGameFailed": "Exiting the room will cause the game failed", "areYourSureExit": "Are you sure exit?", "notAllowedDuringGame": "Not allowed during the game", "cannotSwitchMode": "You cannot switch modes during the game.", "notAllowedKickGamerDuringGame": "Not allowed to kick gamer during the game", "gameStart": "Game start", "gameOverLowerCase": "Game over", "gameOverAndWinner": "Game over! The winner is {name}", "@gameOverAndWinner": {"type": "string", "placeholders": {"name": {}}}, "tapToOpenMic": "Tap to open the mic", "waitSent": "Wait a while to send.", "goSetting": "Go setting", "networkError": "Network connection failure", "recently": "Recently", "inviteYourFriends": "Invite your friends", "exitRoomTitle": "Are you sure exit the room?", "exitRoomContent": "It can cause the room to close.", "switchMic": "Are you sure change the mic position?", "followRoomContent": "Follow the room to receive party news!", "followUserGuideContent": "Follow the user, join next time!", "followAll": "Follow All", "clickTheMicPosition": "Click the mic position to chat!", "inChatRoom": "In {name} chat room", "@inChatRoom": {"type": "string", "placeholders": {"name": {}}}, "setSuccessfully": "Set successfully", "upMicGuideContent": "Join the Conversation!", "welcomeToVoiceParty": "Welcome to Voice party", "newHandRewardContent": "Here are some greeting gifts for you", "porn": "Porn", "userNotInRoom": "The user not in the room.", "live": "Live", "blockRoomConfirm": "Are you sure you want to block this room?", "getIt": "Get it", "matchSwitch": "Match switch", "matchSwitchContent": "Close will not match people for you.", "open": "Open", "inviteJoinRoom": "Invite to chat!", "invitePlayLudo": "Invite to play <PERSON><PERSON>", "join": "Join", "play": "Play", "deleteCommentSure": "Deleted comments cannot be recovered", "user": "User", "followed": "Followed", "agoraReconnect": "Reconnect network, Please wait…", "switchRoomMode": "Switch room mode", "switchRoomModeContent": "Are you sure change the room mode?", "micApplyHasSent": "Take mic application has been sent", "approvedMic": "Approved to take the mic", "agree": "Agree", "agreed": "Agreed", "userApplyTakeMic": "applies for mic", "applyMicList": "Apply for the mic list", "emptyTheList": "Empty the list", "applyTakeMic": "Apply to take the mic", "takeMic": "Take Mic", "level": "Level", "title": "Title", "ranking": "Ranking", "today": "Today", "weekly": "Weekly", "giftsReceived": "Gift received", "giftsSent": "Gifts sent", "roomGifts": "Room gifts", "daily": "Daily", "monthly": "Monthly", "reward": "<PERSON><PERSON>", "receivedRankExplain": "Ranking is based on the diamond value of gifts you received", "sentRankExplain": "Ranking is based on the diamond value of gifts you sent", "roomRankExplain": "Ranking is based on the diamond value of gifts room received", "diamondRank": "Diamonds ranking", "diamondRankExplain": "Ranking is based on the diamond value of gifts you sent in this room.", "drawRewards": "Draw to win big rewards", "draw": "Draw", "haveDrawDays": "You have draw for {num,plural, =0{{num} day}=1{{num} day}=2{{num} days}few{{num} days}other{{num} days}} continuously.", "@haveDrawDays": {"type": "string", "placeholders": {"num": {}}}, "userLevel": "User Level", "levelMedal": "Level Medal", "levelUpRewards": "Level Up rewards", "waysToLevelUp": "Ways to Level Up", "benefitsHighLevel": "Benefits of High Level", "benefitsLevelContent1": "1. You will get rewards when you level up to special level.", "benefitsLevelContent2": "2. Higher levels will help you get more attention.", "levelIsIncreasing": "Your level is increasing at standard speed.", "exp": "{num} Exp", "@exp": {"type": "string", "placeholders": {"num": {}}}, "todayUpperLimit": "Today's upper limit: ", "hot": "Hot", "related": "Related", "createMyRoomUpCase": "Create My Room", "closed": "Closed", "recentlyRoomEmpty": "You have not joined any rooms.", "goFindRooms": "Go Find Rooms", "ludoMatch": "Ludo Match", "bonus": "Bonus", "levelUpgradeTitle": "Congrats! Your level up to", "wearTitle": "Wear Title", "activity": "Activity", "currentTitle": "Currently-worn titles", "empty": "Empty", "unknownError": "Unknown error", "rewardsHasSent": "The rewards has been sent to you.", "plagiarism": "Plagiarism", "reportDescribe": "Describe your problem you are experiencing...", "attachment": "Attachment", "reportAttachmentContent": "Optional, the video size does not exceed 20M", "pleaseDescribeReport": "Please describe the problem you want to report.", "cancelPin": "Cancel pin", "congratsYouGot": "Congrats! You got", "rewardsSentYourBackpack": "Rewards have been sent to your backpack.", "titleWearInstructions": "Title wear instructions", "titleWearDesc": "You can wear titles you earned in Winker. The source of the Title comes from the activities and achievements you finish in the Winker. The titles you are wearing will be displayed in the profile and room profile. You can wear a maximum of three titles.", "personalInformation": "Personal information page", "userProfileCard": "Personal information page in voice room", "chatInTheRoom": "Chat in the room", "ludoGame": "Ludo game", "followedRoomEmpty": "You have not followed any rooms.", "thankForSubmit": "Thanks for your submit", "formIdEmpty": "Form id empty", "updateTime": "Update time: {time}({timeZone}{timeOffset})", "@updateTime": {"type": "string", "placeholders": {"time": {}, "timeZone": {}, "timeOffset": {}}}, "enterEffect": "Enter effect", "roomTheme": "Room theme", "sendToFriend": "Send to friend", "userHasItem": "The user has purchased the item", "unqualified": "Unqualified", "upgradeRoomLevelTip": "Please upgrade your room to level {num} to change the settings", "@upgradeRoomLevelTip": {"type": "string", "placeholders": {"num": {}}}, "numDiamonds": "{num,plural, =0{{num} diamond}=1{{num} diamond}=2{{num} diamonds}few{{num} diamonds}other{{num} diamonds}}", "@numDiamonds": {"type": "string", "placeholders": {"num": {}}}, "roomLevel": "Room level", "roomLeveUpperLimit": "Room Exp daily upper limit: ", "changedInviteOnly": "The owner can change mic permission to invitation-only", "roomAdminsTitle": "Room Admins", "roomAdmins": "{num} admins", "@roomAdmins": {"type": "string", "placeholders": {"num": {}}}, "coins": "Golds", "roomLevelUpgrade": "Congrats! Room upgrade to LV.{num}", "@roomLevelUpgrade": {"type": "string", "placeholders": {"num": {}}}, "whoEnterTheRoom": "enter the room", "checkInBackpack": "Check in backpack", "sendGoodsByUser": "{user} send a {goods} {name} to you.", "@sendGoodsByUser": {"type": "string", "placeholders": {"user": {}, "goods": {}, "name": {}}}, "my": "My", "goodsHasExpired": "The item has expired", "pleaseSelectUser": "Please select the user who want to send", "alreadyInThisRoom": "Already in this room", "recommend": "Recommend", "youHasItem": "You have purchased the item", "tooFrequentlyTip": "Request too frequently, please try again later", "micIsLocked": "Mic position is locked", "passwordWrong": "Password wrong", "unfollowRoomWillRemoveAdminIdentity": "Unfollow room will remove your admin identity", "drawForFree": "Draw\nfor free", "onlyAdminCanPlayMusic": "Only owner and admin can play the music", "newFans": "New fans", "diamondsGift": "Diamonds gift", "sendGiftToOwner": "Send free newcomer gift to the owner", "sendGiftToAdmin": "Send free newcomer gift to the admin", "ludo": "<PERSON><PERSON>", "uno": "Uno", "knifeChallenge": "Knife game", "dominoes": "Dominoes", "playCenter": "Play center", "invitePlayKnife": "Invite to knife challenge", "invitePlayDomino": "Invite to play <PERSON><PERSON><PERSON>", "youGotFriendsInHala": "You got {count} friends in Winker, please give us 5 stars~", "@youGotFriendsInHala": {"type": "string", "placeholders": {"count": {}}}, "youAlreadyGotConversations": "You already got {count} conversations in Winker, please give us 5 stars", "@youAlreadyGotConversations": {"type": "string", "placeholders": {"count": {}}}, "forSomeoneToMatchYou": "Someone matches you in 3 seconds, please give us 5 stars!", "youHaveFollowersAndFriends": "You have {followers} followers and {friends} friends in Winker waiting for you", "@youHaveFollowersAndFriends": {"type": "string", "placeholders": {"followers": {}, "friends": {}}}, "youGetMatchEverySeconds": "You get a match every three seconds…", "transfer": "Transfer", "round": "Round ", "ofToday": " of Today", "gameSelectTips": "Select the quantity of diamonds > Select items", "selectTime": "Select Time", "drawing": "Drawing", "todayWinnings": "Today's Winnings", "results": "Results:", "gameRecords": "Participation Records", "howToPlay": "How to Play", "turntableGameWin": "Congrats! You won {num} diamonds in this round.", "@turntableGameWin": {"type": "string", "placeholders": {"num": {}}}, "turntableGameFail": "Sorry, you didn't win in this round.", "turntableGameNoJoin": "You didn't participate in this round.", "biggestWinners": "Biggest winners of this round", "sureSpendDiamonds": "Are you sure you want to spend {num} on this item?", "@sureSpendDiamonds": {"type": "string", "placeholders": {"num": {}}}, "nextNoShow": "Don't show next time", "bonusPackage": "Bonus package", "gotRechargeRewards": "You got recharge rewards!", "getBigBonus": "get big bonus values 1000 diamonds", "luckyWheel": "Lucky Wheel", "start": "Start", "waiting": "Waiting", "rules": "Rules", "entryFee": "Entry Fee:", "joinTheLuckyWheel": "Join the Lucky wheel", "out": "OUT", "moreThan3ParticipantsToStartLuckyWheel": "A minimum of {count} participants is required to start the Lucky Wheel. Once it starts, others cannot join.", "@moreThan3ParticipantsToStartLuckyWheel": {"type": "string", "placeholders": {"count": {}}}, "luckyWheelRules": "1. Only the room owner and admin can start the Lucky Wheel and set the entry fee.\n\n2. After being started, the Lucky Wheel requires a minimum of 3 participants within 5 minutes to start spinning.\n\n3. If there are 3 or more participants within 5 minutes, the room owner ann admin can manually spin the Lucky wheel, or the Lucky wheel will start spinning automatically 5 minutes after being started. If there are less than 3 participants within 5 minutes, the Lucky Wheel will automatically closed and the entry fee will be refunded.\n\n4. When the Lucky Wheel starts spinning, it will randomly pick an eliminated participant until there's only one participant left. This participant wins 90% of the total entry fee.", "sureToCloseLuckyWheel": "Are you sure close the Lucky wheel? The entry fee will be refunded to participants", "luckyWheelIsEndAndReturnYourBalance": "The Lucky Wheel is ended. Diamonds has been returned to your balance. ", "luckyWheelIsAutoEnd": "Due to the Lucky Wheel hasn’t started for a long time, the event is closed. Diamonds has been returned to your balance.", "sureToJoinLuckyWheel": "Are you sure spend {diamonds} diamonds to join the Lucky wheel. The winner will take 90% of the prize pool.", "@sureToJoinLuckyWheel": {"type": "string", "placeholders": {"diamonds": {}}}, "doNotRemindMeNextTime": "Do not remind me next time", "iStartedLuckyWheel": "I started the Lucky wheel.", "joinNow": "Join now", "gaming": "Gaming", "luckyWheelIsEnd": "The Lucky wheel is ended.", "luckyWheelWinner": "{name} was the lucky winner in the Lucky Wheel event and won {count} diamonds!", "@luckyWheelWinner": {"type": "string", "placeholders": {"name": {}, "count": {}}}, "superBenefits": "Super benefits", "countriesRegions": "Countries/Regions", "participationRecord": "Participation Record", "winningFruit": "Winning Fruit: {name}", "@winningFruit": {"type": "string", "placeholders": {"name": {}}}, "selectedFruits": "Selected Fruits:", "correct": "Correct", "wrong": "Wrong", "topWinnersOfToday": "Top winners of today", "turntableGameRules1": "1. Choose the quantity of diamonds, and then choose a fruit to spend the diamonds on.", "turntableGameRules2": "2. You can select up to 8 fruits in each round. There' s no upper limit to the quantity of diamonds that you can spend.", "turntableGameRules3": "3. For each round, you have 40 seconds to select fruits, after which the winning fruit will drawn.", "turntableGameRules4": "4. If you have spend diamonds on the winning fruit, you'll win the corresponding prize.", "turntableGameRules5": "5. The final interpretation right of this game belongs to <PERSON><PERSON>.", "numRound": "Round: {num}", "@numRound": {"type": "string", "placeholders": {"num": {}}}, "worldwide": "Worldwide", "firstRechargeTitle": "Recharge", "supporter": "Supporter", "enter": "Enter", "eventWithEmoji": "🎉 Event", "createEventForYourRoom": "Create a special event for your room", "events": "Events", "createMyEvent": "Create my event", "createEvent": "Create event", "roomEventMaximum": "The room has reached its maximum activity", "eventDetails": "Event details", "eventName": "Event name", "eventDescTips": "More information, make guest know your event details.", "selectRoom": "Select room", "youCanCreateEventTips": "You can create event for your manage room.", "eventTag": "Event tag", "eventTime": "Event time", "cancelEventCreateTitle": "Exit without finishing?", "cancelEventCreateContent": "If you leave now, your event won't be created and your progress won't be saved.", "startDateAndTime": "Start date and time", "endDateAndTime": "End date and time", "reviewEvent": "Review event", "addCoverPhoto": "Add cover photo", "createByNumDiamonds": "Create({num} Diamonds)", "@createByNumDiamonds": {"type": "string", "placeholders": {"num": {}}}, "eventInReview": "Event in review", "betterLuckNextTime": "Better luck next time", "congratulations": "Congratulations!", "powerBar": "Power bar", "rewards": "Rewards", "giftAwardResetTime": "Daily reset at 00:00 (GMT+3)", "treasureBoxRoomLevelLimit": "Only the room level reach LV.{level} have treasure box", "@treasureBoxRoomLevelLimit": {"type": "string", "placeholders": {"level": {}}}, "giftAwardRules": "1. Only the rooms that reach Level 5 have treasure box.\n2. The box key is obtained by sending diamonds gifts in the room.\n3. When the progress bar of the box key is filled up, the treasure box will be opened.\n4. Everyone in the room has a chance to get the rewards in the treasure box.\n5. It may take a few seconds for the system to deliver the rewards to your account after the treasure box is opened. Diamonds will be added to your balance, Avatar frame, room theme will be added to your backpack.\n6. The higher the level of the treasure box, the bigger the rewards in it.\n7. The progress bar of the box key will be reset at 00:00 (GMT+3) everyday.", "gotFromTreasureBox": "{name} got {award} {count} from Treasure Box", "@gotFromTreasureBox": {"type": "string", "placeholders": {"name": {}, "award": {}, "count": {}}}, "liveNow": "Live Now", "selectYourRoom": "Select your room", "myRoom": "My Room", "adminRoom": "Admin room", "tomorrow": "Tomorrow", "cancelEvent": "Cancel Event", "cancelEventConfirm": "Cancel the event will clears the subscriber, and you need to recreate the event to gather your friends.", "ended": "Ended", "event": "Event", "inviteParticipateEvent": "I invite you to participate in {userName} 's {eventName} party, come and join <PERSON><PERSON> to play together.", "@inviteParticipateEvent": {"type": "string", "placeholders": {"userName": {}, "eventName": {}}}, "youGotFrom": "You got {name} from {from}.", "@youGotFrom": {"type": "string", "placeholders": {"name": {}, "from": {}}}, "applyEventRewards": "Apply event rewards", "unSubscribeTip": "Unsubscribe event you will miss the surprise!", "addToYourCalendar": "Add event to Your Calendar", "keepDeviceCalendar": "Keep tracking this event by adding it to your device's calendar", "addUpcomingEvent": "Add upcoming events that you're subscribed in to the calendar", "addToCalendar": "Add to Calendar", "apply": "Apply", "invalidName": "Invalid name", "invalidDescription": "Invalid description", "invalidTime": "Invalid time", "subscribe": "Subscribe", "subscribed": "Subscribed", "makeAProposal": "Make a proposal", "proposal": "Proposal", "proposalPaperDesc": "To the world you may be one person, but to one person you may be the world.", "selectSuitor": "Select your soulmate", "intimacyProposal": "Only the intimacy over 5000 can proposal", "goCoupleZone": "Go love zone", "selectRing": "Select a ring to propose", "proposalVowsTitle": "Write vows", "proposalVowsHint": "Write your words to your love...", "selectTheLetter": "Select the letter", "spendLetterConfirm": "Spend {money} diamonds to make a proposal to {name}", "@spendLetterConfirm": {"type": "string", "placeholders": {"money": {}, "name": {}}}, "proposalDesc": "Two parties can reach the intimacy of 5000 to propose. You can increase intimacy by chatting or sending gifts.", "ring": "Ring", "divorce": "Divorce", "emptyIntimacy": "Ooops, you do not have soulmate.\nOnly the intimacy over 5000 can proposal", "emptyRing": "Buy a ring to make a proposal, show your heart!", "notSale": "Not for sale", "loveZone": "Love Zone", "myLoveZone": "My Love Zone", "loveZoneQA": "Love zone Q&A ", "loveZoneAnswer1": "1、How to have couple love zone?", "loveZoneAnswer2": "2、What is the effect to change the ring of wearing?", "loveZoneAnswer3": "3、How to increase the bless value?", "loveZoneQuestion1": "First, you need have an opposite sex friend over 5000 intimacy then you can propose for her/him. When the other party agrees to your proposal, you can have a couple love zone.", "loveZoneQuestion2": "New ring effect will show in your profile and love zone.", "loveZoneQuestion3": "Sending gifts in the Love Zone can increases the bless value. But the zone owner can not get the diamonds rebate and charisma.", "youDoNotHaveCouple": "You do not have couple", "someoneMaybeYouLove": "Chat with your soulmate", "noQualifiedFriends": "No qualified friends", "goToFind": "Go to find", "refuse": "Refuse", "intimacy": "Intimacy", "intimacyWithScore": "Intimacy: {score}", "@intimacyWithScore": {"type": "string", "placeholders": {"score": {}}}, "intimacyTips": "Intimacy over 5000 can make proposal to become couple", "intimacyInfoTitle1": "Just three steps, get a soulmate!", "intimacyInfoTitle2": "Details for couple", "firstStep": "First step", "makeFriends": "Make friends", "secondStep": "Second step", "thirdStep": "Third step", "intimacySecondStepTips": "Chat or Romance gifts increase the intimacy", "intimacyThirdStepTips": "Propose for your mate", "intimacyInfoTips1": "1、First, you can find the opposite sex friends you want to make through the match or chat room.", "intimacyInfoTips2": "2、Chatting and gifting within the Romance category can increase the intimacy of both parties. The daily limit for chatting with a partner is 50, and gifting is unlimited. After 5,000, you can make a proposal.", "intimacyInfoTips3": "3、If you have no interaction with the other person after 7 days, the intimacy will drop by 10 points every day, to a minimum of 0.", "intimacyInfoTips4": "4、You need to purchase a ring in the store to propose to a friend of the opposite sex. When the other person agrees your proposal to become a couple. If the other party does not deal with the proposal for 3 days, it will be invalid automatically. If you propose failed, the ring will be returned to your backpack.", "intimacyInfoTips5": "5、If you terminate the couple relationship, you can apply to the other party to terminate the relationship with mutual consent. The ring will disappear when the relationship is dissolved. You have to wait three days before you can propose again.", "together": "Together", "blessValue": "Bless value", "anniversary": "Anniversary", "ringBox": "Ring box", "ringDisplay": "Ring display", "recentlyLoveZoneGifts": "Recently love zone gifts", "sendGiftCanIncreaseBlessValue": "Send gifts can increase bless value", "proposalMsg": "Message", "haveTogetherDays": "You have been together {num,plural, =0{{num} day}=1{{num} day}=2{{num} days}few{{num} days}other{{num} days}}, please cherish your relationship..", "@haveTogetherDays": {"type": "string", "placeholders": {"num": {}}}, "findSoulmateFirst": "Find your soulmate first", "waitBeforeDivorce": "Wait 2 days before reinitiating the divorce", "youRejectDivorce": "You reject {name} divorce apply, please cherish…", "@youRejectDivorce": {"type": "string", "placeholders": {"name": {}}}, "annulledCouple": "You have annulled the couple relationship", "threeDayReject": "Three days without processing will automatically reject", "goProposal": "Go proposal", "compulsoryDivorce": "Compulsory", "consensualDivorce": "Consensual", "compulsoryDivorceTip": "Spend {count} diamonds to compulsory divorce. Your marriage will be over and the rings will disappear", "@compulsoryDivorceTip": {"type": "string", "placeholders": {"count": {}}}, "consensualDivorceTip": "When other side approved, the rings will disappear, and will be single. Are you sure send the divorce apply?", "intimacyScoreIncrease": "Congrats! The intimacy between you has increased by ", "proposalSendWait": "Proposal has been send to your love. Please be patient while {name} considers your request. Pray for you.", "@proposalSendWait": {"type": "string", "placeholders": {"name": {}}}, "checkMyProposal": "Check my proposal", "applyCompulsoryDivorce": "apply to compulsory divorce, please make your choice.", "propose": "Propose", "intimacyRanking": "Intimacy ranking", "allRings": "All Rings", "currentlyRing": "Currently ring", "backpackRings": "Backpack rings", "wearANewRing": "Wear a new ring", "ringGuidelines": "Ring guidelines", "wearRingExplain": "1、You can use any value rings.\n2、Wear a new ring do not need others approve.\n3、When you use a new ring, the original ring will not disappear, will be put into the ring box.\n4、It will not change your anniversary times.", "becomeCouple7Days": "Become couple 7 days", "becomeCouple30Days": "Become couple 30 days", "becomeCoupleHalfYear": "Become couple half year", "becomeCoupleOneYear": "Become couple one year", "whoBirthday": "{who}'s birthday", "@whoBirthday": {"type": "string", "placeholders": {"who": {}}}, "editAnniversary": "Edit Anniversary", "twoDays": "2 Days", "togetherDays": "{num,plural, =0{{num}  Day}=1{{num}  Day}=2{{num}  Days}few{{num}  Days}other{{num}  Days}}", "@togetherDays": {"type": "string", "placeholders": {"num": {}}}, "togetherDaysNewLine": "{num,plural, =0{{num}\n Day}=1{{num}\n Day}=2{{num}\n Days}few{{num}\n Days}other{{num}\n Days}}", "@togetherDaysNewLine": {"type": "string", "placeholders": {"num": {}}}, "date": "Date", "pleaseEnterTitle": "Please enter title.", "coupleCenter": "<PERSON><PERSON><PERSON>", "detailsForCouple": "Details for couple", "gameMatch": "Game Match", "beMyBetterHalf": " be my better half", "proposeMsgDesc": "I purchased {name}，make a proposal for you", "@proposeMsgDesc": {"type": "string", "placeholders": {"name": {}}}, "intimacyImageTipsTitle1": "Couple center for proposal", "intimacyImageTipsTitle2": "Propose / Divorce entrance", "intimacyImageTipsTitle3": "Couple profile reference", "intimacyImageTipsTitle4": "Love zone reference", "rejected": "Rejected", "autoRejected": "Auto rejected", "invalid": "Invalid", "away": "Away", "past": "Past", "divorcePetitionWait": "Divorce petition has been send to your cp, please wait for the other party to deal with it.", "divorceCoolingPeriod": "You are still in the cooling period of divorce, please wait 3 days before proposing", "rejectYourDivorce": "rejected your divorce.", "illegalContentReEdit": "The text contains illegal content, please re-edit", "dearMale": "Dear", "dearFemale": "Dear", "nameContainsIllegal": "{name} contains illegal content, please re-edit", "@nameContainsIllegal": {"type": "string", "placeholders": {"name": {}}}, "numSupporter": "{num} supporter", "@numSupporter": {"type": "string", "placeholders": {"num": {}}}, "numTimesForFruity": "{num} times", "@numTimesForFruity": {"type": "string", "placeholders": {"num": {}}}, "waitingToPlay": "Waiting to play", "playThisVideo": "Play this video", "video": "Video", "inviteToWatchVideo": "Invite to watch video", "soundEffect": "Sound effect", "soundEffectIsPlaying": "Sound effect is playing", "party": "Party", "uploadMusic": "Upload Music", "localMusic": "Local Music", "notConnectedWifi": "Not connected to Wi-Fi", "pleaseConnectWifi": "Please connect your phone to Wi-Fi first", "connectedWifiTips": "Make sure Phone and PC connected to the same Wi-Fi, then open <PERSON>'s browser and enter the link below.", "typeInPcBrowser": "Type in the following link on PC browser", "stayInThePage": "Stay in the page while uploading", "uploadList": "Upload list", "got": "got", "fromTreasureBox": "from Treasure Box", "addNewRingInYourBackpack": "Please add new ring in your backpack", "yourPhoneStorageInsufficient": "The storage space of your mobile phone is insufficient.", "networkErrorAndReload": "Network error, please try again reload.", "errorOccurred": "Error occurred, please reload.(code: {code})", "@errorOccurred": {"type": "string", "placeholders": {"code": {}}}, "youAreSuperAdmin": "You are in super admin mode and cannot join this function.", "OnlinePeopleAtParty": "Online people at the party", "chattingInRoom": "chatting in the voice party now.", "chatInputFucPhoto": "Photo", "chatInputFucQA": "Q&A", "chatInputFucGuessFistOn": "Guess Fist invite", "chatInputFucGuessFistOff": "Turn Off Guess Fist", "calculator": "Calculator", "calculatorHint": "Enter activity theme...", "duration": "Duration:", "minutes": "{time,plural, =0{{time} min}=1{{time} min}=2{{time} mins}few{{time} mins}other{{time} mins}}", "@minutes": {"type": "string", "placeholders": {"time": {}}}, "sendGiftsSupportHer": "Send gifts to support her", "sendGiftsSupportHim": "Send gifts to support him", "distanceToTheTopOne": "Distance to the Top 1", "topSupporter": "Top supporter", "topCharming": "Top charming", "total": "Total", "people": "People", "countdown": "Countdown", "end": "End", "WelcomeToJoinParty": "Welcome to join our party!", "calculatorClosed": "Calculator closed.", "calculatorOpenedBy": "Calculator opened by", "topSupporterIs": "The top supporter is", "topCharmingIs": "The top charming is", "switchRoomCloseCalculator": "Switch the room mode will close the calculator.", "calculatorWillClosed": "Calculator will be closed in 1 minutes.", "videoTime": "{time}/{total}", "@videoTime": {"type": "string", "placeholders": {"time": {}, "total": {}}}, "changeVideoVolume": "Change Video Volume", "thisWontAffectOther": "This won't affect other", "selectTheVideo": "Select the video", "userPausedVideoMsg": "{identity} {name} paused the video play.", "@userPausedVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "userPlayVideoMsg": "{identity} {name} play the video.", "@userPlayVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "userCloseVideoMsg": "{identity} {name} closed the video.", "@userCloseVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "videoCannotPlay": "This video cannot play, please change to another one", "videoTryAgain": "Error occurred, please try again.", "pausedVideo": "paused the video play", "sureStopVideo": "Are you sure wanna stop watching videos?", "changeVideoModeTips": "Are you sure change the room mode? It can cause the video stop playing.", "closeVideoRoomTips": "It can cause the room to close and stop watching videos.", "manager": "Manager", "operationTooFrequent": "Operation too frequent. Please try again later.", "videoRoom": "Video room", "youReceivedANewMsg": "You received a new message", "messageNotification": "Message Notification", "closeWillNotShow": "Close will not show it", "unsupportedVideo": "Unsupported video, please upgrade the app version.", "luckyBag": "Lucky bag", "instructions": "Instructions", "instructionDetail": "1、Steps to send a lucky bag: choose the quantity of the gifts or diamonds to be put into a luck bag then choose the number of recepients, finally tap the send buttion.\n2、Each recipients will get a random quantity of gifts or diamonds from the lucky bag. Diamonds will be sent to your wallet and gifts will sent to your backpack. Please check the details in chat notification.\n3、A lucky bag invalid for 10 minutes. The gifts or diamonds in a lucky bag if not claimed within 10 minutes will be refunded to the sender.", "giftValue": "Gift Value:", "numOfRecipients": "Number of recipients:", "world": "World", "diamondQuantity": "Diamond quantity:", "showInWinkerWorld": "Show in Winker world!", "diamondCanBroadcast": "Only value > 1000 diamonds can broadcast to all rooms", "luckyBagDetails": "Lucky bag details", "sendLuckyBag": "Send a lucky bag", "openItNow": "Open it now > ", "iGot": "I got", "fromLuckyBag": "from lucky bag.", "checkInDetails": "check in details! > ", "viewDetail": "View details > ", "haveOpenedLuckyBag": "You have opened this lucky bag", "luckyBagEmpty": "Sorry, the lucky bag is empty..", "bagExpired": "This bag has expired. ", "wishYouLuck": "Wish you luck!", "NoShowNextTime": "Don't show next time", "openedNum": "Opened {receiveNum}/{total}", "@openedNum": {"type": "string", "placeholders": {"receiveNum": {}, "total": {}}}, "luckiestDraw": "Luckiest draw", "giftFrom": "Gifts from", "truthDare": "Truth & Dare", "truthDarePeopleCount": "{count}/10", "@truthDarePeopleCount": {"type": "string", "placeholders": {"count": {}}}, "truthDareQuestion": "{user} punished in this round, please accept the punishment:\n{question}", "@truthDareQuestion": {"type": "string", "placeholders": {"user": {}, "question": {}}}, "host": "Host", "intimacyRewardTips": "Congrats! \nYou both intimacy increase", "cpTask": "Cp task", "cpTaskDailyTime": "Daily reset at 0:00 (GMT+3)", "blindDate": "TMO", "pick": "Pick", "announce": "Announce", "operation": "Operation", "newRound": "New round", "oppositeSexPeople": "At least have an opposite sex people can start next round", "truthDareRunAway": "{user} already exit the room, this round of game is over.", "@truthDareRunAway": {"type": "string", "placeholders": {"user": {}}}, "punishedInRound": "Punished in this round", "cannotSwitchRoomModes": "You cannot switch room modes during the game", "truth": "Truth", "dare": "Dare", "waitingFemaleChoice": "Wait for her choice", "waitingMaleChoice": "Wait for his choice", "selectYourPunishment": "Select your punishment", "cpRoomOpen": "{name} already open, come and join the mic position to participate the couple Pick.", "@cpRoomOpen": {"type": "string", "placeholders": {"name": {}}}, "cpAnnounceTips": "The host will announce everyone's selection, please wait patiently.", "userPickUser": "{mainUser} Pick the {targetUser} in this round.", "@userPickUser": {"type": "string", "placeholders": {"mainUser": {}, "targetUser": {}}}, "userCpSuccess": "Congrats! {mainUser} and {targetUser} has been successfully matched, finish the couple task right now! ", "@userCpSuccess": {"type": "string", "placeholders": {"mainUser": {}, "targetUser": {}}}, "cpNewRound": "The host {name} start a new round.", "@cpNewRound": {"type": "string", "placeholders": {"name": {}}}, "announceHisPick": "Announce his pick", "announceHerPick": "Announce her pick", "closeSeconds": "Automatically close after {count} s", "@closeSeconds": {"type": "string", "placeholders": {"count": {}}}, "matchSucceeds": "Match succeeds", "announceRuleTitle": "Final result", "announceRule": "1、Now you can choose to end the section and announce the results\n2、If two players pick each other up, that will be a successful match", "later": "Later", "userA": "User A", "userB": "User B", "inviteToTruthDare": "Invite to play Truth&Dare", "vipCenter": "VIP Center", "vip_setting": "VIP Setting", "whoCanChatMe": "Who can chat to me?", "mysteriousVisitor": "Mysterious Visitor", "off": "Off", "on": "On", "onlyFriends": "Only friends", "createConversationBySendGift": "Create conversation with you by sending gift", "approvalRequiredToCreateConversation": "Approval is required to create conversation", "onlyApplication": "Only application", "mysteriousVisitorFunction": "Mysterious visitor function on-off", "uploadPicAvatar": "Upload pic Avatar", "uploadGifAvatar": "Upload <PERSON><PERSON>", "vip": "VIP", "notVipUseFunction": "You can not use this function. Please enable privilege.", "validUntilData": "Valid until {date}", "@validUntilData": {"type": "string", "placeholders": {"date": {}}}, "firstLevelTips": "{num} points to level up.", "@firstLevelTips": {"type": "string", "placeholders": {"num": {}}}, "rechargeNow": "Recharge now", "vipNum": "VIP {num}", "@vipNum": {"type": "string", "placeholders": {"num": {}}}, "expNum": "Exp {num}", "@expNum": {"type": "string", "placeholders": {"num": {}}}, "privilegesTitle": "Privileges({num}/{total})", "@privilegesTitle": {"type": "string", "placeholders": {"num": {}, "total": {}}}, "unlimited": "Unlimited", "onlyVipEmoji": "Only vip user can send this emoji", "expText": "Exp", "vipSettled": "VIP Level Settled", "changeCover": "Change cover", "newFriendsRequest": "New friends request", "onlyFriendsCanChat": "You have not opened only friends can chat", "friendsRequestSettings": "Settings -> VIP Settings -> Who can chat to me?", "noNewFriendRequest": "You have no new friend request", "vipCanChangeFriendsRequestSettings": "VIP users can change friends request settings.", "uploadBackground": "Upload background", "addFriend": "Add friend", "added": "Added", "applicationHasBeenSent": "Application has been sent, please wait for the user process", "vipImage": "VIP image", "youHaveReachedVip": "Congrats! You have reached VIP level {level}, ", "@youHaveReachedVip": {"type": "string", "placeholders": {"level": {}}}, "youHaveDownVip": "Your VIP level is down to level {level}, ", "@youHaveDownVip": {"type": "string", "placeholders": {"level": {}}}, "checkPrivilegeDetails": "please check your privilege details!", "checkMewPrivilegeDetails": "please check your new privilege details.", "uploadSuccessfully": "Upload successfully", "vipLevel": "1、VIP Level", "vipLevelDetail": "VIP membership is acquired by earning VIP Exps through your in-app purchases.", "vipValidity": "2、VIP Validity", "vipValidityDetail": "The period of validity for each VIP level is 90 days. During the 90-day period, if your VIP Exps meet the requirement for a higher level, your VIP level will be immediately upgraded and your VIP Exps will be reset to 0. Upgrading by skipping a certain level is supported.\nIf your VIP level is not upgraded during the period, when the period is over, your VIP level will be reset according to the VIP Exps you earned within the period, and your VIP Exps will be reset to 0. For maintain your current VIP level, you need to accumulate the VIP Exps needed from the previous level during the period. Otherwise, your VIP level will be downgraded.\nIf you are not VIP yet, when your VIP Exps meet the requirements of a certain VIP level during a 90-day period, you will reach this VIP level and your VIP Exps will be reset to 0. If you fail to reach any VIP level during the 90-day period,your accumulated VIP Points will be reset to 0 when the period ends.", "vipExps": "3、VIP Exps", "vipExpsDetail": "You can earn VIP Exps by purchasing diamonds, 1 USD equals to 10 VIP Exps. There's no upper limit on how many VIP Exps you can earn every day. If you refund a purchase, all VIP Exps you earned for that purchase will be deducted.The image below shows the VIP Points needed from previous level.", "vipFreeze": "4、VIP Freeze", "vipFreezeDetail": "If you don't have enough VIP Exps to be deducted for a refund, your VIP membership will be frozen until you pay off the VIP Exps you owe. Please note that if your VIP membership is not unfrozen within 90 days of freeze, your VIP Points will not be reset to 0.", "vipExpRequirement": "VIP exp requirement", "openAddFriendsFunction": "1.Open add friends function in the settings", "otherUsersWillSendYou": "2.Other users will send you a friend request", "youCanManagerWhoCanTalkYou": "3.You can manage who can talk to you", "youCanInitiateConversation": "4.Once you agree, you can initiate a conversation", "openMysteriousSetting": "1.Open mysterious visitor in settings", "visitSomeoneProfile": "2.Visit someone’s profile without letting them know who you are", "guideLines": "Guidelines", "vipBadge": "VIP Badge", "privileges": "Privileges", "vipBadgeTips1": "Chat messages in room", "vipBadgeTips2": "Profile card in room", "vipBadgeTips3": "Online user list", "vipBadgeTips4": "Profile", "vipBadgeTips5": "Moment", "vipEnterEffect": "Exclusive Enter effect", "vipEnterEffectTips": "Attract everyone's attention with a gorgeous enter effect", "vipProfileCard": "Exclusive Profile card", "vipProfileCardTips": "Show your honor with a distinguished profile card", "vipAvatarFrame": "Exclusive VIP avatar frame", "vipAvatarFrameTips": "Get more attention with your special VIP frame", "vipChatFrame": "Exclusive VIP chat frame", "vipChatFrameTips": "Your message with gorgeous frame show your friends", "vipColoredUsername": "Colored username", "vipColoredUsernameTips": "Attract everyone's attention with a colored name", "vipPersonalId": "Unique personal id", "vipPersonalIdTips": "Contact VIP customer service to get a unique personal ID for free", "vipCustomizeAvatar": "Customize avatar", "vipCustomizeAvatarTips": "You can upload your favorite photo and make it your avatar", "vipSendRoomImage": "Send image in room", "vipSendRoomImageTips": "You can send image in room", "vipProfileBackground": "Customize profile background", "vipProfileBackgroundTips": "You can upload your favorite photo and make it your profile background", "vipCustomizeRoomTheme": "Customize room theme", "vipCustomizeRoomThemeTips": "Set a custom room theme to show your style", "vipVipGifts": "Exclusive VIP gifts", "vipVipGiftsTips": "Only VIP user can send this gifts", "vipUserList": "Front row on User list", "vipUserListTips": "Enjoy a front row on the list of any rooms", "vipStickersOnMIC": "Exclusive stickers on MIC", "vipStickersOnMICTips": "Use exclusive stickers when you're speaking on MIC.", "vipMysteriousVistors": "Mysterious vistors", "vipMysteriousVistorsTips": "Visit someone's profile without letting them know who you are", "vipDoubleRewards": "Double sign in and tasks rewards", "vipDoubleRewardsTips": "Get double rewards for signing in and completing tasks", "vipUnlimitedText": "Unlimited text", "vipUnlimitedTextTips": "You can send text whatever you want without restriction", "vipActivityPromotion": "Room activity promotion", "vipActivityPromotionTips": "Contact VIP customer service to send post to promote your room activities {num} a month", "@vipActivityPromotionTips": {"type": "string", "placeholders": {"num": {}}}, "vipAddFriends": "Add friends function", "vipAddFriendsTips": "When someone initiates a conversation with you, they have to ask for your consent", "vipCustomerService": "Customer service expert", "vipCustomerServiceTips": "Enjoy the best service from our customer service expert", "vipAdSendGifts": "Customer representatives send gifts", "vipAdSendGiftsTips": "Contact VIP customer service to send gifts in your room", "vipMicProtection": "MIC Protection", "vipMicProtectionTips": "Protection from removal out of MIC", "vipRoomProtection": "Room Protection", "vipRoomProtectionTips": "Protection from removal out of room", "vipNamedGifts": "Named gifts", "vipNamedGiftsTips": "Contact our VIP service. Your user name will show on special gifts", "vipExclusiveAvatarFrame": "Exclusive customize avatar frame", "vipExclusiveAvatarFrameTips": "Contact our VIP service. We will create your own avatar frame for you", "noVip": "NO VIP", "imageIllegal": "The image contains illegal content, please re-upload", "pk": "PK", "votePk": "Vote PK", "giftPk": "Gift PK", "eachPersonCanVoteOnce": "Each person can vote once a time", "calculatedByGifts": "Calculated by the gifts received", "selectDuration": "Select duration(minutes)", "startPK": "Start PK", "jackpotMode": "Jackpot mode", "chooseAtLeastTwo": "Choose at least two people", "jackpotModeRule": "PK joiner will not receive gift refund during the PK. PK Winner will get 30% gift value after the PK.", "specialDiscountGift": "Special discount gift", "giftDiscountPercent": "+{percent}%", "@giftDiscountPercent": {"type": "string", "placeholders": {"percent": {}}}, "valueDiamond": "Value: {diamond}", "@valueDiamond": {"type": "string", "placeholders": {"diamond": {}}}, "buyWithAmount": "{amount} buy", "@buyWithAmount": {"type": "string", "placeholders": {"amount": {}}}, "giftSentYourBackpack": "Gift have been sent to your backpack.", "vote": "Vote", "voted": "Voted", "support": "Support", "pkEndedTips": "The PK ended in a draw", "pkWinner": "PK Winner", "youWillLosePkOnceLeaveRoom": "You will lose the PK once you leave the room", "youCanOnlyOpenOnePlay": "You can only open one play at a time", "pkResult": "PK Result", "win": "Win", "inPk": "in Pk", "userStartGiftPk": "{user} start a gift PK.", "@userStartGiftPk": {"type": "string", "placeholders": {"user": {}}}, "userStartVotePk": "{user} start a vote PK.", "@userStartVotePk": {"type": "string", "placeholders": {"user": {}}}, "userIsWinnerInPk": "{user} is the winner in the PK.", "@userIsWinnerInPk": {"type": "string", "placeholders": {"user": {}}}, "thePkEndInDraw": "The PK end in a draw.", "userGetDiamondsInPk": "{user} gets {count} diamonds in PK.", "@userGetDiamondsInPk": {"type": "string", "placeholders": {"user": {}, "count": {}}}, "tryToTalkHerLabels": "Try to talk her labels", "tryToTalkHisLabels": "Try to talk his labels", "callEnded": "Call ended", "bothSidesShowIdentities": "Both sides show identities, no time limitation", "weakSignal": "Weak signal", "mrWinker": "Mr.<PERSON><PERSON>", "missWinker": "<PERSON>", "hangUp": "Hang up", "hangUpConfirm": "Hang up Confirm", "talkLittleBitLonger": "Talk a little bit longer. Maybe you will find more surprise! Still want to hang up?", "keepTalking": "Keep talking", "freeChance": "{num} free chance", "@freeChance": {"type": "string", "placeholders": {"num": {}}}, "setUp": "Set up", "matchNow": "Match now", "matchTimesIsOver": "Today's match times is over. Please wait until tomorrow.", "useDiamond": "Use {diamond}", "@useDiamond": {"type": "string", "placeholders": {"diamond": {}}}, "toMatch": " to match", "moreFreeChance": "More free chance", "noFreeToday": "No free chance today", "stayInRoomNumMin": "Stay in room for 30min ({num}/30)", "@stayInRoomNumMin": {"type": "string", "placeholders": {"num": {}}}, "microphoneOn": "Microphone on", "microphoneOff": "Microphone off", "earphone": "Earphone", "speaker": "Speaker", "showIdentity": "Show identity", "me": "Me", "connecting": "Connecting...", "inCall": "In call", "thisVoiceCallDisconnected": "Sorry. This voice call is disconnected, please try again", "youCanAccessEachOtherProfiles": "In call with hidden identity. You can access each other's profiles by showing your identities.", "identityShowed": "Identity showed", "pkIsNotFinished": "PK is not finished", "onlyVipPurchase": "Only vip can purchase it", "callMatchTips": "The identities of both sides will be hidden by default during voice call. You can show the identity anytime you like.", "callMatchTip1": "We are matching user for you. Please wait patiently", "callMatchTip2": "Share your moments with others", "callMatchTip3": "Abuse and provoke is strictly forbidden", "overCallMatch": "Currently few users. Please try later.", "callMatchSetTips": "<PERSON> will not call call match for you.", "callMatchRule": "1. Welcome to Call Match! You can match with real users and feel free to talk with them.\n2. Each match will cost some time. It depends on how many user online now. So please be patient.\n3. For each talk, you will have 4min to talk with hidden identity. If you like to talk longer, you can show your identity and convince the opposite to show as well. Once both sides showing identities, you will get extra talk time.\n4. Also, showing identity can allow the opposite to check your profile and learn you more! Showing each other's identities also unlocks the conversation after the call ends\n5. If you get any abused, provoked or impolite behavior, please report to us. Also, you can rate each call (good or bad) and we will get your feedback.", "awaitingResponse": "Awaiting response...", "pleaseRateThisCall": "Please rate this call", "callTime": "Call time: {time}", "@callTime": {"type": "string", "placeholders": {"time": {}}}, "thanksForYourRating": "Thanks for your rating", "matchAgain": "Match again", "someoneCallingYou": "Someone is calling you!", "automaticallyConnected": "A voice call is automatically connected for you. Please get ready.", "lvNum": "Lv.{num}", "@lvNum": {"type": "string", "placeholders": {"num": {}}}, "lv": "Lv.", "family": "family", "familyCapital": "Family", "createFamily": "Create Family", "familyCover": "Family cover", "familyName": "Family name", "familyAnnouncement": "Family announcement", "applySuccess": "Apply successfully", "pleaseWaitApproval": "Please wait for the approval", "familyShareContent": "Come and join <PERSON><PERSON> family {name} ! Let's chat and have fun!", "@familyShareContent": {"type": "string", "placeholders": {"name": {}}}, "familyRepresentative": "Family members", "familyRoom": "Family room", "familyHonorRanking": "{name}: {position}", "@familyHonorRanking": {"type": "string", "placeholders": {"name": {}, "position": {}}}, "id": "ID", "weeklyRank": "Weekly rank", "giftSent": "Gift sent", "giftReceive": "Gift receive", "honor": "Honor", "popular": "Popular", "familyList": "Family list", "patriarch": "Family Leader", "vicePatriarch": "Vice-Family Leader", "member": "Member", "master": "Elders", "familySettings": "Family Settings", "setVicePatriarch": "Set as Vice-Family Leader", "setPatriarch": "Set as Family Leader", "removeFromFamily": "Remove from the family", "cancelVicePatriarch": "<PERSON><PERSON> Vice-Family Leader", "removeUserOutFamily": "Remove {name} out family?", "@removeUserOutFamily": {"type": "string", "placeholders": {"name": {}}}, "conditionForFamilyApplication": "Condition for family application", "familyLevel": "Family level", "familyManagement": "Family management", "muteFamilyNotification": "Mute family notification", "exitFamily": "Quit the family", "joinFamilyErrorTitle": "You do not meet the following requirement to join this family:", "automaticallyPass": "Automatically pass", "auditPass": "<PERSON><PERSON> pass", "applyCondition": "Apply condition", "condition": "Condition", "searchUserName": "Search user name", "familyManage": "Family manage", "setAsVicePatriarch": "Set as Vice-Family Leader", "setAsMaster": "Set as Elders", "disbandFamily": "Disbanding the Family", "pleaseContactCustomerToDisbandFamily": "The family leader cannot leave the family directly. You can contact the official customer service for assistance.", "confirmation": "Confirmation", "yourContributionWillBeClearedIfExitFamily": "After you quit the family, you will clear all the related assets in the family. Are you sure you want to quit the family?", "cancelMaster": "Cancel elders", "enterFamily": "Enter family", "inRoomCannotCallMatch": "You cannot call match in chat room.", "bothOfYouShowId": "Both of you show identities! You can chat with each other.", "hungUp": "Hung up", "familyRequest": "Family request", "reject": "reject", "rejectCapital": "Reject", "searchResult": "Search result", "recommendFamilyTitle": "Some families recommend for you", "searchFamilyTip": "Search family name or ID", "wantJoinFamily": "want to join the family", "goToCheck": "Go to check!", "joinMyFamilySuccess": "join the family successfully!", "refuseFamilyApply": "refuse your family application.", "familyCreateSuccess": "Family create successfully!", "leaveTheFamily": "leave the family.", "kickedOutFamilyBy": "was kicked out of the family by", "refuseFamilyInvitation": "refuse your family invitation.", "createFamilyFailed": "create failed, please contact customer service.", "inviteYouJoinFamily": "invites you to join the family", "joinFamilySuccess": "Joined family successfully.", "invite": "Invite", "familyDisbanded": "Your family has been disbanded.", "joinTheFamily": "Join the family", "invitedToJoinFamily": "+ Invited to join family", "yourFamilyApplicationAlreadySent": "Your application to join has been submitted. Please wait patiently for the family leader's response. ", "invitedSuccessfully": "Invited successfully", "familyRepresentativeRule": "Family Leader, Vice-Family Leader, Elders", "familyRoomRule": "Every family can run a room. Family room belong to the family leader.", "familyRankRule": "Ranking is based on family member contribution", "familyRankExplain": "Ranking is based on the diamond value of gifts family member received", "enterPermission": "Enter permission", "familyMemberOnly": "Family member only", "only30SearchData": "Only show 30 search data, please enter more keywords", "noMore": "No more", "editInformation": "Edit information", "familyUpLevel": "Congrats! Your family level up to LV.{num}", "@familyUpLevel": {"type": "string", "placeholders": {"num": {}}}, "thisFamilyHasBeenDisbanded": "This family has been disbanded.", "youHaveNoAuditRights": "You have no audit rights.", "roomPk": "Room PK", "gems": "<PERSON><PERSON><PERSON>", "withdrawAvailable": "Withdraw available", "gemExchangeDiamond": "{gem} Rubies = {diamond} diamonds.", "@gemExchangeDiamond": {"type": "string", "placeholders": {"gem": {}, "diamond": {}}}, "gemBalance": "Ruby Balance", "numberOfGems": "Number of Rubies", "lackOfGems": "Lack of rubies! please finish more tasks to get rubies.", "wealthLevelUp": "Congrats! Your wealth level up to", "charmLevelUp": "Congrats! Your charm level up to", "activeLevel": "Active Level", "wealthLevel": "Wealth Level", "charmLevel": "Charm Level", "wayWealthLevelUp": "Spend diamonds in Winker", "wayCharmLevelUp": "Receive gifts in Winker", "levelPrivilege": "Level privilege", "levelPrivilegeTip1": "1、Get more attention on your level medal.", "levelPrivilegeTip2": "2、More features can be explored.", "googlePay": "Google pay", "huaweiPay": "Huawei pay", "clickToChangePayMethod": "Click here to change the pay method", "roomPkRanking": "Room PK ranking", "roomPkExplain": "Ranking is baesd on the diamond value of gifts during the room pk.", "matchPk": "Match PK", "invitePk": "Invite PK", "matchTimeout": "Match timeout", "retry": "Retry", "roomPkMatching": "Matching room pk...", "pkMatching": "Matching", "victory": "Victory", "defeat": "Defeat", "pkPoint": "PK point:", "roomPkInvitation": "Room PK invitation", "invited": "Invited", "myFollowRoomTitle": "My follow room", "roomPkInviteTips": " sent you a room PK invitation. Whether accept the challenge?", "rejectPkTips": " reject your room PK invitation.", "roomsUser": "{room}’s {user}", "@roomsUser": {"type": "string", "placeholders": {"room": {}, "user": {}}}, "roomPkNotice": "Room PK notification", "minExchangeGems": "Minimum 10 rubies", "inviteSuccess": "In<PERSON><PERSON> successfully", "todayTimesRunOut": "Today‘s times run out", "uniqueRoomId": "Unique room ID", "uniquePersonalId": "Unique personal ID", "myPersonalId": "My personal ID:", "myRoomId": "My room ID:", "myFamilyRoomId": "My family room ID:", "searchUniqueIds": "Search unique IDs", "selectTimes": "Select times", "calculatorRanking": "Calculator ranking", "pleaseCreateRoom": "You do not currently have a room, please create your own room.", "ripple": "<PERSON><PERSON><PERSON>", "badge": "Badge", "wearBadge": "Wear Badge", "achievementBadge": "Achievement badge", "honorBadge": "Honor badge", "aboutBadges": "About Badges", "aboutBadgeTitle1": "1、Wear Badges", "aboutBadgeDesc1": "All the badges can be worn.\nYou can wear up to 3 badges at the same time.\nIf less than 3 badges are currently being worn, badges will be worn automatically when you get them.\nEach achievement badge has four levels from low to high: Bronze - Sliver - Gold", "aboutBadgeTitle2": "2、Honor Badges", "aboutBadgeDesc2": "The honor badges are award to eligible users on the every sunday and is valid for about one week.", "currentlyWornBadges": "Currently-worn badges", "badgeWearInstructions": "Badge wear instructions", "badgeWearDesc": "You can wear badges you earned in Winker. The source of the Badge comes from the activities and achievements you finish in the Winker. The badges you are wearing will be displayed in the profile and room profile. You can wear a maximum of three badges.", "selectAGift": "Select a gift", "selectGift": "Select gift", "selectGiftType": "Select gift type", "onlyCalculatedDesignateGift": "Only calculated designate gift", "onlyCalculatedFamilyGift": "Only calculated {name} gift", "@onlyCalculatedFamilyGift": {"type": "string", "placeholders": {"name": {}}}, "roomPkRule": "1. The room owner and admin can start the PK in room functions.\n\n2. PK has two modes, namely vote PK and gift PK. In vote PK, each person can choose a favorite friend to support. After vote, the voting cannot be withdrawn. Gift PK You need to send any gift or designated gift to support him. If the creator create the designated gift PK, only the designated gift in the PK will be counted in the PK.\n\n3. The creator can enable the jackpot PK mode. PK joiner will not receive gift refund during the PK. PK Winner will get 30% gift value after the PK.", "myBadges": "My Badges", "awardedOn": "Awarded on {time}", "@awardedOn": {"type": "string", "placeholders": {"time": {}}}, "exchangeGemToDiamond": "Exchange ruby to diamond", "noCurrentRanking": "No current ranking", "youHaveNoJoinTheFamily": "You have no join the family", "searchTheFamilyUser": "Search family members by user nickname", "selectTheUser": "Select the user", "doneNum": "Done({num})", "@doneNum": {"type": "string", "placeholders": {"num": {}}}, "numberOfMic": "Number of Mic", "setMicWhenRoomLevelReaches": "You can set this when your room level reaches Lv.{num}", "@setMicWhenRoomLevelReaches": {"type": "string", "placeholders": {"num": {}}}, "announceDefault": "Welcome to the family!", "someoneAtYouWithBrackets": "[@you]", "someoneAtYou": "Someone @you", "userKickedOutOfFamilyBy": "{user} was kicked out of the family by {manager}.", "@userKickedOutOfFamilyBy": {"type": "string", "placeholders": {"user": {}, "manager": {}}}, "iAmNewToFamily": "Hi, I am new to the family.", "userSetAsVicePatriarch": "{user} set as vice-family leader.", "@userSetAsVicePatriarch": {"type": "string", "placeholders": {"user": {}}}, "userSetAsMaster": "{user} set as Elders.", "@userSetAsMaster": {"type": "string", "placeholders": {"user": {}}}, "userJoinTheFamily": "{user} join the family.", "@userJoinTheFamily": {"type": "string", "placeholders": {"user": {}}}, "userExitTheFamily": "{user} exit the family.", "@userExitTheFamily": {"type": "string", "placeholders": {"user": {}}}, "welcomeToTheFamily": "{user}, welcome to the family!", "@welcomeToTheFamily": {"type": "string", "placeholders": {"user": {}}}, "familyGroup": "Family group", "mics_8_2": "8+2 Mics", "mics_10": "10 Mics", "mics_9": "9 Mics", "mics_5": "5 Mics", "mics_2": "2 Mics", "changeTheNumberMicsWillRemoveUser": "Change the number of mics will remove all user on the mic, are you sure change the number of mics?", "recommendUser": "Recommend user", "followBestSurprise": "Follow them for the best surprise!", "youMayInterest": "You may interest", "cancelAll": "Cancel all", "changeNumberOfMic": "Change the number of mic to {count} seats.", "@changeNumberOfMic": {"type": "string", "placeholders": {"count": {}}}, "cleanChat": "Clean chat", "userCleanedChat": " cleaned the chat", "cleanChatNoticeContent": "Are you sure clean the chat?", "followerWithNum": "Follower: {num}", "@followerWithNum": {"type": "string", "placeholders": {"num": {}}}, "roomTask": "Room task", "roomTaskRefreshTime": "Refresh at 23:59(UTC+3)", "roomTaskGuideText": "Click here to get room task!", "roomTaskMsgText": "Complete the room task to get more rewards !", "knight": "<PERSON>", "baron": "<PERSON>", "count": "Minister", "king": "King", "chief": "Emperor", "winkerPremium": "Winker Premium", "enterMessage": "Enter message", "exclusivePrivileges": "Exclusive Privileges", "getDiamondDay": "Get {num} {type} every day", "@getDiamondDay": {"type": "string", "placeholders": {"num": {}, "type": {}}}, "diamondsForRenewal": "{num} diamonds for renewal", "@diamondsForRenewal": {"type": "string", "placeholders": {"num": {}}}, "activatePremium": "Activate Premium", "youNotWinkerPremium": "You are not a Winker Premium", "activatePremiumConfirm": "Confirm to pay {diamond} diamonds to activate the {name} premium?", "@activatePremiumConfirm": {"type": "string", "placeholders": {"diamond": {}, "name": {}}}, "expiredDate": "Expiry date: {date}", "@expiredDate": {"type": "string", "placeholders": {"date": {}}}, "uploadTheme": "Upload theme", "dayLowCase": "day", "activate": "activate", "inWord": "in", "goldsLowCase": "golds", "gemsLowCase": "rubies", "diamondsLowCase": "diamonds", "days": "days", "activatedValidUntil": "You have successfully enabled {name} premium, valid to {time}", "@activatedValidUntil": {"type": "string", "placeholders": {"name": {}, "time": {}}}, "specialDiscount": "Special discount", "premiumSettings": "Premium settings", "preventFollowingIntoRoom": "Prevent following into room", "youCanNotFollowThisUserIntoRoom": "You can not follow this user into room", "yourLevelIsIncreasingAtPremiumSpeed": "Your level is increasing at Premium speed.", "changePremiumSetting": "1.Change your Settings above", "otherCannotFollowYou": "2.Other users cannot follow you to your room via profile", "dailyRewards": "Daily rewards", "getDiamondsEveryDay": "Get diamonds every day", "premiumBadge": "Premium badge", "showOnProfile": "Show on profile", "showOnRoomProfile": "Show on room profile", "showOnMic": "Show on mic position", "screenMessage": "Screen message", "onlineUserList": "Online user list", "exclusivePremiumAvatar": "Exclusive Premium avatar frame", "premiumAvatarFrameDesc": "Get more attention with your special Premium frame", "exclusivePremiumChat": "Exclusive Premium chat frame", "exclusivePremiumChatDesc": "Your message with gorgeous frame show your friends", "sendImageInRoom": "Send image in room", "sendImageInRoomDesc": "You can send image in room", "discountDay": "Store discount day", "discountDayDesc": "Every Sunday you can purchase store items with a special discount!", "serviceExpert": "Customer service expert", "serviceExpertDesc": "Enjoy the best service from our customer service expert.", "enterEffectTitle": "Enter effect title", "enterEffectTitleDesc": "Attract everyone's attention with a gorgeous enter message", "unlimitedChat": "Unlimited chat", "unlimitedChatDesc": "You can chat whatever you want without restriction.", "levelUpSpeed": "Level up speed", "levelUpSpeedDesc": "<PERSON>ain more level experience.", "preventFollowRoom": "Prevent follow into room", "preventFollowRoomDesc": "You can keep others from following you into room.", "activationBroadcast": "Activation broadcast", "activationBroadcastDesc": "Attract everyone's attention with your Premium broadcast", "priorityReport": "Priority reporting", "priorityReportDesc": "Customer service will process your report information faster", "RebateGiftSend": "Rebate on gift sending", "RebateGiftSendDesc": "Send gifts to others and receive diamond rebate.", "checkInDetail": "Check in details", "coloredNameRoomScreen": "Attract everyone's attention with a colored name show on room screen.", "msgDescLuckyBag": "[Lucky bag]", "sendALuckyBag": "Send a lucky bag", "clickToGetTt": "click to get it! >", "alreadyGotLuckyBag": "Already got this lucky bag.", "luckyBagIsEmpty": "Lucky bag is empty.", "shareEventTagContent": "Come join the special event on Winker now!", "followYou": "Follow you", "followBack": "Follow back", "exchangeKeysToUnlockBox": "Exchange keys to unlock the bonus box", "magicBox": "Magic box", "openBox": "Open box", "sendKeys": "Send keys", "keysGiftedByOthers": "Keys gifted by others: {num}", "@keysGiftedByOthers": {"type": "string", "placeholders": {"num": {}}}, "yourKeyMustBeSentFromAnother": "Your open key must be sent from another user.", "requestKey": "Request key", "requestsHasReachedTheUpperLimit": "The number of requests for this user has reached the upper limit.", "requestSuccessful": "Request successful, please wait for response", "youHaveSentRequest": "You have sent a request to this user", "requestAMagicBoxKey": "Request a magic box key", "pleaseGiveMeKey": "Please give me the key to help me open the treasure chest!", "sentYouAMagicBoxKey": "sent you a magic box key", "youAlreadySentAMagicKey": "You already sent a magic box key to", "iSentYouMagicBoxKey": "I sent you magic box key", "keysCanBeGifted": "Keys can be gifted: {num}", "@keysCanBeGifted": {"type": "string", "placeholders": {"num": {}}}, "sendAKey": "Send a key", "youLackOfKeys": "You lack of keys! Please request keys from other family members.", "sayHi": "Say hi", "freeGift": "Free gift", "treasureTitle": "Task for the treasure", "finishTreasureTask": "Completing the task gives you the opportunity to receive a larger reward.", "luckyBonusNum": "Lucky bonus:", "getItTomorrow": "Get it tomorrow", "treasureTips": "Come and share the rewards from the treasure now!", "treasureTotal": "Treasure total today:", "expired": "expired", "keysAndDiamondsOpenThisBox": "{key} keys and {diamond} diamonds open this box", "@keysAndDiamondsOpenThisBox": {"type": "string", "placeholders": {"key": {}, "diamond": {}}}, "keysOpenThisBox": "{key} keys open this box", "@keysOpenThisBox": {"type": "string", "placeholders": {"key": {}}}, "opened": "Opened", "magicBoxTask": "Magic box task", "enterNumLuckyBag": "Please enter the number of Lucky bag", "luckyBagCharge": "5% commission is charged for each diamond lucky bag", "you": "You", "haveReceivedLuckyBag": "have received the Lucky bag", "youRLuckyBagIsEmpty": ", Your lucky bag is empty!", "luckyBagFrom": "Lucky bag from:", "luckyBagHasExpired": "The lucky bag has expired", "youGot": "You got", "getTheTreasureRewards": "Get the rewards from the treasure now!", "claimAKey": "Request a key", "pleaseGiveMeAKey": "Please give me a key to help me open the treasure chest!", "youDoNotBelongTheFamily": "You do not belong to that family", "calling": "Calling", "sayHello": "[Say hello]", "familyData": "Family data", "instructionChatLuckyBag": "1、Steps to send a lucky bag: choose the quantity of the gifts or diamonds to be put into a luck bag then choose the number of recepients, finally tap the send buttion.\n2、Each recipients will get a random quantity of gifts or diamonds from the lucky bag. Diamonds will be sent to your wallet and gifts will sent to your backpack. Please check the details in chat notification.", "receiverNumRubies": "Receiver got {num} rubies", "@receiverNumRubies": {"type": "string", "placeholders": {"num": {}}}, "senderWealthAddNumExp": "Sender wealth exp +{num}", "@senderWealthAddNumExp": {"type": "string", "placeholders": {"num": {}}}, "receiverCharmAddNumExp": "Receiver charm exp +{num}", "@receiverCharmAddNumExp": {"type": "string", "placeholders": {"num": {}}}, "hotFamily": "Recommended Family", "searchResults": "Search Results", "talent": "Talent", "clickTheBoom": "{name} click the [BOOM]!!", "@clickTheBoom": {"type": "string", "placeholders": {"name": {}}}, "timeToComment": "The performance is end. Time to comment the performance", "modeWillBeEnd": "This mode will be ended. You can switch to other mode after ending.", "skipPerformanceConfirm": "The performance will be skipped. The point of this round will be counted now. Still want to skip?", "performanceEndAdvance": "{name}’s performance is end in advance. It's time for the judges to speak", "@performanceEndAdvance": {"type": "string", "placeholders": {"name": {}}}, "grabMicMode": "Grab mic mode", "specifyMode": "Specify mode", "performanceTime": "Performance time（min）", "specifyPerformer": "Specify performer:", "inviteYouChat": "Invite you to chat", "inviteYouPlayGames": "Invite you play games", "inviteYouForDate": "Invite you for a date", "inviteYouWatchVideo": "Invite you watch a video", "inviteYouForTalent": "Invite you for talent show", "joinTheRoom": "Join the room", "online": "Online", "nearby": "Nearby", "filter": "Filter", "selectYourFavorite": "Select your favorite", "selectYourCity": "Select your city", "setTheCity": "Set the city", "scoresPerformersHere": "Scores for all performers are displayed here.", "clearPoint": "Clear point", "clearPointConfirm": "All performers' points will be cleared. still clear?", "voiceAdjust": "Voice adjust", "earphoneFeedback": "Earphone feedback", "onlyWiredHeadsets": "Only wired headsets supported", "voice": "Voice", "reverb": "Reverb：", "studio": "Studio", "ktv": "KTV", "stereo": "Stereo", "cd": "CD", "echo": "Echo", "distant": "Distant", "partyReverb": "Party", "church": "Church", "piano": "Piano", "hall": "Hall", "naturalReverb": "Natural", "noRank": "No rank", "guest": "GUEST", "leaveTheList": "Leave the list", "sureLeaveTheList": "Leave the list will not able to perform. Still leave?", "performerList": "Performer list", "applyList": "Apply list", "kicked": "Kicked", "onlyByInvitation": "Only by invitation", "inviteToHost": "Invite to host", "inviteToGuest": "Invite to guest", "inviteToList": "Invite to list", "kickOutList": "Kick out list", "youKickedOut": "You have been kicked out the list", "inviteJoinPerformer": "{username} invites you to join into the performer list", "@inviteJoinPerformer": {"type": "string", "placeholders": {"username": {}}}, "thePerformerListFull": "The performer list is full", "applyTooFrequently": "Apply to take mic too frequently. Please try again later", "successfullyApplied": "successfully applied", "youAreGuestNow": "Denied. you are on the guest mic now", "pleaseSetUpYourCity": "Please set up your city for easier location by your local friends.", "matchingASuitableRoom": "Matching a suitable room for you", "noMoreSuitableRoom": "No more suitable room, please subscribe to join the activity below!", "noMoreFamily": "No more families", "youInvitedHost": "You are invited to take the host mic", "youInvitedGuest": "You are invited to take the guest mic", "talentBegins": "Talent room begins!", "performing": "performing", "grabbing": "grabbing", "guestSpeak": "Guests speak", "readyGrab": "Ready grabbing", "grab": "Grab!", "readyToPerform": "Ready to perform!", "grabbedTheMic": "Grabbed the mic!", "insufficientNumberGrabMic": "Insufficient number of people to grab mic", "NoAvailablePerformer": "no available performer, please add first", "skipPerformConfirm": "The performance will be skipped. The point of this round will be counted now. Still want to skip?", "grabbingUp": "Grabbing", "endMode": "End mode", "startAgain": "Start again", "yourCity": "Your city", "thePerformanceIsEnd": "the performance is end", "leaveRoomStopPerforming": "Leave room will stop performing. still leave?", "leaveMicStopPerforming": "Leave mic will stop performing. Still leave?", "leaveNowWillCloseRoom": "leave now will close the room.", "clickBoom": "click the BOOM!!", "talentRule": "1. Welcome to the talent room! In this room, you can be a performer and give a fantastic show. Or you can be a active audience and BOOM for the performers.\n2. This room includes two mode: grab mic mode or specify mode\n  a. Grab mic mode: User will be invited by the host (or room owner/admin. The following only takes the host as an example) or can apply to join to the performer list and become the performer candidate. The host will start the mode and every candidate can grab the mic. The one who grab the mic successfully will be on stage and perform. After performing, the guest can comment and after that, the host will start a new round.\n  b. Specify mode: The candidates in the performer list will be specified by the host and get on to the stage to perform.\n3. What can the audience do?\n  a. Send gift to support the performer and increase the points. Ordinary gift worth 1 diamond = 1 point. Special gift (3 gifts displayed when performing) worth 1 diamond = 2 points\n  b. Click [BOOM] to support the performer. It's free! 1 audience [BOOM] = 10 points. 1 guest [BOOM] = 100 points.\n4. How can i be the guest?\n  a. The guest can be only invited by the host (or room owner/admin)\n  b. The guest can also send gift, click [BOOM] and comment to the performer.\n5. How can i be the host?\n  a. The host can be only invited by the room owner or admin.\n6. The points of all performers will be accumulated and displayed on the cumulative scoreboard according to the ranking.\n7. If you leave mic, leave room or being kicked out while performing, the points you have already got will not be counted.", "faq": "FAQ", "performingSwitchMode": "Performing now. Switch room mode denied", "switchTalentMode": "Switch now will close the talent room. The perform will end. Still want to switch?", "inviteToTalent": "Invite to watch talent show", "noSupporterOnThisRound": "No supporter on this round.", "youCannotChangeSeat": "You cannot change seat", "painter": "<PERSON>", "drawGuess": "Draw & Guess", "loadingNum": "Loading: {num}%", "@loadingNum": {"type": "string", "placeholders": {"num": {}}}, "waitingPlayer": "Waiting player...", "automaticStartNum": "Automatic start: {num}s", "@automaticStartNum": {"type": "string", "placeholders": {"num": {}}}, "oppositeVoice": "Opposite Voice", "turnOnSwitchCanHearOppositeVoice": "Turn on the switch can hear the opposite room voice(all mic)", "allUserInYourRoomCanHearVoice": "All user in your room can hear the opposite voice now! Try it!", "grabPoints": "grab {points} points", "@grabPoints": {"type": "string", "placeholders": {"points": {}}}, "yourRoomGrabPoints": "your room grab {points} points", "@yourRoomGrabPoints": {"type": "string", "placeholders": {"points": {}}}, "theOppositeRoomReceived": "The opposite room received", "yourRoomIsGrabbedPoints": "your room is grabbed {points} points", "@yourRoomIsGrabbedPoints": {"type": "string", "placeholders": {"points": {}}}, "allParticipantsPoints": "all participants {points} points", "@allParticipantsPoints": {"type": "string", "placeholders": {"points": {}}}, "pointsChange": "{points} points", "@pointsChange": {"type": "string", "placeholders": {"points": {}}}, "yourRoomPointsChange": "your room {points} points", "@yourRoomPointsChange": {"type": "string", "placeholders": {"points": {}}}, "theOppositeRoomPointsChange": "the opposite room {points} points", "@theOppositeRoomPointsChange": {"type": "string", "placeholders": {"points": {}}}, "defenseBuff": "defense buff {count}", "@defenseBuff": {"type": "string", "placeholders": {"count": {}}}, "allParticipantsDefenseBuff": "all participants defense buff {count}", "@allParticipantsDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "youRoomDefenseBuff": "your room defense buff {count}", "@youRoomDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "theOppositeRoomDefenseBuff": "the opposite room defense buff {count}", "@theOppositeRoomDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "choosingWords": "Choosing words", "chooseYourWords": "Choose your words", "resultWithWords": "Result: {words}", "@resultWithWords": {"type": "string", "placeholders": {"words": {}}}, "closeIn": "Close in", "hintsWithContent": "Hints: {words}", "@hintsWithContent": {"type": "string", "placeholders": {"words": {}}}, "estimatedRefund": "Estimated refund: ", "SelectActivityProgress": "Select activity progress", "activityProgress": "Activity progress", "result": "Result", "scoreAddNum": "got it right and score ", "pleaseInputAgain": "Network error, please input again", "inputAnswerHere": "Input answer here...", "record": "Record", "chooseWordGuideTip": "Choose the words you want to draw, you can also change the words.", "paintGuideTip": "Use finger draw your words!", "guessGuideTip": "Input your guess!", "queryOneMonth": "Only supports querying records within 1 month", "roomPkRecord": "Room PK Record", "drawGuessFaq": "1. At least 2 players are required to start the game.\n2. Each player takes turns as a painter, drawing the selected words, while the other players are guessers.\n3. The painter scores points based on the number of guessers who guess correctly.\n4. The guessers receive different scores based on the order of the correct answers.\n5. Finally, the players are ranked according to their final scores.", "noRoomPkRecord": "No room pk record yet", "invitePlayDrawGuess": "Play Draw&Guess", "shareScreenShotTo": "Share screen shot to", "undercover": "Undercover", "youCantTakeMicOnOppositeRoom": "You can't take mic on the opposite room", "undercoverTip": "Please determine whether you are an undercover or a civilian, and keep your identity hidden or vote to eliminate the undercover.", "describeYourWords": "Describe words", "voteForUndercover": "Vote for the user you believe most resembles an undercover.", "noVotesForUndercover": "No votes were cast in this round. Automatically proceed to the next round.", "turntable": "Turntable", "reJoinFamilyTip": "After leaving the family, you can only rejoin after 24 hours.", "undercoverNotAllowedToSpeak": "During the game, users who are not currently in the description round are not allowed to speak.", "myWord": "My words: {word}", "@myWord": {"type": "string", "placeholders": {"word": {}}}, "letTheGameStart": "Let the game start", "whoIsTheUndercover": "Who is the undercover?", "tips": "Tips", "roundVoting": "Round {round} Voting", "@roundVoting": {"type": "string", "placeholders": {"round": {}}}, "pass": "Pass", "civilian": "Civilian", "noOut": "No.{position} OUT!", "@noOut": {"type": "string", "placeholders": {"position": {}}}, "coupon": "Coupon", "premium": "Premium", "contentOff": "{content} off", "@contentOff": {"type": "string", "placeholders": {"content": {}}}, "noticeForUse": "Notice for use", "noMoreCoupons": "No more coupons", "used": "Used", "expiredCapital": "Expired", "noCouponsAvailable": "No coupons available", "notAllowedToSpeak": "Not allowed to speak during not your game round.", "notAllowedCloseTheMic": "Not allowed close the mic during the description round.", "undercoverFaq": "1.  At the start of the game, each person will be given a word. Please note that one of the words will be different from the others. Find out who the undercover is. Undercover, please hide your identity and survive until the end. \n2.  Each player has 30 seconds to describe their word in each round, taking turns. \n3.  After the descriptions are completed, all players have 20 seconds to vote. \n4.  The player with the highest number of votes will be eliminated. In case of a tie, a sudden death round will be conducted for those players to describe their words again. If no votes are cast, the game proceeds to the next round. ", "records": "Records", "participant": "Participant", "youCantChangeInThisRound": "You can’t change participant in this round", "optionTitleNum": "Option ({num}/8)", "@optionTitleNum": {"type": "string", "placeholders": {"num": {}}}, "turnOn": "Turn On", "contentCannotEmpty": "Content cannot be empty", "sureTurnoff": "Confirm to turnoff the turntable?", "containSensitive": "Contains sensitive words", "voting": "Voting", "extraContent": "Extra {content}", "@extraContent": {"type": "string", "placeholders": {"content": {}}}, "decreaseContent": "-{content}", "@decreaseContent": {"type": "string", "placeholders": {"content": {}}}, "increaseContent": "+{content}", "@increaseContent": {"type": "string", "placeholders": {"content": {}}}, "turntableResult": "Turntable result: ", "turntableIsClosed": "The turntable is closed.", "update": "Update", "cannotStartTurntable": "You can't not start turntable when lucky wheel turn on", "cannotStartLuckyWheel": "You can't not start lucky wheel when turntable turn on", "notAvailable": "Not available", "inviteUndercover": "Invite to play Undercover", "userTurnedResult": "{username} turned the turntable. The result is: {result}", "@userTurnedResult": {"type": "string", "placeholders": {"username": {}, "result": {}}}, "civilianVictory": "Civilian victory", "civilianDefeat": "Civilian defeat", "undercoverVictory": "Undercover victory", "undercoverDefeat": "Undercover defeat", "customTurntableFaq": "1. Only the room owner and admin can edit, update content, turn on and close the turntable.\n\n2. The room owner and admin can spin the turntable. They can designate the members on the mic to participate the turntable as well.\n\n3. Once the turntable is turned on, the participants who can spin the turnable this round will not change until it is closed\n\n4. While the turntable is spinning, no one else can spin it until it stops.", "turntableStartTip": "You have been selected and you can play the turntable", "noMoreUsersInThisCity": "No more users in this city.", "pleaseTakeMicAndSpeak": "Please take the mic and speak.", "tiePk": "<PERSON>ie P<PERSON>", "noSelectedThisRound": "No participant selected this round", "homeStatePopTitle": "Choose your current status", "homeStatePopSubtitle": "Your status will expire in 24 hours", "statePublishSuccess": "Status set successfully, will expire after 24h", "now": "Just now", "beforeMiuntesAgo": "{count} minutes ago", "@beforeMiuntesAgo": {"type": "string", "placeholders": {"count": {}}}, "beforeHoursAgo": "{num,plural, =0{{num} hour}=1{{num} hour}other{{num} hours}} ago", "@beforeHoursAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeDaysAgo": "{num,plural, =0{{num} day}=1{{num} day}other{{num} days}} ago", "@beforeDaysAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeWeeksAgo": "{num,plural, =0{{num} week}=1{{num} week}other{{num} weeks}} ago", "@beforeWeeksAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeMonthsAgo": "{num,plural, =0{{num} month}=1{{num} month}other{{num} months}} ago", "@beforeMonthsAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeYearsAgo": "{num,plural, =0{{num} year}=1{{num} year}other{{num} years}} ago", "@beforeYearsAgo": {"type": "string", "placeholders": {"num": {}}}, "active": "Active", "homeToBottom": "It's already at the bottom", "regWelcome": "Hey there, I'm <PERSON><PERSON>!\nI'll help you settle in quickly and make new friends around here.", "regTestTip": "Winker is currently in internal testing，and to maintain a positive community atmosphere, we have set up some small barriers during registration.\nYou can access Winker in two ways：\n👇👇🏻👇🏾", "regVerificationPassed": "Congratulations🎉🎉🎉, verification passed!", "regInvitationCode": "Invite Code", "regAnswer": "Answer Test", "regRight": "Right", "regAnsNum": "You can pass by answering {count} questions correctly.", "@regAnsNum": {"type": "string", "placeholders": {"count": {}}}, "regAnsRemaining": "😃Correct answer, just answer {count} more questions correctly and you will pass.", "@regAnsRemaining": {"type": "string", "placeholders": {"count": {}}}, "regNotPass": "🙁Sorry, you did not pass this test.", "regPassed": "👏Congratulations on passing the test, and you will soon be able to meet <PERSON><PERSON>'s friends.", "regCompleteInfo": "Next, we need you to refine your personal information.", "regNickname": "How would you like the friends in winker to address you?", "regNickInputHint": "Enter your nickname", "regNiceNickname": "{name}，I like this name😊.", "@regNiceNickname": {"type": "string", "placeholders": {"name": {}}}, "regGender": "{name}，please choose your gender", "@regGender": {"type": "string", "placeholders": {"name": {}}}, "regGenderMale": "👱Male", "regGenderFemale": "👩Female", "regGenderSexTip": "Once gender is selected, it cannot be changed. Please confirm that your selection is {sex} ", "@regGenderSexTip": {"type": "string", "placeholders": {"sex": {}}}, "regBirthday": "{gender}{name}, please fill in your birthday so that we can recommend more suitable content and friends for you.", "@regBirthday": {"description": "A gendered message", "type": "String", "placeholders": {"gender": {}, "name": {}}}, "regMr": "Mr.", "regMs": "Ms.", "regBirthSame": "🤖Coincidentally, there are {count} Winker friends who share your birthday with you, and perhaps you will meet quietly at some point.", "@regBirthSame": {"type": "string", "placeholders": {"count": {}}}, "regAvatar": "Please select your Avatar.", "regSuccess": "🎉Congratulations on obtaining the Winker Pass「{uid} 」. Now you can enter the Winker community and hope to make new friends here 👇🏻", "@regSuccess": {"type": "string", "placeholders": {"uid": {}}}, "regCardBirthdayTip": "First visit to Winker", "regAccessWinker": "Access Winker", "regMyInvitationCode": "My Invitation Code x", "from": "From", "to": "To", "privateFlights": "Private flights", "invitationCode": "Invitation code", "copyInvitationCode": "Copy Invitation Code", "ScreenshotShare": "Screenshot Share", "savedGallery": "Saved to Gallery!", "selectTheme": "Select Theme", "location": "Location", "brand": "Brand", "postWhichTheme": "Post to which theme", "customizedThemes": "Customized Themes", "customizedThemeTitlePlaceholder": "Introduce your new theme", "customizedThemeSubtitlePlaceholder": "Introduce your new theme description", "customizedThemeToastTitle": "The title is not empty", "publishTitleHintText": "Enter the title", "myThemeSubtitle": "{count} Posts", "@myThemeSubtitle": {"type": "string", "placeholders": {"count": {}}}, "bug": "Bug", "chatIssue": "Chat Issue", "suggestions": "Suggestions", "userIntroEmptyTip": "Write something to introduce yourself~", "userAboutMe": "About Me", "userAboutOther": "About {other}", "@userAboutOther": {"type": "string", "placeholders": {"other": {}}}, "aboutOtherInformation": "About {other} information", "@aboutOtherInformation": {"type": "string", "placeholders": {"other": {}}}, "his": "his", "him": "him", "her": "her", "heFollowYou": "He’s following you", "sheFollowYou": "She’s following you", "Unknown": "Unknown", "userEmptyPostTip": "Posting to get to know you better", "postTipGourmet": "Checked in a gourmet restaurant recently", "postTipPet": "Checked in a gourmet restaurant recently", "postTipJournal": "Checked in a gourmet restaurant recently", "keepPosting": "Keep posting", "customizedThemeNameExist": "The theme is exist", "failed": "Failed", "momentDetailViewProfile": "View Profile", "editTheme": "Edit theme", "reportTheme": "Report Theme", "he": "he", "she": "she", "moveTheme": "Move Theme", "moveToAnotherTheme": "Move to another theme", "copyInviteCode": "Invite Code: {code}. I am experiencing a new social app called 'Winker' and inviting you to join us. My invitation code can only be used once and the number of times is limited. Please join as soon as possible~To download, please click👉🏻 https://winkerchat.com/", "@copyInviteCode": {"type": "string", "placeholders": {"code": {}}}, "pleaseKindlyComment": "Please kindly comment", "newTheme": "New Theme", "myTheme": "My Theme", "winkList": "New Friends", "statusExpired": "The Status expired, please reset your status", "signInCoins": "Sign in to receive golds", "signInReceived": "Received", "signInRewardNumCoins": "Congratulate get {num} golds", "@signInRewardNumCoins": {"type": "string", "placeholders": {"num": {}}}, "sendWink": "Send a wink", "whoWinkAtYou": "Who wink at you", "whoHadWink": "Who i had wink", "sayHiBottomTip": "Sending winker will attract {sex} attention, and mutual winker can start the conversation.", "@sayHiBottomTip": {"type": "string", "placeholders": {"sex": {}}}, "sayFreeHasSent": "Has been sent", "sayFreeWink": "Wink at {sex}", "@sayFreeWink": {"type": "string", "placeholders": {"sex": {}}}, "sayFreeWinkRepeat": "You're already winking at {sex}", "@sayFreeWinkRepeat": {"type": "string", "placeholders": {"sex": {}}}, "sayHiGiftSendChat": "Send to chat", "thinkWink": "Thank you for your wink!", "countDetail": "Count detail", "taskToAchieve": "To Achieve", "gotten": "<PERSON><PERSON>", "youWinkedYou": "You winked {sex}", "@youWinkedYou": {"type": "string", "placeholders": {"sex": {}}}, "heWinkedYou": "{sex} winked you", "@heWinkedYou": {"type": "string", "placeholders": {"sex": {}}}, "chatHotTip": "She received a lot of this gift. Let’s try another gift", "rewardedToast": "{num}golds receive successful", "@rewardedToast": {"type": "string", "placeholders": {"num": {}}}, "wink": "<PERSON><PERSON>", "msgDescWink": "[Win<PERSON>]", "homePage": "Homepage", "myWallet": "My Wallet", "winkCountOutToast": "The wink count has run out.", "locationServiceTipTitle": "Location service is not enabled", "locationServiceTipDes": "To match with nearby users, please enable location services in your phone settings.", "locationServiceTurnOn": "Turn on location", "homeCountdownTip": "Updated after {time}", "@homeCountdownTip": {"type": "string", "placeholders": {"time": {}}}, "discoverMore": "Discover more", "fateBell": "Fate Bell", "fateBellSetting": "<PERSON> Bell Setting", "openFateBell": "Open Fate Bell", "safeDistanceProtection": "Safe distance protection", "safeDistanceProtectionDesc": "Doesn't match users within 500m of me", "fateNewMatch": "The fate bell has rung, there’s a new match", "punch": "Punch", "punchQuick": "Q&A invitation", "punchQuestionTitle": "Congratulations！you won！you can ask {her} a question", "@punchQuestionTitle": {"type": "string", "placeholders": {"her": {}}}, "punchGameFrequent": "Invitations are too frequent, please wait a moment.", "punchGame": "Punch Game", "punchReq": "Sent out an invitation to 'Guess Fist Q&A' and waiting for the other party to join...", "punchReceive": "The other party accepted the 'Guess First Q&A' invitation.", "punchLost": "😅You lost, waiting for the other party to choose a question...", "punchJoinTitle": "The other party wants to play [Guess Fist Q&A]", "reportCenter": "Report Center", "dontReminder": "Don't remind", "showInWorld": "Show in Winker world!", "createRoom": "Create room", "partyListEmptyTip": "Create a room and invite friends to chat", "roomModeChat": "Cha<PERSON>", "roomModeCp": "Cp", "roomModeTruthDare": "Truth or Dare", "status": "Status", "allMessages": "All Messages", "friendsMessages": "Friend's Messages", "roomSettings": "Room settings", "background": "Background", "minimize": "Minimize", "roomNameInputHint": "Please give your room a cool name", "roomAnnounceInputHint": "All members of the room will see the announcement", "setPassword": "Set password", "atTa": "@", "roomApplyMic": "Apply to mic", "roomFreeMic": "Free to mic", "setupAdmin": "Setup admin", "removeAdmin": "Remove admin", "roomExitTitle": "Leave the room", "roomExitDes": "Are you sure to exit the room?", "roomCloseAndExit": "Close and exit", "roomMemberTabSpeakRequest": "Speak request", "roomMemberTabOnline": "Online", "roomMemberTabAdmin": "Admin", "roomMemberTabBlocked": "Blocked", "remove": "Remove", "onMic": "On Mic", "roomWelcome": "Welcome!!", "followedYou": "followed you", "hasFollowedYou": "has followed you", "stay1minInvite": "It’s been a minute since he entered the room, invite him to chat on the mic", "stay2minFollow": "We have been chatting for a while. Follow me and you will be notified immediately if you create a room in the future.", "invitationShort": "Invitation", "downMic": "Down mic", "applyTakeMicSuccess": "apply to take the mic successfully.", "roomMsgRoomName": "The room name was changed to:{roomName}", "@roomMsgRoomName": {"type": "string", "placeholders": {"roomName": {}}}, "editProfile": "Edit Profile", "desYour": "Describle yourself", "alreadyAdded": "Already added", "interestDesTip": "Choose a label to quickly showcase yourself.", "prev": "Prev", "voiceVerifyDesc": "Only user who have passed voice verification can listen to each other’s voice.", "goForVerification": "Go for verification", "voiceVerified": "Voice verified", "sayHiLimitTitle": "Daily greeting limit reached ({num}/Day). \nUnlock more conversations by sending a gift", "@sayHiLimitTitle": {"type": "string", "placeholders": {"num": {}}}, "sayHiSubtitle": "This limit encourages thoughtful choices. New opportunities available after a while", "editProfileLivingTitle": "Select your main living area", "editProfileLivingDes": "The system will recommend nearby users for you.", "mainCountry": "Indonesia", "overseasCountries": "Overseas Countries/Regions", "maxSelectOption": "You can add up to a maximum of {num} tags", "@maxSelectOption": {"type": "string", "placeholders": {"num": {}}}, "minSelectOption": "Please add a minimum of {num} tags", "@minSelectOption": {"type": "string", "placeholders": {"num": {}}}, "talkSomething": "Talk about something…", "tryWriting": "Try writing like this", "familyTip": "Born in Java, currently working and living only child and a well.off family. My dailv life circle is narrow and hope to meet my crush here.", "academic": "Academic", "academicTip": "I studied at an Indonesian university with excellent academic performance.", "work": "Work", "workTip": "I started my own business and opened a Honey Snow ice City milk tea shop.", "icebreakerNewbie": "{he} is newbie and doesn't have many friends yet. Hurry up and say hello to {him}.", "@icebreakerNewbie": {"type": "string", "placeholders": {"he": {}, "him": {}}}, "viewAll": "View all", "voiceVerifyChat1": "Hi", "voiceVerifyChat2": "How are you?", "voiceVerifyChat3": "Your voice sounds lovely!", "userUpdateStatus": "Updated status: {text}", "@userUpdateStatus": {"type": "string", "placeholders": {"text": {}}}, "sayHiTitle": "{She} is so popular, give {her} a gift to express your sincerity.", "@sayHiTitle": {"type": "string", "placeholders": {"She": {}, "her": {}}}, "distanceTag": "Distance {num}km", "@distanceTag": {"type": "string", "placeholders": {"num": {}}}, "stayTimeTag": "Stay in room for {num}", "@stayTimeTag": {"type": "string", "placeholders": {"num": {}}}, "hour": "h", "minute": "min", "blockedTime": "Block time {time}", "@blockedTime": {"type": "string", "placeholders": {"time": {}}}, "chatRoomInvite": "Invite you to the party", "micApplyNotice": "Apply notice", "toLater": "To later", "invitationToMic": "Invitation to Mic", "inviteToMicMessage": "Invite you join discuss", "shareTheRoom": "Shared the room.", "requestSend": "Request send", "listener": "Listener", "lessThanOneMinute": "< 1 minute", "otherViewers": "{num} other viewers", "@otherViewers": {"type": "string", "placeholders": {"num": {}}}, "punish": "<PERSON><PERSON><PERSON>", "punishSelect": "Punish Select", "punishLevel": "Punish Level", "punishSilentRemaining": "You have been banned from speaking on the public screen by the administrator", "notRecommended": "Not Recommended", "doubleConfirmation": "Double confirmation", "doubleConfirmationDes": "Are you sure you want to do this?", "labels": "Labels", "reset": "Reset", "defaultRoomName": "{host}'s room", "@defaultRoomName": {"type": "string", "placeholders": {"host": {}}}, "defaultRoomAnnounce": "Welcome to my room, where you can speak freely！", "shareToOthers": "Share to others", "link": "Link", "roomType": "Room type", "truthStart": "START", "truthOngoing": "Ongoing", "truthSelected": "Selected", "truthGameIntroTitle1": "Get ready to go on the mic.", "truthGameIntroSubtitle1": "Users are considered to have joined the game once they are on the mic.", "truthGameIntroTitle2": "The wheel turns.", "truthGameIntroSubtitle2": "When the wheel is spinning, all players involved in the game have a chance of being selected to be chosen, completely randomly.", "truthGameIntroTitle3": "Question archive", "truthGameIntroSubtitle3": "The player who is drawn, completes either Truth or Dare, given randomly by the system. The drawn theme needs to be completed by the chosen player.", "truthStartLimit": "At least two or more people join the game", "finish": "Finish", "roomReEnterErrTip": "[Error Code：{code}]Please try to exit the room and re-enter.", "@roomReEnterErrTip": {"type": "string", "placeholders": {"code": {}}}, "roomSettingMusic": "Music", "roomSettingClearScreen": "Clear screen", "backgroundMusic": "Background music", "profileChattingInRoom": "{she} is chatting in the room~", "@profileChattingInRoom": {"type": "string", "placeholders": {"she": {}}}, "peopleChattingNow": "People chatting now", "downloadingToLater": "Downloading fail, please try again later", "iHere": "Hi, i'm here!", "sendTo": "Send to", "goldTask": "Gold task", "diamond": "Diamond", "rechargeTip": "Please select the recharge amount:", "diamondRecharger": "Diamond recharger", "insufficientBalance": "Insufficient balance", "insufficientBalanceTip": "（Still need {remaining} diamonds to buy）", "@insufficientBalanceTip": {"type": "string", "placeholders": {"remaining": {}}}, "comboBtnTextTop": "Pressing for", "comboBtnTextBottom": "COMBO", "popularityRank": "Ranking", "permanent": "Permanent", "myRankPlace": "Short of last place: {sum}", "@myRankPlace": {"type": "string", "placeholders": {"sum": {}}}, "lastUpdateTime": "Last update time：{time}", "@lastUpdateTime": {"type": "string", "placeholders": {"time": {}}}, "rechargeNotOpen": "The recharge service within the APP has not been opened yet.", "onlineMostTip": "Display up to 50 online users at most.", "package": "Package", "expireTime": "· Will expire on {time}", "@expireTime": {"type": "string", "placeholders": {"time": {}}}, "giftFloatScreen": "{a} gives {b} a {c}", "@giftFloatScreen": {"type": "string", "placeholders": {"a": {}, "b": {}, "c": {}}}, "popularityList": "Gift received", "favoritesList": "Gift sent", "coupleList": "Couple list", "favoritesRank": "Favorites rank", "listDescription": "List description", "rank1st": "1st", "rank2nd": "2nd", "rank3rd": "3rd", "rankGlobalBanner": "{user} become {rankName} top {order}", "@rankGlobalBanner": {"type": "string", "placeholders": {"user": {}, "rankName": {}, "order": {}}}, "view": "View", "opponentGets": "The opponent gets", "youGet": "You get", "tryBtn": "Try", "probabilityGiftTitle": "Congratulations on popping out of your gift：", "probabilityGiftWinTip": "You'll get it in {num} more draws.", "@probabilityGiftWinTip": {"type": "string", "placeholders": {"num": {}}}, "homeFateBellTitle": "Match Chat", "homeFateBellStatusNormalTitle": "Match", "homeChatPartySubtitle": "Multi person online chat", "homeTruthDareSubtitle": "Meet interesting friends", "chatParty": "Voice Party", "homeBlindDateStatusNormalTitle": "Join", "nearbyNoSettingTitle": "Don't know where you are yet.", "nearbyNoSettingDes": "Turn on location access and <PERSON><PERSON> will recommend the right people in your neighborhood", "openPositioning": "Open positioning", "usingLivingArea": "Using living area", "toChoose": "To choose", "proxyErr": "Please make sure you are not using a proxy.", "noGps": "Failed to obtain location", "noGpsRec": "Unable to recommend nearby people for you", "locate": "Locate", "switchRoomTitle": "Change room", "intimateRanking": "Intimate Ranking", "clientError": "Client-side error", "rewardStatistics": "Reward\nStatistics", "remaining": "Remaining\n{time}", "@remaining": {"type": "string", "placeholders": {"time": {}}}, "rewardStEnd": "Reward statistics ended", "topIndex": "Top{index}:", "@topIndex": {"type": "string", "placeholders": {"index": {}}}, "intimacyLevelIncreasedTo": "Intimacy level increased to", "partyDur": "Party duration: {time}", "@partyDur": {"type": "string", "placeholders": {"time": {}}}, "giftAmount": "Gift amount: {amount}", "@giftAmount": {"type": "string", "placeholders": {"amount": {}}}, "intimacyTip": "There are currently no candidates available. Rewarding the other party can enhance intimacy.", "receiveDiamonds": "Receive diamonds", "seeMoreReplies": "The reported content has been hidden", "introduce": "Introduce", "interact": "Interact", "unburden": "<PERSON>burden", "choose": "<PERSON><PERSON>", "waitingStart": "Waiting to start！", "pleaseChooseLove": "Please choose your love.", "intervalSessionsShort": "The interval between sessions is too short", "text": "Text", "giftNewerGuideTip": "You just got this gift, give it to the host you like~", "incredible": "Incredible!", "failureMatching": "Failure of Matching", "rewardToGet": "There are rewards to be claimed", "submitRepeated": "Please don’t submit repeated applications", "titleList": "Title list", "effectPreview": "Effect Preview", "youHadGot": "{you} had got", "@youHadGot": {"type": "string", "placeholders": {"you": {}}}, "youHaveNotGot": "You haven't got", "atUserName": "@User Name", "titleEffectPreviewMsgContent": "Welcome to the studio.", "awaitingWear": "Awaiting wear", "fansGroupTabTitleHostBenefits": "Host benefits", "fansGroupTabTitleFansList": "Fans List", "fansGroupTotalNum": "Total: {sum} people", "fansGroupTabTitleTreasureRecord": "Treasure record", "treasureChestNum": "Treasure chest number: ", "treasureChestTips": "The more treasure chests fans receive, the more generous rewards they will receive as a host !", "treasureChestRecord": "Treasure chest record", "treasureChestItemTips": "Just received a treasure chest", "joinFanGroup": "Join a fan group", "fanClubList": "fan club list", "whos": "{who}'s", "@whos": {"type": "string", "placeholders": {"who": {}}}, "totalPeople": "Total：{sum} people", "@totalPeople": {"type": "string", "placeholders": {"sum": {}}}, "popularity": "Popularity", "joinFanClubSuc": "Successfully joined the fan club!", "no1": "NO. 1", "rewardDetail": "Reward detail", "lvUnLock": "Lv.{num} Unlock", "@lvUnLock": {"type": "string", "placeholders": {"num": {}}}, "JoinFanGroup": "Join fan group", "drawFanNumReward": "Draw {num} once", "fansGroupTips": "Both giving and receiving gifts boost experience.", "getDrawNum": "Get the number of draws", "viewRules": "View rules", "quitGroup": "Quit group", "noLongerInRoom": "You are no longer in the room", "fanLevelUpgrade": "Your fan level has being upgraded to {num} !", "@fanLevelUpgrade": {"type": "string", "placeholders": {"num": {}}}, "fansNoDrawTimesTip": "Go and seize the opportunity by completing the task", "fansCongratulationUserLevelMsg": "Congratulations! {username} The fan club level has been raised to lv.{num}!", "joinedTheFanGroup": "joined the fan group", "exitFansGroupTitle": "Exit reminder", "exitFansGroupTipsContent": "After logging out, the accumulated popularity points and privileges will be reset to zero, Are you sure you want to perform this operation?", "exitFansGroupTipsConfirm": "I confirm", "exitFansGroupTipsThinkAgain": "Think again", "time": "Time", "check": "Check", "giftFloatScreenByLottery": "{a} won {b} from the room club~", "inDraw": "in draw", "redPackTitle": "Heat bonus triggered", "followAndJoinTheFanClub": "Follow and join the fan club", "rush": "<PERSON>", "redPackResultGetMessage": "You grab from the current red packet", "redPackResultTip": "Remember! The faster your hand speed, the more diamonds you will get!", "redPackResultEmptyTip": "The red packet is empty, your hands are not fast enough! Be faster next time!", "wealthCharmLevelUpTitle": "Congratulations on your upgrade!", "redPackExpired": "Angpao envelope has expired", "heat": "Heat", "privileged": "Privileged", "pokedYou": "poked you!", "respondWithGift": "Respond with gift", "redPackDetail": "Get angpao detail", "redPackGet": "Get", "thisWeek": "This week", "lastWeek": "Last week", "intimacyLowerCase": "intimacy", "rankList": "Ranking list", "followHostNow": "Follow the host now", "inviteJoinRoomClub": "Invite you to join the room club！", "joinRoomClub": "Join the room club", "clubTitleSelect": "Club title select", "clubSelectSecondTitle": "The room clubs you had joined", "clubSelectContent": "In this room,you are not part of the room club, you can display your existing fan lights.", "roomFansNameModifyTip": "Room club names are allowed to be modified once every {sum} days", "changeFanClubName": "Change fan club name", "roomFanClubNameLimit": "{min}~{max} letters/numbers", "roomFanClubNameLimitTitle": "Setup requirements", "roomFanClubNameLimitContent": "· Character limit for fan group name: 2~6 letters/numbers, other characters are not supported\n· The name of the fan group cannot use sensitive words such as pornography or pornography, cannot slander or impersonate other people, and cannot use official names.", "wordCountExceeded": "Word count exceeded", "wordCountInsufficient": "Word count insufficient", "roomFansUpdateTip": "The tasks will reset every day at 00:00.", "currentIntimacy": "Current Intimacy", "chatIntimacyFeatureDes": "Gifting increases intimacy. Higher intimacy unlocks more interactive features", "chatIntimacyUnlockNum": "Still {num} Intimacy unlock", "chatIntimacyUnlockMsgTip": "Congratulations on unlocking the {name} feature ! Experience it now and bring yourself closer to the other person!", "chatReplayRewardMsgTip1": "Reply to the message now to get the", "chatReplayRewardMsgTip2": "reward from receiving the gift.", "howGetIntimacy": "How to get Intimacy", "chatPerIntimacyIncrease": "Chatting can get intimacy （+{num} ）", "sendGiftGetIntimacy": "Send gift to get intimacy", "chatIntimacyInfoFemale": "As the intimacy value is gained, the starlight value will also be gained", "chatNoMoneyTaskTitle": "The diamonds have been used up today. You can obtain it in the following ways.", "freeToGet": "Free to get", "goToRecharge": "Go to recharge", "youSendGift": "You send [{n}]", "sendYouGift": "Send you [{n}]", "unlockFeatureDigest": "[Unlocked feature]", "incomeTasks": "Income tasks", "accumulatedStarlight": "Accumulated starlight", "myIncome": "My Income", "moments": "Moments", "myLevel": "My Level", "intimacyRank": "Intimate Ranking", "stageRewards": "Agency incentives", "issued": "Issued", "incomplete": "Incomplete", "diamondTask": "Diamond Task", "unlocked": "Unlocked", "accumulated": "Accumulated", "starlightRewardNum": "{num} starlight receive successful", "diamondRewardNum": "{num} diamond receive successful", "income": "Income", "personalIncome": "Personal Income", "unionIncome": "Agency Income", "exchangeToDiamond": "Exchange to Diamond", "numOfStarlight": "Number of Starlight", "convertStarlight": "Convertible Starlight:{num}", "exchangeAll": "Exchange all", "exchangeNumDiamond": "Successfully exchange {n} diamonds.", "exchangeDiamonds": "Exchange Diamonds", "accumulatedSubmit": "Accumulated Submit", "taskRewardTitle": "Free rewards! completed task now to get!", "goToGet": "Go to get", "openBluetoothTitle": "Open Bluetooth settings", "openBluetoothDesc": "Turn on Bluetooth settings and complete tasks to get free rewards.", "exchangeStarlight": "Exchange with Starlight", "signReceiveRewards": "Sign in to receive rewards", "applyingJoin": "Applying to join", "levelMax": "Level Max", "applyJoinAgency": "Apply to join the Agency", "email": "Email", "note": "Note", "applyAgencySubmit": "The application has been submitted and is awaiting agency review", "validEmailTip": "Please enter a valid email format.", "agencyContactTitle": "Please add <PERSON><PERSON><PERSON><PERSON> to join the agency", "copyNumber": "Copy Number", "agencyNameId": "· Agency: {name} (ID:{id})", "sTypeAudioLabel": "Audio", "sTypeImageLabel": "Image", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Other resource", "sActionPlayHint": "Play", "sActionPreviewHint": "Preview", "sActionSelectHint": "Select", "sActionSwitchPathLabel": "Switch Path", "sActionUseCameraHint": "Use Camera", "sNameDurationLabel": "Duration", "sUnitAssetCountLabel": "count", "chattingNow": "Chatting now", "giftGallery": "Gift gallery", "lightingGift": "Lighting {num} gifts", "giftGalleryListSubtitle": "Gift will light up when you send the total number required.", "noTitleGifters": "No title gifters", "titleGifters": "Title gifters", "getTitle": "Get title", "gitGalleryRankTitle": "Top 3 gifters", "gitGalleryRankSubtitle": "Noce this gift lit up, it’s No.1 gifter will get recognized as its title gifter.", "giftGalleryIntroductionTitle": "Gift Exhibition Hall Description", "giftGalleryIntroIlluminationSectionTitle": "Gift Illumination Rules", "giftGalleryIntroIlluminationRuleContent": "1. The Gift Exhibition Hall is an interactive gameplay feature in voice rooms. When you receive the specified number of gifts, you can illuminate the corresponding gift (the specific number of gifts required is subject to the information on the page). Click on the gift image to view the gift-giving leaderboard details.\n\n2. The gift-giving leaderboard for individual gifts will only display the top three users. By giving the specified number of gifts, you can have the naming rights for that gift (display range: limited to the gift wall of the recipient users).\n\n3. The gifts in the exhibition hall will be updated irregularly. The newly added gifts will be displayed simultaneously on the gift wall. Giving the specified number of new gifts can also illuminate them.\n", "giftGalleryIntroNameSectionTitle": "Gift Naming Rules", "giftGalleryIntroNameRuleContent": "1. After a gift is illuminated, the user who ranks first on the gift-giving leaderboard can have the naming rights for that gift. The profile picture of the top-ranking user will be displayed below the task of that gift, and the profile picture will be updated in real-time. If multiple users have given the same number of gifts, the user who gave the gift first will be considered the top-ranking user.\n\n2. Once a user successfully names a gift, a public screen notification will be triggered in the room where the gift recipient is currently located.", "consumptionReminder": "Consumption reminder", "consumptionReminderContent": "Do you want to spend {n} diamonds to light up this gift for {sex}?", "thinkAgain": "Think again", "noRemindMeAgain": "Don't remind me again", "nextOne": "Next", "matchingHqUsers": "Matching high-quality users", "moveThemeSucc": "Move Successfully", "homeLudoSubtitle": "The most popular board game", "enteredDays": "{num} had been entered", "noMoreSeat": "No extra seats", "cannotKickMicInPlaying": "Players in the game cannot be kicked out", "cannotDownMicInPlaying": "Players in the game cannot down mic", "cannotDownMicCaptain": "Captain in the game cannot down mic", "changeRoomModeTitle": "Change the Room Type", "changeModeContent": "You need to end the current game to perform this operation. Do you want to end the game?", "endGame": "End the game", "clashEmpire": "Clash Empire", "upgradeRole": "Upgrade Role", "empirePropBuff": "BUFF: Increase Power", "empirePropCamel": "Summoning Battle Camels", "empirePropDragon": "Summoning the Divine Dragon", "score": "Score", "skillCD": "Cooling down, please try again later", "addScore": "Audition Score", "our": "Our", "changeCampFailed": "Failed to join the camp, can not change the camp", "sendGiftFailNoCamp": "Not joined the camp, can't send gifts", "cocosGameLoadFailed": "Game loading failure!", "actNow": "Act now", "giftLightUpRank": "Gift light up rank", "privatePhotos": "Private photos", "honour": "Honour", "kindTips": "Kind tips", "privatePhotoTipHelp": "You can view each other's private photos only when your intimacy reaches 10~.\nText chat and gifts can increase your intimacy", "pictureUploaded": "The picture has been uploaded", "uploadFailed": "Upload failed, please try again later", "gameCenter": "Game Center", "jackPotTips": "Completing tasks earns points，which can be redeemed for rewards.", "congratulationsToGotten": "Congratulations to {userName} on {giftName}", "myIntegrations": "My integrations", "jackPot": "<PERSON><PERSON><PERSON>", "rank": "Rank", "gameNotFound": "Game not Found!", "dailyList": "Daily list", "weeklyList": "Weekly list", "gameCenterRule": "Game Center Rule", "gameCenterRuleTitle1": "1 How to play the 1-point lucky draw?", "gameCenterRuleContent1": "Players who meet the conditions required in the game will have the opportunity to receive task rewards - points. Use points for a lucky draw, with a 100% winning rate!", "gameCenterRuleTitle2": "2 How is the prize pool of the 1-point lucky draw calculated?", "gameCenterRuleContent2": "Use 10 points for one lucky draw with a 100% winning rate!", "gameCenterRuleTitle3": "3 How is the ranking calculated?", "gameCenterRuleContent3": "The ranking is arranged in descending order based on the golds currently won by the user in all games.", "gameCenterRuleTitle4": "4 How is the ranking list refreshed?", "gameCenterRuleContent4": "The ranking list is divided into daily and weekly rankings. The daily ranking will reset at 00:00 every day; the weekly ranking will reset at 00:00 every Monday.", "gameCenterRuleTitle5": "5 Are there rewards for the ranking list?", "gameCenterRuleContent5": "Yes, during the settlement of the weekly ranking, corresponding avatar frames will be awarded to the top three users on the list. Reward distribution time: 1:00 AM on Monday.", "waitingForDrawing": "Waiting for last draw to end", "congNameToGift": " has won", "congOnWinning": "Congratulations on winning", "moreTasks": "More tasks", "rateApp": "Please rate our application!", "imageSizeLimit": "Pictures cannot be larger than {size}M", "@imageSizeLimit": {"type": "int", "placeholders": {"size": {}}}, "youDontHaveTheAccessToUploadGifPicture": "You don't have the access to upload gif picture", "album": "Album", "riskyTips": "Involves risky information; beware of scams.", "roomCover": "Room Cover", "roomWillCloseTips": "Due to inactivity in the room for a long period, the room will automatically close in {time}s.", "@roomWillCloseTips": {"type": "int", "placeholders": {"time": {}}}, "roomClosedDueToProlonged": "The room has been closed due to prolonged inactivity.", "forceClosedTips": "Violating community rules, this room has been closed", "imageTooSmall": "The selected image size is too small.", "quit": "Quit", "automaticStartWhenFull": "Automatic start when full", "waitingForOtherPlayersToSettleGame": "Waiting for other players to settle game", "theMicAreFullUnableToJoinTheGame": "The mic are full, unable to join the game", "theGamePositionIsFull": "The game position is full", "theHostJustKickedYouOutOfTheGamePlease": "The host just kicked you out of the game, please try again later", "loadGameFailed": "{game} failed to load please try again", "baloot": "Baloot", "domino": "Domino", "candy": "<PERSON>", "theGamePositionIsLocked": "The game position is locked", "theRoomOwnerEndsTheGame": "The room owner ends the game.", "myCustomers": "My customers", "noConversation": "No conversation", "diamondBalance": "Diamond Balance", "amountToExchange": "Enter the amount to exchange", "numberOfDiamonds": "Number of Diamonds", "doYouWantToSpend": "Do you want to spend", "toChange": "to change", "containInvaildCharacters": "Maybe contains special characters such as “ ' / <>. Please try others...", "chatConsumes": "Sending messages consumes", "or": "or", "toAddIntimacy": "to add Intimacy", "chatConsumeGoldCoin": "First consume gold coins, and then consume diamonds if gold coins are insufficient", "normal": "Normal", "quickly": "Quickly", "ticket": "Ticket", "theNicknameYouEnteredIsTooShort": "The nickname you entered is too short", "doYouWantToKickThisUserOutOfThe": "Do you want to kick this user out of the game?", "dontRemindMeToday": "Don't remind me today", "cannotExitRoomDuringGame": "You cannot close the room during the game, but you can choose to leave the room.", "transferCaptain": "Transfer Captain", "doYouWantToTransferUserAsTheGameCaptain": "Do you want to transfer {user} as the game captain?", "@doYouWantToTransferUserAsTheGameCaptain": {"type": "string", "placeholders": {"user": {}}}, "recruit": "Recruit", "inviteRoomOnlineUsers": "Recruit Room Online Users", "inviteRoomOnlineEmpty": "There are no other online users in the room", "inviteRoomOnlineAlertTitle": "{username} invites you to join the game and play.", "@inviteRoomOnlineAlertTitle": {"type": "string", "placeholders": {"username": {}}}, "userJoinedTheGame": "The user has joined the game", "inviteFriends": "Invite friends", "noFriendsOnline": "No friends online", "friendsYouMayKnow": "Friends you may know", "playingVoiceRoom": "In voice room", "playingGame": "Playing {game}", "@playingGame": {"type": "string", "placeholders": {"game": {}}}, "inGameTag": "In {game}", "inviteYouToChat": "Invite you to voice chat", "inviteYouToGame": "Invite you to game", "inTheRoomNow": "In the room now", "unLockFreeChatTips": "Unlock free message chat will no longer increase intimacy, sending gifts can increase intimacy.", "tomorrowGetMoreRewards": "Tomorrow sign-in will get more rewards", "received": "Received", "signInDays": "Sign-in for {day} days", "@signInDays": {"type": "int", "placeholders": {"day": {}}}, "newbieTitle": "At the start of a new journey, <PERSON><PERSON> has prepared a welcome gift for you. Enjoy your time on Winker!", "welcomeToWinker": "Welcome to Winker", "whichDay": "Day {day}", "@whichDay": {"type": "int", "placeholders": {"day": {}}}, "chatGuideTipsFate": "Your matching Fate Ball list,click on the one you are interested in to start chatting!", "chatGuideTipsMsg": "All message will be displayed here,and you can click to chat with him/her.", "newbieTasks": "<PERSON>bie Tasks", "milestoneListTasks": "Milestone Tasks", "joinWinkerDays": "Join winker:{num} days", "@joinWinkerDays": {"type": "int", "placeholders": {"num": {}}}, "full": "Full", "waitingForTheGameToStart": "Waiting for the game to start", "findYourFatedMate": "Find Your Fated Mate", "normalLudoTips": "Normal mode, 4 tokens, magic items.", "quicklyLudoTips": "Quickly mode, 1 tokens, magic items.", "noFeedbackYet": "No feedback yet", "intimacyRule1": "A gift worth 3 diamonds = 1 intimacy point.", "intimacyRule2": "A gift worth 40 gold coins = 1 intimacy point conversion.", "intimacyBottomTips": "Chat intimacy is consistent with intimacy . Gifts sent in chat and voice rooms both increase intimacy.", "selectedTopic": "Selected topic", "selectedTopicCount": "{count}/{max}", "@selectedTopicCount": {"type": "string", "placeholders": {"count": {}, "max": {}}}, "thereIsNoContentHere": "There is no content here", "csaeOrCsam": "CSAE/CSAM", "exitWillDelete": "Exit will delete the records what you selected", "discardSelected": "Discard Selected", "sendAndUnlock": "Send a {gift} gift to unlock the chat.", "@sendAndUnlock": {"type": "string", "placeholders": {"gift": {}}}, "chatUnlockedSuccess": "Unlocked successfully, start chatting now!", "csaeOrCsamDetail": "CSAE/CSAM: Child Sexual Abuse and Exploitation/Child Sexual Abuse Materia", "waitForReply": "Wait for a reply before sending another message.", "roomIdKey": "Room ID", "cost": "Cost:", "reportAvatar": "Report avatar", "reportNickname": "Report nickname", "reportIntroduction": "Report introduction", "reportVoiceCard": "Report voice card", "searchHistory": "Search History", "hotRoom": "Hot Room", "searchRoomIdUserId": "Search by room ID/user ID", "playInOrder": "Play in order", "singleCycle": "Single cycle", "playRandomly": "Play randomly", "notFollowedAnyRooms": "you have not followed any rooms.", "roomTag": "Room tag", "addMusic": "Add Music", "levelUnlock": "Level {level} unlock", "@levelUnlock": {"type": "int", "placeholders": {"level": {}}}, "chatRoomUpgrade": "Voice Room Upgrade", "administrator": "Number of Admin", "roomBackground": "Background", "tmo": "TMO", "mine": "Mine", "playDefaultMusic": "Play the default music", "requestStorageData": "\"Winker\" requests access to your storage data.", "contribution": "Contribution", "numDays": "{count} days", "skin": "Skin", "props": "Props", "chatBubble": "<PERSON><PERSON>", "userCar": "Ride", "entranceMessage": "Entrance Message", "homePageEffect": "Home page effect", "letter": "Letter paper", "customBackgroundCard": "custom background card", "miniPostal": "Mini postal", "giftBoxDesc": "Gift Box", "using": "Using", "homePageBackground": "Home page background", "mineMenuBackpack": "Bag", "use": "use", "friendsToBeSent": "Friends to be sent:", "selectFriends": "Select friends", "giftBox": "Gift box:", "sendGiftTips": "Send goods need use a Gift box", "friendNotFound": "This friend is not found", "buyGiftBoxDesc": "Sending gifts requires the gift box ，do you want to spend {price} stars to purchase the gift box?", "@buyGiftBoxDesc": {"type": "string", "placeholders": {"price": {}}}, "pleaseChooseLengthOfPurchase": "Please choose validity period", "countAndCurrency": "{count} {currency}", "@countAndCurrency": {"type": "string", "placeholders": {"count": {}, "currency": {}}}, "bugGoodsDialogTips": "Are you sure you want to spend {star} coins to buy {type}?", "@bugGoodsDialogTips": {"type": "string", "placeholders": {"star": {}, "type": {}}}, "purchasedIt": "You have purchased it!", "doYouWantToUseTheCustomBackgroundCardsDirectly": "Do you want to use the custom background cards directly?", "toUseProp": "Do you want to use this prop?", "lengthOfPurchase": "Validity period", "nw": "New", "micLayoutUse": "Do you want to use the {name}？", "@micLayoutUse": {"type": "string", "placeholders": {"name": {}}}, "helloNiceToMeetYou": "Hello, nice to meet you!", "nameEnteredTheRoom": "{name}...entered the room", "@nameEnteredTheRoom": {"type": "string", "placeholders": {"name": {}}}, "discountOff": "{discount}% off", "@discountOff": {"type": "int", "placeholders": {"discount": {}}}, "max": "Max", "decorations": "Decorations", "followingUser": "Following user", "noOneYouAreFollowing": "You haven't followed any users yet.", "notJoinedAnyRoom": "You haven't joined any room yet.", "msgDescReward": "[<PERSON><PERSON>]", "lackOfCoinsPleaseFinishMoreTasksOrExchangeDiamonds": "Lack of golds! please finish more tasks or exchange diamonds to get golds.", "insufficientCoins": "Insufficient coins, recharge now!", "badgeRule": "Badge rule", "wearBadgeRule": "Wear badge", "wearBadgeRule1": "Light up badge to wear it", "wearBadgeRule2": "Every user can wear 3 badges at most", "wearBadgeRule3": "Badges will be displayed on the personal information page, voice room, and chat page", "badgeRankingRule": "1. The badge leaderboard is ranked according to the total number of stars obtained by users lighting up badges\n\n2. If two or more users have the same number of stars, they will be ranked according to the time when they got stars", "badgeTypeRule": "How to obtain a badge?", "badgeTypeRule1": "Achieve the corresponding conditions to light up the badge", "badgeTypeRule2": "Badge level: bronze-silver-gold-diamond", "badgeRankRule": "Badge rank", "badgeLeaderboard": "Badge leaderboard", "badgeRankRule1": "Lighting up badge of different level can get stars:", "badgeRuleBronze": "Bronze", "badgeRuleSilver": "Silver", "badgeRuleGold": "Gold", "badgeRuleDiamond": "Diamond", "badgeRankRule2": "Users will be ranked according to the stars", "numStars": "{stars} stars", "@numStars": {"type": "int", "placeholders": {"stars": {}}}, "badgeRanking": "Ranking:{ranking}", "@badgeRanking": {"type": "string", "placeholders": {"ranking": {}}}, "congratsYouHaveLightedUp": "Congrats! You have lighted up {badge} badge", "@congratsYouHaveLightedUp": {"type": "string", "placeholders": {"badge": {}}}, "checkAndWearBadge": "You can check and wear badge on the page \"Mine-Settings-Badge\"", "taskBadge": "Task badge", "growthBadge": "Growth badge", "gameBadge": "Game badge", "badgeDisplay": "Badge display", "badgeDisplayFollowingPlaces": "Badge will be displayed at the following places", "onlineUsersListOfVoiceRoom": "Online users list of voice room", "signInForDays": "Sign-in for {day} consecutive {day,plural, =0{day}=1{day}=2{days}few{days}other{days}}", "@signInForDays": {"type": "string", "placeholders": {"day": {}}}, "giftBadge": "Gift Badge", "activityBadge": "Activity Badge", "achievementTime": "Achievement time:", "goToWear": "Go to wear", "myFamily": "My Family", "pleaseEnterChar": "Please enter {from}-{to} characters", "@pleaseEnterChar": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familySlogan": "Family Slogan", "joinMode": "Join mode", "joinWealthLevel": "User Wealth Level Limit", "createWithAmount": "Create with {amount}", "@createWithAmount": {"type": "int", "placeholders": {"amount": {}}}, "createFamilySuccessfully": "You have successfully submitted your family information, please wait for review", "applicationSuccessful": "Application successful", "applicationFailed": "Application failed", "underReview": "Under review", "usernameInvitedYouToJoinFamilynameDoYouAgreeTo": "{userName} invited you to join {familyName}, do you agree to join?", "usernameRefuseYourFamilyApplication": "{userName} refuse your family application.", "usernameWantsToJoinYourFamilyPleaseReviewIt": "{user<PERSON><PERSON>} wants to join your family, please review it!", "congratulationsOnSuccessfullyCreatingTheFamilynameFamilyYouCanNow": "Congratulations on successfully creating the {family<PERSON><PERSON>} family. You can now recruit more members. Go check it out!", "congratulationsOnJoiningTheFamilynameFamilyComeCheckItOut": "Congratulations on joining the {family<PERSON><PERSON>} family, come check it out now!", "usernameRejectedYourApplicationGoCheckOutOtherFamiliesGo": "{user<PERSON><PERSON>} rejected your application. Go check out other families. Go to Family Square!", "youHaveBeenRemovedFromTheFamilynameGoCheckOut": "You have been removed from the {familyName}. Go check out other families. Go to Family Square!", "congratulationsOnBeingAppointedAsFamilynamesPosition": "Congratulations on being appointed as {familyName}’s {position}!", "congratulationsToFamilynameFamilyForUpgradingToLevellevelComeCheck": "Congratulations to {family<PERSON><PERSON>} family for upgrading to Level{level}. come check it out now!", "yourFamilyHasBeenDisbandedGoCheckOutOtherFamilies": "Your family has been disbanded! Go check out other families. Go to Family Square!", "yourFamilyHasBeenDisbanded": "Your family has been disbanded!", "searchByFamilyID": "Search by family ID", "familyTreasury": "Family Treasury", "theFamilyTreasuryWillBeUsedForTheFamilyBlessing": "The Family Treasury will be used for the family lucky bag, and family members can get more benefits and rewards!", "familyTask": "Family Task", "familyLevelExp": "Family level exp", "whenTheFamilyExperienceValueReachesTheUpperLimit": "1. When the family experience value reaches the upper limit, the personal experience value also reaches the upper limit, and no more experience will be gained on that day.", "whenTheFamilyTreasuryReachesTodaysLimitOrThe": "2. When the family treasury reaches today's limit or the total limit, no more family treasury will be added today.", "weeklyContribution": "Weekly Contribution", "monthlyContribution": "Monthly Contribution", "freeJoin": "Free join", "noOneCanSpeak": "No one can speak", "groupMessageNotDisturb": "Group message do not disturb", "familyInfoEdit": "Family info Edit", "familyRoomNameCannotBeModified": "Family room name cannot be modified", "familyRoomCoverCannotBeModified": "Family room cover cannot be modified", "accessPermission": "Access Permission", "allUsers": "All Users", "onlyFamilyMembers": "Only family members", "allUsersCanEnterTheRoom": "All users can enter the room?", "onlyFamilyMembersCanEnterTheRoom": "Only family members can enter the room?", "afterTurningItOnOnlyFamilyMembersCanEnterThe": "After turning it on, only family members can enter the room, and non-family members who are already in the room will be kicked out of the room", "cannotBeSetDuringTheGame": "Cannot be set during the game", "followTheRoom": "Follow the room", "followedSuccessfully": "Followed successfully", "unfollow": "Unfollow", "unfollowSuccessfully": "Unfollow successfully", "nonFamilyMembersAreNotAllowedToEnter": "The family mode has been turned on in this room. Non-family members will be kicked out of the room.", "nonFamilyMembersAreNotAllowedToEnterInThisRoom": "The family mode has been turned on in this room. Non-family members are not allowed to enter.", "disbandTheFamily": "Disband the Family", "familyPrivilege": "Family privilege", "familyNameEditOnce": "Family name is revised once a week", "familySloganEditOnce": "Family slogan is revised once a week", "editFamilyName": "Edit family name", "editFamilySlogan": "Edit family slogan", "nextRevisionDate": "Next revision date:{date}", "@nextRevisionDate": {"type": "string", "placeholders": {"date": {}}}, "myTask": "My task", "expStr": "Exp", "notMeetRuleJoinFamily": "You do not meet the following requirement to join this family:", "familyCoverEditAfterDays": "Family cover s revised once a week，Please edit it after {day}.", "@familyCoverEditAfterDays": {"type": "string", "placeholders": {"day": {}}}, "afterYouQuitTheFamilyYouWillClearAllThe": "After you quit the family, you will clear all the related assets in the family. Are you sure you want to quit the family?", "theFamilyLeaderCannotLeaveTheFamilyDirectlyYouCan": "The family leader cannot leave the family directly. You can contact the official customer service for assistance.", "disbandingTheFamily": "Disbanding the Family", "areYouSureYouWantToDisbandTheFamilyOnce": "Are you sure you want to disband the family? Once the family is disbanded, all personal and family-related assets will be cleared. After applying for disbandment, there is a {days}-day cooling-off period, during which you can cancel the disbandment at any time. After the {days}-day cooling-off period ends, the family will be disbanded.", "@areYouSureYouWantToDisbandTheFamilyOnce": {"type": "int", "placeholders": {"days": {}}}, "youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": "You submitted the family disbandment application on {time}, and it is now in the {days}-day cooling-off period. You can cancel the disbandment at any time.", "@youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": {"type": "int", "placeholders": {"time": {}, "days": {}}}, "cancelDissolution": "Cancel dissolution", "contactCS": "Contact CS {uid}", "coolingOff": "{days}-day cooling-off", "@coolingOff": {"type": "int", "placeholders": {"days": {}}}, "openMuteAll": "{user} enabled global mute.", "@openMuteAll": {"type": "string", "placeholders": {"user": {}}}, "closeMuteAll": "{user}  disabled global mute.", "@closeMuteAll": {"type": "string", "placeholders": {"user": {}}}, "globalMute": "Global Mute", "userMute": "You are banned from speaking", "elders": "Elders", "ironFamily": "Iron Family", "bronzeFamily": "Bronze Family", "silverFamily": "Silver Family", "goldFamily": "Gold Family", "gloryFamily": "Glory Family", "upperLimit": "Upper limit", "noOneCanSpeakTip": "After turning it on,only the family leader\nand vice-family leader can speak.", "inReview": "In Review", "selectCover": "Please upload the family cover.", "familyNameCountInvalid": "The family name must be between {from} and {to} characters.", "@familyNameCountInvalid": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familySloganCountInvalid": "The family slogan must be between {from} and {to} characters.", "@familySloganCountInvalid": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familyInfoRejected": "The family information you submitted has been rejected due to violation of Winker community rules. Please go to the family creation page to view details!", "followingRoom": "Following Room", "eventSubjectPlaceholder": "Enter event theme...", "eventSelectPeriod": "Selection period...", "eventBannerLimit": "* The banner size is 300*600 and it can not be more than 5MB", "eventCreateToast": "You haven't fill in all the information", "eventCreatedShare": "You have created your room event successfully. Share it to Moment now!", "eventShareToMoment": "Share it to Moment", "eventShareTitle": "Subscribe it and participate in the party with me~", "eventShareHost": "Host:", "eventSubject": "Subject:{name}", "@eventSubject": {"type": "string", "placeholders": {"name": {}}}, "evenShareSubscribers": "{num} subscribers", "@evenShareSubscribers": {"type": "int", "placeholders": {"num": {}}}, "evenDetailSubscribers": "Subscribers({num})", "@evenDetailSubscribers": {"type": "int", "placeholders": {"num": {}}}, "evenDetailRoomId": "Room ID", "evenDetailSubject": "Subject", "evenDetailTime": "Time", "evenDetailMedal": "Medal", "eventRuleTitle": "Room Event Rules", "eventRule1": "1. Users need to create their own voice room first and then they can create room event", "eventRule2": "2. Users can create room event within 7 days", "eventRule3": "3. When creating a room event, the event details need to be reviewed. During the review process, the details can be modified. Once approved, only the event time can be changed. Each event can only modify its details once.", "eventMineEmpty": "You haven't created your room event~", "eventCreateNoRoom": "You need to create your own voice room before you create room event", "eventUploadBanner": "Upload Banner", "timeOfDuration": "Time of duration", "yourEventInfoNotFill": "Your room event {name} did not fill in the information", "@yourEventInfoNotFill": {"type": "string", "placeholders": {"name": {}}}, "subject": "subject", "starTime": "start time", "banner": "banner", "eventDescription": "Event description", "enterEventDescription": "Enter event description", "eventRoom": "Event Room", "eventRoomIDType": "Room ID:{id}   ({type})", "@eventRoomIDType": {"type": "string", "placeholders": {"id": {}, "type": {}}}, "personal": "personal", "roomEvent": "Room Event", "rejectReview": "Reject review", "giftGivingDuringTheEvent": "Gift giving during the event", "eventGiftRankTips": "During the event, sending diamond gifts and gold coin gifts will earn gift points.", "giftSendingDetails": "Gift sending details", "diamondGifts": "Diamond gifts", "coinsGift": "Coins gift", "cancelRoomEvent": "Cancel room event", "cancelRoomEventTips": "Do you want to cancel the room event?", "familyLuckyBag": "Family Lucky Bag", "thereAreTimeUntilTheStart": "There are {time} until the start", "clickToGrabTheLuckyBag": "Click to grab the lucky bag", "youAreNotAMemberOfThisFamilyAndCannot": "You are not a member of this family and cannot participate in grabbing the lucky bag", "viewLuckyBagClaimDetails": "View lucky bag claim details", "congratulationsOnReceiving": "Congratulations on receiving", "unfortunately": "Unfortunately", "theLuckyBagHasBeenFullyClaimed": "The lucky bag has been all claimed!", "sendLuckyBagWithoutA": "Send lucky bag", "numberOfLuckyBags": "Number of lucky bags:", "enterTheNumberOfLuckyBags": "Enter the number of lucky bags", "amount": "Amount:", "atLeastNumDiamonds": "At least {num} diamonds", "enterTheTotalDiamondAmountForTheLuckyBag": "Enter the total diamond amount for the lucky bag", "fundDiamond": "1 fund = 1 diamond", "sendingTime": "Sending time:", "selectSendingTime": "Select sending time", "sendingImmediately": "sending immediately", "ifTheFamilyLuckyBagIsNotClaimedWithin": "If the family lucky bag is not claimed within 24 hours, the remaining balance will be reclaimed by the system", "theFamilyHasBeenFrozenUnableToDistributeLuckyBags": "The family has been frozen, unable to distribute lucky bags", "areYouSureYouWantToDistributeATotalOf": "Are you sure you want to distribute a total of {num1} diamonds in {num2} lucky bags at {where1} {where2}?", "luckyBagRecord": "Lucky Bag Record", "bestLuck": "Best luck", "aFamilyLuckyBagWasSentAtRoomPleaseGo": "A family lucky bag was sent at {room}, please go to the family room to grab it!", "theFamilyLuckyBagHasBeenSentPleaseGoTo": "The family lucky bag has been sent, please go to the family room to grab it!", "theFamilyLuckyBagHasNotBeenFullyClaimedYet": "The family lucky bag has not been fully claimed yet, hurry up and grab it in the family room!", "youHaveReachedTheDeviceClaimLimitAndCannotClaim": "You have reached the device claim limit and cannot claim the lucky bag", "nowForReal": "Now", "roomProfile": "Room profile", "chooseRoomLabel": "Choose room label", "familyLuckyBagCannotBeDistributed": "If there are less than {num} family members, the lucky bag cannot be distributed.", "youAreNotFamilyMember": "You are not a member of this family.", "notObtained": "Not obtained", "userTmpBanned": "This user was banned because of violation community rule.", "noRoomCreateEvent": "Before creating a room event, please create your own room first", "noSubscribeEvent": "You haven't subscribed to any events yet. Check out the Room Activities and subscribe to the ones that interest you!\n", "allRoomEvent": "All Room Event", "openTreasure": "open treasure", "luckyBagClaimed": "{num} lucky bag, all claimed in {time} minutes", "theJackPot": "the jackPot", "wonTheAward": "{who} won {multiple} reward, get {award}", "isDropping": "is dropping", "isSending": "is sending"}