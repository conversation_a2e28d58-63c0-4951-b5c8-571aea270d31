{"appName": "Winker\ner", "pingName": "Ping!", "@@locale": "id", "msgDescImage": "[Foto]", "msgDescAudio": "<PERSON><PERSON>", "msgDescVideo": "Pesan Video", "tabHome": "Be<PERSON><PERSON>", "tabMoments": "<PERSON><PERSON>", "tabChat": "Cha<PERSON>", "tabMine": "<PERSON><PERSON>", "pin": "<PERSON><PERSON>", "unpin": "Tidak <PERSON>", "delete": "Hapus", "send": "<PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "match": "Cocok", "copy": "<PERSON><PERSON>", "cancel": "Batalkan", "sureDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghap<PERSON>nya?", "callError": "Kesalahan panggilan", "defaultError": "Server kami sedang sibuk sekarang, silakan coba la", "defaultMatchError": "<PERSON><PERSON><PERSON> anda belum di<PERSON>n, tapi ini bukanlah akhir. Coba lagi nanti!", "iAm": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON> de<PERSON>", "myNameIs": "<PERSON>a saya adalah", "myBirthIs": "<PERSON><PERSON> saya", "switchAvatar": "Ganti sebuah avatar", "female": "<PERSON><PERSON>", "male": "Pria", "setAvatar": "Atur avatar", "tipSetAvatar": "<PERSON><PERSON><PERSON>", "setUpProfile": "<PERSON><PERSON> profil", "tipCompleteInfo": "<PERSON><PERSON><PERSON><PERSON> profil saya", "yourName": "<PERSON><PERSON>", "next": "Berikutnya", "typing": "Ketik...", "recording": "Merekam...", "following": "<PERSON><PERSON><PERSON><PERSON>", "followers": "Pengikut", "visitors": "Pengunjung", "copySuccess": "<PERSON><PERSON><PERSON><PERSON> disalin", "follow": "<PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "editName": "<PERSON> nama", "save": "Simpan", "waitingFirstStep": "<PERSON><PERSON><PERSON> langkah pertama <PERSON>a", "read": "Baca", "describeYourself": "Deskripsikan diri sendiri", "name": "<PERSON><PERSON>", "callInviteVideo": "<PERSON><PERSON> di<PERSON>ng untuk panggilan video", "callInviteAudio": "<PERSON>a diundang untuk  panggilan suara", "tipFollowBack": "<PERSON><PERSON><PERSON> kembali untuk menjadi teman.", "tipHasFollowing": "Mengikuti <PERSON> ini.", "unFollowConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk berhenti mengikuti pengguna ini?", "unFollow": "<PERSON><PERSON>", "unFollow2": "<PERSON><PERSON>", "@unFollow2": {"description": "Accurate translation copy in some languages"}, "report": "Lapor", "reply": "<PERSON><PERSON>", "block": "Blokir", "hintNetError": "<PERSON><PERSON><PERSON><PERSON> gagal.", "itsMatch": "Cocok!", "friendshipMatching": "Pencocokan persahabatan", "findFriends": "<PERSON><PERSON><PERSON> teman", "personalityTests": "<PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "avatar": "Avatar", "nickname": "<PERSON><PERSON>", "birthday": "<PERSON><PERSON>", "interests": "<PERSON><PERSON>", "notifications": "Notif<PERSON><PERSON>", "allNotifications": "<PERSON><PERSON><PERSON>", "onlyMessageMoment": "<PERSON><PERSON> per<PERSON> pesan, momen & inter<PERSON>i", "onlyMessage": "<PERSON><PERSON> pesan", "noNotifications": "<PERSON><PERSON><PERSON> notifika<PERSON>", "blockedList": "<PERSON><PERSON><PERSON> yang <PERSON> blo<PERSON>r", "unblock": "<PERSON><PERSON> blokir", "appLanguage": "Bahasa", "helpAndFeedback": "Bantuan & Umpan balik", "shareApp": "Bagikan Aplikasi", "aboutUs": "<PERSON>tang kami", "privacyPolicy": "<PERSON><PERSON><PERSON><PERSON>  privasi", "termOfUse": "<PERSON><PERSON><PERSON><PERSON>", "logoutAlert": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> t<PERSON> akan menerima pesan baru lagi.", "logout": "<PERSON><PERSON><PERSON>", "auto": "<PERSON><PERSON><PERSON><PERSON>", "forYou": "Untukmu", "moment": "<PERSON><PERSON>", "comments": "Komentar", "saySomething": "<PERSON><PERSON>rakan tentang momen ini dengan {sex}", "@saySomething": {"type": "string", "placeholders": {"sex": {}}}, "post": "Memposting", "tipPublishInput": "Apa yang menarik?", "guidePublishInput": "Perkenalkan diri sendiri dengan kata-kata sederhana...", "canceled": "Di<PERSON><PERSON><PERSON>", "declined": "<PERSON><PERSON><PERSON>", "callFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "callDeclined": "Panggila<PERSON> di<PERSON>", "callCancelled": "Panggilan dibatalkan oleh penelpon", "durationWith": "Durasi: {time}", "@durationWith": {"type": "string", "placeholders": {"time": {}}}, "timeLeftToday": "{num} kali tersisa hari ini", "@timeLeftToday": {"type": "string", "placeholders": {"num": {}}}, "timesNum": "{num,plural, =0{{num} waktu}=1{{num} waktu}=2{{num} waktu}few{{num} waktu}other{{num} waktu}}", "@timesNum": {"type": "string", "placeholders": {"num": {}}}, "holdToSpeak": "<PERSON><PERSON> untuk berb<PERSON>", "swipeUpToCancel": "<PERSON><PERSON><PERSON> ke atas untuk batalkan", "recordingWillStop": "<PERSON><PERSON><PERSON> akan be<PERSON><PERSON>", "imCallInstruction": "<PERSON><PERSON><PERSON> yang lain untuk memulai panggilan suara", "ok": "<PERSON>e", "audioCall": "Panggilan Audio", "videoCall": "Panggilan Video", "personalityTestsDetail": "<PERSON><PERSON><PERSON> teman yang cocok dengan hobi <PERSON>", "matchFriends": "<PERSON><PERSON><PERSON> teman", "findingPerfectMatchTitle": "<PERSON><PERSON>i pasangan Anda yang cocok", "matchFailed": "Pencocokan gagal", "close": "<PERSON><PERSON><PERSON>", "matchSuccess": "Pencocokan berhasil", "facebook": "Facebook", "instagram": "Instagram", "whatsApp": "WhatsApp", "snapchat": "Snapchat", "message": "<PERSON><PERSON>", "more": "Selengkapnya", "followSuccess": "<PERSON><PERSON><PERSON><PERSON>", "chatNow": "<PERSON><PERSON><PERSON>", "noticeTitle": "Not<PERSON><PERSON><PERSON> momen", "noticeLike": "Menyukai pos Anda", "noticeFollow": "mulai mengi<PERSON>", "matesOnlineNow": "Aktif<PERSON> se<PERSON>", "tapAgainToExit": "Ketuk kembali lagi untuk keluar", "skip": "<PERSON><PERSON>", "loginSlogan": "Tempat paling pas untuk menemukan dia yang cocok", "loginAgree": "<PERSON><PERSON> ma<PERSON>, anda setuju", "loginFailed": "<PERSON><PERSON>, silakan coba dengan cara lain.", "signInApple": "<PERSON><PERSON><PERSON> dengan akun <PERSON>", "signInFacebook": "Ma<PERSON>k dengan facebook", "signInFaceGoogle": "Ma<PERSON>k dengan akun <PERSON>", "agreeAlert": "<PERSON><PERSON> setujui dengan <PERSON> privasi dan Ke<PERSON>an penggunaan untuk masuk", "passionate": "<PERSON><PERSON>", "emptyMoment": "<PERSON><PERSON><PERSON> belum ada <PERSON>.", "checkNetWork": "<PERSON><PERSON> periksa koneksi jaringan Anda dan coba lagi", "tryAgain": "Coba lagi", "noFollowing": "Tidak ada yang <PERSON>", "noFollowers": "Tidak ada Pen<PERSON>", "noVisitors": "Tidak ada <PERSON>", "msgDescVideoCall": "[Panggilan video]", "msgDescAudioCall": "[Panggilan audio]", "msgDescShare": "[Bagikan]", "msgDescUnknown": "<PERSON>a menerima pesan baru yang tidak didukung di versi saat ini, harap perbah<PERSON>.", "msgDigestUnknown": "<PERSON>a men<PERSON>ma pesan yang tidak didukung.", "msgDescActivity": "[Event]", "emptyNotice": "Belum ada notifikasi", "mightContainRisk": "Pesan ini mungkin berisi konten yang tidak bersahabat atau Beresiko", "dontMind": "Jika Anda tidak keberatan, ", "tapToView": "ketuk di sini untuk melihat pesan tersembunyi", "feltOffended": "<PERSON><PERSON>a te<PERSON>? ", "tapToReport": "Ketuk di sini untuk melaporkan", "receiveNewMsg": "<PERSON>a menerima pesan baru", "chatSettings": "Pengatura<PERSON>", "alias": "<PERSON><PERSON> lain", "pinChat": "Sematkan <PERSON>lan", "clearChatHistory": "Hapus riwayat Obrolan", "myResults": "<PERSON><PERSON> saya", "test": "<PERSON><PERSON>", "undone": "Belum se<PERSON>ai", "noContentHere": "Tidak ada konten disini", "setRemarks": "<PERSON><PERSON>", "remarks": "Keterangan", "deleted": "<PERSON><PERSON><PERSON>", "tapToSee": "Klik untuk melihat", "destructMsgHint": "<PERSON>kan lama layar\n<PERSON>ya dapat diperiksa sekali", "selfDelete": "<PERSON><PERSON> otomatis set<PERSON>h melihat", "previous": "Sebelumnya", "done": "Se<PERSON><PERSON>", "mediasOverSize": "Ukuran satu video tidak boleh melebihi 20 MB", "emptyComments": "Belum ada komentar", "pictureNotExist": "Gambar ini tidak ada", "userMatchingYourResult": "<PERSON><PERSON><PERSON> yang cocok dengan hasil <PERSON>a", "share": "Bagikan", "shared": "Sudah", "otherPopularTests": "<PERSON><PERSON> populer lainnya", "harassment": "Gangguan", "vulgarOrOffensive": "bahasa vulgar atau menynggung", "violence": "k<PERSON><PERSON><PERSON>", "stalking": "Menguntit", "sexualHarassment": "p<PERSON><PERSON><PERSON> seksual", "inappropriatePhotos": "foto yang tidak pantas", "spam": "Spam", "askingNumber": "pengguna ini sedang meminta nomor saya", "advertising": "pengguna ini sedang memberikan iklan", "suspiciousLink": "pengguna ini mengirimkan tautan yang mencurigakan kepada saya", "inappropriateName": "nama panggilan yang tidak pantas", "askedForMoney": "pengguna ini meminta uang kepada saya", "promotingOtherApps": "mempromos<PERSON><PERSON> aplik<PERSON> lain", "hateSpeech": "Bahasa kebencian", "politicsRelated": "terkait politik", "religionRelated": "terkait agama", "racism": "r<PERSON><PERSON>", "illegal": "Ilegal", "smokeDrugsDrinking": "merokok, narkoba, alkohol", "terrorism": "terorisme", "bloodyViolence": "k<PERSON><PERSON><PERSON> be<PERSON>", "other": "Lain-lain", "description": "<PERSON><PERSON><PERSON><PERSON>", "pleaseDescribe": "<PERSON><PERSON> jelaskan masalah yang anda alami...", "pleaseSelectReason": "<PERSON><PERSON> pilih alasan untuk laporan\nKami tidak akan memberi tahu pengguna ini", "thanksReachingOut": "<PERSON><PERSON> kasih telah men<PERSON>n pengalaman anda", "blockHint": "<PERSON><PERSON><PERSON> anda adalah prioritas utama kami. <PERSON><PERSON> akan menghandlenya secepat mungkin. Untuk menghindari gangguan lebih lanjut, anda juga dapat memblokir pengguna tersebut.", "blockUser": "Memblokir pengguna", "checkDetails": "Periksa detail", "shareToFriends": "Bagikan ke teman-teman", "momentNoExist": "<PERSON>ggahan ini telah dihapus", "sendingMoment": "Mengirim momen…", "postSuccess": "Mengunggahberhasil", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noFirstStep": "Anda belum mengambil langkah pertama", "willLoseMatch": "Anda akan kehilangan pasangan ini jika anda pergi sekarang.", "leaveChat": "Tinggalkan <PERSON>", "keepStaying": "tetap", "dontMiss": "<PERSON>an lewatkan satu pesan pun!", "quizShare": "Hai~ bestie! I<PERSON><PERSON> tes kepribadian sekarang untuk menemukan teman yang cocok dengan hasilmu !", "shareSuccess": "<PERSON><PERSON><PERSON><PERSON>", "shareFail": "<PERSON><PERSON>, silakan coba lagi", "sentButReject": "<PERSON><PERSON> ber<PERSON>il dikirim tetapi ditolak oleh penerima", "checkUpdate": "Cek pembaruan", "postMoment": "<PERSON>en berkualitas tinggi dapat memungkinkan lebih banyak balasan dari teman lain, <h>", "postNow": "tambahkan unggahan baru sekarang!", "turnOnToKnowMessages": "Aktifkan notifikasi untuk mengetahui pesan dan Match baru.", "notifyMe": "<PERSON><PERSON><PERSON> saya", "turnOnNotifications": "Aktifkan notifikasi untuk mengetahui pesan baru", "goToSetNotifications": "<PERSON><PERSON>-Winker-notifikasi-Izinkan notifikasi.", "allow": "Izinkan", "fakeUserMale": "<PERSON><PERSON>", "fakeUserFemale": "<PERSON>", "fakeMsgMale": "<PERSON>", "fakeMsgFemale": "<PERSON>", "enableForMsgMale": "Aktifkan notifikasi untuk menerima pesannya", "enableForMsgFemale": "Aktifkan notifikasi untuk menerima pesannya", "enableForMoment": "Aktifkan notifikasi untuk menerima momen dan komentar yang diperbarui", "greeting1": "<PERSON>", "greeting2": "Hai Halo~", "greeting3": "<PERSON><PERSON> berk<PERSON> den<PERSON>", "communityRules": "Aturan komunitas", "rulesIntro": "Tindakan berikut akan mengakibatkan akun Banned permanen.", "rules1": "❌Meminta informasi pribadi", "rules2": "❌Melecehkan atau Membulipengguna lain", "rules3": "❌ <PERSON><PERSON>ggah konten seksual", "rules4": "❌ Memposting Konten agama atau politik", "rules5": "❌Menggunakan bahasa yang kasar dan tidak pantas", "iUnderstand": "<PERSON><PERSON>", "destructHint": "<PERSON>gan memilih ini, foto atau video hanya dapat dilihat satu kali, dan akan otomatis terhapus sendiri setelahnya.", "sendFeedback": "<PERSON><PERSON> balik", "feedback": "<PERSON><PERSON> balik", "myFeedback": "<PERSON><PERSON> balik saya", "appProblems": "Ma<PERSON>ah aplikasi", "suggestion": "Saran", "others": "<PERSON><PERSON><PERSON>", "screenshot": "<PERSON><PERSON><PERSON> layar", "optional": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON>", "submitSuccess": "<PERSON><PERSON><PERSON><PERSON>", "failedToSubmit": "<PERSON><PERSON>, silakan coba lagi", "waitingReply": "<PERSON><PERSON><PERSON> balasan", "replied": "<PERSON><PERSON><PERSON>", "problemDesc": "<PERSON><PERSON><PERSON><PERSON> masalah", "processingRequest": "<PERSON><PERSON>g Memproses permintaan anda", "userGuide": "Panduan pengguna", "evaluateContent": "<PERSON><PERSON> anda men<PERSON> winker, to<PERSON> tinggalkan bintang 5, terima ka<PERSON>h~", "helpUsImprove": "Bantu kami men<PERSON>", "checkingUpgrade": "Sedang memeriksa versi terbaru, harap tunggu...", "latestVersion": "<PERSON><PERSON><PERSON><PERSON> anda saat ini adalah versi terbaru.", "versionOutOfDate": "Versi aplikasi Anda saat ini kedal<PERSON>, dapatkan versi terbaru untuk menikmati Fitur baru.", "newVersion": "<PERSON><PERSON><PERSON> :", "install": "unduh", "updateNow": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "youTookScreenshot": "<PERSON>a telah men<PERSON> layar <PERSON>", "userTookScreenshot": "{username} telah men<PERSON>p layar", "@userTookScreenshot": {"type": "string", "placeholders": {"username": {}}}, "msgDescSystem": "[Not<PERSON><PERSON><PERSON>]", "chatShouldStayPrivate": "<PERSON><PERSON>, ini tidak keren.\nobrolan harus tetap jadi rahasia", "sentMsgOnceTakeScreen": "<PERSON><PERSON><PERSON> pesan akan dikirim ke pengguna setelah anda menangkap layar <PERSON>lan.", "gotIt": "<PERSON><PERSON><PERSON>", "micAccessBanned": "<PERSON><PERSON><PERSON> tidak diaktifkan", "storageAccessBanned": "<PERSON><PERSON><PERSON>n tidak diaktifkan", "enableMicAccess": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>n\"> \"winker\" dan aktifkan akses mic.", "enableStorageAccess": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>n\" > \"winker\" dan aktif<PERSON> aks<PERSON>.", "goToSettings": "<PERSON><PERSON> ke setelan", "failedToSend": "Gagal <PERSON>giri<PERSON>", "yes": "Ya", "onlyPublishOneMoment": "Anda hanya dapat mengunggah momen satu per satu, yakin ingin mengirim momen baru?", "sexGuideText": "<PERSON><PERSON> kelamin tidak dapat diubah setelah dipilih.", "less": "<PERSON><PERSON><PERSON> sedikit", "matchGuide": "Klik untuk mulai mengobrol dengan teman baru", "beRestrictedFor": "<PERSON><PERSON><PERSON> anda telah dibatasi karena melanggar aturan komunitas.", "bePermanentlyRestricted": "<PERSON><PERSON><PERSON> anda telah <PERSON> secara permanen karena melanggar aturan komunitas", "iGotIt": "<PERSON><PERSON>", "msgTooShort": "<PERSON><PERSON> terlalu singkat", "resendMsg": "<PERSON><PERSON> ulang pesan ini?", "resend": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "day": "<PERSON>", "matchCountEmptyTitle": "<PERSON><PERSON><PERSON> k<PERSON>n anda hari ini telah digunakan", "matchCountEmptyDesc": "Silakan datang besok untuk bertemu teman baru", "discardPublishEdit": "<PERSON>ka anda mening<PERSON>kan halaman ini, anda akan kehilangan <PERSON> ini", "discardPost": "Hapus", "keepEditing": "<PERSON><PERSON>", "fillAccountInfo": "Silakan isi info akun", "selectYourAvatar": "<PERSON><PERSON><PERSON> pilih avatar nda", "tipPassions": "Ini adalah kesempatan sempurna untuk menun<PERSON>kkan lebih banyak tentang anda.", "selectLeastInterest": "<PERSON><PERSON>an pilih satu minat minimal", "enablePhotoAccess": "<PERSON><PERSON> i<PERSON>kan winker untuk mengakses foto perangkat anda di \"Pengaturan> Privasi> Foto", "editNickNameAlert": "Anda dapat mengedit nama panggilan setiap 24 jam, yakin ingin mengu<PERSON>ya?", "canNotEditName": "<PERSON><PERSON> dapat mengedit nama panggilan setiap 24 jam, silakan coba lagi nanti", "canNotEditAvatar": "<PERSON><PERSON> dapat mengedit nama panggilan setiap 24 jam, silakan coba lagi nanti", "blockSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "unblockSuccessfully": "<PERSON><PERSON> blo<PERSON><PERSON> be<PERSON>", "blockConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin memblokir pengguna ini?", "addPassionTip": "Tambahkan Tag minat Anda untuk menarik lebih banyak teman yang berpikiran sama", "saveSuccessfully": "<PERSON><PERSON><PERSON><PERSON> disimpan", "saveFailed": "<PERSON><PERSON> simpan", "about": "Tentang", "currentVersion": "<PERSON><PERSON><PERSON> te<PERSON>:", "copyright": "Hak Cipta © 2023 Hak cipta dilindungi undang-undang", "showYourCharm": "Lakukan langkah pertama untuk menun<PERSON>kkan pesona Anda!", "confirmToClear": "Konfirmasi untuk menghapus semua riwayat Obrolan?", "clear": "<PERSON><PERSON><PERSON><PERSON>", "cleared": "Dibersihkan", "inCallNotice": "<PERSON><PERSON><PERSON> sedang sibuk, silakan coba lagi nanti", "loading": "Sedang Memuat...", "refreshComplete": " se<PERSON><PERSON> ulang", "refreshFailed": "refresh gagal", "followGuide": "<PERSON><PERSON><PERSON> {username} untuk membuka lebih banyak Fitur!", "@followGuide": {"type": "text", "placeholders": {"username": {}}}, "beFollowedGuide": "<PERSON><PERSON><PERSON> {username} untuk membuka lebih banyak Fitur!", "@beFollowedGuide": {"type": "text", "placeholders": {"username": {}}}, "edit": "Sunting", "gif": "Gambar", "heicNotSupported": "Jenis File HEIC tidak didukung.", "loadFailed": "<PERSON><PERSON>", "original": "<PERSON><PERSON>", "preview": "<PERSON>rat<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "emptyList": "Kosongkan daftar", "unSupportedAssetType": "Jenis File HEIC tidak didukung.", "unableToAccessAll": "Tidak dapat mengakses semua File di perangkat", "viewingLimitedAssetsTip": "Hanya lihat File  dan album yang dapat diakses oleh aplikasi.", "changeAccessibleLimitedAssets": "Perbarui daftar File akses terbatas", "accessAllTip": "Aplikasi hanya dapat mengakses beberapa File di perangkat. Masuk ke pengaturan sistem dan izinkan aplikasi untuk mengakses semua file di perangkat.", "goToSystemSettings": "<PERSON>uka pengaturan sistem", "accessLimitedAssets": "Lanjutkan dengan akses terbatas", "accessiblePathName": "file yang dapat diakses", "shareContent": "Hey~ <PERSON><PERSON> lewatkan momen luar biasa ini.", "shareAppContent": "winker ~ <PERSON>gg<PERSON>n suara dan mengobrol dengan siapa pun dari seluruh dunia!", "friends": "<PERSON><PERSON>", "kickOut": "<PERSON><PERSON><PERSON>a telah masuk di perangkat lain.", "hourAgo": "{time,plural, =0{{time} hour}=1{{time} hour}=2{{time} hours}few{{time} hours}other{{time} hours}} sebelumnya", "@hourAgo": {"type": "text", "placeholders": {"time": {}}}, "minuteAgo": "{time,plural, =0{{time} min}=1{{time} min}=2{{time} mins}few{{time} mins}other{{time} mins}} sebelumnya", "@minuteAgo": {"type": "string", "placeholders": {"time": {}}}, "albumNotEnabledTitle": "Akses album tidak diaktifkan", "albumNotEnabledContent": "winker tidak dapat mengakses foto. Izinkan Winker untuk mengakses foto di setelan perangkat.", "goToMoments": "Buka momen untuk menemukan teman yang menarik!", "postMoreMoments": "memposting lebih banyak momen untuk mendapatkan lebih banyak perhatian!", "followSomeInterest": "<PERSON><PERSON><PERSON> beberapa teman menarik sekarang!", "go": "<PERSON><PERSON><PERSON><PERSON>", "postMoments": "memposting momen", "contacts": "Kontak", "search": "<PERSON><PERSON>", "chatHistory": "Riwayat Obrolan", "noResult": "Tidak ada hasil yang di<PERSON>ukan", "chats": "obrolan", "updatingNow": "<PERSON><PERSON><PERSON> se<PERSON>", "sendSomethingNew": "<PERSON><PERSON> sesuatu yang baru", "keepSending": "<PERSON><PERSON>", "completeAccount": "Lengkapi info akun anda untuk menarik lebih banyak teman satu frekuensi", "livingIn": "<PERSON><PERSON><PERSON> di", "aboutMe": "<PERSON><PERSON><PERSON> saya", "personalInfo": "Informasi pribadi", "starSign": "Zodiak", "completePercent": "{percent}% selesai", "@completePercent": {"type": "string", "placeholders": {"percent": {}}}, "region": "Wilayah", "selected": "<PERSON><PERSON><PERSON><PERSON>", "doNotBeShy": "<PERSON>an malu!", "writeABio": "<PERSON><PERSON> untuk memperkenalkan dirimu", "mute": "bisukan", "unmute": "<PERSON><PERSON>", "muteNotifications": "Notifikasi <PERSON>", "newMessages": "{amount} pesan baru", "@newMessages": {"type": "string", "placeholders": {"amount": {}}}, "stopEditProfile": "<PERSON><PERSON><PERSON><PERSON> mengedit profil anda?", "completeYourProfile": "<PERSON><PERSON>r selesai! Lengkapi profil anda untuk menemukan orang yang paling cocok untuk anda!", "notYet": "Lanjutkan", "viewingProfile": "Melihat profil", "characterTitle": "Tipe kepribadian Anda", "todayForCast": "<PERSON><PERSON><PERSON><PERSON> untuk hari ini", "todayBestMatch": "Pencocokan terbaik hari ini", "generalPrediction": "prediksi umum", "luck": "<PERSON><PERSON><PERSON><PERSON>", "money": "<PERSON><PERSON>", "mood": "<PERSON><PERSON><PERSON>", "energy": "Energi", "luckyNumber": "<PERSON><PERSON> keb<PERSON>an", "luckySign": "<PERSON><PERSON> keb<PERSON><PERSON>", "takeTheTest": "<PERSON><PERSON><PERSON>", "completeProfileBubble": "Lengkapi info akun anda untuk menarik lebih banyak teman satu frekuensi", "completeToFindFriends": "Lengkapi semua info akun akan membantu Anda menemukan teman.", "selectUpTen": "<PERSON>a dapat memilih hingga 10 Tag", "recentlyUsed": "<PERSON><PERSON><PERSON><PERSON> akhir-akhir ini", "allStickers": "<PERSON><PERSON><PERSON>", "viewProfile": "<PERSON><PERSON> profil", "resendCode": "<PERSON><PERSON><PERSON>", "resendCodeIn": "<PERSON><PERSON>", "welcomeToAlo": "Selamat datang di winker", "youBastExperience": "Temp<PERSON> anda, <PERSON><PERSON> anda, Pengalaman terbaik anda.", "invalidMobileNumber": "Nomor Ponsel tidak valid", "phoneNumber": "Nomor telepon", "signInWithPhoneNumber": "<PERSON><PERSON><PERSON> dengan ponsel", "mobileNumberConfirmation": "Konfirmasi nomor Ponsel", "enterSmsCodeSentTo": "Kirimkan kode verifikasi ke nomor berikut:", "moreWaysToLogIn": "Ma<PERSON>k dengan lebih banyak cara>", "blockedThisUser": "Anda telah memblokir pengguna ini", "blockedByThisUser": "<PERSON><PERSON> o<PERSON>h pen<PERSON>una ini", "selectCountry": "<PERSON><PERSON><PERSON> negara", "voiceTest": "<PERSON><PERSON>", "chooseAnswerTip": "<PERSON><PERSON><PERSON> jawaban yang paling sesuai dengan anda", "recordQuestions": "<PERSON><PERSON><PERSON> per<PERSON>aan saya", "recordYourQuestion": "<PERSON><PERSON><PERSON> anda", "recordQueTips": "<PERSON><PERSON><PERSON> per<PERSON>aan anda untuk membuka kun<PERSON>, lalu membuat <PERSON>, dan anda akan mendapatkan teman baru!", "pleaseRecord": "<PERSON><PERSON> tekan tombol rekam, lalu baca kata-kata di atas dengan lantang.", "clickToPlay": "Klik untuk memutar", "playing": "Sedang putar", "clickToRecord": "Klik untuk merekam", "resendCodeMax": "<PERSON><PERSON><PERSON> permintaan telah mencapai batas maksimal, coba lagi besok.", "speakTooFast": "<PERSON>, be<PERSON><PERSON><PERSON>~Minum kopi Dulu dan tunggu balasannya!", "notMatchForHer": "Dia bukan Matchmu hari ini, ayo coba mengobrol lagi besok!", "notMatchForHim": "Dia bukan Matchmu hari ini, ayo coba mengobrol lagi besok!", "voiceRecording": "<PERSON><PERSON><PERSON>", "pleaseChooseAnswer": "<PERSON><PERSON><PERSON> pilih jawaban anda", "editQuestions": "<PERSON>", "typeAMessage": "<PERSON><PERSON><PERSON> pesan", "makeFirstStarted": "Klik untuk memu<PERSON>", "change": "Ganti", "congrats": "Selamat!", "youMatchedWith": "<PERSON>a telah <PERSON> dengan ", "notMatchWith": "<PERSON><PERSON>, anda belum Match dengan ", "continueMatch": "Teruskan pencocokan", "goToChat": "Lanjut untuk obrol", "recordUnlockChat": "<PERSON><PERSON> kun<PERSON>", "pleaseTryAgain": "<PERSON><PERSON><PERSON> jaringan silakan coba lagi", "no": "Tidak", "askAQuestion": "<PERSON><PERSON><PERSON> agar anda berdua men<PERSON>", "changeQuestion": "Ganti <PERSON>", "yourQuestion": "Pertanyaan anda:", "typeAnswer": "<PERSON><PERSON><PERSON> jawaban anda{content}", "@typeAnswer": {"type": "string", "placeholders": {"content": {}}}, "illegalContent": "<PERSON><PERSON><PERSON> anda berisi <PERSON>, harap patuhi aturan komunitas.", "unlockHerAnswer": "<PERSON><PERSON><PERSON>aan untuk membuka kunci jawabannya", "unlockHisAnswer": "<PERSON><PERSON><PERSON>aan untuk membuka kunci jawabannya", "answerAreRevealed": "Jawaban<PERSON> akan terungkap ketika Anda semua menjawab", "playWithFriend": "<PERSON>lik dan <PERSON>lah dengan teman anda!", "playQuestionWithYou": "{nick} ingin bermain per<PERSON>aan dengan anda.", "@playQuestionWithYou": {"type": "string", "placeholders": {"nick": {}}}, "sendYouQuestion": "{nick} <PERSON><PERSON><PERSON>.", "@sendYouQuestion": {"type": "string", "placeholders": {"nick": {}}}, "askAnotherQuestion": "Ketuk untuk mengajukan pertanyaan lain", "countDownTimeTips": "Waktu pencocokan anda telah habis. Waktu pencocokan akan dipulihkan dengan hitungan mundur.", "matchedImTips": "Mencocokkan anda dari tes suara.", "msgDescQuestionBox": "[game pertanyaan]", "startQuestionGame": "{nick} sudah memulai <PERSON> pertanyaan.", "@startQuestionGame": {"type": "string", "placeholders": {"nick": {}}}, "exitRecordingTips": "<PERSON><PERSON> sekarang? Catatan audio anda tidak disimpan.", "yourVoiceLow": "<PERSON><PERSON> anda terlalu kecil, tolong bicara lebih keras.", "notVoiceInput": "Tidak ada suara <PERSON>, harap rekam per<PERSON>aan anda di tempat yang tenang.", "clearCache": "<PERSON><PERSON>", "clearSuccess": "cache dihapus", "calculating": "Menghitung", "tryVoiceMatching": "<PERSON><PERSON><PERSON> untuk tes suara, anda akan menemukan lebih banyak teman di winker", "matchedYouByVoice": "{nick} men<PERSON><PERSON><PERSON>n anda dari fungsi tes suara.", "@matchedYouByVoice": {"type": "string", "placeholders": {"nick": {}}}, "theVoiceMatchFindTips": "Temu<PERSON> teman yang satu frekuensi dengan anda dari tes suara.", "listenToTheVoiceQuestion": "Dengarkan pertanyaannya dan pilih jawaban anda", "cherishTheChance": "<PERSON><PERSON>i sebuah kesempatan untuk memilih dengan hati-hati", "sorryNotFoundVoiceMatch": "<PERSON><PERSON>, kami tidak dapat menemukan pengguna yang tepat untuk <PERSON> se<PERSON>, silakan coba lagi nanti.", "times": "kali", "myVoiceVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON> suara saya", "needToVerifyVoice": "winker melakukan yang terbaik untuk memastikan anda memiliki lingkungan sosial nyata yang sehat, jadi harap verifikasi suara anda sekarang agar kami dapat mengetahui hal menarik dari anda!", "yourVoiceVerificationFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> suara anda gagal, coba rekam lagi. ", "verifyVoiceBeforeMatching": "Anda belum <PERSON> suara anda. <PERSON><PERSON> verifikasi suara anda sekarang untuk membuka kunci fitur kami!", "verifyVoiceBeforeListening": "Anda belum <PERSON> suara anda. <PERSON><PERSON> verifikasi suara anda sebelum menden<PERSON>. ", "pleaseReadTheWords": "Baca teks Dibawah ini:", "goToVerify": "Klik untuk verifikasi", "voiceVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clickToRecordVoice": "Klik untuk merekam suara anda", "recordAtLeastSeconds": "<PERSON><PERSON><PERSON> set<PERSON> {second} detik", "verificationInReview": "V<PERSON>fi<PERSON><PERSON> anda sedang ditinjau", "verificationPassed": "Verifikasi lulus", "voiceSeconds": "{seconds}detik", "msgDescExpression": "Stiker", "micPermission": "\"winker\" ingin mengakses mic anda dan mengumpulkan data suara anda untuk mengaktifkan pesan suara, verifikasi identitas hanya saat aplikasi sedang digunakan.", "storagePermission": "\"winker\" ingin mengumpulkan data penyimpanan baca dan tulis untuk mengaktifkan pengiriman gambar dalam pesan hanya saat aplikasi sedang digunakan.", "searchSticker": "Klik ini! Anda dapat mencari Stiker apa pun yang anda inginkan.", "favoriteSticker": "fitur stiker telah kami tambahkan.\nAnda dapat menambahkan stiker ke favorit anda sekarang.", "manageSticker": "Lebih banyak stiker dapat dikelola dan ditambahkan di \"manajemen Koleksi\".", "noneFavorites": "Anda belum menambahkan Stiker apa pun ke favorit anda.", "add": "Tambahkan", "addedSuccess": "<PERSON><PERSON><PERSON><PERSON>", "addToFavorite": "Tambahkan ke favorit", "download": "unduh", "voiceDatingTimeUsedUp": "<PERSON><PERSON>tu hari ini telah habis, silakan coba lagi besok.", "voiceDatingNoPeople": "match terbaik anda tidak ada di sini sekarang, silakan coba lagi nanti.", "ooops": "ups..", "moveFront": "Pindah ke depan", "downloadSuccess": "<PERSON><PERSON><PERSON><PERSON>", "collections": "<PERSON><PERSON><PERSON><PERSON>", "myCollection": "<PERSON><PERSON><PERSON><PERSON> saya", "collectionDetail": "Detail kole<PERSON>i", "searchStickers": "<PERSON><PERSON>", "verifyProfile": "Verifikasi profil", "verifyFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "voiceVerifyMatchCount": "{count} kali tersisa hari ini", "waitForApproval": "<PERSON><PERSON> tunggu per<PERSON>.", "submitSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "iKnow": "<PERSON><PERSON>", "sayHiToYourMate": "Ucapkan hai ke teman anda!", "moreStickers": "Lebih banyak Stiker", "addToMyCollection": "Tambahkan ke koleksi saya", "firstSelectYourFavourite": "<PERSON><PERSON><PERSON>, pilih teks favorit anda dan bacalah dengan suara anda. <PERSON>a dapat mengubah kata-katanya.", "secondClickToRead": "Kedua, klik untuk membaca teks dengan suara anda. ", "swipeRightToLike": "SWIPE KE KANAN UNTUK SUKA", "swipeLeftToPass": "SWIPE KE KIRI UNTUK LEWATI", "playAndPause": "PUTAR DAN JEDA", "matchLikeEachOther": "Anda akan memiliki Match hanya jika anda berdua saling menyukai. Cobalah!", "passNoOneKnow": "<PERSON><PERSON> anda tida<PERSON>, <PERSON><PERSON> saja. Tidak ada yang akan tahu.", "justListenSwipeLike": "Dengarkan saja suara dari pengguna lain, geser ke kanan jika Anda Menyukainya.", "selectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "deleteNum": "Hapus ({num})", "@deleteNum": {"type": "string", "placeholders": {"num": {}}}, "deleteCollectionConfirm": "stiker tidak dapat dipulihkan setelah dihapus. <PERSON><PERSON> sekarang?", "noneMyCollections": "Anda tidak menambahkan koleksi apa pun.", "stickerLimit": "stiker mencapai batas", "answerQuestionGame": "{nick} sudah menjawab <PERSON> pertanyaan.", "@answerQuestionGame": {"type": "string", "placeholders": {"nick": {}}}, "callMatch": "Pencocokan panggilan", "voiceMatch": "Pencocokan suara", "noContent": "<PERSON><PERSON>k ada hasil, silakan coba kata kunci lain.", "personalityTest": "<PERSON><PERSON>", "quizGuideText": "<PERSON><PERSON>ti tes untuk menemukan pasangan ideal anda berdasarkan hasil anda", "testNow": "<PERSON><PERSON>", "greatStart": "<PERSON><PERSON> yang baik", "theyLikeYouBackUnlockChat": "<PERSON>ka mereka menyukai anda juga, anda dapat membuka kunci <PERSON>.", "notBeUsed": "Tidak dapat digunakan pada waktu yang sama", "recordVoiceGuidContent": "Klik untuk membaca kata-kata dengan suara anda", "deleteSureContent": "<PERSON><PERSON><PERSON><PERSON> anda yakin untuk menghapus?", "addTopic": "Tambahkan Topik", "searchHashtag": "<PERSON><PERSON> top<PERSON>", "noMoreTopic": "<PERSON><PERSON>, tidak ada <PERSON>n", "recommendTopic": "<PERSON><PERSON> yang <PERSON>", "cancelPickTopic": "<PERSON><PERSON><PERSON> dari rekaman akan dapat menghapus apa yang anda rekam", "contribute": "Kontribusi", "proposeTopic": "rekomendasikan topik anda kepada kami", "hottest": "<PERSON><PERSON> populer", "latest": "Terbaru", "hashTag": "tagar", "favouriteHashtag": "Klik di sini untuk memilih Tagar favorit anda! ", "postVoiceMoment": "<PERSON><PERSON> da<PERSON>t Memposting voice note dalam beberapa saat sekarang!", "signInRewards": "<PERSON><PERSON> ha<PERSON>", "signContent": "Masuk selama 7 hari untuk mendapatkan kejutan", "signIn": "<PERSON><PERSON><PERSON>", "getForFree": "Dapatkan secara gratis", "signedInToday": "<PERSON><PERSON><PERSON> hari ini", "signedInNumDay": "<PERSON>a telah masuk selama {num} hari.", "@signedInNumDay": {"type": "string", "placeholders": {"num": {}}}, "goldsCanSendGift": "Koin dapat digunakan untuk mengirim hadiah.", "giftsCanSendFriends": "<PERSON><PERSON> hadiah ke teman yang anda sukai.", "backpack": "<PERSON><PERSON><PERSON>", "gift": "<PERSON><PERSON>", "backpackEmpty": "<PERSON>nse<PERSON> anda kosong.", "earnGolds": "<PERSON><PERSON><PERSON> koin", "hours": "{hour} jam", "@hours": {"type": "string", "placeholders": {"hour": {}}}, "msgDescGift": "<PERSON><PERSON>", "quantityShortage": "<PERSON><PERSON><PERSON><PERSON> hadiah", "iSentYou": "<PERSON>a men<PERSON>m ke anda", "sentYouGift": "<PERSON><PERSON><PERSON> anda hadiah", "lackOfCoins": "<PERSON>rang koin! Ayo selesaikan lebih banyak tugas", "spendCoinsToSendGift": "<PERSON><PERSON><PERSON><PERSON> anda akan menggunakan {coin} koin untuk mengirim hadiah ini?", "@spendCoinsToSendGift": {"type": "string", "placeholders": {"coin": {}}}, "sendSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "usesOfGolds": "<PERSON><PERSON><PERSON><PERSON>", "wayToGetGolds": "Cara <PERSON>pa<PERSON>kan Ko<PERSON>", "goldsUses1": "1. <PERSON><PERSON> dapat digunakan untuk mengirim hadiah.", "goldsUses2": "2. <PERSON><PERSON> dapat digunakan untuk menambah waktu pencocokan.", "goldGet1": "1. <PERSON><PERSON> dapat dipero<PERSON>h dengan sign-in.", "goldGet2": "2. <PERSON><PERSON> dapat diperoleh dengan melakukan tugas dan pencapaian.", "myGolds": "<PERSON><PERSON> saya", "details": "Detail", "detail": "<PERSON><PERSON><PERSON>", "unlockChat": "<PERSON><PERSON> hadiah untuk membuka kunci obrolan", "sendAGift": "<PERSON><PERSON> hadiah untuk menu<PERSON>n ap<PERSON> anda.", "notNow": "Tidak se<PERSON>", "tryIt": "Cobanya", "makeGoodImpression": "<PERSON><PERSON> hadiah untuk membuat kesan yang baik.", "sendToChat": "<PERSON><PERSON> ke o<PERSON>lan", "charisma": "<PERSON><PERSON><PERSON>", "wealth": "Kontribusi", "receive": "<PERSON><PERSON><PERSON>", "assetData": "Data aset", "assetDataTitle": "Bagaimana cara mendapatkan kontribusi atau pesona?", "assetDataWealth": "Kontribusi: <PERSON><PERSON> dapat memperoleh poin kontribusi dengan men<PERSON>kan hadiah atau items kepada orang lain:\n1 emas = 1 poin\n1 diamond = 10 poin", "assetDataCharisma": "Pesona: <PERSON><PERSON> dapat memperoleh poin pesona saat menerima hadiah atau items dari pengguna lain:\n1 emas = 1 poin\n1 diamond = 10 poin\nJika anda mengiri<PERSON>kan hadiah kepada diri anda sendiri, anda hanya akan mendapatkan poin kontribusi, bukan poin pesona.", "giftUpperLimit": "<PERSON><PERSON> ini telah mencapai batas atas pengiriman hari ini", "youGetAGift": "Anda men<PERSON>ma hadiah.", "youGetTwoGifts": "Anda menerima dua hadiah.", "youGotNumGifts": "<PERSON><PERSON> <PERSON><PERSON><PERSON> {num,plural, =0{{num} gift}=1{{num} gift}=2{{num} gifts}few{{num} gifts}other{{num} gifts}}.", "@youGotNumGifts": {"type": "string", "placeholders": {"num": {}}}, "golds": "<PERSON><PERSON>", "signInFailed": "<PERSON><PERSON> masuk, harap coba lagi", "profile": "Profil", "dailyTasks": "<PERSON><PERSON>", "milestones": "Pencapaian", "get": "Dapatkan", "youHaveRewards": "Ketuk untuk mendapatkan hadiah", "tasks": "Tugas", "quizProvided": "<PERSON><PERSON> disediakan oleh penyedia konten pihak ketiga.", "signInBackpack": "<PERSON><PERSON> tugas telah dikirim ke ransel.", "youHaveGot": "<PERSON>a telah mendapa<PERSON>kan", "getMoreRewards": "Dapatkan lebih banyak hadiah", "giftSendGuid": "<PERSON>lik untuk mengirim hadiah untuk menu<PERSON>n pengh<PERSON>an <PERSON>!", "deleteAccount": "<PERSON><PERSON> akun", "areYouSureToDeleteAccount": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus akun Anda?", "letUsKnowTheReasonYouLeave": "<PERSON><PERSON> beri tahu kami alasan <PERSON>a pergi.", "iDontLikeWinker": "<PERSON><PERSON> tidak suka <PERSON>", "privacyProblem": "<PERSON><PERSON><PERSON> privasi", "somethingIsBroken": "<PERSON> yang rusak", "typeTourSuggestionsToUs": "Ke<PERSON>k saran Anda kepada kami...", "accountDataCantBeRecovered": "Data akun tidak dapat dipulihkan setelah dihapus.", "accountDeleted": "<PERSON><PERSON><PERSON>", "yourAccountWasDeleted": "<PERSON><PERSON><PERSON> anda telah <PERSON>.", "visibleToPublic": "Dapat dilihat oleh publik", "visibleToFriends": "<PERSON><PERSON><PERSON> dilihat oleh teman", "visibleToMyself": "<PERSON><PERSON><PERSON> dilihat oleh saya sendiri", "public": "Publik", "myself": "<PERSON><PERSON>", "private": "P<PERSON><PERSON><PERSON>", "noPermissionToListenVoice": "Anda tidak memiliki izin untuk mendengarkan verifikasi suara.", "permission": "Permit", "continueToDelete": "<PERSON><PERSON><PERSON><PERSON> untuk men<PERSON>", "requestTimeout": "<PERSON><PERSON><PERSON><PERSON> kehabisan waktu, harap coba lagi.", "okay": "<PERSON>e", "thisAccountHadDeleted": "Akun ini telah di<PERSON>pus", "thisUserHadBanned": "Pengguna ini telah diblokir", "clickToGetTheFree": "Kliknya untuk mendapatkan hadiah gratis.", "matchFilter": "<PERSON><PERSON><PERSON> jeni<PERSON>", "yourBalance": "<PERSON><PERSON>:", "all": "<PERSON><PERSON><PERSON>", "numGolds": "{num,plural, =0{{num} koin}=1{{num} koin}=2{{num} koin}few{{num} koin}other{{num} koin}}", "@numGolds": {"type": "string", "placeholders": {"num": {}}}, "timesFree": "{num,plural, =0{{num} time}=1{{num} time}=2{{num} times}few{{num} times}other{{num} times}} gratis", "@timesFree": {"type": "string", "placeholders": {"num": {}}}, "filterTimesToday": "Anda mencoba terlalu banyak hari ini, silakan coba lagi besok", "numGoldsCountTimes": "Habiskan {num,plural, =0{{num} koin}=1{{num} koin}=2{{num} koin}few{{num} koin}other{{num} koin}} untuk meningkatkan {count,plural, =0{{count} time}=1{{count} time}=2{{count} times}few{{count} times}other{{count} times}}.", "@numGoldsCountTimes": {"type": "string", "placeholders": {"num": {}, "count": {}}}, "unlockMore": "<PERSON>uka kunci lebih banyak", "tooFastAndTakeARest": "<PERSON><PERSON> terlalu cepat, tolong istirah<PERSON>...", "welcomeToCompleteSpace": "Selamat datang untuk melengkapi kamar anonim.", "youWillHaveANewId": "Anda akan memiliki identitas baru termasuk avatar dan nama panggilan.", "youCanThrowIntoSea": "Tidak ada yang akan mengenal <PERSON>, memendam apa pun yang ingin Anda katakan dan tulis ke dalam botol, dan membuangnya ke laut.", "throwTheBottle": "Lemparkan botol", "fishTheBottle": "Memancing botol", "myBottle": "<PERSON><PERSON><PERSON> saya", "bottleAvatarLabel": "Setel avatar untuk ruang anonim ini.", "bottleSettingDesc": "Surat rahasia adalah ruang yang sepenuhnya anonim. Avatar dan nama panggilan yang Anda atur hanya dapat ditampilkan di sini.", "bottleAvatar": "Avatar botol", "bottleNickname": "<PERSON>a panggilan botol", "notOriginal": "Konten ini tidak orisinil", "notOriginalHint": "<PERSON><PERSON> me<PERSON>, beri tahu kami pembuat as<PERSON>ya dengan bukti agar kami dapat memverifikasinya lebih cepat.", "reportSuccess": "<PERSON><PERSON><PERSON><PERSON>", "unCoverIdentityAlert": "Konfirmasi untuk mengirim undangan mengungkap profil <PERSON>?", "unCoverIdentityMsg": "Undang pihak lain untuk mengungkap profil <PERSON> ?", "unCoverIdentitySystemTip": "Ungka<PERSON> profil Winker satu sama lain?{str}", "@unCoverIdentitySystemTip": {"type": "string", "placeholders": {"str": {}}}, "accept": "<PERSON><PERSON><PERSON>", "accepted": "Disetuju<PERSON>", "ignored": "<PERSON><PERSON><PERSON><PERSON>", "ignore": "<PERSON><PERSON><PERSON><PERSON>", "yseWithExclamation": "Ya!", "msgDescUncoverIdentity": "[Ungkapkan profil <PERSON>]", "receivedBottleFrom": "<PERSON>a men<PERSON>ma botol dari ", "receivedBottle": "<PERSON>a men<PERSON>ma botol", "throwAway": "Menyingkirkan", "sent": "Terkirim", "selectTheTopic": "<PERSON><PERSON><PERSON>", "congratsNoExclamation": "Se<PERSON>at", "throwBottleInOcean": "<PERSON><PERSON> <PERSON>i, tulis apa pun yang ingin anda katakan ke dalam botol, k<PERSON><PERSON><PERSON> buang botol tersebut ke laut, mungkin seseorang akan memungutnya.", "pickBottleAndTalk": "<PERSON><PERSON> <PERSON>, ambil botolnya dan men<PERSON><PERSON><PERSON> dengan mere<PERSON>.", "clickChangeBottleAvatar": "Klik ini untuk mengubah avatar dan nama panggilan.", "youCantSendBottle": "Anda kehabisan waktu hari ini, silakan kembali besok!", "exitNoSaveInfo": "Informasi yang ada tidak akan disimpan jika keluar", "haveUncovered": "Anda berdua menem<PERSON>n profil <PERSON> anda dari surat rahasia.", "uncoverLimit": "Anda hanya dapat mengungkap sekali sehari.", "deleteUser": "Hapus <PERSON>", "hasClosedConversation": "<PERSON><PERSON> lain telah mengakhiri percakapan ini.", "deleteBottleConfirm": "<PERSON><PERSON><PERSON> menghapus obrolan ini, pihak terkait tidak akan lagi menerima pesan <PERSON>.", "triedTooMuchAndComeTomorrow": "Anda mencoba terlalu banyak hari ini, silakan coba lagi besok.", "whetherSpendGoldsToFish": "Anda kehabisan waktu bebas hari ini dan ingin menghabiskan {coins} untuk memancing sekali lagi?", "@whetherSpendGoldsToFish": {"type": "string", "placeholders": {"coins": {}}}, "xGolds": "{coins} koin", "@xGolds": {"type": "string", "placeholders": {"coins": {}}}, "fishFailedTryAgain": "Gagal memancing, coba lagi.", "sendBottleFirstTitle": "Untuk melan<PERSON>, kirim botol dulu hari ini.", "sendBottleFirstContent": "Anda belum mengirim botol hari ini, kiri<PERSON><PERSON> botol dulu.", "yourGoldReturnedBalance": "<PERSON><PERSON> memancing, koin <PERSON>a telah dikembalikan ke saldo anda.", "fishFailed": "Gagal memancing", "tryBottle": "Coba surat rahasia, anda dapat menemukan lebih banyak teman di <PERSON>", "iGetIt": "<PERSON><PERSON>", "uncoverIdentity": "Ungkapkan profil", "mysteryLetter": "Surat rahasia", "mysteryLetterImTitle": "Surat rahasia", "bottleFrom": "<PERSON><PERSON><PERSON> dari ", "realPersonVerified": "Orang asli diver<PERSON>", "receivedABottle": "Botol diterima", "shareYourMomentTitle": "Bagikan momen anda", "shareYourMomentContent": "<PERSON><PERSON> anda akan menambah lebih banyak suka dan teman.", "GoToPost": "<PERSON><PERSON> ke <PERSON>an", "shareHalama": "Bagikan Winker ke teman anda", "shareHaContent": "Bagikan kebahagiaan Winker dengan teman-teman anda.", "shareMomentTodayContent": "<PERSON><PERSON> anda akan menambah lebih banyak suka dan teman.", "guidePostMoment": "Klik di sini untuk memposting cerita hari ini kepada orang lain!", "safeMode": "Mode aman <PERSON>, informasi anda berada di bawah perlindungan kami.", "chatMode": "Mode obrolan", "nowMode": "SEKARANG", "freeMode": "Model bebas", "freeModeContent": "Konten sensitif tidak dapat dibatasi dalam mode bebas.", "safeModeContentToast": "Mode aman <PERSON>, juga dapat diubah ke mode bebas disini!", "safeModeContent": "Informasi anda dapat dilindungi dalam mode aman.", "youSentTheRequest": "<PERSON>a telah men<PERSON> per<PERSON>, harap tunggu per<PERSON>.", "safeModeName": "<PERSON> aman", "inviteToEnable": "Undang untuk mengaktifkan", "enabled": "Diaktifkan", "enable": "Aktifkan", "cancelRequest": "Batalkan permin<PERSON>", "waitingForOtherSideAccept": "<PERSON><PERSON><PERSON>", "invitationHasSent": "Undangan telah dikirim.", "switchMode": "Ingin beralih ke mode ini?", "sendChatModeRequest": "{name} mengirim permintaan untuk mengubah mode obrolan anda.", "@sendChatModeRequest": {"type": "string", "placeholders": {"name": {}}}, "safeModeDetailContent": "Dalam mode aman, kami akan melindungi privasi anda dan memastikan bahwa informasi pribadi anda tidak diperoleh oleh pengguna lain.", "freeModeDetailContent": "Dalam mode bebas, anda dapat mengekspresikan diri dengan bebas, tetapi pihak lain harus menerima undangan anda untuk membuka mode yang sama. ", "changeChatMode": "{name} mengubah mode obrolan", "@changeChatMode": {"type": "string", "placeholders": {"name": {}}}, "msgDescChatMode": "[Ubah mode obrolan]", "withdrawRequest": "<PERSON><PERSON> lawan mencabut permintaan ini.", "requestTooMuch": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON>a dapat mengirimkan dua undangan per hari kepada pengguna ini.", "youCanUnlockTips": "<PERSON>a dapat membuka kunci batasan dengan <h>", "changeChatModeTips": "ubah mode obrolan", "changeSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "mall": "Mal", "purchase": "Bel<PERSON>", "earnGold": "<PERSON><PERSON><PERSON> koin emas >", "avatarFrame": "Bingkai", "daysTimeout": "{num,plural, =0{{num} hari}=1{{num} hari}=2{{num} hari}few{{num} hari}other{{num} hari}}", "@daysTimeout": {"type": "string", "placeholders": {"num": {}}}, "hoursTimeout": "{num,plural, =0{{num} jam}=1{{num} jam}=2{{num} jam}few{{num} jam}other{{num} jam}}", "@hoursTimeout": {"type": "string", "placeholders": {"num": {}}}, "secondsTimeout": "{num,plural,=0{{num} detik}=1{{num} detik}=2{{num} detik}few{{num} detik}other{{num} detik}}", "@secondsTimeout": {"type": "string", "placeholders": {"num": {}}}, "chatFrame": "Gelembung", "coinsAndDay": "{coins} / {day,plural, =0{{day}hari}=1{{day}hari}=2{{day}hari}few{{day}hari}other{{day}hari}}", "@coinsAndDay": {"type": "string", "placeholders": {"day": {}, "coins": {}}}, "purchaseSuccessfully": "Pembelian ber<PERSON>il", "searchByUserId": "Cari berdasar ID pengguna", "searchByUserNickname": "<PERSON><PERSON> be<PERSON> nama", "wear": "<PERSON><PERSON>", "wearing": "Memakai", "successfully": "<PERSON><PERSON><PERSON><PERSON>", "wallet": "Dompet", "store": "<PERSON><PERSON>", "bag": "<PERSON><PERSON><PERSON>", "task": "Tugas", "niceToMeetYou": "Hai! <PERSON>ang berkenalan dengan anda!", "wouldYouLikeToShareVoice": "<PERSON><PERSON><PERSON><PERSON> anda ingin membagikan momen anda agar yang lain dapat mendengar suara anda?", "shareToMoment": "Bagikan ke momen", "titleVoiceVerifyGuide": "Bagaimana cara mendapatkan lebih banyak balasan?", "contentVoiceVerifyGuide": "Verifikas<PERSON> suara anda untuk memastikan bahwa anda adalah orang yang nyata.\nLolos validasi untuk prioritas pencocokan.", "ensuresSafety": "Winker memastikan bahwa semua pengguna berada di lingkungan yang aman dan otentik.", "getMoreInWinker": "Kamu bisa mendapatkan lebih banyak eksposur dan rekomendasi di <PERSON>.", "protectYourInfo": "<PERSON><PERSON> akan melindungi privasi dan informasi pribadi Anda.", "moreVerifiedUser": "Lebih banyak pengguna terverifikasi", "passingVerification": "<PERSON><PERSON> verifi<PERSON>i untuk mengobrol dengannya~", "VerifyUnlockMatch": "<PERSON><PERSON><PERSON><PERSON><PERSON> suara anda untuk membuka kunci fitur pencocokan!", "VerifyUnlockCreate": "<PERSON><PERSON><PERSON><PERSON><PERSON> suara anda untuk membuka kunci fitur ruangan!", "VerifyBeforeListening": "<PERSON><PERSON><PERSON><PERSON>i suara anda sebelum mendengarkan!", "verifyToUnlock": "Verifikasi untuk membuka kunci", "verificationToGet": "Verifikasi untuk mendapatkan", "verificationProtectYou": "Verifi<PERSON><PERSON> dapat melindungi anda", "discardNow": "<PERSON><PERSON> se<PERSON>?", "protectYourPrivacy": "<PERSON><PERSON>, kami pastikan akan melindungi privasi dan informasi anda.", "morePriority": "Selangkah lagi untuk mendapatkan prioritas lebih untuk mengobrol dengan orang lain.", "groupYourInfo": "Anda dapat memilih verifikasi audio tidak terlihat. Apakah anda yakin ingin keluar?", "passGetMatch": "Prioritas pencocokan dapat diperoleh setelah lolos verifikasi. <PERSON><PERSON><PERSON>h anda yakin untuk keluar ?", "continueText": "Lanjutkan", "exit": "<PERSON><PERSON><PERSON>", "thirdLoginCancel": "Info akun anda hanya akan digunakan untuk login. <PERSON><PERSON> akan memastikan semua pengguna dalam lingkungan aman dan dalam privasi.", "thirdLoginNetworkError": "<PERSON><PERSON><PERSON>, silakan coba lagi nanti.", "otherLoginWays": "<PERSON> masuk lain", "verificationCodeError": "<PERSON>de verifi<PERSON>i salah", "tryAnotherWay": "Ingin mencoba cara lain?", "agreeToTermAndPolicy": "<PERSON><PERSON><PERSON>gan <PERSON> dan <PERSON>", "agreeTo": "<PERSON><PERSON><PERSON> ", "and": " dan ", "pleaseReadAndAgree": "<PERSON><PERSON> baca dan setujui", "agreePrivacyContent": "Info anda hanya akan digunakan untuk membuat akun. Kami tidak akan membocorkan privasi pengguna.", "agreeAndContinue": "<PERSON><PERSON><PERSON> dan lan<PERSON>kan", "verifyProfileSecurely": "Verifi<PERSON><PERSON> profil dengan aman", "editAvatar": "Edit Avatar", "newText": "<PERSON><PERSON>", "changeAvatar": "Ganti Avatar", "photoToAvatar": "Foto ke Avatar", "avatarHistory": "Riwayat avatar", "film": "Film", "filmDesc": "<PERSON><PERSON><PERSON><PERSON> anda ingin kembali ke <PERSON>?", "cartoon": "<PERSON><PERSON><PERSON>", "cartoonDesc": "Menjadi superhero kartun dan seni digital dalam 5 detik!", "painting": "<PERSON><PERSON><PERSON>", "paintingDesc": "<PERSON><PERSON> selfie anda menjadi lukisan cantik!", "free": "<PERSON><PERSON><PERSON>", "fourteenCentury": "Abad ke-14", "nineteenCentury": "Abad ke-19", "twentyOneCentury": "Abad ke-21", "anime": "Anime", "baby": "Bay<PERSON>", "digitalArt": "Seni Digital", "natural": "Natural", "beauty": "Cantik", "fantasy": "Fantasi", "saveAvatar": "<PERSON>mpan avatar", "editPhoto": "Edit foto", "selectFilter": "<PERSON><PERSON><PERSON><PERSON> filter", "selectBackground": "<PERSON><PERSON><PERSON> latar belakang", "askShareToMoment": "<PERSON>pa anda ingin membagikannya ke momen？", "shareMomentPhotoToAvatar": "Saya mengubah foto saya ke avatar yang disediakan oleh <PERSON>!", "deeplinkPhotoToAvatar": "Foto untuk avatar di <PERSON>", "timesUsedUp": "Kehabisan waktu", "uploadTimeUsedUpDesc": "<PERSON><PERSON><PERSON> upload anda habis hari ini, kembali lagi besok!", "selectSinglePhoto": "<PERSON><PERSON>h satu foto", "uploadClearPhoto": "<PERSON><PERSON> unggah foto tubuh bagian atas yang jela<PERSON>, jika tidak, kon<PERSON>i akan gagal.", "faceNotDetected": "Wajah tidak terdeteksi. Coba lagi.", "picturesTips": "<PERSON><PERSON><PERSON>u banyak sentuhan pada gambar!", "completeProfile": "<PERSON>il <PERSON>", "completeProfileContentMatch": "Dapatkan lebih banyak prioritas dan kesempatan mengobrol dengan melengkapi profil anda.", "completeProfileContentGiftChat": "Buat orang lain membalas anda dengan lebih mudah dengan melengkapi profil anda!", "youHaveInterests": "Anda memiliki tiga minat yang sama!", "heLiving": "Dia tinggal di: tempat dekat anda!", "sheLiving": "Dia tinggal di: tempat dekat anda!", "weHaveTheInterests": "Kami memiliki minat yang sama!", "canWeGetKnow": "<PERSON><PERSON><PERSON>h kita berkena<PERSON>?", "avatarPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON> avatar", "history": "Riwayat", "setToAvatar": "<PERSON><PERSON> ke avatar", "deleteHistory": "Hapus riwayat？", "userContent": "Konten pen<PERSON>una", "reportUserTitle": "Laporkan Pengguna", "blockUserTitle": "Blokir Pengguna", "niceToMeetYouInBioGuide": "<PERSON>, senang berkenalan dengan<PERSON>!", "completedProfile": "<PERSON>a telah men<PERSON> profil", "completeYourProfileInChat": "<PERSON><PERSON><PERSON><PERSON> profil anda, dapatkan balasan dengan mudah!", "camera": "<PERSON><PERSON><PERSON>", "cameraPermission": "\"Winker\" ingin mengakses kamera anda dan mengumpulkan data foto anda untuk mengaktifkan pengunggahan foto, fungsi kamera hanya tersedia saat aplikasi sedang digunakan.", "cameraAccessBanned": "<PERSON><PERSON><PERSON> kamera tidak diaktifkan", "enableCameraAccess": "<PERSON><PERSON> \"Pengaturan\"> \"Win<PERSON>\" dan aktifkan akses kamera.", "completePercentInUserCard": "Profil selesai {percent}%", "@completePercentInUserCard": {"type": "string", "placeholders": {"percent": {}}}, "selectYourCountry": "<PERSON><PERSON><PERSON> negara anda", "maleShareGuideContent": "<PERSON><PERSON>kan persahabatan yang intim!", "femaleShareGuideContent": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang aman campur privasi!", "shareToYouFriends": "Bagikan ke teman anda", "bioCardTitle": "Selamat!\nAnda mendapat Kartu Identitas Winker！", "bioCardContent": "<PERSON><PERSON><PERSON><PERSON> Anda ingin berbagi momen agar orang lain lebih mengenal Anda", "idCardTitle": "Kartu Identitas Winker", "giveUpCard": "<PERSON><PERSON><PERSON><PERSON> anda ingin menyerah dengan kartu identitas anda?", "recreateAgain": "Anda dapat membuat ulang dengan melengkapi profil lagi.", "selectYourAppLan": "<PERSON><PERSON><PERSON> bahasa aplik<PERSON> anda", "multipleFace": "Beberapa wajah telah terdeteksi.\nPilih satu untuk melanjutkan.", "diamonds": "Diamond", "exchange": "<PERSON><PERSON>", "goStore": "<PERSON>gi ke toko", "myBalance": "<PERSON><PERSON>", "recharge": "<PERSON><PERSON>", "contactUs": "<PERSON><PERSON><PERSON><PERSON> kami", "diamondsBalance": "Saldo Diamond", "exchangeHint": "<PERSON><PERSON><PERSON>", "minExchangeDiamond": "Minimal 10 Berlian", "exchangeSuccessfully": "<PERSON><PERSON><PERSON>", "rechargeSucceeds": "<PERSON><PERSON><PERSON> ulang ber<PERSON>", "purchasingWait": "Sedang membeli... <PERSON><PERSON> tunggu", "insufficientDiamonds": "<PERSON><PERSON><PERSON> tidak men<PERSON>, isi ulang sekarang!", "roomTypeTips": "Tipe...", "keep": "Te<PERSON>", "enteredTheRoom": "<PERSON><PERSON><PERSON> ini", "atWho": "<PERSON><PERSON>", "@atWho": {"type": "string", "placeholders": {"name": {}}}, "roomId": "ID: {id}", "@roomId": {"type": "string", "placeholders": {"id": {}}}, "tag": "Tag", "announcement": "<PERSON><PERSON><PERSON>", "follower": "Pengikut", "country": "Negara", "theme": "<PERSON><PERSON>", "lock": "<PERSON><PERSON><PERSON>", "mode": "Mode", "roomMicPermission": "<PERSON><PERSON>", "blockLost": "Daftar Blokir", "password": "<PERSON><PERSON>", "@password": {"type": "string", "placeholders": {"pw": {}}}, "roomName": "<PERSON><PERSON>", "roomAnnounceWordCount": "Hitung/1000", "@roomAnnounceWordCount": {"type": "string", "placeholders": {"count": {}}}, "onlineUserCount": "Pengguna Online : {count}", "@onlineUserCount": {"type": "string", "placeholders": {"count": {}}}, "everyone": "<PERSON><PERSON>p orang", "invitedOnly": "<PERSON><PERSON> yang di<PERSON>ng", "editSuccessfully": "<PERSON><PERSON><PERSON><PERSON> diedit", "owner": "Pemilik", "sendGifts": "<PERSON><PERSON>", "admin": "Admin", "leave": "<PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "unlock": "Terkun<PERSON>", "roomKickOut": "<PERSON><PERSON><PERSON><PERSON>", "takeTheMic": "Ambil Mikrofon", "leaveTheMic": "Tinggalkan Mikrofon", "lockTheMic": "<PERSON><PERSON><PERSON>", "unlockTheMic": "<PERSON><PERSON>", "welcomeJoinRoom": " , selamat datang di kamar! <PERSON>an ragu untuk grab mikrofon dan mengikuti kami!", "allOnMic": "<PERSON><PERSON><PERSON> di Mikrofon", "allInRoom": "<PERSON><PERSON><PERSON> <PERSON>mar", "pleaseEnterRoomPw": "<PERSON>lahkan masukkan kata sandi dengan 4 digit", "roomMode": "<PERSON>", "invitation": "Undang mikrofon", "explore": "<PERSON><PERSON><PERSON><PERSON>", "managed": "Dikelola", "official": "<PERSON><PERSON><PERSON>", "music": "Mu<PERSON>", "sureUnlockRoom": "Yakin untuk membuka kunci kamar <PERSON>", "sure": "<PERSON><PERSON>", "game": "Gim", "create": "Buat", "chooseChatTopic": "<PERSON><PERSON><PERSON> topik obrolan", "createMyRoom": "<PERSON><PERSON><PERSON> ruangan saya", "random": "Acak", "combo": "Kombo", "sureKickOutRoom": "Yakin untuk mengeluarkan pengguna ini dari ruangan? dia tidak akan bisa masuk ke ruangan anda dalam 24 jam", "blockUserPermanently": "Blokir pengguna ini secara permanen", "inviteYouToTakeMic": "{who} mengundang anda untuk mengambil mic", "@inviteYouToTakeMic": {"type": "string", "placeholders": {"who": {}}}, "notice": "Pemberitahuan", "youHaveKickedOutRoom": "<PERSON>a telah di<PERSON>rkan dari ruangan ini", "youHaveKickedOutMic": "Anda mening<PERSON>kan mic sekarang. Datang lagi nanti!", "roomFunction": "<PERSON><PERSON><PERSON>", "myMusic": "<PERSON><PERSON>", "myLocalMusic": "<PERSON>sik <PERSON>al <PERSON>", "searchForSongs": "<PERSON><PERSON> lagu", "uploadMusicToYourPhone": "Tidak ada musik yang tersedia, unggah musik ke ponsel anda terlebih dahulu", "scanning": "Sedang memindai...", "totalMusic": "Total", "@totalMusic": {"type": "string", "placeholders": {"count": {}}}, "pauseMusicWhileTuringOffMic": "Menjeda musik saat mematikan mikrofon", "musicFileNotExist": "File musik tidak ada", "playError": "<PERSON><PERSON><PERSON> pemutaran", "exitTheRoom": "<PERSON><PERSON><PERSON> dari r<PERSON>an", "receivedGifts": "<PERSON><PERSON>", "highCaseTimes": "<PERSON><PERSON><PERSON>", "joined": "Bergabung", "mins": "menit", "recommendOtherRoom": "Merek<PERSON>ndas<PERSON>n ruangan lain untuk anda", "takeMicFirst": "<PERSON><PERSON><PERSON> ambil mic dulu", "confirmDeleteSong": "Konfirmasi untuk menghapus lagu ini?", "hostColsedTheRoom": "Host menutu<PERSON> ruangan", "messages": "<PERSON><PERSON>", "NoPeopleOnMic": "Tidak ada siapa pun di mic", "sendLowercase": "kirim", "giftNumUpLimit": "<PERSON><PERSON><PERSON><PERSON>{count}", "@giftNumUpLimit": {"type": "string", "placeholders": {"count": {}}}, "sendUser": "kirim", "balance": "<PERSON><PERSON>", "restore": "Pulihkan", "upgradeYourWinkerVersion": "Tingkatkan versi <PERSON> anda", "enterTheRoom": "<PERSON><PERSON><PERSON> kamar ini", "enterThePassword": "<PERSON><PERSON><PERSON><PERSON>", "noPeopleInRoom": "Tidak ada siapa pun di ruangan", "greaterThanCount": "<PERSON><PERSON><PERSON> atau Kontribusi harus lebih besar dari {count}", "@greaterThanCount": {"type": "string", "placeholders": {"count": {}}}, "unqualifiedUser": "Pengguna yang tidak memenuhi syarat", "createRoomGuideFirst": "<PERSON>a dapat mengubah nama dan cover ruangan anda kapan saja, pilih tag favorit anda!", "createRoomGuideSecond": "Undang teman anda untuk bergabung di pesta!", "replyToUnlockMore": "Balas percakapan untuk membuka kunci lebih banyak!", "voiceParty": "Pesta Suara", "inviteYouJoinRoom": "Mengundang anda bergabung di {name}", "@inviteYouJoinRoom": {"type": "string", "placeholders": {"name": {}}}, "room": "<PERSON><PERSON><PERSON>", "notification": "Notif<PERSON><PERSON>", "roomIsOpening": "<PERSON><PERSON><PERSON> {nick} yang anda ikuti dibuka!", "@roomIsOpening": {"type": "string", "placeholders": {"nick": {}}}, "userHavingParty": "{nick} sedang membuka pesta!", "@userHavingParty": {"type": "string", "placeholders": {"nick": {}}}, "searchByRoomId": "Cari menurut ID ruangan", "canNotEnterRoom": "<PERSON>a tidak dapat masuk ke ruang obrolan", "roomIsClosed": "<PERSON><PERSON>an saat ini tidak online, silakan kembali lagi nanti", "shareYourRoom": "Undang temanmu untuk mengobrol!", "youAreSetAdmin": "<PERSON><PERSON> diatur sebagai administrator", "youHadRevokedAdmin": "<PERSON>a telah menca<PERSON> admin", "noMoreRooms": "Tidak ada ruangan lain", "takeMicToJoinGame": "Ambil mic terlebih dahulu untuk bergabung dengan game", "gameBalance": "Saldo:", "entryFees": "Biaya masuk:", "liveRoomShareContentAndPwd": "Kami memiliki percakapan seru di sini! <PERSON><PERSON>k ke Winker untuk bergabung 「{name}」{roomId}! Kata sandi ruangan adalah {pwd}", "@liveRoomShareContentAndPwd": {"type": "string", "placeholders": {"name": {}, "roomId": {}, "pwd": {}}}, "liveRoomShareContent": "Kami memiliki percakapan seru di sini! Masuk ke Winker untuk bergabung 「{name}」{roomId}!", "@liveRoomShareContent": {"type": "string", "placeholders": {"name": {}, "roomId": {}}}, "checkDetailInfo": "Periksa info detail", "gameRoom": "Ruang game", "chatRoom": "<PERSON><PERSON> o<PERSON>", "pleaseExitRoom": "<PERSON><PERSON> keluar dari ruangan dulu", "matching": "Mencocokkan", "matchingPeoplePlayWith": "Mencocokkan orang untuk bermain dengan", "editWithoutPermission": "<PERSON><PERSON><PERSON> tanpa izin", "roomHasBanned": "<PERSON><PERSON><PERSON> te<PERSON> di <PERSON>", "followThisRoom": "<PERSON><PERSON><PERSON> r<PERSON> ini", "ready": "Siap", "playerPosition": "Pemain {pos}", "@playerPosition": {"type": "string", "placeholders": {"pos": {}}}, "msgNew": "<PERSON><PERSON>", "gameOver": "<PERSON><PERSON><PERSON><PERSON>", "playAgain": "Main lagi", "gameCharge": "<PERSON>i komisi 10%", "roomLostConnect": "<PERSON>uangan ini terputus", "inputCannotBeNull": "Input tidak boleh kosong", "playerNotReady": "<PERSON><PERSON><PERSON> tidak siap atau tidak cukup", "gameError": "Kesalahan game, kode salah:{code}", "@gameError": {"type": "string", "placeholders": {"code": {}}}, "callInRoomError": "Panggilan tidak didukung di dalam ruangan", "matchInRoomError": "Pencocokan game tidak didukung di dalam kamar", "reload": "<PERSON><PERSON> ul<PERSON>", "exitRoomWillLeaveGame": "<PERSON><PERSON><PERSON> dari ruangan akan mening<PERSON> game", "exitRoomWillCauseGameFailed": "<PERSON><PERSON><PERSON> dari ruangan akan menyebabkan game gagal", "areYourSureExit": "<PERSON><PERSON><PERSON><PERSON> anda yakin untuk keluar?", "notAllowedDuringGame": "Tidak diperbolehkan selama game ini", "cannotSwitchMode": "Anda tidak dapat beralih mode selama game ini.", "notAllowedKickGamerDuringGame": "Tidak diperbolehkan untuk mengeluarkan gamer selama game", "gameStart": "<PERSON><PERSON> game", "gameOverLowerCase": "<PERSON><PERSON><PERSON>", "gameOverAndWinner": "Game telah berakhir! <PERSON><PERSON><PERSON><PERSON><PERSON> adalah {name}", "@gameOverAndWinner": {"type": "string", "placeholders": {"name": {}}}, "tapToOpenMic": "Ketuk untuk membuka mic", "waitSent": "<PERSON><PERSON>gu beberapa saat untuk mengirim.", "goSetting": "<PERSON>gi ke pengaturan", "networkError": "Kegagalan koneksi jaringan", "recently": "<PERSON><PERSON><PERSON>", "inviteYourFriends": "Undang teman anda", "exitRoomTitle": "<PERSON><PERSON><PERSON><PERSON> anda yakin keluar dari ruangan ini?", "exitRoomContent": "<PERSON>i akan menyeba<PERSON>kan ruangan ditutup.", "switchMic": "<PERSON><PERSON><PERSON><PERSON> Anda untuk mengubah posisi mikrofon?", "followRoomContent": "<PERSON><PERSON><PERSON> r<PERSON>an untuk menerima berita pesta!", "followUserGuideContent": "<PERSON><PERSON><PERSON>, be<PERSON><PERSON><PERSON><PERSON> lain kali!", "followAll": "<PERSON><PERSON><PERSON>", "clickTheMicPosition": "Klik posisi mic untuk mengobrol!", "inChatRoom": "<PERSON> ruang obrolan {name}", "@inChatRoom": {"type": "string", "placeholders": {"name": {}}}, "setSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "upMicGuideContent": "Bergabung dalam Percakapan!", "welcomeToVoiceParty": "Selamat datang di pesta Suara", "newHandRewardContent": "<PERSON><PERSON><PERSON> hadiah ucapan untuk anda", "porn": "Porno", "userNotInRoom": "Pengguna tidak ada di dalam ruangan.", "live": "Live", "blockRoomConfirm": "<PERSON><PERSON><PERSON><PERSON> anda yakin ingin memblokir ruangan ini?", "getIt": "<PERSON><PERSON><PERSON>", "matchSwitch": "Ganti match", "matchSwitchContent": "Tidak akan mencocokkan orang untuk anda saat ditutup.", "open": "<PERSON><PERSON>", "inviteJoinRoom": "Undang untuk ngobrol!", "invitePlayLudo": "Undang untuk bermain <PERSON>", "join": "<PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON><PERSON>", "deleteCommentSure": "Komentar yang dihapus tidak dapat dipulihkan", "user": "Pengguna", "followed": "<PERSON><PERSON> mengi<PERSON>", "agoraReconnect": "<PERSON><PERSON><PERSON><PERSON> kembali <PERSON>, <PERSON><PERSON> tunggu…", "switchRoomMode": "Beralih mode ruangan", "switchRoomModeContent": "<PERSON><PERSON><PERSON><PERSON> anda yakin mengubah mode ruangan?", "micApplyHasSent": "<PERSON><PERSON>hon<PERSON> mengambil mic telah di<PERSON>rim", "approvedMic": "Disetujui untuk mengambil mic", "agree": "<PERSON><PERSON><PERSON>", "agreed": "Disetuju<PERSON>", "userApplyTakeMic": "mengajukan naik mic", "applyMicList": "Mendaftar untuk daftar mic", "emptyTheList": "Kosongkan daftar", "applyTakeMic": "Ajukan permohonan untuk mengambil mic", "takeMic": "Ambil <PERSON>", "level": "Level", "title": "Titel", "ranking": "<PERSON><PERSON><PERSON>", "today": "<PERSON> ini", "weekly": "Mingguan", "giftsReceived": "<PERSON><PERSON><PERSON>", "giftsSent": "Mengirim", "roomGifts": "<PERSON><PERSON>", "daily": " <PERSON><PERSON>", "monthly": "Bulanan", "reward": "<PERSON><PERSON>", "receivedRankExplain": "<PERSON><PERSON><PERSON> berdasar pada nilai diamond dari hadiah yang anda terima", "sentRankExplain": "<PERSON><PERSON><PERSON> berdasar pada nilai diamond dari hadiah yang anda kirim", "roomRankExplain": "<PERSON><PERSON><PERSON> berdasar pada nilai diamond dari hadiah ruangan yang anda terima", "diamondRank": "Peringkat diamond", "diamondRankExplain": "<PERSON><PERSON><PERSON> didasarkan pada nilai diamond dari hadiah yang anda kirim di ruangan ini.", "drawRewards": "Mengundi untuk memenangkan hadiah besar", "draw": "Gambar", "haveDrawDays": "<PERSON><PERSON> telah mengundi untuk {num,plural, =0{{num} day}=1{{num} day}=2{{num} days}few{{num} days}other{{num} days}} terus-menerus.", "@haveDrawDays": {"type": "string", "placeholders": {"num": {}}}, "userLevel": "Level Pengguna", "levelMedal": "Medali Level", "levelUpRewards": "<PERSON><PERSON>", "waysToLevelUp": "<PERSON>", "benefitsHighLevel": "Manfaat Level Tinggi", "benefitsLevelContent1": "1. <PERSON><PERSON> akan menda<PERSON> hadiah saat level naik ke level khusus.", "benefitsLevelContent2": "2. Level yang lebih tinggi akan membantu anda mendapatkan lebih banyak perhatian.", "levelIsIncreasing": "Level anda sedang meningkat dengan kecepatan standar.", "exp": "Exp", "@exp": {"type": "string", "placeholders": {"num": {}}}, "todayUpperLimit": "Batas atas hari ini: ", "hot": "Populer", "related": "Terkait", "createMyRoomUpCase": "<PERSON><PERSON><PERSON>", "closed": "Ditutup", "recentlyRoomEmpty": "Anda belum bergabung dengan ruangan mana pun.", "goFindRooms": "<PERSON><PERSON><PERSON>", "ludoMatch": "Pencocokan Ludo", "bonus": "Bonus", "levelUpgradeTitle": "Selamat! Level anda naik ke", "wearTitle": "Pakai Titel", "activity": "Event", "currentTitle": "Titel yang dipakai saat ini", "empty": "Kosong", "unknownError": "<PERSON><PERSON><PERSON> yang belum diketahui", "rewardsHasSent": "<PERSON><PERSON> telah di<PERSON> kepada anda.", "plagiarism": "Plagiat", "reportDescribe": "Deskripsikan masalah yang sedang anda alami...", "attachment": "Lam<PERSON>ran", "reportAttachmentContent": "Opsional, ukuran video tidak melebihi 20M", "pleaseDescribeReport": "<PERSON><PERSON> <PERSON><PERSON><PERSON>an masalah yang ingin anda laporkan.", "cancelPin": "Batalkan pin", "congratsYouGot": "Selamat! Anda telah dapat", "rewardsSentYourBackpack": "<PERSON><PERSON> telah di<PERSON> ke ransel anda.", "titleWearInstructions": "Instruksi pema<PERSON>an titel", "titleWearDesc": "Anda dapat memakai titel yang diperoleh di Winker. Titel berasal dari aktivitas dan pencapaian yang anda selesaikan di Winker. Titel yang anda pakai akan ditampilkan di profil dan profil ruangan anda. Anda dapat memakai hingga tiga titel.", "personalInformation": "Halaman informasi pribadi", "userProfileCard": "Halaman informasi pribadi di ruangan", "chatInTheRoom": "Ngob<PERSON> di <PERSON>uang<PERSON>", "ludoGame": "Game Ludo", "followedRoomEmpty": "Anda belum mengikuti ruangan mana pun.", "thankForSubmit": "<PERSON><PERSON> kasih atas submit anda", "formIdEmpty": "ID formulir kosong", "updateTime": "Waktu pembaruan: {time}({timeZone}{timeOffset})", "@updateTime": {"type": "string", "placeholders": {"time": {}, "timeZone": {}, "timeOffset": {}}}, "enterEffect": "<PERSON><PERSON><PERSON> ma<PERSON>k", "roomTheme": "<PERSON><PERSON>", "sendToFriend": "<PERSON><PERSON> ke teman", "userHasItem": "Pengguna ini telah membeli item tersebut", "unqualified": "Tidak memenuhi syarat", "upgradeRoomLevelTip": "Tingkatkan ruangan anda ke level {num} untuk mengubah setelan", "@upgradeRoomLevelTip": {"type": "string", "placeholders": {"num": {}}}, "numDiamonds": "{num,plural, =0{{num} berlian}=1{{num} berlian}=2{{num} berlian}few{{num} berlian}other{{num} berlian}}", "@numDiamonds": {"type": "string", "placeholders": {"num": {}}}, "roomLevel": "Level ruangan", "roomLeveUpperLimit": "Batas atas harian <PERSON>p ruang<PERSON>: ", "changedInviteOnly": "Pemilik dapat mengubah izin mic hanya penerima undangan", "roomAdminsTitle": "<PERSON><PERSON>", "roomAdmins": "{num} admin", "@roomAdmins": {"type": "string", "placeholders": {"num": {}}}, "coins": "<PERSON><PERSON>", "roomLevelUpgrade": "Selamat! Ruangan di-upgrade ke LV.{num}", "@roomLevelUpgrade": {"type": "string", "placeholders": {"num": {}}}, "whoEnterTheRoom": "masuki r<PERSON> ini", "checkInBackpack": "Cek in ransel", "sendGoodsByUser": "{user} mengirimkan {goods} {name} kepada anda.", "@sendGoodsByUser": {"type": "string", "placeholders": {"user": {}, "goods": {}, "name": {}}}, "my": "<PERSON><PERSON>", "goodsHasExpired": "Item ini telah kedalu<PERSON>sa", "pleaseSelectUser": "Silahkan pilih pengguna yang ingin dikirim", "alreadyInThisRoom": "Sudah di ruangan ini", "recommend": "Rekomendasikan", "youHasItem": "Anda telah membeli item tersebut", "tooFrequentlyTip": "Permin<PERSON><PERSON> terlalu sering, coba lagi nanti", "micIsLocked": "Posisi mic terkunci", "passwordWrong": "Kata sandi salah", "unfollowRoomWillRemoveAdminIdentity": "<PERSON><PERSON><PERSON><PERSON> mengikuti ruangan akan menghapus identitas admin anda", "drawForFree": "Undian\ngratis", "onlyAdminCanPlayMusic": "<PERSON><PERSON> pemilik dan admin yang dapat memutar musik", "newFans": "<PERSON><PERSON><PERSON><PERSON> baru", "diamondsGift": "<PERSON><PERSON> diamond", "sendGiftToOwner": "<PERSON><PERSON> hadiah pendatang baru gratis kepada pemilik", "sendGiftToAdmin": "<PERSON><PERSON> hadiah pendatang baru gratis kepada admin", "ludo": "<PERSON><PERSON>", "uno": "Uno", "knifeChallenge": "Game pisau", "dominoes": "Domino", "playCenter": "<PERSON><PERSON><PERSON> berma<PERSON>", "invitePlayKnife": "Undang ke tantangan pisau", "invitePlayDomino": "Undang untuk bermain <PERSON>", "youGotFriendsInHala": "<PERSON><PERSON> punya {count} te<PERSON> di <PERSON>, tolong tinggalkan bintang 5~", "@youGotFriendsInHala": {"type": "string", "placeholders": {"count": {}}}, "youAlreadyGotConversations": "<PERSON>a sudah mendapatkan {count} per<PERSON><PERSON><PERSON> <PERSON>, tolong tinggalkan bintang 5", "@youAlreadyGotConversations": {"type": "string", "placeholders": {"count": {}}}, "forSomeoneToMatchYou": "Seseorang match dengan anda da<PERSON> 3 detik, tolong tinggalkan bintang 5!", "youHaveFollowersAndFriends": "<PERSON>a memilik<PERSON> {followers} pengikut dan{friends} teman di Winker yang sedang menunggu anda", "@youHaveFollowersAndFriends": {"type": "string", "placeholders": {"followers": {}, "friends": {}}}, "youGetMatchEverySeconds": "Anda mendapatkan match setiap tiga detik…", "transfer": "Mengirim", "round": "<PERSON><PERSON> ", "ofToday": " hari ini", "gameSelectTips": "Pilih kuantitas diamond > Pilih item", "selectTime": "<PERSON><PERSON><PERSON>", "drawing": "Mengundi", "todayWinnings": "<PERSON><PERSON><PERSON><PERSON>", "results": "Hasil:", "gameRecords": "Catatan Partisipasi", "howToPlay": "<PERSON> bermain", "turntableGameWin": "Selamat! Anda memen<PERSON> {num} diamond di ronde ini.", "@turntableGameWin": {"type": "string", "placeholders": {"num": {}}}, "turntableGameFail": "<PERSON><PERSON>, anda tidak menang di ronde ini.", "turntableGameNoJoin": "Anda tidak berpartisipasi dalam ronde ini.", "biggestWinners": "Pemenang terbesar ronde ini", "sureSpendDiamonds": "Yakin ingin mengh<PERSON>n {num} untuk item ini?", "@sureSpendDiamonds": {"type": "string", "placeholders": {"num": {}}}, "nextNoShow": "<PERSON><PERSON> tamp<PERSON>kan lain kali", "bonusPackage": "Paket bonus", "gotRechargeRewards": "Anda mendapat hadiah isi ulang!", "getBigBonus": "dapatkan nilai bonus besar 1000 diamond", "luckyWheel": "<PERSON><PERSON>", "start": "<PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON>", "rules": "<PERSON><PERSON><PERSON>", "entryFee": "Biaya <PERSON>:", "joinTheLuckyWheel": "Gabung di Roda Keberuntungan", "out": "KELUAR", "moreThan3ParticipantsToStartLuckyWheel": "<PERSON><PERSON><PERSON><PERSON> minimal {count} peserta untuk memulai <PERSON>unt<PERSON>. <PERSON><PERSON><PERSON> dimula<PERSON>, orang lain tidak dapat bergabung.", "@moreThan3ParticipantsToStartLuckyWheel": {"type": "string", "placeholders": {"count": {}}}, "luckyWheelRules": "1. <PERSON>ya pemilik dan admin ruangan yang dapat mengaktifkan Roda Keberuntungan dan menetapkan biaya masuk.\n\n2. <PERSON>elah roda keberuntungan dimulai, setidaknya 3 orang harus mulai spin dalam waktu 5 menit.\n\n3. <PERSON><PERSON> ada 3 peserta atau lebih dalam waktu 5 menit, admin ruangan dapat spin roda keberuntungan secara manual, atau roda keberuntungan akan mulai spin secara otomatis setelah 5 menit dimulai. Jika peserta kurang dari 3 orang dalam waktu 5 menit, roda akan ditutup secara otomatis dan biaya masuk akan dikembalikan.\n\n4. Saat roda keberuntungan mulai spin, peserta yang tersingkir akan dapat dipilih secara acak hingga hanya tersisa satu peserta. Peserta tersebut memenangkan 90% dari total biaya masuk.", "sureToCloseLuckyWheel": "Yakin menutup <PERSON>? Biaya masuk akan dikembalikan kepada peserta", "luckyWheelIsEndAndReturnYourBalance": "<PERSON><PERSON> be<PERSON>. Diamond telah dikembalikan ke saldo anda. ", "luckyWheelIsAutoEnd": "<PERSON><PERSON>beruntungan belum juga dimulai, event ini ditutup. Diamond telah dikembalikan ke saldo anda.", "sureToJoinLuckyWheel": "Ya<PERSON><PERSON>h ingin mengh<PERSON>n {diamonds} diamond untuk bergabung dengan roda <PERSON>. Pemenang akan menerima 90% dari kumpulan hadiah.", "@sureToJoinLuckyWheel": {"type": "string", "placeholders": {"diamonds": {}}}, "doNotRemindMeNextTime": "<PERSON><PERSON> ingatkan saya lain kali", "iStartedLuckyWheel": "<PERSON><PERSON>.", "joinNow": "<PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "luckyWheelIsEnd": "<PERSON><PERSON>an telah be<PERSON>.", "luckyWheelWinner": "{name} adalah pemenang yang beruntung dalam event <PERSON><PERSON> dan memenangkan {count} diamond!", "@luckyWheelWinner": {"type": "string", "placeholders": {"name": {}, "count": {}}}, "superBenefits": "Keuntungan Super", "countriesRegions": "Negara/Wilayah", "participationRecord": "Catatan Partisipasi", "winningFruit": "Memenangkan Buah: {name}", "@winningFruit": {"type": "string", "placeholders": {"name": {}}}, "selectedFruits": "<PERSON><PERSON><PERSON>:", "correct": "Betul", "wrong": "<PERSON><PERSON>", "topWinnersOfToday": "Pemenang teratas hari ini", "turntableGameRules1": "1. <PERSON><PERSON><PERSON> j<PERSON> berl<PERSON>, lalu pilih buah untuk menghabi<PERSON>n berlian.", "turntableGameRules2": "2. <PERSON><PERSON> dapat memilih hingga 8 buah di setiap ronde. Tidak ada batas atas jumlah berlian yang dapat Anda habiskan.", "turntableGameRules3": "3. <PERSON><PERSON><PERSON> set<PERSON> ronde, <PERSON><PERSON> memiliki waktu 40 detik untuk memilih buah, dan mengundi buah yang menang set<PERSON>.", "turntableGameRules4": "4. <PERSON><PERSON> <PERSON><PERSON>n berlian pada buah yang menang, <PERSON><PERSON> akan memen<PERSON> hadiah yang se<PERSON>ai.", "turntableGameRules5": "5. <PERSON>k interpretasi terakhir dari game ini adalah milik <PERSON>.", "numRound": "Ronde: {num}", "@numRound": {"type": "string", "placeholders": {"num": {}}}, "worldwide": "Global", "firstRechargeTitle": "<PERSON><PERSON>", "supporter": "Pendukung", "enter": "<PERSON><PERSON><PERSON>", "eventWithEmoji": "🎉 Acara", "createEventForYourRoom": "<PERSON><PERSON>t acara khusus untuk kamar <PERSON>", "events": "<PERSON>car<PERSON>", "createMyEvent": "<PERSON><PERSON>t acara saya", "createEvent": "Buat acara", "roomEventMaximum": "Ruang ini telah mencapai event maksimum", "eventDetails": "Detail acara", "eventName": "Nama event", "eventDescTips": "Informasi selengkapnya agar tamu mengetahui detail acara Anda.", "selectRoom": "<PERSON><PERSON><PERSON> kamar", "youCanCreateEventTips": "<PERSON>a dapat membuat acara untuk kamar manajemen.", "eventTag": "Tag acara", "eventTime": "<PERSON><PERSON><PERSON> a<PERSON>a", "cancelEventCreateTitle": "<PERSON><PERSON><PERSON> tanpa men<PERSON>?", "cancelEventCreateContent": "<PERSON><PERSON> <PERSON>, acara Anda tidak akan dibuat dan progres Anda tidak akan disimpan.", "startDateAndTime": "<PERSON><PERSON> dan waktu mulai", "endDateAndTime": "<PERSON><PERSON> dan waktu berakhir", "reviewEvent": "<PERSON><PERSON><PERSON><PERSON>", "addCoverPhoto": "Tambahkan foto sampul", "createByNumDiamonds": "Buat({num} <PERSON><PERSON><PERSON>)", "@createByNumDiamonds": {"type": "string", "placeholders": {"num": {}}}, "eventInReview": "Acara sedang ditinjau", "betterLuckNextTime": "<PERSON><PERSON><PERSON> beruntung lain kali", "congratulations": "Selamat!", "powerBar": "Batang pen<PERSON>ak", "rewards": "<PERSON><PERSON>", "giftAwardResetTime": "Reset harian pada 00:00 (GMT+3)", "treasureBoxRoomLevelLimit": "<PERSON>ya kamar yang levelnya mencapai LV.{level} yang memiliki kotak harta karun", "@treasureBoxRoomLevelLimit": {"type": "string", "placeholders": {"level": {}}}, "giftAwardRules": "1. <PERSON><PERSON> kamar yang mencapai level 5 yang memiliki kotak harta karun.\n2. Kunci kotak dapat diperoleh dengan mengirimkan hadiah berlian di kamar.\n3. Kotak harta karun akan terbuka saat bilah progres tombol kotak terisi.\n4. Setiap orang di kamar dapat memiliki kesempatan untuk memperoleh hadiah di kotak harta karun.\n5. <PERSON><PERSON><PERSON> membuka kotak, beberapa detik mungkin diperlukan agar hadiah dapat dikirim ke akun Anda. <PERSON><PERSON>ian akan ditambahkan ke saldo <PERSON>, bingkai avatar, dan tema kamar akan ditambahkan ke inventaris Anda.\n6. Semakin tinggi level kotak harta karun, semakin besar hadiah di dalamnya.\n7. Bilah progres tombol kotak akan disetel ulang pada pukul 00:00 (GMT+3) setiap hari.", "gotFromTreasureBox": "{name} mendapat<PERSON> {award} {count} <PERSON><PERSON>", "@gotFromTreasureBox": {"type": "string", "placeholders": {"name": {}, "award": {}, "count": {}}}, "liveNow": "Live Sekarang", "selectYourRoom": "<PERSON><PERSON><PERSON>", "myRoom": "<PERSON><PERSON> saya", "adminRoom": "<PERSON><PERSON> admin", "tomorrow": "Besok", "cancelEvent": "Batalkan Acara", "cancelEventConfirm": "Pembatalan acara akan menyebabkan penghapusan pelanggan dan Anda harus membuat ulang acara untuk mengumpulkan teman-teman Anda.", "ended": "<PERSON><PERSON><PERSON>", "event": "<PERSON>car<PERSON>", "inviteParticipateEvent": "<PERSON><PERSON> anda untuk bergabung dalam pesta {eventName} {userName}, datang dan bergabunglah dengan <PERSON>ker untuk bermain bersama.", "@inviteParticipateEvent": {"type": "string", "placeholders": {"userName": {}, "eventName": {}}}, "youGotFrom": "<PERSON><PERSON> men<PERSON> {name} dari {from}.", "@youGotFrom": {"type": "string", "placeholders": {"name": {}, "from": {}}}, "applyEventRewards": "<PERSON><PERSON><PERSON> hadiah acara", "unSubscribeTip": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> akan melew<PERSON> kejutannya!", "addToYourCalendar": "Tambahkan acara ke Kalender Anda", "keepDeviceCalendar": "Melacak acara ini terus dengan menambahkannya ke kalender perangkat Anda", "addUpcomingEvent": "Tambahkan acara mendatang yang Anda langgan ke kalender", "addToCalendar": "Tambahkan ke Kalender", "apply": "Terapkan", "invalidName": "<PERSON><PERSON> tidak valid", "invalidDescription": "Deskripsi tidak valid", "invalidTime": "<PERSON><PERSON><PERSON> tidak valid", "subscribe": "Subscribe", "subscribed": "Telah Subscribe", "makeAProposal": "<PERSON><PERSON><PERSON>", "proposal": "<PERSON><PERSON>", "proposalPaperDesc": "<PERSON><PERSON> dunia, kamu adalah seorang; tetapi bagi seseorang, kamu adalah seluruh dunianya.", "selectSuitor": "<PERSON><PERSON><PERSON> be<PERSON>", "intimacyProposal": "<PERSON><PERSON> keintiman lebih dari 5000 yang dapat melamar", "goCoupleZone": "Buka zona cinta", "selectRing": "<PERSON><PERSON><PERSON> cincin untuk melamar", "proposalVowsTitle": "Buat sumpah", "proposalVowsHint": "Tuliskan kata-kata untuk cinta Anda...", "selectTheLetter": "Pilih surat ini", "spendLetterConfirm": "Habiskan {money} be<PERSON>ian untuk membuat lamaran ke {name}", "@spendLetterConfirm": {"type": "string", "placeholders": {"money": {}, "name": {}}}, "proposalDesc": "Kedua pihak bisa melamar dengan keintiman 5.000. Anda bisa meningkatkan keakraban dengan mengobrol atau memberi hadiah.", "ring": "Cincin", "divorce": "Cerai", "emptyIntimacy": "Ups, <PERSON>a tidak memiliki belahan jiwa.\n<PERSON><PERSON> keintiman lebih dari 5000 yang dapat melamar", "emptyRing": "<PERSON>i cincin untuk melamar, tunjukkan hatimu!", "notSale": "Tidak untuk dijual", "loveZone": "Zona Cinta", "myLoveZone": "Zona Cinta Saya", "loveZoneQA": "Q&A zona cinta ", "loveZoneAnswer1": "1、Bagaimana memiliki zona cinta pasangan?", "loveZoneAnswer2": "2, <PERSON><PERSON> efek untuk mengganti cincin yang dikenakan?", "loveZoneAnswer3": "3. <PERSON><PERSON><PERSON> cara meningkatkan nilai keb<PERSON>han?", "loveZoneQuestion1": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus memiliki teman lawan jenis yang memiliki hubungan dengan keintiman lebih dari 5000 sebelum Anda dapat melamarnya. Ke<PERSON>ka pihak lawan itu menyetu<PERSON>i la<PERSON>n <PERSON>, <PERSON>a dapat memiliki zona cinta pasangan.", "loveZoneQuestion2": "<PERSON><PERSON>k cincin baru akan ditampilkan di profil dan zona cinta Anda.", "loveZoneQuestion3": "Mengirim hadiah di zona cinta dapat meningkatkan nilai keber<PERSON>han. Tetapi pemilik zona tidak bisa mendapatkan rabat berlian dan pesona.", "youDoNotHaveCouple": "Anda tidak memiliki pasangan", "someoneMaybeYouLove": "Mengobrol dengan belahan jiwa <PERSON>", "noQualifiedFriends": "Tidak ada teman yang memenuhi syarat", "goToFind": "<PERSON><PERSON> untuk menem<PERSON>n", "refuse": "Menolak", "intimacy": "<PERSON><PERSON><PERSON>", "intimacyWithScore": "<PERSON><PERSON><PERSON>: {score}", "@intimacyWithScore": {"type": "string", "placeholders": {"score": {}}}, "intimacyTips": "<PERSON><PERSON> keintiman lebih dari 5000, <PERSON><PERSON> dapat melamar menjadi pasangan", "intimacyInfoTitle1": "<PERSON>ya tiga langkah, dapatkan belahan jiwa!", "intimacyInfoTitle2": "Detail pasangan", "firstStep": "<PERSON><PERSON><PERSON> pertama", "makeFriends": "<PERSON><PERSON>", "secondStep": "<PERSON><PERSON><PERSON>", "thirdStep": "Lang<PERSON><PERSON> ketiga", "intimacySecondStepTips": "<PERSON><PERSON><PERSON> atau hadiah romantis dapat meningkatkan keintiman", "intimacyThirdStepTips": "<PERSON><PERSON><PERSON> ke pasangan Anda", "intimacyInfoTips1": "1. <PERSON><PERSON><PERSON>, <PERSON><PERSON> bisa mencari teman lawan jenis yang ingin kamu jalin melalui pencocokan atau kamar o<PERSON>lan.", "intimacyInfoTips2": "2. <PERSON><PERSON><PERSON> dan hadiah gaya Romantik dapat meningkatkan kemesraan antara kedua belah pihak. <PERSON><PERSON> harian untuk mengobrol dengan pasangan adalah 50 kali, sedangkan mengirim hadiah tidak terbatas. Setelah hngga 5.000, <PERSON>a jadi bisa melamar.", "intimacyInfoTips3": "3. <PERSON><PERSON> tidak ada interaksi dengan pihak lawan jenis setelah 7 hari, k<PERSON><PERSON><PERSON> akan turun 10 poin set<PERSON>p hari, dan poin terendah adalah 0 poin.", "intimacyInfoTips4": "<PERSON>a perlu membeli cincin di toko untuk melamar teman lawan jenis. Ketika pihak lawan menyetujui lamaran Anda untuk menjadi pasangan. Jika pihak lawan tidak memproses lamaran ini dalam waktu 3 hari, lamaran tersebut secara otomatis akan kedaluwar<PERSON>. <PERSON><PERSON> lamaran <PERSON> gagal, cincin itu akan dimasukkan kembali ke dalam ransel <PERSON>a.", "intimacyInfoTips5": "5. <PERSON><PERSON> hal putusnya hubungan pasangan, permohonan untuk memutuskan hubungan dapat diajukan kepada pihak lawan dengan kesepakatan bersama . Cincin ini akan hilang dengan putusnya hubungan. Anda harus menunggu tiga hari sebelum dapat melamar lagi.", "together": "<PERSON><PERSON><PERSON>", "blessValue": "<PERSON><PERSON>", "anniversary": "<PERSON> jadi", "ringBox": "Kotak cincin", "ringDisplay": "<PERSON><PERSON><PERSON> cincin", "recentlyLoveZoneGifts": "<PERSON><PERSON> zona cinta terbaru", "sendGiftCanIncreaseBlessValue": "<PERSON><PERSON><PERSON> hadiah dapat mening<PERSON>kan nilai berkat", "proposalMsg": "<PERSON><PERSON>", "haveTogetherDays": "<PERSON>a telah bersama {num,plural, =0{{num} day}=1{{num} day}=2{{num} days}few{{num} days}other{{num} days}}, harap hargai hubungan <PERSON>a..", "@haveTogetherDays": {"type": "string", "placeholders": {"num": {}}}, "findSoulmateFirst": "<PERSON><PERSON><PERSON> belahan jiwamu te<PERSON><PERSON> da<PERSON>u", "waitBeforeDivorce": "Tunggu 2 hari sebelum mengajukan cerai lagi", "youRejectDivorce": "<PERSON>a menolak permohonan cerai {name}, harap hargai…", "@youRejectDivorce": {"type": "string", "placeholders": {"name": {}}}, "annulledCouple": "Anda telah membatalkan hubungan pasangan", "threeDayReject": "Tiga hari tanpa pemrosesan akan ditolak secara otomatis", "goProposal": "<PERSON>", "compulsoryDivorce": "<PERSON><PERSON><PERSON><PERSON>", "consensualDivorce": "Konsensual", "compulsoryDivorceTip": "Habiskan {count} berlian untuk memaksa perceraian. <PERSON><PERSON><PERSON>a akan berakhir dan cincinnya akan hilang", "@compulsoryDivorceTip": {"type": "string", "placeholders": {"count": {}}}, "consensualDivorceTip": "<PERSON><PERSON><PERSON> pihak lain setuju, cincinnya akan hilang dan Anda akan menjadi lajang. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengajukan permintaan cerai?", "intimacyScoreIncrease": "Selamat! Keintiman di antara Anda berdua telah meningkat ", "proposalSendWait": "<PERSON>an telah diki<PERSON>kan ke kekasih Anda. <PERSON><PERSON> bersabar sementara {name} mempertimbangkan permintaan Anda. Doakan yang terbaik untuk Anda.", "@proposalSendWait": {"type": "string", "placeholders": {"name": {}}}, "checkMyProposal": "<PERSON>k lamaran saya", "applyCompulsoryDivorce": "a<PERSON>kan permohonan untuk perceraian wajib, silakan buat pilihan <PERSON>.", "propose": "<PERSON>", "intimacyRanking": "<PERSON><PERSON><PERSON> kein<PERSON>", "allRings": "<PERSON><PERSON><PERSON>", "currentlyRing": "<PERSON><PERSON>cin saat ini", "backpackRings": "<PERSON><PERSON><PERSON> ran<PERSON>", "wearANewRing": "<PERSON><PERSON> cincin baru", "ringGuidelines": "Panduan cincin", "wearRingExplain": "1. <PERSON><PERSON> da<PERSON>t menggunakan cincin bernilai apa pun.\n2. Memakai cincin baru tidak memerlukan persetujuan dari orang lain.\n3. <PERSON>at <PERSON>a menggunakan cincin baru, cincin lama tidak akan hilang, dan akan dimasukkan ke dalam kotak cincin.\n4. <PERSON><PERSON><PERSON> hari jadi pernikahan Anda tidak akan diubah karenanya.", "becomeCouple7Days": "Menjadi pasangan 7 hari", "becomeCouple30Days": "<PERSON><PERSON><PERSON> pasangan 30 hari", "becomeCoupleHalfYear": "<PERSON><PERSON><PERSON> pasangan set<PERSON>ah tahun", "becomeCoupleOneYear": "<PERSON><PERSON><PERSON> p<PERSON>", "whoBirthday": "ulang tahun {who}'", "@whoBirthday": {"type": "string", "placeholders": {"who": {}}}, "editAnniversary": "<PERSON>", "twoDays": "2 Hari", "togetherDays": "{num,plural, =0{{num}  hari}=1{{num}  hari}=2{{num}  hari}few{{num}  hari}other{{num}  hari}}", "@togetherDays": {"type": "string", "placeholders": {"num": {}}}, "togetherDaysNewLine": "{num,plural, =0{{num}\n <PERSON>}=1{{num}\nHari}=2{{num}\n Hari}few{{num}\nHari}other{{num}\nHari}}", "@togetherDaysNewLine": {"type": "string", "placeholders": {"num": {}}}, "date": "Tanggal", "pleaseEnterTitle": "<PERSON><PERSON><PERSON> masukkan gelar.", "coupleCenter": "<PERSON><PERSON><PERSON>", "detailsForCouple": "Detail pasangan", "gameMatch": "Pencocokan game", "beMyBetterHalf": " jadi<PERSON> separuh saya yang lebih baik", "proposeMsgDesc": "<PERSON><PERSON> me<PERSON> {name}，melamar <PERSON>a", "@proposeMsgDesc": {"type": "string", "placeholders": {"name": {}}}, "intimacyImageTipsTitle1": "Pusat pasangan untuk lamaran", "intimacyImageTipsTitle2": "Pintu masuk melamar/bercerai", "intimacyImageTipsTitle3": "Referensi profil pasangan", "intimacyImageTipsTitle4": "Referensi zona cinta", "rejected": "<PERSON><PERSON><PERSON>", "autoRejected": "<PERSON><PERSON><PERSON>tom<PERSON>", "invalid": "Tidak valid", "away": "Jauh", "past": "<PERSON><PERSON>", "divorcePetitionWait": "Pengajuan cerai sudah terkirim ke pasangan <PERSON>a, mohon tunggu diprosesi oleh pihak lain.", "divorceCoolingPeriod": "<PERSON><PERSON> masih dalam masa tenang untuk perceraian, harap tunggu 3 hari sebelum melamar", "rejectYourDivorce": "perceraian <PERSON><PERSON>.", "illegalContentReEdit": "<PERSON><PERSON> berisi konten il<PERSON>, harap di-edit kembali", "dearMale": "<PERSON><PERSON>", "dearFemale": "<PERSON><PERSON>", "nameContainsIllegal": "{name} be<PERSON><PERSON> konten il<PERSON>, harap di-edit kembali", "@nameContainsIllegal": {"type": "string", "placeholders": {"name": {}}}, "numSupporter": "{num} pendukung", "@numSupporter": {"type": "string", "placeholders": {"num": {}}}, "numTimesForFruity": "{num} kali", "@numTimesForFruity": {"type": "string", "placeholders": {"num": {}}}, "waitingToPlay": "<PERSON>ung<PERSON> untuk bermain", "playThisVideo": "Putar video ini", "video": "Video", "inviteToWatchVideo": "Undang untuk menonton video", "soundEffect": "<PERSON><PERSON><PERSON>", "soundEffectIsPlaying": "Efek suara sedang diputar", "party": "Pesta", "uploadMusic": "<PERSON><PERSON><PERSON>", "localMusic": "Musik Lokal", "notConnectedWifi": "Tidak terhubung ke Wi-Fi", "pleaseConnectWifi": "<PERSON>p sambungkan ponsel Anda ke Wi-Fi terlebih dahulu", "connectedWifiTips": "Pastikan Ponsel dan PC terhubung ke Wi-Fi yang sama, lalu buka browser PC dan masukkan tautan berikut ini.", "typeInPcBrowser": "Ketik tautan berikut di browser PC", "stayInThePage": "Tetap di halaman saat mengunggah", "uploadList": "<PERSON><PERSON><PERSON>ah", "got": "dapatkan", "fromTreasureBox": "<PERSON><PERSON>", "addNewRingInYourBackpack": "<PERSON><PERSON><PERSON> tambahkan cincin baru di ransel <PERSON>", "yourPhoneStorageInsufficient": "<PERSON><PERSON> pen<PERSON>n ponsel Anda tidak mencu<PERSON>.", "networkErrorAndReload": "<PERSON><PERSON><PERSON>, coba lagi muat ulang.", "errorOccurred": "<PERSON><PERSON><PERSON><PERSON>, harap muat ulang.(kode: {code})", "@errorOccurred": {"type": "string", "placeholders": {"code": {}}}, "youAreSuperAdmin": "Anda berada dalam mode admin super dan tidak dapat bergabung dengan fungsi ini.", "OnlinePeopleAtParty": "Yang online di pesta", "chattingInRoom": "mengobrol di pesta suara sekarang.", "chatInputFucPhoto": "Foto", "chatInputFucQA": "<PERSON><PERSON><PERSON> dan <PERSON>", "chatInputFucGuessFistOn": "Mengundang Tebakan Tos", "chatInputFucGuessFistOff": "Mematikan <PERSON>", "calculator": "<PERSON><PERSON><PERSON>", "calculatorHint": "Masukkan tema event...", "duration": "Du<PERSON>i:", "minutes": "{time,plural, =0{{time} menit}=1{{time} menit}=2{{time} menit}few{{time} menit}other{{time} menit}}", "@minutes": {"type": "string", "placeholders": {"time": {}}}, "sendGiftsSupportHer": "<PERSON><PERSON> hadiah untuk menduku<PERSON>", "sendGiftsSupportHim": "<PERSON><PERSON> hadiah untuk menduku<PERSON>", "distanceToTheTopOne": "Jarak ke Top 1", "topSupporter": "Pendukung top", "topCharming": "Penawan top", "total": "Total", "people": "Orang", "countdown": "<PERSON>ung mundur", "end": "<PERSON><PERSON><PERSON>", "WelcomeToJoinParty": "Selamat datang di pesta kami!", "calculatorClosed": "<PERSON><PERSON><PERSON> ditutup.", "calculatorOpenedBy": "Kalkulator dibuka oleh", "topSupporterIs": "Pendukung top adalah", "topCharmingIs": "<PERSON> paling menawan adalah", "switchRoomCloseCalculator": "Beralih mode kamar akan mematikan kalkulator.", "calculatorWillClosed": "<PERSON><PERSON><PERSON> akan ditutup dalam 1 menit.", "videoTime": "Total waktu", "@videoTime": {"type": "string", "placeholders": {"time": {}, "total": {}}}, "changeVideoVolume": "Ubah Volume Video", "thisWontAffectOther": "<PERSON> lain tidak akan terpeng<PERSON><PERSON>ya", "selectTheVideo": "Memilih video ini", "userPausedVideoMsg": "{identity} {name} men<PERSON><PERSON> pem<PERSON> video.", "@userPausedVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "userPlayVideoMsg": "{identity} {name} memutar video ini.", "@userPlayVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "userCloseVideoMsg": "{identity} {name} mematikan video ini.", "@userCloseVideoMsg": {"type": "string", "placeholders": {"identity": {}, "name": {}}}, "videoCannotPlay": "Video ini tidak dapat diputar, harap ganti ke video lain", "videoTryAgain": "<PERSON> yang salah, harap coba lagi.", "pausedVideo": "pemutaran video ini dijeda", "sureStopVideo": "Anda yakin ingin berhenti menonton video?", "changeVideoModeTips": "Yakin ingin mengubah mode kamar? Ini akan menyebabkan video berhenti diputar.", "closeVideoRoomTips": "Ini dapat menyebabkan ruang ditutup dan berhenti menonton video.", "manager": "Pen<PERSON><PERSON>", "operationTooFrequent": "Operasi terlalu sering, coba lagi nanti.", "videoRoom": "<PERSON><PERSON> video", "youReceivedANewMsg": "<PERSON>a menerima pesan baru", "messageNotification": "Not<PERSON><PERSON><PERSON>", "closeWillNotShow": "<PERSON><PERSON><PERSON> tidak akan men<PERSON>a", "unsupportedVideo": "Video tidak didukung, harap tingkatkan versi aplikasi.", "luckyBag": "Lucky bag", "instructions": "Instruksi", "instructionDetail": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pengiriman tas keberuntungan: pilih jumlah hadiah atau diamond yang akan dimasukkan ke dalam tas keberuntungan, lalu pilih jumlah penerima, dan te<PERSON>hir klik tombol kirim.\n2. <PERSON><PERSON><PERSON> penerima akan secara acak menerima hadiah atau berlian dari tas keberuntungan. <PERSON><PERSON>ian akan dikirim ke dompet Anda dan hadiah akan dikirim ke ransel Anda. <PERSON>lakan periksa detailnya di notifikasi obrolan.\n3. Tas keberuntungan berlaku selama 10 menit. <PERSON><PERSON> hadiah atau berlian tidak diklaim dalam waktu 10 menit, maka akan dikembalikan ke pengirim.", "giftValue": "<PERSON><PERSON>:", "numOfRecipients": "<PERSON><PERSON><PERSON>:", "world": "Dunia", "diamondQuantity": "<PERSON><PERSON><PERSON>:", "showInWinkerWorld": "<PERSON><PERSON><PERSON><PERSON> di dunia Winker!", "diamondCanBroadcast": "<PERSON><PERSON> nilai > 1000 berlian yang dapat disiarkan ke semua kamar", "luckyBagDetails": "Detail lucky bag", "sendLuckyBag": "<PERSON><PERSON> lucky bag", "openItNow": "Bukanya sekarang > ", "iGot": "<PERSON><PERSON>", "fromLuckyBag": "dari tas keber<PERSON>an.", "checkInDetails": "cek detail! > ", "viewDetail": "Melihat detailnya > ", "haveOpenedLuckyBag": "Anda telah membuka lucky bag ini", "luckyBagEmpty": "<PERSON><PERSON>, lucky bag ini kosong..", "bagExpired": "Tas ini telah kedalu<PERSON>. ", "wishYouLuck": "<PERSON><PERSON><PERSON> Anda beruntung!", "NoShowNextTime": "<PERSON><PERSON> tamp<PERSON>kan lain kali", "openedNum": "Buka {receiveNum}/{total}", "@openedNum": {"type": "string", "placeholders": {"receiveNum": {}, "total": {}}}, "luckiestDraw": "Undian paling beruntung", "giftFrom": "<PERSON><PERSON>", "truthDare": "Jujur&Tantangan", "truthDarePeopleCount": "hitung/10", "@truthDarePeopleCount": {"type": "string", "placeholders": {"count": {}}}, "truthDareQuestion": "{user} dihukum di ronde ini, harap terima hukumannya:\n{question}", "@truthDareQuestion": {"type": "string", "placeholders": {"user": {}, "question": {}}}, "host": "Host", "intimacyRewardTips": "Selamat!\n<PERSON><PERSON><PERSON> Anda berdua meningkat", "cpTask": "Tugas pasangan", "cpTaskDailyTime": "Reset harian pada 0:00 (GMT+3)", "blindDate": "<PERSON><PERSON>", "pick": "<PERSON><PERSON><PERSON>", "announce": "<PERSON><PERSON><PERSON>", "operation": "Operasi", "newRound": "<PERSON><PERSON> baru", "oppositeSexPeople": "<PERSON><PERSON> berik<PERSON>nya dapat dimulai setelah setidaknya ada seorang dari lawan jenis baru", "truthDareRunAway": "{user} sudah keluar dari room, game dalam ronde ini sudah berakhir.", "@truthDareRunAway": {"type": "string", "placeholders": {"user": {}}}, "punishedInRound": "Dihukum di ronde ini", "cannotSwitchRoomModes": "Anda tidak dapat beralih mode kamar selama game ini", "truth": "<PERSON><PERSON><PERSON>", "dare": "Tantangan", "waitingFemaleChoice": "Menunggu pilihannya", "waitingMaleChoice": "Tung<PERSON> pili<PERSON>nya", "selectYourPunishment": "<PERSON><PERSON><PERSON>", "cpRoomOpen": "{name} te<PERSON><PERSON>, datang dan bergabunglah dengan posisi mikrofon untuk berpartisipasi dalam <PERSON> pasangan.", "@cpRoomOpen": {"type": "string", "placeholders": {"name": {}}}, "cpAnnounceTips": "Host akan mengumumkan pilihan dari semua orang, harap tunggu dengan sabar.", "userPickUser": "{mainUser} Pilih {targetUser} di babak ini.", "@userPickUser": {"type": "string", "placeholders": {"mainUser": {}, "targetUser": {}}}, "userCpSuccess": "Selamat! {mainUser} dan {targetUser} telah ber<PERSON><PERSON>, selesaikan tugas berpasangan sekarang juga! ", "@userCpSuccess": {"type": "string", "placeholders": {"mainUser": {}, "targetUser": {}}}, "cpNewRound": "Host {name} me<PERSON><PERSON> babak baru.", "@cpNewRound": {"type": "string", "placeholders": {"name": {}}}, "announceHisPick": "<PERSON><PERSON><PERSON> p<PERSON>", "announceHerPick": "<PERSON><PERSON><PERSON> p<PERSON>", "closeSeconds": "<PERSON><PERSON><PERSON> otomatis set<PERSON> {count} detik", "@closeSeconds": {"type": "string", "placeholders": {"count": {}}}, "matchSucceeds": "Pencocokan berhasil", "announceRuleTitle": "Hasil final", "announceRule": "1. <PERSON><PERSON><PERSON>a dapat memilih untuk mengakhiri bagian ini dan mengumumkan hasilnya\n2. <PERSON><PERSON> dua pemain saling memilih, art<PERSON><PERSON> pencocokan berhasil", "later": "<PERSON><PERSON>", "userA": "Pengguna A", "userB": "Pengguna B", "inviteToTruthDare": "Undang untuk bermain Jujur&Tantangan", "vipCenter": "Pusat VIP", "vip_setting": "Pengaturan VIP", "whoCanChatMe": "Siapa yang bisa mengobrol dengan saya?", "mysteriousVisitor": "<PERSON><PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON><PERSON><PERSON>", "on": "Aktif", "onlyFriends": "<PERSON><PERSON>", "createConversationBySendGift": "Buat percakapan dengan Anda dengan men<PERSON> hadiah", "approvalRequiredToCreateConversation": "Persetujuan diperlukan untuk membuat percakapan", "onlyApplication": "<PERSON><PERSON>", "mysteriousVisitorFunction": "<PERSON><PERSON><PERSON> pengunjung misterius aktif-nonaktif", "uploadPicAvatar": "Unggah Avatar foto", "uploadGifAvatar": "Unggah Avatar Gif", "vip": "Vip", "notVipUseFunction": "Anda tidak dapat menggunakan fungsi ini. Harap aktifkan hak istimewa.", "validUntilData": "<PERSON><PERSON><PERSON><PERSON> hingga {date}", "@validUntilData": {"type": "string", "placeholders": {"date": {}}}, "firstLevelTips": "{num} poin untuk naik level.", "@firstLevelTips": {"type": "string", "placeholders": {"num": {}}}, "rechargeNow": "<PERSON><PERSON> ulang sekarang", "vipNum": "Nomor VIP", "@vipNum": {"type": "string", "placeholders": {"num": {}}}, "expNum": "Nomor EXP", "@expNum": {"type": "string", "placeholders": {"num": {}}}, "privilegesTitle": "<PERSON><PERSON> is<PERSON>wa", "@privilegesTitle": {"type": "string", "placeholders": {"num": {}, "total": {}}}, "unlimited": "Tak terbatas", "onlyVipEmoji": "<PERSON><PERSON> pengguna vip yang dapat mengirim emoji ini", "expText": "Exp", "vipSettled": "Level VIP Diselesaikan", "changeCover": "Ganti sampul", "newFriendsRequest": "Permintaan teman baru", "onlyFriendsCanChat": "Anda belum membuka dan hanya teman yang bisa mengobrol", "friendsRequestSettings": "Pengaturan -> Pengaturan VIP -> <PERSON><PERSON> yang dapat mengobrol dengan saya?", "noNewFriendRequest": "Anda tidak memiliki permintaan pertemanan baru", "vipCanChangeFriendsRequestSettings": "Pengguna VIP dapat mengubah pengaturan permintaan pertemanan.", "uploadBackground": "<PERSON><PERSON>ah latar belakang", "addFriend": "Tambahkan teman", "added": "Ditambahkan", "applicationHasBeenSent": "<PERSON><PERSON><PERSON><PERSON> telah <PERSON>, harap tunggu proses pengguna", "vipImage": "Gambar VIP", "youHaveReachedVip": "Selamat! Anda telah mencapai level VIP {level}, ", "@youHaveReachedVip": {"type": "string", "placeholders": {"level": {}}}, "youHaveDownVip": "Level VIP <PERSON>a turun ke level {level}, ", "@youHaveDownVip": {"type": "string", "placeholders": {"level": {}}}, "checkPrivilegeDetails": "silakan periksa detail hak istimewa Anda!", "checkMewPrivilegeDetails": "silakan periksa detail hak istimewa baru <PERSON>.", "uploadSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "vipLevel": "1.Level VIP", "vipLevelDetail": "Keanggotaan VIP diperoleh melalui pembelian dalam aplikasi untuk mendapatkan VIP Exps.", "vipValidity": "2. Validitas VIP", "vipValidityDetail": "Setiap level VIP berl<PERSON><PERSON> selama 90 hari. Selama periode 90 hari, jika VIP Exps Anda mencapai persyaratan level yang lebih tinggi, level VIP <PERSON><PERSON> akan segera ditingkatkan dan VIP Exps Anda akan diatur ulang ke 0. Peningkatan level dengan melewatkan level tertentu dapat didukung.\nJika level VIP Anda tidak ditingkatkan selama periode ini, setelah periode ini berakhir, level VIP <PERSON><PERSON> akan direset sesuai dengan VIP Exps yang Anda peroleh selama periode ini, dan \nVIP Exps akan direset ke 0. Untuk mempertahankan level VIP Anda saat ini, Anda perlu mengumpulkan VIP Exps yang diperlukan untuk level sebelumnya selama periode ini. Jika tidak, level VIP And<PERSON> akan diturunkan.\nJika Anda belum menjadi VIP, ketika VIP Exps Anda memenuhi persyaratan level VIP tertentu dalam waktu 90 hari, Anda akan mencapai level VIP tersebut dan VIP Exps Anda akan diatur ulang ke 0. Jika Anda tidak mencapai level VIP mana pun dalam 90 hari, aku<PERSON>lasi poin VIP Anda akan disetel ulang menjadi 0 dalam 90 hari.", "vipExps": "3. VIP Exps", "vipExpsDetail": "VIP Exps bisa Anda dapatkan dengan membeli diamond, 10 VIP Exps bisa ditukar dengan 1 USD. Tidak ada batasan atas berapa banyak VIP Exps yang bisa Anda peroleh per hari. <PERSON><PERSON> <PERSON><PERSON> dana pembelian, semua VIP Exps yang Anda peroleh untuk pembelian itu akan dikurangi. Poin VIP yang diperlukan untuk level sebelumnya ditunjukan di gambar berikut.", "vipFreeze": "4. <PERSON><PERSON><PERSON><PERSON>", "vipFreezeDetail": "Jika Anda tidak memiliki VIP Exps yang cukup yang dapat dipotong untuk membayar pengembalian dana, keanggotaan VIP Anda akan diblokir hingga Anda melunasi VIP Exps yang terhutang. Harap diperhatikan bahwa jika keanggotaan VIP Anda belum dibatalkan pembekuan dalam waktu 90 hari setelah dibekukan, poin VIP Anda tidak akan direset ke 0.", "vipExpRequirement": "Persyaratan VIP exp", "openAddFriendsFunction": "1. <PERSON><PERSON> fungsi menambahkan teman di pengaturan", "otherUsersWillSendYou": "2. <PERSON><PERSON><PERSON> lain akan men<PERSON>mi <PERSON>a permintaan pertemanan", "youCanManagerWhoCanTalkYou": "3. <PERSON><PERSON> dapat mengatur siapa yang dapat berbicara dengan <PERSON>a", "youCanInitiateConversation": "4. <PERSON><PERSON><PERSON>, <PERSON><PERSON> dapat memulai per<PERSON>", "openMysteriousSetting": "1. <PERSON><PERSON> pengunjung misterius di pengaturan", "visitSomeoneProfile": "2.<PERSON><PERSON><PERSON><PERSON> profil seseorang tanpa memberi tahu mereka siapa <PERSON>a", "guideLines": "Panduan", "vipBadge": "Medali VIP", "privileges": "<PERSON><PERSON>", "vipBadgeTips1": "<PERSON>esan o<PERSON>lan di kamar", "vipBadgeTips2": "Kartu profil di kamar", "vipBadgeTips3": "Daftar pengguna online", "vipBadgeTips4": "Profil", "vipBadgeTips5": "<PERSON><PERSON>", "vipEnterEffect": "Efek Enter Eksklusif", "vipEnterEffectTips": "Menarik perhatian semua orang dengan efek enter yang menarik", "vipProfileCard": "Kartu Profil Eksklusif", "vipProfileCardTips": "Tunjukkan kehormatan Anda dengan kartu profil yang terkenal", "vipAvatarFrame": "Bingkai avatar VIP eksklusif", "vipAvatarFrameTips": "Dapatkan lebih banyak perhatian dengan bingkai VIP khusus Anda", "vipChatFrame": "Bingkai obrolan VIP eksklusif", "vipChatFrameTips": "<PERSON><PERSON><PERSON><PERSON> pesan Anda ke teman Anda dengan bingkai cantik", "vipColoredUsername": "<PERSON><PERSON> pen<PERSON>una berwarna", "vipColoredUsernameTips": "<PERSON><PERSON><PERSON> per<PERSON>ian semua orang dengan nama berwarna", "vipPersonalId": "Id pribadi yang unik", "vipPersonalIdTips": "Hubungi layanan pelanggan VIP dan dapatkan ID pribadi unik secara gratis", "vipCustomizeAvatar": "<PERSON><PERSON> k<PERSON>", "vipCustomizeAvatarTips": "Anda dapat mengunggah foto favorit Anda dan menjadikannya avatar Anda", "vipSendRoomImage": "<PERSON><PERSON> gambar di kamar", "vipSendRoomImageTips": "<PERSON>a dapat mengirim gambar di kamar", "vipProfileBackground": "Sesuaikan latar belakang profil", "vipProfileBackgroundTips": "Anda dapat mengunggah foto favorit Anda dan menjadikannya latar belakang avatar Anda", "vipCustomizeRoomTheme": "<PERSON><PERSON><PERSON><PERSON> tema kamar", "vipCustomizeRoomThemeTips": "Tetapkan tema ruang khusus untuk menu<PERSON>n gaya <PERSON>a", "vipVipGifts": "Hadiah VIP eksklusif", "vipVipGiftsTips": "<PERSON>ya pengguna VIP yang dapat mengirim hadiah ini", "vipUserList": "Baris depan pada daftar Pengguna", "vipUserListTips": "Nikmati barisan depan di daftar kamar mana pun", "vipStickersOnMIC": "Stiker eksklusif di MIKROFON", "vipStickersOnMICTips": "Gunakan stiker eksklusif saat Anda berbicara di MIKROFON.", "vipMysteriousVistors": "<PERSON><PERSON><PERSON><PERSON> misterius", "vipMysteriousVistorsTips": "Kunjungi profil seseorang tanpa memberi tahu mereka siapa Anda", "vipDoubleRewards": "<PERSON><PERSON><PERSON> ganda dan hadiah tugas", "vipDoubleRewardsTips": "Dapatkan hadiah ganda untuk masuk dan menyelesaikan tugas", "vipUnlimitedText": "Teks tanpa batas", "vipUnlimitedTextTips": "Anda dapat mengirim teks apa pun yang Anda inginkan tanpa batasan", "vipActivityPromotion": "Promosi event ruang", "vipActivityPromotionTips": "Hu<PERSON><PERSON><PERSON> layanan pelanggan VIP setia<PERSON> bulan untuk mengirim postingan yang mempromosikan acara kamar <PERSON> {num}", "@vipActivityPromotionTips": {"type": "string", "placeholders": {"num": {}}}, "vipAddFriends": "Tambahkan fungsi teman", "vipAddFriendsTips": "<PERSON><PERSON><PERSON> se<PERSON>orang memulai per<PERSON> den<PERSON>, mereka harus meminta izin <PERSON>", "vipCustomerService": "<PERSON><PERSON> layanan pela<PERSON>", "vipCustomerServiceTips": "<PERSON><PERSON><PERSON> layanan terbaik dari ahli layanan pelanggan kami", "vipAdSendGifts": "Perwakilan pelanggan mengirim hadiah", "vipAdSendGiftsTips": "Hubung<PERSON> layanan pelanggan VIP untuk mengirim hadiah di kamar <PERSON>a", "vipMicProtection": "Perlindungan MIKROFON", "vipMicProtectionTips": "Perlindungan dari penghapusan dari MIKROFON", "vipRoomProtection": "<PERSON><PERSON><PERSON><PERSON>", "vipRoomProtectionTips": "<PERSON><PERSON><PERSON><PERSON> dari pemindahan keluar kamar", "vipNamedGifts": "<PERSON><PERSON>", "vipNamedGiftsTips": "<PERSON><PERSON><PERSON><PERSON> layanan VIP kami. <PERSON><PERSON> pengguna <PERSON>a akan ditampilkan pada hadiah spesial  ", "vipExclusiveAvatarFrame": "Bingkai avatar kustomisasi eksklusif", "vipExclusiveAvatarFrameTips": "<PERSON>lahkan hubungi layanan VIP kami. Kami akan membuat bingkai avatar Anda sendiri untuk Anda", "noVip": "TIADA VIP", "imageIllegal": "<PERSON><PERSON><PERSON> ini berisi konten ilegal, harap <PERSON>-un<PERSON><PERSON> kembali", "pk": "<PERSON><PERSON><PERSON><PERSON>", "votePk": "<PERSON><PERSON><PERSON><PERSON>", "giftPk": "<PERSON><PERSON><PERSON><PERSON>", "eachPersonCanVoteOnce": "Setiap orang hanya dapat bersuara satu kali", "calculatedByGifts": "Dihitung dari hadiah yang diterima", "selectDuration": "<PERSON><PERSON><PERSON> du<PERSON> (menit)", "startPK": "<PERSON><PERSON>", "jackpotMode": "Mode jackpot", "chooseAtLeastTwo": "<PERSON><PERSON><PERSON> setidaknya dua orang", "jackpotModeRule": "Peserta <PERSON>an tidak akan menerima penge<PERSON>lian hadiah selama periode Pertarungan. Pemenang Pertarungan akan mendapatkan hadiah senilai 30% set<PERSON><PERSON> se<PERSON>ai.", "specialDiscountGift": "<PERSON><PERSON> diskon spesial", "giftDiscountPercent": "<PERSON><PERSON>", "@giftDiscountPercent": {"type": "string", "placeholders": {"percent": {}}}, "valueDiamond": "<PERSON><PERSON>: {diamond}", "@valueDiamond": {"type": "string", "placeholders": {"diamond": {}}}, "buyWithAmount": "{amount} beli", "@buyWithAmount": {"type": "string", "placeholders": {"amount": {}}}, "giftSentYourBackpack": "<PERSON><PERSON> telah di<PERSON> ke ransel <PERSON>.", "vote": "<PERSON><PERSON><PERSON><PERSON>", "voted": "<PERSON><PERSON>", "support": "<PERSON><PERSON><PERSON>", "pkEndedTips": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "pkWinner": "<PERSON><PERSON><PERSON><PERSON>", "youWillLosePkOnceLeaveRoom": "Anda akan kehilangan Per<PERSON>ungan begitu Anda mening<PERSON>kan kamar", "youCanOnlyOpenOnePlay": "Anda hanya dapat membuka satu permainan dalam satu waktu", "pkResult": "<PERSON><PERSON>", "win": "<PERSON><PERSON>", "inPk": "<PERSON><PERSON>", "userStartGiftPk": " {user} me<PERSON><PERSON> hadiah.", "@userStartGiftPk": {"type": "string", "placeholders": {"user": {}}}, "userStartVotePk": " {user} me<PERSON><PERSON>.", "@userStartVotePk": {"type": "string", "placeholders": {"user": {}}}, "userIsWinnerInPk": "{user} ad<PERSON>h pemenang di Pertarung<PERSON>.", "@userIsWinnerInPk": {"type": "string", "placeholders": {"user": {}}}, "thePkEndInDraw": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> imbang.", "userGetDiamondsInPk": "{user} mendapatkan{count} berlian <PERSON><PERSON>.", "@userGetDiamondsInPk": {"type": "string", "placeholders": {"user": {}, "count": {}}}, "tryToTalkHerLabels": "Cobalah untuk membicarakan labelnya", "tryToTalkHisLabels": "Cobalah untuk membicarakan labelnya", "callEnded": "<PERSON>gg<PERSON><PERSON> be<PERSON>", "bothSidesShowIdentities": "Kedua pihak mengidentifikasi diri mereka sendiri, tanpa batasan waktu", "weakSignal": "<PERSON><PERSON> le<PERSON>", "mrWinker": "<PERSON><PERSON>", "missWinker": "<PERSON><PERSON>", "hangUp": "Angkat", "hangUpConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> Hang up", "talkLittleBitLonger": "Bicara yang lebih banyak lagi. <PERSON>ng<PERSON> Anda akan menemukan lebih banyak kejutan! Tetap mau hang up?", "keepTalking": "Tetap be<PERSON>", "freeChance": "{num} kesempatan gratis", "@freeChance": {"type": "string", "placeholders": {"num": {}}}, "setUp": "<PERSON><PERSON>", "matchNow": "Cocokkan se<PERSON>ng", "matchTimesIsOver": "<PERSON><PERSON>tu pencocokan hari ini telah berakhir. <PERSON><PERSON> tunggu sampai besok.", "useDiamond": "<PERSON><PERSON><PERSON>{diamond}", "@useDiamond": {"type": "string", "placeholders": {"diamond": {}}}, "toMatch": " cocokan", "moreFreeChance": "Lebih banyak kesempatan gratis", "noFreeToday": "Tidak ada kesempatan gratis hari ini", "stayInRoomNumMin": "Tetap di kamar selama 30 menit ({num}/30)", "@stayInRoomNumMin": {"type": "string", "placeholders": {"num": {}}}, "microphoneOn": "Mikrofon aktif", "microphoneOff": "Mikrofon mati", "earphone": "Earphone", "speaker": "Pembicara", "showIdentity": "Tunjukkan identitas", "me": "<PERSON><PERSON>", "connecting": "Sedang menghubungkan...", "inCall": "<PERSON><PERSON>", "thisVoiceCallDisconnected": "<PERSON><PERSON><PERSON><PERSON>n suara ini terputus, harap coba lagi", "youCanAccessEachOtherProfiles": "Panggilan dilakukan secara anonim. <PERSON>a dapat mengakses profil satu sama lain dengan mengungkapkan siapa diri Anda.", "identityShowed": "Identitas ditunjukkan", "pkIsNotFinished": "Pertarungan belum se<PERSON>ai", "onlyVipPurchase": "<PERSON><PERSON> vip yang dapat membelinya", "callMatchTips": "<PERSON><PERSON><PERSON> pangg<PERSON>n suara, identitas kedua belah pihak disembunyikan secara default. Anda dapat mengungkapkan identitas Anda kapan saja.", "callMatchTip1": "<PERSON><PERSON>n pengguna untuk Anda. <PERSON><PERSON> tunggu", "callMatchTip2": "Bagikan momen Anda dengan orang lain", "callMatchTip3": "<PERSON><PERSON><PERSON> kejam dan provokasi sangat dilarang", "overCallMatch": "Saat ini sedikit pengguna. <PERSON>lakan coba lagi nanti.", "callMatchSetTips": "Tutup tidak akan memanggil call match untuk Anda.", "callMatchRule": "1. Selamat datang di Call Match! Anda dapat mencocokkan dengan pengguna nyata dan mengobrol dengan mereka sesuka hati Anda.\n2. Setiap pencocokan membutuhkan waktu. Itu tergantung pada berapa banyak pengguna yang online sekarang. Jadi harap bersabar.\n3. Untuk setiap bicara, Anda memiliki waktu 4 menit untuk memberikan bicara dengan menyembunyikan identitas Anda. Jika Anda suka banyak bicara, Anda bisa menunjukkan siapa diri Anda dan meyakinkan pihak lawan untuk melakukan hal yang sama. Setelah kedua belah pihak mengidentifikasi, Anda akan diberikan waktu bicara tambahan.\n4. <PERSON><PERSON> itu, menunjukkan identitas Anda memungkinkan pihak lawan untuk dapat melihat informasi pribadi Anda dan mengetahui lebih banyak tentang Anda! Menunjukkan identitas pihak lawan juga dapat membuka kunci panggilan setelah panggilan selesai\n5. <PERSON><PERSON> <PERSON><PERSON> men<PERSON> pele<PERSON>han, provo<PERSON><PERSON> atau perilaku tidak sopan, harap laporkan kepada kami. <PERSON><PERSON> itu, <PERSON><PERSON> dapat menilai setiap panggilan (baik atau buruk) dan kami akan menerima tanggapan Anda.", "awaitingResponse": "<PERSON><PERSON><PERSON> respon…...", "pleaseRateThisCall": "<PERSON><PERSON>an menilai panggilan ini", "callTime": "<PERSON><PERSON><PERSON>: {time}", "@callTime": {"type": "string", "placeholders": {"time": {}}}, "thanksForYourRating": "<PERSON><PERSON> kasih atas penilaian Anda", "matchAgain": "Cocokkan lagi", "someoneCallingYou": "Seseorang sedang memanggil Anda!", "automaticallyConnected": "<PERSON><PERSON><PERSON> penggilan suara terkoneksi secara otomatis untuk Anda. Harap bersiap-siap.", "lvNum": "Lv", "@lvNum": {"type": "string", "placeholders": {"num": {}}}, "lv": "Lv", "family": "family", "familyCapital": "Family", "createFamily": "<PERSON><PERSON><PERSON> family", "familyCover": "Cover family", "familyName": "Nama family", "familyAnnouncement": "Pengumuman family", "applySuccess": "<PERSON><PERSON><PERSON><PERSON>", "pleaseWaitApproval": "<PERSON><PERSON> tunggu <PERSON>", "familyShareContent": "Datang dan bergabunglah dengan family Winker {name}! Mari mengobrol dan bersenang-senang!", "@familyShareContent": {"type": "string", "placeholders": {"name": {}}}, "familyRepresentative": "Anggota Family", "familyRoom": "<PERSON><PERSON> family", "familyHonorRanking": "{name} {position}", "@familyHonorRanking": {"type": "string", "placeholders": {"name": {}, "position": {}}}, "id": "ID", "weeklyRank": "Peringkat mingguan", "giftSent": "Mengirim", "giftReceive": "<PERSON><PERSON> yang di<PERSON>ima", "honor": "<PERSON><PERSON><PERSON>", "popular": "Popular", "familyList": "<PERSON><PERSON><PERSON> family", "patriarch": "Ketua Family", "vicePatriarch": "<PERSON><PERSON><PERSON> Family", "member": "Anggota", "master": "<PERSON><PERSON><PERSON><PERSON>", "familySettings": "Pengaturan Family", "setVicePatriarch": "Angkat sebagai Wakil Ketua Family", "setPatriarch": "Angkat sebagai Ketua Family", "removeFromFamily": "Hapus dari family", "cancelVicePatriarch": "Batalkan W<PERSON> Family", "removeUserOutFamily": "<PERSON><PERSON><PERSON><PERSON> {name} dari family?", "@removeUserOutFamily": {"type": "string", "placeholders": {"name": {}}}, "conditionForFamilyApplication": "Persyaratan untuk permintaan family", "familyLevel": "Level family", "familyManagement": "<PERSON><PERSON><PERSON><PERSON> family", "muteFamilyNotification": "Bisukan not<PERSON> family", "exitFamily": "<PERSON><PERSON><PERSON> dari family", "joinFamilyErrorTitle": "Anda tidak memenuhi persyaratan berikut untuk bergabung dengan family ini:", "automaticallyPass": "<PERSON><PERSON> secara otomatis", "auditPass": "Audit lulus", "applyCondition": "<PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON>", "searchUserName": "Cari nama pengguna", "familyManage": "<PERSON><PERSON><PERSON><PERSON> family", "setAsVicePatriarch": "Angkat sebagai Wakil Ketua Family", "setAsMaster": "Angkat sebagai Sesepuh", "disbandFamily": "Bubarkan family", "pleaseContactCustomerToDisbandFamily": "Ketua family tidak bisa keluar langsung. <PERSON><PERSON>an hubungi layanan pelanggan resmi untuk proses lebih lanjut.", "confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourContributionWillBeClearedIfExitFamily": "<PERSON><PERSON><PERSON> Anda keluar dari family, semua aset yang terkait dengan keluarga akan dihapus. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar dari family?", "cancelMaster": "Batalkan Se<PERSON>puh", "enterFamily": "<PERSON><PERSON><PERSON> family", "inRoomCannotCallMatch": "Anda tidak dapat memanggil pencocokan di ruang obrolan.", "bothOfYouShowId": "Anda berdua telah menun<PERSON>kkan identitas! Anda berdua dapat mengobrol satu sama lain.", "hungUp": "Hang up", "familyRequest": "Permintaan Family", "reject": "tolak", "rejectCapital": "<PERSON><PERSON>", "searchResult": "<PERSON><PERSON>", "recommendFamilyTitle": "Beberapa family direkomendasikan untuk Anda", "searchFamilyTip": "Cari nama atau ID family", "wantJoinFamily": "ingin bergabung dengan family", "goToCheck": "<PERSON><PERSON><PERSON>t untuk cek!", "joinMyFamilySuccess": "berhasil bergabung dengan family besar!", "refuseFamilyApply": "menolak permintaan family Anda.", "familyCreateSuccess": "Family Anda berhasil dibuat!", "leaveTheFamily": "tinggalkan family ini.", "kickedOutFamilyBy": "dikeluarkan dari family oleh", "refuseFamilyInvitation": "menolak undangan family Anda.", "createFamilyFailed": "gagal dibuat, silakan hubungi layanan pelanggan.", "inviteYouJoinFamily": "mengundang Anda untuk bergabung dengan family ini", "joinFamilySuccess": "<PERSON><PERSON><PERSON><PERSON> bergabung dengan family.", "invite": "<PERSON><PERSON>", "familyDisbanded": "Family <PERSON>a te<PERSON> di<PERSON>.", "joinTheFamily": "Bergabung dengan family ini", "invitedToJoinFamily": "+ Diundang untuk bergabung dengan family", "yourFamilyApplicationAlreadySent": "<PERSON><PERSON><PERSON><PERSON>a telah di<PERSON>. <PERSON><PERSON> tunggu tanggapan dari ketua family dengan sabar ", "invitedSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "familyRepresentativeRule": "<PERSON><PERSON><PERSON> Family, Wakil <PERSON>a Family, <PERSON><PERSON><PERSON><PERSON>", "familyRoomRule": "Setiap family bisa menjalankan ruang. Ruang dimiliki ketua family.", "familyRankRule": "Peringkat dihitung berdasarkan kontribusi anggota family", "familyRankExplain": "Pering<PERSON> dihitung berdasarkan nilai berlian hadiah yang anggota menerima", "enterPermission": "<PERSON><PERSON> ma<PERSON>k", "familyMemberOnly": "Hanya anggota family", "only30SearchData": "Hanya 30 data pencarian yang di<PERSON>, silakan masukkan lebih banyak kata kunci", "noMore": "<PERSON><PERSON>da yang lebih banyak", "editInformation": "Edit informasi", "familyUpLevel": "Selamat! Family Anda levelnya telah naik ke Lv. {num}", "@familyUpLevel": {"type": "string", "placeholders": {"num": {}}}, "thisFamilyHasBeenDisbanded": "Family ini telah di<PERSON>.", "youHaveNoAuditRights": "Anda tidak memiliki hak audit.", "roomPk": "<PERSON><PERSON><PERSON><PERSON>", "gems": "Rubi", "withdrawAvailable": "Penarikan tersedia", "gemExchangeDiamond": "Rubi = berlian.", "@gemExchangeDiamond": {"type": "string", "placeholders": {"gem": {}, "diamond": {}}}, "gemBalance": "<PERSON><PERSON>", "numberOfGems": "<PERSON><PERSON><PERSON>", "lackOfGems": "Kurangnya rubi! tolong selesaikan lebih banyak tugas untuk mendapatkan rubi.", "wealthLevelUp": "Selamat! Kekayaan Anda levelnya telah naik ke", "charmLevelUp": "Selamat! Pesona Anda levelnya naik ke", "activeLevel": "<PERSON><PERSON><PERSON>", "wealthLevel": "<PERSON><PERSON><PERSON>", "charmLevel": "<PERSON><PERSON><PERSON>", "wayWealthLevelUp": "Habiskan diamond di Winker", "wayCharmLevelUp": "<PERSON><PERSON> had<PERSON> di <PERSON>", "levelPrivilege": "Level hak istimewa", "levelPrivilegeTip1": "1. Dapatkan lebih banyak perhatian pada medali level Anda.", "levelPrivilegeTip2": "2. <PERSON><PERSON><PERSON> banyak fitur yang bisa dieksplorasi.", "googlePay": "Pembayaran Google", "huaweiPay": "<PERSON><PERSON><PERSON><PERSON>", "clickToChangePayMethod": "Klik di sini untuk mengubah metode pembayaran", "roomPkRanking": "<PERSON><PERSON><PERSON>", "roomPkExplain": "<PERSON><PERSON><PERSON> didasarkan pada nilai berlian dari hadiah selama pertarungan kamar.", "matchPk": "Pertarungan Match", "invitePk": "Undang Pertarungan", "matchTimeout": "Pencocokan kehabisan waktu", "retry": "Coba lagi", "roomPkMatching": "Cocokkan pertarungan kamar...", "pkMatching": "Mencocokkan", "victory": "<PERSON><PERSON>", "defeat": "<PERSON><PERSON><PERSON>", "pkPoint": "<PERSON><PERSON>:", "roomPkInvitation": "Undangan Pertarungan kamar", "invited": "<PERSON><PERSON><PERSON>", "myFollowRoomTitle": "<PERSON><PERSON> yang saya ikuti", "roomPkInviteTips": " undangan pertarungan kamar telah dikirimkan kepada Anda. <PERSON><PERSON><PERSON>h Anda menerima tantangan?", "rejectPkTips": " menolak undangan pertarungan kamar <PERSON>.", "roomsUser": "<PERSON><PERSON>", "@roomsUser": {"type": "string", "placeholders": {"room": {}, "user": {}}}, "roomPkNotice": "Notifikasi pertarungan kamar", "minExchangeGems": "Minimal 10 rubi", "inviteSuccess": "<PERSON>dan<PERSON> ber<PERSON>il", "todayTimesRunOut": "Waktu hari ini habis", "uniqueRoomId": "ID kamar unik", "uniquePersonalId": "Id pribadi yang unik", "myPersonalId": "ID pribadi saya:", "myRoomId": "ID kamar saya:", "myFamilyRoomId": "ID ruang family saya:", "searchUniqueIds": "Cari ID unik", "selectTimes": "<PERSON><PERSON><PERSON> waktu", "calculatorRanking": "<PERSON><PERSON><PERSON> kalk<PERSON>", "pleaseCreateRoom": "Anda tidak memiliki kamar saat ini, silakan buat kamar Anda sendiri.", "ripple": "Riak", "badge": "<PERSON>i", "wearBadge": "<PERSON><PERSON>", "achievementBadge": "<PERSON><PERSON>", "honorBadge": "<PERSON><PERSON>", "aboutBadges": "Tentang <PERSON>i", "aboutBadgeTitle1": "1. <PERSON><PERSON>", "aboutBadgeDesc1": "Se<PERSON>a lencana bisa dipakai.\nAnda dapat memakai hingga 3 lencana sekaligus.\nJika saat ini Anda memakai kurang dari 3 lencana, lencana tersebut akan dikenakan secara otomatis saat Anda mendapatkannya.\nSetiap lencana prestasi memiliki empat tingkat, dari terendah hingga tertinggi: Perunggu-Perak-Emas", "aboutBadgeTitle2": "2. <PERSON><PERSON>", "aboutBadgeDesc2": "Medali kehormatan diberikan kepada pengguna yang memenuhi syarat setiap hari <PERSON> dan berlaku selama sekitar satu minggu.", "currentlyWornBadges": "Medali yang dipakai saat ini", "badgeWearInstructions": "Inst<PERSON><PERSON> pema<PERSON> medali", "badgeWearDesc": "Anda bisa memakai medali yang anda peroleh di <PERSON>. Sumber medali berasal dari aktivitas dan pencapaian yang anda selesaikan di Winker. Medali yang Anda kenakan akan ditampilkan di profil dan profil ruangan. Anda dapat memakai maksimal tiga lencana.", "selectAGift": "<PERSON><PERSON>h satu hadiah", "selectGift": "<PERSON><PERSON><PERSON> hadiah", "selectGiftType": "<PERSON><PERSON><PERSON> tipe hadiah", "onlyCalculatedDesignateGift": "<PERSON><PERSON> hadiah yang di<PERSON>ung", "onlyCalculatedFamilyGift": "<PERSON><PERSON> hadiah {name} yang <PERSON>ung", "@onlyCalculatedFamilyGift": {"type": "string", "placeholders": {"name": {}}}, "roomPkRule": "1. P<PERSON><PERSON>k dan admin kamar dapat memulai <PERSON>an dalam fungsi kamar.\n\n2. Terdapat dua mode untuk Pertarungan, yaitu <PERSON> suara dan <PERSON> hadiah. <PERSON><PERSON> suara, setiap orang dapat memilih teman favorit untuk memberikan dukungan. <PERSON><PERSON><PERSON> pemungutan suara, suara tidak dapat ditarik kembali. Anda perlu mendukungnya dengan hadiah apa pun atau hadiah yang ditunjuk. Jika pencipta membuat Pertarungan hadiah yang ditentukan, hanya hadiah yang ditentukan dalam Pertarungan yang akan dihitung dalam Pertarungan.\n\n3. Pencipta dapat mengaktifkan mode Pertarungan jackpot . Peserta Pertarungan tidak akan dapat menerima pengembalian uang hadiah selama Pertarung<PERSON>. Pemenang akan menerima 30% dari nilai hadiah setelah <PERSON>tarung<PERSON>.", "myBadges": "<PERSON><PERSON>", "awardedOn": "<PERSON><PERSON><PERSON><PERSON> pada {time}", "@awardedOn": {"type": "string", "placeholders": {"time": {}}}, "exchangeGemToDiamond": "Tukarkan rubi ke berlian", "noCurrentRanking": "Tidak ada peringkat saat ini", "youHaveNoJoinTheFamily": "Anda belum bergabung dengan family ini", "searchTheFamilyUser": "Cari anggota family berdasarkan nama pengguna", "selectTheUser": "Cari pengguna ini", "doneNum": "Selesai({num})", "@doneNum": {"type": "string", "placeholders": {"num": {}}}, "numberOfMic": "<PERSON><PERSON><PERSON>", "setMicWhenRoomLevelReaches": "Anda dapat menyetelnya saat level kamar Anda mencapai Lv.{num}", "@setMicWhenRoomLevelReaches": {"type": "string", "placeholders": {"num": {}}}, "announceDefault": "Selamat datang di family!", "someoneAtYouWithBrackets": "[@<PERSON><PERSON>]", "someoneAtYou": "Seseorang @Anda", "userKickedOutOfFamilyBy": "{user} dikeluarkan dari family oleh {manager}.", "@userKickedOutOfFamilyBy": {"type": "string", "placeholders": {"user": {}, "manager": {}}}, "iAmNewToFamily": "<PERSON>, saya baru datang ke family ini.", "userSetAsVicePatriarch": "{user} diangkat sebagai Wakil Ketua Family.", "@userSetAsVicePatriarch": {"type": "string", "placeholders": {"user": {}}}, "userSetAsMaster": "{user} diangkat sebagai sesepuh.", "@userSetAsMaster": {"type": "string", "placeholders": {"user": {}}}, "userJoinTheFamily": "{user} bergabung dengan family ini.", "@userJoinTheFamily": {"type": "string", "placeholders": {"user": {}}}, "userExitTheFamily": "{user} keluar family.", "@userExitTheFamily": {"type": "string", "placeholders": {"user": {}}}, "welcomeToTheFamily": "{user}, selamat datang di family ini!", "@welcomeToTheFamily": {"type": "string", "placeholders": {"user": {}}}, "familyGroup": "<PERSON><PERSON> family", "mics_8_2": "8+2 Mikrofon", "mics_10": "10 Mikrofon", "mics_9": "9 Mikrofon", "mics_5": "5 Mikrofon", "mics_2": "2 Mikrofon", "changeTheNumberMicsWillRemoveUser": "Mengubah jumlah mikrofon akan dapat menghapus semua pengguna di mikrofon tersebut, apa<PERSON>h <PERSON>a yakin akan mengubah jumlah mikrofon?", "recommendUser": "Pengguna yang direkomendasikan", "followBestSurprise": "<PERSON><PERSON>ti mereka untuk kejutan terbaik!", "youMayInterest": "<PERSON><PERSON> mungkin berminat", "cancelAll": "Batalkan semua", "changeNumberOfMic": "<PERSON><PERSON> jumlah mikro<PERSON>n men<PERSON> {count} kursi.", "@changeNumberOfMic": {"type": "string", "placeholders": {"count": {}}}, "cleanChat": "Bersihkan obrolan", "userCleanedChat": " membersihkan obrolan ini", "cleanChatNoticeContent": "<PERSON><PERSON><PERSON><PERSON> Anda yakin menghapus obrolan?", "followerWithNum": "Pengikut: {num}", "@followerWithNum": {"type": "string", "placeholders": {"num": {}}}, "roomTask": "<PERSON><PERSON> kamar", "roomTaskRefreshTime": "Segarkan pada 23:59(UTC+3)", "roomTaskGuideText": "Klik di sini untuk mendapatkan tugas kamar!", "roomTaskMsgText": "Selesaikan tugas kamar untuk mendapatkan lebih banyak hadiah!", "knight": "Ksatria", "baron": "<PERSON>", "count": "<PERSON><PERSON><PERSON>", "king": "<PERSON>", "chief": "Kai<PERSON>", "winkerPremium": "Winker Premium", "enterMessage": "<PERSON><PERSON><PERSON><PERSON> pesan", "exclusivePrivileges": "Hak Istimewa Eksklusif", "getDiamondDay": "Dapatkan {num} {type} per hari", "@getDiamondDay": {"type": "string", "placeholders": {"num": {}, "type": {}}}, "diamondsForRenewal": "{num} be<PERSON>ian untuk per<PERSON>", "@diamondsForRenewal": {"type": "string", "placeholders": {"num": {}}}, "activatePremium": "Aktifkan Premium", "youNotWinkerPremium": "<PERSON><PERSON> bukan Winker Premium", "activatePremiumConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membayar {diamond} berlian untuk mengaktifkan premium? {name}?", "@activatePremiumConfirm": {"type": "string", "placeholders": {"diamond": {}, "name": {}}}, "expiredDate": "Tanggal kedaluwarsa: {date}", "@expiredDate": {"type": "string", "placeholders": {"date": {}}}, "uploadTheme": "<PERSON><PERSON><PERSON> tema", "dayLowCase": "hari", "activate": "mengaktifkan", "inWord": "di", "goldsLowCase": "koin", "gemsLowCase": "rubi", "diamondsLowCase": "<PERSON><PERSON><PERSON>", "days": "hari", "activatedValidUntil": "Anda telah ber<PERSON>il mengaktifkan {name} premium, berlaku hingga {time}", "@activatedValidUntil": {"type": "string", "placeholders": {"name": {}, "time": {}}}, "specialDiscount": "Diskon khusus", "premiumSettings": "Pengaturan premium", "preventFollowingIntoRoom": "<PERSON><PERSON><PERSON> ikut ke kamar", "youCanNotFollowThisUserIntoRoom": "Anda tidak dapat mengikuti pengguna ini ke dalam kamar", "yourLevelIsIncreasingAtPremiumSpeed": "Level Anda sedang meningkat dengan kecepatan Premium.", "changePremiumSetting": "1.<PERSON><PERSON> Pengaturan Anda di atas", "otherCannotFollowYou": "2.<PERSON><PERSON><PERSON> lain tidak dapat mengikuti Anda ke kamar Anda melalui profil", "dailyRewards": "<PERSON><PERSON> ha<PERSON>", "getDiamondsEveryDay": "Dapat<PERSON> berlian set<PERSON> hari", "premiumBadge": "Medali Premium", "showOnProfile": "<PERSON><PERSON><PERSON><PERSON> di profil", "showOnRoomProfile": "<PERSON><PERSON><PERSON><PERSON> di profil kamar", "showOnMic": "Tampilkan di posisi mikrofon", "screenMessage": "<PERSON><PERSON> layar", "onlineUserList": "Daftar pengguna online", "exclusivePremiumAvatar": "Bingkai avatar Premium Eksklusif", "premiumAvatarFrameDesc": "Dapatkan lebih banyak perhatian dengan bingkai Premium khusus Anda", "exclusivePremiumChat": "Bingkai obrolan Premium Eksklusif", "exclusivePremiumChatDesc": "<PERSON><PERSON><PERSON><PERSON> pesan Anda ke teman Anda dengan bingkai cantik", "sendImageInRoom": "<PERSON><PERSON> gambar di kamar", "sendImageInRoomDesc": "<PERSON>a dapat mengirim gambar di kamar", "discountDay": "<PERSON> diskon toko", "discountDayDesc": "<PERSON><PERSON><PERSON> hari <PERSON>a dapat membeli barang-barang di toko dengan diskon khusus!", "serviceExpert": "<PERSON><PERSON> layanan pela<PERSON>", "serviceExpertDesc": "<PERSON><PERSON><PERSON> layanan terbaik dari ahli layanan pelanggan kami.", "enterEffectTitle": "<PERSON><PERSON><PERSON><PERSON> gelar efek", "enterEffectTitleDesc": "Menarik perhatian semua orang dengan pesan masuk yang menarik", "unlimitedChat": "Obrolan tanpa batas", "unlimitedChatDesc": "Anda dapat mengobrol apa pun yang Anda inginkan tanpa batasan.", "levelUpSpeed": "Tingkatkan kecepatan", "levelUpSpeedDesc": "Dapatkan lebih banyak pengalaman level.", "preventFollowRoom": "<PERSON><PERSON><PERSON> ikut ke kamar", "preventFollowRoomDesc": "<PERSON>a dapat mencegah orang lain mengikuti Anda ke kamar.", "activationBroadcast": "Aktifkan siaran", "activationBroadcastDesc": "<PERSON><PERSON> per<PERSON>ian semua orang dengan siaran Premium Anda", "priorityReport": "Pelaporan prioritas", "priorityReportDesc": "Layanan pelanggan akan memproses informasi laporan Anda lebih cepat", "RebateGiftSend": "<PERSON><PERSON> untuk <PERSON> hadiah", "RebateGiftSendDesc": "<PERSON><PERSON> hadiah ke orang lain dan terima rabat berlian.", "checkInDetail": "Cek detail", "coloredNameRoomScreen": "<PERSON><PERSON><PERSON> per<PERSON>ian semua orang dengan nama berwarna yang ditampilkan di layar kamar.", "msgDescLuckyBag": "[Lucky bag]", "sendALuckyBag": "<PERSON><PERSON> lucky bag", "clickToGetTt": "klik untuk mendapatkannya! >", "alreadyGotLuckyBag": "<PERSON>dah dapat lucky bag ini.", "luckyBagIsEmpty": "Lucky bag kosong.", "shareEventTagContent": "Ayo bergabung dalam event spesial di Winker sekarang!", "followYou": "Mengikutimu", "followBack": "<PERSON><PERSON><PERSON> kem<PERSON>i", "exchangeKeysToUnlockBox": "Tukarkan kunci untuk membuka kotak bonus", "magicBox": "Kotak ajaib", "openBox": "Buka kotak", "sendKeys": "<PERSON><PERSON> k<PERSON>", "keysGiftedByOthers": "<PERSON><PERSON>i yang diberikan oleh orang lain: {num}", "@keysGiftedByOthers": {"type": "string", "placeholders": {"num": {}}}, "yourKeyMustBeSentFromAnother": "<PERSON><PERSON><PERSON> terbuka Anda harus dikirim dari pengguna lain.", "requestKey": "<PERSON><PERSON><PERSON><PERSON> kunci", "requestsHasReachedTheUpperLimit": "<PERSON><PERSON><PERSON> permintaan untuk pengguna ini telah mencapai batas atas.", "requestSuccessful": "<PERSON><PERSON><PERSON><PERSON>, harap tunggu respon", "youHaveSentRequest": "Anda telah mengirim permintaan ke pengguna ini", "requestAMagicBoxKey": "Meminta kunci kotak ajaib", "pleaseGiveMeKey": "Tolong berikan kunci agar saya dapat membuka kotak harta karun ini!", "sentYouAMagicBoxKey": "mengirimi Anda kunci kotak ajaib", "youAlreadySentAMagicKey": "Anda sudah mengirim kunci kotak ajaib ke", "iSentYouMagicBoxKey": "<PERSON><PERSON>a kunci kotak ajaib", "keysCanBeGifted": "<PERSON><PERSON><PERSON> dapat di<PERSON>: {num}", "@keysCanBeGifted": {"type": "string", "placeholders": {"num": {}}}, "sendAKey": "<PERSON><PERSON> satu kunci", "youLackOfKeys": "Kekurangan kunci! Silakan minta kunci dari anggota family yang lain.", "sayHi": "Katakan hai", "freeGift": "<PERSON><PERSON> gratis", "treasureTitle": "Tugas untuk harta karun", "finishTreasureTask": "Menyelesaikan tugas dapat memberi Anda kesempatan untuk menerima hadiah yang lebih besar.", "luckyBonusNum": "Bonus keber<PERSON><PERSON>an:", "getItTomorrow": "Dapatkannya besok", "treasureTips": "Datang dan bagikan hadiah dari harta karun sekarang!", "treasureTotal": "Total harta karun hari ini:", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keysAndDiamondsOpenThisBox": "{key} kunci dan {diamond} berlian dapat membuka kotak ini", "@keysAndDiamondsOpenThisBox": {"type": "string", "placeholders": {"key": {}, "diamond": {}}}, "keysOpenThisBox": "{key} kunci dapat membuka kotak ini", "@keysOpenThisBox": {"type": "string", "placeholders": {"key": {}}}, "opened": "<PERSON><PERSON><PERSON>", "magicBoxTask": "Tugas kotak ajaib", "enterNumLuckyBag": "<PERSON><PERSON><PERSON> masukkan jumlah lucky bag", "luckyBagCharge": "Komisi 5% ditagih untuk setiap lucky bag", "you": "<PERSON><PERSON>", "haveReceivedLuckyBag": "telah menerima lucky bag ini", "youRLuckyBagIsEmpty": ", Lucky bag Anda kosong!", "luckyBagFrom": "Lucky bag dari:", "luckyBagHasExpired": "Lucky bag ini telah kedaluwarsa", "youGot": "<PERSON><PERSON> men<PERSON>", "getTheTreasureRewards": "Da<PERSON>t<PERSON> hadiah dari harta karun sekarang!", "claimAKey": "<PERSON><PERSON> seku<PERSON>i", "pleaseGiveMeAKey": "Tolong berikan satu kunci agar saya dapat membuka kotak harta karun ini!", "youDoNotBelongTheFamily": "Kamu bukan anggota family ini", "calling": "Memanggil", "sayHello": "[Katakan halo]", "familyData": "Data family", "instructionChatLuckyBag": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>man lucky bag: <PERSON><PERSON><PERSON> jumlah hadiah atau berlian yang akan dimasukkan ke dalam lucky bag, lalu pilih jumlah penerima, dan terakhir klik tombol kirim.\n2. <PERSON><PERSON><PERSON> penerima akan secara acak menerima hadiah atau berlian dari lucky bag. <PERSON><PERSON><PERSON> akan dikirim ke dompet Anda dan hadiah akan dikirim ke ransel Anda. <PERSON>lakan periksa detailnya di notifikasi obrolan.", "receiverNumRubies": "<PERSON><PERSON><PERSON> mendapat {num} rubi", "@receiverNumRubies": {"type": "string", "placeholders": {"num": {}}}, "senderWealthAddNumExp": "Exp kekayaan pen<PERSON> +{num}", "@senderWealthAddNumExp": {"type": "string", "placeholders": {"num": {}}}, "receiverCharmAddNumExp": "Exp pesona penerima +{num}", "@receiverCharmAddNumExp": {"type": "string", "placeholders": {"num": {}}}, "hotFamily": "Family yang <PERSON>n", "searchResults": "<PERSON><PERSON>", "talent": "Bakat", "clickTheBoom": "{name} klik [BOOM]!!", "@clickTheBoom": {"type": "string", "placeholders": {"name": {}}}, "timeToComment": "Pertunjukan sudah berakhir. Ini saatnya untuk memberikan komentar", "modeWillBeEnd": "Mode ini akan diakhiri. <PERSON>a dapat beralih ke mode lain setelah berakhir.", "skipPerformanceConfirm": "Pertunjukan ini akan dilewati. Poin untuk babak ini akan dihitung sekarang. Masih mau lewati?", "performanceEndAdvance": "<PERSON><PERSON><PERSON><PERSON> {name} be<PERSON><PERSON> lebih awal. Ini saatnya para juri berbicara", "@performanceEndAdvance": {"type": "string", "placeholders": {"name": {}}}, "grabMicMode": "Mode grab mikrofon", "specifyMode": "<PERSON> yang di<PERSON>", "performanceTime": "<PERSON><PERSON><PERSON>（menit）", "specifyPerformer": "<PERSON><PERSON><PERSON> yang <PERSON>:", "inviteYouChat": "Undang Anda untuk mengobrol", "inviteYouPlayGames": "Undang Anda untuk bermain game", "inviteYouForDate": "Undang Anda untuk berkencan", "inviteYouWatchVideo": "Undang Anda untuk menonton video", "inviteYouForTalent": "Undang Anda untuk pertunjukan berbakat", "joinTheRoom": "Bergabung dengan kamar ini", "online": "Aktif", "nearby": "Se<PERSON>tar", "filter": "Filter", "selectYourFavorite": "<PERSON><PERSON><PERSON> favorit <PERSON>a", "selectYourCity": "<PERSON><PERSON>h kota Anda", "setTheCity": "Setel kota ini", "scoresPerformersHere": "Skor untuk semua pemain ditampilkan di sini.", "clearPoint": "<PERSON><PERSON><PERSON><PERSON> poin", "clearPointConfirm": "<PERSON><PERSON><PERSON> poin pemain akan di<PERSON>. Tetap bersih<PERSON>?", "voiceAdjust": "<PERSON><PERSON><PERSON><PERSON>", "earphoneFeedback": "Umpan balik earphone", "onlyWiredHeadsets": "Hanya headset berkabel yang didukung", "voice": "<PERSON><PERSON>", "reverb": "Berkumandang:", "studio": "Studio", "ktv": "KTV", "stereo": "Stereo", "cd": "CD", "echo": "<PERSON>ema", "distant": "Jarak", "partyReverb": "Pesta", "church": "Gereja", "piano": "Piano", "hall": "<PERSON><PERSON>", "naturalReverb": "Natural", "noRank": "<PERSON><PERSON><PERSON>", "guest": "TAMU", "leaveTheList": "Tinggalkan list ini", "sureLeaveTheList": "Tinggalkan daftar tidak akan bisa tampil. Tetap pergi?", "performerList": "Datar pemain", "applyList": "<PERSON><PERSON><PERSON>", "kicked": "Tendang keluar", "onlyByInvitation": "<PERSON><PERSON>n", "inviteToHost": "Undang untuk host", "inviteToGuest": "Undang tamu", "inviteToList": "<PERSON><PERSON><PERSON>", "kickOutList": "<PERSON>ftar tendang keluar", "youKickedOut": "<PERSON>a telah di<PERSON>n dari daftar", "inviteJoinPerformer": "{username} mengundang Anda untuk bergabung ke dalam daftar pemain", "@inviteJoinPerformer": {"type": "string", "placeholders": {"username": {}}}, "thePerformerListFull": "<PERSON><PERSON><PERSON> pemain penuh", "applyTooFrequently": "Permintaan untuk mengambil mic terlalu sering. Coba lagi nanti", "successfullyApplied": "permin<PERSON><PERSON> ber<PERSON>", "youAreGuestNow": "<PERSON><PERSON><PERSON>. <PERSON>a berada di mikrofon tamu sekarang", "pleaseSetUpYourCity": "<PERSON><PERSON>an atur kota Anda agar teman-teman lokal Anda lebih mudah menemukannya.", "matchingASuitableRoom": "Mencocokkan kamar yang cocok untuk Anda", "noMoreSuitableRoom": "T<PERSON>da ruang lagi yang cocok, si<PERSON><PERSON> subscribe untuk mengikuti event di bawah ini!", "noMoreFamily": "Tidak ada lagi family", "youInvitedHost": "<PERSON><PERSON> di<PERSON>ng untuk mengambil mikrofon host", "youInvitedGuest": "<PERSON>a diundang untuk mengambil mikrofon tamu", "talentBegins": "<PERSON><PERSON> be<PERSON> dimula<PERSON>!", "performing": "tampil", "grabbing": "grab/rebut", "guestSpeak": "<PERSON><PERSON>", "readyGrab": "Siap merebut", "grab": "Merebut!", "readyToPerform": "Siap untuk tampil!", "grabbedTheMic": "Raih mikrofonnya!", "insufficientNumberGrabMic": "Jumlah orang tidak cukup untuk merebut mikrofon", "NoAvailablePerformer": "tidak ada pemain yang tersedia, harap tambahkan terle<PERSON>h dahulu", "skipPerformConfirm": "Pertunjukan ini akan dilewati. Poin untuk babak ini akan dihitung sekarang. Masih mau lewati?", "grabbingUp": "Grab/rebut", "endMode": "<PERSON> be<PERSON>", "startAgain": "<PERSON><PERSON> lagi", "yourCity": "Kota Anda", "thePerformanceIsEnd": "pertunjukan ini telah berakhir", "leaveRoomStopPerforming": "<PERSON><PERSON><PERSON> dari kamar akan dapat berhenti tampil. tetap keluar?", "leaveMicStopPerforming": "<PERSON><PERSON><PERSON> dari mikrofon akan dapat berhenti tampil. Tetap keluar?", "leaveNowWillCloseRoom": "pergi sekarang akan dapat menutup kamar.", "clickBoom": "klik BOOM!!", "talentRule": "1. Selamat datang di Kamar Berbakat! <PERSON> kamar ini, <PERSON>a bisa menjadi penampil dan menampilkan pertunjukan yang luar biasa. Atau Anda bisa menjadi anggota audiens yang aktif, yang berfungsi untuk menyemangati para penampil.\n2. <PERSON><PERSON> ini meliputi dua mode: Mode grab mikrofon atau mode yang ditunjuk\nMode grab mikrofon: host (atau pemilik/admin kamar) dapat mengundang pengguna. Berikut ini hanya menggunakan host sebagai contoh) atau Anda dapat mendaftar untuk bergabung dengan daftar pemain dan menjadi kandidat pemain. Host akan memulai mode dan setiap kandidat dapat merebut mikrofon. Siapa pun yang meraih mikrofon akan tampil di atas panggung. Setelah pertunjukan, para tamu dapat berkomentar, setelah itu host akan memulai babak baru.\nb. Mode yang ditunjuk: kandidat dalam daftar pemain ditunjuk oleh host dan tampil di atas panggung.\n3. Apa yang dapat dilakukan audiens?\na. <PERSON><PERSON>n hadiah untuk mendukung pemain dan meningkatkan poin. Hadiah umum senilai 1 berlian = 1 poin. Hadiah spesial (3 hadiah saat tampil) senilai 1 berlian = 2 poin. \nKlik [BOOM] untuk mendukung pemain. Gratis! 1 [BOOM] dari penonton= 10 poin. 1 [BOOM] dari tamu = 100 poin.\n4. Bagaimana saya bisa menjadi tamu?\na. Tamu hanya dapat diundang oleh host (atau pemilik/admin kamar)\nb. Para tamu juga dapat mengirimkan hadiah, klik [BOOM] untuk meninggalkan pesan untuk penampil.\n5. Bagaimana saya bisa menjadi host?\na. Host hanya dapat diundang oleh pemilik atau admin kamar.\n6. Poin dari semua pemain akan diakumulasikan dan ditampilkan di papan skor kumulatif sesuai dengan peringkat mereka.\n7. Dalam hal Anda meninggalkan mikrofon, meninggalkan kamar atau dikeluarkan dari kamar selama pertunjukan, poin yang Anda peroleh tidak akan dihitung.", "faq": "<PERSON><PERSON><PERSON>", "performingSwitchMode": "Sedang tampil sekarang. Beralih mode kamar di<PERSON>lak", "switchTalentMode": "<PERSON><PERSON><PERSON> sekarang akan dapat menutup kamar berbakat. Pertunjukan akan berakhir. Tetap ingin beralih?", "inviteToTalent": "Undang untuk menonton pertunjukan berbakat", "noSupporterOnThisRound": "Tidak ada pendukung di babak ini.", "youCannotChangeSeat": "<PERSON>a tidak dapat mengubah kursi", "painter": "Pelukis", "drawGuess": "Gambar&Tebak", "loadingNum": "Sedang memuat:{num}%", "@loadingNum": {"type": "string", "placeholders": {"num": {}}}, "waitingPlayer": "<PERSON><PERSON><PERSON> pemain...", "automaticStartNum": "<PERSON><PERSON> otomati<PERSON>:{num}detik", "@automaticStartNum": {"type": "string", "placeholders": {"num": {}}}, "oppositeVoice": "<PERSON><PERSON>", "turnOnSwitchCanHearOppositeVoice": "Nyalakan sakelar dapat mendengar suara ruangan seberang (semua mikrofon)", "allUserInYourRoomCanHearVoice": "<PERSON><PERSON>a pengguna di room Anda dapat mendengar suara di seberang sekarang! <PERSON><PERSON>ah!", "grabPoints": "raih {points} poin", "@grabPoints": {"type": "string", "placeholders": {"points": {}}}, "yourRoomGrabPoints": "<PERSON><PERSON> {points} poin", "@yourRoomGrabPoints": {"type": "string", "placeholders": {"points": {}}}, "theOppositeRoomReceived": "<PERSON><PERSON> seber<PERSON> diterima", "yourRoomIsGrabbedPoints": "<PERSON><PERSON> {points} poin", "@yourRoomIsGrabbedPoints": {"type": "string", "placeholders": {"points": {}}}, "allParticipantsPoints": "semua peserta {points} poin", "@allParticipantsPoints": {"type": "string", "placeholders": {"points": {}}}, "pointsChange": "{points} poin", "@pointsChange": {"type": "string", "placeholders": {"points": {}}}, "yourRoomPointsChange": "<PERSON><PERSON> {points} poin", "@yourRoomPointsChange": {"type": "string", "placeholders": {"points": {}}}, "theOppositeRoomPointsChange": "kamar seberang {points} poin", "@theOppositeRoomPointsChange": {"type": "string", "placeholders": {"points": {}}}, "defenseBuff": "buff per<PERSON><PERSON>n{count}", "@defenseBuff": {"type": "string", "placeholders": {"count": {}}}, "allParticipantsDefenseBuff": "buff per<PERSON>anan semua peserta {count}", "@allParticipantsDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "youRoomDefenseBuff": "buff per<PERSON><PERSON><PERSON> ka<PERSON> {count}", "@youRoomDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "theOppositeRoomDefenseBuff": "buff per<PERSON><PERSON>n kamar se<PERSON> {count}", "@theOppositeRoomDefenseBuff": {"type": "string", "placeholders": {"count": {}}}, "choosingWords": "<PERSON><PERSON><PERSON><PERSON> kata-kata", "chooseYourWords": "<PERSON><PERSON><PERSON><PERSON> kata-kata <PERSON>a", "resultWithWords": "Hasil: {words}", "@resultWithWords": {"type": "string", "placeholders": {"words": {}}}, "closeIn": "<PERSON><PERSON><PERSON>", "hintsWithContent": "Petunjuk: {words}", "@hintsWithContent": {"type": "string", "placeholders": {"words": {}}}, "estimatedRefund": "Estimasi pengembalian dana: ", "SelectActivityProgress": "Pilih progres event", "activityProgress": "Progres event", "result": "<PERSON><PERSON>", "scoreAddNum": "da<PERSON>t<PERSON><PERSON> dengan benar dan skor ", "pleaseInputAgain": "<PERSON><PERSON><PERSON>, silakan masukkan lagi", "inputAnswerHere": "<PERSON><PERSON><PERSON> jawaban disini...", "record": "Catatan", "chooseWordGuideTip": "<PERSON><PERSON><PERSON> kata-kata yang ingin <PERSON>, <PERSON><PERSON> juga dapat mengganti kata-katanya.", "paintGuideTip": "<PERSON>akan jari dan menggambar kata-kata Anda!", "guessGuideTip": "Ma<PERSON>kkan tebakan <PERSON>!", "queryOneMonth": "Hanya catatan kueri dalam 1 bulan yang dapat didukung", "roomPkRecord": "<PERSON><PERSON><PERSON>", "drawGuessFaq": "1. <PERSON><PERSON><PERSON><PERSON> minimal 2 pemain untuk memulai game ini.\n2. <PERSON><PERSON><PERSON> pemain bergiliran menjadi pelukis, menggambar kata yang dipilih sementara pemain lain menebak.\n3. Pelukis akan memberi skor sesuai dengan jumlah orang yang menebak dengan benar.\n4. Penerbak mendapatkan poin yang berbeda sesuai dengan urutan jawaban yang benar.\n5. <PERSON><PERSON><PERSON>, pemain diberi peringkat berdasarkan skor final yang mereka peroleh.", "noRoomPkRecord": "Belum ada catatan pertarungan kamar", "invitePlayDrawGuess": "Bermain Gambar&Tebak", "shareScreenShotTo": "Bagikan tangkapan layar ke", "undercover": "<PERSON><PERSON><PERSON>", "youCantTakeMicOnOppositeRoom": "Anda tidak dapat mengambil mikrofon di ruangan seberang", "undercoverTip": "<PERSON><PERSON> tentukan apakah Anda seorang penyamar atau orang sipil, tetaplah sembunyikan identitas Anda atau bersuara untuk menghilangkan penyamar.", "describeYourWords": "<PERSON><PERSON><PERSON><PERSON><PERSON> kata-kata", "voteForUndercover": "<PERSON><PERSON><PERSON><PERSON> untuk pengguna yang menurut Anda paling seperti penyamar.", "noVotesForUndercover": "Tiada suara yang diberikan pada ronde ini. Lanjutkan ronde berikutnya secara otomatis.", "turntable": "<PERSON><PERSON>", "reJoinFamilyTip": "<PERSON><PERSON><PERSON> keluar dari family, <PERSON>a hanya dapat bergabung kembali setelah 24 jam.", "undercoverNotAllowedToSpeak": "Selama game, pengguna yang tidak berada di babak deskripsi saat ini tidak diperbolehkan untuk berbicara.", "myWord": "<PERSON><PERSON>-kata saya: {word}", "@myWord": {"type": "string", "placeholders": {"word": {}}}, "letTheGameStart": "Mari gamenya dimulai", "whoIsTheUndercover": "<PERSON><PERSON> penyamar?", "tips": "Tips", "roundVoting": "<PERSON><PERSON><PERSON><PERSON><PERSON> {round} <PERSON><PERSON>", "@roundVoting": {"type": "string", "placeholders": {"round": {}}}, "pass": "<PERSON><PERSON>", "civilian": "Orang Sipil", "noOut": "No.{position} KELUAR!", "@noOut": {"type": "string", "placeholders": {"position": {}}}, "coupon": "<PERSON><PERSON><PERSON>", "premium": "Premium", "contentOff": "{content} mati", "@contentOff": {"type": "string", "placeholders": {"content": {}}}, "noticeForUse": "<PERSON><PERSON><PERSON><PERSON> pengg<PERSON>an", "noMoreCoupons": "Tidak ada kupon yang lain", "used": "<PERSON><PERSON>", "expiredCapital": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noCouponsAvailable": "Tidak ada kupon yang tersedia", "notAllowedToSpeak": "Tidak diperbolehkan untuk berbicara selama bukan ronde game Anda.", "notAllowedCloseTheMic": "Tidak diperbolehkan menutup mikrofon selama ronde deskripsi.", "undercoverFaq": "1. Pada saatnya game dimulai di awal, setiap orang akan mendapatkan satu kata. <PERSON><PERSON>ikan bahwa ada salah satu kata yang berbeda dari kata-kata yang lain. <PERSON>i tahu siapa penyamar itu. <PERSON> penyamar, harap sembunyikan identitas Anda dan hidup sampai akhir.\n2. <PERSON><PERSON>p pemain memiliki waktu 30 detik di setiap ronde untuk mendeskripsikan kata mereka, secara bergiliran.\n3. <PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>, semua pemain memiliki waktu 20 detik untuk bersuara.\n4. <PERSON><PERSON>in dengan suara terbanyak akan dieliminasi. <PERSON><PERSON> seri, akan ada ronde kematian mendadak di mana para pemain mendeskripsikan kata-kata mereka lagi. <PERSON><PERSON> tidak ada suara, permainan berlanjut ke babak berikutnya. ", "records": "Catatan", "participant": "<PERSON><PERSON><PERSON>", "youCantChangeInThisRound": "Anda tidak dapat mengganti peserta di ronde ini", "optionTitleNum": "Opsi ({num}/8)", "@optionTitleNum": {"type": "string", "placeholders": {"num": {}}}, "turnOn": "<PERSON><PERSON>", "contentCannotEmpty": "Konten tidak boleh kosong", "sureTurnoff": "Anda yakin ingin menutup meja putar?", "containSensitive": "<PERSON><PERSON><PERSON> kata-kata sensitif", "voting": "<PERSON><PERSON><PERSON><PERSON>", "extraContent": "Ekstra {content}", "@extraContent": {"type": "string", "placeholders": {"content": {}}}, "decreaseContent": "Penurunan Konten", "@decreaseContent": {"type": "string", "placeholders": {"content": {}}}, "increaseContent": "<PERSON><PERSON><PERSON>", "@increaseContent": {"type": "string", "placeholders": {"content": {}}}, "turntableResult": "<PERSON><PERSON> meja putar: ", "turntableIsClosed": "<PERSON>ja putar ini ditutup.", "update": "<PERSON><PERSON><PERSON><PERSON>", "cannotStartTurntable": "Anda tidak dapat memulai meja putar saat roda keberuntungan menyala", "cannotStartLuckyWheel": "Anda tidak dapat memulai roda keberuntungan saat meja putar menyala", "notAvailable": "Tidak tersedia", "inviteUndercover": "Undang untuk bermain Undercover", "userTurnedResult": "{username} memutar meja putar ini. Hasilnya adalah: {result}", "@userTurnedResult": {"type": "string", "placeholders": {"username": {}, "result": {}}}, "civilianVictory": "Orang sipil menang", "civilianDefeat": "Orang sipil kalah", "undercoverVictory": "<PERSON><PERSON><PERSON>", "undercoverDefeat": "<PERSON><PERSON><PERSON>", "customTurntableFaq": "1. <PERSON><PERSON> pemilik dan admin kamar yang dapat men<PERSON>, me<PERSON><PERSON><PERSON> konte<PERSON>, membuka dan menutup meja putar.\n\n2. Pemilik dan admin dapat memutar meja putar. <PERSON>reka juga dapat menetapkan anggota di mikrofon untuk berpartisipasi dalam meja putar.\n\n3. <PERSON><PERSON><PERSON> meja putar dihidupkan, peserta yang dapat memutar meja putar untuk ronde  ini tidak akan berubah sampai dimatikan\n\n4. Saat meja putar berputar, tidak ada yang bisa memutarnya sampai berhenti.", "turntableStartTip": "Anda telah dipilih dan Anda dapat memutar meja putar ini", "noMoreUsersInThisCity": "Tidak ada lagi pengguna di kota ini.", "pleaseTakeMicAndSpeak": "<PERSON><PERSON>an ambil mikrofon dan berb<PERSON>.", "tiePk": "Pertarungan Seri", "noSelectedThisRound": "Tidak ada peserta yang dipilih untuk ronde ini", "homeStatePopTitle": "<PERSON><PERSON><PERSON>mu saat ini", "homeStatePopSubtitle": "Status anda akan hilang dalam 24 jam", "statePublishSuccess": "Status berhasil diposting, akan hilang setelah 24 jam", "now": "Baru saja", "beforeMiuntesAgo": "{count} menit lalu", "@beforeMiuntesAgo": {"type": "string", "placeholders": {"count": {}}}, "beforeHoursAgo": "{num,plural,=0{{num} jam}=1{{num} jam}other{{num} jam}} lalu", "@beforeHoursAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeDaysAgo": "{num,plural,=0{{num} hari}=1{{num} hari}other{{num} hari}} lalu", "@beforeDaysAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeWeeksAgo": "{num,plural,=0{{num} minggu}=1{{num} minggu}other{{num} minggu}} lalu", "@beforeWeeksAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeMonthsAgo": "{num,plural,=0{{num} bulan}=1{{num} bulan}other{{num} bulan}} lalu", "@beforeMonthsAgo": {"type": "string", "placeholders": {"num": {}}}, "beforeYearsAgo": "{num,plural,=0{{num} tahun}=1{{num} tahun}other{{num} tahun}} lalu", "@beforeYearsAgo": {"type": "string", "placeholders": {"num": {}}}, "active": "Aktif", "homeToBottom": "<PERSON><PERSON> bawah", "regWelcome": "Hai, aku Win<PERSON>!\nSaya akan membantu anda menyesuaikan diri dengan cepat dan mencari teman baru di sekitar sini.", "regTestTip": "Winker saat ini sedang dalam pengujian internal, dan untuk menjaga suasana komunitas yang positif, kami telah menyiapkan beberapa pertanyaan selama pendaftaran.\n<PERSON>a dapat mengakses Winker dengan dua cara：\n👇👇🏻👇🏾", "regVerificationPassed": "Selamat🎉🎉🎉, verifi<PERSON>i ber<PERSON>il!", "regInvitationCode": "<PERSON><PERSON>", "regAnswer": "Jawaban Tes", "regRight": "<PERSON><PERSON>", "regAnsNum": "Anda bisa lolos dengan menjawab {count} soal dengan benar.", "@regAnsNum": {"type": "string", "placeholders": {"count": {}}}, "regAnsRemaining": "😃Jawaban benar, cukup jawab {count} per<PERSON>aan lagi dengan benar dan kamu akan lolos.", "@regAnsRemaining": {"type": "string", "placeholders": {"count": {}}}, "regNotPass": "🙁Maaf, anda belum lulus tes ini.", "regPassed": "👏Selamat kamu telah lolos tes, dan sebentar lagi kamu bisa bertemu dengan teman-teman di <PERSON>.", "regCompleteInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kami memb<PERSON> anda untuk memperbaiki informasi pribadi anda.", "regNickname": "<PERSON><PERSON><PERSON> masukkan nama panggilan <PERSON>.", "regNickInputHint": "<PERSON><PERSON><PERSON>n nama panggilan anda", "regNiceNickname": "{name}，<PERSON><PERSON> suka nama ini😊.", "@regNiceNickname": {"type": "string", "placeholders": {"name": {}}}, "regGender": "{name}，silakan pilih jenis kelamin anda", "@regGender": {"type": "string", "placeholders": {"name": {}}}, "regGenderMale": "👱Pria", "regGenderFemale": "👩Wanita", "regGenderSexTip": "<PERSON><PERSON> kelamin tidak dapat diubah setelah dipilih. <PERSON><PERSON> cek kembali bahwa pilihan anda adalah {sex}", "@regGenderSexTip": {"type": "string", "placeholders": {"sex": {}}}, "regBirthday": "{gender}{name}, harap isi tanggal lahir anda agar kami dapat merekomendasikan konten dan teman yang sesuai untuk anda.", "@regBirthday": {"description": "A gendered message", "type": "String", "placeholders": {"gender": {}, "name": {}}}, "regMr": "Tn.", "regMs": "Nn.", "regBirthSame": "🤖Wow, ada {count} teman <PERSON>ker yang memiliki hari ulang tahun yang sama denganmu, mungkin kamu akan menemukan sesuatu yang seru dari mereka.", "@regBirthSame": {"type": "string", "placeholders": {"count": {}}}, "regAvatar": "<PERSON><PERSON>an pilih avatar anda.", "regSuccess": "🎉Selamat telah mendapatkan Aks<PERSON>「{uid} 」. Sekarang kamu bisa masuk ke komunitas Winker dan temukan teman barumu di sini 👇🏻", "@regSuccess": {"type": "string", "placeholders": {"uid": {}}}, "regCardBirthdayTip": "Kunjungan pertama ke Winker", "regAccessWinker": "<PERSON><PERSON><PERSON>", "regMyInvitationCode": "Kode Undangan Saya x", "from": "<PERSON><PERSON>", "to": "<PERSON>", "privateFlights": "Penerbangan pribadi", "invitationCode": "<PERSON><PERSON>", "copyInvitationCode": "<PERSON><PERSON>", "ScreenshotShare": "Berbagi Screenshot", "savedGallery": "Disimpan ke Galeri !", "selectTheme": "<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "brand": "<PERSON><PERSON>", "postWhichTheme": "Posting ke tema pilihan", "customizedThemes": "Tema Custom", "customizedThemeTitlePlaceholder": "Perkenalkan tema baru anda", "customizedThemeSubtitlePlaceholder": "Perken<PERSON><PERSON> deskripsi tema baru anda", "customizedThemeToastTitle": "Titel sudah terisi", "publishTitleHintText": "Masukkan titel", "myThemeSubtitle": "{count} <PERSON><PERSON><PERSON>", "@myThemeSubtitle": {"type": "string", "placeholders": {"count": {}}}, "bug": "Bug", "chatIssue": "<PERSON><PERSON><PERSON>", "suggestions": "Saran", "userIntroEmptyTip": "<PERSON>lis bio untuk memperkenalkan dirimu~", "userAboutMe": "<PERSON><PERSON><PERSON> saya", "userAboutOther": "Tentang {other}", "@userAboutOther": {"type": "string", "placeholders": {"other": {}}}, "aboutOtherInformation": "Tentang informasi {other}", "@aboutOtherInformation": {"type": "string", "placeholders": {"other": {}}}, "his": "dia", "him": "dia", "her": "dia", "heFollowYou": "<PERSON>a mengiku<PERSON>mu", "sheFollowYou": "<PERSON>a mengiku<PERSON>mu", "Unknown": "Tidak Diketahui", "userEmptyPostTip": "Posting untuk mengenal anda lebih baik", "postTipGourmet": "Check in di restoran rekomendasi baru-baru ini", "postTipPet": "Check in di restoran rekomendasi baru-baru ini", "postTipJournal": "Check in di restoran rekomendasi baru-baru ini", "keepPosting": "<PERSON><PERSON> posting", "customizedThemeNameExist": "<PERSON><PERSON> te<PERSON>", "failed": "Gaga<PERSON>", "momentDetailViewProfile": "<PERSON><PERSON>", "editTheme": "<PERSON>", "reportTheme": "<PERSON><PERSON><PERSON>", "he": "dia", "she": "dia", "moveTheme": "<PERSON><PERSON> Tema", "moveToAnotherTheme": "Pindah ke tema lain", "copyInviteCode": "Kode Undangan: {code}. <PERSON>a menggunakan aplikasi sosial baru bernama '<PERSON><PERSON>' dan mengundang anda untuk bergabung dengan kami. Kode undangan saya hanya dapat digunakan satu kali dan jumlahnya terbatas. Ayo gabung secepatnya~Untuk mendownload, silakan klik👉🏻 https://winkerchat.com/", "@copyInviteCode": {"type": "string", "placeholders": {"code": {}}}, "pleaseKindlyComment": "<PERSON><PERSON><PERSON> berk<PERSON>", "newTheme": "<PERSON><PERSON> baru", "myTheme": "<PERSON><PERSON> saya", "winkList": "<PERSON><PERSON>", "statusExpired": "Status sudah kadal<PERSON>, silahkan ubah statusmu", "signInCoins": "Masuk Untuk Menerima <PERSON>", "signInReceived": "Diterima", "signInRewardNumCoins": "Selamat Mendapatkan Koin", "@signInRewardNumCoins": {"type": "string", "placeholders": {"num": {}}}, "sendWink": "<PERSON><PERSON>", "whoWinkAtYou": "<PERSON> kamu", "whoHadWink": "<PERSON><PERSON> yang aku wink", "sayHiBottomTip": "<PERSON><PERSON><PERSON>kan winker akan menarik per<PERSON>ian {sex}, dan jika saling winker dapat memulai percakapan.", "@sayHiBottomTip": {"type": "string", "placeholders": {"sex": {}}}, "sayFreeHasSent": "Telah dikirim", "sayFreeWink": "Wink ke {sex}", "@sayFreeWink": {"type": "string", "placeholders": {"sex": {}}}, "sayFreeWinkRepeat": "<PERSON><PERSON> sudah wink ke {sex}", "@sayFreeWinkRepeat": {"type": "string", "placeholders": {"sex": {}}}, "sayHiGiftSendChat": "<PERSON><PERSON> ke o<PERSON>lan", "thinkWink": "<PERSON><PERSON> kasih atas wink kamu!", "countDetail": "Hitung detailnya", "taskToAchieve": "Mencapai", "gotten": "Didapat", "youWinkedYou": "<PERSON><PERSON> <PERSON> {sex}", "@youWinkedYou": {"type": "string", "placeholders": {"sex": {}}}, "heWinkedYou": "{sex} wink ke kamu", "@heWinkedYou": {"type": "string", "placeholders": {"sex": {}}}, "chatHotTip": "Dia sudah menerima banyak hadiah ini. Ayo coba hadiah lainnya", "rewardedToast": "<PERSON><PERSON><PERSON>", "@rewardedToast": {"type": "string", "placeholders": {"num": {}}}, "wink": "<PERSON><PERSON><PERSON>", "msgDescWink": "[Win<PERSON>]", "homePage": "Be<PERSON><PERSON>", "myWallet": "<PERSON>pet ku", "winkCountOutToast": "<PERSON><PERSON>an winker telah habis.", "locationServiceTipTitle": "Layanan lokasi tidak diaktifkan", "locationServiceTipDes": "Untuk match dengan pengguna terdekat, aktifkan layanan lokasi di pengaturan ponsel anda.", "locationServiceTurnOn": "Aktifkan Lokasi", "homeCountdownTip": "<PERSON><PERSON><PERSON><PERSON> setelah {time}", "@homeCountdownTip": {"type": "string", "placeholders": {"time": {}}}, "discoverMore": "<PERSON><PERSON><PERSON> lebih banyak", "fateBell": "Lonceng Takdir", "fateBellSetting": "Pengaturan Bel Takdir", "openFateBell": "<PERSON><PERSON> Lonceng Takdir", "safeDistanceProtection": "<PERSON><PERSON><PERSON><PERSON> jarak aman", "safeDistanceProtectionDesc": "Tidak match dengan pengguna dalam jarak 500m dari saya", "fateNewMatch": "<PERSON>nceng takdir telah berb<PERSON>yi, ada match baru", "punch": "<PERSON><PERSON>", "punchQuick": "Q&A Undangan", "punchQuestionTitle": "Selamat! Kamu menang! Kamu dapat mengajukan pertanyaan kepada {her}", "@punchQuestionTitle": {"type": "string", "placeholders": {"her": {}}}, "punchGameFrequent": "<PERSON><PERSON><PERSON><PERSON> sering mengundang, harap tunggu sebentar.", "punchGame": "Game Tos", "punchReq": "Mengirim undangan untuk 'Q&A Tebakan Tos' dan menunggu pihak lain untuk bergabung...", "punchReceive": "<PERSON><PERSON> lain menerima undangan 'Q&A Tebakan Tos'.", "punchLost": "😅Kamu kalah, menunggu pihak lain untuk memilih pertanyaan...", "punchJoinTitle": "<PERSON><PERSON><PERSON> bermain <PERSON>", "reportCenter": "Pusat <PERSON>", "dontReminder": "<PERSON><PERSON>", "showInWorld": "<PERSON><PERSON><PERSON><PERSON> di dunia Winker!", "createRoom": "<PERSON><PERSON><PERSON><PERSON>", "partyListEmptyTip": "<PERSON><PERSON>t ruangan dan undang teman untuk mengobrol", "roomModeChat": "Mengobrol", "roomModeCp": "Cp", "roomModeTruthDare": "Jujur & Tantangan", "status": "Status", "allMessages": "<PERSON><PERSON><PERSON>", "friendsMessages": "<PERSON><PERSON>", "roomSettings": "<PERSON><PERSON><PERSON><PERSON> ruangan", "background": "<PERSON><PERSON> be<PERSON>", "minimize": "<PERSON><PERSON><PERSON><PERSON>l", "roomNameInputHint": "<PERSON><PERSON>n nama yang keren pada ruanganmu", "roomAnnounceInputHint": "<PERSON><PERSON><PERSON> orang di ruangan akan melihat pengumuman tersebut", "setPassword": "<PERSON><PERSON><PERSON> kata sandi", "atTa": "at/et", "roomApplyMic": "Mengajukan naik mic", "roomFreeMic": "Bebas ambil mic", "setupAdmin": "Pengaturan admin", "removeAdmin": "Hapus admin", "roomExitTitle": "Meninggal<PERSON> ruangan", "roomExitDes": "<PERSON><PERSON><PERSON><PERSON> anda yakin keluar dari ruangan ini?", "roomCloseAndExit": "<PERSON><PERSON> dan keluar", "roomMemberTabSpeakRequest": "<PERSON><PERSON><PERSON><PERSON> berb<PERSON>", "roomMemberTabOnline": "Aktif", "roomMemberTabAdmin": "Admin", "roomMemberTabBlocked": "Diblokir", "remove": "Hapus", "onMic": "Di <PERSON>", "roomWelcome": "Selamat datang!!", "followedYou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFollowedYou": "sudah mengikutimu", "stay1minInvite": "Dia sudah masuk ruangan beberapa menit, ajak dia ngobrol di mic", "stay2minFollow": "Kita sudah mengobrol sebentar. Ikuti aku dan kamu akan mendapat notifikasi jika kamu membuat ruangan di masa mendatang.", "invitationShort": "Undangan", "downMic": "<PERSON><PERSON> dari mic", "applyTakeMicSuccess": "a<PERSON><PERSON> agar berhasil mengambil mic.", "roomMsgRoomName": "<PERSON><PERSON> r<PERSON> di<PERSON>h men<PERSON>:{roomName}", "@roomMsgRoomName": {"type": "string", "placeholders": {"roomName": {}}}, "editProfile": "Edit Profil", "desYour": "Deskripsik<PERSON> dirimu", "alreadyAdded": "Sudah ditambahkan", "interestDesTip": "Pilih label untuk menampilkan dirimu dengan cepat.", "prev": "Sebelumnya", "voiceVerifyDesc": "<PERSON><PERSON> pengguna yang telah lulus verifikasi suara yang dapat mendengarkan suara satu sama lain.", "goForVerification": "Klik untuk verifikasi", "voiceVerified": "<PERSON><PERSON> te<PERSON>", "sayHiLimitTitle": "<PERSON>as sapa harian tercapai ({num}/Hari).\n<PERSON><PERSON> lebih banyak percakapan dengan mengirimkan hadiah", "@sayHiLimitTitle": {"type": "string", "placeholders": {"num": {}}}, "sayHiSubtitle": "Batasan ini mendorong pilihan yang bijaksana. Peluang baru tersedia setelah beberapa saat", "editProfileLivingTitle": "<PERSON><PERSON>h daerah tempatmu tinggal", "editProfileLivingDes": "Sistem akan merekomendasikan pengguna terdekat untukmu.", "mainCountry": "Indonesia", "overseasCountries": "Negara/Wilayah Luar Negeri", "maxSelectOption": "<PERSON><PERSON> dapat menambahkan maksimal {num} tag", "@maxSelectOption": {"type": "string", "placeholders": {"num": {}}}, "minSelectOption": "Harap tambahkan minimal {num} tag", "@minSelectOption": {"type": "string", "placeholders": {"num": {}}}, "talkSomething": "Mengobrol tentang sesuatu…", "tryWriting": "<PERSON><PERSON><PERSON> seperti ini", "familyTip": "<PERSON><PERSON>, sekarang sedang bekerja dan hidup sebagai anak tunggal dari keluarga sederhana. Lingkaran pertemananku cukup sempit jadi aku berharap bisa bertemu crushku di sini.", "academic": "Akademik", "academicTip": "Aku kuliah di Universitas Indonesia dengan prestasi akademik yang baik.", "work": "<PERSON><PERSON><PERSON>", "workTip": "Aku baru saja mulai bisnisku sendiri dan membuka toko teh susu Honey Snow Ice City.", "icebreakerNewbie": "{he} adalah pendatang baru dan belum memiliki banyak teman. Ayo sapa {him}.", "@icebreakerNewbie": {"type": "string", "placeholders": {"he": {}, "him": {}}}, "viewAll": "<PERSON><PERSON>a", "voiceVerifyChat1": "<PERSON>", "voiceVerifyChat2": "<PERSON>gai<PERSON> kabarmu?", "voiceVerifyChat3": "<PERSON><PERSON>mu terdengar merdu!", "userUpdateStatus": "Status diperbarui: {text}", "@userUpdateStatus": {"type": "string", "placeholders": {"text": {}}}, "sayHiTitle": "{She} sangat populer, berikan hadiah untuk {her} agar ia merasakan ketulusanmu.", "@sayHiTitle": {"type": "string", "placeholders": {"She": {}, "her": {}}}, "distanceTag": "Jarak {num}km", "@distanceTag": {"type": "string", "placeholders": {"num": {}}}, "stayTimeTag": "Tetap di ruangan selama {num}", "@stayTimeTag": {"type": "string", "placeholders": {"num": {}}}, "hour": "Jam", "minute": "Menit", "blockedTime": "<PERSON><PERSON><PERSON> blo<PERSON>r{time}", "@blockedTime": {"type": "string", "placeholders": {"time": {}}}, "chatRoomInvite": "Undang kamu ke pesta", "micApplyNotice": "Terapkan pemberitahuan", "toLater": "<PERSON><PERSON>", "invitationToMic": "Undangan naik <PERSON>", "inviteToMicMessage": "Mengundangmu bergabung pada obrolan", "shareTheRoom": "Membagikan ruangan.", "requestSend": "Terkirim", "listener": "<PERSON><PERSON><PERSON>", "lessThanOneMinute": "< 1 menit", "otherViewers": "{num} p<PERSON><PERSON>a la<PERSON>ya", "@otherViewers": {"type": "string", "placeholders": {"num": {}}}, "punish": "<PERSON><PERSON><PERSON>", "punishSelect": "<PERSON><PERSON><PERSON>", "punishLevel": "Level <PERSON><PERSON>man", "punishSilentRemaining": "<PERSON><PERSON> telah dilarang berbicara di layar publik oleh administrator", "notRecommended": "Tidak direkomendasikan", "doubleConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> ganda", "doubleConfirmationDes": "Apakah kamu yakin ingin melakukan ini?", "labels": "Label", "reset": "<PERSON><PERSON>", "defaultRoomName": "<PERSON><PERSON><PERSON> {host}", "@defaultRoomName": {"type": "string", "placeholders": {"host": {}}}, "defaultRoomAnnounce": "Selamat datang di ruanganku, tempat kamu dapat berbicara dengan bebas<PERSON>", "shareToOthers": "Bagikan ke orang lain", "link": "Tautan", "roomType": "<PERSON><PERSON>", "truthStart": "MULAI", "truthOngoing": "Process", "truthSelected": "<PERSON><PERSON><PERSON><PERSON>", "truthGameIntroTitle1": "Bersiaplah untuk naik ke mic.", "truthGameIntroSubtitle1": "Pengguna dianggap telah bergabung dalam game setelah mereka naik ke mic.", "truthGameIntroTitle2": "<PERSON><PERSON> be<PERSON>.", "truthGameIntroSubtitle2": "<PERSON>at roda be<PERSON>, semua pemain yang terlibat dalam permainan memiliki peluang untuk dipilih, se<PERSON><PERSON><PERSON> secara acak.", "truthGameIntroTitle3": "<PERSON><PERSON><PERSON>", "truthGameIntroSubtitle3": "<PERSON><PERSON>in yang dipilih, harus menyelesaikan Truth or Dare, yang diberikan secara acak oleh sistem. Tema yang dipilih harus diselesaikan oleh pemain yang dipilih.", "truthStartLimit": "Setidaknya harus ada dua orang atau lebih bergabung dalam permainan", "finish": "Se<PERSON><PERSON>", "roomReEnterErrTip": "[<PERSON><PERSON>：{code}]Silakan coba keluar dari ruangan dan masuk kembali.", "@roomReEnterErrTip": {"type": "string", "placeholders": {"code": {}}}, "roomSettingMusic": "Mu<PERSON>", "roomSettingClearScreen": "<PERSON><PERSON><PERSON> layar", "backgroundMusic": "Latarbelakang musik", "profileChattingInRoom": "{she} sedang mengobrol di ruang<PERSON>~", "@profileChattingInRoom": {"type": "string", "placeholders": {"she": {}}}, "peopleChattingNow": "Orang-orang mengo<PERSON>l sekarang", "downloadingToLater": "<PERSON><PERSON>, silahkan coba lagi", "iHere": "Hai, aku disini!", "sendTo": "<PERSON><PERSON> ke", "goldTask": "Tugas Gold", "diamond": "<PERSON><PERSON><PERSON>", "rechargeTip": "<PERSON><PERSON><PERSON> pilih jumlah isi ulang:", "diamondRecharger": "<PERSON><PERSON>", "insufficientBalance": "Saldo tidak men<PERSON>", "insufficientBalanceTip": "(<PERSON><PERSON><PERSON> {remaining} diamond untuk dibeli)", "@insufficientBalanceTip": {"type": "string", "placeholders": {"remaining": {}}}, "comboBtnTextTop": "<PERSON><PERSON><PERSON> untuk", "comboBtnTextBottom": "Kombo", "popularityRank": "<PERSON><PERSON><PERSON>", "permanent": "<PERSON><PERSON><PERSON>", "myRankPlace": "Kekurangan posisi terakhir: {sum}", "@myRankPlace": {"type": "string", "placeholders": {"sum": {}}}, "lastUpdateTime": "<PERSON><PERSON><PERSON> pembaruan terakhir: {time}", "@lastUpdateTime": {"type": "string", "placeholders": {"time": {}}}, "rechargeNotOpen": "<PERSON>anan isi ulang dalam APP belum dibuka.", "onlineMostTip": "Tampilkan maksimal 50 pengguna online.", "package": "<PERSON><PERSON>", "expireTime": "· <PERSON><PERSON> be<PERSON> pada {time}", "@expireTime": {"type": "string", "placeholders": {"time": {}}}, "giftFloatScreen": "{a} memberikan {b} sebuah {c}", "@giftFloatScreen": {"type": "string", "placeholders": {"a": {}, "b": {}, "c": {}}}, "popularityList": "<PERSON><PERSON>", "favoritesList": "<PERSON><PERSON>", "coupleList": "Daftar pasangan", "favoritesRank": "<PERSON><PERSON><PERSON>", "listDescription": "<PERSON><PERSON><PERSON>", "rank1st": "1", "rank2nd": "2", "rank3rd": "3", "rankGlobalBanner": "{user} menjadi {rankName} top {order}", "@rankGlobalBanner": {"type": "string", "placeholders": {"user": {}, "rankName": {}, "order": {}}}, "view": "Lihat", "opponentGets": "<PERSON><PERSON> men<PERSON>", "youGet": "<PERSON><PERSON>", "tryBtn": "Coba", "probabilityGiftTitle": "Selamat atas keluarnya hadiah <PERSON>：", "probabilityGiftWinTip": "<PERSON>a akan mendapa<PERSON> dalam {num} undian lagi", "@probabilityGiftWinTip": {"type": "string", "placeholders": {"num": {}}}, "homeFateBellTitle": "Cari match", "homeFateBellStatusNormalTitle": "Cocok", "homeChatPartySubtitle": "Mengobrol Seru", "homeTruthDareSubtitle": "Temu<PERSON> teman baru", "chatParty": "Pesta Suara", "homeBlindDateStatusNormalTitle": "Ikut", "nearbyNoSettingTitle": "<PERSON><PERSON><PERSON> berada tidak di<PERSON>.", "nearbyNoSettingDes": "Aktifkan akses lokasi dan Win<PERSON> akan merekomendasikan orang yang tepat disekitarmu", "openPositioning": "Po<PERSON>i terbuka", "usingLivingArea": "Menggunakan area tinggal", "toChoose": "<PERSON><PERSON><PERSON><PERSON>", "proxyErr": "Harap untuk memastikan kamu tidak menggunakan proxy.", "noGps": "Gagal mendapatkan lokasi", "noGpsRec": "Tidak dapat merekomendasikan orang terdekat untukmu", "locate": "<PERSON><PERSON><PERSON><PERSON>", "switchRoomTitle": "Pi<PERSON><PERSON>", "intimateRanking": "<PERSON><PERSON><PERSON>", "clientError": "<PERSON><PERSON><PERSON> sisi klien", "rewardStatistics": "<PERSON><PERSON>", "remaining": "<PERSON><PERSON>\n{time}", "@remaining": {"type": "string", "placeholders": {"time": {}}}, "rewardStEnd": "<PERSON><PERSON><PERSON><PERSON> hadiah be<PERSON>hir", "topIndex": "Peringkat{index}:", "@topIndex": {"type": "string", "placeholders": {"index": {}}}, "intimacyLevelIncreasedTo": "Level Keintiman naik ke", "partyDur": "Durasi pesta: {time}", "@partyDur": {"type": "string", "placeholders": {"time": {}}}, "giftAmount": "<PERSON><PERSON><PERSON> hadiah: {amount}", "@giftAmount": {"type": "string", "placeholders": {"amount": {}}}, "intimacyTip": "Saat ini tidak ada kandidat yang tersedia. <PERSON><PERSON> pengh<PERSON>an kepada pihak lain dapat meningkatkan keintiman.", "receiveDiamonds": "Terima diamonds", "seeMoreReplies": "Ko<PERSON>n yang dilaporkan telah disembunyikan", "introduce": "Perkenalan", "interact": "<PERSON><PERSON><PERSON>", "unburden": "Ungkapkan", "choose": "<PERSON><PERSON><PERSON>", "waitingStart": "<PERSON>unggu untuk bermain！", "pleaseChooseLove": "<PERSON><PERSON><PERSON> pilih jawaban anda", "intervalSessionsShort": "Interval antar sesi terlalu pendek", "text": "Teks", "giftNewerGuideTip": "Anda baru saja mendapat hadiah ini, berikan kepada tuan rumah yang Anda suka ~", "incredible": "Luar biasa!", "failureMatching": "Kegagalan Pencocokan", "rewardToGet": "<PERSON> hadiah yang harus diklaim", "submitRepeated": "<PERSON><PERSON> jangan men<PERSON>mkan permohonan berulang kali", "titleList": "<PERSON><PERSON><PERSON> judul", "effectPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "youHadGot": "{you} telah mendapat", "@youHadGot": {"type": "string", "placeholders": {"you": {}}}, "youHaveNotGot": "Anda belum mendapatkan", "atUserName": "@Nama User", "titleEffectPreviewMsgContent": "Selamat datang di keluarga.", "awaitingWear": "<PERSON>ung<PERSON> keausan", "fansGroupTabTitleHostBenefits": "Keuntungan Host", "fansGroupTabTitleFansList": "Daftar Fans", "fansGroupTotalNum": "Total: {sum} orang", "fansGroupTabTitleTreasureRecord": "Catatan <PERSON>", "treasureChestNum": "Nomor peti harta karun: ", "treasureChestTips": "Semakin banyak peti harta karun yang diterima fans, semakin banyak hadiah luar biasa yang akan diterima kalian sebagai Host!", "treasureChestRecord": "Catatan <PERSON>", "treasureChestItemTips": "<PERSON>u saja menerima peti harta karun", "joinFanGroup": "Bergabung dengan grup fans", "fanClubList": "daftar klub fans", "whos": "<PERSON><PERSON> {who}", "@whos": {"type": "string", "placeholders": {"who": {}}}, "totalPeople": "Total: {sum} orang", "@totalPeople": {"type": "string", "placeholders": {"sum": {}}}, "popularity": "Popularitas", "joinFanClubSuc": "Berhasil bergabung dengan klub fans!", "no1": "Nomor 1", "rewardDetail": "Detail hadiah", "lvUnLock": "Membuka Lv.{num}", "@lvUnLock": {"type": "string", "placeholders": {"num": {}}}, "JoinFanGroup": "Bergabung dengan grup fans", "drawFanNumReward": "Mengundi {num} sekali", "fansGroupTips": "<PERSON>i dan men<PERSON>ma hadiah mening<PERSON>kan pengalaman.", "getDrawNum": "<PERSON><PERSON> j<PERSON>ian", "viewRules": "<PERSON><PERSON>", "quitGroup": "<PERSON><PERSON><PERSON> grup", "noLongerInRoom": "<PERSON>mu tidak lagi berada di dalam ruangan", "fanLevelUpgrade": "Level fansmu telah ditingkatkan ke {num} !", "@fanLevelUpgrade": {"type": "string", "placeholders": {"num": {}}}, "fansNoDrawTimesTip": "Cek dan raih kesempatan dengan menyelesaikan tugas", "fansCongratulationUserLevelMsg": "Selamat! {username} Level klub fans telah dinaikkan menjadi lv.{num}!", "joinedTheFanGroup": "bergabung dengan grup fans", "exitFansGroupTitle": "Pengingat Keluar", "exitFansGroupTipsContent": "<PERSON><PERSON><PERSON>, aku<PERSON>lasi poin popularitas dan hak istimewa akan diatur ulang ke nol, <PERSON><PERSON> kamu yakin ingin melakukan operasi ini?", "exitFansGroupTipsConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitFansGroupTipsThinkAgain": "Pikirkan lagi", "time": "<PERSON><PERSON><PERSON>", "check": "Cek", "giftFloatScreenByLottery": "{a} won {b} dari klub kamar ~", "inDraw": "da<PERSON> undian", "redPackTitle": "Bonus panas dipicu", "followAndJoinTheFanClub": "<PERSON><PERSON><PERSON> dan gabunglah dengan klub fans", "rush": "<PERSON><PERSON>", "redPackResultGetMessage": "<PERSON><PERSON> mengambil dari angpao saat ini", "redPackResultTip": "Ingat! Semakin cepat kecepatan tanganmu, semakin banyak diamond yang akan kamu dapatkan!", "redPackResultEmptyTip": "<PERSON><PERSON><PERSON> kosong, tanganmu tidak cukup cepat! Lebih cepat lain kali!", "wealthCharmLevelUpTitle": "Selamat atas peningkatanmu!", "redPackExpired": "Amplop Angpao telah ked<PERSON>", "heat": "Panas", "privileged": "<PERSON><PERSON>", "pokedYou": "memanggilmu!", "respondWithGift": "<PERSON><PERSON><PERSON> dengan hadiah", "redPackDetail": "Dapatkan detail angpao", "redPackGet": "Cek", "thisWeek": "<PERSON><PERSON> ini", "lastWeek": "<PERSON><PERSON>", "intimacyLowerCase": "kein<PERSON>man", "rankList": "<PERSON><PERSON><PERSON>", "followHostNow": "Ikuti host se<PERSON><PERSON>", "inviteJoinRoomClub": "Undangan untuk bergabung ke klub ruangan!", "joinRoomClub": "Bergabung dengan klub ruangan", "clubTitleSelect": "Pilih nama klub", "clubSelectSecondTitle": "Klub ruangan tempatmu bergabung", "clubSelectContent": "<PERSON> ini, kamu bukan bagian dari klub ruangan, kamu dapat menampilkan penggemar yang ada.", "roomFansNameModifyTip": "Nama klub ruangan diizinkan untuk diubah setiap {sum} Hari sekali", "changeFanClubName": "Mengubah nama klub penggemar", "roomFanClubNameLimit": "{min}~{max} huruf/angka", "roomFanClubNameLimitTitle": "Persyaratan <PERSON>yiapan", "roomFanClubNameLimitContent": "· Batas karakter untuk nama grup penggemar: 2 ~ 6 huruf / angka, karakter lain tidak didukung\n· Nama grup penggemar tidak boleh menggunakan kata-kata sensitif seperti pornografi atau pornoaksi, tidak boleh memfitnah atau menyamar sebagai orang lain, dan tidak boleh menggunakan nama official.", "wordCountExceeded": "<PERSON><PERSON><PERSON> kata terl<PERSON><PERSON>i", "wordCountInsufficient": "<PERSON><PERSON><PERSON> kata tidak mencu<PERSON>pi", "roomFansUpdateTip": "Tugas akan diatur ulang setiap hari pada pukul 00:00.", "currentIntimacy": "<PERSON><PERSON><PERSON> In<PERSON>", "chatIntimacyFeatureDes": "<PERSON><PERSON> hadiah dapat mening<PERSON>kan keintiman. <PERSON><PERSON><PERSON> yang lebih tinggi akan membuka lebih banyak fitur interaktif", "chatIntimacyUnlockNum": "<PERSON><PERSON><PERSON> {num} <PERSON><PERSON> kunci keintiman", "chatIntimacyUnlockMsgTip": "Selamat telah membuka kunci {name} fitur! Alami sekarang dan bawa diri Anda lebih dekat dengan orang lain!", "chatReplayRewardMsgTip1": "Balas pesan sekarang untuk mendapatkan", "chatReplayRewardMsgTip2": "hadiah dari menerima hadiah.", "howGetIntimacy": "Cara mendapa<PERSON><PERSON>", "chatPerIntimacyIncrease": "Chatting bisa mendapatkan keintiman (+{num} ）", "sendGiftGetIntimacy": "<PERSON><PERSON> hadiah untuk mendapatkan keintiman", "chatIntimacyInfoFemale": "Saat nilai keintiman diperoleh, nilai cahaya bintang juga akan diperoleh", "chatNoMoneyTaskTitle": "Diamond telah digunakan hari ini. Kamu bisa mendapatkannya dengan cara-cara berikut.", "freeToGet": "Bebas ambil mic", "goToRecharge": "<PERSON>gi ke isi ulang", "youSendGift": "<PERSON>a men<PERSON>m [{n}]", "sendYouGift": "Mengirim Anda [{n}]", "unlockFeatureDigest": "[<PERSON><PERSON> kunci fitur]", "incomeTasks": "Tugas pendapatan", "accumulatedStarlight": "Akumulasi starlight", "myIncome": "Pendapatan Saya", "moments": "<PERSON><PERSON>", "myLevel": "Level Saya", "intimacyRank": "<PERSON><PERSON><PERSON>", "stageRewards": "<PERSON><PERSON><PERSON>", "issued": "Dikeluarkan", "incomplete": "Tidak Lengkap", "diamondTask": "Tugas Diamond", "unlocked": "<PERSON><PERSON> kunci", "accumulated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starlightRewardNum": "{num} starlight berhasil diterima", "diamondRewardNum": "{num} diamond berhasil diterima", "income": "Pendapatan", "personalIncome": "Pendapatan Pribadi", "unionIncome": "Pendapatan Agen", "exchangeToDiamond": "Tukarkan ke Diamond", "numOfStarlight": "<PERSON><PERSON><PERSON>", "convertStarlight": "Starlight Konvertibel:{num}", "exchangeAll": "<PERSON><PERSON><PERSON> semua", "exchangeNumDiamond": "<PERSON><PERSON><PERSON><PERSON> {n} <PERSON><PERSON><PERSON>.", "exchangeDiamonds": "Tukarkan Diamond", "accumulatedSubmit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskRewardTitle": "Hadiah gratis! <PERSON><PERSON>ai<PERSON> tugas sekarang untuk dapatkan!", "goToGet": "Lanjut untuk dapatkan", "openBluetoothTitle": "<PERSON>uka pengaturan Bluetooth", "openBluetoothDesc": "Aktifkan pengaturan Bluetooth dan selesaikan tugas untuk mendapatkan hadiah gratis.", "exchangeStarlight": "<PERSON><PERSON> dengan Starlight", "signReceiveRewards": "<PERSON><PERSON><PERSON> untuk mendapatkan hadiah", "applyingJoin": "Mendaftar", "levelMax": "Level Maksimal", "applyJoinAgency": "Mendaftar ke Agensi", "email": "email", "note": "Catatan", "applyAgencySubmit": "Aplikasi telah diajukan dan sedang menunggu peninjauan agensi", "validEmailTip": "Silakan masukkan format email dengan benar.", "agencyContactTitle": "<PERSON><PERSON><PERSON> kami, dan bekerja sama dengan kami.", "copyNumber": "<PERSON><PERSON>", "agencyNameId": "· Agensi: {name} (ID:{id})", "sTypeAudioLabel": "<PERSON><PERSON>", "sTypeImageLabel": "Gambar", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Sumber daya lainnya", "sActionPlayHint": "<PERSON><PERSON><PERSON>", "sActionPreviewHint": "<PERSON>rat<PERSON><PERSON><PERSON>", "sActionSelectHint": "<PERSON><PERSON><PERSON>", "sActionSwitchPathLabel": "<PERSON><PERSON><PERSON>", "sActionUseCameraHint": "<PERSON><PERSON><PERSON>", "sNameDurationLabel": "<PERSON><PERSON><PERSON>", "sUnitAssetCountLabel": "hitung", "chattingNow": "Mengobrol <PERSON>ng", "giftGallery": "<PERSON><PERSON> hadiah", "lightingGift": "<PERSON><PERSON><PERSON> {num} <PERSON><PERSON>", "giftGalleryListSubtitle": "<PERSON><PERSON> akan naik ketika kamu mengirim jumlah total yang diperlukan.", "noTitleGifters": "Tidak ada titel pemberi hadiah", "titleGifters": "Titel pemberi hadiah", "getTitle": "Titel", "gitGalleryRankTitle": "3 Pemberi Hadiah <PERSON>", "gitGalleryRankSubtitle": "<PERSON><PERSON> hadiah ini naik, No.1 pemberi hadiah akan diakui titel pemberi hadiahnya.", "giftGalleryIntroductionTitle": "<PERSON><PERSON><PERSON><PERSON>", "giftGalleryIntroIlluminationSectionTitle": "<PERSON><PERSON><PERSON>", "giftGalleryIntroIlluminationRuleContent": "1. <PERSON><PERSON>eran Hadiah adalah fitur gameplay interaktif di ruang suara. Ketika kamu menerima jumlah hadiah yang ditentukan, kamu dapat menaikkan hadiah yang sesuai (jumlah hadiah tertentu yang diperlukan tergantung pada informasi di halaman). <PERSON><PERSON> gambar hadiah untuk melihat detail papan peringkat pemberi hadiah.\n\n2. Papan peringkat pemberi hadiah untuk hadiah individu hanya akan menampilkan tiga pengguna teratas. Dengan memberikan jumlah hadiah yang ditentukan, kamu dapat memiliki hak penamaan untuk hadiah tersebut (rentang tampilan: terbatas pada dinding hadiah pengguna penerima).\n\n3. <PERSON><PERSON> di ruang pameran akan diperbarui secara teratur. Hadiah yang baru ditambahkan akan ditampilkan secara bersamaan di dinding hadiah. Memberikan jumlah hadiah baru yang ditentukan juga dapat menaikkan mereka.\n", "giftGalleryIntroNameSectionTitle": "<PERSON><PERSON><PERSON>", "giftGalleryIntroNameRuleContent": "1. <PERSON><PERSON><PERSON> hadiah din<PERSON><PERSON><PERSON>, pengguna yang menempati peringkat pertama di papan peringkat pemberian hadiah dapat memiliki hak penamaan untuk hadiah tersebut. Gambar profil pengguna peringkat teratas akan ditampilkan di bawah tugas hadiah tersebut, dan gambar profil akan diperbarui secara real-time. <PERSON><PERSON> beberapa pengguna telah memberikan jumlah hadiah yang sama, pengguna yang memberikan hadiah terlebih dahulu akan dianggap sebagai pengguna peringkat teratas.\n\n2. <PERSON><PERSON>h pengguna berhasil memberi nama hadiah, pemberitahuan layar publik akan muncul di ruangan tempat penerima hadiah saat ini berada.\n\n3. Data untuk fitur gameplay ini akan direset pada pukul 00:00 setiap tanggal 1 setiap bulannya. Pada saat itu, hadiah yang naik akan kembali ke keadaan normal, dan status penamaan pengguna akan diatur ulang.", "consumptionReminder": "Pengingat konsumsi", "consumptionReminderContent": "<PERSON><PERSON><PERSON>h kamu ingin mengh<PERSON>n {n} diamond untuk menaikkan hadiah ini ke {sex}?", "thinkAgain": "Pikirkan lagi", "noRemindMeAgain": "<PERSON>an ingatkan lagi", "nextOne": "Berikutnya", "matchingHqUsers": "Mencocokkan pengguna berkualitas tinggi", "moveThemeSucc": "<PERSON><PERSON><PERSON><PERSON> disimpan", "homeLudoSubtitle": "Permainan papan paling populer", "enteredDays": "{num} telah dim<PERSON>", "noMoreSeat": "Tidak ada kursi tambahan", "cannotKickMicInPlaying": "<PERSON><PERSON><PERSON> da<PERSON> game tidak bisa ditendang keluar", "cannotDownMicInPlaying": "<PERSON><PERSON><PERSON> da<PERSON> game tidak bisa ditendang keluar", "cannotDownMicCaptain": "Kapten dalam game tidak bisa down mic", "changeRoomModeTitle": "Mengubah Tipe <PERSON>", "changeModeContent": "Anda harus mengakhiri permainan saat ini untuk melakukan operasi ini. Apakah Anda ingin mengakhiri permainan?", "endGame": "<PERSON><PERSON><PERSON>", "clashEmpire": "<PERSON><PERSON>", "upgradeRole": "<PERSON><PERSON><PERSON>", "empirePropBuff": "BUFF: Meningkatkan Kekuatan", "empirePropCamel": "Memanggil Unta Tempur", "empirePropDragon": "Memanggil Naga Terkuat", "score": "<PERSON><PERSON>", "skillCD": "<PERSON><PERSON>, silakan coba lagi nanti", "addScore": "Skor Audisi", "our": "<PERSON><PERSON>", "changeCampFailed": "Gagal bergabung dengan kamp, tidak dapat mengubah kamp", "sendGiftFailNoCamp": "Tidak bergabung dengan kamp, tidak dapat mengirim hadiah", "cocosGameLoadFailed": "Kegagalan pemuatan game!", "actNow": "Tidak se<PERSON>", "giftLightUpRank": "<PERSON><PERSON> naik pang<PERSON>", "privatePhotos": "Foto pribadi", "honour": "<PERSON><PERSON><PERSON>", "kindTips": "<PERSON><PERSON><PERSON> jenis", "privatePhotoTipHelp": "Anda dapat melihat foto pribadi satu sama lain hanya ketika keintiman Anda mencapai 10~.Obrolan teks dan hadiah dapat meningkatkan keintiman Anda", "pictureUploaded": "<PERSON><PERSON><PERSON> te<PERSON>", "uploadFailed": "Upload gagal, coba lagi nanti", "gameCenter": "Pusat Game", "jackPotTips": "Menyelesaikan tugas mendapatkan poin, yang dapat ditukarkan dengan hadiah.", "congratulationsToGotten": "Selamat untuk {userName} atas {giftName}", "myIntegrations": "<PERSON>te<PERSON><PERSON> saya", "jackPot": "Jackpot", "rank": "Ranking", "gameNotFound": "Game tidak ditemukan!", "dailyList": "<PERSON><PERSON><PERSON>", "weeklyList": "<PERSON><PERSON><PERSON>", "gameCenterRule": "Aturan P<PERSON>t Game", "gameCenterRuleTitle1": "1 Bagaimana cara memainkan undian ber<PERSON><PERSON> 1 poin?", "gameCenterRuleContent1": "<PERSON><PERSON><PERSON> yang memenuhi persyaratan yang diperlukan dalam permainan akan memiliki kesempatan untuk menerima hadiah tugas - poin. <PERSON><PERSON>n poin untuk undian berhadiah, dengan tingkat kemenangan 100%!", "gameCenterRuleTitle2": "2 Bagaimana jumlah hadiah undian berhadiah 1 poin di<PERSON>ung?", "gameCenterRuleContent2": "Gunakan 10 poin untuk satu undian dengan tingkat kemenangan 100%!", "gameCenterRuleTitle3": "3 Bagaimana peringkat dihitung?", "gameCenterRuleContent3": "Pemeringkatan disusun dalam urutan menurun berdasarkan koin yang saat ini dimenangkan oleh pengguna di semua game.", "gameCenterRuleTitle4": "4 Bagaimana daftar peringkat diperbarui?", "gameCenterRuleContent4": "Daftar peringkat dibagi menjadi peringkat harian dan mingguan. Peringkat harian akan diatur ulang pada pukul 00:00 setiap hari; peringkat mingguan akan diatur ulang pada pukul 00:00 setiap hari <PERSON>.", "gameCenterRuleTitle5": "5 Apa<PERSON>h ada hadiah untuk daftar peringkat?", "gameCenterRuleContent5": "<PERSON>, se<PERSON>a penye<PERSON>n peringkat mingguan, bingkai avatar yang sesuai akan diberikan kepada tiga pengguna teratas dalam daftar. Waktu distribusi hadiah: 1:00 pagi pada hari <PERSON>.", "waitingForDrawing": "Menunggu pengundian terakhir berakhir", "congNameToGift": " telah menang", "congOnWinning": "Selamat atas per<PERSON>hannya", "moreTasks": "<PERSON><PERSON> la<PERSON>ya", "rateApp": "<PERSON><PERSON>an beri peringkat aplikasi kami!", "imageSizeLimit": "Foto tidak bisa lebih dari ukuran yang sudah ditentukan", "@imageSizeLimit": {"type": "int", "placeholders": {"size": {}}}, "youDontHaveTheAccessToUploadGifPicture": "Anda tidak mempunyai akses untuk meng-upload foto gif", "album": "Album", "riskyTips": "Informasi ini mengandung risiko; waspada terhadap penipuan.", "roomCover": "<PERSON><PERSON><PERSON>", "roomWillCloseTips": "<PERSON><PERSON> Anda tidak aktif di ruang dalam waktu yang lama, r<PERSON>a akan otomatis ditutup dalam {time} detik.", "@roomWillCloseTips": {"type": "int", "placeholders": {"time": {}}}, "roomClosedDueToProlonged": "<PERSON><PERSON> telah ditutup karena tidak ada yang aktif dalam waktu yang lama.", "forceClosedTips": "<PERSON><PERSON><PERSON> aturan komunitas, ruangan ini telah ditutup.", "imageTooSmall": "Ukuran gambar yang dipilih terlalu kecil.", "quit": "<PERSON><PERSON><PERSON><PERSON>", "automaticStartWhenFull": "<PERSON><PERSON> otom<PERSON> ketika penuh", "waitingForOtherPlayersToSettleGame": "<PERSON><PERSON><PERSON> pemain lain menye<PERSON><PERSON><PERSON> permainan", "theMicAreFullUnableToJoinTheGame": "<PERSON><PERSON><PERSON> penuh, tidak bisa ikut game", "theGamePositionIsFull": "Posisi game sudah penuh", "theHostJustKickedYouOutOfTheGamePlease": "<PERSON><PERSON> rumah baru saja mengeluarkan Anda dari perma<PERSON>n, coba lagi nanti", "loadGameFailed": "{game} gagal dimuat, silakan coba lagi", "baloot": "Baloot", "domino": "Domino", "candy": "<PERSON>", "theGamePositionIsLocked": "Posisi game terkunci", "theRoomOwnerEndsTheGame": "Pemilik r<PERSON>an menga<PERSON> permainan.", "myCustomers": "Pelang<PERSON> Say<PERSON>", "noConversation": "Tidak ada perca<PERSON>pan", "diamondBalance": "Saldo Diamond", "amountToExchange": "<PERSON><PERSON><PERSON><PERSON> jumlah yang akan ditukar", "numberOfDiamonds": "<PERSON><PERSON><PERSON>", "doYouWantToSpend": "A<PERSON><PERSON>h kamu ingin mengh<PERSON>?", "toChange": "untuk mengubah", "containInvaildCharacters": "Mung<PERSON> mengandung karakter khusus se<PERSON>i “ ' / <>. <PERSON><PERSON><PERSON> coba yang lain...", "chatConsumes": "Mengirim pesan menghabiskan", "or": "atau", "toAddIntimacy": "untuk menambah keintiman", "chatConsumeGoldCoin": "Konsumsi gold coin terle<PERSON><PERSON> dahulu, jika gold coin kurang konsumsi diamond", "normal": "Biasa", "quickly": "<PERSON>gan cepat", "ticket": "<PERSON><PERSON><PERSON>", "theNicknameYouEnteredIsTooShort": "<PERSON><PERSON><PERSON> yang <PERSON>a masukkan terlalu pendek", "doYouWantToKickThisUserOutOfThe": "Ingin mengeluarkan pengguna ini dari game?", "dontRemindMeToday": "<PERSON>an ingatkan aku hari ini", "cannotExitRoomDuringGame": "Anda tidak dapat menutup ruangan selama permainan berl<PERSON>g, tetapi <PERSON>a dapat memilih untuk meninggalkan ruangan.", "transferCaptain": "Kapten Transfer", "doYouWantToTransferUserAsTheGameCaptain": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mentransfer {user} sebagai kapten permainan?", "@doYouWantToTransferUserAsTheGameCaptain": {"type": "string", "placeholders": {"user": {}}}, "recruit": "<PERSON><PERSON><PERSON><PERSON>", "inviteRoomOnlineUsers": "Pengguna Ruang Rekrutmen Online", "inviteRoomOnlineEmpty": "Tidak ada pengguna online lain di ruangan tersebut", "inviteRoomOnlineAlertTitle": "{username} mengundang Anda untuk bergabung dalam permainan dan bermain.", "@inviteRoomOnlineAlertTitle": {"type": "string", "placeholders": {"username": {}}}, "userJoinedTheGame": "Pengguna telah bergabung dalam permainan", "inviteFriends": "Undang teman", "noFriendsOnline": "Tidak ada teman online", "friendsYouMayKnow": "<PERSON><PERSON> yang mungkin <PERSON>a kenal", "playingVoiceRoom": "<PERSON>uang suara", "playingGame": "Main {game}", "@playingGame": {"type": "string", "placeholders": {"game": {}}}, "inGameTag": "Main {game}", "inviteYouToChat": "Mengundang Anda untuk obrolan suara", "inviteYouToGame": "Mengundang Anda untuk mengobrol", "inTheRoomNow": "<PERSON> dalam ruangan se<PERSON>ng", "unLockFreeChatTips": "Membuka obrolan pesan gratis tidak akan lagi meningkatkan keintiman, mengirimkan hadiah dapat meningkatkan keintiman.", "tomorrowGetMoreRewards": "Besok login akan mendapatkan lebih banyak hadiah", "received": "<PERSON><PERSON><PERSON>", "signInDays": "<PERSON><PERSON><PERSON> se<PERSON>a {day} hari", "@signInDays": {"type": "int", "placeholders": {"day": {}}}, "newbieTitle": "<PERSON> awal perja<PERSON>n baru, <PERSON><PERSON> telah menyi<PERSON>kan hadiah sambutan untuk Anda. <PERSON><PERSON>i waktu Anda di Win<PERSON>!", "welcomeToWinker": "Selamat datang di Winker", "whichDay": "<PERSON> {day}", "@whichDay": {"type": "int", "placeholders": {"day": {}}}, "chatGuideTipsFate": "Daftar Bola Takdir yang cocok untuk Anda, klik yang Anda minati untuk mulai mengobrol!", "chatGuideTipsMsg": "<PERSON><PERSON><PERSON> pesan akan di<PERSON> di sini, dan <PERSON>a dapat mengklik untuk mengobrol dengannya.", "newbieTasks": "<PERSON><PERSON>", "milestoneListTasks": "Tugas <PERSON>", "joinWinkerDays": "<PERSON><PERSON><PERSON> dengan winker:{num} hari", "@joinWinkerDays": {"type": "int", "placeholders": {"num": {}}}, "full": "<PERSON><PERSON>", "waitingForTheGameToStart": "<PERSON>unggu permainan dimulai", "findYourFatedMate": "<PERSON><PERSON><PERSON>", "normalLudoTips": "Mode Normal, 4 Token, <PERSON><PERSON>.", "quicklyLudoTips": "Mode Cepat, 1 Token, <PERSON>em <PERSON>ib.", "noFeedbackYet": "Belum ada umpan balik.", "intimacyRule1": "<PERSON><PERSON> se<PERSON> 3 diamond = 1 poin keintiman.", "intimacyRule2": "<PERSON><PERSON> se<PERSON> 40 koin emas = 1 poin keintiman.", "intimacyBottomTips": "<PERSON><PERSON><PERSON> obrolan sesuai dengan keintiman. <PERSON><PERSON> yang dikirim di obrolan dan ruangan sama-sama meningkatkan keintiman.", "selectedTopic": "<PERSON><PERSON> yang dipilih", "selectedTopicCount": "{count}/{max}", "@selectedTopicCount": {"type": "string", "placeholders": {"count": {}, "max": {}}}, "thereIsNoContentHere": "Tidak ada konten di sini", "csaeOrCsam": "CSAE/CSAM", "exitWillDelete": "<PERSON><PERSON><PERSON> akan menghapus catatan yang <PERSON>a pilih", "discardSelected": "<PERSON><PERSON> yang dipilih", "sendAndUnlock": "<PERSON><PERSON> hadiah {gift} untuk membuka o<PERSON>.", "@sendAndUnlock": {"type": "string", "placeholders": {"gift": {}}}, "chatUnlockedSuccess": "<PERSON><PERSON><PERSON><PERSON>, mulai o<PERSON><PERSON> se<PERSON>!", "csaeOrCsamDetail": "CSAE/CSAM: <PERSON><PERSON><PERSON><PERSON> dan Eksploitasi Seksual Anak/<PERSON><PERSON>han Seksual Anak", "waitForReply": "<PERSON><PERSON><PERSON> balasan sebelum mengirim pesan lainnya.", "roomIdKey": "ID ruangan", "cost": "Biaya:", "reportAvatar": "Laporkan avatar", "reportNickname": "<PERSON><PERSON><PERSON>", "reportIntroduction": "Laporkan Perkenalan", "reportVoiceCard": "Laporkan Kartu Suara", "searchHistory": "Riwayat Pencarian", "hotRoom": "Ruangan Hot", "searchRoomIdUserId": "Cari berdasarkan ID ruangan/ID pengguna", "playInOrder": "Putar Be<PERSON>tan", "singleCycle": "Putar Ulang Satu <PERSON>gu", "playRandomly": "Putar Secara Acak", "notFollowedAnyRooms": "<PERSON>a belum ikuti r<PERSON>an a<PERSON>pun.", "roomTag": "Tag <PERSON>uang<PERSON>", "addMusic": "Tambah Musik", "levelUnlock": "Buka kunci level {level}", "@levelUnlock": {"type": "int", "placeholders": {"level": {}}}, "chatRoomUpgrade": "Peningkatan Ruangan", "administrator": "<PERSON><PERSON><PERSON>", "roomBackground": "Background", "tmo": "TMO", "mine": "<PERSON><PERSON><PERSON>", "playDefaultMusic": "Putar musik default", "requestStorageData": "\"Winker\" meminta akses ke penyimpanan data Anda.", "contribution": "Kontribusi", "numDays": "{count} hari", "skin": "<PERSON><PERSON>", "props": "Alat", "chatBubble": "Gelembung", "userCar": "Kendar<PERSON>", "entranceMessage": "<PERSON><PERSON><PERSON>", "homePageEffect": "<PERSON><PERSON><PERSON> halaman beranda", "letter": "Kertas surat", "customBackgroundCard": "kartu custom latar belakang", "miniPostal": "Kartu pos mini", "giftBoxDesc": "<PERSON><PERSON>", "using": "Menggunakan", "homePageBackground": "<PERSON><PERSON> belakang halaman beranda", "mineMenuBackpack": "Tas", "use": "menggunakan", "friendsToBeSent": "<PERSON><PERSON> yang akan di<PERSON>:", "selectFriends": "<PERSON><PERSON><PERSON> teman", "giftBox": "<PERSON>k hadiah:", "sendGiftTips": "<PERSON><PERSON> hadiah diperlukan 1 kotak hadiah", "friendNotFound": "<PERSON><PERSON> hadiah perlu menggunakan 1 kotak hadiah", "buyGiftBoxDesc": "<PERSON><PERSON> hadiah diperlukan kotak hadiah, a<PERSON><PERSON>h Anda ingin menghabi<PERSON>n {price} star untuk beli kotak hadiah?", "@buyGiftBoxDesc": {"type": "string", "placeholders": {"price": {}}}, "pleaseChooseLengthOfPurchase": "<PERSON><PERSON>an pilih masa berlaku", "countAndCurrency": "{count} {currency}", "@countAndCurrency": {"type": "string", "placeholders": {"count": {}, "currency": {}}}, "bugGoodsDialogTips": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghabi<PERSON>n {star} koin untuk beli {type}?", "@bugGoodsDialogTips": {"type": "string", "placeholders": {"star": {}, "type": {}}}, "purchasedIt": "Anda telah membelinya!", "doYouWantToUseTheCustomBackgroundCardsDirectly": "<PERSON><PERSON>ka<PERSON> Anda ingin menggunakan kartu latar belakang custom secara langsung?", "toUseProp": "<PERSON><PERSON><PERSON>h Anda ingin menggunakan alat ini?", "lengthOfPurchase": "<PERSON><PERSON>", "nw": "<PERSON><PERSON>", "micLayoutUse": "<PERSON><PERSON><PERSON><PERSON>a ingin mengg<PERSON> {name} ？", "@micLayoutUse": {"type": "string", "placeholders": {"name": {}}}, "helloNiceToMeetYou": "Halo, salam kenal!", "nameEnteredTheRoom": "{name}... masuki r<PERSON>an ini", "@nameEnteredTheRoom": {"type": "string", "placeholders": {"name": {}}}, "discountOff": "{discount}% diskon", "@discountOff": {"type": "int", "placeholders": {"discount": {}}}, "max": "<PERSON><PERSON><PERSON><PERSON>", "decorations": "<PERSON><PERSON><PERSON>", "followingUser": "Pengguna yang kamu ikut", "noOneYouAreFollowing": "Anda belum mengikuti pengguna siapa pun.", "notJoinedAnyRoom": "Anda belum bergabung dengan ruangan mana pun.", "msgDescReward": "[<PERSON><PERSON>]", "lackOfCoinsPleaseFinishMoreTasksOrExchangeDiamonds": "<PERSON>rang koin! <PERSON>lakan selesaikan lebih banyak tugas atau tukar koin dengan berlian.", "insufficientCoins": "Koin tidak cukup, isi ulang sekarang!", "badgeRule": "<PERSON><PERSON><PERSON>", "wearBadgeRule": "Pak<PERSON> medali", "wearBadgeRule1": "Menyala medali untuk pakai", "wearBadgeRule2": "Maksimal 3 medali dapat dipakai", "wearBadgeRule3": "Medali akan di<PERSON>pilkan di halaman informasi pribadi, r<PERSON><PERSON>, dan o<PERSON><PERSON>", "badgeRankingRule": "1. Peringkat medali dihitung berdasarkan jumlah bintang yang diperoleh pengguna dari menyala medali", "badgeTypeRule": "Bagaimana mendapatkan medali?", "badgeTypeRule1": "<PERSON><PERSON>i persyaratan yang sesuai untuk menyalakan medali", "badgeTypeRule2": "Level medali: perunggu-perak-emas-berlian", "badgeRankRule": "<PERSON><PERSON><PERSON>", "badgeLeaderboard": "<PERSON><PERSON><PERSON>", "badgeRankRule1": "Menyala medali dengan level yang berbeda bisa mendapatkan bintang:", "badgeRuleBronze": "<PERSON><PERSON><PERSON>", "badgeRuleSilver": "<PERSON><PERSON>", "badgeRuleGold": "<PERSON><PERSON>", "badgeRuleDiamond": "<PERSON><PERSON><PERSON>", "badgeRankRule2": "Pengguna akan diberi peringkat berdasarkan bintang", "numStars": "{stars}bintang", "@numStars": {"type": "int", "placeholders": {"stars": {}}}, "badgeRanking": "Peringkat:{ranking}", "@badgeRanking": {"type": "string", "placeholders": {"ranking": {}}}, "congratsYouHaveLightedUp": "Selamat! Anda telah men<PERSON> {badge} medali", "@congratsYouHaveLightedUp": {"type": "string", "placeholders": {"badge": {}}}, "checkAndWearBadge": "<PERSON>a dapat memeriksa dan memakai medali di halaman \"Saya\"-\"Medali\"", "taskBadge": "Tugas medali", "growthBadge": "<PERSON><PERSON>", "gameBadge": "Medali Game", "badgeDisplay": "Tam<PERSON><PERSON> medali", "badgeDisplayFollowingPlaces": "Medali akan di<PERSON>pilkan di halaman informasi pribadi, r<PERSON><PERSON>, dan o<PERSON><PERSON>", "onlineUsersListOfVoiceRoom": "Daftar pengguna online di ruangan", "signInForDays": "Check-in berturut-turut selama {day} {day,plural, =0{hari}=1{hari}=2{hari}few{hari}other{hari}}", "@signInForDays": {"type": "string", "placeholders": {"day": {}}}, "giftBadge": "<PERSON><PERSON>", "activityBadge": "Medali Event", "achievementTime": "<PERSON><PERSON><PERSON>:", "goToWear": "Memakai", "myFamily": "Family Saya", "pleaseEnterChar": "<PERSON><PERSON><PERSON> masukkan {from}-{to} karakter", "@pleaseEnterChar": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familySlogan": "Slogan Family", "joinMode": "Mode bergabung", "joinWealthLevel": "Batas <PERSON>", "createWithAmount": "Buat dengan bayar {amount}", "@createWithAmount": {"type": "int", "placeholders": {"amount": {}}}, "createFamilySuccessfully": "<PERSON>a telah ber<PERSON>il mengirimkan informasi family And<PERSON>. <PERSON><PERSON> tunggu untuk ditinjau", "applicationSuccessful": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "applicationFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "underReview": "Sedang Ditinjau", "usernameInvitedYouToJoinFamilynameDoYouAgreeTo": "{userName} mengundang Anda untuk bergabung dengan {familyName}, apakah Anda setuju untuk bergabung?", "usernameRefuseYourFamilyApplication": "{userName} telah menolak permintaan <PERSON>.", "usernameWantsToJoinYourFamilyPleaseReviewIt": "{userName} ingin masuk ke family Anda! Cek permintaannya!", "congratulationsOnSuccessfullyCreatingTheFamilynameFamilyYouCanNow": "Selamat! Family {familyName} telah berhasil dibuat. Sekarang Anda bisa merekrut lebih banyak anggota. Cek sekarang!", "congratulationsOnJoiningTheFamilynameFamilyComeCheckItOut": "Selamat! Anda telah bergabung dengan family {familyName}. Yuk! Cek sekarang!", "usernameRejectedYourApplicationGoCheckOutOtherFamiliesGo": "<PERSON><PERSON><PERSON><PERSON> {userName}. Coba lihat family lain! Pergi ke pusat family!", "youHaveBeenRemovedFromTheFamilynameGoCheckOut": "Anda telah di<PERSON>rkan dari family {familyName}. Coba cari family lain! Pergi ke pusat family!", "congratulationsOnBeingAppointedAsFamilynamesPosition": "Selamat! Anda telah diangkat sebagai {position} di family {familyName}!", "congratulationsToFamilynameFamilyForUpgradingToLevellevelComeCheck": "Selamat! Family Anda {familyName} telah naik ke level {level}! Yuk. Cek sekarang!", "yourFamilyHasBeenDisbandedGoCheckOutOtherFamilies": "Family Anda telah di<PERSON>. Coba cari family lain! Pergi ke pusat family!", "yourFamilyHasBeenDisbanded": "Family Anda telah di<PERSON>!", "searchByFamilyID": "<PERSON><PERSON> family", "familyTreasury": "Harta Family", "theFamilyTreasuryWillBeUsedForTheFamilyBlessing": "Harta family akan dipakai untuk Tas aket Keberuntungan Family, memberikan lebih banyak keuntungan dan hadiah bagi anggota family!", "familyTask": "Misi Family", "familyLevelExp": "Exp Level Family", "whenTheFamilyExperienceValueReachesTheUpperLimit": "1. Jika Exp family telah mencapai batas maksimum, maka kontribusi individu juga akan mencapai batas. <PERSON><PERSON> hari itu, <PERSON><PERSON> tidak akan mendapatkan Exp family maupun kontribusi individu lagi.", "whenTheFamilyTreasuryReachesTodaysLimitOrThe": "2. <PERSON>ka harta family telah mencapai batas harian atau batas total, hari itu tidak akan ada lagi peningkatan.", "weeklyContribution": "Kontribusi <PERSON>n", "monthlyContribution": "Kontribusi Bulanan", "freeJoin": "<PERSON><PERSON><PERSON> untuk bergabung", "noOneCanSpeak": "<PERSON><PERSON><PERSON> anggota di<PERSON>n", "groupMessageNotDisturb": "<PERSON><PERSON>", "familyInfoEdit": "Edit informasi family", "familyRoomNameCannotBeModified": "Nama ruang family tidak dapat diubah", "familyRoomCoverCannotBeModified": "Cover ruang family tidak dapat diubah", "accessPermission": "<PERSON>k akses ruang", "allUsers": "<PERSON><PERSON><PERSON>", "onlyFamilyMembers": "<PERSON>ya untuk anggota family", "allUsersCanEnterTheRoom": "<PERSON><PERSON><PERSON> pengguna dapat memasuki ruang family?", "onlyFamilyMembersCanEnterTheRoom": "Hanya anggota family yang dapat memasuki ruang?", "afterTurningItOnOnlyFamilyMembersCanEnterThe": "Hanya anggota family yang dapat memasuki ruang setelah fitur ini diakti<PERSON>, dan pengguna lain akan otomatis dikeluarkan", "cannotBeSetDuringTheGame": "Tidak dapat mengubah pengaturan selama permainan berlangsung", "followTheRoom": "<PERSON><PERSON><PERSON> r<PERSON> ini", "followedSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "unfollow": "<PERSON><PERSON><PERSON><PERSON>", "unfollowSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "nonFamilyMembersAreNotAllowedToEnter": "Mode family telah diakti<PERSON>kan di ruang ini. Yang bukan anggota family akan dikeluarkan dari ruang ini.", "nonFamilyMembersAreNotAllowedToEnterInThisRoom": "Mode family telah diaktifkan di ruang ini. Yang bukan anggota family tidak bisa masuk ke ruang ini.", "disbandTheFamily": "Bubarkan Family", "familyPrivilege": "Hak is<PERSON>wa family", "familyNameEditOnce": "Nama family hanya diubah seminggu sekali", "familySloganEditOnce": "Slogan family hanya diubah seminggu sekali", "editFamilyName": "Edit nama family", "editFamilySlogan": "Edit slogan family", "nextRevisionDate": "Tanggal perubahan berikutnya:{date}", "@nextRevisionDate": {"type": "string", "placeholders": {"date": {}}}, "myTask": "<PERSON><PERSON> saya", "expStr": "Exp", "notMeetRuleJoinFamily": "Anda tidak memenuhi persyaratan berikut untuk bergabung dengan family ini:", "familyCoverEditAfterDays": "Cover family hanya diubah seminggu sekali, silakan dicoba lagi setelah {day} hari.", "@familyCoverEditAfterDays": {"type": "string", "placeholders": {"day": {}}}, "afterYouQuitTheFamilyYouWillClearAllThe": "<PERSON><PERSON><PERSON> Anda keluar dari family, semua aset yang terkait dengan keluarga akan dihapus. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar dari family?", "theFamilyLeaderCannotLeaveTheFamilyDirectlyYouCan": "Ketua family tidak bisa keluar langsung. <PERSON><PERSON>an hubungi layanan pelanggan resmi untuk proses lebih lanjut.", "disbandingTheFamily": "Bubarkan Family", "areYouSureYouWantToDisbandTheFamilyOnce": "Yakin ingin membubarkan family? <PERSON><PERSON><PERSON> pem<PERSON>, se<PERSON>a aset pribadi dan aset family akan dihapus. <PERSON>a memiliki {days} hari masa tenang untuk membatalkan sebelum family benar-benar dibubarkan. <PERSON>elah masa tenang selama {days} hari be<PERSON>, family akan dibubarkan.", "@areYouSureYouWantToDisbandTheFamilyOnce": {"type": "int", "placeholders": {"days": {}}}, "youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": "Anda mengajukan pembubaran family pada {time}. Saat ini dalam {days} hari masa tenang dan dapat membatalkan kapan saja.", "@youSubmittedTheFamilyDisbandmentApplicationOnTimeAndIt": {"type": "int", "placeholders": {"time": {}, "days": {}}}, "cancelDissolution": "<PERSON><PERSON>", "contactCS": "Hubungi CS {uid}", "coolingOff": "{days} hari masa tenang", "@coolingOff": {"type": "int", "placeholders": {"days": {}}}, "openMuteAll": "Bisu global tidiaktifkan {user}.", "@openMuteAll": {"type": "string", "placeholders": {"user": {}}}, "closeMuteAll": "Bisu global tidak tidiaktifkan {user}.", "@closeMuteAll": {"type": "string", "placeholders": {"user": {}}}, "globalMute": "<PERSON><PERSON><PERSON>", "userMute": "<PERSON>a sudah di<PERSON>n", "elders": "<PERSON><PERSON><PERSON><PERSON>", "ironFamily": "Family Besi", "bronzeFamily": "Family Perunggu", "silverFamily": "Family Perak", "goldFamily": "Family Emas", "gloryFamily": "Family Kemuliaan", "upperLimit": "Batas", "noOneCanSpeakTip": "<PERSON><PERSON><PERSON>, hanya ketua family dan wakil ketua family bisa berbicara.", "inReview": "Sedang Ditinjau", "selectCover": "<PERSON><PERSON><PERSON> un<PERSON>ah cover family.", "familyNameCountInvalid": "Nama family harus antara {from} - {to}karakter.", "@familyNameCountInvalid": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familySloganCountInvalid": "<PERSON><PERSON><PERSON> un<PERSON>ah cover family", "@familySloganCountInvalid": {"type": "int", "placeholders": {"from": {}, "to": {}}}, "familyInfoRejected": "Informasi family yang Anda ungguh telah ditolak karena melanggar peraturan komunitas Winker. <PERSON>lakan pergi ke halaman pembuatan family untuk melihat detailnya!", "followingRoom": "Room yang ikuti", "eventSubjectPlaceholder": "Isi tema event...", "eventSelectPeriod": "Pilih period...", "eventBannerLimit": "Unggah banner dengan ukuran 300×600 dan maksimal 5MB", "eventCreateToast": "Anda belum mengisi semua informasi", "eventCreatedShare": "Anda telah berhasil membuat event ruang Anda. Bagikan ke Moment sekarang!", "eventShareToMoment": "Bagikan ke Momen", "eventShareTitle": "Subscribe dan ikutan pesta bareng kami", "eventShareHost": "Host:", "eventSubject": "Tema:{name}", "@eventSubject": {"type": "string", "placeholders": {"name": {}}}, "evenShareSubscribers": "{num} Subscriber", "@evenShareSubscribers": {"type": "int", "placeholders": {"num": {}}}, "evenDetailSubscribers": "Subscriber {num}", "@evenDetailSubscribers": {"type": "int", "placeholders": {"num": {}}}, "evenDetailRoomId": "Room ID", "evenDetailSubject": "<PERSON><PERSON>", "evenDetailTime": "<PERSON><PERSON><PERSON>", "evenDetailMedal": "Medal", "eventRuleTitle": "Aturan Event", "eventRule1": "1. Pengguna harus membuat ruang suara sendiri terlebih dahulu sebelum dapat membuat event ruang", "eventRule2": "2. <PERSON>gg<PERSON> dapat membuat event ruang untuk jadwal hingga 7 hari ke depan", "eventRule3": "3. Saat membuat event ruang, informasi event harus melalui proses verifikasi. <PERSON><PERSON><PERSON> proses ini, pengguna masih dapat mengubah informasi event. <PERSON><PERSON><PERSON> di<PERSON>, hanya waktu event yang dapat diubah. Setiap event hanya bisa mengubah informasi 1 kali.", "eventMineEmpty": "Anda belum membuat event ruang~", "eventCreateNoRoom": "Anda perlu membuat ruang suara Anda sendiri sebelum Anda membuat event ruang", "eventUploadBanner": "<PERSON><PERSON><PERSON>", "timeOfDuration": "<PERSON><PERSON><PERSON>", "yourEventInfoNotFill": "<PERSON><PERSON><PERSON> lengkapi informasi untuk {name} event", "@yourEventInfoNotFill": {"type": "string", "placeholders": {"name": {}}}, "subject": "tema", "starTime": "waktu mulai", "banner": "banner", "eventDescription": "Deskripsi event", "enterEventDescription": "<PERSON>i <PERSON> event", "eventRoom": "Ruang Event", "eventRoomIDType": "Room ID:{id} ({type})", "@eventRoomIDType": {"type": "string", "placeholders": {"id": {}, "type": {}}}, "personal": "personal", "roomEvent": "Event Ruang", "rejectReview": "<PERSON><PERSON><PERSON>", "giftGivingDuringTheEvent": "Riwayat Pen<PERSON>", "eventGiftRankTips": "<PERSON><PERSON> berlian dan koin yang dikirim selama event akan mendapatkan poin.", "giftSendingDetails": "Detail <PERSON>man hadiah event", "diamondGifts": "<PERSON><PERSON>", "coinsGift": "<PERSON><PERSON>", "cancelRoomEvent": "Batalkan event", "cancelRoomEventTips": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membatalkan event di ruang ini?", "familyLuckyBag": "Lucky Bag Family", "thereAreTimeUntilTheStart": "Tinggal {time} lagi sampai waktu mulai mengambil", "clickToGrabTheLuckyBag": "<PERSON><PERSON> untuk ambil lucky bag", "youAreNotAMemberOfThisFamilyAndCannot": "<PERSON>a bukan anggota family ini, jadi tidak bisa ikut merebut lucky bag", "viewLuckyBagClaimDetails": "Lihat detail pengambilan lucky bag", "congratulationsOnReceiving": "Selamat mendapatkan", "unfortunately": "Sangat disayangkan", "theLuckyBagHasBeenFullyClaimed": "Lucky bag sudah habis diambil!", "sendLuckyBagWithoutA": "<PERSON><PERSON><PERSON> lucky bag", "numberOfLuckyBags": "<PERSON><PERSON><PERSON> lucky bag:", "enterTheNumberOfLuckyBags": "<PERSON><PERSON> jumlah lucky bag", "amount": "<PERSON><PERSON><PERSON>:", "atLeastNumDiamonds": "Setidaknya {num} berlian", "enterTheTotalDiamondAmountForTheLuckyBag": "Masukkan total berlian lucky bag", "fundDiamond": "1 dana = 1 berlian", "sendingTime": "<PERSON><PERSON><PERSON>:", "selectSendingTime": "<PERSON><PERSON> waktu <PERSON>", "sendingImmediately": "segera <PERSON>n", "ifTheFamilyLuckyBagIsNotClaimedWithin": "Jika lucky bag family tidak diambil dalam 24 jam, sisa keber<PERSON>ungan akan dikembalikan oleh sistem", "theFamilyHasBeenFrozenUnableToDistributeLuckyBags": "Family ini telah dibekukan dan tidak dapat membagikan lucky bag", "areYouSureYouWantToDistributeATotalOf": "Yakin ingin membagikan lucky bag senilai total {num1} berlian pada {where2} {where1}, dengan {num2} orang yang dapat mengkla<PERSON>?", "luckyBagRecord": "Riwayat Lucky bag", "bestLuck": "<PERSON><PERSON>", "aFamilyLuckyBagWasSentAtRoomPleaseGo": "Lucky bag family dibagikan pada {room}, silakan masuk ke ruang family untuk mengambilnya!", "theFamilyLuckyBagHasBeenSentPleaseGoTo": "Lucky bag family sudah dibagikan, silakan masuk ke ruang family untuk mengambilnya!", "theFamilyLuckyBagHasNotBeenFullyClaimedYet": "Lucky bag family belum habis diklaim, cepat masuk ke ruang family untuk ambil sekarang!", "youHaveReachedTheDeviceClaimLimitAndCannotClaim": "Perang<PERSON> ini sudah mencapai batas klaim, tidak bisa ambil lucky bag", "nowForReal": "<PERSON><PERSON><PERSON>", "roomProfile": "<PERSON><PERSON>", "chooseRoomLabel": "<PERSON><PERSON><PERSON> model ruang", "familyLuckyBagCannotBeDistributed": "Jika jumlah anggota family kurang dari {num} orang, maka lucky bag tidak dapat dibagikan.", "youAreNotFamilyMember": "Anda bukan anggota family ini.", "notObtained": "Tidak dapatkan", "userTmpBanned": "Pengguna ini diblokir karena melanggar peraturan komunitas.", "noRoomCreateEvent": "Saat ini tidak ada ruang, dan event ruang tidak dapat dibuat", "noSubscribeEvent": "<PERSON><PERSON> belum subscribe event apapun. <PERSON><PERSON>an kunjungi halaman event dan subscribe event yang Anda sukai!\n", "allRoomEvent": "Semua event ruang", "openTreasure": "buka harta", "luckyBagClaimed": "{num} lucky bag di<PERSON>bil dalam {time} menit", "theJackPot": "jack<PERSON>ot", "wonTheAward": "{who} menang {multiple}, dapat {award}", "isDropping": "sedang bagikan", "isSending": "sedang bagikan"}