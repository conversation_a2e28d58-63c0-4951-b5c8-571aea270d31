import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:service/service.dart';

import 'nova_asset_picker_builder_delegate.dart';
import 'nova_asset_picker_viewer_builder_delegate.dart';

class ResPickerViewer {
  static ResPickerViewer? _inst;

  ResPickerViewer._();

  /// Static method to push with the navigator.
  /// 跳转至选择预览的静态方法
  static Future<List<AssetEntity>?> pushToViewer(
      BuildContext context, {
        int currentIndex = 0,
        required List<AssetEntity> previewAssets,
        required ThemeData themeData,
        DefaultAssetPickerProvider? selectorProvider,
        ThumbnailSize? previewThumbnailSize,
        List<AssetEntity>? selectedAssets,
        SpecialPickerType? specialPickerType,
        int? maxAssets,
        bool shouldReversePreview = false,
        AssetSelectPredicate<AssetEntity>? selectPredicate,
        PermissionRequestOption permissionRequestOption =
        const PermissionRequestOption(),
        WidgetBuilder? bottomExtraBuilder,
        ValueChanged<BuildContext>? onPreviewLoaded,
      }) async {
    await AssetPicker.permissionCheck(requestOption: permissionRequestOption);
    final Widget viewer = AssetPickerViewer<AssetEntity, AssetPathEntity>(
      builder: NovaAssetPickerViewerBuilderDelegate(
        currentIndex: currentIndex,
        previewAssets: previewAssets,
        provider: selectedAssets != null
            ? AssetPickerViewerProvider<AssetEntity>(
          selectedAssets,
          maxAssets: maxAssets ??
              selectorProvider?.maxAssets ??
              defaultMaxAssetsCount,
        )
            : null,
        themeData: themeData,
        previewThumbnailSize: previewThumbnailSize,
        specialPickerType: specialPickerType,
        selectedAssets: selectedAssets,
        selectorProvider: selectorProvider,
        maxAssets: maxAssets,
        shouldReversePreview: shouldReversePreview,
        selectPredicate: selectPredicate,
        bottomExtraBuilder: bottomExtraBuilder,
        onPreviewLoaded: onPreviewLoaded,
      ),
    );
    final PageRouteBuilder<List<AssetEntity>> pageRoute =
    PageRouteBuilder<List<AssetEntity>>(
      pageBuilder: (_, __, ___) => viewer,
      transitionsBuilder: (_, Animation<double> animation, __, Widget child) {
        return FadeTransition(opacity: animation, child: child);
      },
    );
    final List<AssetEntity>? result =
    await Navigator.maybeOf(context)?.push<List<AssetEntity>>(pageRoute);
    return result;
  }
}
