import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:service/service.dart';

import 'nova_asset_picker_builder_delegate.dart';

class ResPicker {
  static ResPicker? _inst;

  ResPicker._();

  static Future<List<AssetEntity>?> pickAssets(
    BuildContext context, {
    Key? key,
    PermissionRequestOption? permissionRequestOption,
    AssetPickerConfig pickerConfig = const AssetPickerConfig(),
    bool useRootNavigator = true,
    AssetPickerPageRouteBuilder<List<AssetEntity>>? pageRouteBuilder,
    WidgetBuilder? bottomExtraBuilder,
    ValueChanged<BuildContext>? onPickerLoaded,
    ValueChanged<BuildContext>? onPreviewLoaded,
    ValueChanged<bool>? onPreviewSend,
  }) async {
    permissionRequestOption ??= PermissionRequestOption(
      androidPermission: AndroidPermission(
        type: pickerConfig.requestType,
        mediaLocation: false,
      ),
    );
    final PermissionState ps = await AssetPicker.permissionCheck(
      requestOption: permissionRequestOption,
    );
    final AssetPickerPageRoute<List<AssetEntity>> route = pageRouteBuilder?.call(const SizedBox.shrink()) ??
        AssetPickerPageRoute<List<AssetEntity>>(
          builder: (_) => const SizedBox.shrink(),
        );
    final DefaultAssetPickerProvider provider = DefaultAssetPickerProvider(
      maxAssets: pickerConfig.maxAssets,
      pageSize: pickerConfig.pageSize,
      pathThumbnailSize: pickerConfig.pathThumbnailSize,
      selectedAssets: pickerConfig.selectedAssets,
      requestType: pickerConfig.requestType,
      sortPathDelegate: pickerConfig.sortPathDelegate,
      filterOptions: pickerConfig.filterOptions,
      initializeDelayDuration: route.transitionDuration,
    );
    final Widget picker = AssetPicker<AssetEntity, AssetPathEntity>(
      key: key,
      builder: NovaAssetPickerBuilderDelegate(
        provider: provider,
        initialPermission: ps,
        gridCount: pickerConfig.gridCount,
        pickerTheme: pickerConfig.pickerTheme,
        gridThumbnailSize: pickerConfig.gridThumbnailSize,
        previewThumbnailSize: pickerConfig.previewThumbnailSize,
        specialPickerType: pickerConfig.specialPickerType,
        specialItemPosition: pickerConfig.specialItemPosition,
        specialItemBuilder: pickerConfig.specialItemBuilder,
        loadingIndicatorBuilder: pickerConfig.loadingIndicatorBuilder,
        selectPredicate: pickerConfig.selectPredicate,
        shouldRevertGrid: pickerConfig.shouldRevertGrid,
        limitedPermissionOverlayPredicate: pickerConfig.limitedPermissionOverlayPredicate,
        pathNameBuilder: pickerConfig.pathNameBuilder,
        textDelegate: pickerConfig.textDelegate,
        themeColor: pickerConfig.themeColor,
        locale: Localizations.maybeLocaleOf(context),
        bottomExtraBuilder: bottomExtraBuilder,
        onPickerLoaded: onPickerLoaded,
        onPreviewLoaded: onPreviewLoaded,
        onPreviewSend: onPreviewSend,
      ),
    );
    final List<AssetEntity>? result = await Navigator.maybeOf(
      context,
      rootNavigator: useRootNavigator,
    )?.push<List<AssetEntity>>(
      pageRouteBuilder?.call(picker) ?? AssetPickerPageRoute<List<AssetEntity>>(builder: (_) => picker),
    );
    return result;
  }
}
