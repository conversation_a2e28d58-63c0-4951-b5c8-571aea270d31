import 'package:service/service.dart';

class DefaultTextDelegate extends AssetPickerTextDelegate{

  /// Confirm string for the confirm button.
  /// 确认按钮的字段
  @override
  String get confirm => LocaleStrings.instance.done;

  /// Cancel string for back button.
  /// 返回按钮的字段
  @override
  String get cancel => LocaleStrings.instance.cancel;

  /// Edit string for edit button.
  /// 编辑按钮的字段
  @override
  String get edit => LocaleStrings.instance.edit;

  /// GIF indicator string.
  /// GIF指示的字段
  @override
  String get gifIndicator => 'GIF';

  /// Load failed string for item.
  /// 资源加载失败时的字段
  @override
  String get loadFailed => LocaleStrings.instance.loadFailed;

  /// Original string for original selection.
  /// 选择是否原图的字段
  @override
  String get original => LocaleStrings.instance.original;

  /// Preview string for preview button.
  /// 预览按钮的字段
  @override
  String get preview => LocaleStrings.instance.preview;

  /// Select string for select button.
  /// 选择按钮的字段
  @override
  String get select => LocaleStrings.instance.select;

  /// Empty list string for empty asset list.
  /// 资源列表为空时的占位字段
  @override
  String get emptyList => LocaleStrings.instance.emptyList;

  /// Un-supported asset type string for assets that
  /// belongs to [AssetType.other].
  /// 未支持的资源类型的字段
  @override
  String get unSupportedAssetType => LocaleStrings.instance.unSupportedAssetType;

  /// "Unable to access all assets in album".
  @override
  String get unableToAccessAll => LocaleStrings.instance.unableToAccessAll;

  @override
  String get viewingLimitedAssetsTip => LocaleStrings.instance.viewingLimitedAssetsTip;

  @override
  String get changeAccessibleLimitedAssets => LocaleStrings.instance.changeAccessibleLimitedAssets;

  @override
  String get accessAllTip => LocaleStrings.instance.accessAllTip;

  @override
  String get goToSystemSettings => LocaleStrings.instance.goToSystemSettings;

  /// "Continue accessing some assets".
  @override
  String get accessLimitedAssets => LocaleStrings.instance.accessLimitedAssets;

  @override
  String get accessiblePathName => LocaleStrings.instance.accessiblePathName;

  /// Semantics fields.
  ///
  /// Fields below are only for semantics usage. For customizable these fields,
  /// head over to [EnglishAssetPickerTextDelegate] for fields understanding.
  @override
  String get sTypeAudioLabel => LocaleStrings.instance.sTypeAudioLabel;

  @override
  String get sTypeImageLabel => LocaleStrings.instance.sTypeImageLabel;

  @override
  String get sTypeVideoLabel => LocaleStrings.instance.sTypeVideoLabel;

  @override
  String get sTypeOtherLabel => LocaleStrings.instance.sTypeOtherLabel;

  @override
  String get sActionPlayHint => LocaleStrings.instance.sActionPlayHint;

  @override
  String get sActionPreviewHint => LocaleStrings.instance.sActionPlayHint;

  @override
  String get sActionSelectHint => LocaleStrings.instance.sActionSelectHint;

  @override
  String get sActionSwitchPathLabel => LocaleStrings.instance.sActionSwitchPathLabel;

  @override
  String get sActionUseCameraHint => LocaleStrings.instance.sActionUseCameraHint;

  @override
  String get sNameDurationLabel => LocaleStrings.instance.sNameDurationLabel;

  @override
  String get sUnitAssetCountLabel => LocaleStrings.instance.sUnitAssetCountLabel;


}