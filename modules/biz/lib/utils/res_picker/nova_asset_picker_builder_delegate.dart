import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:service/service.dart';
import 'package:wechat_picker_library/wechat_picker_library.dart';
import 'dart:ui' as ui;

import 'res_picker_viewer.dart';

/// 根据nova ui以及相关功能定制的asset_picker
/// 对比与DefaultAssetPickerBuilderDelegate 扩展了如下属性 ：
/// [bottomExtraBuilder] 底部的控件
/// [loading]
class NovaAssetPickerBuilderDelegate extends DefaultAssetPickerBuilderDelegate {
  NovaAssetPickerBuilderDelegate({
    required super.provider,
    required super.initialPermission,
    super.gridCount,
    super.pickerTheme,
    super.specialItemPosition,
    super.specialItemBuilder,
    super.loadingIndicatorBuilder,
    super.selectPredicate,
    super.shouldRevertGrid,
    super.limitedPermissionOverlayPredicate,
    super.pathNameBuilder,
    super.themeColor,
    super.textDelegate,
    super.locale,
    super.gridThumbnailSize = defaultAssetGridPreviewSize,
    super.previewThumbnailSize,
    super.specialPickerType,
    super.keepScrollOffset = false,
    this.bottomExtraBuilder,
    this.onPickerLoaded,
    this.onPreviewLoaded,
    this.onPreviewSend,
  });

  final WidgetBuilder? bottomExtraBuilder;
  final ValueChanged<BuildContext>? onPickerLoaded;
  final ValueChanged<BuildContext>? onPreviewLoaded;
  final ValueChanged<bool>? onPreviewSend;

  bool _hasCall = false;

  @override
  Widget previewButton(BuildContext context) {
    return bottomExtraBuilder?.call(context) ?? super.previewButton(context);
  }

  @override
  Widget confirmButton(BuildContext context) {
    return Consumer<DefaultAssetPickerProvider>(
      builder: (_, DefaultAssetPickerProvider p, __) {
        final bool isSelectedNotEmpty = p.isSelectedNotEmpty;
        final bool shouldAllowConfirm = isSelectedNotEmpty || p.previousSelectedAssets.isNotEmpty;
        return MaterialButton(
          minWidth: shouldAllowConfirm ? 48 : 20,
          height: appBarItemHeight,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          color: theme.colorScheme.secondary,
          disabledColor: theme.splashColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          onPressed: shouldAllowConfirm
              ? () {
                  Navigator.maybeOf(context)?.maybePop(p.selectedAssets);
                }
              : null,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          child: ScaleText(
            isSelectedNotEmpty && !isSingleAssetMode
                ? '${textDelegate.confirm}'
                    ' (${p.selectedAssets.length}/${p.maxAssets})'
                : textDelegate.confirm,
            style: TextStyle(
              color: shouldAllowConfirm ? theme.textTheme.bodyLarge?.color : theme.textTheme.bodySmall?.color,
              fontSize: 17,
              fontWeight: FontWeight.normal,
            ),
            semanticsLabel: isSelectedNotEmpty && !isSingleAssetMode
                ? '${semanticsTextDelegate.confirm}'
                    ' (${p.selectedAssets.length}/${p.maxAssets})'
                : semanticsTextDelegate.confirm,
          ),
        );
      },
    );
  }

  @override
  Widget bottomActionBar(BuildContext context) {
    Widget child = Container(
      height: bottomActionBarHeight + context.bottomPadding,
      padding: const EdgeInsets.symmetric(horizontal: 20).copyWith(
        bottom: context.bottomPadding,
      ),
      color: theme.bottomAppBarTheme.color?.withOpacity(
        theme.bottomAppBarTheme.color!.opacity * (isAppleOS(context) ? .9 : 1),
      ),
      child: Row(
        children: <Widget>[
          isPreviewEnabled ? Expanded(child: previewButton(context)) : const Spacer(),
          if (isPreviewEnabled || !isSingleAssetMode) confirmButton(context),
        ],
      ),
    );
    if (isPermissionLimited) {
      child = Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[accessLimitedBottomTip(context), child],
      );
    }
    if (isAppleOS(context)) {
      child = ClipRect(
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(
            sigmaX: appleOSBlurRadius,
            sigmaY: appleOSBlurRadius,
          ),
          child: child,
        ),
      );
    }
    return child;
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasCall) {
      _hasCall = true;
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await Future.delayed(Duration(milliseconds: 200));
        onPickerLoaded?.call(context);
      });
    }
    return super.build(context);
  }

  @override
  Future<void> viewAsset(
      BuildContext context,
      int? index,
      AssetEntity currentAsset,
      ) async {
    final p = context.read<DefaultAssetPickerProvider>();
    // - When we reached the maximum select count and the asset is not selected,
    //   do nothing.
    // - When the special type is WeChat Moment, pictures and videos cannot
    //   be selected at the same time. Video select should be banned if any
    //   pictures are selected.
    if ((!p.selectedAssets.contains(currentAsset) && p.selectedMaximumAssets) ||
        (isWeChatMoment &&
            currentAsset.type == AssetType.video &&
            p.selectedAssets.isNotEmpty)) {
      return;
    }
    final revert = effectiveShouldRevertGrid(context);
    List<AssetEntity> current;
    final List<AssetEntity>? selected;
    final int effectiveIndex;
    if (isWeChatMoment) {
      if (currentAsset.type == AssetType.video) {
        current = <AssetEntity>[currentAsset];
        selected = null;
        effectiveIndex = 0;
      } else {
        if (index == null) {
          current = p.selectedAssets;
          current = current.reversed.toList(growable: false);
        } else {
          current = p.currentAssets;
        }
        current = current
            .where((AssetEntity e) => e.type == AssetType.image)
            .toList();
        selected = p.selectedAssets;
        final i = current.indexOf(currentAsset);
        effectiveIndex = revert ? current.length - i - 1 : i;
      }
    } else {
      selected = p.selectedAssets;
      if (index == null) {
        current = p.selectedAssets;
        current = current.reversed.toList(growable: false);
        effectiveIndex = selected.indexOf(currentAsset);
      } else {
        current = p.currentAssets;
        effectiveIndex = revert ? current.length - index - 1 : index;
      }
    }
    final List<AssetEntity>? result = await ResPickerViewer.pushToViewer(
      context,
      currentIndex: effectiveIndex,
      previewAssets: current,
      themeData: theme,
      previewThumbnailSize: previewThumbnailSize,
      selectPredicate: selectPredicate,
      selectedAssets: selected,
      selectorProvider: p,
      specialPickerType: specialPickerType,
      maxAssets: p.maxAssets,
      shouldReversePreview: revert,
      bottomExtraBuilder: bottomExtraBuilder,
      onPreviewLoaded: onPreviewLoaded,
    );
    if (result != null) {
      onPreviewSend?.call(true);
      Navigator.maybeOf(context)?.maybePop(result);
    }
  }
}
