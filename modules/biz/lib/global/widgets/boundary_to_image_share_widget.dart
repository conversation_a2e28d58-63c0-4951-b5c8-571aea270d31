import 'dart:io';
import 'dart:ui';
import 'package:service/common/statistics/game_record_statistics.g.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:tuple/tuple.dart';

import 'package:biz/biz.dart';
import 'package:biz/biz/share/handler/share_app_handle.dart';
import 'package:biz/biz/share/share_other_app_widget.dart';
import 'package:biz/biz/share/share_to_friends_helper.dart';
import 'package:biz/utils/time_util.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:service/global/config/text_extension.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/permission/permission_util.dart';
import 'package:service/modules/common/model/file_model.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/common/upload/upload_file.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/utils/asset_compressor.dart';
import 'package:service/utils/loading.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../biz/moment/moments_publish/bloc/moment_publish_bloc.dart';
import '../../biz/share/share_widget.dart';

/// 弹出界面转图片组件分享弹窗
void showBoundaryToImageWidgetShareDialog({
  required BuildContext context,
  required Widget boundaryToImageWidget,
  ShareTemplate? shareTemplate,
  bool showFacebook = false,
  bool showInstagram = true,
  bool showWhatsApp = true,
  bool moveFriendBeforeMoment = false,
  Function(ShareApps shareAppsType)? shareTypeClickCallback,
  required String from,
}) async {
  DialogScheduler.instance().schedule(() async {
    GameRecordStatistics.reportGameRecordShareImp();
    showDialog(
      (_) => _BoundaryToImageWidgetShareDialog(
        boundaryToImageWidget: boundaryToImageWidget,
        shareTemplate: shareTemplate,
        showFacebook: showFacebook,
        showInstagram: showInstagram,
        showWhatsApp: showWhatsApp,
        moveFriendBeforeMoment: moveFriendBeforeMoment,
        shareTypeClickCallback: shareTypeClickCallback,
        from: from,
      ),
      barrierDismissible: true,
      context: context,
    );
  });
}

class _BoundaryToImageWidgetShareDialog extends StatelessWidget {
  final Widget boundaryToImageWidget;
  final ShareTemplate? shareTemplate;
  final bool showFacebook;
  final bool showInstagram;
  final bool showWhatsApp;
  final bool moveFriendBeforeMoment;
  final Function(ShareApps shareAppsType)? shareTypeClickCallback;
  final String from;

  final int compressFileWidthHeight = 400;

  _BoundaryToImageWidgetShareDialog({
    required this.boundaryToImageWidget,
    this.shareTemplate,
    required this.showFacebook,
    required this.showInstagram,
    required this.showWhatsApp,
    required this.moveFriendBeforeMoment,
    this.shareTypeClickCallback,
    required this.from,
  });

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    Widget _bottomShareWidget() {
      final momentAndFriend = [
        OtherAppInfo(
            LocaleStrings.instance.moment, Res.iconHMomentH, ShareApps.moment),
        OtherAppInfo(LocaleStrings.instance.friends, Res.iconHFriends,
            ShareApps.friends),
      ];

      final List<OtherAppInfo> shareList = [
        if (showWhatsApp)
          OtherAppInfo(LocaleStrings.instance.whatsApp, Res.iconHShareWhatsapp,
              ShareApps.whatsapp),
        if (showFacebook)
          OtherAppInfo(LocaleStrings.instance.facebook, Res.iconHShareFacebook,
              ShareApps.facebook),
        if (showInstagram)
          OtherAppInfo(LocaleStrings.instance.instagram,
              Res.iconHShareInstagram, ShareApps.instagram),
        if (moveFriendBeforeMoment) ...momentAndFriend.reversed.toList(),
        if (!moveFriendBeforeMoment) ...momentAndFriend,
        OtherAppInfo(LocaleStrings.instance.save, Res.userHIconSaveGameRecord,
            ShareApps.extra),
      ];

      Widget _itemWidget(
          {required BuildContext context, required OtherAppInfo shareInfo}) {
        return ScaleTapWidget(
          onTap: () => _handleShare(context: context, shareInfo: shareInfo),
          child: Container(
              constraints: BoxConstraints(minWidth: 75.pt),
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 5.pt),
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    FLImage.asset(
                      shareInfo.icon,
                      width: 46.pt,
                      height: 46.pt,
                    ),
                    SizedBox(height: 9.pt),
                    TextExtension.fastStyle(shareInfo.name,
                        fontWeight: FontWeight.normal,
                        fontSize: 13.sp,
                        colorValue: 0xFF0B0B2B),
                  ])),
        );
      }

      return Container(
        width: double.infinity,
        height: 130.pt,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.pt),
                topRight: Radius.circular(10.pt))),
        child: Column(
          children: [
            SizedBox(height: 10.5.pt),
            Container(
                width: 40.pt,
                height: 4.pt,
                decoration: BoxDecoration(
                    color: Color(0xFFA3AFC2),
                    borderRadius: BorderRadius.circular(2.pt))),
            SizedBox(height: 19.5.pt),
            SizedBox(
                width: 375.pt,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: shareList.map(
                        (shareInfo) {
                          if (shareList.length <= 4) {
                            return Container(
                              width: 375.pt / shareList.length,
                              child: Center(
                                child: _itemWidget(
                                    context: context, shareInfo: shareInfo),
                              ),
                            );
                          } else {
                            return _itemWidget(
                                context: context, shareInfo: shareInfo);
                          }
                        },
                      ).toList()),
                )),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: () => routerUtil.pop(),
      child: Material(
          color: Colors.transparent,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Spacer(),
              RepaintBoundary(key: _globalKey, child: boundaryToImageWidget),
              Spacer(),
              _bottomShareWidget(),
            ],
          )),
    );
  }

  Future<Tuple2> _imageData() async {
    final boundary =
        _globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    final image = await boundary?.toImage(pixelRatio: 3);
    final byteData = await image?.toByteData(format: ImageByteFormat.png);
    return Tuple2(byteData?.buffer.asUint8List(), image);
  }

  void _handleShare(
      {required BuildContext context, required OtherAppInfo shareInfo}) async {
    shareTypeClickCallback?.call(shareInfo.shareApps);
    if (shareInfo.shareApps == ShareApps.extra) {
      bool hasPermission = true;
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      // android10 以下才判断权限
      if (androidInfo.version.sdkInt < 29)  {
        hasPermission = await checkPermission(type: PermissionType.photo);
      }
      if (hasPermission) {
      showLoading();
        final pngBytes = (await _imageData()).item1;
        if (pngBytes == null) {
          hideLoading();
          toast(LocaleStrings.instance.unknownError);
          return;
        }
        final dirPath = await getTemporaryDirectory();
        final fileName = '${DateTime.now().millisecondsSinceEpoch}.png';
        final file = File(join(dirPath.path, 'screenshot', fileName));
        if (!file.existsSync()) {
          await file.create(recursive: true);
        }
        final photoFile = await file.writeAsBytes(pngBytes, flush: true);

        // saveImage 方法默认保存的是 jpg 这里改为保存自定义文件方式
        final result = await ImageGallerySaver.saveFile(photoFile.path,
            isReturnPathOfIOS: true, name: fileName);
        if (result is Map && result["isSuccess"] == true) {
          toast(LocaleStrings.instance.saveSuccessfully);
        } else {
          toast(LocaleStrings.instance.saveFailed);
        }
        if (file.existsSync()) {
          await file.delete();
        }
        hideLoading();
      } else {
        showGoSettingDialog(context, type: PermissionType.photo);
      }
    } else if (shareInfo.shareApps == ShareApps.moment) {
      showLoading();
      final result = await _imageData();
      final pngBytes = result.item1;
      final image = result.item2;
      if (pngBytes == null || image == null) {
        hideLoading();
        toast(LocaleStrings.instance.unknownError);
        return;
      }
      hideLoading();
      routerUtil.push(R_MOMENTS_PUBLISH, params: {
        P_IMAGE_FILE: ShareImageToMomentModel(
            imageData: pngBytes, width: image.width, height: image.height)
      });
    } else if (shareInfo.shareApps == ShareApps.friends) {
      showLoading();
      final pngBytes = (await _imageData()).item1;
      if (pngBytes == null) {
        hideLoading();
        toast(LocaleStrings.instance.unknownError);
        return;
      }

      final fileCompress = await _saveToFile(pngBytes: pngBytes);
      if (fileCompress == null) {
        hideLoading();
        toast(LocaleStrings.instance.shareFail);
        return;
      }

      final int compressFileWidthHeight = 400;
      final uploader = UploadFiles(
        fileList: [
          FileModel(
              path: fileCompress.path, name: "tmp_pic_0", height: compressFileWidthHeight, width: compressFileWidthHeight)
        ],
        complete: (files) async {
          hideLoading();
          /// 分享窗
          showShareDialog(
            context: FLRouter.routeObserver.getLastContext(),
            title: "",
            shareWidgetType: ShareWidgetType.withContact,
            template: ImageShare(image: files[0].url ?? ""),
            includeGroupChat: true
          );
        },
        uploadFail: (_, {code, msg}) {
          hideLoading();
          toast(LocaleStrings.instance.pleaseTryAgain);
        },
      );
      uploader.upload();
    } else {
      final pngBytes = (await _imageData()).item1;
      if (pngBytes == null) {
        toast(LocaleStrings.instance.unknownError);
        return;
      }
      showLoading();
      final fileCompress = await _saveToFile(pngBytes: pngBytes);
      if (fileCompress == null) {
        hideLoading();
        toast(LocaleStrings.instance.shareFail);
        return;
      }

      final int compressFileWidthHeight = 400;
      final uploader = UploadFiles(
        fileList: [
          FileModel(
              path: fileCompress.path, name: "tmp_pic_0", height: compressFileWidthHeight, width: compressFileWidthHeight)
        ],
        complete: (files) async {
          hideLoading();
          final shareTemplate = ImageShare(image: files[0].url ?? "");

          switch (shareInfo.shareApps) {
            case ShareApps.facebook:
              shareAppHandle.shareToFacebook(shareTemplate,
                  shareResultCallback: (type, success, target) {
                    hideLoading();
                  });
              break;
            case ShareApps.instagram:
              shareAppHandle.shareToInstagram(shareTemplate,
                  shareResultCallback: (type, success, target) {
                    hideLoading();
                  });
              break;

          /// 这三个选项上面已经处理
            case ShareApps.moment:
            case ShareApps.extra:
            case ShareApps.friends:

            /// 没有这三个选项
            case ShareApps.whatsapp:
              shareAppHandle.shareToWhatsApp(shareTemplate,
                  shareResultCallback: (type, result) {
                    hideLoading();
                  });
              break;
            case ShareApps.system:
            case ShareApps.link:
            default:
              hideLoading();
              break;
          }
        },
        uploadFail: (_, {code, msg}) {
          hideLoading();
          toast(LocaleStrings.instance.pleaseTryAgain);
        },
      );
      uploader.upload();
    }
  }

  Future<File?> _saveToFile({
    required List<int> pngBytes,
  }) async {
    String currentTimeString = TimeUtil.getDayByFormat(
        DateTime.now().millisecondsSinceEpoch, "yyyy_MM_dd_HH_mm_ss");
    final dirPath = await getTemporaryDirectory();
    final file = File(join(dirPath.path, 'image', '$currentTimeString.png'));
    if (!file.existsSync()) {
      await file.create(recursive: true);
    }
    await file.writeAsBytes(pngBytes, flush: true);

    final fileCompress = await AssetCompressor.compressImageFile(
        file, compressFileWidthHeight, compressFileWidthHeight,
        quality: 80);
    return fileCompress;
  }
}
