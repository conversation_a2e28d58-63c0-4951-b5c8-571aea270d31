import 'dart:async';
import 'dart:io';

import 'package:biz/biz.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/ornament/model/user_ornament.dart';
import 'package:service/modules/user/const/events.dart';

import 'bloc/user_avatar_bloc.dart';

const LinearGradient defaultAvatarGradientBorder = LinearGradient(
  colors: [
    Color(0xFFB600FF),
    Color(0xFF8100FF),
  ],
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
);

const LinearGradient defaultRoomStatusGradient = LinearGradient(
  colors: [
    Color(0xFFB781FD),
    Color(0xFF693AF4),
  ],
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
);

const double defaultAvatarGradientBorderWidth = 2;

typedef OnTapCallback = void Function({String? rid});

const double kUserAvatarFrameRadio = 1.6;

///
/// 用户头像类
/// 在房状态，如果想直接展现在房状态，设置[showRoomStatus]为true即可，
/// 如果想异步自动加载是否在房状态，除了设置[showRoomStatus]为true，同时需要设置[loadRoomStatus]为true以及[userId]
///
class UserAvatar extends StatefulWidget {
  const UserAvatar({
    Key? key,
    required this.url,
    this.size,
    this.isOnline,
    this.frameUrl,
    this.placeholder,
    this.file,
    this.avatarCode,
    this.decoration,
    this.borderRadius,
    this.userId,
    this.hasFrame = false,
    this.avatarCodeError,
    this.showRoomStatus = false,
    this.inGameStatus = false,
    this.roomStatusDecoration,
    this.roomStatusIcon,
    this.roomStatusText,
    this.loadRoomStatus = false,
    this.roomFontSize,
    this.onTap,
    this.loadAvatarFrameEnd,
    this.borderColor,
    this.inRoom = false,
    this.forceShowSvga = false,
    this.borderWidth,
    this.gradientBorder,
    this.gradientBorderWidth,
    this.itemType,
    this.isBanned = false,
  }) : super(key: key);

  final String? itemType;

  /// url
  final String? url;

  /// 用户id，用于取用户头像框
  final String? userId;

  /// 强行设置头像框url
  final String? frameUrl;

  /// 此处暂定为两种状态：在线/不在线
  final bool? isOnline;

  /// 头像尺寸，加入头像框后，总尺寸为 size * 1.6,需要预留头像框的位置
  final double? size;

  final Widget? placeholder;

  /// 头像code
  final String? avatarCode;

  /// 房间内的组件
  final bool inRoom;

  /// 某些场景不判断内存状态
  final bool forceShowSvga;

  /// 头像本地文件
  final File? file;

  final Decoration? decoration;

  /// 头像圆角大小
  ///
  /// [borderRadius] 为null时默认为圆形头像
  final BorderRadius? borderRadius;

  /// 默认没有头像框
  final bool hasFrame;

  /// 本地没有对应avatarCode回调
  final VoidCallback? avatarCodeError;

  /// 展示在房状态
  final bool showRoomStatus;

  /// 展示在游戏房状态
  final bool inGameStatus;

  /// 在房状态前面的图标
  final String? roomStatusIcon;

  /// 在房状态本案
  final String? roomStatusText;

  /// 加载在房状态
  final bool loadRoomStatus;

  final double? roomFontSize;

  final BoxDecoration? roomStatusDecoration;

  final OnTapCallback? onTap;

  /// 加载头像框信息结束回调
  final Function(bool)? loadAvatarFrameEnd;

  /// 头像外边框颜色，默认没有外边框
  final Color? borderColor;
  final double? borderWidth;

  /// 头像渐变边框样式，与[borderColor]互斥使用
  final Gradient? gradientBorder;
  final double? gradientBorderWidth;

  /// 是否被封号了
  final bool isBanned;

  @override
  _UserAvatarState createState() => _UserAvatarState();
}

class _UserAvatarState extends State<UserAvatar> with TickerProviderStateMixin {
  final _fit = BoxFit.cover;
  final _defaultSize = 40.pt;

  /// 头像框url
  String frameUrl = "";
  double _ratio = kUserAvatarFrameRadio;

  SvgaInfo? svgaInfo;
  String? svgThumb;

  final SvgaController _svgController = SvgaController();

  /// 装扮更新事件
  StreamSubscription? _subscription;

  int updateTime = 0;

  bool initFrame = true;

  /// 是否显示svg缩略图
  bool showSvgThumb = false;

  UserAvatarBloc? _bloc;

  @override
  void initState() {
    _initAvatar();
    super.initState();
    _bloc = UserAvatarBloc(showRoomStatus: widget.showRoomStatus && widget.loadRoomStatus, uid: widget.userId ?? "");
  }

  void _initAvatar() {
    /// 有头像框占位才会监听
    if (widget.hasFrame) {
      _ratio = kUserAvatarFrameRadio;
      if (widget.userId?.isNotEmpty == true) {
        svgThumb = userOrnamentService.getFrameThumbById(widget.userId!);
        showSvgThumb = svgThumb?.isNotEmpty ?? false;
        _subscription?.cancel();
        _subscription = rxUtil.observer<UserOrnament>(UserStatusEvent.updateOrnament).listen(_updateOrnament);
      }

      /// 没有uid没有头像框url，不走初始化头像框
      if (widget.userId?.isNotEmpty != true && widget.frameUrl?.isNotEmpty != true) {
        svgaInfo = null;
        svgThumb = null;
        showSvgThumb = false;
        return;
      }
      _svgController.setBeginCallback((info) {
        showSvgThumb = false;
        if (mounted) setState(() {});
      });
      _initFrame();
    } else {
      _ratio = 1;
    }
  }

  void _initFrame() async {
    final nowTime = DateTime.now().millisecondsSinceEpoch;

    /// 防止装扮更新事件导致初始化加载两次
    if (initFrame && nowTime - updateTime < 5 * 1000) return;
    String avatarFrameUrl = frameUrl;

    if (widget.frameUrl?.isNotEmpty == true) {
      frameUrl = widget.frameUrl ?? '';
    } else if (widget.userId?.isNotEmpty == true) {
      updateTime = nowTime;
      final userOrnament = await userOrnamentService.getUserOrnament(widget.userId!);
      initFrame = false;
      frameUrl = userOrnament?.avatarFrame ?? "";
      svgThumb = userOrnament?.avatarFrameThumb;
    } else {
      frameUrl = '';
    }

    if (avatarFrameUrl != frameUrl) {
      /// 房间外不显示
      bool showSvga = (widget.forceShowSvga ||
              (await commonService.showMySvga() && await widget.userId == accountService.currentUid()))
          ? true
          : (widget.inRoom ? (await commonService.showSvgaInRoom()) : (await commonService.showSvgaOutRoom()));
      if (showSvga && frameUrl.isNotEmpty == true && frameUrl.endsWith("svga")) {
        final date = DateTime.now();
        svgaInfo = await userOrnamentService.getSvgaInfoByUrl(frameUrl);
        int diff = DateTime.now().difference(date).inMilliseconds;

        /// 减慢svga加载速度，防止在页面打开过程中同时打开太多带动画的头像，导致页面动画卡顿
        if (diff < 1000) {
          await Future.delayed(Duration(milliseconds: 500));
        }
      } else {
        svgaInfo = null;
        showSvgThumb = svgThumb?.isNotEmpty == true;
      }
      if (widget.hasFrame) {
        widget.loadAvatarFrameEnd?.call(svgaInfo != null);
      }
      if (mounted) setState(() {});
    }
  }

  @override
  void didUpdateWidget(covariant UserAvatar oldWidget) {
    if (oldWidget.userId != widget.userId || oldWidget.frameUrl != widget.frameUrl || oldWidget.url != widget.url) {
      _initAvatar();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<UserAvatarBloc, UserAvatarState>(
        bloc: _bloc,
        builder: (context, state) {
          double avatarSize = (widget.size ?? _defaultSize);

          Decoration? decoration;
          EdgeInsetsGeometry? padding;
          // 渐变颜色边框,不加载的情况下直接显示，加载则看返回结果
          if (widget.gradientBorder != null ||
              (widget.showRoomStatus && !widget.loadRoomStatus) ||
              (widget.loadRoomStatus && state.liveStatusModel?.showLiveRoom == true)) {
            decoration = BoxDecoration(
              gradient: widget.gradientBorder ?? defaultAvatarGradientBorder,
              shape: BoxShape.circle,
            );
            padding = EdgeInsets.all(widget.gradientBorderWidth ?? defaultAvatarGradientBorderWidth);
          } else if (widget.decoration != null) {
            decoration = widget.decoration;
          } else if (widget.borderRadius != null || widget.borderColor != null) {
            // 纯色边框
            decoration = BoxDecoration(
              borderRadius: widget.borderRadius ?? null,
              shape: widget.borderRadius != null ? BoxShape.rectangle : BoxShape.circle,
              border: widget.borderColor != null
                  ? Border.all(
                      color: widget.borderColor!,
                      width: widget.borderWidth ?? 1.pt,
                    )
                  : null,
            );
          }

          Widget clipChild;

          if (widget.borderRadius != null) {
            clipChild = ClipRRect(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(avatarSize / 2),
              child: _avatar(),
            );
          } else {
            clipChild = ClipOval(
              child: _avatar(),
            );
          }
          Widget avatarWidthBorder = Container(
            padding: padding,
            decoration: decoration,
            child: clipChild,
          );

          var child = Container(
            width: avatarSize * _ratio,
            height: avatarSize * _ratio,
            child: Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                avatarWidthBorder,
                if (widget.hasFrame) _avatarFrame(),
                widget.inGameStatus
                    ? _gameStatus()
                    : widget.showRoomStatus
                        ? _roomStatus(avatarSize)
                        : Positioned(
                            bottom: widget.hasFrame ? (_ratio - 1) / 2 * avatarSize : 0.pt,
                            right: widget.hasFrame ? (_ratio - 1) / 2 * avatarSize + 3.pt : 3.pt,
                            child: widget.isOnline ?? false ? _onlineStatusWidget() : Container()),
              ],
            ),
          );

          return widget.onTap != null ? _wrapTap(child) : child;
        });
  }

  Widget _avatarFrame() {
    if (svgaInfo != null) {
      return SvgaWidget(
        svgaInfo: svgaInfo,
        controller: _svgController,
      );
    } else if (frameUrl.isNotEmpty == true && !frameUrl.endsWith("svga")) {
      return CachedNetworkImage(imageUrl: frameUrl);
    } else if (showSvgThumb && svgThumb?.isNotEmpty == true) {
      return CachedNetworkImage(imageUrl: svgThumb ?? "");
    } else {
      return Container();
    }
  }

  Widget _avatar() {
    if (widget.isBanned) {
      final size = widget.size ?? _defaultSize;
      return Container(
        width: size,
        height: size,
        color: Color(0xFFB2B2B2),
        padding: EdgeInsets.all(size / 6),
        child: FLImage.asset(Res.profileAvatarBan, fit: BoxFit.contain),
      );
    }

    if (widget.avatarCode?.isNotEmpty == true) {
      return FLImage.asset('res/avatar/${widget.avatarCode}.webp',
          width: widget.size ?? _defaultSize, height: widget.size ?? _defaultSize, fit: _fit, errorBuilder: (
        context,
        error,
        stackTrace,
      ) {
        widget.avatarCodeError?.call();
        return _netOrFileAvatar();
      });
    }
    return _netOrFileAvatar();
  }

  Widget _netOrFileAvatar() {
    if (widget.file != null) {
      return Image.file(
        widget.file!,
        width: widget.size ?? _defaultSize,
        height: widget.size ?? _defaultSize,
        fit: _fit,
      );
    } else if (widget.url?.isNotEmpty == true) {
      return CachedNetworkImage(
        imageUrl: widget.url!,
        placeholder: (_, __) => GlobalWidgets.imagePlaceholder(),
        errorWidget: (context, str, value) => widget.placeholder ?? _buildDefault(),
        width: widget.size ?? _defaultSize,
        height: widget.size ?? _defaultSize,
        fit: _fit,
        fadeInDuration: Duration.zero,
      );
    } else {
      return widget.placeholder ?? _buildDefault();
    }
  }

  Widget _buildDefault() {
    return Container(
      width: widget.size ?? _defaultSize,
      height: widget.size ?? _defaultSize,
      child: FLImage.asset(Res.profileDefaultAvatar),
    );
  }

  Widget _onlineStatusWidget() {
    return Container(
      height: 10.pt,
      width: 10.pt,
      decoration: BoxDecoration(
          color: R.color.secondaryCyanBlueColor,
          border: Border.all(color: Colors.white, width: 1.pt),
          borderRadius: BorderRadius.all(Radius.circular(5.pt))),
    );
  }

  void _updateOrnament(UserOrnament userOrnament) {
    if (userOrnament.uid == widget.userId) {
      _initFrame();
      return;
    }
  }

  Widget _roomStatus(double size) {
    // if (widget.itemType == MomentListTypeStatistic.recommend || widget.itemType == MomentListTypeStatistic.following) {
    //   return _inRoomStatusForFeed(size);
    // }
    return _inRoomStatus(size);
  }

  Widget _gameStatus() {
    return Positioned(
      bottom: 5.pt,
      child: FLImage.asset(Res.homeIconGame, width: 22.pt, fit: BoxFit.cover),
    );
  }

  /// 在房状态
  Widget _inRoomStatus(double size) {
    if (widget.loadRoomStatus && _bloc?.state.liveStatusModel?.showLiveRoom != true) {
      return SizedBox.shrink();
    }
    double fontSize;
    if (widget.roomFontSize == null) {
      fontSize = size < 65.pt ? (size * 0.2) : 13.sp;
    } else {
      fontSize = widget.roomFontSize!;
    }
    double textPaddingTop = 2;

    var bottom = widget.hasFrame ? (size * (kUserAvatarFrameRadio - 1)) / 2 - 4.pt : _spacing(size);

    Widget icon;
    if (widget.roomStatusIcon != null) {
      icon = FLImage.asset(widget.roomStatusIcon!, width: 0.18 * size);
    } else {
      icon = Lottie.asset(Assets.liveUserInRoom, width: size / 5.5, fit: BoxFit.cover, repeat: true);
    }

    return Positioned(
        bottom: bottom,
        child: Container(
          // height: 15.pt,
          constraints: BoxConstraints(
            maxHeight: size * 0.24,
            minHeight: size * 0.24,
            maxWidth: size * 1.25,
          ),
          padding: EdgeInsetsDirectional.only(start: size / 11, end: size / 11),
          decoration: widget.roomStatusDecoration ??
              BoxDecoration(
                gradient: defaultRoomStatusGradient,
                borderRadius: BorderRadius.all(Radius.circular(7.pt)),
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              icon,
              SizedBox(width: size / 18),
              Flexible(
                child: Padding(
                  padding: EdgeInsetsDirectional.only(top: textPaddingTop),
                  child: Text(
                    widget.roomStatusText ?? LocaleStrings.instance.live,
                    style: TextStyle(
                      fontWeight: FontWeightExt.heavy,
                      // fontStyle: FontStyle.italic,
                      fontSize: fontSize,
                      height: 1,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  Widget _inRoomStatusForFeed(double size) {
    if (!widget.showRoomStatus) {
      return SizedBox.shrink();
    }

    Widget icon;
    if (widget.roomStatusIcon != null) {
      icon = FLImage.asset(widget.roomStatusIcon!, width: 0.13 * size);
    } else {
      icon = Lottie.asset(Assets.liveUserInRoom, width: 10.pt, height: 8.pt, fit: BoxFit.cover, repeat: true);
    }
    double avatarMargin = (_ratio - 1) / 2 * size;
    return Positioned(
      top: avatarMargin - 3.pt,
      right: avatarMargin,
      child: Visibility(
        visible: _bloc?.state.liveStatusModel?.showLiveRoom == true,
        child: Container(
            width: 20.pt,
            height: 14.pt,
            alignment: Alignment.center,
            decoration: BoxDecoration(color: Color(0xFFB600FF), borderRadius: BorderRadius.circular(7.pt)),
            child: icon),
      ),
    );
  }

  Widget _wrapTap(Widget child) {
    return GestureDetector(
      onTap: () {
        String? rid = _bloc?.state.liveStatusModel?.roomId;
        if (rid?.trimNotEmpty == true && _bloc?.state.liveStatusModel?.showLiveRoom == true) {
          widget.onTap?.call(rid: rid);
        } else {
          widget.onTap?.call(rid: null);
        }
      },
      child: child,
    );
  }

  double _spacing(double size) {
    if (size <= 65.pt) return -4.pt;
    return 0.pt;
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _svgController.dispose();
    super.dispose();
  }
}
