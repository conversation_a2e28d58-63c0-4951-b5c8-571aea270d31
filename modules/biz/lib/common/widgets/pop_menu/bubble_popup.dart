import 'package:flutter/material.dart';

enum BubblePosition { top, bottom }

class BubblePopup {
  final BuildContext context;
  final Widget content;
  final double width;
  final BubblePosition position;
  final Color backgroundColor;
  final Gradient? gradient;
  final Duration animationDuration;
  final double triangleHeight;
  final double triangleWidth;
  final double offsetX;
  final double offsetY;
  final double borderRadius;
  final VoidCallback? onTap;
  final Offset localToGlobalOffset;
  final double bounceHeight;
  final Duration bounceDuration;
  final bool showBounceAnimation;

  OverlayEntry? _entry;

  BubblePopup({
    required this.context,
    required this.content,
    required this.width,
    this.position = BubblePosition.top,
    this.backgroundColor = Colors.white,
    this.gradient,
    this.animationDuration = const Duration(milliseconds: 300),
    this.triangleHeight = 9,
    this.triangleWidth = 15,
    this.offsetX = 0,
    this.offsetY = 0,
    this.borderRadius = 8,
    this.onTap,
    this.localToGlobalOffset = Offset.zero,
    this.bounceHeight = 8, // 抖动幅度
    this.bounceDuration = const Duration(milliseconds: 600), // 抖动动画时长
    this.showBounceAnimation = false,
  });

  void show(GlobalKey targetKey) {
    final overlay = Overlay.of(context);
    final rb = targetKey.currentContext?.findRenderObject();
    if (rb == null && rb is! RenderBox) return;

    final renderBox = rb as RenderBox;
    final targetPosition = renderBox.localToGlobal(localToGlobalOffset);
    final targetSize = renderBox.size;

    final entry = OverlayEntry(
      builder: (context) {
        return _BubblePopup(
          targetPosition: targetPosition,
          targetSize: targetSize,
          content: content,
          width: width,
          position: position,
          backgroundColor: backgroundColor,
          gradient: gradient,
          animationDuration: animationDuration,
          triangleHeight: triangleHeight,
          triangleWidth: triangleWidth,
          offsetX: offsetX,
          offsetY: offsetY,
          onDismiss: dismiss,
          borderRadius: borderRadius,
          bounceHeight: bounceHeight,
          bounceDuration: bounceDuration,
          showBounceAnimation: showBounceAnimation,
          onTap: onTap,
        );
      },
    );
    _entry = entry;
    overlay.insert(entry);
  }

  void dismiss() {
    _entry?.remove();
  }
}

class _BubblePopup extends StatefulWidget {
  final Offset targetPosition;
  final Size targetSize;
  final Widget content;
  final double width;
  final BubblePosition position;
  final Color backgroundColor;
  final Gradient? gradient;
  final Duration animationDuration;
  final VoidCallback onDismiss;
  final double triangleHeight;
  final double triangleWidth;
  final double offsetX;
  final double offsetY;
  final double borderRadius;
  final double bounceHeight;
  final Duration bounceDuration;
  final bool showBounceAnimation;
  final VoidCallback? onTap;

  const _BubblePopup({
    required this.targetPosition,
    required this.targetSize,
    required this.content,
    required this.width,
    required this.position,
    required this.backgroundColor,
    this.gradient,
    required this.animationDuration,
    required this.onDismiss,
    required this.triangleHeight,
    required this.triangleWidth,
    required this.offsetX,
    required this.offsetY,
    required this.borderRadius,
    required this.bounceHeight,
    required this.bounceDuration,
    required this.showBounceAnimation,
    this.onTap,
  });

  @override
  State<_BubblePopup> createState() => _BubblePopupState();
}

class _BubblePopupState extends State<_BubblePopup> with TickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _slideAnimation;
  late final Animation<double> _fadeAnimation;

  late final AnimationController _bounceController;
  late final Animation<Offset> _bounceAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, widget.position == BubblePosition.top ? 0.2 : -0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.forward();

    // 抖动动画
    _bounceController = AnimationController(
      vsync: this,
      duration: widget.bounceDuration,
    );

    _bounceController.addListener(() {
      if (_bounceController.status == AnimationStatus.completed) {
        _bounceController.reverse();
      }
    });

    _bounceAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(0, widget.bounceHeight),
    ).animate(
      CurvedAnimation(
        parent: _bounceController,
        curve: Curves.easeInOut,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(Duration(milliseconds: 500));
      if (!mounted) return;
      _bounceController.repeat(reverse: true);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  /// 播放反向动画后移除弹窗
  void _dismiss() {
    _controller.reverse().then((_) => widget.onDismiss());
  }

  double _getPopupTopPosition() {
    final baseTop = widget.position == BubblePosition.bottom
        ? widget.targetPosition.dy - widget.targetSize.height - widget.triangleHeight * 2
        : widget.targetPosition.dy + widget.targetSize.height;

    return baseTop + widget.offsetY;
  }

  double _getPopupLeftPosition() {
    final baseLeft = widget.targetPosition.dx + widget.targetSize.width / 2 - widget.width / 2;

    return baseLeft + widget.offsetX;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _dismiss,
      child: Material(
        color: Colors.transparent,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              top: widget.targetPosition.dy,
              left: widget.targetPosition.dx,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  widget.onTap?.call();

                  _dismiss();
                },
                child: SizedBox(
                  width: widget.targetSize.width,
                  height: widget.targetSize.width,
                ),
              ),
            ),
            Positioned(
              top: _getPopupTopPosition(),
              left: _getPopupLeftPosition(),
              child: widget.showBounceAnimation
                  ? AnimatedBuilder(
                      animation: _bounceAnimation,
                      builder: (context, child) {
                        final val = _bounceController.value * widget.bounceHeight;
                        return Transform.translate(
                          offset: Offset(0, val),
                          child: child,
                        );
                      },
                      child: _contentWidget(),
                    )
                  : _contentWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _contentWidget() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: LayoutBuilder(
          builder: (context, constraints) {
            return IntrinsicHeight(
              child: IntrinsicWidth(
                child: CustomPaint(
                  painter: _BubblePainter(
                      color: widget.backgroundColor,
                      gradient: widget.gradient,
                      width: widget.width,
                      triangleWidth: widget.triangleWidth,
                      triangleHeight: widget.triangleHeight,
                      position: widget.position,
                      offsetX: widget.offsetX,
                      borderRadius: widget.borderRadius),
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: widget.position == BubblePosition.top ? widget.triangleHeight : 0,
                      bottom: widget.position == BubblePosition.bottom ? widget.triangleHeight : 0,
                    ),
                    child: widget.content,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class _BubblePainter extends CustomPainter {
  final Color color;
  final Gradient? gradient;
  final double width;
  final double triangleWidth;
  final double triangleHeight;
  final BubblePosition position;
  final double offsetX;
  final double borderRadius;

  _BubblePainter({
    required this.color,
    this.gradient,
    required this.width,
    required this.triangleWidth,
    required this.triangleHeight,
    required this.position,
    required this.offsetX,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = color;

    if (gradient != null) {
      paint.shader = gradient!.createShader(Offset.zero & size);
    }

    final path = Path();
    final rect =
        Rect.fromLTWH(0, position == BubblePosition.top ? triangleHeight : 0, width, size.height - triangleHeight);
    final rRect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    path.addRRect(rRect);

    if (position == BubblePosition.top) {
      path.moveTo(width / 2 - triangleWidth / 2 - offsetX, triangleHeight);
      path.lineTo(width / 2 - offsetX, 0);
      path.lineTo(width / 2 + triangleWidth / 2 - offsetX, triangleHeight);
    } else {
      path.moveTo(width / 2 - triangleWidth / 2 - offsetX, size.height - triangleHeight);
      path.lineTo(width / 2 - offsetX, size.height);
      path.lineTo(width / 2 + triangleWidth / 2 - offsetX, size.height - triangleHeight);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
