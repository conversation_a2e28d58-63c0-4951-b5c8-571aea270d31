import 'package:biz/biz.dart';
import 'package:biz/global/widgets/sex_age_chip.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/global/widgets/user_color_name_widget.dart';
import 'package:biz/global/widgets/user_nick_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/user_info.dart';

class UserAvatarNameGenderWidget extends StatefulWidget {
  final String uid;
  final bool fromServer;
  final double nameMaxWidth;
  final bool showNameColor;
  final String? from;

  const UserAvatarNameGenderWidget({
    super.key,
    required this.uid,
    required this.nameMaxWidth,
    this.fromServer = false,
    this.showNameColor = true,
    this.from,
  });

  @override
  State<UserAvatarNameGenderWidget> createState() => _UserAvatarNameGenderWidgetState();
}

class _UserAvatarNameGenderWidgetState extends State<UserAvatarNameGenderWidget> {
  UserInfo? _userInfo;

  void _fetchUserInfo() async {
    _userInfo = await userService.getUserInfo(widget.uid, fromServer: widget.fromServer);
    if (mounted) setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
  }

  @override
  void didUpdateWidget(covariant UserAvatarNameGenderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.uid != widget.uid) {
      _fetchUserInfo();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.uid.isEmpty) return SizedBox.shrink();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            routerUtil.push(R_USER_INFO, params: {P_UID: widget.uid, P_STATISTIC_FROM: widget.from});
          },
          child: UserAvatar(
            url: _userInfo?.avatar,
            size: 24.pt,
            hasFrame: true,
            userId: _userInfo?.uid,
            avatarCodeError: () => const SizedBox.shrink(),
          ),
        ),
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: widget.nameMaxWidth),
          child: widget.showNameColor
              ? UserNickWidget(
                  child: UserColorNameWidget(
                    maxLines: 1,
                    style: TextStyle(
                      color: R.color.color20,
                      fontSize: 15.sp,
                      fontWeight: FontWeightExt.heavy,
                    ),
                    name: _userInfo?.displayedName ?? '',
                    uid: _userInfo?.uid,
                    key: ValueKey(_userInfo?.uid),
                    needListener: false,
                  ),
                  vipLevel: _userInfo?.vipInfo?.level ?? 0,
                )
              : Text(
                  _userInfo?.displayedName ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: R.color.color20,
                    fontSize: 15.sp,
                    fontWeight: FontWeightExt.heavy,
                  ),
                ),
        ),
        5.wSpace,
        GenderIconWidget(sex: _userInfo?.sex)
      ],
    );
  }
}
