import 'dart:ui';

import 'package:biz/biz.dart';
import 'package:service/modules/family/model/family_config_model.dart';
import 'package:service/modules/family/model/family_info_model.dart';

extension FamilyInfoModelExt on FamilyInfoModel {
  Future<FamilyLevelConfig?> get levelConfig async => await familyService.getFamilyLevelConfig(level: level ?? 0);

  int? get canEditCoverTime => editTimeModel?.cover;

  Future<bool> get canCoverEdit async => _canEdit(canEditCoverTime);

  Future<String> get coverRemainingDays async => _remainingDays(canEditCoverTime);

  int? get canEditNameTime => editTimeModel?.familyName;

  Future<bool> get canNameEdit async => _canEdit(canEditNameTime);

  int? get canEditSloganTime => editTimeModel?.announcement;

  Future<bool> get canSloganEdit async => _canEdit(canEditSloganTime);

  Future<bool> _canEdit(int? time) async {
    if (time == null) return true;
    if (time <= 0) return true;

    final timeFix = await getService<AbsStimeService>()?.getTimeFix() ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000 + timeFix;

    return currentTime > time;
  }

  Future<String> _remainingDays(int? time) async {
    if (time == null) return '';
    if (time <= 0) return '';

    final deathLine = DateTime.fromMillisecondsSinceEpoch(canEditCoverTime! * 1000);
    final timeFix = await getService<AbsStimeService>()?.getTimeFix() ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch + timeFix * 1000;

    final date = DateTime.fromMillisecondsSinceEpoch(currentTime);
    final days = deathLine.difference(date).inDays;
    if (days > 0) {
      return LocaleStrings.instance.daysTimeout(days).toLowerCase();
    }
    final hours = deathLine.difference(date).inHours;
    if (hours > 0) {
      return LocaleStrings.instance.hoursTimeout(hours).toLowerCase();
    }
    final seconds = deathLine.difference(date).inSeconds;
    if (seconds > 0) {
      return LocaleStrings.instance.secondsTimeout(seconds).toLowerCase();
    }
    return '';
  }
}

extension FamilyLevelConfigExt on FamilyLevelConfig {
  List<Color> get gradientColors =>
      bgColors?.mapNotNull((e) => e.trim().toColor).toList() ?? [const Color(0xFF58EEBB), const Color(0xFF34B6BB)];
}
