import 'dart:convert';

import 'package:biz/biz.dart';
import 'package:biz/biz/family/family_home/widget/family_join_result_widget.dart';
import 'package:biz/biz/share/share_widget.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/common/statistics/family_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/biz_error_code.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/family/model/family_info_model.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/share/const/events.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/loading.dart';

/// 家族详情的分享按钮
class FamilyShareWidget extends StatelessWidget {
  final FamilyInfoModel? familyInfoModel;
  final Color? bgColor;
  final Color? iconColor;
  FamilyShareWidget({this.familyInfoModel, this.bgColor, this.iconColor});

  @override
  Widget build(BuildContext context) {
    if (familyInfoModel == null) return const SizedBox.shrink();

    final isFreeze = familyInfoModel!.isFrozen == 1;
    return GeneralIconBtn(
      icon: isFreeze ? Res.familyIconCantShare : Res.roomShare,
      width: 26.pt,
      height: 26.pt,
      iconSize: Size(18.pt, 18.pt),
      iconColor: iconColor,
      backgroundColor: bgColor ?? Color(0xFFFBFBFB).withOpacity(0.2),
      onTap: () => isFreeze ? null : _showFamilyShareDialog(familyInfo: familyInfoModel!, context: context),
    );
  }
}

/// 家族分享弹窗
void _showFamilyShareDialog({required FamilyInfoModel familyInfo, BuildContext? context}) async {
  if (familyInfo.id?.isEmpty ?? true) return;

  FamilyStatistics.reportFamilyDetailsShareClick(familyId: familyInfo.id);

  /// 拼接deeplink
  var deepLink = "${DeepLink.deepLinkProto}${R_FAMILY_DETAIL.replaceFirst("/", "")}"
      "${"?$P_ID=${familyInfo.id}"}${"&$P_STATISTIC_FROM=share"}";

  String content = LocaleStrings.instance.familyShareContent(familyInfo.name ?? "");

  /// 分享窗
  showShareDialog(
    title: LocaleStrings.instance.inviteFriends,
    contactBtnTitle: LocaleStrings.instance.invite,
    context: FLRouter.routeObserver.getLastContext(),
    showFriends: false,
    showIns: true,
    shareWidgetType: ShareWidgetType.withContact,
    margin: EdgeInsets.symmetric(horizontal: 15.pt),
    filterFamily: true,
    contactSelectWithClose: false,
    contactSelectCallback: (userInfo) async {
      showLoading();
      final rsp = await familyService.inviteJoinFamily(familyId: familyInfo.id ?? '', targetId: userInfo.uid);
      hideLoading();

      final styleConfirm = TextStyle(color: R.color.primaryColor, fontSize: 16.sp, fontWeight: FontWeightExt.heavy);
      final confirm = LocaleStrings.instance.confirm;
      if (rsp.isSuccess) {
        rxUtil.send(ShareEvent.shareContacts, userInfo.uid);
        toast(LocaleStrings.instance.shareSuccess);

        FamilyStatistics.reportFamilyDetailsInviteSucc(
            toGender: userInfo.sexStr, toUid: userInfo.uid, familyId: familyInfo.id);
      } else if (rsp.code == BizErrorCode.familyOnCooling) {
        final canJoinTime = rsp.data?.canJoinTime ?? 0;
        final timeFix = await getService<AbsStimeService>()?.getTimeFix() ?? 0;
        final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000 + timeFix;
        final diff = canJoinTime - currentTime;
        if (diff > 0) {
          showAlertDialog(
            contentWidget: FamilyJoinResultWidget(
              totalSeconds: diff,
              title: rsp.msg ?? '',
              onTimeOut: () => routerUtil.pop(context: context),
            ),
            confirmText: confirm,
            styleConfirm: styleConfirm,
            showCancel: false,
            forceShow: true,
            context: context,
          );
        } else {
          showAlertDialog(
            content: rsp.msg ?? LocaleStrings.instance.pleaseTryAgain,
            confirmText: confirm,
            styleConfirm: styleConfirm,
            showCancel: false,
            forceShow: true,
            context: context,
          );
        }
      } else {
        showAlertDialog(
          content: rsp.msg ?? LocaleStrings.instance.pleaseTryAgain,
          confirmText: confirm,
          styleConfirm: styleConfirm,
          showCancel: false,
          forceShow: true,
          context: context,
        );
        Log.e('FamilyShare', rsp.msg ?? '');
      }
    },
    shareStatisticCallback: (type, ShareApps? share, user) {
      if (user != null) {
        // FamilyStatistics.reportFamilyDetailsShareSucc(toGender: user.sexStr, toUid: user.uid, familyId: familyInfo.id);
      } else if (share != null) {
        String? shareType;
        switch (share) {
          case ShareApps.extra:
            shareType = "moment";
            break;
          default:
            shareType = "${share.name}";
        }
        FamilyStatistics.reportFamilyDetailsShareSucc(toUid: shareType, familyId: familyInfo.id);
      }
    },
    template: FamilyShare(
        imContent: json.encode(familyInfo.toJson()),
        content: content,
        // familyInfo: familyInfo,
        deepLink: deepLink),
  );
}
