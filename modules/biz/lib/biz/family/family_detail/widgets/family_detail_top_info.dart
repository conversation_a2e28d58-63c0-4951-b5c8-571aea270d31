import 'package:biz/biz.dart';
import 'package:biz/biz/family/family_detail/widgets/family_ranking_widget.dart';
import 'package:biz/biz/family/family_detail/widgets/family_title_widget.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_party_list_item.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/family_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/modules/family/model/family_config_model.dart';
import 'package:service/modules/family/model/family_honor_model.dart';
import 'package:service/modules/family/model/family_info_model.dart';
import 'package:service/modules/family/model/family_member_model.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';

import 'family_representative_widget.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

class FamilyDetailTopInfo extends StatefulWidget {
  final FamilyInfoModel? familyInfoModel;
  final FamilyHonorModel? familyHonorModel;
  final List<FamilyMemberModel>? familyMemberList;
  final RoomPartyListItemEntity? familyRoomInfo;
  final FamilyLevelConfig? levelConfig;
  final int? role;
  final bool isJoinTheFamily;
  final int treasureBalance;
  final int taskExp;

  FamilyDetailTopInfo({
    this.role,
    this.familyInfoModel,
    this.familyHonorModel,
    this.familyMemberList,
    this.familyRoomInfo,
    this.levelConfig,
    this.isJoinTheFamily = false,
    this.treasureBalance = 0,
    this.taskExp = 0,
  });

  @override
  State<StatefulWidget> createState() => _FamilyDetailTopInfoState();
}

class _FamilyDetailTopInfoState extends State<FamilyDetailTopInfo> {
  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        10.hSpace,
        _infoWidget(),
        SizedBox(height: 12.pt),
        _sloganWidget(),
        SizedBox(height: 12.pt),
        _bottomInfo(),
      ],
    );
  }

  Widget _bottomInfo() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(14.pt)),
        color: Colors.white,
      ),
      padding: EdgeInsets.only(top: 14.pt),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FamilyRepresentativeWidget(
              role: widget.role, familyId: widget.familyInfoModel?.id ?? '', list: widget.familyMemberList),
          if (widget.familyRoomInfo != null) ...[
            FamilyTitleWidget(title: LocaleStrings.instance.familyRoom),
            10.hSpace,
            RoomPartyListItem(
              item: widget.familyRoomInfo!,
              index: 0,
              padding: EdgeInsets.symmetric(horizontal: 6.pt),
              onTap: () {
                final roomId = widget.familyRoomInfo?.rid ?? '';
                final familyId = widget.familyInfoModel?.id ?? '';
                LiveRoomHandler.joinRoom(roomId, from: StatisticPageFrom.familyProfile);

                FamilyStatistics.reportFamilyDetailsRoomEnter(familyId: familyId, roomId: roomId);
              },
            ),
          ],
          13.hSpace,
          FamilyRankingWidget(
            familyId: widget.familyInfoModel?.id,
            levelInfo: widget.familyInfoModel?.levelInfo,
            levelConfig: widget.levelConfig,
            isJoinTheFamily: widget.isJoinTheFamily,
            taskExp: widget.taskExp,
          ),
          20.hSpace,
        ],
      ),
    );
  }

  Widget _infoWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 22.pt, right: 13.pt),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.pt),
            child: GlobalWidgets.cachedNetImage(
                imageUrl: widget.familyInfoModel?.cover ?? '', width: 80.pt, height: 80.pt),
          ),
          20.wSpace,
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 200.pt),
                child: Text(
                  widget.familyInfoModel?.name ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontWeight: FontWeightExt.heavy, fontSize: 19.sp, color: Colors.white),
                ),
              ),
              SizedBox(height: 10.pt),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  FLImage.asset(Res.familyIconListMember, height: 11.pt, fit: BoxFit.cover),
                  5.wSpace,
                  Text(
                    '${widget.familyInfoModel?.currMember}/${widget.familyInfoModel?.maxMember}',
                    style: TextStyle(fontSize: 11.sp, color: const Color(0xFFECECEC), fontWeight: FontWeightExt.medium),
                  ),
                  17.wSpace,
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 120.pt),
                    child: Text(
                      LocaleStrings.instance.roomId(widget.familyInfoModel?.id ?? ''),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: const Color(0xFFECECEC),
                        fontWeight: FontWeightExt.medium,
                      ),
                    ),
                  ),
                  5.wSpace,
                  ScaleTapWidget(
                    onTap: () {
                      String id = widget.familyInfoModel?.id ?? '';
                      Clipboard.setData(ClipboardData(text: id));
                      toast("${LocaleStrings.instance.copySuccess}");
                    },
                    child: FLImage.asset(
                      Res.familyIconCopy,
                      height: 11.pt,
                      fit: BoxFit.cover,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.pt),
              Row(
                children: [
                  _familyTreasury(),
                  10.wSpace,
                  GestureDetector(
                      onTap: () {
                        if (widget.isJoinTheFamily) {
                          routerUtil.push(R_FAMILY_LEVEL, params: {P_ID: widget.familyInfoModel?.id ?? ''});
                        }
                      },
                      behavior: HitTestBehavior.opaque,
                      child: CachedNetworkImage(
                          imageUrl: widget.levelConfig?.icon ?? '', height: 20.pt, fit: BoxFit.cover)),
                ],
              )
            ],
          ),
          Spacer(),
          Visibility(
            visible: widget.isJoinTheFamily,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GeneralIconBtn(
                  icon: Res.familyIconSetting,
                  width: 26.pt,
                  height: 26.pt,
                  iconSize: Size(18.pt, 18.pt),
                  backgroundColor: Color(0xFFFBFBFB).withOpacity(0.2),
                  onTap: () => routerUtil.push(R_FAMILY_SETTING, params: {
                    P_ID: widget.familyInfoModel?.id,
                    P_FAMILY_ROLE: widget.role
                    //     // P_FAMILY_ROLE: widget.familyInfoModel?.familyRole,
                  }),
                ),
                14.hSpace,
                GeneralIconBtn(
                  icon: Res.familyIconChatGroup,
                  width: 26.pt,
                  height: 26.pt,
                  iconSize: Size(18.pt, 18.pt),
                  backgroundColor: Color(0xFFFBFBFB).withOpacity(0.2),
                  onTap: () {
                    final fId = widget.familyInfoModel?.id ?? '';
                    if (fId.isEmpty) return;

                    routerUtil.push(R_CHAT,
                        params: {
                          P_TARGET_ID: 'family_$fId',
                          P_CONVERSATION_TYPE: RCIMIWConversationType.group,
                          P_STATISTIC_FROM: StatisticPageFrom.familyProfile,
                        },
                        context: context);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _familyTreasury() {
    return GestureDetector(
      onTap: widget.isJoinTheFamily
          ? () => routerUtil.push(R_FAMILY_TREASURY, params: {P_ID: widget.familyInfoModel?.id})
          : null,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.centerLeft,
        children: [
          Container(
            height: 15.pt,
            padding: EdgeInsets.only(left: 29.pt, right: 7.pt),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7.pt),
              color: Colors.white.withOpacity(0.68),
            ),
            constraints: BoxConstraints(minWidth: 45.pt),
            child: Row(
              // mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '${NumFormatUtils.numFormat(widget.treasureBalance)}',
                  style: TextStyle(
                    fontSize: 13.pt,
                    fontWeight: FontWeightExt.heavy,
                    color: const Color(0xFFF77A27),
                  ),
                ),
                5.wSpace,
                Visibility(
                  visible: widget.isJoinTheFamily,
                  child: FLImage.asset(Res.familyIconGoldArrow, height: 10.pt, fit: BoxFit.cover),
                ),
              ],
            ),
          ),
          Positioned(
            left: -4.pt,
            child: FLImage.asset(Res.familyIconGold, width: 29.pt, height: 18.pt),
          )
        ],
      ),
    );
  }

  Widget _sloganWidget() {
    final slogan = widget.familyInfoModel?.announcement ?? '';
    return GestureDetector(
      onTap: () {
        showAlertDialog(
          title: LocaleStrings.instance.familySlogan,
          content: slogan,
          styleContent: TextStyle(fontSize: 14.sp, color: Color(0xff202020), fontWeight: FontWeightExt.heavy),
          confirmText: LocaleStrings.instance.confirm,
          styleConfirm: TextStyle(color: R.color.primaryColor, fontSize: 16.sp, fontWeight: FontWeightExt.heavy),
          showCancel: false,
        );
      },
      child: Padding(
        padding: EdgeInsets.only(left: 24.pt, right: 14.pt),
        child: Row(
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 280.pt,
              ),
              child: Text(
                slogan,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12.sp,
                  fontWeight: FontWeightExt.medium,
                ),
              ),
            ),
            Spacer(),
            Row(
              children: [
                Text(
                  LocaleStrings.instance.more,
                  style: TextStyle(
                    color: const Color(0xFFFFF450),
                    fontSize: 13.sp,
                    fontWeight: FontWeightExt.medium,
                  ),
                ),
                4.wSpace,
                FLImage.asset(Res.familyIconSloganArrow, height: 9.pt, fit: BoxFit.cover)
              ],
            )
          ],
        ),
      ),
    );
  }
}
