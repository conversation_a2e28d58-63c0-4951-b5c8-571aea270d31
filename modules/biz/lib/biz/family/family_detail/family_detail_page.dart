import 'package:biz/biz.dart';
import 'package:biz/biz/family/family_detail/widgets/family_share_widget.dart';
import 'package:biz/biz/family/family_extension.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/app_bar/icon_btns.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/styled_tab_indicator.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/family/model/family_tabs_model.dart';

import 'bloc/family_detail_bloc.dart';
import 'family_rank_page/family_rank_page.dart';
import 'widgets/family_detail_top_info.dart';
import 'widgets/join_or_invited_btn.dart';
import 'widgets/persisten_header_builder.dart';

@FRoute(desc: "家族详细页面", url: R_FAMILY_DETAIL)
class FamilyDetailPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _FamilyDetailPageState();
}

class _FamilyDetailPageState extends State<FamilyDetailPage> with TickerProviderStateMixin {
  late FamilyDetailBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = FamilyDetailBloc(tickerProvider: this);
  }

  @override
  void dispose() {
    _bloc.scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    final padding = MediaQuery.paddingOf(context);
    _bloc.appBarPaddingTop = padding.top;
    return LifecycleBlocBuilder<FamilyDetailBloc, FamilyDetailState>(
      bloc: _bloc,
      builder: (_, __) {
        return Stack(
          fit: StackFit.expand,
          children: [
            ColoredBox(color: Colors.white),
            _bg(_bloc.state.model?.profile?.gloryLevel ?? 0),
            Positioned(
              top: 97.pt,
              right: -45.pt,
              child: Opacity(
                opacity: 0.14,
                child: CachedNetworkImage(
                  imageUrl: _bloc.state.levelConfig?.badgeBg ?? '',
                  width: 190.pt,
                  fit: BoxFit.cover,
                  errorWidget: (_, __, ___) => const SizedBox.shrink(),
                ),
              ),
            ),
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: CommonAppBar(
                leading: backBtn(iconColor: Color.lerp(Colors.white, Colors.black, _bloc.state.appbarOpacity)),
                actions: [
                  Visibility(
                    visible: _bloc.state.model?.isPatriarch ?? false,
                    child: FamilyShareWidget(
                      familyInfoModel: _bloc.state.model?.profile,
                      bgColor:
                          Color.lerp(Colors.white.withOpacity(0.2), const Color(0x1AA3AFC2), _bloc.state.appbarOpacity),
                      iconColor: Color.lerp(Colors.white, Colors.black, _bloc.state.appbarOpacity),
                    ),
                  ),
                  Container(
                    alignment: Alignment.center,
                    child: IconBtn(
                      icon: Res.familyIconTips,
                      iconSize: Size(10.pt, 15.pt),
                      widgetSize: Size(26.pt, 26.pt),
                      iconColor: Color.lerp(Colors.white, Colors.black, _bloc.state.appbarOpacity),
                      margin: EdgeInsets.symmetric(horizontal: 14.pt),
                      bgDecoration: BoxDecoration(
                        color: Color.lerp(
                            Colors.white.withOpacity(0.2), const Color(0x1AA3AFC2), _bloc.state.appbarOpacity),
                        borderRadius: BorderRadius.circular(13.pt),
                      ),
                      onTap: () {
                        routerUtil.push(R_WEB, params: {P_URL: urlFamilyFaq});
                      },
                    ),
                  ),
                ],
                title: LocaleStrings.instance.familyCapital,
                titleColor: Color.lerp(Colors.white, Colors.black, _bloc.state.appbarOpacity),
                systemOverlayStyle:
                    _bloc.state.appbarOpacity == 1 ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light,
                backgroundColor: Colors.white.withOpacity(_bloc.state.appbarOpacity),
              ),
              body: _body(),
            ),
            if ((_bloc.state.model?.userFamily?.familyId == _bloc.familyId ||
                    (_bloc.state.model?.userFamily?.familyId?.isEmpty ?? true)) &&
                _bloc.state.model != null)
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Visibility(
                  visible: !_bloc.state.isJoinTheFamily && _bloc.state.model?.profile?.applyStatus == 1,
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      IgnorePointer(
                        ignoring: true,
                        child: Container(
                          height: 145.pt,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.white.withOpacity(0), Colors.white.withOpacity(1)],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 15.pt, right: 15.pt, bottom: 26.pt),
                        child: JoinOrInvitedBtn(
                          familyId: _bloc.familyId,
                          isInFamily: _bloc.state.isJoinTheFamily,
                          isInApply: _bloc.state.model?.profile?.applyStatus == 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _bg(int level) {
    final levelGradient = _bloc.state.levelConfig?.gradientColors ?? [const Color(0xFF58EEBB), const Color(0xFF34B6BB)];
    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      child: Container(
        width: 1.w,
        height: 1.w * 246 / 375,
        decoration: BoxDecoration(
          gradient: levelGradient.isNotEmpty
              ? LinearGradient(
                  colors: levelGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
        ),
      ),
      // child: FLImage.asset(_getBgRes(level), width: 1.w, fit: BoxFit.cover),
    );
  }

  Widget _body() {
    if (_bloc.state.isError) {
      return GlobalWidgets.netFailedWidget(
          btnAction: () => _bloc.add(InitDataEvent()),
          height: 300.pt,
          logoSize: Size(150.pt, 120.pt),
          padding: EdgeInsets.symmetric(horizontal: 30.pt));
    } else if (_bloc.state.model == null) {
      return GlobalWidgets.pageLoading();
    } else {
      return ExtendedNestedScrollView(
        controller: _bloc.scrollController,
        headerSliverBuilder: _headerSliverBuilder,
        body: _bloc.tabController == null ? SizedBox.expand() : _tabView(),
      );
    }
  }

  List<Widget> _headerSliverBuilder(BuildContext context, bool innerBoxIsScrolled) {
    return <Widget>[
      SliverOverlapAbsorber(
        handle: ExtendedNestedScrollView.sliverOverlapAbsorberHandleFor(context),
        sliver: SliverToBoxAdapter(
          child: FamilyDetailTopInfo(
            isJoinTheFamily: _bloc.state.isJoinTheFamily,
            role: _bloc.state.familyRole,
            familyHonorModel: _bloc.state.model?.familyHonor,
            familyInfoModel: _bloc.state.model?.profile,
            familyMemberList: _bloc.state.members,
            familyRoomInfo: _bloc.state.model?.roomInfo,
            treasureBalance: _bloc.state.treasureBalance ?? 0,
            levelConfig: _bloc.state.levelConfig,
            taskExp: _bloc.state.taskExp ?? 0,
          ),
        ),
      ),
      SliverPersistentHeader(
        pinned: true,
        floating: true,
        delegate: PersistentHeaderBuilder(
            min: 40.pt,
            max: 40.pt,
            builder: (BuildContext context, double offset, bool overlapsContent) {
              return Container(
                height: 40.pt,
                color: Colors.white,
                // padding: EdgeInsets.symmetric(horizontal: 16.pt),
                alignment: Alignment.center,
                child: _bloc.tabController == null ? SizedBox.shrink() : _tabBar(),
              );
            }),
      )
    ];
  }

  Widget _tabBar() {
    if (_bloc.tabBar == null) {
      _bloc.tabBar = TabBar(
        controller: _bloc.tabController,
        unselectedLabelStyle: TextStyle(fontSize: 13.sp, color: R.color.colorB2, fontWeight: FontWeightExt.medium),
        labelStyle: TextStyle(fontSize: 15.sp, color: R.color.color20, fontWeight: FontWeightExt.medium),
        labelPadding: EdgeInsets.zero,
        indicatorSize: TabBarIndicatorSize.label,
        // isScrollable: true,
        indicator: StyledTabIndicator(
          margin: 0.pt,
          width: 15.pt,
          height: 2.pt,
          fillColor: R.color.primaryColor,
        ),
        tabs: _tabs(),
      );
    }
    return _bloc.tabBar!;
  }

  List<Widget> _tabs() {
    final tabs = <Widget>[];
    final s = LocaleStrings.instance;
    for (int index = 0; index < _bloc.state.tabs.length; index++) {
      final element = _bloc.state.tabs[index];
      var title = '';
      switch (element) {
        case FamilyTabsModel.typeWeeklyContribution:
          title = s.weeklyContribution;
          break;
        case FamilyTabsModel.typeMonthlyContribution:
          title = s.monthlyContribution;
          break;
        default:
          title = element.replaceAll('_', ' ');
      }
      // var title = element == 'gift_sent' ? s.giftSent : (element == 'gift_receive' ? s.giftReceive : s.honor);
      tabs.add(Tab(child: Text(title)));
    }
    return tabs;
  }

  Widget _tabView() {
    if (_bloc.tabView == null) {
      _bloc.tabView = TabBarView(
          controller: _bloc.tabController,
          children: _bloc.state.tabs.map((e) {
            return FamilyRankPage(type: e, familyId: _bloc.familyId);
          }).toList());
    }
    return ColoredBox(color: Colors.white, child: _bloc.tabView!);
  }
}
