import 'package:biz/biz.dart';
import 'package:biz/biz/family/family_extension.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/family_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/family/model/family_config_model.dart';
import 'package:service/modules/family/model/family_info_model.dart';

class MyFamilyCardWidget extends StatelessWidget {
  MyFamilyCardWidget({super.key, this.familyInfo, this.levelConfig});

  final FamilyInfoModel? familyInfo;
  final FamilyLevelConfig? levelConfig;

  @override
  Widget build(BuildContext context) {
    final hasFamily = familyInfo?.status == 1;
    final levelGradient = hasFamily
        ? (levelConfig?.gradientColors ?? [const Color(0xFFAFB5D7), const Color(0xFFC4CAE0)])
        : [const Color(0xFFAFB5D7), const Color(0xFFC4CAE0)];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleStrings.instance.myFamily,
          style: TextStyle(
            color: R.color.color20,
            fontSize: 13.sp,
            fontWeight: FontWeightExt.heavy,
          ),
        ),
        14.hSpace,
        Container(
          height: 79.pt,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.pt),
            gradient: levelGradient.isEmpty
                ? null
                : LinearGradient(
                    colors: levelGradient,
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                  ),
          ),
          child: hasFamily ? _familyWidget() : _buildCreateWidget(),
        )
      ],
    );
  }

  /// 创建家族
  Widget _buildCreateWidget() {
    final inReview = familyInfo?.isInReview ?? false;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        final user = await userService.getCurrentUserInfo();
        final isTmpLock = await user?.isTmpBaned;
        if ( isTmpLock?? false) {
          toast(LocaleStrings.instance.beRestrictedFor);
          return;
        }

        routerUtil.push(R_FAMILY_CREATE);

        FamilyStatistics.reportFamilySquareCreateClick();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: 0,
            right: 0,
            child: FLImage.asset(Res.familyImgCreate, height: 79.pt, fit: BoxFit.cover),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!inReview) ...[
                Container(
                  width: 22.pt,
                  height: 22.pt,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.16),
                    borderRadius: BorderRadius.circular(11.pt),
                  ),
                  child: UnconstrainedBox(
                    child: FLImage.asset(Res.familyIconCreate, height: 13.pt, fit: BoxFit.cover),
                  ),
                ),
                14.wSpace,
              ],
              Text(
                inReview ? LocaleStrings.instance.inReview : LocaleStrings.instance.createFamily,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeightExt.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _familyWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        routerUtil.push(R_FAMILY_DETAIL, params: {P_ID: familyInfo?.id, P_STATISTIC_FROM: 'my_family'});

        FamilyStatistics.reportFamilySquareEntermy(familyId: familyInfo?.id);
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: -20.pt,
            right: -10.pt,
            child: Opacity(
                opacity: 0.2,
                child: CachedNetworkImage(imageUrl: levelConfig?.badgeBg ?? '', height: 140.pt, fit: BoxFit.cover)),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.pt),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 58.pt,
                  height: 58.pt,
                  alignment: Alignment.center,
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.34),
                    borderRadius: BorderRadius.circular(6.pt),
                  ),
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(6.pt)),
                    child: CachedNetworkImage(
                        imageUrl: familyInfo?.cover ?? '', width: 54.pt, height: 54.pt, fit: BoxFit.cover),
                  ),
                ),
                20.wSpace,
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: 180.pt),
                        child: Text(
                          familyInfo?.name ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeightExt.bold,
                          ),
                        ),
                      ),
                      6.hSpace,
                      Row(
                        children: [
                          CachedNetworkImage(imageUrl: levelConfig?.icon ?? '', height: 20.pt, fit: BoxFit.cover),
                          Spacer()
                        ],
                      )
                    ],
                  ),
                ),
                CachedNetworkImage(imageUrl: levelConfig?.badge ?? '', height: 69.pt, fit: BoxFit.cover),
                10.wSpace,
              ],
            ),
          ),
        ],
      ),
    );
  }
}
