import 'package:biz/biz.dart';
import 'package:biz/biz/banner_widget/banner_widget.dart';
import 'package:biz/biz/family/family_home/family_home_controller.dart';
import 'package:biz/biz/family/family_home/family_hot_page/family_hot_page.dart';
import 'package:biz/biz/family/family_home/widget/family_list.dart';
import 'package:biz/biz/family/family_home/widget/my_family_card.dart';
import 'package:biz/biz/home/<USER>/home_style_tab_indicator.dart';
import 'package:biz/biz/live_room/page/room_party/room_list_page/room_party_page.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/app_bar/icon_btns.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/nav/nav_bg_widget.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/global/widget/keep_wrapper.dart';
import 'package:service/modules/ad/const/const.dart';
import 'package:service/modules/live/room/model/room_party_tab_list.dart';

@FRoute(desc: "家族广场", url: R_FAMILY_HOME)
class FamilyHomePage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _FamilyHomePageState();
  }
}

class _FamilyHomePageState extends State<FamilyHomePage>
    with GetStateBuilderMixin<FamilyHomePage, FamilyHomeController>, SingleTickerProviderStateMixin {
  final ScrollController _sController = ScrollController();

  final _tabTitles = [
    LocaleStrings.instance.familyRoom,
    LocaleStrings.instance.hot,
    LocaleStrings.instance.nw,
  ];

  late final TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: _tabTitles.length, vsync: this, initialIndex: 0);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _sController.dispose();

    super.dispose();
  }

  @override
  FamilyHomeController initCtl() {
    return FamilyHomeController();
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusLoading() => GlobalWidgets.scaffoldPageLoading();

  @override
  Widget buildStatusContent(BuildContext context) {
    return Stack(
      children: [
        Container(color: const Color(0xFFF5F7F9)),
        NavBgWidget(asset: Res.roomBgPage),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CommonAppBar(
            backgroundColor: Colors.transparent,
            leading: backBtn(),
            title: LocaleStrings.instance.familyCapital,
            actions: [
              Container(
                alignment: Alignment.center,
                child: IconBtn(
                  icon: Res.familyIconTips,
                  iconSize: Size(10.pt, 15.pt),
                  widgetSize: Size(26.pt, 26.pt),
                  margin: EdgeInsets.symmetric(horizontal: 14.pt),
                  bgDecoration:
                      BoxDecoration(color: Colors.white.withOpacity(0.4), borderRadius: BorderRadius.circular(13.pt)),
                  onTap: () {
                    routerUtil.push(R_WEB, params: {P_URL: urlFamilyFaq});
                  },
                ),
              ),
            ],
          ),
          body: ExtendedNestedScrollView(
            controller: _sController,
            onlyOneScrollInBody: true,
            headerSliverBuilder: (context, boxIsScrolled) {
              return [_bodyContentHeader(context)];
            },
            body: _tabBarView(), //_listWidget(),
          ),
        )
      ],
    );
  }

  SliverOverlapAbsorber _bodyContentHeader(BuildContext context) {
    return SliverOverlapAbsorber(
      handle: ExtendedNestedScrollView.sliverOverlapAbsorberHandleFor(context),
      sliver: SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(top: 10.pt),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _banner(),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.pt, vertical: 10.pt),
                child: Obx(() {
                  return MyFamilyCardWidget(familyInfo: getCtl.familyInfo.value, levelConfig: getCtl.levelConfig);
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _banner() {
    return BannerWidget(
      type: BannerType.familyList,
      itemHeight: (1.w - 20.pt) * 115 / 356,
      margin: EdgeInsets.only(left: 10.pt, right: 10.pt, bottom: 10.pt),
      from: 'family',
    );
  }

  Widget _topTabBar() {
    final tabList = _tabTitles.mapNotNull(Text.new).toList();
    return SizedBox(
      height: 50,
      child: Row(
        children: [
          12.wSpace,
          Expanded(
              child: TabBar(
            isScrollable: true,
            controller: _tabController,
            labelStyle: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeightExt.black,
              color: R.color.color20,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeightExt.heavy,
              color: R.color.colorB2,
            ),
            labelPadding: EdgeInsets.only(right: 30.pt),
            indicatorSize: TabBarIndicatorSize.label,
            indicator: HomeStyledTabIndicator(
              margin: 11.pt,
              width: 20.pt,
              height: 4.pt,
            ),
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            tabs: tabList,
            onTap: (index) {
              final page = index == 0
                  ? 'room'
                  : index == 1
                      ? 'list'
                      : 'new';
              getCtl.reportImp(page);
            },
          )),
          _searchWidget(),
          5.wSpace,
        ],
      ),
    );
  }

  Widget _tabBarView() {
    return Column(
      children: [
        _topTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              KeepWrapper(
                child: RoomPartyLisPage(
                  tabModel: TabListItem(key: 'family', name: 'family'),
                  emptyWidget: Container(
                    height: 200,
                    alignment: Alignment.center,
                    child: SizedBox(
                      child: Column(
                        children: [
                          40.hSpace,
                          FLImage.asset(Res.emptyEmptyUserPost, width: 200.pt, height: 170.pt, fit: BoxFit.cover),
                          Text(
                            LocaleStrings.instance.noContentHere,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFFC5C5C5),
                              fontSize: 13.sp,
                              fontWeight: fontWeightRegular,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              KeepWrapper(child: FamilyHotPage()),
              KeepWrapper(child: const FamilyListWidget(tab: FamilyListTab.nw, showEmpty: true, from: "square_new")),
            ],
          ),
        )
      ],
    );
  }

  Widget _searchWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        routerUtil.push(R_FAMILY_SEARCH);
      },
      child: Padding(
        padding: EdgeInsets.all(10.pt),
        child: FLImage.asset(Res.familyIconSearch, height: 14.pt, fit: BoxFit.cover),
      ),
    );
  }
}
