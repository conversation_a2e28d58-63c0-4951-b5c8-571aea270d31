import 'dart:ui';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/event/create/room_event_create_logic.dart';
import 'package:biz/biz/live_room/page/event/widget/room_event_rule_page.dart';
import 'package:biz/biz/live_room/page/event/widget/room_event_share_widget.dart';
import 'package:biz/biz/moment/moments_publish/bloc/moment_publish_bloc.dart';
import 'package:biz/common/widgets/edit_box/edit_box_widget.dart';
import 'package:biz/common/widgets/value_picker_sheet.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/crop_image/crop_image_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/select_time_dialog/select_time_dialog.dart';
import 'package:biz/utils/res_picker/photo_video_picker.dart';
import 'package:biz/utils/time_util.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/widget/soft_keyboard_wrapper.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';
import 'package:service/modules/moments/const/enums.dart';

@FRoute(desc: "创建房间活动", url: R_EVENT_CREATE)
class RoomEventCreatePage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _RoomEventCreatePageState();
  }
}

class _RoomEventCreatePageState extends State<RoomEventCreatePage>
    with GetStateBuilderMixin<RoomEventCreatePage, RoomEventCreateLogic> {
  final GlobalKey _globalKey = GlobalKey();

  /// 图片宽高比
  final double _aspectRatio = 2;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  RoomEventCreateLogic initCtl() {
    return RoomEventCreateLogic();
  }

  @override
  Widget buildStatusLoading() => GlobalWidgets.scaffoldPageLoading();

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: LocaleStrings.instance.createEvent,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        actions: [
          Container(
            alignment: Alignment.center,
            child: IconBtn(
              icon: Res.familyIconTips,
              iconSize: Size(10.pt, 15.pt),
              widgetSize: Size(26.pt, 26.pt),
              margin: EdgeInsets.symmetric(horizontal: 14.pt),
              onTap: _showRuleAlert,
            ),
          ),
        ],
      ),
      body: SafeArea(child: _body()),
      resizeToAvoidBottomInset: false,
    );
  }

  Widget _body() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        // getCtl.showRoomMenu.value = false;
      },
      // behavior: HitTestBehavior.opaque,
      child: Stack(
        children: [
          Obx(() {
            final show = getCtl.showShare.value;
            final info = getCtl.eventInfo.value;
            return Visibility(
              visible: show,
              child: Center(
                child: RepaintBoundary(
                  key: _globalKey,
                  child: RoomEventShareWidget(eventInfo: info, imageFile: getCtl.imageResult.value?.file),
                ),
              ),
            );
          }),
          Container(
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.symmetric(horizontal: 10.pt),
                    children: [
                      SizedBox(height: 10.pt),
                      _titleWidget(LocaleStrings.instance.eventName),
                      _nameWidget(),
                      _titleWidget(LocaleStrings.instance.eventDescription, isRequired: false),
                      _descWidget(),
                      _titleWidget(LocaleStrings.instance.startDateAndTime),
                      Obx(_startTimeWidget),
                      _titleWidget(LocaleStrings.instance.endDateAndTime),
                      Obx(_endTimeWidget),
                      _buildRoomWidget(),
                      _titleWidget(LocaleStrings.instance.eventUploadBanner),
                      Obx(_bannerWidget),
                      Container(
                        padding: EdgeInsets.fromLTRB(6.pt, 16.pt, 6.pt, 16.pt),
                        child: Text(
                          LocaleStrings.instance.eventBannerLimit,
                          style: TextStyle(fontSize: 12.sp, color: Color(0xFFC3C7C9), fontWeight: FontWeight.normal),
                        ),
                      )
                    ],
                  ),
                ),
                _createBtn(),
                SizedBox(height: 35.pt),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _titleWidget(String title, {bool isRequired = true}) {
    return Container(
      alignment: Alignment.centerLeft,
      height: 40.pt,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 14.sp, color: R.color.color20, fontWeight: FontWeightExt.heavy),
          ),
          2.wSpace,
          Visibility(
            visible: isRequired,
            child: Text(
              '*',
              style: TextStyle(fontSize: 14.sp, color: Colors.red, fontWeight: FontWeightExt.heavy),
            ),
          ),
        ],
      ),
    );
  }

  /// 活动名称
  Widget _nameWidget() {
    return SizedBox(
      height: 42.pt,
      child: IgnorePointer(
        ignoring: !getCtl.canEdit,
        child: EditBoxWidget(
          hintText: LocaleStrings.instance.eventSubjectPlaceholder,
          textStyle: TextStyle(fontSize: 13.sp, color: R.color.color20, fontWeight: FontWeightExt.medium),
          controller: getCtl.nameController,
          maxLine: 1,
          maxLength: getCtl.canEdit ? 50 : null,
        ),
      ),
    );
  }

  /// 活动描述
  Widget _descWidget() {
    return SizedBox(
      height: 104.pt,
      child: IgnorePointer(
        ignoring: !getCtl.canEdit,
        child: EditBoxWidget(
          hintText:
              !getCtl.canEdit && getCtl.descController.text.isEmpty ? '' : LocaleStrings.instance.enterEventDescription,
          textStyle: TextStyle(fontSize: 13.sp, color: R.color.color20, fontWeight: FontWeightExt.medium),
          controller: getCtl.descController,
          maxLine: 10,
          maxLength: getCtl.canEdit ? 500 : null,
        ),
      ),
    );
  }

  Widget _timeWidget(String time, Color color) {
    return Container(
      height: 42.pt,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.pt),
        color: Color(0xFFF5F7F9),
      ),
      child: Row(
        children: [
          SizedBox(width: 12.pt),
          FLImage.asset(
            Res.liveEventIconClock,
            width: 13.pt,
            height: 13.pt,
            color:  R.color.colorB2,
          ),
          SizedBox(width: 10.pt),
          Text(
            time,
            style: TextStyle(fontSize: 14.sp, color: color, fontWeight: FontWeight.normal),
          ),
        ],
      ),
    );
  }

  /// 开始时间
  Widget _startTimeWidget() {
    final startTime = getCtl.startTime.value * 1000;
    final hasTime = startTime != 0;
    return GestureDetector(
      onTap: () {
        final now = DateTime.now();
        final nowTime = now.subtract(Duration(minutes: now.minute, seconds: now.second));
        final defaultTime = nowTime.add(Duration(hours: 1));
        final startDateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
        bool isBefore = startDateTime.isBefore(now);
        showSelectTimeDialog(context,
            title: LocaleStrings.instance.startDateAndTime,
            initialDateTime: isBefore ? defaultTime : startDateTime,
            minuteInterval: 5,
            minimumDate: now,
            maximumDate: now.add(Duration(days: 7)), onSelect: (int time) {
          getCtl.setStartTime(time);
        });
      },
      child: _timeWidget(
          hasTime ? TimeUtil.getDayByFormat(startTime, 'yyyy.MM.dd HH:mm') : LocaleStrings.instance.eventSelectPeriod,
          hasTime ? R.color.color20 : R.color.colorB2),
    );
  }

  /// 结束时间
  Widget _endTimeWidget() {
    final hasTime = getCtl.selectedOption.value.isNotEmpty;
    return GestureDetector(
      onTap: () {
        showValuePickerDialog(
          context: context,
          index: getCtl.options.indexWhere((e) => getCtl.selectedOption.value == e),
          values: getCtl.options,
          title: LocaleStrings.instance.timeOfDuration,
          onConfirm: (index, value) {
            getCtl.selectedOption.value = value;
            final time = (index + 1) * 60 * 60;
            getCtl.endTimeDuration.value = time;
            getCtl.setEndTime(time);
          },
        );
      },
      child: _timeWidget(hasTime ? getCtl.selectedOption.value : LocaleStrings.instance.timeOfDuration,
          hasTime ? R.color.color20 : R.color.colorB2),
    );
  }

  Widget _buildRoomWidget() {
    return Obx(() {
      final rooms = getCtl.rooms;
      if (rooms.length < 2 || !getCtl.canEdit) return SizedBox.shrink();

      final show = getCtl.showRoomMenu.value;
      return Column(
        children: [
          _titleWidget(LocaleStrings.instance.eventRoom),
          Container(
            height: show ? 48.pt : 42.pt,
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: show ? const Color(0xFFE9E9E9) : Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.pt),
                topRight: Radius.circular(10.pt),
              ),
            ),
            child: PopupMenuButton<MyRoomListItemEntity>(
              // key: ValueKey('value'),
              offset: Offset(0, 42.pt),
              padding: EdgeInsets.zero,
              elevation: 0,
              constraints: BoxConstraints(minWidth: 1.w - 20.pt),
              itemBuilder: (BuildContext context) {
                return _getRoomMenuList(rooms);
              },
              onOpened: () => getCtl.showRoomMenu.value = true,
              onCanceled: () {
                getCtl.showRoomMenu.value = false;
              },
              onSelected: (_) => getCtl.showRoomMenu.value = false,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(17.pt),
                  bottomRight: Radius.circular(17.pt),
                ),
              ),
              color: const Color(0xFFE9E9E9),
              child: Container(
                height: 42.pt,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F7F9),
                  borderRadius: BorderRadius.circular(10.pt),
                ),
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    15.wSpace,
                    Expanded(
                      child: Obx(() {
                        final info = getCtl.roomInfo.value;
                        if (info == null) return SizedBox.shrink();
                        return Text(
                          LocaleStrings.instance.eventRoomIDType(info.roomShow ?? '', info.rType),
                          style: TextStyle(
                            color: R.color.color20,
                            fontSize: 13.sp,
                            fontWeight: FontWeightExt.medium,
                          ),
                        );
                      }),
                    ),
                    FLImage.asset(Res.liveEventIconRoomArrow, width: 11.pt, fit: BoxFit.cover),
                    15.wSpace,
                  ],
                ),
              ),
            ),
          )
        ],
      );
    });
  }

  List<PopupMenuItem<MyRoomListItemEntity>> _getRoomMenuList(List<MyRoomListItemEntity> rooms) {
    return rooms.mapNotNull((e) {
      final isSelected = getCtl.roomInfo.value?.roomId == e.roomId;

      return PopupMenuItem<MyRoomListItemEntity>(
        value: e,
        height: 40.pt,
        onTap: () {
          getCtl.roomInfo.value = e;
          getCtl.showRoomMenu.value = false;
        },
        child: Text(
          LocaleStrings.instance.eventRoomIDType(e.roomShow ?? '', e.rType),
          style: TextStyle(
            color: isSelected ? R.color.primaryColor : R.color.color90,
            fontSize: 12.sp,
            fontWeight: FontWeightExt.heavy,
          ),
        ),
      );
    }).toList();
  }

  /// banner 区域
  Widget _bannerWidget() {
    return GestureDetector(
      onTap: _pickAction,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            clipBehavior: Clip.antiAlias,
            width: double.infinity,
            height: (1.w - 20.pt) / _aspectRatio,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.pt),
              color: Color(0xFFF5F7F9),
            ),
            child: Stack(
              // clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                FLImage.asset(
                  Res.liveEventIconBannerAdd,
                  width: 82.pt,
                  height: 82.pt,
                  color: const Color(0xFFDBDBDB),
                ),
                _editCoverWidget(),
              ],
            ),
          ),
          Visibility(
            visible: getCtl.hasCover && getCtl.canEdit,
            child: _deleteWidget(),
          ),
        ],
      ),
    );
  }

  /// banner 图片
  Widget _editCoverWidget() {
    final file = getCtl.imageResult.value?.file;
    if (file != null) {
      return Image.file(
        file,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      );
    } else if (getCtl.cover.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: getCtl.cover.value,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
      );
    }

    return SizedBox();
  }

  /// 删除按钮
  Widget _deleteWidget() {
    return Positioned(
      top: -6.pt,
      right: -5.pt,
      child: GestureDetector(
        onTap: () {
          getCtl.setImageList(null);
        },
        child: Container(
          width: 25.pt,
          height: 25.pt,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.5.pt),
            color: Color(0xFFFF0000),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              FLImage.asset(
                Res.liveEventIconDelete,
                width: 13.pt,
                height: 13.pt,
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 创建按钮
  Widget _createBtn() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 18.pt),
      child: SoftKeyboardWrapper(
        child: GeneralBtn(
          onTap: _create,
          height: 50.pt,
          fontSize: 19.sp,
          fontWeight: FontWeightExt.heavy,
          title: getCtl.editEventId.isEmpty ? LocaleStrings.instance.create : LocaleStrings.instance.submit,
        ),
      ),
    );
  }

  /// 选择图片事件
  void _pickAction() {
    if (!mounted || !getCtl.canEdit) {
      return;
    }
    pickPhotoVideo(
      context,
      type: MediaType.image,
      maxSize: 1,
      authPage: ReportAuthPage.createEvent,
      withCrop: (result) {
        getCtl.setImageList(result);
      },
      cropParams: CropParams(initialSize: 0.97, aspectRatio: _aspectRatio, cropQuality: 80),
    );
  }

  /// 创建事件
  void _create() {
    getCtl.create((info) {
      if (!mounted || info == null) return;

      DialogScheduler.instance().schedule(
        () => showDialog(
            (_) => _RoomEventCreateSuccessDialog(onDismiss: () => routerUtil.pop(context: context), onShare: _share),
            context: context,
            barrierDismissible: false,
            barrierColor: Colors.transparent),
        context: context,
      );
    });
  }

  void _showRuleAlert() {
    showEventRuleSheet();
  }

  void _share() async {
    // RoomHomeStatistics.reportRoomEventShareClick();
    final boundary = _globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    final image = await boundary?.toImage(pixelRatio: 3);
    final byteData = await image?.toByteData(format: ImageByteFormat.png);
    if (!mounted) return;
    if (byteData != null && image != null) {
      final pngBytes = byteData.buffer.asUint8List();
      routerUtil.popAndPush(
        R_MOMENTS_PUBLISH,
        params: {P_IMAGE_FILE: ShareImageToMomentModel(imageData: pngBytes, width: image.width, height: image.height)},
        context: context,
      );
    } else {
      routerUtil.popAndPush(R_MOMENTS_PUBLISH, context: context);
    }
  }
}

extension MyRoomListItemEntityExt on MyRoomListItemEntity {
  String get rType {
    if (roomType == 'family') return LocaleStrings.instance.family;

    return LocaleStrings.instance.personal;
  }
}

class _RoomEventCreateSuccessDialog extends StatelessWidget {
  final VoidCallback? onShare;
  final VoidCallback? onDismiss;

  const _RoomEventCreateSuccessDialog({this.onShare, this.onDismiss});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              routerUtil.pop(context: context);
              onDismiss?.call();
            },
            child: Container(color: const Color(0xBF000000)),
          ),
        ),
        Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 37.pt),
            padding: EdgeInsets.fromLTRB(20.pt, 45.pt, 20.pt, 22.pt),
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(19.pt)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  LocaleStrings.instance.eventCreatedShare,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    height: 2,
                    color: R.color.color20,
                    fontSize: 14.sp,
                    fontWeight: FontWeightExt.heavy,
                  ),
                ),
                33.hSpace,
                GeneralBtn(
                  height: 40.pt,
                  title: LocaleStrings.instance.eventShareToMoment,
                  backgroundColor: R.color.primaryColor,
                  onTap: () {
                    routerUtil.pop(context: context);
                    onShare?.call();
                  },
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
