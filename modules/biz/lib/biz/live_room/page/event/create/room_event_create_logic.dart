import 'package:biz/biz.dart';
import 'package:biz/common/widgets/edit_box/edit_box_widget.dart';
import 'package:biz/global/widgets/crop_image/crop_image_widget.dart';
import 'package:biz/global/widgets/no_enough_money_dialog.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/common/model/file_model.dart';
import 'package:service/modules/live/event/model/event_model.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/utils/loading.dart';
import 'package:service/common/statistics/room_home_statistics.g.dart';

class RoomEventCreateLogic extends AbsGetController with GetStatusCtlMix {
  /// 活动主题输入
  final EditBoxController nameController = EditBoxController();
  final EditBoxController descController = EditBoxController();

  final options = ['1 Hour', '2 Hours', '3 Hours'];

  RxString selectedOption = ''.obs;

  String get editEventId => getArgument(P_ID) ?? '';

  bool get canEdit => editEventId.isEmpty || (eventInfo.value?.isReject ?? false);

  bool get hasCover => (imageResult.value?.file != null || cover.isNotEmpty);

  RxString cover = ''.obs;
  Rxn<CropResult> imageResult = Rxn();

  RxInt startTime = 0.obs;
  RxInt endTime = 0.obs;
  RxInt endTimeDuration = 0.obs;

  Rxn<MyRoomListItemEntity> roomInfo = Rxn();

  RxList<MyRoomListItemEntity> rooms = RxList.empty();

  RxBool showRoomMenu = false.obs;

  RxBool showShare = false.obs;

  Rxn<RoomEventInfo> eventInfo = Rxn();

  String get _eventName => nameController.text;

  String get _eventDesc => descController.text;

  final _keySpSave = "_key_save_event_";

  /// sp
  final _livePref = Preferences.newInstance(spLive);

  @override
  void stateInit() {
    super.stateInit();
  }

  @override
  void stateDispose() {
    nameController.dispose();
    descController.dispose();
    super.stateDispose();
  }

  @override
  void onReady() {
    super.onReady();

    _initData();

    RoomHomeStatistics.reportRoomEventCreateImp();
  }

  // ----------------------------------------- Private -----------------------------------------

  void _initData() {
    if (editEventId.isNotEmpty) {
      fetchEventInfo();
    }
    _fetchRoomInfo();
  }

  void _fetchRoomInfo() async {
    final rsp = await roomService.myRooms();
    final list = rsp.data?.list ?? [];

    rooms.value = list;
    roomInfo.value = list.firstOrNull;

    if (viewStatus != GetStatusView.content) viewStatus = GetStatusView.content;
  }

  Future<String?> _uploadBanner() async {
    final image = imageResult.value;
    if (image?.file.path.isEmpty ?? true) {
      return null;
    }

    final result = await commonService.upload([
      FileModel(path: image!.file.path, name: "activity_pic_0", height: image.height, width: image.width),
    ]);

    var fileInfo = result?.first;
    if (fileInfo?.url?.isEmpty ?? true) {
      toast(LocaleStrings.instance.checkNetWork);
      return null;
    }
    return fileInfo?.url;
  }

  // ----------------------------------------- Public -----------------------------------------

  void setImageList(CropResult? cropResult) {
    if (isClosed) return;

    imageResult.value = cropResult;
    showShare.value = false;

    if (cropResult == null) {
      cover.value = '';
      showShare.value = false;
    }
  }

  void setStartTime(int t) {
    final time = t ~/ 1000;
    if (time != 0 && endTimeDuration != 0) {
      endTime.value = time + endTimeDuration.value;
    }
    startTime.value = time;
  }

  void setEndTime(int time) {
    if (startTime != 0) {
      endTime.value = startTime.value + time;
    }
  }

  void fetchEventInfo() async {
    if (editEventId.isEmpty) return;

    showLoading();

    final res = await eventService.getEventDetail(eventId: editEventId);

    hideLoading();
    if (isClosed) return;

    if (!res.isSuccess) {
      routerUtil.pop();
      return;
    }

    final info = res.data;

    eventInfo.value = info;

    nameController.text = info?.topic ?? '';

    descController.text = info?.description ?? '';

    cover.value = info?.cover ?? '';
    startTime.value = info?.startTime ?? 0;
    endTime.value = info?.endTime ?? 0;

    final timeOffset = endTime.value - startTime.value;
    endTimeDuration.value = timeOffset;
    if (timeOffset != 0) {
      final duration = endTimeDuration ~/ 3600;
      final idx = duration.round() - 1;
      selectedOption.value = options.getSafeElement(idx) ?? '';
    }

    if (viewStatus != GetStatusView.content) viewStatus = GetStatusView.content;
  }

  void create(Function(RoomEventInfo? info)? callback) async {
    if (_eventName.trim().isEmpty) {
      toast(LocaleStrings.instance.yourEventInfoNotFill(LocaleStrings.instance.subject));
      return;
    }
    if (startTime == 0) {
      toast(LocaleStrings.instance.yourEventInfoNotFill(LocaleStrings.instance.starTime));
      return;
    }
    if (endTime == 0) {
      toast(LocaleStrings.instance.yourEventInfoNotFill(LocaleStrings.instance.timeOfDuration.toLowerCase()));
      return;
    }
    if (hasCover == false) {
      toast(LocaleStrings.instance.yourEventInfoNotFill(LocaleStrings.instance.banner));
      return;
    }

    showLoading();

    if (cover.isEmpty) {
      final url = await _uploadBanner();
      if (url?.isEmpty ?? true) {
        hideLoading();
        toast(LocaleStrings.instance.checkNetWork);
        return;
      }
      cover.value = url!;
    }

    FlatHttpResponse<RoomEventInfo?>? res;
    final info = eventInfo.value;
    final isReject = info?.isReject ?? false;
    if (editEventId.isNotEmpty && info != null) {
      // 修改
      if (isReject) {
        info.topic = _eventName;
        info.cover = cover.value;
        info.description = _eventDesc;
      }
      info.startTime = startTime.value;
      info.endTime = endTime.value;
      res = await eventService.updateEvent(
        event: info,
        cover: isReject ? cover.value : null,
        title: isReject ? _eventName : null,
        description: isReject ? _eventDesc : null,
        startTime: startTime.value,
        endTime: endTime.value,
      );
    } else {
      final RoomEventInfo eventInfo = RoomEventInfo();
      eventInfo.roomId = roomInfo.value?.roomId;
      eventInfo.topic = _eventName;
      eventInfo.cover = cover.value;
      eventInfo.startTime = startTime.value;
      eventInfo.endTime = endTime.value;
      eventInfo.description = _eventDesc;
      res = await eventService.createRoomEvent(eventInfo: eventInfo, step: 4);

      RoomHomeStatistics.reportRoomEventCreateClick(result: (res?.isSuccess ?? false) ? 'succ' : 'fail');
    }

    hideLoading();
    if (res?.isSuccess == true) {
      if (res?.data != null) {
        if (editEventId.isNotEmpty && info != null) {
          /// 更新修改成功sp记录
          _livePref.setInt(accountService.keyWrapUid("$_keySpSave${info.id}"), DateTime.now().millisecondsSinceEpoch);

          routerUtil.pop();
        } else {
          eventInfo.value = res!.data;
          showShare = true.obs;
          if (callback != null) callback(res.data!);
        }
      }
    } else if (res?.code == 1019) {
      showNoEnoughMoneyDialog(from: StatisticPageFrom.createEvent, currencyType: CurrencyType.diamond);
    } else if (res?.code == 1041) {
      showAlertDialog(confirmText: LocaleStrings.instance.roomEventMaximum, showCancel: false);
    } else {
      toast(res?.msg ?? LocaleStrings.instance.tryAgain);
    }
  }
}
