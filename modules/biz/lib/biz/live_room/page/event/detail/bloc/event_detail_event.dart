part of 'event_detail_bloc.dart';

@immutable
abstract class EventDetailEvent {}

class EventInitData extends EventDetailEvent {
  final bool? hasError;

  final RoomEventInfo? eventDetail;

  final UserInfo? createUserInfo;

  EventInitData({this.eventDetail, this.hasError, this.createUserInfo});
}

class EventTryAgain extends EventDetailEvent {}

class EventSubscriberLoadMore extends EventDetailEvent {}

class EventSubscriberData extends EventDetailEvent {
  final List<EventUserInfo>? subscriberList;

  final String? lastId;

  final bool? hasMore;

  EventSubscriberData({this.subscriberList, this.lastId, this.hasMore});
}

class EventCancel extends EventDetailEvent {}