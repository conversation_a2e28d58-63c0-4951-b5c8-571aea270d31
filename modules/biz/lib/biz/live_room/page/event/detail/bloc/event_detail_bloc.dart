import 'package:biz/global/bloc/base_bloc.dart';
import 'package:meta/meta.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/room_home_statistics.g.dart';
import 'package:service/modules/live/event/const/events.dart';
import 'package:service/modules/live/event/model/event_list_model.dart';
import 'package:service/modules/live/event/model/event_model.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';

part 'event_detail_event.dart';

part 'event_detail_state.dart';

class EventDetailBloc extends BaseBloc<EventDetailEvent, EventDetailState> {
  EventDetailBloc() : super(EventDetailState()) {
    on<EventInitData>((event, emitter) => emitter.call(
        state.clone(eventDetail: event.eventDetail, hasError: event.hasError, createUserInfo: event.createUserInfo)));

    on<EventTryAgain>(_tryAgain);

    on<EventSubscriberLoadMore>((_, __) => _subscriberLoadMore());

    on<EventSubscriberData>((event, emitter) =>
        emitter.call(state.clone(hasMore: event.hasMore, lastId: event.lastId, subscriberList: event.subscriberList)));

    on<EventCancel>(_cancel);
  }

  String get detailId => getArgument<String>(P_ID) ?? "";

  String get from => getArgument<String>(P_STATISTIC_FROM) ?? "";

  RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initBloc() {
    super.initBloc();

    _initData();

    listenRxEvent<RoomEventInfo>(RoomEventActivity.update, (value) {
      add(EventInitData(eventDetail: value));
    });

    listenRxEvent<String>(RoomEventActivity.subscribe, (value) async {
      if (value == state.eventDetail?.id) {
        add(EventInitData(
            eventDetail: state.eventDetail
              ?..hasSubscribe = true
              ..subscribeTotal = (state.eventDetail?.subscribeTotal ?? 0) + 1));

        /// 订阅者列表初始化
        _subscriberLoadMore(init: true);
      }
    });

    listenRxEvent<String>(RoomEventActivity.unsubscribe, (value) async {
      if (value == state.eventDetail?.id) {
        add(EventInitData(
            eventDetail: state.eventDetail
              ?..hasSubscribe = false
              ..subscribeTotal = (state.eventDetail?.subscribeTotal ?? 1) - 1));

        /// 订阅者列表初始化
        _subscriberLoadMore(init: true);
      }
    });
  }

  void _initData() async {
    var resp = await eventService.getEventDetail(eventId: detailId);

    if (!resp.isSuccess) {
      add(EventInitData(hasError: true));
      return;
    }

    UserInfo? createUserInfo;
    if (resp.data?.createUserId?.isNotEmpty == true) {
      createUserInfo = await userService.getUserInfo(resp.data?.createUserId ?? "");
    }

    add(EventInitData(hasError: false, eventDetail: resp.data, createUserInfo: createUserInfo));

    /// 订阅者列表初始化
    _subscriberLoadMore(init: true);

    RoomHomeStatistics.reportRoomEventDetailsImp(roomEventId: resp.data?.id, roomId: resp.data?.roomId);
  }

  /// 重试
  void _tryAgain(EventTryAgain event, Emitter emitter) {
    emitter.call(state.clone(hasError: false));
    _initData();
  }

  /// 订阅列表加载更多
  void _subscriberLoadMore({bool init = false}) async {
    if (!init && state.hasMore == false) {
      refreshController.loadNoData();
      refreshController.loadComplete();
      return;
    }

    var resp = await eventService.getSubscriberList(eventId: detailId, startId: init ? "" : state.lastId);

    if (!resp.isSuccess) return;

    List<EventUserInfo> subscriberList = init ? [] : ([]..addAll(state.subscriberList ?? []));
    subscriberList.addAll(resp.data?.list ?? []);
    add(EventSubscriberData(
        subscriberList: subscriberList, lastId: resp.data?.lastId, hasMore: resp.data?.hasMore ?? true));

    if (resp.data?.list?.isNotEmpty != true) {
      refreshController.loadNoData();
    }
    refreshController.loadComplete();
  }

  void _cancel(EventCancel event, Emitter emitter) async {
    showLoading();
    var resp = await eventService.cancelEvent(eventId: state.eventDetail?.id ?? "");
    hideLoading();
    if (resp.isSuccess) {
      routerUtil.pop();
    } else {
      toast(resp.msg ?? LocaleStrings.instance.unknownError);
    }
  }
}
