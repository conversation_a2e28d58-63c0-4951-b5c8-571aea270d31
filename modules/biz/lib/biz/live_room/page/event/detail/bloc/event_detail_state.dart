part of 'event_detail_bloc.dart';

@immutable
class EventDetailState {
  EventDetailState(
      {this.eventDetail,
      this.hasError = false,
      this.createUserInfo,
      this.subscriberList,
      this.lastId,
      this.hasMore = true});

  final RoomEventInfo? eventDetail;

  final UserInfo? createUserInfo;

  final bool? hasError;

  final List<EventUserInfo>? subscriberList;

  final String? lastId;

  final bool? hasMore;

  EventDetailState clone(
      {RoomEventInfo? eventDetail,
      bool? hasError,
      UserInfo? createUserInfo,
      List<EventUserInfo>? subscriberList,
      String? lastId,
      bool? hasMore}) {
    return EventDetailState(
        eventDetail: eventDetail ?? this.eventDetail,
        hasError: hasError ?? this.hasError,
        createUserInfo: createUserInfo ?? this.createUserInfo,
        subscriberList: subscriberList ?? this.subscriberList,
        lastId: lastId ?? this.lastId,
        hasMore: hasMore ?? this.hasMore);
  }
}
