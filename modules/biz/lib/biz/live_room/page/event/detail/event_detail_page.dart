import 'dart:convert';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/live_room/page/event/detail/widgets/interested_widget.dart';
import 'package:biz/biz/live_room/page/event/room_event_share_dialog.dart';
import 'package:biz/biz/live_room/page/event/widget/event_action_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/common/widgets/pop_menu/popup_window.dart';
import 'package:biz/common/widgets/user_avatar_name_gender_widget.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/utils/time_util.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/room_home_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/live/event/model/event_list_model.dart';
import 'package:service/modules/live/event/model/event_model.dart';
import 'package:service/utils/deep_link.dart';

import 'bloc/event_detail_bloc.dart';

@FRoute(desc: '活动详情', url: R_EVENT_DETAIL)
class EventDetailPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _EventDetailPageState();
  }
}

class _EventDetailPageState extends State<EventDetailPage> {
  final _bloc = EventDetailBloc();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _bloc.setArguments(ModalRoute.of(context)?.settings.arguments);
    return LifecycleBlocBuilder<EventDetailBloc, EventDetailState>(
      bloc: _bloc,
      builder: (_, state) => Stack(
        children: [
          Container(color: const Color(0xFFF9FAFB)),
          FLImage.asset(Res.roomBgPage, width: double.infinity, fit: BoxFit.cover),
          _body(context)
        ],
      ),
    );
  }

  /// body
  Widget _body(BuildContext context) {
    if (_bloc.state.hasError ?? false) {
      return _netFailed();
    } else if (_bloc.state.eventDetail == null) {
      return GlobalWidgets.pageLoading();
    }
    return Scaffold(
      appBar: _appBar(),
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          SmartRefresher(
            controller: _bloc.refreshController,
            primary: false,
            onLoading: () => _bloc.add(EventSubscriberLoadMore()),
            enablePullDown: false,
            enablePullUp: _bloc.state.hasMore ?? true,
            // physics: NeverScrollableScrollPhysics(),
            footer: GlobalWidgets.refreshFoot(),
            child: _list(context),
          ),
          Positioned(
            bottom: 0.pt,
            left: 0.pt,
            right: 0.pt,
            child: _bottomBtn(),
          ),
        ],
      ),
    );
  }

  Widget _list(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.only(bottom: 119.pt, left: 10.pt, right: 10.pt),
      itemCount: (_bloc.state.subscriberList ?? []).length + 1,
      itemBuilder: (_, int index) {
        if (index == 0) {
          return _topWidgets(context);
        }
        EventUserInfo userInfo = _bloc.state.subscriberList![index - 1];
        return InterestedWidget(userInfo);
      },
    );
  }

  /// 订阅列表顶部所有widget
  Widget _topWidgets(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _cover(),
        _hostWidget(),
        _userInfoWidget(),
        10.hSpace,
        _divider,
        _roomWidget(),
        _divider,
        _headItemWidget(LocaleStrings.instance.evenDetailSubject, _bloc.state.eventDetail?.topic ?? ''),
        _divider,
        _descWidget(),
        _divider,
        _headItemWidget(
            LocaleStrings.instance.evenDetailTime,
            TimeUtil.getEventTimeStartToEnd(
                showEnd: true,
                startTime: (_bloc.state.eventDetail?.startTime ?? 0) * 1000,
                endTime: (_bloc.state.eventDetail?.endTime ?? 0) * 1000)),
        _divider,
        _giftWidget(context),
        Visibility(
          visible: _bloc.state.subscriberList?.isNotEmpty == true,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.pt),
            child: _headerTitleWidget(
              LocaleStrings.instance.evenDetailSubscribers(_bloc.state.eventDetail?.subscribeTotal ?? 0),
            ),
          ),
        ),
      ],
    );
  }

  Divider get _divider => const Divider(height: 1, color: Color(0xFFF2F2F2));

  Widget _hostWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.pt),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _headerTitleWidget(LocaleStrings.instance.host),
          GeneralIconBtn(
            width: 30.pt,
            height: 18.pt,
            icon: Res.liveEventIconDetailRoom,
            iconSize: Size(15.pt, 12.pt),
            backgroundColor: R.color.primaryColor,
            onTap: () {
              LiveRoomHandler.joinRoom(_bloc.state.eventDetail?.roomId ?? '', from: StatisticPageFrom.roomEventDetail);
            },
          )
        ],
      ),
    );
  }

  Widget _userInfoWidget() {
    return UserAvatarNameGenderWidget(
      uid: _bloc.state.eventDetail?.createUserId ?? '',
      nameMaxWidth: 200.pt,
      from: StatisticPageFrom.roomEventDetail,
    );
  }

  Widget _roomWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        LiveRoomHandler.joinRoom(_bloc.state.eventDetail?.roomId ?? '', from: StatisticPageFrom.roomEventDetail);
      },
      child: Row(
        children: [
          _headItemWidget(LocaleStrings.instance.evenDetailRoomId, _bloc.state.eventDetail?.roomId ?? ''),
          Spacer(),
          Text(
            LocaleStrings.instance.enter,
            style: TextStyle(color: R.color.color20, fontSize: 12.sp, fontWeight: FontWeightExt.medium),
          ),
          7.wSpace,
          FLImage.asset(Res.commonArrowRight, width: 6, fit: BoxFit.cover),
        ],
      ),
    );
  }

  Widget _descWidget() {
    final desc = _bloc.state.eventDetail?.description ?? '';
    if (desc.trim().isEmpty) return const SizedBox.shrink();

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        showAlertDialog(
          title: LocaleStrings.instance.eventDescription,
          content: _bloc.state.eventDetail?.description,
          showCancel: false,
          styleConfirm: TextStyle(color: R.color.primaryColor, fontSize: 16.sp, fontWeight: FontWeightExt.heavy),
        );
      },
      child: Row(
        children: [
          _headItemWidget(LocaleStrings.instance.description, desc),
          Spacer(),
          FLImage.asset(Res.commonArrowRight, width: 6, fit: BoxFit.cover),
        ],
      ),
    );
  }

  Widget _giftWidget(BuildContext context) {
    final list = _bloc.state.eventDetail?.supporterList ?? [];
    if (list.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _headerTitleWidget(LocaleStrings.instance.giftGivingDuringTheEvent),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            FLImage.asset(Res.liveEventIconGiftCrown, height: 18.pt, fit: BoxFit.cover),
            8.wSpace,
            Text(
              '${_bloc.state.eventDetail?.supportTotal ?? 0}',
              style: TextStyle(
                color: const Color(0xFFF537E5),
                fontSize: 12.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
            8.wSpace,
            FLImage.asset(Res.liveEventIconGiftHeart, height: 14.pt, fit: BoxFit.cover),
            10.wSpace,
            _RoomEventGiftTipsWidget(_bloc.state.eventDetail),
            Spacer(),
            Text(
              '${LocaleStrings.instance.supporter}:${_bloc.state.eventDetail?.supportPeopleTotal ?? 0}',
              style: TextStyle(
                color: const Color(0xFFF77A27),
                fontSize: 11.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 150.pt,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                left: 14.pt,
                top: 18.pt,
                child: _giftRankingWidget(2),
              ),
              Positioned(
                top: 0,
                child: _giftRankingWidget(1),
              ),
              Positioned(
                right: 17.pt,
                top: 31.pt,
                child: _giftRankingWidget(3),
              ),
            ],
          ),
        ),
        10.hSpace,
        _divider,
      ],
    );
  }

  Widget _giftRankingWidget(int rank) {
    final list = _bloc.state.eventDetail?.supporterList ?? [];
    final user = rank == 1
        ? list.firstOrNull
        : rank == 2
            ? list.getSafeElement(1)
            : list.getSafeElement(2);

    final height = rank == 1
        ? 97.pt
        : rank == 2
            ? 88.pt
            : 81.pt;
    final avatarHeight = rank == 1
        ? 65.pt
        : rank == 2
            ? 60.pt
            : 55.pt;
    final bg = rank == 1
        ? Res.liveEventBgGiftTop1
        : rank == 2
            ? Res.liveEventBgGiftTop2
            : Res.liveEventBgGiftTop3;
    return GestureDetector(
      onTap: () {
        routerUtil.push(R_USER_INFO, params: {P_UID: user?.uid, P_STATISTIC_FROM: StatisticPageFrom.roomEventDetail});
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              UserAvatar(url: user?.avatar ?? '', size: avatarHeight),
              FLImage.asset(bg, height: height, fit: BoxFit.cover),
            ],
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 96.pt),
            child: Text(
              user?.nickName ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: R.color.color20,
                fontSize: 14.sp,
                fontWeight: FontWeightExt.heavy,
              ),
            ),
          ),
          5.hSpace,
          Visibility(
            visible: user != null,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                FLImage.asset(Res.liveEventIconGiftHeart, height: 14.pt, fit: BoxFit.cover),
                8.wSpace,
                Text(
                  NumFormatUtils.numFormat(user?.contribution ?? 0),
                  style: TextStyle(
                    color: const Color(0xFFF537E5),
                    fontSize: 12.sp,
                    fontWeight: FontWeightExt.medium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _headItemWidget(String title, String content) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.pt),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _headerTitleWidget(title),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 250.pt),
            child: Text(
              content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 12.sp, color: R.color.colorB2, fontWeight: FontWeightExt.medium),
            ),
          ),
          10.hSpace,
        ],
      ),
    );
  }

  Widget _headerTitleWidget(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.pt),
      child: Text(
        title,
        style: TextStyle(fontSize: 14.sp, color: R.color.color20, fontWeight: FontWeightExt.heavy),
      ),
    );
  }

  /// 标题栏
  CommonAppBar _appBar() {
    return CommonAppBar(
      title: LocaleStrings.instance.details,
      backgroundColor: Colors.transparent,
      actions: [
        IconBtn(
          icon: Res.commonShare,
          iconColor: Colors.black,
          iconSize: Size(13.pt, 13.pt),
          widgetSize: Size(26.pt, 26.pt),
          bgDecoration: BoxDecoration(color: Colors.white.withOpacity(0.4), borderRadius: BorderRadius.circular(13.pt)),
          onTap: () {
            if (_bloc.state.eventDetail != null) {
              _showRoomEventShareDialog(_bloc.state.eventDetail!);
            }
            RoomHomeStatistics.reportRoomEventDetailsShareClick(
              roomEventId: _bloc.state.eventDetail?.id,
              roomId: _bloc.state.eventDetail?.roomId,
            );
          },
        ),
      ],
    );
  }

  /// 封面图
  Widget _cover() {
    final cover = _bloc.state.eventDetail?.cover ?? "";
    final inReview = _bloc.state.eventDetail?.isAudit ?? false;
    final isFamily = _bloc.state.eventDetail?.isFamily ?? false;
    final showCancel = (_bloc.state.eventDetail?.isMyEvent ?? false) && (_bloc.state.eventDetail?.isUnStart ?? false);
    return Container(
      height: (1.w - 20.pt) * 0.5,
      width: 1.w - 20.pt,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.pt)),
      child: Stack(
        children: [
          CachedNetworkImage(
            imageUrl: cover,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
          Visibility(
            visible: inReview,
            child: Container(
              height: 200.pt,
              width: double.infinity,
              color: Colors.black.withOpacity(0.7),
              alignment: Alignment.center,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 19.pt, vertical: 9.pt),
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.white.withOpacity(0.26), width: 1.pt),
                    borderRadius: BorderRadius.all(Radius.circular(18.pt))),
                child: Text(LocaleStrings.instance.eventInReview,
                    style: TextStyle(color: Colors.white, fontSize: 14.sp, fontWeight: fontWeightRegular)),
              ),
            ),
          ),
          Visibility(
            visible: isFamily,
            child: Positioned(
              top: 10.pt,
              right: 0.pt,
              child: Container(
                alignment: Alignment.center,
                padding: EdgeInsets.only(left: 13.pt, right: 8.pt),
                height: 19.pt,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Res.liveEventBgFamily),
                    fit: BoxFit.fill,
                  ),
                ),
                constraints: BoxConstraints(minWidth: 52.pt),
                child: Text(
                  LocaleStrings.instance.family.firstCharToUpperCase,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: const Color(0xFFC3C3C3), fontSize: 11.sp, fontWeight: FontWeightExt.medium),
                ),
              ),
            ),
          ),
          Visibility(
            visible: showCancel,
            child: Positioned(
              bottom: 8.pt,
              right: 8.pt,
              child: GestureDetector(
                onTap: () {
                  showAlertDialog(
                      title: LocaleStrings.instance.cancelRoomEvent,
                      content: LocaleStrings.instance.cancelRoomEventTips,
                      styleCancel: TextStyle(color: R.color.colorB2, fontSize: 16.sp, fontWeight: FontWeightExt.heavy),
                      styleConfirm:
                          TextStyle(color: R.color.primaryColor, fontSize: 16.sp, fontWeight: FontWeightExt.heavy),
                      onConfirm: () {
                        _bloc.add(EventCancel());
                      });
                },
                child: Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 13.pt),
                  height: 22.pt,
                  decoration:
                      BoxDecoration(color: Colors.white.withOpacity(0.84), borderRadius: BorderRadius.circular(11.pt)),
                  child: Text(
                    LocaleStrings.instance.cancelEvent,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: R.color.color20, fontSize: 13.sp, fontWeight: FontWeightExt.medium),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 底部按钮
  Widget _bottomBtn() {
    if (_bloc.state.eventDetail == null) return const SizedBox.shrink();

    if (_bloc.state.eventDetail?.isAudit ?? false) return const SizedBox.shrink();

    final isMyEvent = _bloc.state.eventDetail?.isMyEvent;
    return Container(
      height: 119,
      alignment: Alignment.center,
      child: Stack(
        children: [
          IgnorePointer(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.white.withAlpha(0), Colors.white.withAlpha(255)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),
          Center(
            child: SizedBox(
              width: 1.w - 36.pt,
              height: 47.pt,
              child: EventActionWidget(
                minWidth: 1.w - 36.pt,
                height: 47.pt,
                eventInfo: _bloc.state.eventDetail!,
                from: _bloc.from,
                safeArea: false,
                isMe: isMyEvent,
                fontSize: 19.sp,
                gradient: LinearGradient(
                  colors: [const Color(0xFFB781FD), const Color(0xFF693AF4)],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 网络异常
  Widget _netFailed() {
    return EmptyWidget(
      // logoAsset: Res.iconHEmptyNetH,
      title: LocaleStrings.instance.hintNetError,
      detail: LocaleStrings.instance.checkNetWork,
      btnSting: LocaleStrings.instance.tryAgain,
      btnAction: () => _bloc.add(EventTryAgain()),
    );
  }

  /// 活动分享弹窗
  void _showRoomEventShareDialog(RoomEventInfo eventInfo) async {
    if ((eventInfo.id?.isEmpty ?? true) || (eventInfo.createUserId?.isEmpty ?? true)) return;

    var userInfo = await userService.getUserInfo(eventInfo.createUserId!);

    /// 拼接deeplink
    var deepLink = "${DeepLink.deepLinkProto}${R_EVENT_DETAIL.replaceFirst("/", "")}"
        "${"?$P_ID=${eventInfo.id}"}${"&$P_STATISTIC_FROM=share"}";
    String content = LocaleStrings.instance.inviteParticipateEvent(userInfo?.nickname ?? '', eventInfo.topic ?? "");

    if (!mounted) return;

    showRoomEventShareDialog(
      context: context,
      eventInfo: eventInfo,
      shareTemplate: LiveEventShare(
          imContent: json.encode(eventInfo.toJson()), content: content, eventInfo: eventInfo, deepLink: deepLink),
    );
  }
}

class _RoomEventGiftTipsWidget extends StatelessWidget {
  final RoomEventInfo? eventInfo;

  const _RoomEventGiftTipsWidget(this.eventInfo);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        PopupWindow.show(
          context,
          _RoomEventGiftRankPopWidget(eventInfo),
          offset: Offset(90, 0),
          elevation: 10,
          barrierColor: Colors.transparent,
          alignment: PopupWindowAlign.topCenter,
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.pt),
        child: FLImage.asset(Res.liveEventIconGiftTips, height: 18.pt, fit: BoxFit.cover),
      ),
    );
  }
}

class _RoomEventGiftRankPopWidget extends StatelessWidget {
  final RoomEventInfo? eventInfo;

  const _RoomEventGiftRankPopWidget(this.eventInfo);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 170.pt,
      padding: EdgeInsets.symmetric(horizontal: 12.pt, vertical: 15.pt),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.pt)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleStrings.instance.eventGiftRankTips,
            style: TextStyle(
              color: R.color.color20,
              fontSize: 11.sp,
              fontWeight: FontWeightExt.medium,
            ),
          ),
          15.hSpace,
          _giftWidget(false),
          6.hSpace,
          _giftWidget(true),
          18.hSpace,
          Text(
            LocaleStrings.instance.giftSendingDetails,
            style: TextStyle(
              color: R.color.color20,
              fontSize: 11.sp,
              fontWeight: FontWeightExt.medium,
            ),
          ),
          11.hSpace,
          Row(
            children: [
              RichText(
                text: TextSpan(
                  style: TextStyle(fontWeight: FontWeightExt.medium, fontSize: 11.sp),
                  children: [
                    TextSpan(
                        text: '${LocaleStrings.instance.diamondGifts}: ', style: TextStyle(color: R.color.color90)),
                    TextSpan(text: '${eventInfo?.totalDiamond ?? 0}', style: TextStyle(color: const Color(0xFF9317FF))),
                  ],
                ),
              ),
              5.wSpace,
              FLImage.asset(Res.financeDiamond, height: 14.pt, fit: BoxFit.cover),
            ],
          ),
          5.hSpace,
          Row(
            children: [
              RichText(
                text: TextSpan(
                  style: TextStyle(fontWeight: FontWeightExt.medium, fontSize: 11.sp),
                  children: [
                    TextSpan(
                        text: '${LocaleStrings.instance.diamondGifts}: ', style: TextStyle(color: R.color.color90)),
                    TextSpan(text: '${eventInfo?.totalCoin ?? 0}', style: TextStyle(color: const Color(0xFFFFA800))),
                  ],
                ),
              ),
              5.wSpace,
              FLImage.asset(Res.financeIconExchangeCoin, height: 14.pt, fit: BoxFit.cover),
            ],
          ),
        ],
      ),
    );
  }

  Widget _giftWidget(bool isCoin) {
    final txtColor = isCoin ? const Color(0xFFFFA800) : const Color(0xFF9317FF);
    final icon = isCoin ? Res.financeGoldCoin : Res.financeDiamond;
    return Row(
      children: [
        Text(
          '${LocaleStrings.instance.gift.toLowerCase()}:',
          style: TextStyle(
            color: R.color.color90,
            fontSize: 11.sp,
            fontWeight: FontWeightExt.medium,
          ),
        ),
        ConstrainedBox(
          constraints: BoxConstraints(minWidth: 20.pt),
          child: Text(
            '${isCoin ? 20 : 1}',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: txtColor,
              fontSize: 11.sp,
              fontWeight: FontWeightExt.medium,
            ),
          ),
        ),
        FLImage.asset(icon, height: 14.pt, fit: BoxFit.cover),
        5.wSpace,
        Text(
          '=',
          style: TextStyle(
            color: R.color.color90,
            fontSize: 11.sp,
            fontWeight: FontWeightExt.medium,
          ),
        ),
        5.wSpace,
        Text(
          '1',
          style: TextStyle(
            color: const Color(0xFFF537E5),
            fontSize: 11.sp,
            fontWeight: FontWeightExt.medium,
          ),
        ),
        5.wSpace,
        FLImage.asset(Res.liveEventIconGiftHeart, height: 14.pt, fit: BoxFit.cover),
      ],
    );
  }
}
