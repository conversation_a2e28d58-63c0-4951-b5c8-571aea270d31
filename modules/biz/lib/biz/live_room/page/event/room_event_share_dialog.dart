import 'package:biz/biz/live_room/page/event/widget/room_event_share_widget.dart';
import 'package:flutter/material.dart' hide showDialog;

import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/live/event/model/event_model.dart';

import 'package:biz/global/widgets/boundary_to_image_share_widget.dart';

void showRoomEventShareDialog({
  required BuildContext context,
  required RoomEventInfo eventInfo,
  ShareTemplate? shareTemplate,
}) async {
  showBoundaryToImageWidgetShareDialog(
    context: context,
    boundaryToImageWidget: RoomEventShareWidget(eventInfo: eventInfo),
    shareTemplate: shareTemplate,
    showFacebook: false,
    showInstagram: true,
    moveFriendBeforeMoment: true,
    from: "room_event_details",
  );
}
