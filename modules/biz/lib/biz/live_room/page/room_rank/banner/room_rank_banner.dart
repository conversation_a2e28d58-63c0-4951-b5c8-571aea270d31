import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/global/widget/text_scroll.dart';
import 'package:service/service.dart';

import '../../../component/top_bar/room_join_fans_widget.dart';
import '../../room/room_main_controller.dart';
import 'room_rank_banner_controller.dart';

/// 房间排行榜入口
class RoomRankBanner extends StatefulWidget {
  final RoomMainController roomController;

  const RoomRankBanner({
    super.key,
    required this.roomController,
  });

  @override
  State<RoomRankBanner> createState() => _RoomRankBannerState();
}

class _RoomRankBannerState extends State<RoomRankBanner> {
  late RoomRankBannerController _controller;

  String get _getTag => '_$hashCode';

  @override
  void initState() {
    super.initState();
    _controller = Get.put<RoomRankBannerController>(RoomRankBannerController(), tag: _getTag);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _controller.onTapBanner,
      child: GetBindingBuilder(
        controller: _controller,
        child: GetBuilder<RoomRankBannerController>(
          global: false,
          init: _controller,
          assignId: true,
          tag: _getTag,
          builder: (logic) {
            bool showTopChange = _controller.showChangeBanner;
            String showText = _controller.showText;
            Widget child;
            if (showTopChange) {
              child = _RankChangeTopWidget(
                text: showText,
                key: ValueKey(showText),
              );
            } else {
              child = _buildRankBanner();
            }
            return Row(
              children: [
                Container(
                  height: 27.pt, // 固定高度，防止child height change抖动
                  child: AnimatedSwitcher(
                      duration: Duration(milliseconds: 500),
                      child: child,
                      layoutBuilder: (Widget? currentChild, List<Widget> previousChildren) {
                        return Stack(
                          alignment: Alignment.centerLeft,
                          children: <Widget>[
                            ...previousChildren,
                            if (currentChild != null) currentChild,
                          ],
                        );
                      }),
                ),
                4.wSpace,
                if (roomService.getCurrentRoom()?.isFamilyRoom == false)
                  _buildJoinFansIcon()
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildJoinFansIcon() {
    return Obx(() {
      int level = widget.roomController.fansController.fansLevel.value;
      bool showRedDot = widget.roomController.fansController.showTopFansRedDot();
      return GestureDetector(
        onTap: widget.roomController.fansController.onTapJoinFans,
        child: Container(
          width: 27.pt,
          height: 27.pt,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.14),
            borderRadius: BorderRadius.circular(50.pt),
          ),
          child: JoinFansWidget(
            showAdd: !roomService.isRoomOwner() && level <= 0,
            level: level > 0 ? level : null,
            showRedDot: showRedDot,
          ),
        ),
      );
    });
  }

  Widget _buildRankBanner() {
    return Container(
      padding: EdgeInsets.all(5.pt),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.18),
        borderRadius: BorderRadius.circular(13.pt),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FLImage.asset(Res.roomRankIcRoomRank, width: 17.pt, height: 17.pt),
          3.wSpace,
          Text(
            LocaleStrings.instance.contribution,
            style: TextStyle(
              color: const Color(0xFFFF8C12),
              fontSize: 10.pt,
              fontWeight: FontWeightExt.heavy,
              height: 1.2,
            ),
          ),
          Icon(
            Icons.keyboard_arrow_right_outlined,
            color: const Color(0xFFFF8C12),
            size: 16.pt,
          ),
        ],
      ),
    );
  }
}

class _RankChangeTopWidget extends StatelessWidget {
  final String text;

  const _RankChangeTopWidget({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxWidth: 208.pt),
      padding: EdgeInsetsDirectional.only(top: 3.pt, bottom: 3.pt, start: 4.pt, end: 4.pt),
      decoration: BoxDecoration(
        gradient: R.color.btnColorfulGradient,
        borderRadius: BorderRadius.circular(12.pt),
        boxShadow: [
          BoxShadow(
            color: Color(0x4D9046F1),
            blurRadius: 18,
            spreadRadius: 0,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: TextScroll(
        '\u{00A0} $text \u{00A0}', // 两边留白防止阴影
        mode: TextScrollMode.endless,
        velocity: Velocity(pixelsPerSecond: Offset(20, 0)),
        delayBefore: Duration(milliseconds: 1000),
        pauseBetween: Duration(milliseconds: 1000),
        style: TextStyle(
          color: roomTheme.theme.textColor,
          fontSize: 11.pt,
          fontWeight: FontWeightExt.heavy,
          height: 1.4,
        ),
        fadedBorder: true,
        fadeBorderSide: FadeBorderSide.right,
        intervalSpaces: 3,
      ),
    );
  }
}
