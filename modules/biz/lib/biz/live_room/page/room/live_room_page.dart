import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift/gift_barrage_display_widget.dart';
import 'package:biz/biz/live_room/component/gift/room_big_gift_display_widget.dart';
import 'package:biz/biz/live_room/component/host_incentive/host_play_time_banner.dart';
import 'package:biz/biz/live_room/component/mic_seat/room_mic_seat_widget.dart';
import 'package:biz/biz/live_room/component/music/widgets/music_player_widget.dart';
import 'package:biz/biz/live_room/component/room_bg_widget.dart';
import 'package:biz/biz/live_room/component/switch_room_banner/switch_room_banner_widget.dart';
import 'package:biz/biz/live_room/component/top_bar/room_top_bar_widget.dart';
import 'package:biz/biz/live_room/plugins/ad/room_right_bottom_banner.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/message/room_message_entrance_widget.dart';
import 'package:biz/common/widgets/draggable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/cocos_game/model/empire_config_model.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';

import '../../../game/gameview/new_game/new_game_widget.dart';
import '../../component/bottom_bar/room_bottom_bar_widget.dart';
import '../../component/chat/room_welcome_msg_widget.dart';
import '../../component/chat_view/room_chat_view_widget.dart';
import '../../component/gift/float_screen/gift_float_screen_widget.dart';
import '../../component/mic_seat/widget/mic_apply_notice_widget.dart';
import '../../component/top_bar/room_top_owner_widget.dart';
import '../../widgets/web_game_carousel/web_game_carousel_widget.dart';
import '../room_rank/banner/room_rank_banner.dart';
import 'room_main_controller.dart';

@FRoute(desc: "房间页面", url: R_LIVE_LIVE_ROME)
class LiveRoomPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _LiveRoomPageState();
  }
}

class _LiveRoomPageState extends State<LiveRoomPage> {
  late String _getXTag;

  late RoomMainController roomController;

  @override
  void initState() {
    super.initState();
    _getXTag = 'room_${roomService.getCurrentRoomId()}';
    roomController = Get.put<RoomMainController>(RoomMainController(), tag: _getXTag)
      ..getXTag = _getXTag
      ..initModuleController()
      ..setUpData(model: roomService.getCurrentRoom())
      ..onInitFollowStatus();
  }

  @override
  void dispose() {
    super.dispose();
    Get.delete<RoomMainController>(tag: _getXTag);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {},
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: GetBindingBuilder(
          controller: roomController,
          child: AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle.light,
            child: Stack(
              children: [
                RoomBgWidget(),
                _buildBody(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        /// 游戏容器
        Align(
          alignment: Alignment.center,
          child: Obx(_gameView),
        ),

        /// 房间页
        Positioned.fill(child: _page()),
        ..._registerGetController(),

        /// 播放器
        _musicPlayerWidget(),

        /// H5游戏入口
        _webGameEntrance(),

        /// 右下角广告栏
        _rightBottomAdBanner(),

        /// 礼物流光
        _giftStreamer(),
        _chatEntranceWidget(),

        /// 礼物飘屏
        _giftFloatScreen(),

        /// 礼物播放
        _giftDisplay(),

        /// 欢迎消息轮播
        _welcomeCarousel(),
        _switchRoomBanner(),
      ],
    );
  }

  /// 游戏金币区域
  Widget _gameView() {
    final currentRoom = roomService.getCurrentRoom();
    final mode = roomController.roomMode.value;
    final gameKey = "${mode}_${currentRoom?.roomId}";
    switch (mode) {
      case RoomMode.LUDO_GAME:
        return NewGameWidget(
          roomService.getCurrentRoomId() ?? '',
          GameType.game_ludo,
          key: ValueKey(gameKey),
          isQuickMode: roomService.getCurrentRoom()?.isQuickMode ?? false,
          // playerNum: currentRoom?.gameConfig?.playerNum ?? 4,
        );
      case RoomMode.DOMINO_GAME:
        return NewGameWidget(
          roomService.getCurrentRoomId() ?? '',
          GameType.game_domino,
          key: ValueKey(gameKey),
        );
      case RoomMode.CANDY_GAME:
        return NewGameWidget(
          roomService.getCurrentRoomId() ?? '',
          GameType.game_candy,
          key: ValueKey(gameKey),
          playerNum: 2,
        );
      case RoomMode.UNO_GAME:
        return NewGameWidget(
          roomService.getCurrentRoomId() ?? '',
          GameType.game_uno,
          key: ValueKey(gameKey),
        );
    }

    return SizedBox();
  }

  Widget _page() {
    return Stack(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: statusBarHeight),
            RoomTopBarWidget(getXTag: _getXTag),
            _buildBannerSpace(),
            RoomMicSeatWidget(getXTag: _getXTag),
            _buildDanmuArea(),
            MicApplyNoticeWidget(mainController: roomController),
            RoomBottomBarWidget(mainController: roomController),
          ],
        ),
        Obx(() {
          final show = roomController.showFollowGuide.value;
          if (!show) return const SizedBox.shrink();
          return StreamBuilder<Offset>(
            stream: rxUtil.observer<Offset>(RoomOperationEvent.roomFollowAvailable),
            builder: (context, snapshot) {
              return _buildFollowGuide(snapshot.data);
            },
          );
        }),
      ],
    );
  }

  Widget _buildFollowGuide(Offset? offset) {
    if (offset == null) return SizedBox.shrink();

    return Positioned(
      top: offset.dy + 32.pt, // 按钮底部 + 间距
      left: offset.dx - (languageService.isId ? 15.pt : 30.pt), // 气泡偏移
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 尖角
          Container(
            margin: EdgeInsets.only(right: 50.pt),
            child: Transform.translate(
              offset: Offset(10.pt, 0),
              child: CustomPaint(
                size: Size(10.pt, 6.pt),
                painter: TrianglePainter(),
              ),
            ),
          ),
          // 气泡主体
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.pt, vertical: 4.pt),
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [
                Color(0xFF7D0AFE),
                Color(0xFFB91EFD),
              ]),
              borderRadius: BorderRadius.circular(12.pt),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF7D0AFE).withOpacity(0.3),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              LocaleStrings.instance.followTheRoom,
              style: TextStyle(
                color: Colors.white,
                fontSize: 10.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerSpace() {
    return SizedBox(
      height: 25.pt,
      width: double.infinity,
      child: Stack(
        alignment: Alignment.centerLeft,
        clipBehavior: Clip.none,
        children: [
          PositionedDirectional(
            end: 0,
            child: _buildHostPlayTimeBanner(),
          ),
          _buildRankBanner(),
        ],
      ),
    );
  }

  Widget _buildRankBanner() {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 13.pt),
      child: RoomRankBanner(roomController: roomController),
    );
  }

  Widget _buildHostPlayTimeBanner() {
    return Padding(
      padding: EdgeInsetsDirectional.only(end: 8.pt),
      child: Row(
        children: [
          _familyLuckBag(),
          2.wSpace,
          _cocosGameEnter(),
          HostPlayTimeBanner(roomController: roomController),
        ],
      ),
    );
  }

  Widget _familyLuckBag() {
    return Obx(() {
      final luckyBagId = roomController.familyLuckyBagController.smallLuckyBagId.value;
      if (luckyBagId == null || luckyBagId == 0) {
        return SizedBox.shrink();
      }

      // 先获取计时器对象
      final timer = roomController.familyLuckyBagController.getTimer(luckyBagId);
      final leftTime = timer?.leftTime.value ?? "";
      final leftSec = timer?.leftSec ?? 0;
      final count = roomController.familyLuckyBagController.luckyBagCount.value;

      if (timer != null) {
        return Padding(
          padding: EdgeInsetsDirectional.only(end: 8.pt),
          child: GestureDetector(
            onTap: () {
              roomController.familyLuckyBagController.onTapSmallFamilyLuckyBag();
            },
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                Column(
                  children: [
                    FLImage.asset(Res.familyIcLuckyBagBody, height: 23.pt),
                    if(leftSec > 0)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 2.pt),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(50.pt)
                        ),
                        child: Text(
                          leftTime,
                          style: TextStyle(
                            color: Color(0xFFFF0303),
                            fontSize: 8.pt,
                            fontWeight: FontWeightExt.medium,
                          ),
                        ),
                      )

                  ],
                ),
                if (count > 0)
                  PositionedDirectional(
                    top: -4.pt,
                    end: -4.pt,
                    child: Container(
                      height: 11.pt,
                      decoration: BoxDecoration(
                        color: R.color.warningColor,
                        border: Border.all(
                          color: Colors.white,
                          width: 1.pt,
                        ),
                        borderRadius: BorderRadius.circular(50.pt)
                      ),
                      constraints: BoxConstraints(
                        minWidth: 11.pt
                      ),
                      padding: EdgeInsets.symmetric(horizontal:2.pt),
                      child: Center(
                        child: Text(
                          count > 9 ? '9+' : '$count',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8.pt,
                            fontWeight: FontWeightExt.heavy,
                            height: 1.2,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      } else {
        return SizedBox.shrink();
      }
    },
    );
  }

  Widget _cocosGameEnter() {
    return Obx(() {
      H5PageInfo? info = roomService.cocosGameService.cocosH5Info.value;
      if (info == null || info.show != true) {
        return SizedBox.shrink();
      }
      return Padding(
        padding: EdgeInsetsDirectional.only(end: 8.pt),
        child: GestureDetector(
          onTap: () {
            var url = cocosGameHelp;
            if (info.param?.isNotEmpty == true) {
              url = "$url?${info.param}";
            }
            routerUtil.push(R_WEB, params: {P_URL: url});
          },
          child: FLImage.asset(Res.cocosGameGamePk, width: 26.pt, height: 26.pt),
        ),
      );
    });
  }

  /// 礼物流光
  Widget _giftStreamer() {
    return IgnorePointer(
      child: GiftBarrageDisplayWidget(
        margin: EdgeInsets.only(
          top: roomService.getCurrentRoom()?.startCalculator == true ? 350.pt : 315.pt,
        ),
      ),
    );
  }

  /// 礼物动画
  Widget _giftDisplay() {
    return Obx(
        () => IgnorePointer(child: RoomBigGiftDisplayWidget(showUserCar: !roomController.showSwitchRoomBanner.value)));
  }

  Widget _welcomeCarousel() {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: EdgeInsets.only(top: 150.pt, left: 7.5.pt),
        child: RoomWelcomeMsgWidget(),
      ),
    );
  }

  /// 顶部全房飘屏动画
  Widget _giftFloatScreen() {
    return PositionedDirectional(
      top: statusBarHeight + kRoomTopBarHeight,
      child: GiftFloatScreenWidget(type: GiftFloatType.room),
    );
  }

  Widget _switchRoomBanner() {
    return Obx(() => roomController.showSwitchRoomBanner.value
        ? Positioned(
            bottom: 132.pt,
            left: 0,
            child: SwitchRoomBannerWidget(
              onAnimationAutoComplete: () {
                roomController.showSwitchRoomBanner.value = false;
              },
              onTap: () {
                roomController.showSwitchRoomBanner.value = false;
                roomController.switchMatchRoom();
              },
            ))
        : SizedBox.shrink());
  }

  /// 控制器生命周期容器注册
  List<Widget> _registerGetController() {
    return [
      GetBindingBuilder(child: SizedBox.shrink(), controller: roomController.fansController),
      GetBindingBuilder(child: SizedBox.shrink(), controller: roomController.redPackController),
      GetBindingBuilder(child: SizedBox.shrink(), controller: roomController.familyLuckyBagController),
      GetBindingBuilder(child: SizedBox.shrink(), controller: roomController.charmWealthController),
    ];
  }

  Widget _webGameEntrance() {
    double safeBottom = MediaQuery.of(context).padding.bottom;
    return Positioned(
      right: 0,
      bottom: safeBottom + roomTheme.theme.roomBottomBarHeight + 75.pt,
      child: WebGameCarouselWidget(),
    );
  }

  Widget _rightBottomAdBanner() {
    double safeBottom = MediaQuery.of(context).padding.bottom;
    return Positioned(
      right: roomTheme.theme.roomAdBannerMargin,
      bottom: safeBottom + roomTheme.theme.roomBottomBarHeight + 5.pt,
      child: RoomRightBottomBanner(controller: roomController.adController),
    );
  }

  /// 弹幕区域
  Widget _buildDanmuArea() {
    return Obx(() {
      var isGameMode = RoomInfoModel.checkGameMode(roomController.roomMode.value);
      Widget danmuWidget = RoomChatViewWidget(mainController: roomController);
      if (!isGameMode) {
        return Expanded(
          child: danmuWidget,
        );
      } else {
        return Expanded(
            child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: LayoutBuilder(builder: (context, constraint) {
                /// 获取此控件的距离屏幕顶部以及底部的距离
                WidgetUtils.post((_) {
                  RenderObject? renderBox = context.findRenderObject();
                  if (renderBox is RenderBox) {
                    // 获取顶部偏移
                    var offset = renderBox.localToGlobal(Offset.zero);
                    var offsetY = offset.dy;
                    // 获取控件的高度
                    var widgetHeight = constraint.maxHeight;

                    roomController.setGameViewRect(offsetY, widgetHeight);
                  }
                });

                return SizedBox.shrink();
              }),
            ),
            Container(
              height: 88.pt,
              child: danmuWidget,
            )
          ],
        ));
      }
    });
  }

  Widget _chatEntranceWidget() {
    final width = 55.pt;
    final height = 55.pt;
    final dx = 1.w - 10.pt - width;
    final dy = 1.h - 190.pt - kBottomNavigationBarHeight;
    return DraggableWidget(
      offsetX: dx,
      offsetY: dy,
      width: width,
      height: height,
      child: RoomMessageEntranceWidget(),
    );
  }

  Widget _musicPlayerWidget() {
    return Positioned(
      right: -1,
      top: 1.h - 230.pt - kBottomNavigationBarHeight,
      child: MusicPlayerWidget(roomController),
    );
  }
}
