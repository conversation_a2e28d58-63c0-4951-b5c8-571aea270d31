import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/disturb_alert_dialog.dart';
import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge/recharge_dialog.dart';
import 'package:biz/biz/home/<USER>/home_handler.dart';
import 'package:biz/biz/live_room/component/bottom_bar/dialog/room_more_dialog_controller.dart';
import 'package:biz/biz/live_room/component/bottom_bar/room_bottom_controller.dart';
import 'package:biz/biz/live_room/component/chat_view/room_chat_view_controller.dart';
import 'package:biz/biz/live_room/component/dialog/invite_take_mic_dialog.dart';
import 'package:biz/biz/live_room/component/dialog/reward_probability_gift_result_dialog.dart';
import 'package:biz/biz/live_room/component/dialog/room_send_gift_guide_dialog.dart';
import 'package:biz/biz/live_room/component/fans/room_fans_controller.dart';
import 'package:biz/biz/live_room/component/gift_gallery/gift_gallery_detail_dialog.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_widget.dart';
import 'package:biz/biz/live_room/component/mic_seat/room_mic_seat_controller.dart';
import 'package:biz/biz/live_room/component/music/background_music/background_music_controller.dart';
import 'package:biz/biz/live_room/component/profile/room_profile_controller.dart';
import 'package:biz/biz/live_room/component/settings/room_setting_controller.dart';
import 'package:biz/biz/live_room/component/share/room_share_dialog.dart';
import 'package:biz/biz/live_room/component/sign_in/sign_in_reward_widget.dart';
import 'package:biz/biz/live_room/component/user_charm_wealth/user_charm_wealth_controller.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/live_room/page/room_rank/room_rank_helper.dart';
import 'package:biz/biz/live_room/plugins/ad/room_ad_controller.dart';
import 'package:biz/biz/live_room/plugins/red_pack/red_pack_controller.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/turntable_game.dart';
import 'package:biz/biz/live_room/widgets/room_close_countdown_widget.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/global/widgets/crop_image/crop_image_widget.dart';
import 'package:biz/utils/res_picker/photo_video_picker.dart';
import 'package:flutter/material.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/room_statistic_handler.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/cocos_game/cocos_game_cache.dart';
import 'package:service/modules/cocos_game/model/game_empire_notice_ext.dart';
import 'package:service/modules/common/model/file_model.dart';
import 'package:service/modules/common/upload/upload_file.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/gift/model/gift_gallery_list_model.dart';
import 'package:service/modules/home/<USER>/home_wink_source_handler.dart';
import 'package:service/modules/live/mic_seat/abs_mic_seat_service.dart';
import 'package:service/modules/live/mic_seat/const/enums.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';
import 'package:service/modules/live/room/model/room_theme_model.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_game.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/loading.dart';
import 'package:wakelock/wakelock.dart';

import '../../plugins/family_lucky_bag/family_lucky_bag_controller.dart';

mixin AbsRoomModuleController {
  RoomMainController get mainController;
}

class RoomMainController extends AbsGetController {
  late String getXTag;

  /// 房间名称
  RxnString roomName = RxnString();

  /// 房间公告
  RxnString roomAnnounce = RxnString();

  /// 显示关注房主
  RxnBool showOwnerFollow = RxnBool(true);

  RxBool showFollowGuide = false.obs;

  RxBool isFollowed = false.obs;

  /// 房间是否上锁
  RxnBool roomHasPsw = RxnBool(false);
  RxnString password = RxnString();

  RxnString roomOwnerAvatar = RxnString();
  RxnString roomOwnerAvatarCode = RxnString();

  RxnString roomID = RxnString();

  RxnString roomShowID = RxnString();

  /// 房间模式
  RxnString roomMode = RxnString();

  /// sudGame viewRect
  Rxn<Rect> roomSudGameRect = Rxn();

  Rxn<RoomThemeModel> themeModel = Rxn();

  /// 房间在房人数
  RxnInt onLineNum = RxnInt();

  /// 用户房间的角色
  RxnInt userType = RxnInt();

  ///是否显示切房banner
  final showSwitchRoomBanner = false.obs;

  //是否开启礼物激励
  final enableGiftIncentives = false.obs;

  /// 房间上麦权限类型：'everyone' 自由上麦，'invited_only' 申请上麦
  RxnString micPermission = RxnString();

  /// 房间上封面
  RxnString cover = RxnString();

  /// 房间等级
  RxnInt roomLevel = RxnInt();

  //是否是自由麦
  bool get isFreeMic => micPermission == 'everyone';

  Rxn<MicStatus> micStatus = Rxn<MicStatus>(MicStatus.normal);

  RxnInt micApplyCount = RxnInt();

  Rxn<PbRoomUser> onLineRankTopUser = Rxn();

  RxList<String> h5GameList = RxList.empty();

  RoomSettingController get settingsController => Get.find<RoomSettingController>(tag: getXTag);

  RoomMoreDialogController get moreController => Get.find<RoomMoreDialogController>(tag: getXTag);

  RoomProfileController get profileController => Get.find<RoomProfileController>(tag: getXTag);

  RoomMicSeatController get micSeatController => Get.find<RoomMicSeatController>(tag: getXTag);

  RoomBottomController get bottomController => Get.find<RoomBottomController>(tag: getXTag);

  RoomChatViewController get chatController => Get.find<RoomChatViewController>(tag: getXTag);

  RoomBackgroundMusicController get backgroundMusicController => Get.find<RoomBackgroundMusicController>(tag: getXTag);

  // HostPlayTimeController get hostPlayTimeController => Get.find<HostPlayTimeController>(tag: getXTag);

  RoomFansController get fansController => Get.find<RoomFansController>(tag: getXTag);

  UserCharmWealthController get charmWealthController => Get.find<UserCharmWealthController>(tag: getXTag);

  RoomRedPackController get redPackController => Get.find<RoomRedPackController>(tag: getXTag);

  FamilyLuckyBagController get familyLuckyBagController => Get.find<FamilyLuckyBagController>(tag: getXTag);

  RoomAdController get adController => Get.find<RoomAdController>(tag: getXTag);

  /// 是否开播
  bool get isStart => getArgument<bool>(P_ROOM_START) ?? false;

  /// 打开礼物面板
  String get openGift => getArgument<String>(P_ROOM_OPEN_GIFT) ?? '0';

  /// 打开水果机
  String get openFruitGame => getArgument<String>(P_ROOM_FRUIT_GAME) ?? '0';

  String get bsGameID => getArgument<String>(P_ROOM_BS_GAME_ID) ?? '';

  bool _hadShowInviteMicDialog = false;

  String? get _from => getArgument<String>(P_STATISTIC_FROM);

  //跟随进房的目标用户id
  String? get followRoomTargetId => getArgument<String>(P_TARGET_ID);

  bool? get showSwitchRoomBannerParam => getArgument<bool>(P_SWITCH_ROOM_BANNER);

  bool get loadSignIn => getArgument<bool?>(P_ROOM_SIGN_IN) ?? false;

  static const String kSendGiftGuideKey = "kSendGiftGuideKey";
  Timer? _sendGiftGuideTimer;

  bool _isGameMutedBefore = false;

  String get openBcGameId => getArgument<String>(P_ROOM_BC_GAME_ID) ?? '';

  bool get isOwner => userType.value == RoomUserType.OWNER;

  bool get isAdmin {
    return isOwnerOrAdmin || accountService.isSuperAdmin();
  }

  bool get isOwnerOrAdmin {
    return isOwner || userType.value == RoomUserType.ADMINER;
  }

  @override
  void firstFrame() {
    super.firstFrame();
    Wakelock.enable();
    _setupSwitchRoomBanner();

    /// 移除非当前房间页面的其他直播页面（包括弹窗）
    RouterUtil.removeAllMiddle(R_LIVE_LIVE_ROME);

    /// 移除最小化窗口
    rxUtil.send(RoomOperationEvent.openRoomPage, 1);

    bool hasPop = false;

    if (openBcGameId.isNotEmpty) {
      _onOpenBcGame(openBcGameId);
    } else if (openGift.isTrue) {
      openGiftPanel();
      hasPop = true;
    } else if (openFruitGame.isTrue) {
      openFruitGamePanel();
      hasPop = true;
    } else if (bsGameID.isNum) {
      /// TODO GameSDK
      // BsGameManager.instance.joinGameById(gameId: bsGameID.toInt(), apiSource: gameSourceEnterRoom);
      hasPop = true;
    }

    if (loadSignIn) {
      roomService.signInController.getReward(roomId: roomService.getCurrentRoomId()!);
      hasPop = true;
    }
    if (roomService.getCurrentRoom()?.openTurntable == true) {
      turntableGameService.starPopFruitPanelTimer();
    }

    if (!hasPop) {
      var matchInfo = roomService.cocosGameService.matchInfo;
      if (matchInfo?.matchId.isValid() == true) {
        showCocosGameDialog(matchInfo);
      }
    }
  }

  @override
  void stateInit() {
    super.stateInit();
    _initRoomEventObserver();
    initConfig();
    _checkSendGiftGuide();
    roomService.cocosGameService.loadConfig();
  }

  @override
  void stateDispose() {
    _cancelSendGiftGuideTimer();
    releaseModuleController();
    turntableGameService.cancelPopFruitPanelTimer();
    Wakelock.disable();
    super.stateDispose();
  }

  @override
  void onForeground() {
    Wakelock.enable();

    /// 之前未静音，进入前台要恢复声音
    /// 若之前静音，则不需要恢复
    if (!_isGameMutedBefore) {
      Log.i("GameMute", "resume mute");
      newGameService.setMusicEnable(true);
    }

    super.onForeground();
  }

  @override
  void onBackground() async {
    Wakelock.disable();

    _isGameMutedBefore = !(await newGameService.getMusicEnable());

    /// 之前未静音，进入后台要把声音静音掉
    /// 若之前已静音，则不需要处理
    if (!_isGameMutedBefore) {
      Log.i("GameMute", "mute");
      newGameService.setMusicEnable(false);
    }

    super.onBackground();
  }

  /// 初始化各个模块Controller
  void initModuleController() {
    Get.lazyPut<RoomSettingController>(() => RoomSettingController(this), tag: getXTag);
    Get.lazyPut<RoomMoreDialogController>(() => RoomMoreDialogController(this), tag: getXTag);
    Get.lazyPut<RoomProfileController>(() => RoomProfileController(this), tag: getXTag);
    Get.lazyPut<RoomMicSeatController>(() => RoomMicSeatController(this), tag: getXTag);
    Get.lazyPut<RoomChatViewController>(() => RoomChatViewController(this), tag: getXTag);
    Get.lazyPut<RoomBottomController>(() => RoomBottomController(this), tag: getXTag);
    Get.lazyPut<RoomBackgroundMusicController>(() => RoomBackgroundMusicController(this), tag: getXTag);
    // Get.lazyPut<HostPlayTimeController>(() => HostPlayTimeController(this), tag: getXTag);
    Get.lazyPut<RoomFansController>(() => RoomFansController(this), tag: getXTag);
    Get.lazyPut<RoomRedPackController>(() => RoomRedPackController(this), tag: getXTag);
    Get.lazyPut<FamilyLuckyBagController>(() => FamilyLuckyBagController(this), tag: getXTag);
    Get.lazyPut<UserCharmWealthController>(UserCharmWealthController.new, tag: getXTag);
    Get.lazyPut<RoomAdController>(() => RoomAdController(this), tag: getXTag);
  }

  /// 释放各个模块Controller
  void releaseModuleController() {
    Get.delete<RoomSettingController>(tag: getXTag);
    Get.delete<RoomMoreDialogController>(tag: getXTag);
    Get.delete<RoomProfileController>(tag: getXTag);
    Get.delete<RoomMicSeatController>(tag: getXTag);
    Get.delete<RoomChatViewController>(tag: getXTag);
    Get.delete<RoomBottomController>(tag: getXTag);
    Get.delete<RoomBackgroundMusicController>(tag: getXTag);
    // Get.delete<HostPlayTimeController>(tag: getXTag);
    Get.delete<RoomFansController>(tag: getXTag);
    Get.delete<RoomRedPackController>(tag: getXTag);
    Get.delete<FamilyLuckyBagController>(tag: getXTag);
    Get.delete<UserCharmWealthController>(tag: getXTag);
    Get.delete<RoomAdController>(tag: getXTag);
  }

  void _initRoomEventObserver() {
    listenRxEvent<RoomInfoModel>(RoomEvent.roomInfoUpdate, updateRoomInfo);
    listenRxEvent<RoomInfoModel>(RoomEvent.roomModeUpdate, updateRoomInfo);
    listenRxEvent<int>(RoomEvent.updateOnLineNum, _updateRoomOnlineCount);
    listenRxEvent<RoomUserInfoModel?>(RoomEvent.inviteMic, _showInviteMic);
    listenRxEvent<String>(UserRelationEvent.follow, _onFollowUser);
    listenRxEvent<String>(UserRelationEvent.unFollow, _onUnFollowUser);

    //申请上麦数量更新
    listenRxEvent<int>(RoomMicSeatEvent.applyNumUpdate, _applyUpMicNumUpdate);
    listenRxEvent<MicStatus>(RoomMicOptEvent.micStatusChange, _onMicStatusChange);

    listenRxEvent<PbRoomUser?>(RoomRankEvent.topChange, _rankTopChange);

    listenRxEvent<PbRoomProbabilityGiftWinInfo>(RoomGiftEvent.rewardProbabilityGift, _rewardProbabilityGift);
    listenRxEvent<bool>(RoomMicSeatEvent.switchGiftRewardStatistics, _switchGiftIncentive);
    listenRxEvent<List<RoomUserInfoModel>?>(RoomEvent.updateEnterRoomOnLineUsers, _updateEnterRoomUserList);
    listenRxEvent<int>(RoomEvent.signInRewardOpen, _onSignInRewardOpen);
    listenRxEvent<int>(RoomEvent.openFruitGamePanel, _onOpenFruitGamePanel);
    listenRxEvent<PbRoomUserGiftWallLightedNotice>(RoomEvent.chatMsgGiftWallLightTry, _onGiftWallLightMsgClick);
    listenRxEvent<String>(RoomEvent.beforeLeaveRoom, (value) {});
    listenRxEvent<PbRoomGameEmpireNotice>(RoomGameEmpireEvent.notice, _onReceiveGameEmpireNotice);

    listenRxEvent<int>(RoomEvent.willCloseTips, _handleRoomClosePush);

    listenRxEvent<bool>(RoomGameEvent.musicEnable, (value) async {
      /// 监听静音按钮，更新状态
      _isGameMutedBefore = !value;
    });

    listenRxEvent<int>(RoomEvent.roomLevelUpdate, (value) async {
      roomLevel.value = value;
    });

    listenRxEvent<String>(RoomEvent.openBcGame, _onOpenBcGame);

    listenRxEvent<PbRoomUser>(RoomUserEvent.addAdmin, (_) {
      userType.value = RoomUserType.ADMINER;
    });
    listenRxEvent<PbRoomUser>(RoomUserEvent.removeAdmin, (_) {
      userType.value = RoomUserType.MEMBER;
    });
  }

  /// 当前用户是房主
  bool isRoomOwner() {
    if (roomService.getCurrentUid() != null && roomService.getCurrentRoom()?.roomOwner != null) {
      return roomService.getCurrentUid() == roomService.getCurrentRoom()?.roomOwner;
    } else {
      return false;
    }
  }

  void setUpData({RoomInfoModel? model, bool? isUpdate}) {
    Log.d('room', 'RoomMainController setUpData isUpdate=$isUpdate,model=$model');
    var roomInfo = model ?? roomService.getCurrentRoom();
    if (roomInfo != null) {
      if (roomInfo.isClose) {
        _reportCloseImpl();
        toast(LocaleStrings.instance.roomIsClosed, isDark: true);
        LiveRoomHandler.closeRoomPage();
        return;
      }

      roomName.value = roomInfo.name;
      roomLevel.value = roomInfo.level;
      cover.value = roomInfo.cover ?? roomInfo.roomOwnerAvatar;
      if (roomService.getCurrentUid() == roomInfo.roomOwner) {
        showOwnerFollow.value = false;
      } else {
        showOwnerFollow.value = roomInfo.hasFollow != true;
      }

      isFollowed.value = roomInfo.isFollowed == 1;

      roomOwnerAvatar.value = roomInfo.roomOwnerAvatar;
      roomOwnerAvatarCode.value = roomInfo.roomOwnerAvatarCode;
      roomID.value = roomInfo.roomId;
      roomShowID.value = roomInfo.showId;
      roomAnnounce.value = roomInfo.announce;

      roomHasPsw.value = roomInfo.hasPw == 1;
      password.value = roomInfo.password;

      themeModel.value = roomInfo.roomTheme;

      onLineNum.value = roomInfo.roomUserCount;

      userType.value = roomInfo.userType;

      micPermission.value = roomInfo.micPermission;

      roomMode.value = roomInfo.mode;

      h5GameList.value = roomInfo.h5GameList ?? [];

      if (roomInfo.fansClubInfo?.hasJoin == true) {
        fansController.fansLevel.value = roomInfo.fansClubInfo?.level ?? -1;
      } else {
        fansController.fansLevel.value = -1;
      }
    }
    onLineRankTopUser.value = roomService.onlineTopOneUser;
    fansController.showFansGetTimesRedType.value = roomService.fansService.showFansDrawGetRedDotType;
  }

  void initConfig() {
    initRTMAndRTCEngine();
  }

  //rtc和rtm建连
  void initRTMAndRTCEngine() async {
    if (!roomService.isRoomChannel()) {
      await roomService.joinRoomChannel();
    } else if (isPageValid) {
      micStatus.value = micSeatService.micStatus;
    }
  }

  void _updateEnterRoomUserList(List<RoomUserInfoModel>? onLineUsers) async {
    if (!roomService.isRoomOwner() || micSeatService.hasAutoUpMic) {
      return;
    }
    bool gotoAutoMic = false;
    if (onLineUsers?.isNotEmpty == true) {
      for (var element in onLineUsers!) {
        if (element.uid == accountService.currentUid()) {
          gotoAutoMic = true;
          break;
        }
      }
    }
    if (gotoAutoMic && isPageValid && !micSeatService.hasAutoUpMic) {
      await micSeatService.autoUpMic();
    }
  }

  _onMicStatusChange(MicStatus status) {
    micStatus.value = status;
    Log.d('room', 'RoomMainController MicStatusChange status=$status');
  }

  void updateRoomInfo(RoomInfoModel model) {
    Log.d('room', 'RoomMainController updateRoomInfo $model');
    setUpData(model: model, isUpdate: true);
  }

  void _updateRoomOnlineCount(int count) {
    onLineNum.value = count;
    roomService.mergeRoomInfo(roomUserCount: count);
  }

  String? get from {
    return getArgument(P_STATISTIC_FROM) ?? LiveRoomHandler.from;
  }

  /// 跳转私聊
  void goChat({
    required BuildContext context,
    String? uid,
    String? from,
    bool? needCheck = true,
  }) async {
    if (uid == null) {
      return;
    }
    var staFrom = from ?? StatisticPageFrom.liveRoom;

    ///检查防骚扰限制
    var disturbChatResult = await checkDisturbChat(uid, staFrom);
    if (disturbChatResult.result && !disturbChatResult.checkTypeResult) {
      return;
    }
    routerUtil.push(R_CHAT, params: {
      P_TARGET_ID: uid,
      P_CONVERSATION_TYPE: RCIMIWConversationType.private,
      P_STATISTIC_FROM: StatisticPageFrom.liveRoom,
      P_SOURCE: ChatSourceType.room,
      P_CHECK_PRE_CHAT: needCheck,
    });
  }

  _onFollowUser(String targetUid) {
    if (targetUid == roomService.getCurrentRoom()?.roomOwner) {
      showOwnerFollow.value = false;
    }
  }

  _onUnFollowUser(String targetUid) {
    if (targetUid == roomService.getCurrentRoom()?.roomOwner) {
      showOwnerFollow.value = true;
    }
  }

  /// 关注房主
  Future<bool> followOwner({required BuildContext context, String? from, bool? showToast = true}) async {
    var uid = roomService.getCurrentRoom()?.roomOwner;
    if (uid == null || uid.isEmpty) return Future.value(false);
    return await followUser(context: context, uid: uid, from: from, showToast: showToast);
  }

  /// 房间里面关注用户
  /// 成功后 会向对方发送一条消息
  Future<bool> followUser({required BuildContext context, String? uid, String? from, bool? showToast = true}) async {
    ///是否被封禁
    var context = getContext();
    if (uid == null || context != null && (await hasBeRestricted(context))) {
      return false;
    }
    var page = from ?? StatisticPageFrom.liveRoom;
    showLoading();
    var resp = await socialService.oprFollow(targetUid: uid, isFollow: true, page: page);
    hideLoading();
    if (resp.isSuccess) {
      if (showToast == true) {
        toast(LocaleStrings.instance.successfully, isDark: true);
      }
      return true;
    } else {
      toast(resp.msg ?? "", isDark: true);
      return false;
    }
  }

  /// 邀请上麦
  Future<bool> inviteMic({required String uid}) async {
    var response = await micSeatService.inviteMic(uid: uid);
    // 异步报告统计信息
    void report() async {
      var roomUserResp = await roomService.getRoomUserCard(uid);
      var sex;
      if (roomUserResp.isSuccess) {
        sex = roomUserResp.data?.sexStr;
      }
      RoomStatistics.reportRoomInvitedMic(
          roomType: roomService.getCurrentRoom()?.roomType,
          toUid: uid,
          toGender: sex,
          type: roomService.getCurrentRoom()?.mode,
          roomId: roomService.getCurrentRoom()?.roomId,
          act: '1',
          from: from);
    }

    if (response.isSuccess) {
      report();
      return true;
    } else {
      // 失败时显示提示信息
      toast(response.msg ?? LocaleStrings.instance.pleaseTryAgain, isDark: true);
      return false;
    }
  }

  void _showInviteMic(RoomUserInfoModel? info, {String? role, bool checkShow = true}) {
    if (info == null) return;
    var context = getContext();
    if (context == null) return;
    DialogScheduler.instance().schedule(
      () async {
        var context = getContext();
        if (context == null) return;
        if (checkShow) {
          if (_hadShowInviteMicDialog) return;
          _hadShowInviteMicDialog = true;
        }

        return showInviteMicDialog(
          infoModel: info,
          context: context,
          onConfirm: () {
            _hadShowInviteMicDialog = false;
            _acceptInviteRequest(roomService.getCurrentUid()!);
          },
          onDismiss: () {
            _hadShowInviteMicDialog = false;
          },
        );
      },
      context: context,
      single: true,
      path: R_LIVE_ROOM_INVITE_MIC,
      priority: 2,
    );
  }

  void _acceptInviteRequest(String uid) async {
    var response = await micSeatService.handleInviteMic(uid: uid, accept: true);
    reportFbEvent(name: 'room_take_mic');
    reportRoomTakeMicSucc(from: _from, status: '1');
    reportFbEvent(name: 'room_take_mic_succ');
    reportKochavaEvent(name: 'room_take_mic_succ');
    reportRoomTakeMic(status: '1');
    if (!response.isSuccess) {
      toast(response.msg ?? LocaleStrings.instance.pleaseTryAgain, isDark: true);
    }
  }

  /// share Room and send a message
  void shareRoom(BuildContext context) {
    RoomStatistics.reportRoomShareImp(
        roomId: roomService.getCurrentRoomId(), type: roomService.getCurrentRoom()?.mode, from: 'screen');
    showRoomShareDialog();
  }

  void _reportCloseImpl() {
    RoomStatistics.reportRoomCloseImp(
        roomId: roomService.getCurrentRoomId(),
        type: roomService.getCurrentRoom()?.mode,
        status: roomService.getCurrentRoom()?.isFollowed?.toString());
  }

  /// 申请上麦
  void applyUpMic({int? position}) async {
    if (accountService.isSuperAdmin()) {
      toast(LocaleStrings.instance.youAreSuperAdmin, isDark: true);
      return;
    }
    micSeatService.toggleMicStatus(position: position);
  }

  void cancelApplyMic() async {
    showLoading();
    final resp = await micSeatService.applyMic(
        action: ApplyMicAction.cancel, uid: roomService.getCurrentUid(), roomId: roomService.getCurrentRoomId());
    hideLoading();
    if (!resp.isSuccess) {
      toast(resp.msg ?? '', isDark: true);
    }
  }

  void _applyUpMicNumUpdate(int applyNum) {
    micApplyCount.value = applyNum;
  }

  void resetFollowRoomTargetId() {
    setArguments({P_TARGET_ID: null});
  }

  void _rankTopChange(PbRoomUser? value) {
    // only change refresh UI.
    if (!equalRankOnLineTopOne(onLineRankTopUser.value, value)) {
      onLineRankTopUser.value = value;
    }
  }

  void _rewardProbabilityGift(PbRoomProbabilityGiftWinInfo notice) {
    showRewardProbabilityGiftResultDialog(notice: notice);
  }

  void _setupSwitchRoomBanner() {
    showSwitchRoomBanner.value = showSwitchRoomBannerParam ?? false;
  }

  void switchMatchRoom() {
    if (roomMode.value != null) {
      startRoomMatch(roomMode: roomMode.value!, isSwitchRoomTip: true, isCheckSwitchRoom: false);
    }
  }

  void _switchGiftIncentive(bool enable) {
    enableGiftIncentives.value = enable;
  }

  void _checkSendGiftGuide() async {
    RoomInfoModel? roomInfo = roomService.getCurrentRoom();
    UserInfo? ownerInfo = await userService.getUserInfo(roomInfo?.roomOwner ?? '');
    UserInfo? userInfo = await userService.getCurrentUserInfo();

    if (ownerInfo != null &&
        userInfo != null &&
        ownerInfo.uid != userInfo.uid &&
        sexToString(ownerInfo.sex) != sexToString(userInfo.sex)) {
      String key = '${kSendGiftGuideKey}_${accountService.getAccountInfo()!.uid}';
      int? lastPopTime = roomService.preferences.getInt(key);
      if (lastPopTime != null) {
        DateTime lastPopDate = DateTime.fromMillisecondsSinceEpoch(lastPopTime);

        var lastPopDateFormat = DateFormat('yyyy-MM-dd').format(lastPopDate);
        DateTime appOpenConvertDate = DateTime.parse(lastPopDateFormat);

        DateTime currentTimeDate = DateTime.now();
        int days = currentTimeDate.difference(appOpenConvertDate).inDays;
        if (days < 1) {
          return;
        }
      }
      _cancelSendGiftGuideTimer();
      _sendGiftGuideTimer = Timer(Duration(seconds: 20), () {
        if (!mounted) {
          return;
        }
        _checkSendBackpackGift(ownerInfo, userInfo);
      });
    }
  }

  void _checkSendBackpackGift(UserInfo ownerInfo, UserInfo userInfo) async {
    List<BackpackGift>? list = await giftService.getBagGifts(tag: '', fromServer: true);
    if (list?.isNotEmpty == true) {
      GiftInfo? giftInfo = list!.first.info!;
      for (var element in list) {
        if (element.info?.price != null && giftInfo?.price != null && element.info!.price! < giftInfo!.price!) {
          giftInfo = element.info;
        }
      }
      if (giftInfo != null && mounted) {
        int time = DateTime.now().millisecondsSinceEpoch;
        String key = '${kSendGiftGuideKey}_${accountService.getAccountInfo()!.uid}';
        roomService.preferences.setInt(key, time);
        showRoomSendGiftGuideDialog(ownerInfo: ownerInfo, userInfo: userInfo, giftInfo: giftInfo);
      }
    }
  }

  void _cancelSendGiftGuideTimer() {
    if (_sendGiftGuideTimer != null) {
      _sendGiftGuideTimer?.cancel();
      _sendGiftGuideTimer = null;
    }
  }

  /// 打开礼物面板
  void openGiftPanel() {
    if (mounted) {
      showGiftPanelDialog(context: getContext()!);
    }
  }

  void openFruitGamePanel() {
    final canOpen = accountService.hadBc;
    if (!canOpen) return;

    if (mounted) {
      showTurntableGame(getContext()!);
    }
  }

  void _onSignInRewardOpen(int value) {
    if (mounted) {
      showRoomSignInRewardDialog(mainController: this, context: getContext()!);
    }
  }

  void _onOpenFruitGamePanel(int value) {
    openFruitGamePanel();
  }

  void _onOpenBcGame(String gameId) async {
    // 是否有权限
    if (!accountService.hadBc) return;

    final webGameIds = roomService.getCurrentRoom()?.h5GameList ?? [];

    // 在后端配置列表才能打开
    final canOpen = webGameIds.contains(gameId);
    if (!canOpen) return;

    final config = await gameService.fetchWebGameConfig();

    final game = config?.webGameList?.firstWhereOrNull((e) => e.gameId == gameId);
    if (game == null) return;

    final url = game.url ?? '';
    if (url.isEmpty) return;

    /// 已经打开游戏面板，若相同游戏点击go则不跳转
    if (FLRouter.routeObserver.lastRouter?.settings.name == R_LIVE_COMMON_WEB_DIALOG) {
      final arguments = FLRouter.routeObserver.lastRouter?.settings.arguments;
      if (arguments is Map<String, String>) {
        final currentUrl = arguments["url"];
        if (currentUrl == url) {
          return;
        }
      }
    }

    routerUtil.popUntil(R_LIVE_LIVE_ROME);
    if (url.startsWith('http')) {
      routerUtil.push(R_COMMON_WEB_PAGE, params: {"url": url});
    } else {
      DeepLink.jump(url);
    }
  }

  void _onGiftWallLightMsgClick(PbRoomUserGiftWallLightedNotice value) {
    showGiftGalleryGiftDetailDialog(
        targetUid: value.receiverUid, giftItem: GiftListItem.fromPb(value), from: StatisticPageFrom.liveRoom);
  }

  /// 设置游戏视图大小
  void setGameViewRect(double offsetY, double height) {
    /// 避免重复设置，获取到不同值导致视图发生变化
    if (roomSudGameRect.value != null) {
      return;
    }
    if (offsetY >= 0 && height >= 0) {
      roomSudGameRect.value = Rect.fromLTWH(0, offsetY, 0, height);
    } else {
      roomSudGameRect.value = null;
    }
  }

  void _onReceiveGameEmpireNotice(PbRoomGameEmpireNotice value) {
    if (mounted) {
      if (value.isMatch()) {
        showCocosGameDialog(value.matchInfo);
        // adController.updateCocosGame();
      } else if (value.isResult()) {
        routerUtil.removeAll(R_LIVE_ROOM_COCOS_GAME, context: getContext());
        DialogScheduler.instance().removeDialog(R_LIVE_ROOM_COCOS_GAME);

        /// TODO GameSDK
        // CocoasGameResultDialog.showResultDialog(value.resultInfo);
        // adController.updateCocosGame();
        // 胜利向房间中插入公屏消息
        if (value.resultInfo.result == 1 && value.resultInfo.message.isNotEmpty) {
          roomChatService.sendCocoasGameResultMsg(message: value.resultInfo.message);
        }
      }
    }
  }

  void showCocosGameDialog(RoomGameEmpireMatchInfo? matchInfo) {
    if (mounted && matchInfo != null) {
      if (RouterUtil.isTop(R_LIVE_ROOM_COCOS_GAME)) {
        return;
      }
      routerUtil.removeAll(R_LIVE_ROOM_COCOS_GAME, context: getContext());
      CocosGameCache.inst.getGamePath();

      /// TODO GameSDK
      // showCocosGameBottomDialog(context: getContext(), matchInfo: matchInfo);
    }
  }

  void editCover() {
    if (userType.value != RoomUserType.OWNER) return;

    if (roomService.getCurrentRoom()?.isFamilyRoom == true) {
      toast(LocaleStrings.instance.familyRoomCoverCannotBeModified);
      return;
    }

    final context = getContext();
    if (context == null) return;
    pickPhotoVideo(
      context,
      type: MediaType.image,
      maxSize: 1,
      cropParams: CropParams(
        aspectRatio: 1,
        initialSize: 0.75,
      ),
      withCrop: (result) {
        if (result.width < 100 || result.height < 100) {
          toast(LocaleStrings.instance.imageTooSmall);
          return;
        }
        final path = result.file.path;
        // result.width
        final uploader = UploadFiles(
            fileList: [FileModel(path: path, name: "roomCover_pic_0", height: 150, width: 150)],
            complete: (files) async {
              final file = files.firstOrNull;
              if (file == null) return;

              final cover = file.url ?? '';
              if (cover.isEmpty) {
                toast(LocaleStrings.instance.pleaseTryAgain);
                return;
              }

              final result = await roomService.editRoomInfo(roomCover: cover);
              if (result.isSuccess) {
                toast(LocaleStrings.instance.editSuccessfully);
                final info = roomService.getCurrentRoom()?..cover = cover;
                rxUtil.send<RoomInfoModel?>(RoomEvent.roomInfoUpdate, info);

                this.cover.value = cover;
                settingsController.update();
                // rxUtil.send(RoomEvent.myRoomInfoUpdate, 0);
              } else {
                toast(result.msg ?? LocaleStrings.instance.defaultError);
              }
            },
            uploadFail: (list, {code, msg}) {
              toast(msg ?? LocaleStrings.instance.pleaseTryAgain);
            },
            roomId: roomID.value);
        uploader.upload();
      },
    );
  }

  void _handleRoomClosePush(int time) {
    /// 房主才显示
    if (roomService.getCurrentRoom()?.isOwnerOrAdmin == false) return;

    showAlertDialog(
      context: getContext(),
      contentWidget: RoomCloseCountdownWidget(
        time: time,
        timeOutCallback: () {
          routerUtil.removeAll(R_LIVE_ROOM_WILL_CLOSE_DIALOG);
        },
      ),
      showCancel: false,
      confirmText: LocaleStrings.instance.ok,
      onConfirm: () {
        roomService.keepAlive();
      },
      routeSettings: dialogRouteSettings(R_LIVE_ROOM_WILL_CLOSE_DIALOG),
    );
  }

  /// 点击关注房间
  void onTapFollowRoom() async {
    RoomStatistics.reportRoomFollowClick(
        roomId: roomID.value, roomType: roomService.getCurrentRoom()?.isFamilyRoom == true ? "family" : "normal");
    final resp = await roomService.followerRoom(roomId: roomID.value);
    if (resp.isSuccess) {
      toast(LocaleStrings.instance.followedSuccessfully);
      final roomInfo = roomService.getCurrentRoom()?..isFollowed = 1;
      roomService.updateRoomInfo(roomInfo: roomInfo);
      showFollowGuide.value = false;
    } else {
      toast(resp.msg ?? LocaleStrings.instance.defaultError);
    }
  }

  void editRoomInfo({String? mode, String? fee, String? gameMode, String? tagId, String? tagName}) async {
    showLoading();
    var resp = await roomService.editRoomInfo(
      roomMode: mode,
      fee: fee,
      gameMode: gameMode,
      tagId: tagId,
      tagName: tagName,
    );
    hideLoading();
    if (resp.isSuccess) {
      routerUtil.popUntil(R_LIVE_LIVE_ROME);
    } else {
      if (resp.code == 1019) {
        /// 余额不足弹起充值弹框
        showRechargeDialog(index: 1, context: getContext());
      } else {
        toast(resp.msg ?? LocaleStrings.instance.defaultError, isDark: true);
      }
    }
    RoomStatistics.reportRoomFunctionChange(type: mode, roomId: roomService.getCurrentRoom()?.roomId);
  }

  /// 初始化关注引导显示状态
  void onInitFollowStatus() {
    bool showedRoomFollowGuide =
        roomService.preferences.getBool(_getSpKey(spKeyShowedRoomFollowGuide), defaultValue: false) ?? false;
    if (!showedRoomFollowGuide) {
      showFollowGuide.value =
          roomService.getCurrentRoom()?.isOwner == false && roomService.getCurrentRoom()?.isFollowed == 0;
      roomService.preferences.setBool(_getSpKey(spKeyShowedRoomFollowGuide), true);
    }
  }

  String _getSpKey(String key) {
    return key + (accountService.currentUid() ?? '') + (roomService.getCurrentRoomId() ?? '');
  }
}
