import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/chat_view/room_chat_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/model/room_mode_list_model.dart';

import 'create_room_controller.dart';

class RoomModeListWidget extends StatefulWidget {
  final CreateRoomController controller;

  RoomModeListWidget({
    required this.controller,
  });

  @override
  State<StatefulWidget> createState() => _RoomModeListWidgetState();
}

class _RoomModeListWidgetState extends State<RoomModeListWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.controller.canSelectModeList()) {
      return _roomModeList(widget.controller.modeList!);
    }
    return SizedBox.shrink();
  }

  Widget _roomModeList(List<RoomModeListItem> list) {
    var rest = <Widget>[];
    for (final item in list) {
      if (item.lock == 1) continue;
      
      final roomMode = item.mode ?? '';
      switch (roomMode) {
        case RoomMode.CHAT:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateChat,
              selIcon: Res.roomTypeChatSel,
              title: LocaleStrings.instance.chat,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;

        case RoomMode.TRUTH_DARE:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateTruthDare,
              selIcon: Res.roomTypeTruthDareSel,
              title: LocaleStrings.instance.truthDare,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;

        case RoomMode.BLIND_DATE:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateBlindDate,
              selIcon: Res.roomTypeBlindDateSel,
              title: LocaleStrings.instance.blindDate,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;
        case RoomMode.LUDO_GAME:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateLudo,
              selIcon: Res.roomTypeCreateLudoSel,
              title: LocaleStrings.instance.ludo,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;
        case RoomMode.DOMINO_GAME:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateDomino,
              selIcon: Res.roomTypeCreateDominoSel,
              title: LocaleStrings.instance.domino,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;
        case RoomMode.CANDY_GAME:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateCandy,
              selIcon: Res.roomTypeCreateCandySel,
              title: LocaleStrings.instance.candy,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;
        case RoomMode.UNO_GAME:
          rest.add(_buildRoomItem(
              normalIcon: Res.roomTypeCreateUno,
              selIcon: Res.roomTypeCreateUnoSel,
              title: LocaleStrings.instance.uno,
              isSelected: widget.controller.isSelected(roomMode),
              onTap: () => widget.controller.changeRoomByType(roomMode)));
          break;
      }
    }

    int itemCount = rest.length;
    double margin = 18.pt;
    double w = MediaQuery.of(context).size.width - margin * 2;
    double itemHeight = 102.pt;

    double crossAxisCount = itemCount > 2 ? 3 : 2;
    double itemWidth = 0;
    if (itemCount > 3) {
      crossAxisCount = 3.5;
      itemWidth = 130.pt;
    } else {
      itemWidth = w / crossAxisCount;
    }

    double childAspectRatio = itemHeight / itemWidth;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 12.pt),
        Padding(
          padding: EdgeInsetsDirectional.only(start: 18.pt),
          child: Text(
            LocaleStrings.instance.roomType,
            style: TextStyle(
              color: Color(0xFF7F7F7F),
              fontSize: 14.sp,
              fontWeight: FontWeightExt.heavy,
            ),
          ),
        ),
        SizedBox(height: 8.pt),
        Container(
          margin: EdgeInsets.symmetric(horizontal: margin),
          padding: EdgeInsets.symmetric(vertical: 18.pt),
          decoration: BoxDecoration(
            color: R.color.backgroundColor.withOpacity(0.5),
            borderRadius: BorderRadius.all(Radius.circular(14.pt)),
          ),
          child: SizedBox(
            height: itemHeight,
            child: ScrollConfiguration(
              behavior: OverScrollBehavior(),
              child: GridView(
                physics: itemCount > 3 ? null : NeverScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                children: rest,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  mainAxisSpacing: 0,
                  crossAxisSpacing: 0,
                  childAspectRatio: childAspectRatio,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 12.pt),
      ],
    );
  }

  Widget _buildRoomItem({
    required String normalIcon,
    required String selIcon,
    required String title,
    required bool isSelected,
    required Function() onTap,
  }) {
    Widget textWidget = Container(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 8.pt, vertical: 2.pt),
      decoration: BoxDecoration(
        color: isSelected ? R.color.primaryColor : Colors.transparent,
        borderRadius: BorderRadius.all(Radius.circular(11.pt)),
      ),
      child: Text(
        title,
        style: TextStyle(
          color: isSelected ? Colors.white : R.color.textBlueColor2,
          fontSize: 12.sp,
          fontWeight: FontWeightExt.heavy,
        ),
      ),
    );

    return Center(
      child: ScaleTapWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FLImage.asset(
                isSelected ? selIcon : normalIcon,
                width: 54.pt,
                height: 74.pt,
              ),
              SizedBox(height: 5.pt),
              textWidget,
            ],
          ),
          onTap: onTap),
    );
  }
}
