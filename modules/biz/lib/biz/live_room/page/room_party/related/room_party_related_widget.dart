import 'package:biz/biz.dart';
import 'package:biz/biz/home/<USER>/home_for_you_list_widget.dart';
import 'package:biz/biz/live_room/component/chat_view/room_chat_view_widget.dart';
import 'package:biz/biz/live_room/page/room_party/related/room_party_related_controller.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_party_list_item.dart';
import 'package:biz/global/date_format_utils.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/model/room_party_related_list.dart';

class RoomPartyRelatedWidget extends StatefulWidget {
  const RoomPartyRelatedWidget({super.key});

  @override
  State<RoomPartyRelatedWidget> createState() => _RoomPartyRelatedWidgetState();
}

class _RoomPartyRelatedWidgetState extends State<RoomPartyRelatedWidget>
    with GetStateBuilderMixin<RoomPartyRelatedWidget, RoomPartyRelatedController> {
  @override
  RoomPartyRelatedController initCtl() {
    return RoomPartyRelatedController();
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return ScrollConfiguration(
      behavior: OverScrollBehavior(),
      child: SmartRefresher(
        controller: getCtl.refreshController,
        onRefresh: getCtl.refreshData,
        onLoading: getCtl.loadMoreData,
        enablePullDown: true,
        enablePullUp: getCtl.hasMore,
        primary: true,
        physics: ScrollPhysics(),
        header: GlobalWidgets.refreshHeader(),
        footer: GlobalWidgets.refreshFoot(),
        child: ListView.separated(
          itemCount: getCtl.list.length,
          separatorBuilder: (_, __) => 9.hSpace,
          itemBuilder: (_, index) {
            final model = getCtl.list[index];
            return _item(model);
          },
        ),
      ),
    );
  }

  @override
  Widget buildStatusLoading() {
    return Container(
      width: 1.w,
      height: 0.5.h,
      child: Center(
        child: GlobalWidgets.loadingIndicator(),
      ),
    );
  }

  @override
  Widget buildStatusError({String? errMsg}) {
    return GlobalWidgets.netFailedWidget(
      btnAction: () {
        getCtl.refreshData();
      },
      padding: EdgeInsets.only(top: 80.pt),
    );
  }

  @override
  Widget buildStatusEmpty() {
    return EmptyWidget(
      logoAsset: Res.emptyEmptyUserPost,
      logoSize: Size(254.pt, 254.pt),
      btnSize: Size(295.pt, 50.pt),
      detail: LocaleStrings.instance.emptyTheList,
      btnAction: () => getCtl.refreshData(),
    );
  }

  Widget _item(ListItem model) {
    return OfficialRoomContainerWidget(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FLImage.netImg(model.roomCover, height: 80.pt, fit: BoxFit.cover),
          Expanded(
            child: Padding(
              padding: EdgeInsets.fromLTRB(14.pt, 17.pt, 16.pt, 12.pt),
              child: Row(
                children: [
                  Expanded(child: _infoWidget(model)),
                  Visibility(
                    visible: model.isOpen,
                    child: GestureDetector(
                      onTap: () {
                        routerUtil.push(
                          R_LIVE_ROME_JOIN,
                          params: {
                            P_ID: model.roomId,
                            P_STATISTIC_FROM: 'home_related',
                          },
                        );
                        RoomStatistics.reportRoomListClick(content: model.roomTag, from: 'recently');
                      },
                      child: HomeChatButton(inRoom: true, inGame: model.modeIcon?.isNotEmpty == true),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      isOfficial: model.isOfficial == true,
      officialLogo: model.officialLogo,
    );
  }

  Widget _infoWidget(ListItem model) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          model.roomName,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 16.pt,
            fontWeight: FontWeightExt.heavy,
            color: R.color.color20,
          ),
        ),
        Text(
          LocaleStrings.instance.enteredDays(DateFormatUtils.dataFormatTime(model.enterTime)),
          style: TextStyle(
            fontSize: 11.pt,
            fontWeight: FontWeightExt.medium,
            color: R.color.color90,
          ),
        ),
      ],
    );
  }
}
