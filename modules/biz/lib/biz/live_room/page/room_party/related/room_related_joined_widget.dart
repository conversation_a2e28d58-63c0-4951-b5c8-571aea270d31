import 'package:biz/biz.dart';
import 'package:biz/biz/home/<USER>/home_for_you_list_widget.dart';
import 'package:biz/biz/live_room/component/chat_view/room_chat_view_widget.dart';
import 'package:biz/biz/live_room/page/room_party/related/room_related_joined_ctl.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_level_widget.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_tag_widget.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';

import '../../../../family/widgets/family_level_widget.dart';
import '../widget/room_party_list_item.dart';

class RoomRelatedJoinedWidget extends StatefulWidget {
  const RoomRelatedJoinedWidget({super.key});

  @override
  State<RoomRelatedJoinedWidget> createState() => _RoomRelatedJoinedWidgetState();
}

class _RoomRelatedJoinedWidgetState extends State<RoomRelatedJoinedWidget>
    with GetStateBuilderMixin<RoomRelatedJoinedWidget, RoomRelatedJoinedCtl> {
  @override
  RoomRelatedJoinedCtl initCtl() {
    return RoomRelatedJoinedCtl();
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return ScrollConfiguration(
      behavior: OverScrollBehavior(),
      child: SmartRefresher(
        controller: getCtl.refreshController,
        onRefresh: getCtl.refreshData,
        onLoading: getCtl.loadMoreData,
        enablePullDown: true,
        enablePullUp: true,
        primary: true,
        physics: ScrollPhysics(),
        header: GlobalWidgets.refreshHeader(),
        footer: GlobalWidgets.refreshFoot(),
        child: ListView.separated(
          itemCount: getCtl.list.length,
          separatorBuilder: (_, __) => 9.hSpace,
          itemBuilder: (_, index) {
            final model = getCtl.list[index];
            return _item(model);
          },
        ),
      ),
    );
  }

  @override
  Widget buildStatusLoading() {
    return Container(
      width: 1.w,
      height: 0.5.h,
      child: Center(
        child: GlobalWidgets.loadingIndicator(),
      ),
    );
  }

  @override
  Widget buildStatusError({String? errMsg}) {
    return GlobalWidgets.netFailedWidget(
      btnAction: () {
        getCtl.refreshData();
      },
      padding: EdgeInsets.only(top: 80.pt),
    );
  }

  @override
  Widget buildStatusEmpty() {
    return EmptyWidget(
      logoAsset: Res.emptyEmptyVisitor,
      logoSize: Size(254.pt, 254.pt),
      detail: LocaleStrings.instance.notJoinedAnyRoom,
    );
  }

  Widget _item(RoomPartyListItemEntity model) {
    final isFamilyRoom = model.isFamilyRoom && (model.familyLevel ?? 0) > 0;
    return OfficialRoomContainerWidget(child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        FLImage.auto(model.roomCover ?? '', height: 80.pt, width: 80.pt, fit: BoxFit.cover),
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(14.pt, isFamilyRoom ? 6.5.pt : 17.pt, 16.pt, isFamilyRoom ? 5.pt : 10.pt),
            child: Row(
              children: [
                Expanded(child: _infoWidget(model)),
                Visibility(
                  visible: model.isOpen,
                  child: GestureDetector(
                    onTap: () {
                      routerUtil.push(
                        R_LIVE_ROME_JOIN,
                        params: {
                          P_ID: model.rid,
                          P_STATISTIC_FROM: 'following',
                        },
                      );
                      RoomStatistics.reportRoomListClick(content: model.roomTag, from: 'following');
                    },
                    child: HomeChatButton(inRoom: true, inGame: model.modeIcon?.isNotEmpty == true),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    ), isOfficial: model.isOfficial ?? false, officialLogo: model.officialLogo);
  }

  Widget _infoWidget(RoomPartyListItemEntity model) {
    final isFamilyRoom = model.isFamilyRoom && (model.familyLevel ?? 0) > 0;
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 150.pt),
          child: Text(
            model.roomName ?? '',
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 16.pt,
              fontWeight: FontWeightExt.heavy,
              color: R.color.textColor1,
            ),
          ),
        ),
        Row(
          children: [
            OnlineNumWidget(num: model.onlineCnt ?? 0),
            10.wSpace,
            RoomLevelWidget(level: model.level ?? 0),
            RoomTagWidget(title: model.roomTag ?? '', color: model.roomTagColor?.toColor ?? R.color.primaryColor),
          ],
        ),
        if (isFamilyRoom) FamilyLevelWidget(level: model.familyLevel ?? 0),
      ],
    );
  }
}
