import 'package:biz/biz.dart';
import 'package:biz/biz/ad/float_btn/float_btn.dart';
import 'package:biz/biz/ad/float_btn/no_scaling_animation.dart';
import 'package:biz/biz/home/<USER>/home_style_tab_indicator.dart';
import 'package:biz/biz/live_room/page/event/event_list/event_list_page.dart';
import 'package:biz/biz/live_room/page/room_party/controller/room_party_controller.dart';
import 'package:biz/biz/live_room/page/room_party/recommend/room_party_recommend_page.dart';
import 'package:biz/biz/live_room/page/room_party/related/room_related_page.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/common/statistics/roomlist_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/keep_wrapper.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';

/// Party房间列表页面
class RoomPartyPage extends StatefulWidget {
  const RoomPartyPage({Key? key}) : super(key: key);

  @override
  State<RoomPartyPage> createState() => _RoomPartyPageState();
}

class _RoomPartyPageState extends State<RoomPartyPage> with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  final RoomPartyController _listController = RoomPartyController();

  TabController? _tabController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
    _tabController?.addListener(() {
      if (_tabController?.indexIsChanging ?? true) return;
      final index = _tabController?.index ?? 0;
      final tab = index == 0
          ? 'related'
          : index == 1
              ? 'recommend'
              : 'room_event';
      RoomStatistics.reportRooTabClick(rooTabClick: tab);
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBindingBuilder(
      controller: _listController,
      child: GetBuilder(
        init: _listController,
        assignId: true,
        builder: (_) {
          return Stack(
            children: [
              Container(
                color: const Color(0xFFF5F7F9),
              ),
              FLImage.asset(Res.roomBgPage, width: double.infinity, fit: BoxFit.cover),
              SafeArea(
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: _page(),
                  floatingActionButton: FloatBtn(
                    size: Size(56.pt, 56.pt),
                    type: FloatAdType.party,
                  ),
                  floatingActionButtonLocation: FloatBtnLocation(context),
                  floatingActionButtonAnimator: NoScalingAnimation(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _topTabBar() {
    return Row(
      children: [
        7.wSpace,
        Expanded(child: _tabBar()),
        14.wSpace,
        _searchWidget(),
        14.wSpace,
        Obx(() {
          return _RoomPageMyRoomWidget(
            info: _listController.info.value,
            onTap: () => _listController.joinRoom(from: 'home_room'),
          );
        }),
        14.wSpace,
      ],
    );
  }

  Widget _page() {
    return Column(
      children: [
        _topTabBar(),
        Expanded(
          child: _tabBarView(),
        ),
      ],
    );
  }

  Widget _tabBar() {
    if (_tabController == null) return const SizedBox.shrink();

    final tabList = [LocaleStrings.instance.related, LocaleStrings.instance.recommend, LocaleStrings.instance.roomEvent]
        .mapNotNull((e) => Tab(child: Text(e)))
        .toList();

    return Container(
      height: 28.pt + 26.pt,
      child: TabBar(
        isScrollable: true,
        controller: _tabController,
        labelStyle: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeightExt.heavy,
          color: R.color.color20,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeightExt.medium,
          color: R.color.colorB2,
        ),
        labelPadding: EdgeInsets.symmetric(horizontal: 7.pt),
        indicatorSize: TabBarIndicatorSize.label,
        indicator: HomeStyledTabIndicator(
          margin: 0.pt,
          width: 20.pt,
          height: 4.pt,
        ),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: tabList,
      ),
    );
  }

  Widget _tabBarView() {
    if (_tabController == null) return const SizedBox.shrink();

    return TabBarView(
      controller: _tabController,
      children: [
        KeepWrapper(child: RoomRelatedPage(roomController: _listController)),
        KeepWrapper(child: RoomPartyRecommendPage()),
        Obx(() => KeepWrapper(child: EventListPage(roomId: _listController.info.value?.roomId)))
      ],
    );
  }

  Widget _searchWidget() {
    return GestureDetector(
      onTap: () {
        routerUtil.push(R_LIVE_ROOM_SEARCH);
        RoomlistStatistics.reportRoomSearchClick();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 26.pt,
            height: 26.pt,
            decoration: BoxDecoration(color: Colors.white.withOpacity(0.4), borderRadius: BorderRadius.circular(13.pt)),
          ),
          FLImage.asset(Res.roomSearch, width: 14.pt, height: 14.pt),
        ],
      ),
    );
  }
}

class _RoomPageMyRoomWidget extends StatelessWidget {
  final RoomInfoModel? info;
  final VoidCallback? onTap;

  const _RoomPageMyRoomWidget({this.info, this.onTap});

  @override
  Widget build(BuildContext context) {
    final hasRoom = info != null;
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 26.pt,
            height: 26.pt,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: hasRoom
                      ? [
                          const Color(0xFF4591FE),
                          const Color(0xFFB139E9),
                          const Color(0xFFDD4EAA),
                          const Color(0xFFFF847C),
                        ]
                      : [
                          const Color(0xFFFF5A00),
                          const Color(0xFFFFC352),
                        ],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight),
              borderRadius: BorderRadius.circular(13.pt),
            ),
          ),
          FLImage.asset(Res.roomIconMyroomHomeBg, width: 16.pt, height: 16.pt),
          Padding(
            padding: EdgeInsets.only(top: 2.pt),
            child: hasRoom
                ? FLImage.asset(Res.roomIconMyRoomHome, width: 8.pt, fit: BoxFit.cover)
                : FLImage.asset(
                    Res.roomIconCreateRoomAdd,
                    width: 8.pt,
                    fit: BoxFit.cover,
                  ),
          ),
        ],
      ),
    );
  }
}
