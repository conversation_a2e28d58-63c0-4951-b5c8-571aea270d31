import 'package:biz/biz.dart';
import 'package:biz/biz/main/event.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/operation_statistics.g.dart';
import 'package:service/common/statistics/room_home_statistics.g.dart';
import 'package:service/modules/ad/const/const.dart';
import 'package:service/modules/ad/model/banner_resp.dart';
import 'package:service/modules/live/api/live_api.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_party_tab_list.dart';
import 'package:service/modules/ranking/model/rank_top_show_model.dart';

class RoomPartyRecommendController extends AbsGetController with GetStatusCtlMix {
  static const String adBannerList = 'adBannerList';

  final TickerProvider? tickerProvider;

  RoomPartyRecommendController(this.tickerProvider);

  RxList<TabListItem> tabList = RxList.empty();

  TabController? tabController;

  RxList<BannerItem> adList = RxList.empty();

  Rxn<RankTopShowModel> rankModel = Rxn();

  bool get isAdListIsEmpty => adList.isEmpty;

  @override
  void stateInit() {
    super.stateInit();

    _initData();
    _addListener();
  }

  @override
  void stateDispose() {
    tabController?.dispose();
    super.stateDispose();
  }

  // ----------------------------------------- Private -----------------------------------------

  void _initData() {
    _loadAdList();
    _loadRanking();
    _loadTabList();
  }

  void _addListener() {
    listenRxEvent<MainPageName>(MainTabEvent.tabSwitched, (value) {
      if (value == MainPageName.live) {
        if (tabList.isEmpty) {
          _loadTabList();
        }
      }
    });
    listenRxEvent<int>(RoomListEvent.recommendListRefresh, (value) {
      _loadAdList();
      _loadRanking();
    });
  }

  void _loadTabList() async {
    final resp = await liveApi.getRoomListTab();
    if (resp.isSuccess && resp.data?.tabList.isNotEmpty == true) {
      tabList.clear();
      final list = resp.data!.tabList;
      _createTabController(list);
      tabList.addAll(list);
    }
    if (viewStatus != GetStatusView.content) viewStatus = GetStatusView.content;
  }

  void _createTabController(List<TabListItem> tabs) {
    final item = tabs.firstOrNull;

    RoomHomeStatistics.reportRoomLiveListClick(content: item?.key);

    tabController?.dispose();
    tabController = TabController(length: tabs.length, vsync: tickerProvider!, initialIndex: 0);
    tabController?.addListener(() {
      if (tabController?.indexIsChanging ?? true) return;

      TabListItem item = tabs[tabController!.index];
      RoomHomeStatistics.reportRoomLiveListClick(content: item.key);
    });
  }

  void _loadAdList() async {
    final list = await adService.getBannerList(type: BannerType.roomList);

    bool same = true;
    if (list.length == adList.length) {
      if (list.isNotEmpty == true) {
        for (int i = 0; i < list.length; i++) {
          if (list[i] != adList[i]) {
            same = false;
          }
        }
      }
    } else {
      same = false;
    }

    if (!same) {
      adList.clear();
      adList.addAll(list);

      for (final e in adList) {
        OperationStatistics.reportBannerMomentImp(
          content: '${e.id}',
          from: StatisticPageFrom.tabLiveRoomList,
          activityName: e.extraInfo.activityId,
        );
      }
    }
    if (viewStatus != GetStatusView.content) viewStatus = GetStatusView.content;
  }

  void _loadRanking() async {
    final rsp = await rankingService.rankTopShow();
    rankModel.value = rsp.data;

    if (viewStatus != GetStatusView.content) viewStatus = GetStatusView.content;
  }

// ----------------------------------------- Public -----------------------------------------
}
