import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room_party/recommend/room_party_recommend_controller.dart';
import 'package:biz/biz/live_room/page/room_party/room_list_page/room_party_page.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_party_ad_item.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_party_rank_widget.dart';
import 'package:biz/common/widgets/vertical_carouse_widget.dart';
import 'package:biz/global/widgets/styled_tab_indicator.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/family_statistics.g.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/keep_wrapper.dart';

class RoomPartyRecommendPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _RoomPartyRecommendPageState();
  }
}

class _RoomPartyRecommendPageState extends State<RoomPartyRecommendPage>
    with GetStateBuilderMixin<RoomPartyRecommendPage, RoomPartyRecommendController>, SingleTickerProviderStateMixin {
  final ScrollController _sController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _sController.dispose();

    super.dispose();
  }

  @override
  RoomPartyRecommendController initCtl() {
    return RoomPartyRecommendController(this);
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return ExtendedNestedScrollView(
      controller: _sController,
      onlyOneScrollInBody: true,
      headerSliverBuilder: (context, boxIsScrolled) {
        return [_bodyContentHeader(context)];
      },
      body: Column(
        children: [
          Obx(_tabBar),
          Expanded(child: _tabBarView()),
        ],
      ), //_listWidget(),
    );
  }

  SliverOverlapAbsorber _bodyContentHeader(BuildContext context) {
    return SliverOverlapAbsorber(
      handle: ExtendedNestedScrollView.sliverOverlapAbsorberHandleFor(context),
      sliver: SliverToBoxAdapter(
        child: _topWidget(),
      ),
    );
  }

  Widget _tabBar() {
    if (getCtl.tabList.isEmpty) return SizedBox.shrink();

    List<Widget> tabList = [];
    for (var element in getCtl.tabList) {
      tabList.add(
        Tab(
          child: Text(element.name),
        ),
      );
    }

    return Container(
      height: 30.pt,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 10.pt),
      child: TabBar(
        isScrollable: true,
        controller: getCtl.tabController,
        labelStyle: TextStyle(fontSize: 15.sp, fontWeight: FontWeightExt.medium, color: R.color.color20),
        unselectedLabelStyle: TextStyle(fontSize: 13.sp, fontWeight: FontWeightExt.medium, color: R.color.colorB2),
        labelPadding: EdgeInsets.symmetric(horizontal: 7.pt),
        indicatorSize: TabBarIndicatorSize.label,
        indicator: StyledTabIndicator(margin: 0, width: 15.pt, height: 2.pt, fillColor: R.color.primaryColor),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: tabList,
      ),
    );
  }

  Widget _tabBarView() {
    if (getCtl.tabList.isEmpty) return SizedBox.shrink();
    final tabs = getCtl.tabList.mapNotNull((e) => KeepWrapper(child: RoomPartyLisPage(tabModel: e))).toList();

    return TabBarView(controller: getCtl.tabController, children: tabs);
  }

  Widget _topWidget() {
    return Column(
      children: [Obx(_bannerWidget), Obx(_rankWidget), 20.hSpace],
    );
  }

  Widget _bannerWidget() {
    if (getCtl.isAdListIsEmpty) return const SizedBox.shrink();

    final bannerHeight = (1.w - 20.pt) * 105 / 356;
    return Container(
      height: bannerHeight,
      padding: EdgeInsets.symmetric(horizontal: 10.pt),
      margin: EdgeInsets.only(top: 5.pt, bottom: 10.pt),
      child: RoomPartyAdItem(controller: getCtl, itemHeight: bannerHeight),
    );
  }

  Widget _rankWidget() {
    final rankItem = getCtl.rankModel.value;
    if (rankItem == null) return const SizedBox.shrink();

    final width = (1.w - 20.pt - 8.pt) / 2;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.pt),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          VerticalCarouselWidget(
            size: Size(width, 65.pt),
            children: [
              RoomPartyRankWidget(
                bg: Res.roomBgRankGiftSend,
                width: width,
                title: LocaleStrings.instance.giftSent,
                titleColor: const Color(0xFF9624D1),
                items: rankItem.contribute ?? [],
                onTap: () {
                  routerUtil.push(R_RANK, params: {P_RANK_TYPE: 'contribute'});

                  RoomStatistics.reportRankingEntranceClick(type: 'Gift send');
                },
              ),
              RoomPartyRankWidget(
                bg: Res.roomBgRankGiftReceived,
                width: width,
                title: LocaleStrings.instance.giftsReceived,
                titleColor: const Color(0xFF2438D1),
                items: rankItem.charm ?? [],
                onTap: () {
                  routerUtil.push(R_RANK, params: {P_RANK_TYPE: 'charm'});

                  RoomStatistics.reportRankingEntranceClick(type: 'Gift received');
                },
              ),
              RoomPartyRankWidget(
                isIntimacy: true,
                bg: Res.roomBgRankIntimacy,
                width: width,
                title: LocaleStrings.instance.intimacy,
                titleColor: const Color(0xFFD7364D),
                items: rankItem.intimacyModels,
                onTap: () {
                  routerUtil.push(R_RANK, params: {P_RANK_TYPE: 'intimacy'});

                  RoomStatistics.reportRankingEntranceClick(type: 'intimacy');
                },
              ),
              RoomPartyRankWidget(
                bg: Res.roomBgRankGiftGallery,
                width: width,
                title: LocaleStrings.instance.giftGallery,
                titleColor: const Color(0xFFD55B02),
                items: rankItem.gallery ?? [],
                onTap: () {
                  routerUtil.push(R_RANK, params: {P_RANK_TYPE: 'gift_gallery'});

                  RoomStatistics.reportRankingEntranceClick(type: 'Gift gallery');
                },
              ),
              RoomPartyRankWidget(
                bg: Res.roomBgRankFamily,
                width: width,
                title: LocaleStrings.instance.familyCapital,
                titleColor: const Color(0xFFC72222),
                items: rankItem.family ?? [],
                onTap: () {
                  routerUtil.push(R_RANK, params: {P_RANK_TYPE: 'family'});

                  RoomStatistics.reportRankingEntranceClick(type: 'family');
                },
              ),
            ],
          ),
          RoomPartyRankWidget(
            bg: Res.roomBgFamilyEntrance,
            width: width,
            title: LocaleStrings.instance.familyCapital,
            titleColor: const Color(0xFFD12424),
            items: [],
            showRanking: false,
            onTap: () {
              routerUtil.push(R_FAMILY_HOME, params: {P_STATISTIC_FROM: StatisticPageFrom.roomList});

              FamilyStatistics.reportFamilyEntranceClick();
            },
          ),
        ],
      ),
    );
  }
}
