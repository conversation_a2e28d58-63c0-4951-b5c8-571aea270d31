import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_level_widget.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_tag_widget.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';

import '../../../../family/widgets/family_level_widget.dart';

/// 首页列表项目的单个View
class RoomPartyListItem extends StatefulWidget {
  final RoomPartyListItemEntity item;
  final int index;
  final EdgeInsetsGeometry? padding;
  final VoidCallback onTap;

  const RoomPartyListItem({
    Key? key,
    required this.item,
    required this.index,
    required this.onTap,
    this.padding,
  }) : super(key: key);

  @override
  State<RoomPartyListItem> createState() => _RoomPartyListItemState();
}

class _RoomPartyListItemState extends State<RoomPartyListItem> {
  @override
  void initState() {
    super.initState();
    _reportRoomExposure();
  }

  void _reportRoomExposure() {
    RoomStatistics.reportEventRoomExposure(
      roomId: widget.item.rid,
      uid: accountService.currentUid(),
      ownerId: widget.item.roomOwner,
      refer: StatisticPageFrom.liveRoomList,
      mode: widget.item.roomMode,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: _buildContent(widget.item),
    );
  }

  Widget _buildContent(RoomPartyListItemEntity item) {
    final child = Row(
      children: [
        Stack(
          children: [
            FLImage.netImg(item.roomCover ?? '', height: 80.pt, width: 80.pt),
            Visibility(
              visible: item.enterPermission == 1,
              child: Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(1.pt),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(2.pt), color: Colors.black.withOpacity(0.78)),
                  child: FLImage.asset(Res.roomSettingsIcAccessPermissonLock, width: 12.pt, fit: BoxFit.cover),
                ),
              ),
            )
          ],
        ),
        Expanded(
            child: Padding(
          padding: EdgeInsets.fromLTRB(14.pt, 7.pt, 12.pt, 5.pt),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildRoomName(),
                    7.hSpace,
                    Row(
                      children: [
                        3.wSpace,
                        _hot(),
                        RoomLevelWidget(level: item.level ?? 0),
                        RoomTagWidget(
                            title: item.roomTag ?? '', color: item.roomTagColor?.toColor ?? R.color.primaryColor),
                      ],
                    ),
                    Visibility(
                      visible: (item.familyLevel ?? 0) > 0,
                      child: Padding(
                        padding: EdgeInsets.only(top: 7.pt),
                        child: FamilyLevelWidget(level: item.familyLevel ?? 0),
                      ),
                    )
                  ],
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildRoomType(),
                  12.hSpace,
                  OnlineNumWidget(num: item.onlineCnt ?? 0),
                ],
              ),
            ],
          ),
        ))
      ],
    );
    return OfficialRoomContainerWidget(
      child: child,
      isOfficial: item.isOfficial ?? false,
      officialLogo: item.officialLogo,
      padding: widget.padding,
    );
  }

  /// 房间类型
  Widget _buildRoomType() {
    final icon = widget.item.modeIcon ?? Res.roomIconItemChat;
    return FLImage.asset(icon, height: 25.pt, fit: BoxFit.cover);
  }

  /// 房间名称
  Widget _buildRoomName() {
    final roomNameWidget = ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 185.pt),
      child: Text(
        widget.item.roomName ?? '',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 15.pt,
          fontWeight: FontWeightExt.heavy,
          color: widget.item.isOfficial == true ? Colors.white : Colors.black,
          height: 1,
        ),
      ),
    );
    if (widget.item.isOfficial == true) {
      return ShaderMask(
        shaderCallback: (bounds) {
          return LinearGradient(colors: [Color(0xFF378BFF), Color(0xFF7D4EF9), Color(0xFFCE42C3), Color(0xFFFF847E)])
              .createShader(Offset.zero & bounds.size, textDirection: Directionality.of(context));
        },
        child: roomNameWidget,
      );
    } else {
      return roomNameWidget;
    }
  }

  Widget _hot() {
    final val = widget.item.recoVal ?? 0;
    if (val <= 0) return const SizedBox.shrink();

    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        Container(
          height: 12.pt,
          padding: EdgeInsets.only(left: 15.pt, right: 7.pt),
          margin: EdgeInsets.only(right: 9.pt),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6.pt),
            border: Border.all(color: const Color(0xFFFF8D12), width: 0.5.pt),
          ),
          child: Text(
            NumFormatUtils.numFormat(val),
            style: TextStyle(fontSize: 10.pt, fontWeight: FontWeightExt.medium, color: Color(0xFFFF8C12), height: 1.3),
          ),
        ),
        Positioned(
          left: -10.pt,
          child: Container(
            width: 30.pt,
            height: 30.pt,
            child: SvgaWidget(
              fit: BoxFit.contain,
              svgaInfo: SvgaInfo(assetUrl: Res.roomSvgaRoomHotRocket, repeat: true),
              checkUrlOnly: true,
            ),
          ),
        )
      ],
    );
  }
}

class OfficialRoomContainerWidget extends StatelessWidget {
  final EdgeInsetsGeometry? padding;

  OfficialRoomContainerWidget(
      {super.key, required this.child, required this.isOfficial, this.officialLogo, this.padding});

  final Widget child;
  final bool isOfficial;
  String? officialLogo;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6.pt),
      padding: padding ?? EdgeInsets.symmetric(horizontal: 4.pt, vertical: 4.pt),
      child: Stack(children: [
        Container(
            height: 80.pt,
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
                color: Colors.white,
                image:
                    isOfficial ? DecorationImage(image: AssetImage(Res.roomBgOfficialRoomBg), fit: BoxFit.cover) : null,
                borderRadius: BorderRadius.circular(10.pt),
                boxShadow: [
                  BoxShadow(color: Colors.black.withOpacity(0.4), blurRadius: 0, offset: Offset.zero),
                ]),
            child: child),
        if (isOfficial)
          Transform.translate(
            offset: Offset(-4.pt, -3.pt),
            child: Stack(
              children: [
                CachedNetworkImage(imageUrl: officialLogo ?? "", width: 49.pt, height: 43.pt),
              ],
            ),
          )
      ]),
    );
  }
}

class OnlineNumWidget extends StatelessWidget {
  const OnlineNumWidget({super.key, required this.num});

  final int num;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        FLImage.asset(Res.roomIcOnline, height: 10.pt),
        3.5.wSpace,
        Text(
          "$num",
          style: TextStyle(color: Color(0xFF909090), fontSize: 11.pt, fontWeight: FontWeightExt.medium),
        )
      ],
    );
  }
}
