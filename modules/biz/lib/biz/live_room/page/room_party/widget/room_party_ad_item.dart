import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room_party/recommend/room_party_recommend_controller.dart';
import 'package:biz/global/widgets/carousel_banner/carousel_banner.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';

import '../room_list_page/room_party_list_controller.dart';
import 'room_party_banner_item.dart';

class RoomPartyAdItem extends StatefulWidget {
  final RoomPartyRecommendController controller;
  final double itemHeight;

  const RoomPartyAdItem({
    super.key,
    required this.controller,
    this.itemHeight = 0,
  });

  @override
  State<RoomPartyAdItem> createState() => _RoomPartyAdItemState();
}

class _RoomPartyAdItemState extends State<RoomPartyAdItem> {
  @override
  Widget build(BuildContext context) {
    // final rank = widget.controller.rankItem;
    final count = widget.controller.adList.length;
    return GetBuilder(
        init: widget.controller,
        global: false,
        autoRemove: false,
        id: RoomPartyRecommendController.adBannerList,
        builder: (controller) {
          if (widget.controller.adList.isEmpty) return const SizedBox.shrink();
          return Container(
            height: widget.itemHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.pt),
            ),
            child: CarouselBannerWidget(
              loop: widget.controller.adList.length > 1,
              autoplay: widget.controller.adList.length > 1,
              itemBuilder: (BuildContext context, int index) {
                // if (rank != null && index == count - 1) {
                //   return RoomRankBannerItemWidget(
                //     itemModel: rank,
                //     from: StatisticPageFrom.tabLiveRoomList,
                //     itemHeight: widget.itemHeight,
                //   );
                // }
                final itemModel = widget.controller.adList[index];
                return RoomPartyBannerItem(
                  itemModel: itemModel,
                  itemHeight: widget.itemHeight,
                  from: StatisticPageFrom.tabLiveRoomList,
                );
              },
              itemCount: count,
              pagination: count <= 1
                  ? null
                  : SwiperPagination(
                      builder: DotSwiperPaginationBuilder(
                          size: 6.pt,
                          activeSize: 6.pt,
                          activeColor: Colors.white,
                          color: Colors.white.withOpacity(0.4),
                          space: 3.pt),
                      margin: EdgeInsets.only(bottom: 5.pt)),
            ),
          );
        });
  }
}
