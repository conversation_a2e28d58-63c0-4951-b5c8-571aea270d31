import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/modules/ad/model/ad_banner_model.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';

class RoomPartyController extends AbsGetController {
  Rxn<RoomInfoModel> info = Rxn();

  List<AdBannerItemModel> adList = [];

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
    refreshData();
  }

  void _initRx() {
    ///注册用户信息更新通知
    // listenRxEvent<List<String>>(UserStatusEvent.updateUsers, (uids) {
    //   final currentUid = accountService.currentUid();
    //   if (currentUid != null && uids.contains(currentUid)) {
    //    updateMyInfo();
    //   }
    // });

    listenRxEvent<RoomInfoModel>(RoomEvent.createRoom, (value) {
      _fetchMyRoomInfo();
    });

    listenRxEvent<String>(RoomEvent.closeRoom, (value) {
      // 关闭房间后有缓存 不请求接口 直接改状态
      info.value?.status = 'close';
    });

    listenRxEvent<int>(RoomListEvent.subListRefresh, (value) {
      _fetchMyRoomInfo();
    });
  }

  void refreshData() async {
    _fetchMyRoomInfo();
  }

  @override
  void onLocaleChange(String localeName) {
    super.onLocaleChange(localeName);
    refreshData();
  }

  bool hasOpenRoom() {
    return info.value?.isOpen == true;
    //return roomPartyFriend?.myRoomInfo != null &&roomPartyFriend?.myRoomInfo?.rid.isNotEmpty == true;
  }

  // void updateMyInfo() {
  //   if (roomPartyFriend?.myRoomInfo == null ||
  //       accountService.currentUid() == null) {
  //     return;
  //   }
  //   UserInfo? userInfo =
  //       userService.getUserInfoInCache(accountService.currentUid()!);
  //   if (userInfo == null) return;
  //   bool needRefresh = false;
  //   if (userInfo.avatarCode != null) {
  //     roomPartyFriend!.myRoomInfo!.avatarCode = userInfo.avatarCode!;
  //     needRefresh = true;
  //   }
  //   if (userInfo.avatar != null) {
  //     roomPartyFriend!.myRoomInfo!.avatar = userInfo.avatar!;
  //     needRefresh = true;
  //   }
  //   if (needRefresh) {
  //     update([idFriendList]);
  //   }
  // }


  Future<void> _fetchMyRoomInfo() async {
    final rsp = await roomService.getMyRoomInfo();
    info.value = rsp.data;

    // if (_friendListLoading) return;
    // _friendListLoading = true;
    // FlatHttpResponse<RoomPartyFriend> resp = await liveApi.partyFriendList();
    // _friendListLoading = false;
    // _friendListLoaded = true;
    // if (resp.isSuccess) {
    //   roomPartyFriend = resp.data;
    // } else {
    //   toast(resp.msg ?? LocaleStrings.instance.defaultError);
    // }
    // update([idFriendList]);
  }

  void joinRoom({required String from}) async {
    final model = info.value;

    final roomId = model?.roomId ?? '';
    if (model?.isClose ?? false) {
      await roomService.createOrResetRoom(
        mode: model?.mode ?? '',
        roomName: model?.name,
        roomId: roomId,
        create: true,
      );
    }

    LiveRoomHandler.joinRoom(roomId, from: from);
  }
}
