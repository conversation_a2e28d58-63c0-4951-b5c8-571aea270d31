import 'package:biz/biz.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/modules/live/room/model/room_party_tab_list.dart';

import '../widget/room_party_list_item.dart';
import 'room_party_list_controller.dart';

class RoomPartyLisPage extends StatefulWidget {
  final TabListItem? tabModel;
  final Widget? emptyWidget;

  RoomPartyLisPage({
    Key? key,
    this.tabModel,
    this.emptyWidget,
  }) : super(key: key);

  @override
  State<RoomPartyLisPage> createState() => _RoomPartyLisPageState();
}

class _RoomPartyLisPageState extends State<RoomPartyLisPage> {
  final RoomPartyListController _controller = RoomPartyListController();

  @override
  void initState() {
    super.initState();
    _controller.tabModel = widget.tabModel;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
        init: _controller,
        global: false,
        autoRemove: true,
        builder: (_) {
          return page();
        },
      ),
    );
  }

  Widget page() {
    final list = _controller.list;
    final length = list.length;

    if (_controller.isLoading()) {
      return Container(
        width: 1.w,
        height: 0.5.h,
        child: Center(
          child: GlobalWidgets.loadingIndicator(),
        ),
      );
    } else if (_controller.hasError()) {
      return GlobalWidgets.netFailedWidget(
        btnAction: () {
          _controller.loadList(refresh: true);
        },
        padding: EdgeInsets.only(top: 80.pt),
      );
    } else if (length <= 0) {
      return widget.emptyWidget ?? EmptyWidget(
        scrollable: false,
        logoAsset: Res.emptyEmptyUserPost,
        logoSize: Size(200.pt, 200.pt),
        btnSize: Size(295.pt, 44.pt),
        detail: LocaleStrings.instance.partyListEmptyTip,
        btnAction: () => _controller.loadList(refresh: true),
      );
    }

    return SmartRefresher(
      controller: _controller.refreshController,
      onRefresh: _controller.refreshData,
      onLoading: _controller.loadMoreData,
      enablePullDown: true,
      enablePullUp: list.isNotEmpty,
      primary: true,
      physics: ClampingScrollPhysics(),
      header: GlobalWidgets.refreshHeader(),
      footer: GlobalWidgets.refreshFoot(loadingText: ''),
      child: ListView.separated(
        shrinkWrap: true,
        itemBuilder: (ctx, idx) {
          final item = list[idx];
          return RoomPartyListItem(
            item: item,
            index: idx,
            onTap: () {
              final roomId = item.rid ?? '';
              if (roomId.isEmpty) return;
              _controller.joinRoom(roomId, from: StatisticPageFrom.tabLiveRoomList);

              RoomStatistics.reportRoomListClick(content: item.roomTag, from: widget.tabModel?.key);
            },
          );
        },
        separatorBuilder: (ctx, idx) {
          return 9.hSpace;
        },
        itemCount: length,
      ),
    );
  }
}
