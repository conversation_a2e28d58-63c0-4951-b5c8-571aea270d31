import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/main/event.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/room_home_statistics.g.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_party_list_model.dart';
import 'package:service/modules/live/room/model/room_party_tab_list.dart';

class RoomPartyListController extends AbsGetController with GetStatusCtlMix {
  bool _isLoading = true;
  String? _errMsg;
  RefreshController refreshController = RefreshController();
  TabListItem? tabModel;
  List<RoomPartyListItemEntity> list = [];


  DateTime? _requestTime;

  int _page = 1;

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
    loadList(refresh: true);

    RoomHomeStatistics.reportRoomLiveListImp(content: tabModel?.key);
  }

  void _initRx() {
    listenRxEvent<MainPageName>(MainTabEvent.tabSwitched, (value) {
      if (value == MainPageName.live) {
        _tryRequestList();
      }
    });
  }

  void refreshData() {
    loadList(refresh: true);
  }

  void loadMoreData() {
    loadList(refresh: false);
  }

  @override
  void onPageShow() {
    super.onPageShow();
    _tryRequestList();
  }

  @override
  void onLocaleChange(String localeName) {
    super.onLocaleChange(localeName);
    loadList(refresh: true);
  }

  bool isListEmpty() => list.isEmpty;

  bool hasError() => _errMsg != null;

  bool isLoading() => _isLoading;

  bool _requesting = false;

  void _tryRequestList() {
    if (_checkReqTimeInterval()) {
      loadList(refresh: true);
    }
  }

  bool _checkReqTimeInterval() {
    if (_requestTime == null) {
      return true;
    } else {
      return DateTime.now().difference(_requestTime!) >= Duration(minutes: 5);
    }
  }

  Future<void> loadList({bool refresh = false}) async {
    if (_requesting) return;

    if (isListEmpty()) {
      _isLoading = true;
      update();
    } else if (refresh) {
      WidgetUtils.post((duration) {
        refreshController.requestRefresh(needCallback: false);
      });
    }

    if (refresh) {
      _page = 1;
      rxUtil.send(RoomListEvent.subListRefresh, 1);
      rxUtil.send(RoomListEvent.recommendListRefresh, 1);
    }
    _requestTime = DateTime.now();

    _requesting = true;
    final resp = await roomService.roomListByPage(tab: tabModel?.key ?? '', page: _page);
    _requesting = false;

    _isLoading = false;
    if (resp.isSuccess) {
      _errMsg = null;
      _page += 1;

      if (refresh) {
        list.clear();
        refreshController.refreshCompleted(resetFooterState: true);
      } else {
        refreshController.loadComplete();
      }
      final addList = resp.data?.list ?? [];
      list.addAll(addList);
    } else {
      _errMsg = resp.msg;
      if (refresh) {
        refreshController.refreshFailed();
      } else {
        refreshController.loadFailed();
      }
    }
    update();
  }

  void joinRoom(String rid, {required String from}) {
    LiveRoomHandler.joinRoom(rid, from: from);
  }
}
