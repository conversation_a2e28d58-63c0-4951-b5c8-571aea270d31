import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/throttled_tap_handler.dart';
import 'package:service/modules/live/plugins/red_pack/red_pack_models.dart';
import 'package:service/service.dart';

import 'red_pack_controller.dart';

/// 抢红包的Widget
class RedPackRushWidget extends StatelessWidget {
  final RoomRedPackController controller;

  RedPackRushWidget({super.key, required this.controller});

  final double _width = 296.pt;
  final double _height = 340.ph;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: _width,
      height: _height,
      child: _buildBody(context),
    );
  }

  _buildBody(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        SizedBox(width: _width, height: _height),
        _buildBg(),
        _buildTitle(),
        Positioned.fill(child: _buildContent()),
        _buildClose(context),
      ],
    );
  }

  Widget _buildBg() {
    return Obx(() {
      var isFans = controller.mainController.fansController.isRoomFans.value;
      String bg = isFans ? Res.roomRedPackRedPackBg : Res.roomRedPackRedPackJoinFansBg;
      return FLImage.asset(bg, width: _width, height: _height);
    });
  }

  _buildTitle() {
    return PositionedDirectional(
      top: 16.ph,
      child: Text(
        LocaleStrings.instance.redPackTitle,
        style: TextStyle(
          color: Colors.white,
          fontSize: 21.sp,
          fontWeight: FontWeightExt.black,
          fontStyle: FontStyle.italic,
          shadows: [
            Shadow(
              offset: Offset(0, 2.pt),
              blurRadius: 4.pt,
              color: Color(0xB3A7010F),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsetsDirectional.only(top: 85.ph),
      child: _RedPackRushWidget(controller),
    );
  }

  _buildClose(BuildContext context) {
    double tapPadding = 10.pt;
    return PositionedDirectional(
      top: -tapPadding,
      end: -tapPadding,
      child: GestureDetector(
        onTap: () {
          controller.onTapClose(context);
        },
        child: Padding(
          padding: EdgeInsets.all(tapPadding),
          child: FLImage.asset(Res.roomRedPackRedPackClose, width: 28.pt, height: 28.pt),
        ),
      ),
    );
  }
}

class _RedPackRushWidget extends StatelessWidget {
  final RoomRedPackController controller;

  const _RedPackRushWidget(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        GestureDetector(
          onTap: () {
            controller.onTapRedPack(context);
          },
          child: Stack(
            alignment: Alignment.center,
            children: [
              _rushBtnBg(),
              _rushText(),
              _rushCountDown(),
            ],
          ),
        ),
        Spacer(),
        Padding(
          padding: EdgeInsetsDirectional.only(start: 21.pt, end: 21.pt, bottom: 21.pt),
          child: _des(),
        ),
      ],
    );
  }

  _rushBtnBg() {
    return Obx(() {
      var stage = controller.stage.value;
      String icBtn;
      if (stage == RedPackStage.available) {
        icBtn = Res.roomRedPackRedPackGet;
      } else {
        icBtn = Res.roomRedPackRedPackBtnWait;
      }
      return FLImage.asset(
        icBtn,
        height: 149.ph,
      );
    });
  }

  _rushText() {
    return Obx(() {
      var stage = controller.stage.value;
      Color textColor;
      if (stage == RedPackStage.available) {
        textColor = Color(0xFFF7412D);
      } else {
        textColor = Color(0x801F1F1F);
      }
      return Container(
        constraints: BoxConstraints(maxWidth: 102.pt),
        child: FittedBox(
          child: Text(
            LocaleStrings.instance.rush,
            style: TextStyle(
              color: textColor,
              fontSize: 36.pt,
              fontWeight: FontWeightExt.black,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    });
  }

  _rushCountDown() {
    return PositionedDirectional(
      bottom: 30.ph,
      child: Obx(() {
        var stage = controller.stage.value;
        if (stage == RedPackStage.available) {
          return SizedBox.shrink();
        }
        var sec = controller.getTimer(controller.curRedPackId)?.waitingCountDownSec.value ?? 0;
        return RichText(
          text: TextSpan(children: [
            TextSpan(
              text: "$sec",
              style: TextStyle(
                color: Color(0xFF858585),
                fontSize: 18.pt,
                fontWeight: FontWeightExt.medium,
              ),
            ),
            TextSpan(
              text: "s",
              style: TextStyle(
                color: Color(0xFF858585),
                fontSize: 15.pt,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ]),
        );
      }),
    );
  }

  Widget _des() {
    return Obx(() {
      String content = controller.showContent.value;
      return Text(
        content,
        style: TextStyle(
          color: Colors.white,
          fontSize: 13.pt,
          fontWeight: FontWeightExt.heavy,
          height: 1.38,
          shadows: [
            Shadow(
              offset: Offset(0, 1.pt),
              blurRadius: 2.pt,
              color: Color(0xB3A7010F),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      );
    });
  }
}

class _RedPackJoinFansWidget extends StatelessWidget {
  final RoomRedPackController controller;

  const _RedPackJoinFansWidget(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        90.hSpace,
        Expanded(child: _buildCenterText()),
        _buildBottomBtn(),
        35.hSpace,
      ],
    );
  }

  Widget _buildCenterText() {
    return Obx(() {
      String text = controller.noFansShowContent.value;
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 21.pt),
        child: SingleChildScrollView(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.pt,
              fontWeight: FontWeightExt.black,
              height: 1.38,
              shadows: [
                Shadow(
                  offset: Offset(0, 1.pt),
                  blurRadius: 2.pt,
                  color: Color(0xB3A7010F),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    });
  }

  _buildBottomBtn() {
    RxInt? secRx = controller.getTimer(controller.curRedPackId)?.waitingCountDownSec;
    if (secRx != null) {
      return Obx(() {
        var sec = secRx.value;
        return _buildJoinBtn(sec);
      });
    }
    return _buildJoinBtn(0);
  }

  Widget _buildJoinBtn(int sec) {
    return ThrottledTapHandler(
      onTap: controller.onTapJoinFans,
      isDark: true,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(Res.roomRedPackRedPackJoinBtnBg, width: 221.pt, height: 65.pt),
          Container(
            width: 202.pt,
            child: RichText(
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(children: [
                TextSpan(
                  text: LocaleStrings.instance.followAndJoinTheFanClub,
                  style: TextStyle(
                    color: Color(0xFFE35700),
                    fontSize: 18.pt,
                    fontWeight: FontWeightExt.black,
                    fontStyle: FontStyle.italic,
                    height: 1.38,
                  ),
                ),
                if (sec > 0) ...[
                  TextSpan(
                    text: " （$sec",
                    style: TextStyle(
                      color: Color(0x8FE35700),
                      fontSize: 16.pt,
                      fontWeight: FontWeightExt.medium,
                    ),
                  ),
                  TextSpan(
                    text: "s",
                    style: TextStyle(
                      color: Color(0x8FE35700),
                      fontSize: 13.pt,
                      fontWeight: FontWeightExt.medium,
                    ),
                  ),
                  TextSpan(
                    text: "）",
                    style: TextStyle(
                      color: Color(0x8FE35700),
                      fontSize: 16.pt,
                      fontWeight: FontWeightExt.medium,
                    ),
                  ),
                ]
              ]),
            ),
          )
        ],
      ),
    );
  }
}
