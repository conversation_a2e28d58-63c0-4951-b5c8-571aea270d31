import 'dart:async';
import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/widgets/room_h5_dialog.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/live/plugins/red_pack/red_pack_api.dart';
import 'package:service/modules/live/plugins/red_pack/red_pack_models.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/utils/loading.dart';

import 'red_pack_dialog.dart';
import 'red_pack_result_list_dialog.dart';
import 'red_pack_timer.dart';

const String _tag = 'redPack';

const redPackUINone = 0;
const redPackUIRush = 1;
const redPackUIResult = 2;

class RoomRedPackController extends AbsGetController with AbsRoomModuleController {
  @override
  RoomMainController mainController;

  RoomRedPackController(this.mainController);

  Rxn<RedPackStage> stage = Rxn(RedPackStage.none);

  /// UI 类型 ，[redPackUIRush]为抢红包，[redPackUIResult]为结果
  RxInt uiType = 0.obs;
  final Map<int, RedPackTimer> _redPackTimerMap = {};

  /// 中间大红包数据ID
  int curRedPackId = -1;
  RxString showContent = ''.obs;
  RxString noFansShowContent = ''.obs;

  /// 小红包的数据
  RxnInt smallRedPackId = RxnInt();
  RxBool smallRedPackCanGet = false.obs;
  RxInt redPackCount = RxInt(0);
  RxBool hasRedPack = RxBool(false);

  final List<PbRoomRedPackNotice> _redPackQueue = [];

  RxDouble curProgress = 0.0.obs;
  RxString progressText = ''.obs;

  RxInt rewardSum = 0.obs;

  bool _isGrabRedPack = false;

  @override
  void stateInit() {
    super.stateInit();
    _addRxLis();
    _loadData();
  }

  @override
  void stateDispose() {
    curRedPackId = -1;
    _clearTimers();
    super.stateDispose();
  }

  void _clearTimers() {
    _redPackTimerMap.forEach((key, value) {
      value.cancelTimer();
    });
    _redPackTimerMap.clear();
  }

  void _addRxLis() {
    listenRxEvent<PbRoomRedPackNotice>(RedPackEvent.redPackNotice, _handRedPackNotice);
    listenRxEvent<RoomRedPackScheduleInfo?>(RedPackEvent.redPackProgress, _handleRedPackProgress);
  }

  void _loadData() async {
    var resp = await redPackApi.redPackList(roomId: '${roomService.getCurrentRoomId()}');
    LogExt.largeD(_tag, () => '$resp');
    if (resp.isSuccess) {
      _redPackQueue.clear();
      if (resp.data?.packList.isNotEmpty == true) {
        _redPackQueue.addAll(resp.data!.packList);
      }

      /// 创建定时器
      _clearTimers();
      for (var element in _redPackQueue) {
        if (element.stageEnum == RedPackStage.generating) {
          var timer = RedPackTimer(id: element.id, total: element.produceStage.waitTime);
          _redPackTimerMap[element.id] = timer;
          timer.startTimer();
        }
      }

      _updateSmallRedPack();
      _handleRedPackProgress(resp.data?.scheduleInfo);
    } else {
      toast(resp.msg ?? LocaleStrings.instance.defaultError);
    }
  }

  void onTapRedPack(BuildContext context) async {
    if (stage == RedPackStage.available) {
      if (_isGrabRedPack) {
        return;
      }
      showLoading();
      _isGrabRedPack = true;
      var delay = Random().nextInt(1501) + 500;
      await Future.delayed(Duration(milliseconds: delay));
      int redPackId = curRedPackId;
      var resp = await redPackApi.grabRedPack(roomId: '${roomService.getCurrentRoomId()}', packId: '$redPackId');
      hideLoading();
      _isGrabRedPack = false;
      bool needRemove = false;
      if (resp.isSuccess || resp.code == 1070) {
        // 1070未超时，但是红包已经抢完
        rewardSum.value = resp.data?.amount ?? 0;
        uiType.value = redPackUIResult;
        needRemove = true;
      } else if (resp.code == 1069) {
        // 红包过期
        toast(resp.msg ?? LocaleStrings.instance.defaultError, isDark: true);
        needRemove = true;
        curRedPackId = -1;
        routerUtil.pop(context: context);
      } else {
        toast(resp.msg ?? LocaleStrings.instance.defaultError, isDark: true);
      }
      if (needRemove) {
        _removeRedPackQueue(redPackId);
        _updateSmallRedPack();
      }
    }
  }

  void _removeRedPackQueue(int? id) {
    if (id != null) {
      _redPackQueue.removeWhere((element) => element.id == id);
      _redPackTimerMap.removeWhere((key, value) => key == id);
    }
  }

  void onTapClose(BuildContext context) {
    routerUtil.pop(context: context);
    curRedPackId = -1;
    showContent.value = '';
    noFansShowContent.value = '';
  }

  RedPackTimer? getTimer(int? id) {
    if (id == null) {
      return null;
    }
    return _redPackTimerMap[id];
  }

  void _handRedPackNotice(PbRoomRedPackNotice value) {
    LogExt.largeD(_tag, () => 'handRedPackNotice stage=${value.stageEnum},curRedPackId=$curRedPackId,value=$value');

    bool canPop = value.stageEnum == RedPackStage.generating || value.stageEnum == RedPackStage.available;
    bool popDialog = false;
    bool isExist = false;
    int insertIndex = 0;
    for (var i = _redPackQueue.length - 1; i > -1; i--) {
      var item = _redPackQueue[i];
      if (item.id == value.id) {
        if (value.stageEnum == RedPackStage.resultAnnounced || value.stageEnum == RedPackStage.none) {
          _redPackQueue.removeAt(i);
          _redPackTimerMap.remove(item.id);
        } else {
          _redPackQueue[i] = value;
          insertIndex = i;
        }
        isExist = true;
      }
    }
    if (!isExist && canPop) {
      _redPackQueue.add(value);
      insertIndex = _redPackQueue.length - 1;
    }

    /// 创建定时器
    if (value.stageEnum == RedPackStage.generating) {
      var timer =
          _redPackTimerMap.putIfAbsent(value.id, () => RedPackTimer(id: value.id, total: value.produceStage.waitTime));
      timer.startTimer();
    }

    /// 发送公屏
    if (value.stageEnum == RedPackStage.generating) {
      _sendRedPackTipMsg(value);
    } else if (value.stageEnum == RedPackStage.resultAnnounced) {
      _sendRedPackResultChatMsg(value);
    }

    int index = -1;
    // 没有中间红包
    if (curRedPackId < 0) {
      if (canPop) {
        index = insertIndex;
        popDialog = true;
      }
    } else {
      // 有中间红包
      index = _redPackQueue.indexWhere((element) => element.id == curRedPackId);
    }
    if (index > -1) {
      var centerRedPack = _redPackQueue[index];
      _updateCenterRedPack(notice: centerRedPack, popDialog: popDialog);
    }

    _updateSmallRedPack();
  }

  void _updateCenterRedPack({
    required PbRoomRedPackNotice notice,
    bool popDialog = false,
  }) {
    RedPackStage stage = notice.stageEnum;
    this.stage.value = stage;
    switch (stage) {
      case RedPackStage.generating:
        showContent.value = notice.produceStage.showContent;
        noFansShowContent.value = notice.noFansShowContent;
        if (popDialog) {
          curRedPackId = notice.id;
          uiType.value = redPackUIRush;
          showRedPackDialog(controller: this);
        }
        break;
      case RedPackStage.available:
        showContent.value = notice.startStage.showContent;
        noFansShowContent.value = notice.noFansShowContent;
        if (popDialog) {
          curRedPackId = notice.id;
          uiType.value = redPackUIRush;
          showRedPackDialog(controller: this);
        }
        break;
      default:
        break;
    }
  }

  void _updateSmallRedPack({bool? showToast}) {
    PbRoomRedPackNotice? notice;
    if (_redPackQueue.isNotEmpty) {
      notice = _redPackQueue.first;
    }
    smallRedPackId.value = notice?.id;
    removeSmallValue() {
      smallRedPackCanGet.value = false;
      _removeRedPackQueue(notice?.id);
      if (showToast == true) {
        toast(LocaleStrings.instance.redPackExpired);
      }
    }

    if (notice == null) {
      removeSmallValue();
    } else {
      RedPackStage stage = notice.stageEnum;
      switch (stage) {
        case RedPackStage.none:
          removeSmallValue();
          break;
        case RedPackStage.generating:
          smallRedPackCanGet.value = false;
          break;
        case RedPackStage.available:
          smallRedPackCanGet.value = true;
          break;
        case RedPackStage.resultAnnounced:
          removeSmallValue();
          break;
      }
    }
    redPackCount.value = _redPackQueue.length;
    hasRedPack.value = redPackCount.value > 0;
  }

  void onTapSmallRedPack() {
    var notice = _redPackQueue.first;
    LogExt.largeD(_tag, () => 'onTapSmallRedPack $notice');
    _updateCenterRedPack(popDialog: true, notice: notice);
    _updateSmallRedPack(showToast: true);
  }

  void _handleRedPackProgress(RoomRedPackScheduleInfo? value) {
    if (value == null || value.nextGearsVal == 0) {
      return;
    }
    progressText.value = '${NumFormatUtils.numFormat(value.curVal)}/${  NumFormatUtils.numFormat(value.nextGearsVal)}';
    curProgress.value = max(value.curVal / value.nextGearsVal, 1);
  }

  void onTapBanner() {
    var url = roomService.isRoomOwner()
        ? roomHostRewardUrl(roomId: roomService.getCurrentRoomId())
        : roomRedPackUrl(roomId: roomService.getCurrentRoomId());
    showRoomH5Dialog(url);
  }

  /// 点击结果页面的确认按钮
  void onTapResultClose() {
    curRedPackId = -1;
    routerUtil.pop();
  }

  void onCloseResult() {
    uiType.value = redPackUINone;
  }

  void onTapJoinFans() async {
    showLoading();
    bool goJoinFans = false;
    if (roomService.getCurrentRoom()?.hasFollow != true) {
      goJoinFans = await mainController.followOwner(context: getContext()!, showToast: false);
    } else {
      goJoinFans = true;
    }
    if (goJoinFans) {
      await mainController.fansController.joinFans();
    }
    hideLoading();
  }

  void _sendRedPackTipMsg(PbRoomRedPackNotice value) {
    roomChatService.sendRedPackTipMsg(notice: value);
  }

  void _sendRedPackResultChatMsg(PbRoomRedPackNotice value) {
    roomChatService.sendRedPackResultMsg(
      notice: value,
      onTapBtn: () {
        showRedPackResultListDialog(packResult: value.resultStage);
      },
    );
  }
}
