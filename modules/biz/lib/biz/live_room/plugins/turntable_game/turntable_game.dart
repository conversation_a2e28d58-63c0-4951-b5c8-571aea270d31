// ignore_for_file: must_be_immutable

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/balance.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/game_history.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/game_rank.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/game_result.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/game_rule.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/select_diamonds_widget.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/widget/turntable.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/common/widgets/icon_button.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/common/statistics/fruitparty_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/utils/value_parsers.dart';

import '../../component/gift/float_screen/gift_float_screen_widget.dart';
import 'bloc/turntable_game_bloc.dart';
import 'turntable_game_const.dart';

void showTurntableGame(BuildContext? context) {
  DialogScheduler.instance().schedule(
    () async {
      var retVal = await showBottomSheet((_) => TurntableGame(),
          enableDrag: false, routeSettings: dialogRouteSettings(R_LIVE_ROOM_TURNTABLE_GAME));

      final roomInfo = roomService.getCurrentRoom();
      FruitpartyStatistics.reportFruitpartyMainImp(roomId: roomInfo?.roomId, type: roomInfo?.mode);
      return retVal;
    },
    context: context,
    single: true,
    path: R_LIVE_ROOM_TURNTABLE_GAME,
  );
}

class TurntableGame extends StatelessWidget {
  TurntableGame({super.key});

  TurntableGameBloc _bloc = TurntableGameBloc();

  final _rule = const GameRule();

  final _history = const GameHistory();

  final _ranking = const GameRanking();

  final _result = const TurntableGameResult();

  @override
  Widget build(BuildContext context) {
    _bloc = TurntableGameBloc();
    double aspectRatio = 375 / 661;
    return WillPopScope(
      onWillPop: () async {
        return await _bloc.onWillPop();
      },
      child: Stack(
        children: [
          AspectRatio(
            aspectRatio: aspectRatio,
            child: Container(
              width: 1.w,
              decoration: panelDecoration,
              child: LifecycleBlocBuilder(
                bloc: _bloc,
                builder: (BuildContext context, state) {
                  return _body(context);
                },
              ),
            ),
          ),
          Positioned.fill(child: _history),
          Positioned.fill(child: _rule),
          Positioned.fill(child: _ranking),
          Positioned.fill(child: _result),
          // Positioned.fill(top: 0.pt, child: GiftFloatScreenWidget(type: GiftFloatType.room)),
        ],
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 12.pt),
        _title(context),
        SizedBox(height: 6.pt),
        BalanceWidget(todayWin: _bloc.state.gameData?.user.todayWin ?? 0),
        14.hSpace,
        Expanded(child: _mainPanel(context))
      ],
    );
  }

  Widget _title(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 88.pt,
          child: Row(
            children: [
              8.wSpace,
              FIconButton(
                icon: Res.commonBack,
                iconSize: Size(24.pt, 24.pt),
                iconColor: Colors.white,
                onTap: () => routerUtil.pop(context: context),
              )
            ],
          ),
        ),
        Expanded(
          child: Center(
            child: RichText(
              text: TextSpan(
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.white,
                    fontWeight: FontWeightExt.medium,
                  ),
                  children: [
                    TextSpan(text: LocaleStrings.instance.round),
                    TextSpan(
                        text: "${_bloc.state.gameData?.gameInfo.round ?? 0}",
                        style: const TextStyle(color: Color(0xFFFFA800))),
                    TextSpan(text: LocaleStrings.instance.ofToday),
                  ]),
            ),
          ),
        ),
        SizedBox(
          width: 88.pt,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              FIconButton(
                icon: Res.roomTurntableGameIconHistory,
                iconSize: Size(24.pt, 24.pt),
                onTap: () => _bloc.add(ShowHistoryEvent()),
              ),
              FIconButton(
                icon: Res.roomTurntableGameIconQa,
                iconSize: Size(24.pt, 24.pt),
                onTap: () => _bloc.add(ShowHelpEvent()),
              ),
              8.wSpace,
            ],
          ),
        ),
      ],
    );
  }

  Widget _mainPanel(BuildContext context) {
    if (_bloc.state.gameData?.gameInfo.id.isInvalid() ?? true) {
      return GlobalWidgets.pageLoading();
    }
    double height = 531 / 375 * 1.w;
    final MediaQueryData data = MediaQuery.of(context);
    final EdgeInsets padding = data.padding;

    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        SizedBox(
          width: 1.w,
          height: height,
        ),
        PositionedDirectional(
          bottom: 0,
          child: FLImage.asset(
            Res.roomTurntableGameBgGameDestop,
            width: 1.w,
          ),
        ),
        _rankBtn(),
        Positioned(
          top: 46.pt,
          child: Turntable(onBet: () => _bloc.hasTapBet = true),
        ),
        Positioned(
          top: 343.pt,
          left: 23.pt,
          right: 23.pt,
          child: Center(
            child: Text(
              LocaleStrings.instance.gameSelectTips,
              style: TextStyle(color: const Color(0x73FFFFFF), fontSize: 13.sp, fontWeight: FontWeightExt.medium),
            ),
          ),
        ),
        Positioned(
          top: 361.pt,
          child: SelectDiamondsWidget(
            gameInfo: _bloc.state.gameData!.gameInfo,
          ),
        ),
        Positioned(bottom: 32.pt, child: _gameRecentResult()),
        if (padding.bottom > 0)
          PositionedDirectional(
            bottom: 0,
            width: 1.w,
            child: SizedBox(
              height: padding.bottom,
              width: 1.w,
              child: FLImage.asset(
                Res.roomTurntableGameBgGameDestopBottom,
                height: padding.bottom,
                width: 1.w,
                fit: BoxFit.fill,
              ),
            ),
          )
      ],
    );
  }

  Widget _rankBtn() {
    return PositionedDirectional(
      top: -7.pt,
      end: 48.pt,
      child: GestureDetector(
        onTap: () => _bloc.add(ShowRankingEvent()),
        child: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                Res.roomTurntableGameBtnRanking,
              ),
            ),
          ),
          width: 109.pt,
          height: 40.pt,
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FLImage.asset(
                  Res.roomTurntableGameIcRankStar,
                  width: 18.pt,
                ),
                3.wSpace,
                Text(
                  LocaleStrings.instance.ranking,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 13.sp,
                    fontWeight: FontWeightExt.heavy,
                    height: 1.38,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _gameRecentResult() {
    return Container(
      width: 322.pt,
      height: 48.pt,
      decoration: BoxDecoration(
        color: const Color(0x3D000000),
        borderRadius: BorderRadius.circular(16.pt),
        border: Border.all(color: const Color(0xFF7139B5), width: 1.pt),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 12.pt,
          ),
          Text(
            LocaleStrings.instance.results,
            style: TextStyle(color: Colors.white, fontWeight: FontWeightExt.heavy, fontSize: 16.sp),
          ),
          Expanded(
              child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _bloc.state.gameData?.gameInfo.recentResult.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              final result = toInt(_bloc.state.gameData?.gameInfo.recentResult[index]);
              return _gameResultItem(index, result);
            },
          ))
        ],
      ),
    );
  }

  Widget _gameResultItem(int index, int id) {
    final gambleItems = _bloc.state.gameData?.gameInfo.gambleItems;
    if (id <= 0 || (gambleItems?.isEmpty ?? true)) {
      return const SizedBox();
    }
    final item = gambleItems?.safeFirstWhere((element) => element.id == id);
    if (item == null) {
      return const SizedBox();
    }
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.pt),
          child: GlobalWidgets.cachedNetImage(
            imageUrl: item.img,
            width: 32.pt,
            height: 32.pt,
          ),
        ),
        if (index == 0)
          Positioned(
            bottom: 2.pt,
            child: Container(
              padding: EdgeInsets.only(left: 4.pt, right: 4.pt),
              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(6.pt)),
              child: Text(
                LocaleStrings.instance.newText.toLowerCase(),
                style: TextStyle(
                  color: const Color(0xFF8000FF),
                  fontSize: 11.sp,
                  fontWeight: FontWeightExt.heavy,
                  height: 1.2,
                ),
              ),
            ),
          )
      ],
    );
  }
}
