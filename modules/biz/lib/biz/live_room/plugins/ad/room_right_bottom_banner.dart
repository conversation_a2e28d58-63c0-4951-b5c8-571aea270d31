import 'dart:async';

import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/room_banner_item_widget.dart';
import 'package:biz/global/widgets/carousel_banner/carousel_banner.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/operation_statistics.g.dart';
import 'package:service/modules/ad/model/banner_resp.dart';
import 'package:service/service.dart';

import 'room_ad_controller.dart';

class RoomRightBottomBanner extends StatefulWidget {
  final RoomAdController controller;

  const RoomRightBottomBanner({super.key, required this.controller});

  @override
  State<RoomRightBottomBanner> createState() => _RoomRightBottomBannerState();
}

class _RoomRightBottomBannerState extends State<RoomRightBottomBanner> {
  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
      controller: widget.controller,
      child: GetBuilder(
        init: widget.controller,
        global: false,
        autoRemove: false,
        builder: (_) {
          if (widget.controller.adList.isEmpty) return SizedBox.shrink();

          return Container(
            width: roomTheme.theme.roomAdBannerWidth,
            height: roomTheme.theme.roomAdBannerHeight,
            padding: EdgeInsets.only(bottom: widget.controller.adList.length <= 1 ? 10.pt : 0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.pt),
              child: CarouselBannerWidget(
                loop: widget.controller.adList.length > 1,
                autoplay: widget.controller.adList.length > 1,
                outer: true,
                itemBuilder: (BuildContext context, int index) {
                  final itemModel = widget.controller.adList[index];
                  return RoomBannerItemWidget(itemModel: itemModel, from: StatisticPageFrom.liveRoom);
                },
                itemCount: widget.controller.adList.length,
                pagination: widget.controller.adList.length <= 1
                    ? null
                    : SwiperPagination(
                        builder: DotSwiperPaginationBuilder(
                            size: 6.pt,
                            activeSize: 6.pt,
                            activeColor: Colors.white,
                            color: Colors.white.withOpacity(0.4),
                            space: 2.pt),
                        margin: EdgeInsets.only(bottom: 0),
                      ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class CocosGameScoreBannerItem extends StatefulWidget {
  final VoidCallback? onTap;

  CocosGameScoreBannerItem({
    super.key,
    this.onTap,
  });

  @override
  State<CocosGameScoreBannerItem> createState() => _CocosGameScoreBannerItemState();
}

class _CocosGameScoreBannerItemState extends State<CocosGameScoreBannerItem> with SingleTickerProviderStateMixin {
  late final AnimationController _lottieController;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _lottieController = AnimationController(
      vsync: this,
    );
    _timer = Timer.periodic(Duration(seconds: 10), (Timer timer) {
      _play();
    });
  }

  void _cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  void _play() {
    _lottieController.reset();
    _lottieController.forward();
  }

  @override
  void dispose() {
    _lottieController.dispose();
    _cancelTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: _game(),
    );
  }

  Widget _game() {
    return Lottie.asset(
      Assets.liveCocosGameBanner,
      controller: _lottieController,
      fit: BoxFit.cover,
      onLoaded: (composition) {
        _lottieController.duration = composition.duration;
        _lottieController.reset();
        _lottieController.forward();
      },
      repeat: true,
    );
  }
}
