import 'dart:convert';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/share/room_share_controller.dart';
import 'package:biz/biz/share/share_widget.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';

import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/biz/share/contacts/model/share_item_model.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:biz/biz/share/share_other_app_widget.dart';
/// 房间分享弹窗
void showRoomShareDialog() async {
  if (!roomService.isInLive()) return;
  var roomInfo = roomService.getCurrentRoom();
  var userInfo = await roomService.getCurrentUserInfo();

  /// 拼接房间deeplink
  var deepLink =
      "${DeepLink.deepLinkProto}${R_LIVE_ROME_JOIN.replaceFirst("/", "")}"
      "${"?$P_ID=${roomInfo?.roomId}"}${"&$P_STATISTIC_FROM=share"}";
  String content = "";

  String link =
      DeepLink.getWebDeepLinkUrl(baseUrl: roomShareUrl, rid: roomInfo?.roomId);

  /// 如果房间有密码，并且自己是房主，分享的房间链接自动带有密码
  // if (roomInfo?.hasPw == 1 && (userInfo?.isOwner ?? false)) {
  //   deepLink = "$deepLink${"&$P_ROOM_PWD=${roomInfo?.password}"}";
  //   content = LocaleStrings.instance.liveRoomShareContentAndPwd(
  //       roomInfo?.name ?? "", roomInfo?.roomId ?? "", roomInfo?.password ?? "");
  // } else {
    content = LocaleStrings.instance
        .liveRoomShareContent(roomInfo?.name ?? "", roomInfo?.roomId ?? "");
  // }

  final roomInfoJson = roomInfo?.toJson() ?? "";
  if (roomInfoJson is Map<String, dynamic>) {
    roomInfoJson.remove('room_mode_list');
  }
  // 分享窗
  showShareDialog(
      context: FLRouter.routeObserver.getLastContext(),
      showFriends: false,
      includeGroupChat: true,
      title: LocaleStrings.instance.inviteYourFriends,
      shareWidgetType: ShareWidgetType.withContact,
      margin: EdgeInsets.symmetric(horizontal: 15.pt),
      routeSettings: dialogRouteSettings(R_LIVE_ROOM_SHARE),
      shareResultCallback: (type, success, target) async {
        if (success && userInfo != null) {
          roomChatService.sendShareRoomMsg(userInfo: userInfo);
        }
      },
      shareStatisticCallback: (type, share, user) {
        RoomStatistics.reportRoomShareClick(
            toGender: user?.sexStr,
            toUid: user?.uid,
            act: share?.name,
            mode: roomService.getCurrentRoom()?.mode,
            roomId: roomService.getCurrentRoomId());
      },
      template: LiveRoomShare(
          content: content,
          imContent: json.encode(roomInfoJson),
          icon: roomInfo?.roomOwnerAvatar,
          link: link,
          deepLink: deepLink));

  // dialog.showBottomSheet((_) =>
  //     RoomShareWidget(),
  //     routeSettings: RouteSettings(name: R_LIVE_ROOM_SHARE)
  // );
}

class RoomShareWidget extends StatefulWidget {
  @override
  State<RoomShareWidget> createState() => _RoomShareWidgetState();

}

class _RoomShareWidgetState extends State<RoomShareWidget> {

  final RoomShareController _controller = RoomShareController();

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
            init: _controller,
            autoRemove: false,
            global: false,
            builder: (controller){
              return _page();
            }
        )
    );
  }

  Widget _page() {
    return Container(
      height: 283.pt,
      width: double.infinity,
      decoration: BoxDecoration(
          color: R.color.textColor1,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(16.pt),
          )
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _topView(),
          _recentlyContactWidget(),
          _controller.hasSelectedContact() ? _shareContactButton() : _shareList()
        ],
      ),
    );
  }

  Widget _topView() {
    return Padding(
      padding: EdgeInsets.only(left: 16.pt, right: 16.pt, top: 16.pt),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {

            },
            child: FLImage.asset(Res.chatArrowDown, width: 26.pt, height: 26.pt),
          ),
          GlobalWidgets.spacingHorizontal(10.pt),
          Expanded(
            child: Text(
              LocaleStrings.instance.shareToOthers,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeightExt.heavy,
              ),
            ),
          ),
          GlobalWidgets.spacingHorizontal(10.pt),
          Opacity(
              opacity: 0,
            child: SizedBox(
              width: 26.pt,
              height: 26.pt,
            ),
          )
        ],
      ),
    );
  }

  Widget _recentlyContactWidget() {

    if (_controller.contactList.isEmpty) return SizedBox.shrink();

    return Container(
      height: 125.pt,
      width: double.infinity,
      padding: EdgeInsets.only(top: 29.pt, bottom: 32.pt),
      child: GridView.builder(
        padding: EdgeInsets.symmetric(horizontal: 3.pt),
        scrollDirection: Axis.horizontal,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 1,
            mainAxisSpacing: 9.pt,
        ),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          final entity = _controller.contactList[index];
          return _contactItem(entity);
        },
        itemCount: _controller.contactList.length,
      ),
    );
  }

  Widget _contactItem(ShareFriendItemModel itemModel) {
    return Column(
      children: [
        _avatar(itemModel),
        GlobalWidgets.spacingVertical(5.pt),
        _userName(itemModel)
      ]
    );
  }

  Widget _avatar(ShareFriendItemModel itemModel) {
    return GestureDetector(
      onTap: (){
        _controller.onClickSelectedContact(itemModel: itemModel);
      },
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          UserAvatar(
            url: itemModel.userInfo?.avatar ?? '',
            avatarCode: itemModel.userInfo?.avatarCode,
            size: 42.pt,
          ),

          Positioned(
              right: 0,
              bottom: 0,
              child: Visibility(
                  visible: _controller.hasSelectedContactState(itemModel: itemModel),
                  child: FLImage.asset(Res.roomShareCheck, width: 14.pt, height: 14.pt)
              )
          )
        ],
      ),
    );
  }

  Widget _userName(ShareFriendItemModel itemModel) {
    return Text(
      itemModel.userInfo?.nickname ?? '',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          color: Colors.white.withOpacity(0.5),
          fontSize: 12.sp,
          fontWeight: FontWeightExt.book,
      ),
    );
  }

  Widget _shareContactButton() {
    return Container(
      height: 50.pt,
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.pt),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14.pt),
          gradient: R.color.btnPrimaryLinearGradient
      ),
      child: Text(
        LocaleStrings.instance.share,
        style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeightExt.heavy
        ),
      ),
    );
  }

  Widget _shareList() {
    return Container(
      height: 64.pt,
      child: GridView.builder(
        scrollDirection: Axis.horizontal,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          mainAxisSpacing: 9.pt,
        ),
        itemBuilder: (context, index) {
          final entity = _controller.shareApps[index];
          return _shareItem(entity);
        },
        itemCount: _controller.shareApps.length,
      ),
    );
  }

  Widget _shareItem(OtherAppInfo itemModel) {
    return Column(
        children: [
          _shareAppIcon(itemModel),
          GlobalWidgets.spacingVertical(5.pt),
          _shareAppName(itemModel)
        ]
    );
  }

  Widget _shareAppIcon(OtherAppInfo itemModel) {
    return FLImage.asset(itemModel.icon, width: 42.pt, height: 42.pt);
  }

  Widget _shareAppName(OtherAppInfo itemModel) {
    return Text(
      itemModel.name,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Colors.white.withOpacity(0.5),
        fontSize: 12.sp,
        fontWeight: FontWeightExt.book,
      ),
    );
  }

}