import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/global/widgets/input_bar/bottom_panel/bottom_panel.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:get/get.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/widget/soft_keyboard_wrapper.dart';

import 'room_bottom_controller.dart';

Future showInputBarDialog({
  required BuildContext context,
  required RoomMainController controller,
}) async {
  await showBottomSheet(
    (context) {
      return InputBar(controller: controller);
    },
    context: context,
    enableDrag: false,
    barrierColor: Colors.transparent,
    barrierDismissible: true,
    routeSettings: RouteSettings(name: R_LIVE_BOTTOM_INPUT_DIALOG),
  );
  controller.bottomController.inputPanelVisible = false;
}

class InputBar extends StatefulWidget {
  final RoomMainController controller;

  const InputBar({Key? key, required this.controller}) : super(key: key);

  @override
  State<InputBar> createState() => _InputBarState();
}

class _InputBarState extends State<InputBar> {
  late RoomBottomController bottomController;

  final String _tag = 'roomInputBar';

  double? _keyBoardHeight;

  /// 第一帧的底部距离
  double firstBottomInset = 0;

  @override
  void initState() {
    super.initState();
    bottomController = widget.controller.bottomController;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SoftKeyboardVisibilityBuilder(
        builder: (BuildContext context, bool isKeyboardVisible) {
          _keyBoardHeight = max(
              _keyBoardHeight ?? 0, MediaQuery.of(context).viewInsets.bottom);
          _onKeyBoardChangeShow(context);
          return _buildBody(context);
        },
      ),
    );
  }

  Column _buildBody(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _input(),
        _emojiDrawer(),
      ],
    );
  }

  Widget _input() {
    return Container(
      color: Colors.white,
      height: 54.pt,
      padding: EdgeInsets.symmetric(horizontal: 13.pt, vertical: 10.pt),
      child: Row(
        textDirection: TextDirection.ltr,
        children: [
          Expanded(
            child: Stack(
              alignment: Alignment.centerRight,
              children: [_textField(), _emojiBtn()],
            ),
          ),
          SizedBox(
            width: 11.pt,
          ),
          _sendBtn(),
        ],
      ),
    );
  }

  Widget _emojiDrawer() {
    return Obx(() {
      bool visible = bottomController.emojiPanelVisible.value;
      Widget? child;
      if (visible) {
        child = InputBottomPanel(
            showSticker: false,
            showBottomBar: true,
            deleteCallback: () {
              if (bottomController.editingController.text.isNotEmpty) {
                var text = bottomController.editingController.text.characters
                    .skipLast(1)
                    .string;
                bottomController.editingController.text = text;
              }
            },
            canSend: true,
            emojiSelectCallBack: (value) {
              final text = '${bottomController.editingController.text}$value';
              bottomController.editingController.text = text;
            },
            stickerSend: (sticker, from) {
              bottomController.sendSticker(sticker: sticker, from: from);
            });
      }
      return Container(
        constraints: BoxConstraints(minHeight: _keyBoardHeight!),
        color: Colors.white,
        child: child,
      );
    });
  }

  Widget _textField() {
    return Obx(() {
      final prefixText = getPrefixText();
      return ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 150.pt),
        child: TextField(
          controller: bottomController.editingController,
          maxLines: null,
          onEditingComplete: () => bottomController.onSendMsg(),
          autofocus: true,
          focusNode: bottomController.editingFocusNode,
          keyboardType: TextInputType.multiline,
          decoration: InputDecoration(
              filled: true,
              fillColor: Color(0x1AA3AFC2),
              hintText: LocaleStrings.instance.roomTypeTips,
              hintStyle: TextStyle(color: Color(0xffBAC4CD)),
              border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(22.pt)),
              contentPadding: EdgeInsets.fromLTRB(
                  14.pt,
                  0.pt,
                  50.pt,
                  0.pt),
              prefix: prefixText?.isNotEmpty == true
                  ? Container(
                constraints: BoxConstraints(maxWidth: 150.pt),
                child: Text(
                  prefixText ?? "",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: R.color.primaryLightColor,
                  ),
                ),
              )
                  : null),
          style: TextStyle(
            fontSize: 15.sp,
            color: R.color.textColor1,
          ),
          cursorColor: R.color.primaryColor,
          cursorHeight: 18.pt,
          cursorWidth: 2.pt,
          onTap: () => bottomController.emojiPanelVisible.value = false,
          onChanged: (_) => bottomController.onInputChange(),
        ),
      );
    });
  }

  String? getPrefixText() {
    return bottomController.atUser.value != null
        ? "@${bottomController.atUser.value!.nickname}"
        : null;
  }

  Widget _emojiBtn() {
    // 使用textField的suffix来放按钮，点击时会触发键盘弹出，所以这里自己摆放
    return Padding(
      padding: EdgeInsets.only(right: 17.pt),
      child: OpacityTapWidget(
        onTap: () => bottomController.onInputTypeChangeEvent(),
        child: FLImage.asset(
          Res.commonEmoji,
          width: 24.pt,
          height: 24.pt,
        ),
      ),
    );
  }

  Widget _sendBtn() {
    return ScaleTapWidget(
      child: FLImage.asset(
        Res.chatChatBarSend,
        width: 33.pt,
        height: 33.pt,
      ),
      onTap: () => bottomController.onSendMsg(),
    );
  }

  /// 处理软键盘用户自己点击关闭，输入面板也更随一起关闭
  void _onKeyBoardChangeShow(BuildContext context) {
    final bottomInset = softKeyboardHeight(context);
    if (firstBottomInset == 0) {
      firstBottomInset = bottomInset;
      return;
    }
    bool visible;

    /// firstBottomInset小于bottomInset 说明键盘是展开的，否则是收起的
    if (firstBottomInset < bottomInset) {
      visible = true;
    } else if (firstBottomInset > bottomInset && bottomInset == 0) {
      visible = false;
    } else {
      return;
    }
    firstBottomInset = 0;

    /// 其他页面的键盘变化不监听
    var routers = FLRouter.routeObserver.getRoutes();
    if (routers.last.settings.name != R_LIVE_BOTTOM_INPUT_DIALOG) return;

    if (bottomController.inputPanelVisible != visible) {
      Log.i(_tag, 'visible = $visible');
      if ((!visible && bottomController.emojiPanelVisible.value) || visible) {
        return;
      }
      if (!visible) {
        bottomController.hideInputBar();
      }
    }
  }
}
