import 'dart:ui';

import 'package:biz/biz/live_room/component/bottom_bar/dialog/room_more_dialog.dart';
import 'package:biz/biz/live_room/component/bottom_bar/room_bottom_bar_clipper.dart';
import 'package:biz/biz/live_room/component/fans/dialog/room_fans_meta_list_dialog.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/utils/time_util.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/throttled_tap_handler.dart';
import 'package:service/modules/live/mic_seat/abs_mic_seat_service.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room_fans/model/room_fans_model.dart';
import 'package:service/service.dart';

import '../../page/room/room_main_controller.dart';
import 'fruit_machine_entry_widget.dart';
import 'room_bottom_controller.dart';

class RoomBottomBarWidget extends StatefulWidget {
  final RoomMainController mainController;

  const RoomBottomBarWidget({Key? key, required this.mainController}) : super(key: key);

  @override
  State<RoomBottomBarWidget> createState() => _RoomBottomBarWidgetState();
}

class _RoomBottomBarWidgetState extends State<RoomBottomBarWidget> {
  late RoomBottomController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.mainController.bottomController;
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
      controller: _controller,
      child: Stack(
        children: [
          _background(),
          _actionButtonRow(),
        ],
      ),
    );
  }

  Widget _background() {
    double safeBottom = MediaQuery.paddingOf(context).bottom;
    double screenWidth = MediaQuery.sizeOf(context).width;
    return Obx(() {
      MicStatus? micStatus = widget.mainController.micStatus.value;
      if (micStatus == MicStatus.normal || micStatus == MicStatus.request) {
        return ClipPath(
          clipper: RoomBottomBarClipper(
              circleRect: Rect.fromLTWH(9.pt, 0, 64.pt, 64.pt),
              overlayRect: Rect.fromLTWH(0, 8.pt, screenWidth, safeBottom + roomTheme.theme.roomBottomBarHeight)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              width: 1.w,
              height: safeBottom + roomTheme.theme.roomBottomBarHeight + 8.pt,
              color: const Color(0xFF030305).withOpacity(0.76),
            ),
          ),
        );
      }

      return Container(
        width: 1.w,
        height: safeBottom + roomTheme.theme.roomBottomBarHeight + 2.pt,
        child: widget.mainController.roomMode.value != RoomMode.LUDO_GAME
            ? ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Container(
                    color: Colors.black.withOpacity(0.7),
                  ),
                ),
              )
            : const SizedBox.shrink(),
      );
    });
  }

  Widget _actionButtonRow() {
    return Obx(() {
      MicStatus? micStatus = widget.mainController.micStatus.value;
      double topPadding = 16.pt;
      if (micStatus == MicStatus.normal || micStatus == MicStatus.request) {
        topPadding = 5.pt;
      }

      bool showFruitMachine = _controller.showFruitMachine.value;
      bool showBlindBox = _controller.showBlindBoxEnter.value;
      bool showGameCenter = _controller.showGameCenterEnter.value;

      // 游戏列表为空且不是管理员时 不显示更多按钮
      final showMore =  _controller.showMoreDialog.value;

      return Padding(
        padding: EdgeInsets.only(top: topPadding),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            15.wSpace,
            _buildMicBtn(),
            10.wSpace,
            Expanded(child: _buildInput()),
            if (showGameCenter)
              Padding(
                padding: EdgeInsetsDirectional.only(start: 8.pt),
                child: ThrottledTapHandler(
                  onTap: _controller.onClickGameCenter,
                  child: FLImage.asset(
                    Res.gameCenterIcRoomEntry,
                    width: 32.pt,
                    height: 32.pt,
                  ),
                ),
              ),
            if (showFruitMachine)
              Padding(
                padding: EdgeInsetsDirectional.only(start: 8.pt),
                child: SvgaEntryWidget(
                  onTap: _controller.onClickFruitMachine,
                  staticRes: Res.roomTurntableGameFruitMatchine,
                  svgaPath: Res.svgaFruitMachineEntryDiamond,
                  isFruitMachine: true,
                ),
              ),
            if (showBlindBox)
              Padding(
                padding: EdgeInsetsDirectional.only(start: 8.pt),
                child: SvgaEntryWidget(
                  onTap: _controller.onClickBlindBox,
                  staticRes: Res.roomBlindBox,
                ),
              ),
            8.wSpace,
            _giftButton(),
            Visibility(
              visible: showMore,
              child: Padding(
                padding: EdgeInsetsDirectional.only(start: 8.pt),
                child: ThrottledTapHandler(
                  onTap: () {
                    showRoomMoreDialog(widget.mainController, context: context);
                  },
                  child: FLImage.asset(
                    Res.roomIconBottomBarMore,
                    width: 32.pt,
                    height: 32.pt,
                  ),
                ),
              ),
            ),
            15.wSpace,
          ],
        ),
      );
    });
  }

  Widget _buildMicBtn() {
    return Obx(() {
      MicStatus? micStatus = widget.mainController.micStatus.value;
      if (micStatus == MicStatus.normal) return _onMicView();
      if (micStatus == MicStatus.request) return _onMicRequestView();

      String micIcon;
      if (micStatus == MicStatus.open) {
        micIcon = Res.roomBarMicNormal;
      } else {
        micIcon = Res.roomBarMicDisable;
      }
      return GestureDetector(
        onTap: _controller.onClickMicBtn,
        child: Container(
          width: 36.pt,
          height: 36.pt,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.12),
            borderRadius: BorderRadius.circular(18.pt),
          ),
          child: FLImage.asset(
            micIcon,
            width: 26.pt,
            height: 26.pt,
            fit: BoxFit.fill,
          ),
        ),
      );
    });
  }

  Widget _onMicView() {
    return GestureDetector(
      onTap: _controller.onClickMicBtn,
      child: FLImage.asset(Res.roomBarNoMic, width: 52.pt, height: 52.pt),
    );
  }

  Widget _onMicRequestView() {
    return GestureDetector(
      child: Container(
        width: 54.pt,
        height: 54.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.12),
          borderRadius: BorderRadius.circular(27.pt),
        ),
        child: FLImage.asset(Res.roomMicStatusRequestIcon, width: 39.pt, height: 39.pt),
      ),
      onTap: _controller.onClickCancelRequestMic,
    );
  }

  _buildInput() {
    return GestureDetector(
      onTap: _controller.showInputBar,
      child: Container(
        height: 34.pt,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: roomTheme.theme.bottomBtnBg,
          borderRadius: BorderRadius.circular(17.pt),
        ),
        child: Row(
          children: [
            _buildFansLevel(),
            Text(
              LocaleStrings.instance.roomTypeTips,
              style: TextStyle(color: roomTheme.theme.textColor, fontSize: 13.pt),
            ),
            Spacer(),
            _buildEmojiBtn(),
          ],
        ),
      ),
    );
  }

  Widget _buildFansLevel() {
    return Obx(() {
      bool isFans = widget.mainController.fansController.isRoomFans.value;
      var fansClubShow = roomService.fansService.fansClubShow.value;
      if (isFans || fansClubShow?.canShowFanLevel() != true || roomService.isRoomOwner()) {
        return SizedBox(width: 13.pt);
      }
      return Row(
        children: [
          GestureDetector(
            onTap: _showFansMetaList,
            child: Padding(
              padding: EdgeInsetsDirectional.only(start: 5.pt, end: 5.pt),
              child: UserFanLevelWidget(
                level: fansClubShow?.level ?? 0,
                imgWidth: 31.pt,
                imgHeight: 27.pt,
                fontSize: 15.pt,
              ),
            ),
          ),
          Container(
            width: 2.pt,
            height: 16.pt,
            color: Color(0x66858585),
          ),
          5.wSpace,
        ],
      );
    });
  }

  _buildEmojiBtn() {
    return GestureDetector(
      onTap: () => _controller.onClickEmoji(context: context),
      child: Container(
        width: 36.pt,
        height: 36.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
          Res.roomBarEmoji,
          width: 24.pt,
          height: 24.pt,
        ),
      ),
    );
  }

  Widget _giftButton() {
    return GestureDetector(
      onTap: _controller.onClickGiftButton,
      child: Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: [
          _buildGiftIcon(),
          // _buildGiftRedDot(),
          // _buildFreeGift(),
          _buildGiftNum(),
        ],
      ),
    );
  }

  Widget _buildGiftIcon() {
    return FLImage.asset(Res.roomIconBottomBarGift, width: 32.pt, height: 36.pt);
  }

  Widget _buildGiftRedDot() {
    return PositionedDirectional(
      top: 3.pt,
      end: 3.pt,
      child: Obx(() {
        bool? show = _controller.hasGiftRedDot.value;
        if (show != true) {
          return SizedBox.shrink();
        }
        return Container(
          width: 8.pt,
          height: 8.pt,
          decoration: BoxDecoration(
            color: R.color.warningColor,
            shape: BoxShape.circle,
            border: Border.all(color: Color(0xFF191A1B), width: 1.pt),
          ),
        );
      }),
    );
  }

  Widget _buildGiftNum() {
    return PositionedDirectional(
      top: -3.pt,
      start: 20.pt,
      child: Obx(() {
        int value = _controller.giftNum.value;
        if (value <= 0) {
          return SizedBox.shrink();
        }
        String valueStr = value.toString();
        if (value > 99) {
          valueStr = '99+';
        }
        Color borderColor =
            widget.mainController.roomMode == RoomMode.LUDO_GAME ? Colors.transparent : Color(0xFF161718);
        return Container(
          height: 18.pt,
          padding: EdgeInsets.symmetric(horizontal: 3.pt),
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [
              Color(0xFFBD8AFF),
              Color(0xFF6236F5),
            ]),
            borderRadius: BorderRadius.circular(10.pt),
            border: Border.all(color: borderColor, width: 2),
          ),
          child: Center(
            child: Text(
              valueStr,
              style: TextStyle(
                fontSize: 10.pt,
                color: Colors.white,
                fontWeight: FontWeightExt.heavy,
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildFreeGift() {
    return PositionedDirectional(
      bottom: -6.pt,
      child: Obx(() {
        bool hasGet = _controller.freeGiftHasGet.value;
        if (hasGet) return _buildFreeGiftGet();
        bool hasCountDown = _controller.freeGiftHasCountDownGift.value;
        int? countDown = _controller.freeGiftCountDown.value;
        if (hasCountDown && countDown != null) {
          return _buildFreeGiftCountDown(countDown);
        }
        return SizedBox.shrink();
      }),
    );
  }

  Widget _buildFreeGiftGet() {
    return Container(
      height: 14.pt,
      padding: EdgeInsetsDirectional.only(start: 6.pt, end: 6.pt, top: 2.pt),
      decoration: BoxDecoration(
        color: R.color.primaryColor,
        borderRadius: BorderRadius.circular(6.pt),
      ),
      child: Text(
        LocaleStrings.instance.get,
        style: TextStyle(
          color: roomTheme.theme.textColor,
          fontSize: 11.pt,
          fontWeight: FontWeightExt.heavy,
          height: 1.1,
        ),
      ),
    );
  }

  Widget _buildFreeGiftCountDown(int sec) {
    return Container(
      height: 14.pt,
      padding: EdgeInsetsDirectional.only(start: 6.pt, end: 6.pt, top: 2.pt),
      decoration: BoxDecoration(
        color: roomTheme.theme.panelBg,
        borderRadius: BorderRadius.circular(6.pt),
      ),
      child: Text(
        TimeUtil.secondToTime(sec),
        style: TextStyle(
          color: Color(0x99FFFFFF),
          fontSize: 11.pt,
          fontWeight: FontWeightExt.heavy,
          height: 1.1,
        ),
      ),
    );
  }

  void _showFansMetaList() {
    RoomFansMetaListDialog.showDialog();
  }
}
