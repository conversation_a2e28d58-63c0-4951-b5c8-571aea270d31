import 'package:biz/biz/sticker/stickers/stickers.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/live/chat/model/chat_sticker_msg_content.dart';
import 'package:service/modules/live/room/model/sticker_model.dart';
import 'package:service/modules/sticker/const/enums.dart';
import 'package:service/modules/sticker/model/sticker.dart';
import 'package:service/service.dart';

import '../mic_seat/manager/mic_sticker_display_manager.dart';

void showEmotionDialog({required BuildContext context, String? from}) {
  showBottomSheet((_) => _EmotionDialog(from: from),
      enableDrag: false,
      context: context,
      routeSettings: dialogRouteSettings(R_LIVE_ROOM_EMOTION));
}

class _EmotionDialog extends StatefulWidget {
  final String? from;

  const _EmotionDialog({Key? key, this.from}) : super(key: key);

  @override
  State<_EmotionDialog> createState() => _EmotionDialogState();
}

const _resDice1 = "res/room/sticker/sticker_dice_1.png";

const _resMora2 = "res/room/sticker/sticker_mora_2.png";

const _pumpMic = "res/room/sticker/sticker_pump_mic.png";

class _EmotionDialogState extends State<_EmotionDialog>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  final String _spKey = "EmotionDialogSpKey";

  final bool isInMic = false ; /*micSeatService.isInMic();*/

  /// 普调麦上表情
  static const String micStickerId = "9";

  /// vip麦上表情
  static const String vipMicStickerId = "10";

  late final _localStickers;

  @override
  void initState() {
    _localStickers = [
      StickerModel(
          resource: _resDice1,
          type: RoomStickerType.dice,
          onTap: () {
            roomChatService.sendLocalSticker(
                type: RoomStickerType.dice, from: widget.from);
          }),
      StickerModel(
          resource: _resMora2,
          type: RoomStickerType.moraSticker,
          onTap: () {
            roomChatService.sendLocalSticker(
                type: RoomStickerType.moraSticker, from: widget.from);
          }),
      StickerModel(
          resource: Res.roomStickerStickerCountdown,
          type: RoomStickerType.countdownSticker,
          onTap: () {
            roomChatService.sendLocalSticker(
                type: RoomStickerType.countdownSticker, from: widget.from);
          }),
      StickerModel(
          resource: _pumpMic,
          type: RoomStickerType.pumpMicSticker,
          onTap: () {
            roomChatService.sendLocalSticker(
                type: RoomStickerType.pumpMicSticker, from: widget.from);
          }),
    ];
    
    int? initialIndex;
    int length = 1;
    if (isInMic) {
      length = 3;
      initialIndex = globalSp.getInt(_spKey, defaultValue: 0);
    } else {
      _localStickers.removeAt(2);
    }

    _tabController = TabController(
        vsync: this, length: length, initialIndex: initialIndex ?? 0);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging == true) return;
      _updateIndex(_tabController.index);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Color(0xf5212121),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.pt),
              topRight: Radius.circular(15.pt))),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200.pt,
              child: TabBarView(controller: _tabController, children: [
                _dicePanel(),
                if (isInMic) _micStickerPanel(),
                if (isInMic) _vipMicPanel()
              ]),
            ),
            Container(
              color: Color(0x1aededed),
              height: 0.5.pt,
            ),
            TabBar(
                controller: _tabController,
                isScrollable: true,
                indicatorSize: TabBarIndicatorSize.label,
                labelPadding: EdgeInsets.zero,
                tabs: [
                  Container(
                    height: 38.pt,
                    width: 52.pt,
                    color: Color(0x14ffffff),
                    alignment: Alignment.center,
                    child: FLImage.asset(_resDice1, width: 23.pt),
                  ),
                  if (isInMic)
                    Container(
                      height: 38.pt,
                      width: 52.pt,
                      color: Color(0x14ffffff),
                      alignment: Alignment.center,
                      child: FLImage.asset(Res.roomIcEmotion, width: 23.pt),
                    ),
                  if (isInMic)
                    Container(
                      height: 38.pt,
                      width: 52.pt,
                      color: Color(0x14ffffff),
                      alignment: Alignment.center,
                      child: FLImage.asset(Res.roomIcVipEmotion, width: 23.pt),
                    ),
                ])
          ],
        ),
      ),
    );
  }

  Widget _micStickerPanel() {
    return StickersWidget(
      micStickerId,
      StickerMenuType.collection,
      stickerSend: sendMicSticker,
    );
  }

  Widget _vipMicPanel() {
    return StickersWidget(
      vipMicStickerId,
      StickerMenuType.collection,
      vip: true,
      stickerSend: sendMicSticker,
    );
  }

  void sendMicSticker(Sticker sticker) {
    final manage = MicStickerDisplayManager.myManager;
    if (manage != null &&
        (manage.stickerQueue.isNotEmpty == true ||
            manage.stickerController.isShowing)) {
      toast(LocaleStrings.instance.waitSent, isDark: true);
      return;
    }
    RoomStatistics.reportRoomEmojiSend(
        roomId: roomService.getCurrentRoomId(),
        type: roomService.getCurrentRoom()?.mode);
    roomChatService.sendSticker(
        sticker: sticker,
        type: RoomStickerType.micSticker,
        from: widget.from);
    routerUtil.pop();
  }

  Widget _dicePanel() {
    return Container(
      height: 227.pt,
      child: GridView.builder(
        padding: EdgeInsets.all(15.pt),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            childAspectRatio: 1,
            crossAxisSpacing: 20.pt,
            mainAxisSpacing: 14.pt),
        itemCount: _localStickers.length,
        itemBuilder: (BuildContext context, int index) {
          final item = _localStickers[index];
          return GestureDetector(
            onTap: () {
              final manage = MicStickerDisplayManager.myManager;
              if (manage != null &&
                  (manage.stickerQueue.isNotEmpty == true ||
                      manage.stickerController.isShowing)) {
                toast(LocaleStrings.instance.waitSent, isDark: true);
                return;
              }
              item.onTap?.call();
              routerUtil.pop();
            },
            behavior: HitTestBehavior.opaque,
            child: FLImage.asset(item.resource, width: item.size ?? 62.pt),
          );
        },
      ),
    );
  }

  void _updateIndex(int index) {
    globalSp.setInt(_spKey, index);
  }
}
