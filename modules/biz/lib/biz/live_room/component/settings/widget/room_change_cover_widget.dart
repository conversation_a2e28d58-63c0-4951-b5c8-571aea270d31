import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/const/enums.dart';

class RoomProfileBaseInfoWidget extends StatelessWidget {
  final RoomMainController mainController;

  const RoomProfileBaseInfoWidget({super.key, required this.mainController});

  bool get _canEdit => mainController.isOwner;

  @override
  Widget build(BuildContext context) {
    return _roomInfoWidget(context);
  }

  Widget _roomInfoWidget(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            _buildCover(),
            20.wSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      mainController.roomName.value ?? '',
                      style: TextStyle(
                        color: const Color(0xFF040220),
                        fontSize: 17.sp,
                        fontWeight: FontWeightExt.heavy,
                      ),
                    ),
                    4.wSpace,
                    Visibility(
                      visible: _canEdit,
                      child: GestureDetector(
                        onTap: () {
                          mainController.profileController.onRoomNameClick();
                        },
                        behavior: HitTestBehavior.opaque,
                        child: Padding(
                          padding: EdgeInsets.all(5.pt),
                          child: FLImage.asset(Res.roomIconProfileEdit, height: 12.pt, fit: BoxFit.cover),
                        ),
                      ),
                    )
                  ],
                ),
                12.hSpace,
                Row(
                  children: [
                    Text(
                      '${LocaleStrings.instance.roomIdKey}:${mainController.roomID.value ?? ''}',
                      style: TextStyle(
                        color: R.color.colorB2,
                        fontSize: 12.sp,
                        fontWeight: FontWeightExt.heavy,
                      ),
                    ),
                    4.wSpace,
                    GestureDetector(
                      onTap: () {
                        final id = mainController.roomID.value ?? '';
                        Clipboard.setData(ClipboardData(text: id));
                        toast("${LocaleStrings.instance.copySuccess}");
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Padding(
                        padding: EdgeInsets.all(5.pt),
                        child: FLImage.asset(Res.roomIconProfileCopy, height: 12.pt, fit: BoxFit.cover),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ],
        ),
        10.hSpace,
        GestureDetector(
          onTap: mainController.userType == RoomUserType.OWNER
              ? () {
                  mainController.settingsController.onRoomAnnounceClick(context);
                }
              : null,
          behavior: HitTestBehavior.opaque,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: Text(
                mainController.roomAnnounce.value ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: R.color.color20,
                  fontSize: 12.sp,
                  fontWeight: FontWeightExt.medium,
                ),
              )),
              Visibility(
                visible: _canEdit,
                child: FLImage.asset(Res.roomIconProfileArrow, height: 8.5.pt, fit: BoxFit.cover),
              ),
            ],
          ),
        ),
        10.hSpace,
        Divider(color: const Color(0xFFF5F7F9), height: 2.pt),
      ],
    );
  }

  Widget _buildCover() {
    final cover = mainController.cover.value ?? '';
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: mainController.userType == RoomUserType.OWNER ? mainController.editCover : null,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(Res.roomBgProfileCover, width: 99.pt, height: 99.pt, fit: BoxFit.cover),
          Positioned(
              top: 18.pt,
              left: 19.pt,
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(5.pt)),
                child: CachedNetworkImage(imageUrl: cover, height: 69.pt, fit: BoxFit.cover),
              )),
          Visibility(
            visible: mainController.userType == RoomUserType.OWNER,
            child: Positioned(
              right: 0,
              bottom: 7.pt,
              child: Container(
                width: 23.pt,
                height: 23.pt,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.5.pt),
                  color: const Color(0xFF9B81FE),
                ),
                child: FLImage.asset(Res.roomIconProfileCamera, height: 12.pt, fit: BoxFit.cover),
              ),
            ),
          )
        ],
      ),
    );
  }
}
