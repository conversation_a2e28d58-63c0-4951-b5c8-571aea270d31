import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';

import 'room_dialog_title_widget.dart';

class RoomTextSceneConfig {
  RoomTextSceneConfig({required this.scene, this.text});

  final RoomTextScene scene;
  final String? text;

  String title() {
    switch (scene) {
      case RoomTextScene.roomName:
        return LocaleStrings.instance.roomName;
      case RoomTextScene.roomAnnounce:
        return LocaleStrings.instance.announce;
    }
  }

  String hint() {
    switch (scene) {
      case RoomTextScene.roomName:
        return LocaleStrings.instance.roomNameInputHint;
      case RoomTextScene.roomAnnounce:
        return LocaleStrings.instance.roomAnnounceInputHint;
    }
  }

  int maxNum() {
    switch (scene) {
      case RoomTextScene.roomName:
        return 20;
      case RoomTextScene.roomAnnounce:
        return 140;
    }
  }
}

enum RoomTextScene {
  roomName,
  roomAnnounce,
}

Future showRoomTextInputDialog({
  required RoomTextSceneConfig config,
  BuildContext? context,
}) async {
  return await showBottomSheet(
    (BuildContext context) {
      return RoomNameInputWidget(
        config: config,
      );
    },
    context: context,
    barrierColor: Colors.transparent,
    enableDrag: false,
    barrierDismissible: false,
    routeSettings:
        RouteSettings(name: '$R_LIVE_ROOM_SETTING/${config.scene.name}'),
  );
}

/// 房间名称输入框
class RoomNameInputWidget extends StatefulWidget {
  final RoomTextSceneConfig config;

  const RoomNameInputWidget({Key? key, required this.config}) : super(key: key);

  @override
  State<RoomNameInputWidget> createState() => _RoomNameInputWidgetState();
}

class _RoomNameInputWidgetState extends State<RoomNameInputWidget> {
  late TextEditingController _controller;

  bool _hasText = false;
  bool _canEdit = true;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.config.text);
    if (accountService.isSuperAdmin()) {
      _canEdit = false;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedPadding(
      padding: MediaQuery.of(context).viewInsets,
      duration: const Duration(milliseconds: 100),
      child: Container(
        height: 195.pt,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.pt)),
        ),
        child: Column(
          children: [
            _buildTop(),
            Divider(color: const Color(0xFFF5F7F9), height: 1),
            _buildTextField(),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    bool isSuperAdmin = accountService.isSuperAdmin();
    return RoomDialogTitleWidget(
      title: widget.config.title(),
      confirmText: isSuperAdmin
          ? LocaleStrings.instance.reset
          : LocaleStrings.instance.done,
      confirmEnable: isSuperAdmin ? true : _hasText,
      onConfirm: _handleEditingComplete,
    );
  }

  _buildTextField() {
    return Container(
      margin: EdgeInsetsDirectional.only(top: 18.pt, start: 18.pt, end: 18.pt),
      height: 103.pt,
      decoration: BoxDecoration(
        color: const Color(0xFFF5F7F9),
        borderRadius: BorderRadius.circular(12.pt),
        border: Border.all(
          color: roomTheme.theme.settingItemBorder,
          width: 0.5,
        ),
      ),
      padding: EdgeInsets.only(left: 11.pt, right: 7.pt),
      child: TextField(
        controller: _controller,
        autofocus: _canEdit,
        enabled: _canEdit,
        cursorColor: R.color.primaryColor,
        keyboardType: TextInputType.text,
        scrollPadding: EdgeInsets.zero,
        decoration: InputDecoration(
          hintText: widget.config.hint(),
          hintStyle: TextStyle(
            fontSize: 13.sp,
            color: R.color.color90,
          ),
          border: InputBorder.none,
          counterStyle: TextStyle(
            fontSize: 13.sp,
            color: R.color.color90,
          ),
          contentPadding: EdgeInsetsDirectional.only(top: 10.pt),
          isCollapsed: true,
        ),
        style: TextStyle(
          fontSize: 13.sp,
          color: R.color.color20,
        ),
        maxLength: widget.config.maxNum(),
        onChanged: (text) {
          if (text.trim().isNotEmpty == true) {
            _hasText = true;
          } else {
            _hasText = false;
          }
          refresh();
        },
        onEditingComplete: _handleEditingComplete,
        maxLines: 3, // 控制滑动区域高度
      ),
    );
  }

  _handleEditingComplete() async {
    String text = _controller.text.trim();
    if (!accountService.isSuperAdmin() && text.trim().isNotEmpty != true) {
      toast(LocaleStrings.instance.inputCannotBeNull, isDark: true);
      return;
    }
    var result;
    if (widget.config.scene == RoomTextScene.roomName) {
      showLoading(type: LoadingType.flutter);
      if (accountService.isSuperAdmin()) {
        var host = roomService.getCurrentRoom()?.roomOwnerNickname ?? '';
        result = await roomService.editRoomInfo(
            roomName: LocaleStrings.instance.defaultRoomName(host));
      } else {
        result = await roomService.editRoomInfo(roomName: text);
      }
    } else if (widget.config.scene == RoomTextScene.roomAnnounce) {
      showLoading(type: LoadingType.flutter);
      if (accountService.isSuperAdmin()) {
        result = await roomService.editRoomInfo(
            announce: LocaleStrings.instance.defaultRoomAnnounce);
      } else {
        result = await roomService.editRoomInfo(announce: text);
      }
    } else {
      return;
    }
    hideLoading(type: LoadingType.flutter);
    if (result.isSuccess) {
      toast(LocaleStrings.instance.editSuccessfully, isDark: true);
      routerUtil.pop();
    } else {
      toast(result.msg ?? LocaleStrings.instance.defaultError, isDark: true);
    }
  }
}
