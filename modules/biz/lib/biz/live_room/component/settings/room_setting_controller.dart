import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/modules/live/room/const/const.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/social/model/report_template.dart';

import '../../page/room/room_main_controller.dart';
import '../../widgets/room_alert_dialog.dart';
import 'room_setting_item_model.dart';
import 'widget/room_txt_input.dart';

class RoomSettingController extends AbsGetController with AbsRoomModuleController {
  final RoomMainController mainController;

  RoomSettingController(this.mainController);

  List<RoomSettingItem> operationItems = [];

  List<RoomSettingItem> superAdminItems = [];

  RxnBool roomSoundSwitch = RxnBool();

  String? roomMode;

  bool isLoading = false;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void stateInit() {
    super.stateInit();
    initData();
  }

  @override
  void stateDispose() {
    super.stateDispose();
  }

  void initData() {
    if (mainController.isAdmin) {
      _initAdminData();
    } else {
      _initNormalData();
    }
  }

  void _clearRoomSettingList(List<RoomSettingItem> list) {
    for (int i = 0; i < list.length; i++) {
      list[i].rxSubType?.close();
      list[i].rxSubType = null;
      list[i].data?.close();
      list[i].data = null;
    }
    list.clear();
  }

  /// 初始化管理员的数据
  Future<void> _initAdminData() async {
    isLoading = true;

    roomMode = roomService.getCurrentRoom()?.mode;

    _clearRoomSettingList(operationItems);

    if (mainController.userType.value != RoomUserType.OWNER) {
      operationItems.add(RoomSettingItem(RoomSettingItemType.report));
    }

    // 房间声音收听开关
    bool soundOpen = roomSoundSwitch.value ?? true;
    RoomSettingItem soundSwitch = RoomSettingItem(
      RoomSettingItemType.soundSwitch,
      rxSubType: Rxn(
        soundOpen ? RoomSettingSubType.soundOpen : RoomSettingSubType.soundClose,
      ),
    );
    operationItems.add(soundSwitch);

    operationItems.add(RoomSettingItem(RoomSettingItemType.minimize));
    operationItems.add(RoomSettingItem(RoomSettingItemType.exit));

    // 超级管理员操作
    _clearRoomSettingList(superAdminItems);
    if (accountService.isSuperAdmin()) {
      superAdminItems.add(RoomSettingItem(RoomSettingItemType.downWeight));
    }

    operationItems.addAll(superAdminItems);

    isLoading = false;
    update();
  }

  bool isRoomMode(String mode) {
    return roomService.getCurrentRoom()?.mode == mode;
  }

  void _initNormalData() {
    _clearRoomSettingList(operationItems);

    operationItems.add(RoomSettingItem(RoomSettingItemType.report));

    // 房间声音收听开关
    bool soundOpen = roomSoundSwitch.value ?? true;
    var soundSwitch = RoomSettingItem(
      RoomSettingItemType.soundSwitch,
      rxSubType: Rxn(
        soundOpen ? RoomSettingSubType.soundOpen : RoomSettingSubType.soundClose,
      ),
    );
    operationItems.add(soundSwitch);

    operationItems.add(RoomSettingItem(RoomSettingItemType.minimize));
    operationItems.add(RoomSettingItem(RoomSettingItemType.exit));

    update();
  }

  void onRoomAnnounceClick(BuildContext context) async {
    routerUtil.pop(context: context);
    await showRoomTextInputDialog(
      context: mainController.getContext(),
      config: RoomTextSceneConfig(
        scene: RoomTextScene.roomAnnounce,
        text: mainController.roomAnnounce.value,
      ),
    );
  }

  void onItemClick(BuildContext context, RoomSettingItem item) async {
    switch (item.type) {
      case RoomSettingItemType.soundSwitch:
        bool muteSound = roomSoundSwitch.value ?? true;
        bool result = await mediaService.muteRemote(muteSound);
        if (result) {
          roomSoundSwitch.value = !muteSound;
          item.rxSubType!.value = !muteSound ? RoomSettingSubType.soundOpen : RoomSettingSubType.soundClose;
          update();
        }
        break;
      case RoomSettingItemType.minimize:
        routerUtil.pop(context: context);
        _handleMiniRoom();
        break;
      case RoomSettingItemType.exit:
        routerUtil.pop(context: context);
        _handleExitRoom();
        break;
      case RoomSettingItemType.report:
        _reportRoom();
        break;
      case RoomSettingItemType.downWeight:
        routerUtil.pop(context: context);
        _handleDownWeight();
        break;
      default:
        break;
    }
  }

  void onTapFollowRoom() async {
    if (roomService.getCurrentRoom()?.isFollowed == 1) {
      final resp = await roomService.removeFollowerRoom(roomId: roomService.getCurrentRoomId() ?? "");
      if (resp.isSuccess) {
        toast(LocaleStrings.instance.unfollowSuccessfully);
        final roomInfo = roomService.getCurrentRoom()?..isFollowed = 0;
        roomService.updateRoomInfo(roomInfo: roomInfo);
      } else {
        toast(resp.msg ?? LocaleStrings.instance.defaultError);
      }
    } else {
      final roomId = mainController.roomID.value;
      RoomStatistics.reportRoomFollowClick(
          roomId: roomId, roomType: roomService.getCurrentRoom()?.isFamilyRoom == true ? "family" : "normal");
      final resp = await roomService.followerRoom(roomId: roomId);
      if (resp.isSuccess) {
        toast(LocaleStrings.instance.followedSuccessfully);
        final roomInfo = roomService.getCurrentRoom()?..isFollowed = 1;
        roomService.updateRoomInfo(roomInfo: roomInfo);
      } else {
        toast(resp.msg ?? LocaleStrings.instance.defaultError);
      }
    }
  }

  Future<void> _reportRoom() async {
    String roomId = roomService.getCurrentRoomId() ?? "";
    final msgs = roomChatService.getMsgs();
    final user = await roomService.getCurrentUserInfo();
    int endIndex = msgs.length >= 20 ? 20 : msgs.length;
    routerUtil.popAndPush(R_SETTINGS_REPORT, params: {
      P_DATA: RoomReport(
        roomId: roomId,
        targetUid: roomService.getCurrentRoom()?.roomOwner ?? "",
        roomOnlineCount: roomService.getTotalInRoom(),
        msgs: msgs.sublist(0, endIndex),
        isFollow: user?.isMember == true ? 1 : 0,
      ),
      P_STATISTIC_FROM: R_LIVE_LIVE_ROME
    });
  }

  void _handleMiniRoom() {
    RoomStatistics.reportRoomKeep(
        type: roomService.getCurrentRoom()?.mode,
        roomId: roomService.getCurrentRoom()?.roomId,
        from: LiveRoomHandler.from);
    routerUtil.pop();
  }

  void _handleExitRoom() {
    final info = roomService.getCurrentRoom();
    final isSuperAdmin = accountService.isSuperAdmin();

    if ((info?.isOwner != true && isSuperAdmin != true)) {
      // 直接退出
      _leaveRoom(false);
      return;
    }

    showRoomAlertDialog(
      context: mainController.getContext(),
      title: LocaleStrings.instance.roomExitTitle,
      content: LocaleStrings.instance.roomExitDes,
      cancelText: LocaleStrings.instance.roomCloseAndExit,
      confirmText: LocaleStrings.instance.exit,
      onCancel: () {
        final isGameMode = info?.isGameMode ?? false;
        if (isGameMode) {
          final isOwner = info?.isOwner ?? false;
          if (isOwner || isSuperAdmin) {
            final isGamePlaying = playerService.isGamePlaying();
            if (isGamePlaying) {
              toast(LocaleStrings.instance.cannotExitRoomDuringGame);
              return;
            } else {
              // 直接退出
              _leaveRoom(false);
              return;
            }
          }
        }

        _leaveRoom(true);
      },
      onConfirm: () {
        // 直接退出
        _leaveRoom(false);
      },
    );
  }

  void _leaveRoom(closeRoom) async {
    await _reportRoomExit();
    var rid = mainController.roomID.value;
    if (rid != null) {
      LiveRoomHandler.leaveRoom(
        rid,
        close: closeRoom,
        leaveRoomCallback: (value) {
          if (value.isSuccess) {
            RoomStatistics.reportRoomCloseClick(roomId: roomService.getCurrentRoomId(), type: 'exit');
            routerUtil.pop(context: mainController.getContext());
          }
        },
      );
    }
    rxUtil.send(RoomEvent.exitRoom, 1);
  }

  Future<void> _reportRoomExit() async {
    final user = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomExit(
        uid: user?.uid,
        gender: user?.sexStr,
        exitType: '1',
        isMyRoom: roomService.getCurrentRoom()?.isOwner == true ? '1' : '2',
        mode: roomService.getCurrentRoom()?.mode,
        roomId: roomService.getCurrentRoom()?.roomId,
        internetErr: '${mediaService.getBadNetworkQualityTime() ~/ 1000}',
        timeDistance: ((roomService.getInRoomTime() / 1000).round()).toString(),
        refer: LiveRoomHandler.from);
  }

  void _handleDownWeight() {
    showRoomAlertDialog(
      title: LocaleStrings.instance.doubleConfirmation,
      content: LocaleStrings.instance.doubleConfirmationDes,
      confirmText: LocaleStrings.instance.yes,
      cancelText: LocaleStrings.instance.cancel,
      onConfirm: () async {
        var resp = await roomService.doPunish(act: RoomPunishType.room, level: RoomPunishLevel.notRecommend);
        if (resp.isSuccess) {
          toast(LocaleStrings.instance.submitSuccess, isDark: true);
        } else {
          toast(resp.msg ?? LocaleStrings.instance.defaultError, isDark: true);
        }
      },
    );
  }
}
