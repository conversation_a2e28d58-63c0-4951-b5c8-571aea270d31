import 'package:biz/biz.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/live/game/const/events.dart';

class AnimatedSettingsButton extends StatefulWidget {
  final VoidCallback? onSettingsTap;
  final VoidCallback? onHelpTap;
  final Function(bool enable)? onMusicTap;
  final double size;

  const AnimatedSettingsButton({
    Key? key,
    this.onSettingsTap,
    this.onHelpTap,
    this.onMusicTap,
    this.size = 28.0,
  }) : super(key: key);

  @override
  State<AnimatedSettingsButton> createState() => _AnimatedSettingsButtonState();
}

class _AnimatedSettingsButtonState extends State<AnimatedSettingsButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;
  bool _isMusicEnabled = true;

  final _subscription = CompositeSubscription();

  @override
  void initState() {
    super.initState();
    newGameService.getMusicEnable().then((value) => setState(() => _isMusicEnabled = value));
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _subscription.add(
        rxUtil.observer<int>(GameEvent.gameStateChange).listen(_onGameStatusChange)
    );
  }

  void _onGameStatusChange(int gameStatus) async {
    if (mounted) {
      _isMusicEnabled = await newGameService.getMusicEnable();
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _subscription.dispose();
    super.dispose();
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _expandAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: _isExpanded
              ? widget.size * 3 + 16.pt // 额外的16是图标之间的间距
              : widget.size,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(
              _isExpanded ? 24.pt : widget.size / 2,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Settings Icon
              GestureDetector(
                child: Container(
                  margin: EdgeInsets.only(top: _isExpanded ? 3.pt : 0),
                  padding: EdgeInsets.only(left: 2.pt),
                  alignment: Alignment.center,
                  width: widget.size,
                  height: widget.size,
                  child: UnconstrainedBox(
                    child: FLImage.asset(Res.gameIcSetting, width: 20.pt, height: 18.pt, fit: BoxFit.fill),
                  ),
                ),
                onTap: () {
                  _toggleExpand();
                  if (!_isExpanded && widget.onSettingsTap != null) {
                    widget.onSettingsTap!();
                  }
                },
              ),
              // Music Icon
              GestureDetector(
                child: Container(
                  padding: EdgeInsets.only(left: 2.pt),
                  alignment: Alignment.center,
                  width: widget.size,
                  height: (widget.size + 6.pt) * _expandAnimation.value,
                  child: Opacity(
                    opacity: _expandAnimation.value,
                    child: UnconstrainedBox(
                      child: FLImage.asset(_isMusicEnabled ? Res.gameIcMusicTurnOn : Res.gameIcMusicTurnOff, width: 20.pt, height: 18.pt, fit: BoxFit.fill),
                    ),
                  ),
                ),
                onTap: () {
                  if (_isExpanded) {
                    _isMusicEnabled = !_isMusicEnabled;
                    setState(() {});
                    widget.onMusicTap?.call(_isMusicEnabled);
                  }
                },
              ),
              // Help Icon
              GestureDetector(
                child: Container(
                  padding: EdgeInsets.only(left: 2.pt),
                  alignment: Alignment.center,
                  width: widget.size,
                  height: (widget.size + 6.pt) * _expandAnimation.value,
                  child: Opacity(
                    opacity: _expandAnimation.value,
                    child: UnconstrainedBox(
                      child: FLImage.asset(Res.gameIcHelp, width: 20.pt, height: 18.pt, fit: BoxFit.fill),
                    ),
                  ),
                ),
                onTap: _isExpanded ? widget.onHelpTap : null,
              ),
            ],
          ),
        );
      },
    );
  }
}
