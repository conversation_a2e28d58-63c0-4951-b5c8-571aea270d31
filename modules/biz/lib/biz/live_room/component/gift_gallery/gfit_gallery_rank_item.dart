
import 'dart:ffi';

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/model/room_gift_gallery_rank.dart';

class GiftGalleryRankItem extends StatefulWidget {
  final int index;
  final RoomGiftGalleryRankItem item;
  final Function(String? rid, String uid)? onTapAvatar;

  const GiftGalleryRankItem({super.key, this.index = 0, required this.item, this.onTapAvatar});

  @override
  State<GiftGalleryRankItem> createState() => _GiftGalleryRankItemState();
}

class _GiftGalleryRankItemState extends State<GiftGalleryRankItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 66.pt,
      alignment: Alignment.centerLeft,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildNum(),
          _userAvatar(),
          SizedBox(width: 9.pt),
          Expanded(child: _nameWidget()),
          SizedBox(width: 18.pt),
          _totalDiamonds(),
          SizedBox(width: 18.pt),
        ],
      ),
    );
  }

  Widget _buildNum() {
    return Container(
      width: 40.pt,
      margin: EdgeInsets.only(left: 5.pt),
      child: Center(
        child: Text(
          '${widget.index + 1}',
          style: TextStyle(
            color: _rankColor(),
            fontSize: 21.pt,
            fontWeight: FontWeightExt.heavy,
            height: 1.2,
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }
  
  Color _rankColor() {
    if (widget.index == 0) {
      return Color(0xFFFFBB25);
    } else if (widget.index == 1) {
      return Color(0xFFBDCDD9);
    } else if (widget.index == 2) {
      return Color(0xFFFF8D76);
    }
    return R.color.primaryColor;
  }

  Widget _userAvatar() {
    return UserAvatar(
      url: widget.item.avatar,
      avatarCode: widget.item.avatarCode,
      size: 42.pt,
      userId: widget.item.uid,
      showRoomStatus: true,
      loadRoomStatus: true,
      onTap: ({String? rid}) {
        widget.onTapAvatar?.call(rid, widget.item.uid);
      },
    );
  }

  Widget _nameWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${widget.item.nickname}',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.pt,
            fontWeight: FontWeightExt.heavy,
            height: 1.1,
          ),
        ),
        SizedBox(height: 5.pt,),
        Text(
          '${widget.item.hasLightNum}/${widget.item.giftNum}',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14.pt,
            fontWeight: FontWeightExt.heavy,
            fontStyle: FontStyle.italic,
            height: 1.1,
          ),
        ),
      ]
    );
  }

  Widget _totalDiamonds() {
    return Row(
      children: [
        Text(
          '${widget.item.totalDiamonds}',
          style: TextStyle(
            color: Colors.white,
            fontSize: 15.pt,
            fontWeight: FontWeightExt.medium,
          )
        ),
        SizedBox(width: 3.pt,),
        FLImage.asset(Res.financeDiamond, width: 14.pt, height: 14.pt)
      ],
    );
  }
}
