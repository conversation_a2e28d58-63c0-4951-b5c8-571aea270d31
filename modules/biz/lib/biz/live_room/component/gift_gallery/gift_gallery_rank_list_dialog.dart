

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/widgets/room_h5_dialog.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/global/widget/image.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;

import 'gift_gallery_rank_list.dart';
void showGiftGalleryRankDialog({String? from, VoidCallback? onDismiss}) {
  dialog.showBottomSheet((_) =>
      GiftGalleryRankListDialog(from: from, onDismiss: onDismiss,),
    routeSettings: RouteSettings(name: R_GIFT_GALLERY_RANK_DIALOG),
  );
}

class GiftGalleryRankListDialog extends StatefulWidget {
  final String? from;
  final VoidCallback? onDismiss;
  const GiftGalleryRankListDialog({super.key, this.from, this.onDismiss,});

  @override
  State<GiftGalleryRankListDialog> createState() => _GiftGalleryRankListDialogState();
}

class _GiftGalleryRankListDialogState extends State<GiftGalleryRankListDialog> {
  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth,
      height: screenWidth * 680 / 375,
      child: Stack(
        children: [
          Positioned.fill(
            child: FLImage.asset(
              Res.roomGiftGalleryIntroBg,
              fit: BoxFit.cover,
            ),
          ),
          Column(
            children: [
              _topView(),
              Expanded(
                child: GiftGalleryRankList(
                  from: widget.from,
                  onDismiss: () {
                    Navigator.of(context).pop();
                    widget.onDismiss?.call();
                  },
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _topView() {
    return Container(
      height: 66.pt,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _backBtn(),
          SizedBox(width: 10.pt,),
          Expanded(child: _title()),
          SizedBox(width: 10.pt,),
          _intro(),
          SizedBox(width: 18.pt,),
        ],
      ),
    );
  }

  Widget _backBtn() {
    return GestureDetector(
      onTap: () {
        routerUtil.pop();
      },
      child: Container(
        width: 48.pt,
        height: 56.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
            Res.commonBack,
            color: Colors.white,
            width: 30.pt,
            height: 30.pt
        ),
      ),
    );
  }

  Widget _title() {
    return Text(
      LocaleStrings.instance.giftLightUpRank,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 21.pt,
        fontWeight: FontWeightExt.heavy,
        color: Colors.white,
      ),
    );
  }

  Widget _intro() {
    return GestureDetector(
      onTap: () {
        showRoomH5Dialog(giftWallHelp, heightPercent: 0.7);
      },
      child: Container(
        width: 30.pt,
        height: 30.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
          Res.roomIntroduction,
          width: 18.pt,
          height: 18.pt,
        ),
      ),
    );
  }
}
