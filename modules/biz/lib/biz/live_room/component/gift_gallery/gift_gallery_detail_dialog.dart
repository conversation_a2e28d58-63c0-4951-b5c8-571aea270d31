import 'package:biz/biz.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/gift/model/gift_gallery_list_model.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:service/modules/gift/model/gift_grallery_rank_list.dart';
import 'gift_gallery_detail_logic.dart';

void showGiftGalleryGiftDetailDialog({
  required String targetUid,
  required GiftListItem giftItem,
  required String from,
  Function(GiftListItem item)? onSendGiftSuccess,
}) {
  dialog.showBottomSheet(
    (_) => GiftGalleryDetailDialog(
      targetUid: targetUid,
      giftItem: giftItem,
      from: from,
      onSendGiftSuccess: onSendGiftSuccess,
    ),
    routeSettings: RouteSettings(name: R_GIFT_GALLERY_DETAIL_DIALOG),
    barrierColor: Colors.transparent,
    enableDrag: true,
  );
}

class GiftGalleryDetailDialog extends StatefulWidget {

  final String targetUid;
  final GiftListItem giftItem;
  final Function(GiftListItem item)? onSendGiftSuccess;
  final String from;
  const GiftGalleryDetailDialog({
    super.key,
    required this.targetUid,
    required this.giftItem,
    required this.from,
    this.onSendGiftSuccess
  });

  @override
  State<GiftGalleryDetailDialog> createState() => _GiftGalleryDetailDialogState();
}

class _GiftGalleryDetailDialogState extends State<GiftGalleryDetailDialog> with GetStateBuilderMixin
<GiftGalleryDetailDialog, GiftGalleryDetailLogic> {

  double _radio = 0;

  @override
  void initState() {
    getCtl.targetUid = widget.targetUid;
    getCtl.giftItem = widget.giftItem;
    getCtl.onSendGiftSuccess = widget.onSendGiftSuccess;
    getCtl.from = widget.from;
    super.initState();
  }

  @override
  GiftGalleryDetailLogic initCtl() {
    return GiftGalleryDetailLogic();
  }

  void _culItemSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    _radio = (screenWidth - 2 * 21.pt) / 333.pt;
  }

  @override
  Widget build(BuildContext context) {
    _culItemSize(context);
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth,
      height: screenWidth * 680 / 375,
      child: Stack(
        children: [
          Positioned.fill(
            child: FLImage.asset(
              Res.roomGiftGalleryBg,
              fit: BoxFit.cover,
            ),
          ),
          _topView(),
          Positioned(
            top: 56.pt,
            left: 0,
            right: 0,
            bottom: 0,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _gift(),
                  _btnContainer(),
                  _rank(screenWidth),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _topView() {
    return Row(
      children: [
        _backBtn(),
        SizedBox(width: 10.pt,),
        Expanded(child: _title()),
        SizedBox(width: 10.pt,),
        SizedBox(width: 56.pt, height: 56.pt,)
      ],
    );
  }

  Widget _backBtn() {
    return GestureDetector(
      onTap: () {
        routerUtil.pop();
      },
      child: Container(
        width: 56.pt,
        height: 56.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
          Res.commonBack,
          color: Colors.white,
          width: 26.pt,
          height: 26.pt
        ),
      ),
    );
  }

  Widget _title() {
    return Text(
      getCtl.giftItem?.giftInfo.name ?? '',
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          fontSize: 18.pt,
          fontWeight: FontWeightExt.heavy,
          color: Colors.white,
      ),
    );
  }

  Widget _gift() {
    return SizedBox(
      width: 214.pt,
      height: 242.pt,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned(
            top: 69.pt,
            child: Container(
              width: 214.pt,
              height: 173.pt,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(9.pt),
                  topRight: Radius.circular(36.pt),
                  bottomLeft: Radius.circular(24.pt),
                  bottomRight: Radius.circular(24.pt),
                ),
                border: Border.all(color: Colors.white.withOpacity(0.7), width: 1,),
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0),
                    Colors.white.withOpacity(0.6),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              ),
            ),
          ),

          getCtl.giftItem?.hasLighted == true ?
          ColorFiltered(
            colorFilter: ColorFilter.matrix([
              1, 0, 0, 0, 0,
              0, 1, 0, 0, 0,
              0, 0, 1, 0, 0,
              0, 0, 0, 1, 0,
            ]),
            child: CachedNetworkImage(
              imageUrl: getCtl.giftItem?.giftInfo.icon ?? '',
              width: 176.pt,
              height: 176.pt,
            ),
          ) : ColorFiltered(
            colorFilter: ColorFilter.matrix([
              0.2126, 0.7152, 0.0722, 0, 0,
              0.2126, 0.7152, 0.0722, 0, 0,
              0.2126, 0.7152, 0.0722, 0, 0,
              0,      0,      0,      1, 0,
            ]),
            child: CachedNetworkImage(
              imageUrl: getCtl.giftItem?.giftInfo.icon ?? '',
              width: 176.pt,
              height: 176.pt,
            ),
          ),

          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: _bottom(),
          ),
        ],
      ),
    );
  }

  Widget _bottom() {
    return Container(
      height: 58.pt,
      padding: EdgeInsets.symmetric(horizontal: 11.pt),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24.pt),
          bottomRight: Radius.circular(24.pt),
        ),
        color: Colors.white.withOpacity(0.6),
      ),
      alignment: Alignment.center,
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  getCtl.giftItem?.namingUser != null
                      ? LocaleStrings.instance.titleGifters
                      : LocaleStrings.instance.noTitleGifters,
                  style: TextStyle(
                      fontSize: 18.pt,
                      fontWeight: FontWeightExt.heavy,
                      color: R.color.textColor2,
                      overflow: TextOverflow.ellipsis,
                      height: 1.1),
                ),
                SizedBox(height: 3.pt,),
                Flexible(
                  child: Text(
                    getCtl.giftItem?.namingUser != null
                        ? (getCtl.giftItem?.namingUser?.nickname ?? '')
                        : '${getCtl.giftItem?.completeNum}/${getCtl.giftItem?.requireNum}',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 16.pt,
                      fontWeight: FontWeightExt.medium,
                      color: R.color.primaryColor,
                      height: 1.1,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 10.pt,),

          getCtl.giftItem?.namingUser != null ?
          UserAvatar(
            url: getCtl.giftItem?.namingUser?.avatar,
            avatarCode: getCtl.giftItem?.namingUser?.avatarCode,
            size: 37.pt,
            userId: getCtl.giftItem?.namingUser?.uid,
            onTap: ({String? rid}) {
              getCtl.goToProfile(getCtl.giftItem?.namingUser?.uid);
            },
          ) : FLImage.asset(
            Res.roomGiftGalleryDefaultAvatar,
            width: 37.pt,
            height: 37.pt,
          ),
        ],
      ),
    );
  }

  Widget _btnContainer() {
    return Container(
      height: 114.pt,
      padding: EdgeInsets.symmetric(horizontal: 21.pt),
      alignment: Alignment.center,
      child: Visibility(
        visible: widget.targetUid != accountService.currentUid(),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _sendSingleGiftBtn(),
            Visibility(
              visible: getCtl.getTitleInfo?.isShow == true,
              child: Expanded(
                child: _sendTotalGiftBtn(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sendSingleGiftBtn() {
    return _wrapActionBtn(
      title: LocaleStrings.instance.send,
      localIcon: Res.financeDiamond,
      countString: '${getCtl.giftItem?.giftInfo.price}',
      onTap: getCtl.onTapSendSingleGift
    );
  }

  Widget _sendTotalGiftBtn() {
    return Padding(
      padding: EdgeInsets.only(left: 20.pt),
      child: _wrapActionBtn(
          title: LocaleStrings.instance.getTitle,
          networkIcon: getCtl.giftItem?.giftInfo.icon,
          countString: '*${getCtl.getTitleInfo?.needNum ?? 0}',
          gradient: LinearGradient(
            colors: [
              Color(0xFF3142FF),
              Color(0xFFC70FFF),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          onTap: getCtl.onTapSendTotalGift
      ),
    );
  }

  Widget _wrapActionBtn({
    required String title,
    required String countString,
    String? localIcon,
    String? networkIcon,
    Gradient? gradient,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 46.pt,
        padding: EdgeInsets.symmetric(horizontal: 11.pt),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(23.pt),
          color: gradient == null ? Colors.white.withOpacity(0.3) : null,
          gradient: gradient
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                  fontSize: 18.pt,
                  fontWeight: FontWeightExt.heavy,
                  color: Colors.white,
                  fontStyle: FontStyle.italic
              ),
            ),

            Text(
              ' (',
              style: TextStyle(
                  fontSize: 18.pt,
                  fontWeight: FontWeightExt.heavy,
                  color: Colors.white,
                  fontStyle: FontStyle.italic
              ),
            ),

            Row(
              children: [
                localIcon != null
                    ? FLImage.asset(
                        Res.financeDiamond,
                        width: 20.pt,
                        height: 20.pt,
                      )
                    : CachedNetworkImage(
                        imageUrl: networkIcon ?? '',
                        width: 20.pt,
                        height: 20.pt,
                      ),
                Text(
                  countString,
                  style: TextStyle(
                      fontSize: 18.pt,
                      fontWeight: FontWeightExt.heavy,
                      color: Colors.white,
                      fontStyle: FontStyle.italic
                  ),
                ),
              ],
            ),

            Text(
              ')',
              style: TextStyle(
                  fontSize: 18.pt,
                  fontWeight: FontWeightExt.heavy,
                  color: Colors.white,
                  fontStyle: FontStyle.italic
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _rank(double screenWidth) {
    return Padding(
      padding: EdgeInsets.only(left: 21.pt, right: 21.pt, bottom: 34.pt),
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          FLImage.asset(Res.roomGiftGalleryRankBg),
          Positioned(
            top: 13.pt,
            left: 21.pt,
            right: 21.pt,
            child: _rankTitleContainer(),
          ),

          Positioned(
            bottom: 48.pt,
            child: Transform.translate(
              offset: Offset(-(screenWidth - 42.pt) / 4 - 15.pt, 0),
              child: _rankUser(getCtl.getItem(1), rank: 1),
            ),
          ),

          Positioned(
            bottom: 67.pt,
            child: _rankUser(getCtl.getItem(0), rank: 0),
          ),

          Positioned(
            bottom: 32.pt,
            child: Transform.translate(
              offset: Offset((screenWidth - 42.pt) / 4 + 15.pt, 0),
              child: _rankUser(getCtl.getItem(2), rank: 2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _rankTitleContainer() {
    return Column(
      children: [
        _rankTitle(),
        SizedBox(height: 3.pt,),
        Text(
          LocaleStrings.instance.gitGalleryRankSubtitle,
          softWrap: true,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 11.pt,
            fontWeight: FontWeightExt.medium,
            color: Colors.white.withOpacity(0.42),
          ),
        ),
      ],
    );
  }

  Widget _rankTitle() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        FLImage.asset(
          Res.roomGiftGallerySectionLeft,
          width: 23.pt,
          height: 23.pt,
        ),
        SizedBox(width: 3.pt,),
        Text(
          LocaleStrings.instance.gitGalleryRankTitle,
          style: TextStyle(
            fontSize: 16.pt,
            fontWeight: FontWeightExt.black,
            fontStyle: FontStyle.italic,
            color: Colors.white,
          ),
        ),
        SizedBox(width: 3.pt,),
        FLImage.asset(
          Res.roomGiftGallerySectionRight,
          width: 23.pt,
          height: 23.pt,
        ),
      ],
    );
  }

  Widget _rankUser(
    RankListItem? item, {
    int rank = 0,
  }) {
    if (item == null) {
      return FLImage.asset(
        Res.roomGiftGalleryDefaultAvatar,
        width: 37.pt,
        height: 37.pt,
      );
    }

    return Container(
      width: 80.pt,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              UserAvatar(
                url: item.avatar,
                avatarCode: item.avatarCode,
                size: rank == 0 ? 56.pt : 36.pt,
                borderColor: rank == 0 ? Color(0xFFFFBC00) : null,
                borderWidth: rank == 0 ? 2 : 0,
                userId: item.uid,
                onTap: ({String? rid}) {
                  getCtl.goToProfile(item.uid);
                },
              ),
              Positioned(
                top: -12.pt,
                right: -12.pt,
                child: Visibility(
                    visible: rank == 0,
                    child: FLImage.asset(
                      Res.roomGiftGalleryRank1,
                      width: 28.pt,
                      height: 28.pt,
                    ),
                ),
              )
            ],
          ),
          ..._name(item)
        ],
      ),
    );
  }

  List<Widget> _name(RankListItem item) {
    return [
      Flexible(
        child: Text(
          item.nickname,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: TextStyle(
            fontSize: 11.pt,
            fontWeight: FontWeightExt.heavy,
            fontStyle: FontStyle.italic,
            color: Colors.white,
          ),
        ),
      ),
      _rankGift(item),
    ];
  }

  Widget _rankGift(RankListItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CachedNetworkImage(
          imageUrl: getCtl.giftItem?.giftInfo.icon ?? '',
          width: 16.pt,
          height: 16.pt,
        ),

        Text(
          '*${item.senderNum}',
          style: TextStyle(
              fontSize: 13.pt,
              fontWeight: FontWeightExt.heavy,
              color: R.color.primaryLightColor,
              fontStyle: FontStyle.italic
          ),
        ),
      ],
    );
  }
}
