

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/chat_view/room_chat_view_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/gift/model/gift_gallery_list_model.dart';
import 'gift_gallery_list_controller.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;

void showGiftGalleryListDialog({required String targetUid, required String from}) {
  dialog.showBottomSheet((_) =>
      GiftGalleryListDialog(targetUid: targetUid, from: from),
      routeSettings: RouteSettings(name: R_GIFT_GALLERY_LIST_DIALOG),
      barrierColor: Colors.transparent
  );
}

class GiftGalleryListDialog extends StatefulWidget {

  final String targetUid;
  final String from;
  final VoidCallback? onDismiss;
  const GiftGalleryListDialog({
    super.key,
    required this.targetUid,
    required this.from,
    this.onDismiss,
  });

  @override
  State<GiftGalleryListDialog> createState() => _GiftGalleryListDialogState();
}

class _GiftGalleryListDialogState extends State<GiftGalleryListDialog> with GetStateBuilderMixin
<GiftGalleryListDialog, GiftGalleryListController> {


double _itemWidth = 0;
  double _itemHeight = 0;
  final int _crossAxisCount = 3;
  final double _itemCrossSpacing = 14.pt;

  @override
  GiftGalleryListController initCtl() {
    return GiftGalleryListController();
  }

  void _culItemSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    _itemWidth = ((screenWidth - 2 * 18.pt - (_crossAxisCount - 1) * _itemCrossSpacing) / 3).floorToDouble();
    _itemHeight = 120.pt;
  }

  @override
  void initState() {
    super.initState();
    getCtl.targetUid = widget.targetUid;
    getCtl.from = widget.from;
  }

  @override
  Widget build(BuildContext context) {
    _culItemSize(context);
    return buildGetWidget(context);
  }

  Widget buildStatusContent(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth,
      height: screenWidth * 680 / 375,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned.fill(
            child: FLImage.asset(
              Res.roomGiftGalleryBg,
              fit: BoxFit.cover,
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 18.pt, right: 18.pt, top: 18.pt),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _topView(),
                _rank(),
                Expanded(child: _list()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _topView() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: _title()),
        SizedBox(width: 16.pt,),

        GestureDetector(
          onTap: getCtl.onTapRule,
          child: Container(
            height: 30.pt,
            alignment: Alignment.center,
            child: FLImage.asset(
              Res.roomIntroduction,
              width: 18.pt,
              height: 18.pt,
            ),
          ),
        ),
      ],
    );
  }

  Widget _title() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              Color(0xFFFFFFAA),
              Color(0xFFFFFFFF),
              Color(0xFFEB97CC),
            ],
            tileMode: TileMode.mirror,
          ).createShader(bounds),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  getCtl.targetUserInfo?.nickname ?? '',
                  style: TextStyle(
                    fontSize: 21.pt,
                    fontWeight: FontWeightExt.heavy,
                    color: Colors.white,
                    overflow: TextOverflow.ellipsis,
                    height: 1.1,
                  ),
                ),
              ),
              Text(
                LocaleStrings.instance.whos(""),
                style: TextStyle(
                  fontSize: 21.pt,
                  fontWeight: FontWeightExt.heavy,
                  color: Colors.white,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                ' ${LocaleStrings.instance.giftGallery.toLowerCase()}',
                style: TextStyle(
                  fontSize: 21.pt,
                  fontWeight: FontWeightExt.heavy,
                  color: Colors.white,
                  overflow: TextOverflow.ellipsis,
                ),
              )
            ],
          ),
        ),
        Text(
          LocaleStrings.instance.giftGalleryListSubtitle,
          softWrap: true,
          style: TextStyle(
            fontSize: 11.pt,
            fontWeight: FontWeightExt.medium,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _rank() {
    return GestureDetector(
      onTap: getCtl.onTapGiftRank,
      child: Container(
        height: 32.pt,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.pt),
          color: R.color.primaryColor
        ),
        margin: EdgeInsets.only(top: 15.pt, bottom: 8.pt),
        padding: EdgeInsets.symmetric(horizontal: 13.pt),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              LocaleStrings.instance.giftLightUpRank,
              style: TextStyle(
                fontSize: 11.pt,
                fontWeight: FontWeightExt.medium,
                fontStyle: FontStyle.italic,
                color: Colors.white,
              ),
            ),
            SizedBox(width: 7.pt,),
            FLImage.asset(
              Res.commonArrowRightWhite,
              width: 12.pt,
              height: 12.pt,
            )
          ],
        ),
      ),
    );
  }

  Widget _list() {
    if (getCtl.list.isNotEmpty) {
      List<Widget> widgets = [];
      for (var element in getCtl.list) {
        widgets.add(
          SliverToBoxAdapter(
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.only(top: 13.pt, bottom: 13.pt),
              child: _header(element),
            ),
          ),
        );

        widgets.add(
          SliverPadding(
            padding: EdgeInsets.only(bottom: 19.pt),
            sliver: SliverGrid.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _crossAxisCount,
                childAspectRatio: _itemWidth / _itemHeight,
                crossAxisSpacing: _itemCrossSpacing,
                mainAxisSpacing: 13.pt,
              ),
              itemCount: element.giftList.length,
              itemBuilder: (BuildContext context, int index) {
                GiftListItem item = element.giftList[index];
                return _giftItem(item);
              },
            ),
          ),
        );
      }

      return CustomScrollView(
        scrollBehavior: OverScrollBehavior(),
        slivers: widgets,
      );
    }
    return SizedBox.shrink();
  }

  Widget _header(GiftGalleryListItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        FLImage.asset(
          Res.roomGiftGallerySectionLeft,
          width: 26.pt,
          height: 26.pt,
        ),
        SizedBox(width: 3.pt,),
        Text(
          "${item.title}(${item.hasLightedNum}/${item.total})",
          style: TextStyle(
            fontSize: 18.pt,
            fontWeight: FontWeightExt.medium,
            fontStyle: FontStyle.italic,
            color: Colors.white,
          ),
        ),
        SizedBox(width: 3.pt,),
        FLImage.asset(
          Res.roomGiftGallerySectionRight,
          width: 26.pt,
          height: 26.pt,
        ),
      ],
    );
  }

  Widget _giftItem(GiftListItem item) {
    return GestureDetector(
      onTap: () {
        getCtl.onClickGift(item);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(9.pt),
          topRight: Radius.circular(36.pt),
          bottomLeft: Radius.circular(24.pt),
          bottomRight: Radius.circular(24.pt),
        ),
        child: Container(
          decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.96),
                  Colors.white.withOpacity(0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
          ),
          child: Column(
            children: [

              item.hasLighted ?
              ColorFiltered(
                colorFilter: ColorFilter.matrix([
                  1, 0, 0, 0, 0,
                  0, 1, 0, 0, 0,
                  0, 0, 1, 0, 0,
                  0, 0, 0, 1, 0,
                ]),
                child: CachedNetworkImage(
                  imageUrl: item.giftInfo.icon ?? '',
                  width: 66.pt,
                  height: 66.pt,
                ),
              ) : ColorFiltered(
                colorFilter: ColorFilter.matrix([
                  0.2126, 0.7152, 0.0722, 0, 0,
                  0.2126, 0.7152, 0.0722, 0, 0,
                  0.2126, 0.7152, 0.0722, 0, 0,
                  0,      0,      0,      1, 0,
                ]),
                child: CachedNetworkImage(
                  imageUrl: item.giftInfo.icon ?? '',
                  width: 66.pt,
                  height: 66.pt,
                ),
              ),

              SizedBox(width: 3.pt,),

              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.pt),
                child: Text(
                  item.giftInfo.name ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 11.pt,
                    fontWeight: FontWeightExt.heavy,
                    color: R.color.textColor2,
                    fontStyle: FontStyle.italic
                  ),
                ),
              ),
              Spacer(),
              _bottom(item)
            ],
          ),
        ),
      ),
    );
  }

  Widget _bottom(GiftListItem item) {
    Widget widget;
    if (item.namingUser != null) {
      widget = _username(item);
    } else {
      widget = _sendGiftCount(item);
    }

    return Container(
      height: 30.pt,
      alignment: Alignment.center,
      color: R.color.primaryColor.withOpacity(0.2),
      padding: EdgeInsets.symmetric(horizontal: 12.pt),
      child: widget,
    );
  }

  Widget _sendGiftCount(GiftListItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        FLImage.asset(
          Res.roomGiftGalleryDefaultAvatar,
          width: 18.pt,
          height: 18.pt,
        ),
        SizedBox(width: 3.pt),
        Text(
          '${item.completeNum}/${item.requireNum}',
          style: TextStyle(
            fontSize: 11.pt,
            fontWeight: FontWeightExt.heavy,
            color: R.color.primaryColor,
          ),
        ),
        SizedBox(width: 4.pt),
        FLImage.asset(
          Res.roomGiftGalleryItemArrow,
          width: 4.pt,
          height: 7.pt,
        ),
      ],
    );
  }

  Widget _username(GiftListItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        UserAvatar(
          url: item.namingUser?.avatar,
          avatarCode: item.namingUser?.avatarCode,
          size: 18.pt,
        ),
        SizedBox(width: 3.pt),
        Flexible(
          child: Text(
            item.namingUser?.nickname ?? '',
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 11.pt,
              fontWeight: FontWeightExt.heavy,
              color: R.color.primaryColor,
              height: 1.1,
            ),
          ),
        ),
        SizedBox(width: 4.pt),
        FLImage.asset(
          Res.roomGiftGalleryItemArrow,
          width: 4.pt,
          height: 7.pt,
        ),
      ],
    );
  }
}




