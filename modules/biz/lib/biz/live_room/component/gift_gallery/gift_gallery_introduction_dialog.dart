
import 'package:biz/biz.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;


void showGiftGalleryRuleDialog() {
  dialog.showBottomSheet((_) =>
      GiftGalleryIntroduction(),
      routeSettings: RouteSettings(name: R_GIFT_GALLERY_RULE_DIALOG),
      barrierColor: Colors.transparent,
      enableDrag: true
  );
}

class GiftGalleryIntroduction extends StatelessWidget {
  const GiftGalleryIntroduction({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth,
      height: screenWidth * 680 / 375,
      child: Stack(
        children: [
          Positioned.fill(
            child: FLImage.asset(
              Res.roomGiftGalleryIntroBg,
              fit: BoxFit.cover,
            ),
          ),
          _topView(),
          Positioned(
            top: 45.pt,
            left: 0,
            right: 0,
            bottom: 0,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(left: 18.pt, right: 18.pt, bottom: 20.pt),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _header(LocaleStrings.instance.giftGalleryIntroIlluminationSectionTitle),
                  _ruleContent(LocaleStrings.instance.giftGalleryIntroIlluminationRuleContent),
                  _header(LocaleStrings.instance.giftGalleryIntroNameSectionTitle),
                  _ruleContent(LocaleStrings.instance.giftGalleryIntroNameRuleContent),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _topView() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _backBtn(),
        Flexible(child: _title()),
        SizedBox(width: 10.pt,)
      ],
    );
  }

  Widget _backBtn() {
    return GestureDetector(
      onTap: () {
        routerUtil.pop();
      },
      child: Container(
        width: 56.pt,
        height: 56.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
            Res.commonBack,
            color: Colors.white,
            width: 26.pt,
            height: 26.pt
        ),
      ),
    );
  }

  Widget _title() {
    return Text(
      LocaleStrings.instance.giftGalleryIntroductionTitle,
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontSize: 18.pt,
        fontWeight: FontWeightExt.heavy,
        color: Colors.white,
      ),
    );
  }

  Widget _header(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 7.pt),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FLImage.asset(
            Res.roomGiftGallerySectionLeft,
            width: 23.pt,
            height: 23.pt,
          ),
          SizedBox(width: 3.pt,),
          ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              colors: [
                Color(0xFFFFFFAA),
                Color(0xFFFFFFFF),
                Color(0xFFEB97CC),
              ],
              tileMode: TileMode.mirror,
            ).createShader(bounds),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeightExt.black,
                fontStyle: FontStyle.italic,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _ruleContent(String content) {
    return Text(
      content,
      softWrap: true,
      style: TextStyle(
        fontSize: 13.pt,
        fontWeight: FontWeightExt.medium,
        color: Colors.white.withOpacity(0.63),
      ),
    );
  }
}
