import 'package:flutter/material.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';

import 'widgets/enter_banner_widget.dart';

class UserCarWidget extends StatelessWidget {
  final PbEnterRoomCarNotice? notice;
  final SvgaController bannerController;
  final Animation<Offset> bannerOffsetAnimation;
  final Animation<double> bannerAlphaAnimation;

  UserCarWidget({
    required this.bannerController,
    required this.bannerOffsetAnimation,
    required this.bannerAlphaAnimation,
    this.notice,
  });

  @override
  Widget build(BuildContext context) {
    if (_isInvalid()) {
      return SizedBox.shrink();
    }
    var bottom = MediaQuery.of(context).padding.bottom;
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        SizedBox.expand(),
        if (roomService.getCurrentRoom()?.isGameMode == true)
          Positioned(
            left: 0,
            right: 0,
            bottom: bottom + 65.pt,
            child: RepaintBoundary(
              child: _enterBannerWidget(),
            ),
          ),
        if (roomService.getCurrentRoom()?.isGameMode == false)
          Align(
            alignment: Alignment.centerLeft,
            child: RepaintBoundary(
              child: _enterBannerWidget(),
            ),
          )
      ],
    );
  }

  Widget _enterBannerWidget() {
    return EnterBannerWidget(
      url: notice!.car.streamerUrl,
      bannerAlphaAnimation: bannerAlphaAnimation,
      bannerOffsetAnimation: bannerOffsetAnimation,
      bannerSvgaController: bannerController,
      user: notice!.user,
    );
  }

  bool _isInvalid() {
    return notice?.car.streamerUrl.isEmpty ?? true;
  }
}
