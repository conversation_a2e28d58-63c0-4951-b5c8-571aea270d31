import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_panel/event/gift_panel_event.dart';
import 'package:biz/biz/live_room/component/gift_panel/free_gift/free_gift_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_banner_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_probability_task_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/select_user/select_gift_user_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_shop_tab_widget.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/styled_tab_indicator.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:service/modules/gift/const/enums.dart';

import 'model/send_mic_menu.dart';

// bool kGiftPanelShowing = false;

/// 礼物面板来源
enum GiftPanelSource {
  // 房间 （为默认值）
  room,
  // 聊天 （页面）
  chat,
  // 群聊
  familyChat,
}

void showGiftPanelDialog({
  String? targetUid,
  String defaultTab = GiftTabType.gift,
  bool showGiftNewerGuide = false,
  bool isUnlockChat = false,
  GiftPanelSource source = GiftPanelSource.room,
  String? groupId,
  BuildContext? context,
}) {
  routerUtil.removeAll(R_LIVE_ROOM_GIFT);

  DialogScheduler.instance().schedule(
        () => dialog.showBottomSheet(
            (_) => GiftPanelWidget(
          targetUid: targetUid,
          defaultTab: defaultTab,
          showGiftNewerGuide: showGiftNewerGuide,
          fromSource: source,
          isUnlockChat: isUnlockChat,
          groupId: groupId,
        ),
        routeSettings: RouteSettings(name: R_LIVE_ROOM_GIFT),
        barrierColor: Colors.transparent),
    context: context,
  );
}

class GiftPanelWidget extends StatefulWidget {
  final String? targetUid;
  final String defaultTab;
  final bool showGiftNewerGuide;
  final GiftPanelSource fromSource;
  final bool isUnlockChat;
  /// 群聊会话id
  final String? groupId;

  GiftPanelWidget(
      {Key? key,
      this.targetUid,
      this.defaultTab = GiftTabType.gift,
      this.showGiftNewerGuide = false,
      this.isUnlockChat = false,
      this.groupId,
      required this.fromSource});

  @override
  State<StatefulWidget> createState() => _GiftPanelWidgetState();
}

class _GiftPanelWidgetState extends State<GiftPanelWidget> with SingleTickerProviderStateMixin {
  late GiftPanelController _controller;

  Widget? selectUserWidget;

  @override
  void initState() {
    super.initState();
    // kGiftPanelShowing = true;
    _controller = GiftPanelController(
      tickerProvider: this,
      targetUid: widget.targetUid,
      defaultTab: widget.showGiftNewerGuide ? GiftTabType.backpack : widget.defaultTab,
      fromSource: widget.fromSource,
      targetId: widget.targetUid,
      isUnlockChat: widget.isUnlockChat,
      groupId: widget.groupId,
    );
  }

  @override
  void dispose() {
    // kGiftPanelShowing = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
          init: _controller,
          global: false,
          autoRemove: false,
          builder: (controller) {
            return ClipRRect(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(21.pt), topRight: Radius.circular(21.pt)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _giftBanner(),
                  _selectUserWidget(),
                  _topView(),
                  Obx(_contentView),
                  _bottomView(),
                ],
              ),
            );
          },
        ));
  }

  Widget _giftBanner() {
    return GetBuilder(
        init: _controller,
        id: _controller.giftBannerTag,
        global: false,
        builder: (controller) {
          return _controller.showGiftBanner
              ? Container(
                  height: 44.pt,
                  margin: EdgeInsets.only(bottom: 5.pt),
                  child: GiftBannerWidget(_controller.selectGift!))
              : SizedBox(width: 1.w, height: 49.pt);
        });
  }

  Widget _selectUserWidget() {
    if (_controller.isFamilyChat == false) {
      return SizedBox();
    }
    if (selectUserWidget != null) {
      return selectUserWidget!;
    }
    var _list = <String>[];
    _list.addAll(_controller.uidList?.toList() ?? []);
    if (_list.isNotEmpty) {
      selectUserWidget = ClipRRect(
        borderRadius:
        BorderRadius.only(topLeft: Radius.circular(12.pt), topRight: Radius.circular(12.pt)),
        child: Container(
          color: Colors.black,
          child: SelectGiftUserWidget(
            uidList: _list,
            selectedUid: _list?.firstOrNull,
            selectCallBack: (userInfo){
              _controller.setSelectedUser(userInfo.uid);
            },
          ),
        ),
      );
    }
    return selectUserWidget ?? SizedBox();
  }

  Widget _bottomView() {
    if (_controller.isFamilyChat == false) {
      return SizedBox();
    }
    return Container(
      height: 50.pt,
      color: Colors.black,
      child: Row(
        children: [
          _coinView(),
          GlobalWidgets.spacingHorizontal(10.pt),
          _diamondView(),
          Expanded(child: Container()),
          _sendWidget(),
          GlobalWidgets.spacingHorizontal(14.pt),
        ],
      ),
    );
  }

  Widget _sendWidget() {
    return GestureDetector(
      onTap: () {
        _controller.quickSendGift();
      },
      child: Container(
        height: 32.pt,
        width: 94.pt,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.pt),  // 圆角
          gradient: LinearGradient(
            colors: [Color(0xFF628FFE), Color(0xFFB959EB), Color(0xFFE468B9), Color(0xFFFE9494)],  // 渐变颜色
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          LocaleStrings.instance.send,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _topView() {
    double height = 36;
    return Obx(() => Visibility(
          visible: _controller.showSelectSendUser.value,
          child: Container(
            height: height,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned(
                    top: 0,
                    left: 0,
                    bottom: 0,
                    child: FLImage.asset(Res.roomGiftPanelSendUserBg1, width: 24, height: height, fit: BoxFit.fill)),
                Positioned(
                    top: 0,
                    left: 24,
                    right: 36,
                    bottom: 0,
                    child: FLImage.asset(Res.roomGiftPanelSendUserBg2, fit: BoxFit.fill)),
                Positioned(
                    top: 0,
                    right: 0,
                    bottom: 0,
                    child: FLImage.asset(Res.roomGiftPanelSendUserBg3, width: 36, height: height, fit: BoxFit.fill)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GlobalWidgets.spacingHorizontal(13.pt.ceilToDouble()),
                    Text(
                      LocaleStrings.instance.send,
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeightExt.heavy,
                        color: R.color.textColor4,
                      ),
                    ),
                    GlobalWidgets.spacingHorizontal(5.pt.ceilToDouble()),
                    _sendGiftSelectUserView(),
                    GlobalWidgets.spacingHorizontal(36.pt.ceilToDouble()),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  Widget _giftExpandView() {
    if (!_controller.showGiftExpandView) return SizedBox.shrink();

    if (_controller.showGiftExpandInfo?.freeTaskGift == true) {
      if (roomGiftService.getFreeGiftList() != null || roomGiftService.getFreeGiftList()?.isNotEmpty == true) {
        return FreeGiftWidget();
      }
    }

    if (_controller.showGiftExpandInfo?.isProbability == true) {
      return GiftProbabilityTaskWidget(giftInfo: _controller.showGiftExpandInfo);
    }

    return SizedBox.shrink();
  }

  Widget _contentView() {
    bool isExistTopView = _controller.showSelectSendUser.value;
    return ClipRRect(
        borderRadius: _controller.isFamilyChat ? BorderRadius.zero :
            BorderRadius.only(topLeft: Radius.circular(isExistTopView ? 0 : 12.pt), topRight: Radius.circular(12.pt)),
        child: Container(
          color: Colors.black,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.fromSource == GiftPanelSource.room)
                GetBuilder(
                    id: _controller.giftExpandViewTag,
                    init: _controller,
                    global: false,
                    builder: (controller) {
                      return _giftExpandView();
                    }),
              _gitContentView(),
            ],
          ),
        ));
  }

  Widget _sendGiftSelectUserView() {
    double screenWidth = MediaQuery.of(context).size.width * 2 / 3;
    return GestureDetector(
      onTap: _controller.onClickSelectUser,
      child: Obx(() => Container(
            height: 24.pt,
            decoration: BoxDecoration(color: Color(0xFF252525), borderRadius: BorderRadius.circular(12.pt)),
            constraints: BoxConstraints(
              maxWidth: screenWidth - 45 - 36.pt,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GlobalWidgets.spacingHorizontal(3.pt),
                _sendGiftSelectUserAvatarView(),
                GlobalWidgets.spacingHorizontal(3.pt),
                Flexible(
                  child: Text(
                    _controller.selectedSendUser.value.name ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeightExt.medium,
                      color: R.color.primaryLightColor,
                    ),
                  ),
                ),
                GlobalWidgets.spacingHorizontal(5.pt),
                FLImage.asset(Res.roomGiftPanelSelectedUserArrow, width: 11.pt, height: 11.pt),
                GlobalWidgets.spacingHorizontal(5.pt),
              ],
            ),
          )),
    );
  }

  Widget _sendGiftSelectUserAvatarView() {
    bool allMicUser = _controller.selectedSendUser.value.targetId == allMic.targetId;
    if (allMicUser) {
      return FLImage.asset(Res.roomGiftPanelIconAllUser, width: 18.pt, height: 18.pt);
    }

    if (_controller.selectedSendUser.value.targetId != null) {
      return UserAvatar(
        url: _controller.selectedSendUser.value.avatar ?? '',
        avatarCode: _controller.selectedSendUser.value.avatarCode,
        size: 18.pt,
      );
    }

    return SizedBox.shrink();
  }

  Widget _gitContentView() {
    return Container(
      height: 306.pt,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _tabBarContainerView(),
          // Divider(color: Colors.white.withOpacity(0.1), height: 1),
          Expanded(child: _tabBarView())
        ],
      ),
    );
  }

  Widget _tabBarContainerView() {
    final List<Widget> list = _controller.isFamilyChat ?
        [Expanded(child: _tabBar()), GlobalWidgets.spacingHorizontal(13.pt)] :
    [Expanded(child: _tabBar()),
      _coinView(),
      GlobalWidgets.spacingHorizontal(10.pt),
      _diamondView(),
      GlobalWidgets.spacingHorizontal(13.pt)];
    return Container(
      height: 35.pt,
      color: Colors.black,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: list
      ),
    );
  }

  Widget _tabBar() {
    if (_controller.tabController == null) return SizedBox.shrink();
    List<Tab> tabs = _controller.giftPanelTabs
        .map(
          (e) => Tab(
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Text(e.name ?? ""),
                if (e.showRedDot)
                  PositionedDirectional(
                    top: -3.pt,
                    end: -4.pt,
                    child: Container(
                      width: 6.pt,
                      height: 6.pt,
                      decoration: BoxDecoration(
                        color: R.color.warningColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        )
        .toList();

    return TabBar(
        controller: _controller.tabController,
        padding: EdgeInsets.only(left: 5.pt),
        isScrollable: true,
        unselectedLabelStyle:
            TextStyle(color: Colors.white.withOpacity(0.5), fontSize: 16.sp, fontWeight: FontWeightExt.medium),
        labelStyle: TextStyle(color: Colors.white, fontSize: 16.sp, fontWeight: FontWeightExt.heavy),
        indicatorPadding: EdgeInsets.symmetric(vertical: 4.pt),
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.symmetric(horizontal: 13.pt),
        indicator: StyledTabIndicator(margin: 8.pt, width: 20.pt, height: 3.pt, fillColor: Colors.white),
        tabs: tabs);
  }

  Widget _tabBarView() {
    if (_controller.tabController == null) return SizedBox.shrink();
    List<Widget> tabs = _controller.giftPanelTabs
        .map((e) => GiftShopTabWidget(
              type: e.type,
              panelController: _controller,
              comboWidgetController: _controller.comboWidgetController,
              showGiftNewerGuide: widget.showGiftNewerGuide,
              fromSource: widget.fromSource,
              isUnlockChat: widget.isUnlockChat,
              targetUid: widget.targetUid,
            ))
        .toList();

    return DecoratedBox(
      position: DecorationPosition.foreground,
      decoration: BoxDecoration(
          gradient: LinearGradient(
        colors: [Colors.transparent, Colors.transparent, Color(0xFF010101)],
        stops: [0, 0.9, 1],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      )),
      child: TabBarView(controller: _controller.tabController, children: tabs),
    );
  }

  Widget _diamondView() {
    return GestureDetector(
      onTap: () => _controller.gotoRecharge(),
      child: Container(
        height: 24.pt,
        padding: EdgeInsets.symmetric(horizontal: 8.pt),
        decoration: BoxDecoration(color: Colors.white.withOpacity(0.1), borderRadius: BorderRadius.circular(12.pt)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FLImage.asset(Res.financeDiamond, width: 16.pt, height: 16.pt),
            GlobalWidgets.spacingHorizontal(3.pt),
            Obx(() {
              final diamond = _controller.diamond.value;
              return Text(
                '${NumFormatUtils.numFormat(diamond)}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: diamond >= 1000 ? 11.sp : 12.sp,
                  fontWeight: FontWeightExt.heavy,
                  color: R.color.textColor4,
                ),
              );
            }),
            GlobalWidgets.spacingHorizontal(5.pt),
            FLImage.asset(Res.roomGiftPanelDiamondArrow, width: 11.pt, height: 11.pt),
          ],
        ),
      ),
    );
  }

  Widget _coinView() {
    return GestureDetector(
      onTap: () => _controller.gotoRecharge(index: 1),
      child: Container(
        height: 24.pt,
        padding: EdgeInsets.symmetric(horizontal: 8.pt),
        decoration: BoxDecoration(color: Colors.white.withOpacity(0.1), borderRadius: BorderRadius.circular(12.pt)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FLImage.asset(Res.financeIconExchangeCoin, width: 16.pt, height: 16.pt),
            GlobalWidgets.spacingHorizontal(3.pt),
            Obx(() {
              final coin = _controller.coin.value;
              return Text(
                '${NumFormatUtils.numFormat(coin)}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: coin >= 1000 ? 11.sp : 12.sp,
                  fontWeight: FontWeightExt.heavy,
                  color: R.color.textColor4,
                ),
              );
            }),
            GlobalWidgets.spacingHorizontal(5.pt),
            FLImage.asset(Res.roomGiftPanelDiamondArrow, width: 11.pt, height: 11.pt),
          ],
        ),
      ),
    );
  }
}
