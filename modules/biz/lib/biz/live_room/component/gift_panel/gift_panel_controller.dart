import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge/recharge_dialog.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/combo_statistics_mixin.dart';
import 'package:biz/biz/live_room/component/gift_panel/event/gift_panel_event.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_select_user_panel.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/gift_item_combo_widget_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/statistic/gift_statistics_mixin.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/global/widgets/no_enough_money_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/gift_statistics.g.dart';
import 'package:service/modules/finance/const/events.dart';
import 'package:service/modules/finance/model/wallet.dart';
import 'package:service/modules/gift/const/enums.dart';
import 'package:service/modules/gift/handler/gift_cache_manager.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/gift/model/gift_tab_config.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/live/mic_seat/model/mic_seat_model.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room_gift/model/send_gift_resp.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/modules/mall/model/goods_detail.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';
import 'package:biz/route/page_time_mixin.dart';

import 'model/send_mic_menu.dart';

class GiftPanelController extends GetxController
    with GetBindingMixin, GetEventMixin, ComboStatisticsMixin, GiftStatisticsMixin, PageTimingMixin {
  static const String pageKey = "gift_panel";

  String? targetUid;

  TabController? tabController;
  TickerProvider tickerProvider;

  List<GiftTabModel> giftPanelTabs = [];
  List<SelectMicModel> listAllMicAndRoom = [];
  List<SelectMicModel> _allMics = [];

  /// 群聊信息
  String? groupId;
  List<String>? uidList;
  String? selectedUid;

  final selectedSendUser = SelectMicModel().obs;

  final diamond = 0.obs;
  final coin = 0.obs;

  //选择送礼人控件是否可以点击，从个人面板过来，不可点击
  final enableSelectSendUser = true.obs;

  //是否显示选择送礼人控件
  final showSelectSendUser = true.obs;

  //是否显示礼物面板扩展视图
  bool showGiftExpandView = false;
  GiftInfo? showGiftExpandInfo;

  String giftBannerTag = "gift_banner";
  String giftExpandViewTag = "gift_expand_banner";
  bool showGiftBanner = false;

  bool hasTapSend = false;
  int _lastTapTime = 0;

  GiftInfo? selectGift;
  GiftInfo? showComboGift;

  BackpackGift? selectBackpackGift;
  BackpackGoodsDetail? selectedGoods;

  final GiftItemComboWidgetController comboWidgetController = GiftItemComboWidgetController();

  bool isStartCombo = false;
  String startComboTab = '';

  //赋值后不再设置为null 由 isStartCombo 来代替，防止异步导致读取错误
  SendGiftResp? lastSendGiftInfo;
  UserInfo? userInfo;

  final String defaultTab;
  final GiftPanelSource fromSource;
  String currentTab;

  final bool isUnlockChat;
  final String? targetId;

  bool get isChat {
    return fromSource == GiftPanelSource.chat || fromSource == GiftPanelSource.familyChat;
  }

  bool get isFamilyChat {
    return fromSource == GiftPanelSource.familyChat;
  }

  GiftPanelController({
    required this.tickerProvider,
    required this.fromSource,
    this.targetUid,
    this.defaultTab = GiftTabType.gift,
    this.currentTab = GiftTabType.gift,
    this.isUnlockChat = false,
    this.targetId,
    this.groupId,
  });

  @override
  void stateInit() {
    super.stateInit();

    currentSelectUid = targetUid ?? "";
    from = fromSource == GiftPanelSource.room ? StatisticPageFrom.liveRoom : StatisticPageFrom.chat;
    roomId = roomService.getCurrentRoomId();
    roomMode = roomService.getRoomMode();

    startTiming(key: pageKey);

    _initRx();
    _initWalletData();
    _initTab();
    _initUserData();
    _initGroupData();
    reportPanelShow(GiftTypeStatistic.shopGifts);
  }

  @override
  void stateDispose() {
    _reportGiftBoardDuration();
    super.stateDispose();
  }

  void _reportGiftBoardDuration() async {
    stopTiming(key: pageKey);
    String onTime = getOnTime(key: pageKey);
    removeTimer(key: pageKey);
    reportGiftBoardDuration(onTime);
  }

  void _initRx() {
    listenRxEvent<Wallet>(FinanceEvent.walletChange, _walletChange);
    listenRxEvent<String>(GiftPanelEvent.switchTab, _switchTab);
  }

  /// 初始化标签栏
  void _initTab() async {
    List<GiftTabModel> list = await giftService.getGiftPanelTabs(fromServer: true);

    /// 初始化小红点展现逻辑
    for (var item in list) {
      if (item.needRedPoint) {
        item.showRedDot = await GiftCacheManager.instance().showGiftTabRedDot(item.type);
      }
    }

    giftPanelTabs.addAll(list);
    if (isChat) {
      giftPanelTabs.removeWhere((element) => element.type == GiftTabType.goods);
    }
    int initialIndex = 0;

    for (GiftTabModel tabElement in list) {
      if (tabElement.type == defaultTab) {
        initialIndex = list.indexOf(tabElement);
        GiftCacheManager.instance().updateGiftTabAddTs(tabElement.type);
        tabElement.showRedDot = false;
        break;
      }
    }

    var selectedTabElement = giftPanelTabs.getSafeElement(initialIndex);
    if (selectedTabElement?.type != null) {
      currentTab = selectedTabElement!.type;
    }

    tabController = TabController(length: giftPanelTabs.length, vsync: tickerProvider, initialIndex: initialIndex);
    tabController?.addListener(() {
      if (tabController?.indexIsChanging == true) return;
      var index = tabController?.index ?? initialIndex;
      var tabElement = giftPanelTabs.getSafeElement(index);
      if (tabElement?.type != null) {
        _updateTabListSelected(willTab: tabElement!);
        currentTab = tabElement!.type;
        GiftCacheManager.instance().updateGiftTabAddTs(tabElement!.type);
        tabElement.showRedDot = false;
        update();
      }

      if (tabElement?.type == GiftTabType.gift) {
        rxUtil.send(GiftPanelEvent.clearSelected, tabElement?.type);
      }
    });

    update();
  }

  String _currentSelectTab() {
    return giftPanelTabs[tabController?.index ?? 0].type ?? '';
  }

  void _initWalletData() async {
    /// 我的钱包
    var wallet = await financeService.getMyWallet();
    diamond.value = wallet?.diamond ?? 0;
    coin.value = wallet?.coin ?? 0;
  }

  void _initGroupData() async {
    if (isFamilyChat == false) return;
    if (targetId?.isNotEmpty == true) {
      uidList = [targetId!];
    } else {
      uidList = (await imCacheService.getGroupInfo(targetId: groupId ?? ""))
          ?.userList
          ?.split(",") ??
          [];
    }
    final myUid = accountService.currentUid();
    uidList = uidList?.where((uid) => uid != myUid).toList();
    selectedUid = uidList?.firstOrNull;
    update();
  }

  /// 在房间的时候，获取麦上列表，并默认选中某个麦上成员
  void _initUserData() async {
    SelectMicModel? selected;

    List<SelectMicModel> micList = await getAllReceiver();

    /// 全麦&全房
    var _allMic = SelectMicModel(targetId: allMic.targetId, name: LocaleStrings.instance.allOnMic);

    listAllMicAndRoom.add(_allMic);
    listAllMicAndRoom.addAll(micList);

    if (micList.isNotEmpty) {
      selected = micList.first;
    }

    if (targetUid?.isNotEmpty ?? false) {
      selected = listAllMicAndRoom.firstWhereOrNull((element) => element.targetId == targetUid!);

      if (selected == null) {
        selected = await getChooseSend();
      }
    }
    _allMics.addAll(micList);
    if (selected != null) {
      showSelectSendUser.value = true;
      selectedSendUser.value = selected;
    } else {
      showSelectSendUser.value = false;
    }

    // 聊天页面弹出礼物面板不展示，顶部选择送礼人的view
    if (isChat) {
      showSelectSendUser.value = false;
    }
  }

  Future<List<SelectMicModel>> getAllReceiver() async {
    List<SelectMicModel> modelList = [];
    final mics = await micSeatService.fetchMicSeat();
    String? currentUserUid = accountService.currentUid();
    for (MicSeatItem item in mics ?? []) {
      if (item.seatHadUser && item.uid?.isNotEmpty == true && item.uid != currentUserUid) {
        modelList.add(SelectMicModel(targetId: item.uid, name: item.nickname, avatar: item.headImgUrl));
      }
    }
    return modelList;
  }

  Future<SelectMicModel?> getChooseSend() async {
    var user = await userService.getUserInfo(targetUid!);
    SelectMicModel chooseSend = SelectMicModel(targetId: user?.uid, name: user?.nickname, avatar: user?.avatar);
    return chooseSend;
  }

  void onClickSelectUser() {
    showGiftSelectUserPanelDialog(
        list: listAllMicAndRoom,
        lastSelectedUid: selectedSendUser.value.targetId,
        onSelected: (SelectMicModel model) {
          selectedSendUser.value = model;
        });
  }

  void _walletChange(Wallet wallet) {
    diamond.value = wallet.diamond ?? 0;
    coin.value = wallet.coin ?? 0;
  }

  void setSelectGift(GiftInfo? giftInfo) {
    if (selectGift != null && selectGift?.id != giftInfo?.id) ;
    {
      endCombo();
    }
    selectGift = giftInfo;

    if (selectGift != null &&
        selectGift?.giftType == GiftType.basic &&
        selectGift?.bannerInfo?.desc?.isNotEmpty == true) {
      showGiftBanner = true;
    } else {
      showGiftBanner = false;
    }
    showGiftExpandView = (selectGift?.freeTaskGift ?? false) || (selectGift?.isProbability ?? false);

    if (showGiftExpandView) {
      showGiftExpandInfo = selectGift;
    } else {
      showGiftExpandInfo = null;
    }
    update([giftBannerTag, giftExpandViewTag]);
  }

  void setSelectBackpackGift(BackpackGift? backpackGift, {bool isEndCombo = true}) {
    if (isEndCombo) {
      if (selectBackpackGift != null && selectBackpackGift?.bagId != backpackGift?.bagId) ;
      {
        endCombo();
      }
    }
    selectBackpackGift = backpackGift;
  }

  void setSelectGood(BackpackGoodsDetail? goods) {
    selectedGoods = goods;
  }

  void setSelectedUser(String? uid) {
    selectedUid = uid;
  }

  void quickSendGift() async {
    if (selectBackpackGift != null) {
      sendGift(tabType: GiftTabType.backpack, backpackGift: selectBackpackGift);
    } else {
      sendGift(tabType: GiftTabType.gift, gift: selectGift);
    }
  }

  /// 发送礼物
  void sendGift({required String tabType, GiftInfo? gift, BackpackGift? backpackGift}) async {
    hasTapSend = true;

    /// 防止重复点击
    var time = DateTime.now().millisecondsSinceEpoch;
    if (time - _lastTapTime < 800) {
      return;
    }
    _lastTapTime = time;

    ///是否被封禁
    if ((await hasBeRestricted(FLRouter.routeObserver.getLastContext()))) {
      return;
    }

    if (fromSource == GiftPanelSource.room) {
      /// 选择全麦时，无人在麦上
      List<SelectMicModel> micList = await getAllReceiver();

      if ((selectedSendUser.value.targetId == allMic.targetId && micList.isEmpty) ||
          selectedSendUser.value.targetId == null) {
        toast(LocaleStrings.instance.NoPeopleOnMic, isDark: true);
        return;
      }

      switch (tabType) {
        case GiftTabType.backpack:
          _sendBackpackGift(backpackGift);
          break;
        case GiftTabType.gift:
          _sendShopGiftWithCheck(gift);
          break;
      }
    } else {
      switch (tabType) {
        case GiftTabType.backpack:
          _sendChatGift(backpackGift: backpackGift);
          break;
        case GiftTabType.gift:
          _sendChatGift(gift: gift);
          break;
      }
    }
  }

  /// 发送商店礼物-检查余额
  void _sendShopGiftWithCheck(GiftInfo? gift) async {
    int selectNum = await _currentSelectNum();
    int giftBagCount = gift?.bagCount ?? 0;
    int giftPrice = gift?.price ?? 0;
    int totalPrice = selectNum * giftPrice;

    if (giftBagCount < selectNum) {
      int remainCost = (selectNum - giftBagCount) * giftPrice;
      final currencyType = gift?.priceType == CurrencyType.diamond.name ? CurrencyType.diamond : CurrencyType.goldCoin;

      bool enoughMoney = await checkMyWallet(price: remainCost, currencyType: currencyType);
      if (!enoughMoney) {
        reportGiftNoMoney(totalPrice, giftInfo: gift, style: "0");

        final index = currencyType == CurrencyType.goldCoin ? 1 : 0;
        gotoRecharge(index: index, sourceFrom: GiftFromStatistic.roomGift);
        return;
      }
    }

    _sendShopGift(gift);
  }

  /// 当前要送出的礼物数
  Future<int> _currentSelectNum() async {
    if (selectedSendUser.value.targetId == allMic.targetId) {
      final mics = await micSeatService.fetchMicSeat();
      int inMicNum = 0;
      var currentUid = accountService.getAccountInfo()?.uid;
      for (MicSeatItem item in mics ?? []) {
        if (item.seatHadUser && currentUid != item.uid) {
          inMicNum++;
        }
      }
      return inMicNum;
    }

    return 1;
  }

  /// 发送礼物
  Future<void> _sendShopGift(GiftInfo? selectedGift) async {
    var receivedIds = _receivedIds();
    int batchType = _getBatchType();

    var targetUser = await _currentSelectSingleUser();

    reportSendGiftStart("buy_send", 1,
        giftInfo: selectedGift, batchType: batchType, sexStr: targetUser?.sexStr, toUid: targetUser?.uid);

    DateTime startTime = DateTime.now();
    showLoading();
    var resp = isChat
        ? await imChatService.sendChatGift(
            giftId: selectedGift?.id ?? "",
            num: 1,
            fUid: receivedIds.join(","),
            comboId: '',
            isUnlockChat: isUnlockChat,
            groupId: groupId,
          )
        : await roomGiftService.sendGift(
            num: 1,
            batchType: batchType,
            giftId: selectedGift?.id ?? "",
            receivedIds: receivedIds,
            totalNum: 1,
            giftInfo: selectedGift);
    hideLoading();
    String endTime = DateTime.now().difference(startTime).inMilliseconds.toString();
    reportSendGiftEnd("buy_send", 1, resp, endTime,
        giftInfo: selectedGift, batchType: batchType, sexStr: targetUser?.sexStr, toUid: targetUser?.uid);

    if (resp.isSuccess) {
      if (resp.data != null) {
        _giftSendSuccess(resp.data!
          ..giftInfo = selectedGift
          ..from = from);
      }
      return;
    }
    _sendGiftFail(resp.code, msg: resp.msg, giftInfo: selectedGift, roomId: roomId, from: from);
  }

  List<String> _receivedIds() {
    if (fromSource == GiftPanelSource.chat) {
      return [targetUid ?? ""];
    }

    if (isFamilyChat) {
      return [selectedUid ?? ""];
    }

    List<String> list = [];

    /// 全麦
    if (selectedSendUser.value.targetId == allMic.targetId) {
      String? currentUserUid = accountService.currentUid();
      for (SelectMicModel item in _allMics) {
        if (item.targetId?.isNotEmpty == true && item.targetId != currentUserUid) {
          list.add(item.targetId!);
        }
      }
    } else if (selectedSendUser.value.targetId?.isNotEmpty ?? false) {
      list.add(selectedSendUser.value.targetId!);
    }
    return list;
  }

  /// 发送背包礼物
  void _sendBackpackGift(BackpackGift? backpackGift) async {
    /// 选中背包
    BackpackGift? selectedBackpack = backpackGift;

    /// 背包礼物
    GiftInfo? giftInfo = selectedBackpack?.info;

    /// 发送数量
    int selectNum = await _currentSelectNum();

    if (giftInfo?.id?.isEmpty ?? true) return;
    if (giftInfo == null) return;

    var targetUser = await _currentSelectSingleUser();

    reportSendClick(GiftTypeStatistic.backpackGifts, selectNum,
        giftInfo: giftInfo, batchType: _getBatchType(), sexStr: targetUser?.sexStr, toUid: targetUser?.uid);

    /// 背包礼物不足
    if ((selectedBackpack?.count ?? 0) < selectNum) {
      toast(LocaleStrings.instance.quantityShortage, isDark: true);
      return;
    }

    var receivedIds = _receivedIds();
    reportSendGiftStart("send", selectNum,
        giftInfo: giftInfo, batchType: _getBatchType(), sexStr: targetUser?.sexStr, toUid: targetUser?.uid);
    DateTime startTime = DateTime.now();
    showLoading();
    var resp = isChat
        ? await imChatService.sendChatGift(
            giftId: giftInfo.id ?? "",
            bagId: selectedBackpack?.bagId,
            batchType: _getBatchType(),
            num: 1,
            fUid: receivedIds.join(","),
            comboId: '',
            giftInfo: giftInfo,
            totalNum: selectNum,
            isUnlockChat: isUnlockChat,
            groupId: groupId)
        : await roomGiftService.sendGift(
            num: 1,
            bagId: selectedBackpack?.bagId,
            batchType: _getBatchType(),
            giftId: giftInfo.id ?? "",
            receivedIds: receivedIds,
            totalNum: selectNum,
            giftInfo: giftInfo);
    hideLoading();
    String endTime = DateTime.now().difference(startTime).inMilliseconds.toString();
    reportSendGiftEnd("send", selectNum, resp, endTime,
        giftInfo: giftInfo, batchType: _getBatchType(), sexStr: targetUser?.sexStr, toUid: targetUser?.uid);
    if (resp.isSuccess) {
      status = 1;

      if (resp.data != null) {
        _giftSendSuccess(resp.data!
          ..giftInfo = giftInfo
          ..from = from);
      }
    } else {
      _sendGiftFail(resp.code,
          msg: resp.msg, giftInfo: giftInfo, bagId: selectedBackpack?.bagId, from: from, roomId: roomId);
    }
  }

  void _giftSendSuccess(SendGiftResp? giftResp) async {
    if (giftResp?.batchType == 0) {
      userInfo = await userService.getUserInfo(giftResp?.receivedIds?[0] ?? "");
      comboUserInfo = userInfo;
    }
    lastSendGiftInfo = giftResp;
    if (lastSendGiftInfo?.giftInfo?.level != null &&
        lastSendGiftInfo!.giftInfo!.level! < 2 &&
        isChat == false) {
      if (lastSendGiftInfo?.badId == null) {
        showComboGift = lastSendGiftInfo?.giftInfo;
      }
      isStartCombo = true;
      startComboTab = _currentSelectTab();
    }
    // var targetEventType = fromSource == GiftPanelSource.room ? RoomGiftEvent.sendSuccess : ChatGiftEvent.sendSuccess;
    if (isChat) {
      giftResp?.isComboGift = isStartCombo;
      rxUtil.send(ChatGiftEvent.sendSuccess, giftResp);
    }
    rxUtil.send(RoomGiftEvent.sendSuccess, giftResp);
  }

  //结束combo 效果，隐藏combo按钮
  void endCombo() {
    if (!isStartCombo) return;

    if (lastSendGiftInfo?.giftInfo?.level != null && lastSendGiftInfo!.giftInfo!.level! < 2) {
      isStartCombo = false;
      startComboTab = '';
      if (isChat) {
        rxUtil.send(ChatGiftEvent.endCombo, lastSendGiftInfo);
      }
    }
  }

  void sendGiftCombo() async {
    if (lastSendGiftInfo == null ||
        (lastSendGiftInfo?.giftId?.isEmpty ?? true) && (lastSendGiftInfo?.badId?.isEmpty ?? true)) {
      return;
    }

    reportComboSendGiftStart(lastSendGiftInfo!);
    DateTime startTime = DateTime.now();
    var resp = fromSource == GiftPanelSource.room
        ? await roomGiftService.sendGift(
            num: lastSendGiftInfo?.num ?? 1,
            bagId: giftPanelTabs[tabController?.index ?? 0].type == GiftTabType.gift ? null : lastSendGiftInfo?.badId,
            batchType: lastSendGiftInfo?.batchType ?? 0,
            giftId: lastSendGiftInfo?.giftId ?? "",
            combo: 1 + (lastSendGiftInfo?.combo ?? 0),
            comboId: lastSendGiftInfo?.giftType == GiftType.blindbox ? null : lastSendGiftInfo?.comboId,
            receivedIds: lastSendGiftInfo?.receivedIds ?? [],
            totalNum: lastSendGiftInfo?.totalNum ?? 1,
            giftInfo: lastSendGiftInfo?.giftInfo)
        : await imChatService.sendChatGift(
            num: lastSendGiftInfo?.num ?? 1,
            bagId: giftPanelTabs[tabController?.index ?? 0].type == GiftTabType.gift ? null : lastSendGiftInfo?.badId,
            batchType: lastSendGiftInfo?.batchType ?? 0,
            giftId: lastSendGiftInfo?.giftId ?? "",
            combo: 1 + (lastSendGiftInfo?.combo ?? 0),
            comboId: lastSendGiftInfo?.giftType == GiftType.blindbox ? null : lastSendGiftInfo?.comboId,
            totalNum: lastSendGiftInfo?.totalNum ?? 1,
            giftInfo: lastSendGiftInfo?.giftInfo,
            fUid: lastSendGiftInfo?.receivedIds?.isNotEmpty == true ? lastSendGiftInfo?.receivedIds?.first ?? "" : "");

    String endTime = DateTime.now().difference(startTime).inMilliseconds.toString();
    reportComboSendGiftEnd(resp, lastSendGiftInfo!, endTime);
    reportComboSendGift(lastSendGiftInfo!);

    if (resp.isSuccess) {
      if (resp.data != null) {
        lastSendGiftInfo = resp.data?..giftInfo = lastSendGiftInfo?.giftInfo;
        _giftSendSuccess(lastSendGiftInfo);
        comboWidgetController.combo();
      }
    } else {
      _sendGiftFail(resp.code,
          msg: resp.msg,
          bagId: giftPanelTabs[tabController?.index ?? 0].type == GiftTabType.gift ? null : lastSendGiftInfo?.badId,
          giftInfo: lastSendGiftInfo?.giftInfo,
          cost: _cost(lastSendGiftInfo),
          batchType: lastSendGiftInfo?.batchType ?? 0,
          from: lastSendGiftInfo?.from,
          sexStr: comboUserInfo?.sexStr,
          toUid: comboUserInfo?.uid,
          roomId: roomService.getCurrentRoomId());
    }
  }

  int _getBatchType() {
    if (fromSource == GiftPanelSource.chat) {
      return 0;
    }

    // 0:单发 1:发在麦位 2:发在线用户
    if (selectedSendUser.value.targetId == allMic.targetId) {
      return 1;
    }

    return 0;
  }

  void _sendGiftFail(int code,
      {String? msg,
      GiftInfo? giftInfo,
      String? bagId,
      int cost = 0,
      String? from,
      String? toUid,
      int batchType = 0,
      String? sexStr,
      String? roomId}) {
    if (code == 1) return;

    /// 背包礼物
    if (bagId?.isNotEmpty ?? false) {
      toast(code == 1020 ? LocaleStrings.instance.quantityShortage : (msg ?? LocaleStrings.instance.failedToSend),
          isDark: true);
      return;
    }

    /// 商店礼物
    /// 金币不足
    else if (code == 1019) {
      GiftStatistics.reportGiftNomoneyPopout(
          cost: '$cost',
          content: giftInfo?.id,
          type: giftInfo?.priceTypeStr,
          from: from,
          toGender: sexStr,
          toUid: toUid,
          status: '${GiftTypeStatistic.getStatus(batchType)}',
          roomId: roomId);
      gotoRecharge(index: 1);
    }

    /// 单日送礼数上限
    else if (code == 1021) {
      toast(LocaleStrings.instance.giftUpperLimit, isDark: true);
    } else {
      toast(msg ?? LocaleStrings.instance.failedToSend, isDark: true);
    }
  }

  int _cost(SendGiftResp? lastSendGift) {
    var total = (lastSendGift?.totalNum ?? 1) * (lastSendGift?.giftInfo?.price ?? 0);
    return total;
  }

  Future<UserInfo?> _currentSelectSingleUser() async {
    var receivedIds = _receivedIds();
    if (_getBatchType() == 0 && receivedIds.isNotEmpty) {
      return await userService.getUserInfo(receivedIds[0]);
    }

    return null;
  }

  void gotoRecharge({int index = 0, String? sourceFrom}) {
    showRechargeDialog(from: sourceFrom ?? from, index: index);
  }

  void _switchTab(String tabType) {
    int index = 0;
    for (GiftTabModel tabElement in giftPanelTabs) {
      if (tabElement.type == tabType) {
        index = giftPanelTabs.indexOf(tabElement);
        break;
      }
    }
    tabController?.index = index;
  }

  void _updateTabListSelected({required GiftTabModel willTab}) {
    if (willTab.type != currentTab) {
      if (currentTab == GiftTabType.backpack) {
        selectBackpackGift = null;
      } else if (currentTab == GiftTabType.goods) {
        selectedGoods = null;
      }
    }
  }

  /// 发送私聊礼物
  void _sendChatGift({GiftInfo? gift, BackpackGift? backpackGift}) async {
    if (gift != null) {
      int giftPrice = gift.price ?? 0;
      int totalPrice = giftPrice;

      final currencyType = gift.priceType == CurrencyType.diamond.name ? CurrencyType.diamond : CurrencyType.goldCoin;
      bool enoughMoney = await checkMyWallet(price: gift.price ?? 0, currencyType: currencyType);
      if (!enoughMoney) {
        reportGiftNoMoney(totalPrice, giftInfo: gift, style: "0");
        // 金币不足唤起充值
        final index = currencyType == CurrencyType.goldCoin ? 1 : 0;
        gotoRecharge(index: index, sourceFrom: GiftFromStatistic.chatGift);
        return;
      }

      _sendShopGift(gift);
      return;
    }

    if (backpackGift != null) {
      _sendBackpackGift(backpackGift);
    }
  }
}
