import 'package:biz/biz.dart';
import 'package:biz/common/widgets/pop_menu/show_more_text_popup.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';

final allMic = SelectMicModel(targetId: "all_mic", name: LocaleStrings.instance.allOnMic);

final allInRoom =
    SelectMicModel(targetId: "all_in_room", name: LocaleStrings.instance.allInRoom);

class SelectMicModel {
  String? targetId;
  String? name;
  String? avatar;
  String? avatarCode;

  SelectMicModel({this.targetId, this.name, this.avatar, this.avatarCode});

  @override
  String toString() {
    return 'SelectMicModel{targetId: $targetId, name: $name, avatar: $avatar, avatarCode: $avatarCode}';
  }
}

/// 选择麦位送礼
class GiftSelectMicPopMenu {
  GiftSelectMicPopMenu._();

  static GiftSelectMicPopMenu? _instance;

  factory GiftSelectMicPopMenu.instance() =>
      _instance ??= GiftSelectMicPopMenu._();

  ShowMorePopup? _popup;

  void show(BuildContext context,
      {required GlobalKey widgetKey,
      required List<SelectMicModel> micList,
      Function(SelectMicModel)? selectCallback,
      double? width}) {
    if (micList.isEmpty) return;

    var rest = <Widget>[];

    for (int i = 0; i < micList.length; i++) {
      rest.add(_item(micList[i], width ?? 160.pt,
          showBorder: i > 0,
          background: micList[i].targetId == allMic.targetId ||
                  micList[i].targetId == allInRoom.targetId
              ? Color(0xFF9F2FE0).withOpacity(0.3)
              : null, callback: () {
        selectCallback?.call(micList[i]);
      }));
    }
    _popup = ShowMorePopup(context,
        child: Column(
          children: rest,
        ),
        width: width ?? 160.pt,
        bottom: 45.pt,
        padding: EdgeInsets.zero,
        backgroundColor: Color(0xFF555555),
        showShadow: false,
        isDownArrow: false,
        borderRadius: BorderRadius.circular(10.0));
    _popup?.show(widgetKey: widgetKey);
  }

  Widget _item(SelectMicModel model, double width,
      {bool showBorder = true, VoidCallback? callback, Color? background}) {
    return Column(
      children: [
        if (showBorder)
          Container(
            width: width,
            height: 0.5.pt,
            color: Color(0xFF808080),
          ),
        HfButton(
            height: 40.pt,
            width: width,
            color: background ?? Colors.transparent,
            onPressed: () {
              callback?.call();
              _popup?.dismiss();
            },
            child: Row(
              children: [
                SizedBox(width: 12.pt),
                Visibility(
                  visible: (model.avatar?.isNotEmpty ?? false) ||
                      (model.avatarCode?.isNotEmpty ?? false),
                  child: UserAvatar(
                      url: model.avatar,
                      avatarCode: model.avatarCode,
                      size: 23.pt),
                ),
                Visibility(
                  visible: (model.avatar?.isNotEmpty ?? false) ||
                      (model.avatarCode?.isNotEmpty ?? false),
                  child: SizedBox(width: 4.pt),
                ),
                Expanded(
                    child: Text(
                  '${model.name}',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontWeight: FontWeightExt.medium,
                      fontSize: 15.sp,
                      color: Colors.white),
                ))
              ],
            ))
      ],
    );
  }

  void dispose() {
    _popup?.dismiss();
  }
}
