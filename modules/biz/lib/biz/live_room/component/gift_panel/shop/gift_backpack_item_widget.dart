
import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/combo_button_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/gift_item_combo_widget_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_backpack_item_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:get/get.dart';
import 'package:service/modules/live/room_gift/model/send_gift_resp.dart';
class GiftBackpackItemWidget extends StatefulWidget {

  void Function(BackpackGift)? onSelected;
  void Function(BackpackGift)? onSend;

  VoidCallback? onComboSend;
  GiftItemComboWidgetController? comboWidgetController;
  GiftPanelController? giftPanelController;
  VoidCallback? comboEndCallback;

  String? type;
  Size itemSize;
  Size iconSize;
  double itemSpacing;
  BackpackGift info;
  bool isSelected;
  bool isShowGiftCombo;

  GiftBackpackItemWidget({
    Key? key,
    this.type,
    required this.info,
    this.itemSize = Size.zero,
    this.iconSize = Size.zero,
    this.itemSpacing = 0,
    this.onSelected,
    this.onSend,
    this.onComboSend,
    this.isSelected = false,
    this.isShowGiftCombo = false,
    this.comboEndCallback,
    this.giftPanelController,
    this.comboWidgetController
  }) : super(key: key);


  @override
  State<StatefulWidget> createState() {
    return _GiftBackpackItemWidgetState();
  }
}

class _GiftBackpackItemWidgetState extends State<GiftBackpackItemWidget> with SingleTickerProviderStateMixin {

  final GiftBackpackItemController _controller = GiftBackpackItemController();

  late AnimationController _scaleController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller.comboWidgetController = widget.comboWidgetController;
    _controller.comboEndCallback = widget.comboEndCallback;

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true); // 无限放大缩小

    _animation = Tween<double>(begin: 1.0, end: 1.3).animate(_scaleController);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller.backpackGift = widget.info;
    _controller.updateGiftCount();
    _controller.showCombo(isShowCombo: widget.isShowGiftCombo);
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
        init: _controller,
        global: false,
        autoRemove: false,
        builder: (controller){
          return GestureDetector(
            onTap: () {
              widget.onSelected?.call(widget.info);
            },
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                if (widget.giftPanelController?.isFamilyChat == true)
                  Positioned.fill(child: Visibility(
                    visible: widget.isSelected,
                    child: Container(decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFFAD50DF),
                        width: 0.5.pt,
                      ),
                      borderRadius: BorderRadius.circular(8.pt),
                    ),
                    ),
                  ),
                  ),
                _itemContentView(),
                if (widget.giftPanelController?.isFamilyChat == false)
                  Positioned.fill(
                      child: selectContentView()
                  ),

                Positioned(
                  top: 0,
                  right: 0,
                  child: _count(),
                ),

                Positioned(
                  top: -24.pt,
                  bottom: 5.pt,
                  child: _comboWidget(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _itemContentView() {
    BackpackGift info = widget.info;
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.pt),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 8.pt),
          _icon(info, width: widget.iconSize.width),
          SizedBox(height: 4.pt),
          _name(info),
          SizedBox(height: 2.pt),
          _time(),
        ],
      ),
    );
  }

  /// icon
  Widget _icon(BackpackGift info, {double width = 0}) {
    if (info.info?.icon?.trim().isEmpty == true) return SizedBox.shrink();

    var child = SizedBox(
      width: width,
      height: width,
      child: CachedNetworkImage(
          imageUrl: info.info?.icon ?? '',
          fit: BoxFit.cover
      ),
    );

    if (widget.giftPanelController?.isFamilyChat == true && widget.isSelected) {
      return ScaleTransition(
        scale: _animation,
        child: child,
      );
    }

    return child;
  }

  /// 名称
  Widget _name(BackpackGift info) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.pt),
      child: Text(
          info.info?.name ?? '',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          textScaleFactor: 1.0,
          style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
              fontWeight: FontWeightExt.medium
          )
      ),
    );
  }

  Widget _time() {
    return Text(
        widget.info.timeoutStr,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.center,
        textScaleFactor: 1.0,
        style: TextStyle(
            fontSize: 11.sp,
            color: R.color.textColor4,
            fontWeight: FontWeightExt.heavy
        )
    );
  }

  Widget _count() {
    return Obx(() =>
        Visibility(
          visible: _controller.giftCount.value > 0,
          child: Container(
            height: 16.pt,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 5.pt),
            constraints: BoxConstraints(
              minWidth: 16.pt
            ),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.pt),
                gradient: LinearGradient(
                  colors: [Color(0xFFBD8AFF), Color(0xFF6236F5)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )
            ),
            child: Text(
                '${_controller.giftCount.value}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                textScaleFactor: 1.0,
                style: TextStyle(
                    fontSize: 11.sp,
                    color: Colors.white,
                    fontWeight: FontWeightExt.heavy
                )
            ),
          ),
        )
    );
  }

  Widget selectContentView() {
    BackpackGift info = widget.info;
    return Visibility(
      visible: widget.isSelected,
      child: GestureDetector(
        onTap: () {
          widget.onSend?.call(widget.info);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.pt),
          child: Container(
            width: widget.itemSize.width,
            height: widget.itemSize.height,
            color: Color(0xFF252525),
            child: Column(
              children: [
                SizedBox(height: 8.pt),
                _icon(info, width: widget.iconSize.width + 8.pt),
                Spacer(),
                _sendBtn()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _sendBtn() {
    return Container(
      height: 26.pt,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(Res.roomGiftPanelGiftSendBtnBg),
          Text(
              LocaleStrings.instance.send,
              maxLines: 1,
              textAlign: TextAlign.center,
              textScaleFactor: 1.0,
              style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.white,
                  fontWeight: FontWeightExt.black
              )
          ),
        ],
      ),
    );
  }

  Widget _comboWidget() {
    return Visibility(
      visible: widget.isShowGiftCombo,
      child: Container(
        width: widget.itemSize.width,
        child: ComboButtonWidget(
          giftInfo: widget.info.info,
          itemSize: widget.itemSize,
          comboNum: _comboNum(),
          onTap: widget.onComboSend,
        ),
      ),
    );
  }

  int _comboNum() {
    SendGiftResp? sendGiftResp = widget.giftPanelController?.lastSendGiftInfo;
    if (sendGiftResp != null) {
      int count = (sendGiftResp.receivedIds?.length ?? 1) * (sendGiftResp.combo ?? 1);
      return count;
    }
    return 0;
  }
}