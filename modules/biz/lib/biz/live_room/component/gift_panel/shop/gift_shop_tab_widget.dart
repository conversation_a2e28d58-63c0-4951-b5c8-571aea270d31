import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/gift_item_combo_widget_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_backpack_item_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_goods_item_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_item_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_shop_tab_controller.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/positioned_notification.dart';
import 'package:biz/global/widgets/shape_clipper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/gift/const/enums.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/mall/model/goods_detail.dart';

class GiftShopTabWidget extends StatefulWidget {
  final String? type;
  final GiftPanelController? panelController;
  final GiftItemComboWidgetController? comboWidgetController;
  final bool showGiftNewerGuide;
  final GiftPanelSource fromSource;
  final bool isUnlockChat;
  final String? targetUid;

  GiftShopTabWidget({
    Key? key,
    this.type,
    this.panelController,
    this.comboWidgetController,
    this.showGiftNewerGuide = false,
    this.isUnlockChat = false,
    this.targetUid,
    required this.fromSource,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GiftShopTabState();
  }
}

class _GiftShopTabState extends State<GiftShopTabWidget>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  late GiftShopTabController _controller;

  OverlayEntry? overlayEntry;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    _controller = GiftShopTabController(
      type: widget.type,
      panelController: widget.panelController,
      showGiftNewerGuide: widget.showGiftNewerGuide,
      fromSource: widget.fromSource,
      targetUid: widget.targetUid,
      isUnlockChat: widget.isUnlockChat,
    );
    super.initState();
  }

  void _removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry?.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    double screenWidth = MediaQuery.of(context).size.width;
    _controller.culGiftItemLayout(screenWidth: screenWidth);
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
        init: _controller,
        global: false,
        autoRemove: false,
        builder: (controller) {
          return WillPopScope(
              child: _body(),
              onWillPop: () {
                _removeOverlay();
                return Future.value(true);
              });
          ;
        },
      ),
    );
  }

  Widget _body() {
    if (_controller.isLoading) {
      return GlobalWidgets.pageLoading();
    }

    List? list;
    switch (widget.type) {
      case GiftTabType.gift:
        list = _controller.gifts;
        break;
      case GiftTabType.backpack:
        list = _controller.backpacks;
        break;
      case GiftTabType.goods:
        list = _controller.goods;
        break;
      default:
        break;
    }
    if (list?.isEmpty ?? true) {
      return Center(
        child: _emptyList(),
      );
    }
    return _giftContent();
  }

  /// 空列表
  Widget _emptyList() {
    return Center(
      child: EmptyWidget(
          logoSize: Size(300, 150),
          logoAsset: Res.emptyEmptyBox,
          btnAction: () {
            _controller.initData();
          },
          btnSting: LocaleStrings.instance.retry),
    );
  }

  Widget _giftContent() {
    int itemCount = 0;
    switch (widget.type) {
      case GiftTabType.gift:
        itemCount = _controller.gifts.length;
        break;
      case GiftTabType.backpack:
        itemCount = _controller.backpacks.length;
        break;
      case GiftTabType.goods:
        itemCount = _controller.goods.length;
        break;
      default:
        break;
    }

    return GridView.builder(
      padding: EdgeInsets.only(left: 10.pt, right: 10.pt, top: 13.pt, bottom: 30.pt),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          mainAxisSpacing: 8.pt,
          crossAxisSpacing: _controller.itemSpacing,
          childAspectRatio: _controller.itemSize.width / _controller.itemSize.height),
      itemCount: itemCount,
      itemBuilder: (BuildContext context, int index) {
        return _item(index);
      },
    );
  }

  Widget _item(int index) {
    switch (widget.type) {
      case GiftTabType.gift:
        GiftInfo info = _controller.gifts[index];
        return _giftItem(info);
      case GiftTabType.backpack:
        BackpackGift info = _controller.backpacks[index];

        if (widget.showGiftNewerGuide && _controller.getGiftNewerGuideFlag() != true && index == 0) {
          return NotificationListener<PositionedNotification>(
              onNotification: (notification) {
                if (notification.offset != null && notification.size != null) {
                  _showGiftNewerGuide(notification.offset!, notification.size!);
                }
                return true;
              },
              child: PositionedNotificationNotifier(child: _backpackItem(info)));
        }

        return _backpackItem(info);
      case GiftTabType.goods:
        BackpackGoodsDetail goodsDetail = _controller.goods[index];
        return GiftGoodsItemWidget(
          good: goodsDetail,
          giftPanelController: widget.panelController,
          itemSpacing: _controller.itemSpacing,
          itemSize: _controller.itemSize,
          iconSize: _controller.iconSize,
          isSelected: _controller.panelController?.selectedGoods?.goodDetail?.id != null &&
              _controller.panelController?.selectedGoods?.goodDetail?.id == goodsDetail.goodDetail?.id,
          onSelected: (BackpackGoodsDetail good) {
            _controller.didSelectBackpackGood(goodDetail: good);
          },
          onSend: (BackpackGoodsDetail goodsDetail) {
            _controller.wear(goodDetail: goodsDetail);
          },
        );
      default:
        break;
    }
    return SizedBox.shrink();
  }

  Widget _giftItem(GiftInfo info) {
    return GiftItemWidget(
      info: info,
      type: widget.type,
      giftPanelController: widget.panelController,
      itemSpacing: _controller.itemSpacing,
      itemSize: _controller.itemSize,
      iconSize: _controller.iconSize,
      isSelected: info.id != null && info.id == _controller.panelController?.selectGift?.id,
      isShowGiftCombo: _controller.panelController?.isStartCombo == true &&
          info.id != null &&
          info.id == _controller.panelController?.showComboGift?.id &&
          _controller.panelController?.startComboTab == GiftTabType.gift,
      comboWidgetController: widget.comboWidgetController,
      onSelected: (GiftInfo giftInfo) {
        _controller.didSelectGift(gift: giftInfo);
      },
      onSend: (GiftInfo giftInfo) {
        _controller.onSendGift(gift: giftInfo);
      },
      onComboSend: _controller.onSendGiftCombo,
      comboEndCallback: _controller.endCombo,
    );
  }

  Widget _backpackItem(BackpackGift info) {
    return GiftBackpackItemWidget(
      info: info,
      type: widget.type,
      giftPanelController: widget.panelController,
      itemSpacing: _controller.itemSpacing,
      itemSize: _controller.itemSize,
      iconSize: _controller.iconSize,
      isSelected: info.info?.id != null && info.info?.id == _controller.panelController?.selectBackpackGift?.info?.id,
      isShowGiftCombo: _controller.panelController?.isStartCombo == true &&
          info.info?.id != null &&
          info.info?.id == _controller.panelController?.lastSendGiftInfo?.giftId &&
          _controller.panelController?.startComboTab == GiftTabType.backpack,
      comboWidgetController: widget.comboWidgetController,
      onSelected: (BackpackGift giftInfo) {
        _controller.didSelectBackpackGift(gift: giftInfo);
      },
      onSend: (BackpackGift giftInfo) {
        _controller.onSendBackpackGift(gift: giftInfo);
      },
      onComboSend: _controller.onSendGiftCombo,
      comboEndCallback: _controller.endCombo,
    );
  }

  void _showGiftNewerGuide(Offset offset, Size size) async {
    _controller.saveGiftNewerGuide();

    RoomUserInfoModel? userInfo = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomGiftGuidePopout(
        uid: userInfo?.uid,
        gender: userInfo?.sexStr,
        roomId: roomService.getCurrentRoom()?.roomId,
        roomMode: roomService.getCurrentRoom()?.mode);

    overlayEntry = OverlayEntry(builder: (context) {
      double iconLeft = offset.dx + size.width - 30.pt - 45.pt;
      if (iconLeft < 0) {
        iconLeft = 0;
      }
      return GestureDetector(
        onTap: _removeOverlay,
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.center,
            children: [
              GestureDetector(
                onTap: _removeOverlay,
                child: ClipPath(
                  clipper: RRectClipper(
                    area: Rect.fromLTWH(offset.dx, offset.dy, size.width, size.height),
                    radius: BorderRadius.circular(8.pt),
                  ),
                  child: Container(
                    width: 1.w,
                    height: 1.h,
                    color: Colors.black.withOpacity(0.8),
                  ),
                ),
              ),
              Positioned(
                top: offset.dy + size.height - 26.pt,
                child: Container(
                  width: MediaQuery.of(context).size.width - 90.pt,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: iconLeft),
                        child: FLImage.asset(Res.roomGiftNewerGuide, width: 86, height: 69.pt),
                      ),
                      Text(LocaleStrings.instance.giftNewerGuideTip,
                          softWrap: true,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontSize: 16.sp, color: R.color.backgroundColor, fontWeight: FontWeightExt.medium))
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
    Overlay.of(context).insert(overlayEntry!);
  }
}
