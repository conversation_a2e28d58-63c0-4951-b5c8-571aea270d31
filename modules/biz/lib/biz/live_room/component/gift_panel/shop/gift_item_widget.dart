
import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/combo_button_widget.dart';
import 'package:biz/biz/live_room/component/gift_panel/combo_btn/gift_item_combo_widget_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_item_controller.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:get/get.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_shop_tab_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/live/room_gift/model/send_gift_resp.dart';
import 'package:service/modules/mall/const/enums.dart';

class GiftItemWidget extends StatefulWidget {

  void Function(GiftInfo)? onSelected;
  void Function(GiftInfo)? onSend;
  VoidCallback? onComboSend;
  GiftPanelController? giftPanelController;
  GiftItemComboWidgetController? comboWidgetController;
  VoidCallback? comboEndCallback;

  String? type;
  Size itemSize;
  Size iconSize;
  double itemSpacing;
  GiftInfo info;
  bool isSelected;
  bool isShowGiftCombo;

  GiftItemWidget({
    Key? key,
    this.type,
    required this.info,
    this.itemSize = Size.zero,
    this.iconSize = Size.zero,
    this.itemSpacing = 0,
    this.onSelected,
    this.onSend,
    this.onComboSend,
    this.isSelected = false,
    this.isShowGiftCombo = false,
    this.comboEndCallback,
    this.giftPanelController,
    this.comboWidgetController
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GiftItemWidgetState();
  }
}

class _GiftItemWidgetState extends State<GiftItemWidget> with SingleTickerProviderStateMixin {

  final GiftItemController _controller = GiftItemController();

  late AnimationController _scaleController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller.giftPanelController = widget.giftPanelController;
    _controller.comboWidgetController = widget.comboWidgetController;
    _controller.comboEndCallback = widget.comboEndCallback;

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true); // 无限放大缩小

    _animation = Tween<double>(begin: 1.0, end: 1.3).animate(_scaleController);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller.giftInfo = widget.info;
    _controller.showCombo(isShowCombo: widget.isShowGiftCombo);
    _controller.updateGiftCount();
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
        init: _controller,
        global: false,
        autoRemove: false,
        builder: (controller){
          return GestureDetector(
            onTap: () {
              widget.onSelected?.call(widget.info);
            },
            child:
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (widget.giftPanelController?.isFamilyChat == true)
                  Positioned.fill(child: Visibility(
                      visible: widget.isSelected,
                      child: Container(decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xFFAD50DF),
                            width: 0.5.pt,
                          ),
                          borderRadius: BorderRadius.circular(8.pt),
                        ),
                      ),
                    ),
                  ),
                _itemContentView(),
                if (widget.giftPanelController?.isFamilyChat == false)
                  Positioned.fill(
                      child: selectContentView()
                  ),
                Positioned(
                    top: 0,
                    right: 0,
                    child: _count()
                ),
                Positioned(
                  top: -24.pt,
                  bottom: 5.pt,
                  child: _comboWidget(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _itemContentView() {
    GiftInfo info = widget.info;
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.pt),
      child: Stack(
        children: [
          _giftTag(),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 8.pt),
              _icon(info, width: widget.iconSize.width),
              SizedBox(height: 4.pt),
              _name(info),
              SizedBox(height: 2.pt),
              _price(info),
            ],
          )
        ],
      ),
    );
  }

  /// icon
  Widget _icon(GiftInfo info, {double width = 0}) {
    if (info.icon?.trim().isEmpty == true) return SizedBox.shrink();

    var child = SizedBox(
      width: width,
      height: width,
      child: CachedNetworkImage(
          imageUrl: info.icon ?? '',
          fit: BoxFit.cover
      ),
    );

    if (widget.giftPanelController?.isFamilyChat == true && widget.isSelected) {
      return ScaleTransition(
        scale: _animation,
        child: child,
      );
    }

    return child;
  }

  Widget _giftTag() {
    GiftInfo info = widget.info;
    return Positioned(
        left: 0,
        top: 0,
        child: IgnorePointer(
          child: Container(
            height: 12.pt,
            child: CachedNetworkImage(
              imageUrl: info.tag ?? '',
              fit: BoxFit.fitHeight,
              placeholder: (_, __) => SizedBox(),
              errorWidget: (_, __, ___) => SizedBox(),
            ),
          ),
        )
    );
  }

  Widget _count() {
    return Obx(() =>
        Visibility(
          visible: _controller.giftCount.value > 0,
          child: Container(
            height: 16.pt,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 5.pt),
            constraints: BoxConstraints(
                minWidth: 16.pt
            ),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.pt),
                gradient: LinearGradient(
                  colors: [Color(0xFFBD8AFF), Color(0xFF6236F5)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )
            ),
            child: Text(
                _controller.giftCount.value < 1000 ? "${_controller.giftCount.value}" : "999+",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                textScaleFactor: 1.0,
                style: TextStyle(
                    fontSize: 11.sp,
                    color: Colors.white,
                    fontWeight: FontWeightExt.heavy
                )
            ),
          ),
        )
    );
  }

  /// 名称
  Widget _name(GiftInfo info) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.pt),
      child: Text(
          info.name ?? '',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          textScaleFactor: 1.0,
          style: TextStyle(
              fontSize: 11.sp,
              color: Color(0xFF909090),
              fontWeight: FontWeightExt.medium,
              height: 1.1,
          )
      ),
    );
  }

  /// 价格
  Widget _price(GiftInfo gift, {bool isSelected = false}) {
    Color priceColor;
    switch (gift.currencyType) {
      case CurrencyType.goldCoin:
        priceColor = Color(0xFFC78300);
      case CurrencyType.diamond:
        priceColor = Color(0xFF9825FD);
      default:
        priceColor = Colors.white;
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
            '${gift.price ?? 0}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            textScaleFactor: 1.0,
            style: TextStyle(
                fontSize: 11.sp,
                color: priceColor,
                fontWeight: FontWeightExt.heavy
            )
        ),
        SizedBox(width: 3.pt),
        FLImage.asset(gift.typeIcon, width: 12.pt, height: 12.pt),
      ],
    );
  }

  Widget selectContentView() {
    GiftInfo info = widget.info;
    return Visibility(
      visible: widget.isSelected,
      child: GestureDetector(
        onTap: () {
          widget.onSend?.call(widget.info);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.pt),
          child: Container(
            width: widget.itemSize.width,
            height: widget.itemSize.height,
            color: Color(0xFF252525),
            child: Column(
              children: [
                _icon(info, width: widget.iconSize.width + 8.pt),
                _price(info, isSelected: true),
                Expanded(child: _sendBtn())
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _sendBtn() {
    return Stack(
      alignment: Alignment.center,
      children: [
        FLImage.asset(Res.roomGiftPanelGiftSendBtnBg),
        Text(
            LocaleStrings.instance.send,
            maxLines: 1,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white,
                fontWeight: FontWeightExt.black
            )
        ),
      ],
    );
  }

  Widget _comboWidget() {
    return Visibility(
      visible: widget.isShowGiftCombo,
      child: Container(
        width: widget.itemSize.width,
        child: ComboButtonWidget(
          giftInfo: widget.info,
          comboNum: _comboNum(),
          itemSize: widget.itemSize,
          onTap: widget.onComboSend,
        ),
      ),
    );
  }

  int _comboNum() {
    SendGiftResp? sendGiftResp = widget.giftPanelController?.lastSendGiftInfo;
    if (sendGiftResp != null) {
      int count = (sendGiftResp.receivedIds?.length ?? 1) * (sendGiftResp.combo ?? 1);
      return count;
    }
    return 0;
  }

}