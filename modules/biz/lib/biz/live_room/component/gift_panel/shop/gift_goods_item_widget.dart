import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_controller.dart';
import 'package:biz/biz/live_room/component/gift_panel/shop/gift_goods_item_controller.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/modules/mall/model/goods_detail.dart';

class GiftGoodsItemWidget extends StatefulWidget {
  final void Function(BackpackGoodsDetail)? onSelected;
  final void Function(BackpackGoodsDetail)? onSend;

  final BackpackGoodsDetail good;
  final Size itemSize;
  final Size iconSize;
  final double itemSpacing;
  final bool isSelected;
  final GiftPanelController? giftPanelController;

  GiftGoodsItemWidget(
      {Key? key,
      required this.good,
      this.itemSize = Size.zero,
      this.iconSize = Size.zero,
      this.itemSpacing = 0,
      this.isSelected = false,
      this.onSelected,
      this.onSend,
      this.giftPanelController})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GiftGoodsItemWidgetState();
  }
}

class _GiftGoodsItemWidgetState extends State<GiftGoodsItemWidget> with SingleTickerProviderStateMixin {
  final GiftGoodsItemController _controller = GiftGoodsItemController();

  late AnimationController _scaleController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller.good = widget.good;

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true); // 无限放大缩小

    _animation = Tween<double>(begin: 1.0, end: 1.3).animate(_scaleController);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
        init: _controller,
        global: false,
        autoRemove: false,
        builder: (controller) {
          return GestureDetector(
            onTap: () {
              widget.onSelected?.call(widget.good);
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.pt),
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.center,
                children: [
                  _itemContentView(),
                  Positioned.fill(child: selectContentView()),
                  Positioned(top: 2.pt, right: 2.pt, child: _wearStatus()),
                  Positioned(top: 0, right: 0, child: _count())
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _itemContentView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: 8.pt),
        _icon(width: widget.iconSize.width),
        SizedBox(height: 4.pt),
        _name(),
        SizedBox(height: 2.pt),
        _time(),
      ],
    );
  }

  /// icon
  Widget _icon({double width = 0}) {
    if (widget.good.goodDetail?.thumb?.isEmpty == true) return SizedBox.shrink();

    var child = SizedBox(
      width: width,
      height: width,
      child: CachedNetworkImage(
        imageUrl: widget.good.goodDetail?.thumb ?? '',
        fit: BoxFit.contain,
      ),
    );

    if (widget.giftPanelController?.isFamilyChat == true && widget.isSelected) {
      return ScaleTransition(
        scale: _animation,
        child: child,
      );
    }

    return child;
  }

  /// 名称
  Widget _name() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.pt),
      child: Text(widget.good.goodDetail?.name ?? '',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeightExt.medium)),
    );
  }

  Widget _time() {
    return Text(widget.good.timeoutStr,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 11.sp, color: R.color.textColor4, fontWeight: FontWeightExt.heavy));
  }

  Widget _wearStatus() {
    return Visibility(
      visible: widget.good.isWearing,
      child: Container(
        width: 16.pt,
        height: 16.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.pt), color: R.color.textColor2),
        child: FLImage.asset(Res.roomGiftGoodWear, width: 13.pt, height: 12.pt),
      ),
    );
  }

  Widget selectContentView() {
    final isProps = widget.good.mallType == MallType.props;
    return Visibility(
      visible: widget.isSelected,
      child: GestureDetector(
        onTap: isProps
            ? null
            : () {
                widget.onSend?.call(widget.good);
              },
        child: Container(
          width: widget.itemSize.width,
          height: widget.itemSize.height,
          color: Color(0xFF252525),
          child: Column(
            children: [
              SizedBox(height: 8.pt),
              _icon(width: widget.iconSize.width + 8.pt),
              Spacer(),
              Visibility(visible: !isProps, child: _sendBtn()),
              Visibility(visible: isProps, child: _name()),
              Visibility(visible: isProps, child: _time()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _sendBtn() {
    return Container(
      height: 26.pt,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(Res.roomGiftPanelGiftSendBtnBg),
          Text(widget.good.isWearing == true ? LocaleStrings.instance.wearing : LocaleStrings.instance.wear,
              maxLines: 1,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 13.sp, color: Colors.white, fontWeight: FontWeightExt.black)),
        ],
      ),
    );
  }

  Widget _count() {
    final count = _controller.good?.count ?? 0;
    return Visibility(
      visible: count > 1,
      child: Container(
        height: 16.pt,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 5.pt),
        constraints: BoxConstraints(minWidth: 16.pt),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.pt),
            gradient: LinearGradient(
              colors: [Color(0xFFBD8AFF), Color(0xFF6236F5)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            )),
        child: Text('$count',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeightExt.heavy)),
      ),
    );
  }
}
