import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/global/widgets/user_nick_widget.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import 'bloc/select_user_bloc.dart';

class SelectGiftUserWidget extends StatelessWidget {
  final List<String>? uidList;
  String? selectedUid;
  final Function(UserInfo)? selectCallBack;

  SelectGiftUserWidget({this.uidList, this.selectCallBack, this.selectedUid})
      : _bloc = SelectUserBloc(uidList: uidList, selectedUid: selectedUid);

  final SelectUserBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<SelectUserBloc, SelectUserState>(
      bloc: _bloc,
      builder: (_, state) => _page(),
    );
  }

  Widget _page() {
    if (_bloc.state.list?.isEmpty ?? true) {
      return SizedBox();
    }

    return Container(
      width: double.infinity,
      height: 70.pt,
      alignment: Alignment.center,
      padding: EdgeInsets.only(right: 5.pt, left: 5.pt,top: 5.pt),
      child: SmartRefresher(
        scrollDirection: Axis.horizontal,
        controller: _bloc.refreshController,
        onLoading: () => _bloc.add(LoadMore()),
        enablePullDown: false,
        enablePullUp: _bloc.state.uidList?.isNotEmpty ?? false,
        physics: BouncingScrollPhysics(),
        footer: GlobalWidgets.refreshFoot(iconPos: IconPosition.top),
        child: _list(),
      ),
    );
  }

  Widget _list() {
    final list = _bloc.state.list ?? [];
    return ListView.builder(
      itemCount: list.length,
      shrinkWrap: false,
      scrollDirection: Axis.horizontal,
      cacheExtent: 360.pt,
      itemBuilder: (BuildContext context, int index) {
        return _item(list[index]);
      },
    );
  }

  Widget _item(UserInfo info) {
    return ScaleTapWidget(
      onTap: () {
        _bloc.add(SelectedUser(selectedUid: info.uid));
        selectCallBack?.call(info);
      },
      child: Container(
        alignment: Alignment.topCenter,
        margin: EdgeInsets.symmetric(horizontal: 4.pt),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(3.pt),
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: _bloc.isSelected(info.uid) ? Color(0xFFAD50DF) : Colors.transparent,
                        width: 1.pt
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: UserAvatar(url: info.avatar, size: 30.pt),
                ),
                if (_bloc.isSelected(info.uid))
                  Container(
                    width: 30.pt,
                    height: 30.pt,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15.pt),
                      color: Colors.black.withOpacity(0.6),
                    ),
                  ),
                if (_bloc.isSelected(info.uid))
                  FLImage.asset(Res.giftGiftUserSelected, width: 13.pt, height: 9.pt),
              ],
            ),
            Container(height: 20.pt,
              width: 42.pt,
              alignment: Alignment.bottomCenter,
              child: Text(
                info.displayedName,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Color(0xff909090),
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

}
