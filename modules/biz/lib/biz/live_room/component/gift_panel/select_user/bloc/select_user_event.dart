part of 'select_user_bloc.dart';

@immutable
abstract class SelectUserEvent {}

class SelectUserEventData implements SelectUserEvent {
  final List<String>? uidList;

  final List<UserInfo>? list;

  SelectUserEventData({this.uidList, this.list});
}


class <PERSON>adMore extends SelectUserEvent {}

class SelectedUser implements SelectUserEvent {
  final String? selectedUid;
  SelectedUser({this.selectedUid});
}