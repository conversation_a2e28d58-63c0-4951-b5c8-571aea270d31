import 'package:biz/biz.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_animation_widget.dart';
import 'package:biz/biz/live_room/component/gift/room_big_gift_diaplay_controller.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/room_gift/model/gift_display_msg.dart';
import 'package:service/modules/user/model/user_info.dart';

import '../user_car/user_car_widget.dart';

class RoomBigGiftDisplayWidget extends StatefulWidget {
  final bool showUserCar;

  const RoomBigGiftDisplayWidget({this.showUserCar = true});

  @override
  State<StatefulWidget> createState() => _GiftDisplayWidgetState();
}

class _GiftDisplayWidgetState extends State<RoomBigGiftDisplayWidget> with TickerProviderStateMixin {
  final RoomBigGiftDisplayController _controller = RoomBigGiftDisplayController();

  @override
  void initState() {
    super.initState();
    final enterBannerAniCtrl = AnimationController(vsync: this, duration: Duration(milliseconds: 7000));
    _controller.init(enterBannerAniCtrl);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
          init: _controller,
          global: false,
          autoRemove: false,
          builder: (controller) {
            return _page();
          },
        ));
  }

  Widget _page() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          _vapGiftWidget(),

          _userCarSvgaWidget(),

          /// 座驾显示
          _userCarWidget(),

          /// 礼物展示
          _svgaGiftWidget(),

          /// 亲密值提升
          _intimacyUpWidget(),
        ],
      ),
    );
  }

  Widget _vapGiftWidget() {
    final show = widget.showUserCar && _controller.showVap;
    return show
        ? ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 1.w, maxHeight: 1.h),
            child: GlobalWidgets.vapWidget(),
          )
        : const SizedBox.shrink();
  }

  Widget _userCarSvgaWidget() {
    final show = widget.showUserCar && _controller.showSvgaCar;
    return Visibility(
      visible: show,
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 1.w, maxHeight: 1.h),
        child: SvgaWidget(
          fit: BoxFit.fitWidth,
          controller: _controller.carSvgaController,
        ),
      ),
    );
  }

  Widget _svgaGiftWidget() {
    var width = double.infinity;
    var height = double.infinity;

    /// 礼物显示
    if ((_controller.giftDisplayMsg?.pbImGift?.level ?? 0) < roomGiftService.getBigGiftLevel() &&
        _controller.giftDisplayMsg?.notice?.batchType != 2) {
      height = 750;
      width = 400;
    }

    return Visibility(
      visible: _controller.giftDisplayMsg?.type == GiftDisplayType.gift,
      maintainSize: true,
      maintainAnimation: true,
      maintainState: true,
      child: Container(
        width: width,
        height: height,
        child: SvgaWidget(
          fit: BoxFit.fitWidth,
          controller: _controller.giftController,
        ),
      ),
    );
  }

  Widget _userCarWidget() {
    // Log.d(tagGiftDisPlay,
    //     ' build userCar type=${_controller.giftDisplayMsg?.type},notice=${_controller.giftDisplayMsg?.carNotice}');
    final show = widget.showUserCar && _controller.giftDisplayMsg?.type == GiftDisplayType.enterEffect;
    return Visibility(
        maintainSize: true,
        maintainAnimation: true,
        maintainState: true,
        visible: show,
        child: _wrapIgnore(
          UserCarWidget(
            bannerController: _controller.bannerSvgaController,
            bannerOffsetAnimation: _controller.bannerOffsetAnim,
            bannerAlphaAnimation: _controller.bannerAlphaAnim,
            notice: _controller.giftDisplayMsg?.carNotice,
          ),
        ));
  }

  Widget _intimacyUpWidget() {
    final isVisible = _controller.giftDisplayMsg?.type == GiftDisplayType.IntimacyLevelUp;
    final data = _controller.giftDisplayMsg?.intimacyUpData;
    if (!isVisible || data == null) {
      return SizedBox.shrink();
    }
    return _wrapIgnore(
      Container(
        width: double.infinity,
        height: double.infinity,
        color: Color(0x99000000),
        child: IntimacyAnimationWidget(
          insets: EdgeInsets.only(bottom: 100.pt),
          intimacyLevel: data.info.intimacyLevel,
          showLevelUp: true,
          targetUser: UserInfo.fromPb(data.sender.user),
          intimacyUser: UserInfo.fromPb(data.receiver.user),
          onComplete: () {
            if (mounted) {
              _controller.onNext();
            }
          },
        ),
      ),
    );
  }

  Widget _wrapIgnore(Widget child) {
    return IgnorePointer(child: child);
  }
}
