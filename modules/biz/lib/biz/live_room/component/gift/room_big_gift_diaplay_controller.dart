import 'package:biz/biz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/file.dart';
import 'package:flutter_vap/flutter_vap.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room_gift/model/gift_display_msg.dart';
import 'package:service/modules/live/room_gift/model/room_gift_display_manager.dart';

class RoomBigGiftDisplayController extends AbsGetController {
  static const String _tag = 'GiftDisplay';

  GiftDisplayMsg? giftDisplayMsg;
  bool showVap = false;
  bool showSvgaCar = false;

  File? file;

  bool _playingVap = false;

  RoomGiftDisplayManager? giftQueueManager;

  /// 礼物控制器
  SvgaController giftController = SvgaController();

  /// 座驾控制器
  final SvgaController carSvgaController = SvgaController();

  /// 进场流光svga控制器
  final SvgaController bannerSvgaController = SvgaController();

  /// 进场横幅动画控制器
  late AnimationController bannerController;

  ///  进场横幅动画
  late Animation<Offset> bannerOffsetAnim;
  late Animation<double> bannerAlphaAnim;

  void init(AnimationController enterBannerAniCtrl) {
    bannerController = enterBannerAniCtrl;
    bannerOffsetAnim = TweenSequence<Offset>([
      TweenSequenceItem(tween: Tween(begin: Offset(0, 0), end: Offset(0, 0)), weight: 1),
    ]).animate(bannerController);
    bannerAlphaAnim =
        Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(parent: bannerController, curve: Interval(0, 0.07)));

    bannerController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        bannerController.reset();
        if (giftQueueManager?.isExecuting == false) {
          // giftDisplayMsg = null;
          // update();
          // giftQueueManager?.complete();
        } else {
          /// 如果仅仅是进场流光，需要在动画结束后恢复队列
          if (giftDisplayMsg?.onlyEnterStreamer == true) {
            giftDisplayMsg = null;
            update();
            giftQueueManager?.complete();
          }
        }
      }
    });

    bannerSvgaController.setEndCallback((info) {
      _svgaEndCallback();
    });

    giftController.setEndCallback((info) {
      _svgaEndCallback();
    });

    carSvgaController.setEndCallback((info) {
      showSvgaCar = false;
      _svgaEndCallback();
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      VapController.stop();
    });
  }

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
  }

  @override
  void stateDispose() {
    VapController.stop();
    giftQueueManager?.clear();
    giftController.stopSvga();
    giftController.dispose();
    bannerSvgaController.dispose();
    bannerController.dispose();
    carSvgaController.dispose();
    super.stateDispose();
  }

  void _initRx() {
    listenRxEvent<GiftDisplayMsg>(RoomGiftEvent.giftDisplay, _giftMsgDisplay);
    listenRxEvent<int>(RoomGiftEvent.stopAll, _stop);
    listenRxEvent<GiftDisplayMsg>(RoomEvent.enterRoom, _giftMsgDisplay);
  }

  void _stop(int value) {
    VapController.stop();
    giftDisplayMsg = null;
    showVap = false;
    _playingVap = false;
    showSvgaCar = false;
    update();

    carSvgaController.stopSvga();
    giftController.stopSvga();
    giftQueueManager?.clear();
    bannerController.stop();
    bannerSvgaController.stopSvga();
  }

  void _svgaEndCallback() {
    _next();
  }

  void _next() {
    giftDisplayMsg = null;
    update();

    if (_playingVap || showSvgaCar) return;
    giftQueueManager?.complete();
  }

  void onNext() {
    _next();
  }

  void _giftMsgDisplay(GiftDisplayMsg giftPbMsg) async {
    bool my = ((giftPbMsg.fromSelf ?? false) && await commonService.showMySvga());

    final showSvgaInRoom = (await commonService.showSvgaInRoom());

    /// 除了赠送道具
    if (giftPbMsg.type != GiftDisplayType.goodsGift && (!my && !showSvgaInRoom)) {
      Log.w(_tag,
          "Gift display cancelled: giftPbMsg.type = ${giftPbMsg.type}, my = $my, showSvgaInRoom = $showSvgaInRoom");
      return;
    }

    if (giftPbMsg.type == GiftDisplayType.gift) {
      final notice = giftPbMsg.notice!;
      if (notice.gift.fileUrl.isEmpty &&
          notice.gift.videoUrl.isEmpty &&
          notice.batchType != 2 &&
          notice.batchType != 1) {
        return;
      }
    } else if (giftPbMsg.type == GiftDisplayType.enterEffect) {
      final notice = giftPbMsg.carNotice!;

      /// 没有座驾且没有任何类型的入场特效时，不做任何处理
      if (notice.car.videoUrl.isEmpty &&
          notice.car.carInfo.url.isEmpty &&
          notice.car.streamerUrl.isEmpty &&
          notice.royalCar.carInfo.url.isEmpty) {
        Log.w(_tag,
            "Car display cancelled: car.videoUrl = ${notice.car.videoUrl}, carInfo.url = ${notice.car.carInfo.url}, royalCarInfo.carInfo.url = ${notice.royalCar.carInfo.url}");
        return;
      }
    } else if (giftPbMsg.type == GiftDisplayType.goodsGift) {
      final notice = giftPbMsg.goodsNotice!;
      if (notice.user.headimgurl.isEmpty || notice.goods.svga.isEmpty) {
        return;
      }
    } else if (giftPbMsg.type == GiftDisplayType.IntimacyLevelUp) {
      if (giftPbMsg.intimacyUpData == null) {
        return;
      }
    }
    if (giftQueueManager == null) {
      giftQueueManager = RoomGiftDisplayManager(giftController, showHandle: _show);
    }
    giftQueueManager?.put(giftPbMsg, fromSelf: giftPbMsg.fromSelf ?? false);
  }

  /// 处理展示
  void _show(GiftDisplayMsg msg, File? file, bool fromSelf) {
    _display(msg, file);
  }

  void _display(GiftDisplayMsg msg, File? file) async {
    Log.d(_tag, ' display msg.type=${msg.type}');

    /// 入场特效
    if (msg.type == GiftDisplayType.enterEffect) {
      rxUtil.send(RoomEvent.showRoomCar, msg.carNotice?.user.uid ?? '');
      _reportCarImpl(msg.carNotice?.car.id);
    }

    if (msg.type == GiftDisplayType.IntimacyLevelUp) {
      giftDisplayMsg = msg;
      showVap = false;
      update();
    } else if (file?.existsSync() == true) {
      // 礼物特效
      final videoUrl = msg.isGiftMsg == true ? msg.notice?.gift.videoUrl : msg.carNotice?.car.videoUrl;
      // 进场流光/礼物
      final svgaUrl = (msg.isGiftMsg == true ? msg.notice?.gift.fileUrl : msg.carNotice?.car.streamerUrl) ?? '';
      // 入场座驾
      final royalCarUrl = msg.carNotice?.royalCar.carInfo.url ?? "";
      // vap 版本
      final vapVersion = msg.isGiftMsg == true ? msg.notice?.gift.vapVersion : msg.carNotice?.car.vapVersion;

      if (videoUrl?.isNotEmpty == true) {
        giftDisplayMsg = msg;
        WidgetUtils.post((_) {
          _showVap(msg: msg, file: file, vapVersion: vapVersion);
        });
      } else if (svgaUrl.isNotEmpty || royalCarUrl.isNotEmpty) {
        giftDisplayMsg = msg;
        showSvgaCar = royalCarUrl.isSvga;
        update();

        if (msg.type == GiftDisplayType.enterEffect) {
          WidgetUtils.post((duration) async {
            if (svgaUrl.isNotEmpty) {
              final svgaFile = await roomGiftService.getCacheFile(svgaUrl);
              _playBannerSvga(svgaFile);
            }
            _playCarWithUrl(royalCarUrl, msg: msg, vapVersion: vapVersion);
          });
        } else {
          WidgetUtils.post((duration) {
            if (mounted) {
              giftController.startSvga(SvgaInfo(file: file, repeat: false));
            } else {
              Log.e(_tag, "Context is null");
            }
          });
        }
      }
      return;
    } else {
      giftQueueManager?.complete();
    }
  }

  /// 播放进场座驾
  void _playCarWithUrl(String url, {GiftDisplayMsg? msg, int? vapVersion}) async {
    if (url.isEmpty) return;
    if (!mounted) return;

    final file = await roomGiftService.getCacheFile(url);
    if (url.isSvga) {
      // showSvgaCar.value = true;
      carSvgaController.startSvga(SvgaInfo(file: file));
      Log.i(_tag, "show svga car: file: ${file?.path}");
    } else if (url.isMp4 && msg != null) {
      _showVap(msg: msg, file: file, vapVersion: vapVersion);
    }
  }

  /// 播放进场流光
  void _playBannerSvga(File? file) {
    if (file == null) return;
    if (file.path.isEmpty) return;
    if (!mounted) return;

    bannerSvgaController.startSvga(SvgaInfo(file: file));
    bannerController.forward(from: 0);

    Log.i(_tag, "show banner streamer: file: ${file.path}");
  }

  /// 视频动画
  void _playWithVap(String filePath, {int? type}) {
    _playingVap = true;
    VapController.playPath(filePath, type: type, resultCallback: _vapCallback);
  }

  /// vap 回调
  void _vapCallback(Map<dynamic, dynamic>? result) {
    if (result?['status'] == 'failure') {
      final msg = result?['errorMsg'];
      Log.i(_tag, "play vap failed: file: $msg");
    }

    _playingVap = false;
    VapController.stop();
    showVap = false;

    /// 如果是进场座驾还要等待进场横幅动效结束才能继续执行
    if (giftDisplayMsg?.isGiftMsg == false && bannerController.isAnimating) {
      update();
      return;
    }

    giftDisplayMsg = null;
    update();
    giftQueueManager?.complete();
  }

  void _showVap({required GiftDisplayMsg msg, required File? file, required int? vapVersion}) async {
    if (!mounted) return;
    final path = file?.path ?? '';
    if (path.isEmpty) return;

    bool vapIsShow = showVap;

    showVap = true;
    update();

    /// 部分手机出现VapView未初始化导致播放失败，如果一开始没有挂载上去，延迟400ms后播放
    if (!vapIsShow) {
      Log.i(_tag, "Delay to play Vap");
      await Future.delayed(Duration(milliseconds: 400));

      if (!mounted) return;
    }

    _playWithVap(path, type: vapVersion);

    Log.i(_tag, "show Vap: msg.type = ${msg.type}, file: $path, vapIsShow = $showVap, vapVersion = $vapVersion");
  }

  void _reportCarImpl(String? id) {
    RoomStatistics.reportRoomEntereffectImp(roomId: roomService.getCurrentRoomId(), content: id);
  }
}
