import 'package:biz/biz/live_room/component/fans/widget/fan_follow_owner_checkbox.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/component/user_card/widget/room_user_card_item_info_widget.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/service.dart';

import 'room_fans_detail_bottom_sheet_view.dart';

void showJoinFansGuideDialog({
  required RoomMainController mainController,
  required String uid,
  BuildContext? context,
}) async {
  DialogScheduler.instance().schedule(
    () => dialog.showBottomSheet(
      (context) => RoomJoinFansGuideDialog(uid: uid, mainController: mainController),
      context: context,
      routeSettings: RouteSettings(name: R_LIVE_ROOM_JOIN_FANS_GUIDE_DIALOG),
    ),
    context: context,
    single: true,
    path: R_LIVE_ROOM_JOIN_FANS_GUIDE_DIALOG,
  );
}

/// 房间内引导加入粉丝团的弹窗
class RoomJoinFansGuideDialog extends StatefulWidget {
  final String uid;
  final RoomMainController mainController;

  RoomJoinFansGuideDialog({
    super.key,
    required this.uid,
    required this.mainController,
  });

  @override
  State<RoomJoinFansGuideDialog> createState() => _RoomJoinFansGuideDialogState();
}

class _RoomJoinFansGuideDialogState extends State<RoomJoinFansGuideDialog>
    with GetStateBuilderMixin<RoomJoinFansGuideDialog, _JoinFansGuideController> {

  @override
  bool isDart = true;

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Stack(
        alignment: Alignment.topCenter,
        clipBehavior: Clip.none,
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 11.pt, vertical: 18.pt),
            decoration: BoxDecoration(
              color: R.color.backgroundColorDialog,
              borderRadius: BorderRadius.circular(16.pt),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _avatarHalfSize.hSpace,
                18.hSpace,
                _buildProfile(),
                29.hSpace,
                _buildInviteText(),
                24.hSpace,
                _buildJoinBtn(),
                13.hSpace,
                _buildFollowCheckBox(),
                16.hSpace,
              ],
            ),
          ),
          PositionedDirectional(
            top: -_avatarHalfSize,
            child: _buildAvatar(),
          ),
        ],
      ),
    );
  }

  @override
  _JoinFansGuideController initCtl() {
    return _JoinFansGuideController(widget.uid, widget.mainController);
  }

  double get _avatarHalfSize => userCardAvatarSize / 2;

  Widget _buildAvatar() {
    return UserAvatar(
      url: getCtl.roomUserInfo?.avatar ?? '',
      size: userCardAvatarSize,
      hasFrame: true,
      inRoom: true,
      userId: getCtl.roomUserInfo?.uid,
      onTap: ({String? rid}) {
        getCtl.goToProfile();
      },
    );
  }

  // 不要 粉丝团等级，显示Host。不要称号,魅力财富
  _buildProfile() {
    return RoomUserCardItemInfoWidget(
      uid: widget.uid,
      nickname: getCtl.roomUserInfo?.nickname,
      isAdmin: getCtl.roomUserInfo?.isAdmin == true,
      isOwner: getCtl.roomUserInfo?.isOwner == true,
      age: getCtl.roomUserInfo?.age,
      sex: getCtl.roomUserInfo?.sex,
      showAddress: getCtl.roomUserInfo?.showAddress,
    );
  }

  _buildInviteText() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.pt),
      child: Text(
        LocaleStrings.instance.inviteJoinRoomClub,
        style: TextStyle(
          color: R.color.primaryLightColor,
          fontSize: 18.pt,
          fontStyle: FontStyle.italic,
          fontWeight: FontWeightExt.heavy,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  _buildJoinBtn() {
    return GestureDetector(
      onTap: getCtl.joinFans,
      child: Container(
        decoration: BoxDecoration(
          gradient: R.color.btnPrimaryLinearGradient,
          borderRadius: BorderRadius.circular(11.pt),
        ),
        height: 46.ph,
        margin: EdgeInsets.symmetric(horizontal: 8.pt),
        alignment: Alignment.center,
        child: Obx(() {
          bool joining = getCtl.isJoiningFans.value;
          if (joining) {
            return GlobalWidgets.cupertinoIndicator(color: Colors.grey);
          }
          return Text(
            LocaleStrings.instance.joinRoomClub,
            style: TextStyle(
              color: roomTheme.theme.textColor,
              fontSize: 18.pt,
              fontWeight: FontWeightExt.heavy,
            ),
          );
        }),
      ),
    );
  }

  _buildFollowCheckBox() {
    return Obx(() {
      bool followed = widget.mainController.showOwnerFollow.value == false;
      if (followed) {
        return SizedBox.shrink();
      }
      bool checked = getCtl.checkFollowOwner.value;
      return FanFollowOwnerCheckBox(
        value: checked,
        onChanged: (value) {
          getCtl.checkFollowOwner.value = value;
        },
      );
    });
  }
}

class _JoinFansGuideController extends AbsGetController {
  final String uid;
  final RoomMainController mainController;

  RoomUserInfoModel? roomUserInfo;

  /// 加入粉丝团是否关注房主
  RxBool checkFollowOwner = true.obs;
  RxBool isJoiningFans = false.obs;

  _JoinFansGuideController(this.uid, this.mainController);

  @override
  void stateInit() {
    super.stateInit();
    // 没有关注过，显示附加-关注房主行为
    checkFollowOwner.value = mainController.showOwnerFollow.value == true;
    initData();
  }

  void initData() async {
    final res = await roomService.getRoomUserCard(uid, forceUpdate: true);
    if (res.isSuccess) {
      roomUserInfo = res.data;
      update();
    } else {
      toast(res.msg ?? LocaleStrings.instance.defaultError);
    }
  }

  void closePage() {
    routerUtil.pop(context: getContext()!);
  }

  void goToProfile() {
    closePage();
    routerUtil.push(R_USER_INFO, params: {
      P_UID: roomUserInfo?.uid,
      P_STATISTIC_FROM: StatisticPageFrom.liveRoomProfile,
      P_STATISTIC_MATCH_TYPE: StatisticPageFrom.liveRoomProfile,
    });
  }

  Future<void> joinFans() async {
    if (isJoiningFans.value) {
      return;
    }
    isJoiningFans.value = true;
    if (checkFollowOwner.value) {
      await mainController.followOwner(context: getContext()!, showToast: false);
    }
    bool success = await mainController.fansController.joinFans();
    isJoiningFans.value = false;
    if (success) {
      closePage();
      RoomFansDetailBottomSheetPage.showBottomSheet(mainController);
    }
  }
}
