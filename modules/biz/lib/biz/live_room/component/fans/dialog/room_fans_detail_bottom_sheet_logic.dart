import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/widgets/room_alert_dialog.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';
import 'package:service/modules/live/room_fans/model/room_fans_info_collection.dart';
import 'package:service/service.dart' hide Rx;
import 'package:service/service.dart';

import 'fans_name_edit_dialog.dart';

class RoomFansDetailBottomSheetLogic extends AbsGetController with GetStatusCtlMix, GetSingleTickerProviderStateMixin {
  late TabController tabController;
  Rxn<RoomFansInfoCollection> roomClubInfo = Rxn<RoomFansInfoCollection>();

  RxBool isFans = false.obs;

  /// 是否显示教堂彩带
  RxBool showChurchRibbon = true.obs;
  RxInt currentExpValue = 0.obs;
  RxInt currentLevelMaxExpValue = 1000.obs;
  RxInt currentLevel = 0.obs;

  List<FansGroupTabType> tabs = [FansGroupTabType.roomTask, FansGroupTabType.hostBenefits, FansGroupTabType.fansList];
  FansGroupTabType initTabType = FansGroupTabType.roomTask;

  String? clubId;

  RoomFansDetailBottomSheetLogic({FansGroupTabType? initTabIndex}) {
    if (initTabIndex != null) {
      initTabType = initTabIndex;
    }
  }

  @override
  void stateInit() async {
    super.stateInit();
    listenRxEvent<FansClubInfo?>(RoomFansGroupEvent.infoUpdate, _updateFansClubInfo);
    listenRxEvent<int>(RoomFansGroupEvent.fansDrawList, _onJumpFansDrawTab);
    listenRxEvent<int>(RoomFansGroupEvent.editedFansGroup, _onEditedFansGroup);

    bool isRoomOwner = roomService.isRoomOwner();
    if (isRoomOwner) {
      tabs.add(FansGroupTabType.treasureRecord);
    }
    isFans.value = roomService.fansService.isRoomFans();
    tabController = TabController(
        length: tabs.length,
        vsync: this,
        initialIndex: tabs.indexOf(initTabType) == -1 ? 0 : tabs.indexOf(initTabType));
    tabController.addListener(() {
      if (tabController.indexIsChanging) return;
      showChurchRibbon.value = tabs[tabController.index] == FansGroupTabType.hostBenefits;
    });
    _loadClubInfo();
  }

  @override
  void stateDispose() {
    tabController.dispose();
    super.stateDispose();
  }

  /// 加载club信息
  void _loadClubInfo({bool showLoading = true}) async {
    if (showLoading) {
      viewStatus = GetStatusView.loading;
    }
    clubId = roomService.fansService.getFansClubInfo()?.id ?? "0";
    FlatHttpResponse<RoomFansInfoCollection> resp =
        await roomService.fansService.fansClubMemberLevelInfo(clubId: clubId!);
    if (resp.isSuccess) {
      if (resp.data != null) {
        roomClubInfo.value = resp.data!;
        currentExpValue.value = resp.data!.memberInfo.contributionValue;
        currentLevel.value = resp.data!.memberInfo.level;
        roomService.fansService.updateFansClubInfo(level: currentLevel.value);

        if (resp.data!.levelCfgList.isNotEmpty && resp.data!.levelCfgList.length >= currentLevel.value) {
          int maxLevel = resp.data!.levelCfgList.last.level;
          if (currentLevel.value < maxLevel) {
            currentLevelMaxExpValue.value = resp.data!.levelCfgList[currentLevel.value].minVal;
          } else {
            currentLevelMaxExpValue.value = resp.data!.levelCfgList.last.minVal;
          }
        }
        viewStatus = GetStatusView.content;
      } else {
        viewStatus = GetStatusView.error;
      }
    } else {
      viewStatus = GetStatusView.error;
    }
  }

  /// 更新粉丝信息
  void _updateFansClubInfo(FansClubInfo? value) {
    bool oldFansValue = isFans.value;
    isFans.value = value?.hasJoin ?? false;

    if (!oldFansValue && isFans.value) {
      // 需要刷新粉丝信息
      _loadClubInfo();
    }
  }

  void _onEditedFansGroup(int value) {
    _loadClubInfo(showLoading: false);
  }

  String getRightTopRes() {
    if (roomService.isRoomOwner()) {
      return Res.commonHelp2;
    } else {
      if (isFans.value) {
        return Res.commonMore6;
      } else {
        return Res.commonHelp2;
      }
    }
  }

  void gotoIntoH5() {
    routerUtil.push(R_COMMON_WEB_PAGE, params: {"url": roomFansClubIntoUrl});
  }

  void onRightTopClick(BuildContext context) {
    if (roomService.isRoomOwner()) {
      gotoIntoH5();
    } else if (isFans.value) {
      // 退出粉丝团
      SmartDialog.showAttach(
        targetContext: context,
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 91.pt,
                height: 74.pt,
                margin: EdgeInsets.only(right: 5.pt),
                decoration:
                    BoxDecoration(color: R.color.backgroundColorDialog, borderRadius: BorderRadius.circular(5.pt)),
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        SmartDialog.dismiss();
                        gotoIntoH5();
                      },
                      child: Container(
                        height: 36.pt,
                        alignment: Alignment.center,
                        child: Text(
                          LocaleStrings.instance.viewRules,
                          style: TextStyle(fontSize: 13, fontWeight: FontWeightExt.heavy, color: Colors.white),
                        ),
                      ),
                    ),
                    Container(
                      height: 2.pt,
                      color: Color(0xff272626),
                    ),
                    GestureDetector(
                      onTap: _exitFansGroup,
                      child: Container(
                        height: 36.pt,
                        alignment: Alignment.center,
                        child: Text(
                          LocaleStrings.instance.quitGroup,
                          style: TextStyle(fontSize: 13, fontWeight: FontWeightExt.heavy, color: R.color.warningColor),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 64.pt),
                child: FLImage.asset(
                  Res.roomFansGroupTriangleBottomHelpDialog,
                  width: 12.pt,
                  height: 7.pt,
                ),
              )
            ],
          );
        },
        alignment: Alignment.topCenter,
        maskColor: Colors.transparent,
      );
    } else {
      gotoIntoH5();
    }
  }

  void jumpTab(FansGroupTabType tab) {
    tabController.animateTo(getTabIndex(tab));
  }

  int getTabIndex(FansGroupTabType tabType) {
    return tabs.indexOf(tabType);
  }

  void _onJumpFansDrawTab(int value) {
    if (roomService.isRoomOwner()) {
      jumpTab(FansGroupTabType.treasureRecord);
    }
  }

  /// 退出粉丝团
  void _exitFansGroup() async {
    SmartDialog.dismiss();
    showRoomAlertDialog(
      context: getContext(),
      title: LocaleStrings.instance.exitFansGroupTitle,
      content: LocaleStrings.instance.exitFansGroupTipsContent,
      cancelText: LocaleStrings.instance.exitFansGroupTipsConfirm,
      confirmText: LocaleStrings.instance.exitFansGroupTipsThinkAgain,
      onCancel: () {
        // 直接退出
        routerUtil.pop(context: getContext());
        exitFansClub();
      },
      onConfirm: () {
        // 退出并关闭
        // routerUtil.pop(context: getContext());
      },
    );
  }

  Future<void> exitFansClub() async {
    FlatHttpResponse response = await roomService.fansService.exitFansClub();
    if (response.isSuccess) {
      _reportExitRoomClub();
    }
    SmartDialog.dismiss();
  }

  /// 展示用户卡片
  void showUserInfoCard(String uid) {
    routerUtil.pop(context: getContext());
    showUserCardDialog(targetId: uid);
  }

  void _reportExitRoomClub() async {
    var user = await userService.getCurrentUserInfo();
    RoomStatistics.reportExitRoomClub(
      uid: user?.uid,
      roomId: roomService.getCurrentRoomId(),
      gender: user?.sexStr,
      ownerUid: roomService.getCurrentRoom()?.roomOwner,
    );
  }

  void onTapEditFansName() {
    if (roomClubInfo.value?.fansClubInfo.canUpdateName != true) {
      toast(LocaleStrings.instance.roomFansNameModifyTip(roomClubInfo.value?.fansClubInfo.updateNameLimitDay ?? 7),
          isDark: true);
    } else if (clubId != null) {
      showFansNameEditDialog(context: getContext(), clubId: clubId!, name: roomClubInfo.value?.fansClubInfo.name ?? '');
    }
  }
}

enum FansGroupTabType { roomTask, hostBenefits, fansList, treasureRecord }

extension FansGroupTabTypeExt on FansGroupTabType {
  Tab getFansGroupTab() {
    Tab resultTab = Tab(text: LocaleStrings.instance.fansGroupTabTitleHostBenefits);
    switch (this) {
      case FansGroupTabType.hostBenefits:
        resultTab = Tab(text: LocaleStrings.instance.fansGroupTabTitleHostBenefits);
        break;
      case FansGroupTabType.fansList:
        resultTab = Tab(text: LocaleStrings.instance.fansGroupTabTitleFansList);
        break;
      case FansGroupTabType.treasureRecord:
        resultTab = Tab(text: LocaleStrings.instance.fansGroupTabTitleTreasureRecord);
        break;
      case FansGroupTabType.roomTask:
        resultTab = Tab(text: LocaleStrings.instance.roomTask);
        break;
    }
    return resultTab;
  }
}
