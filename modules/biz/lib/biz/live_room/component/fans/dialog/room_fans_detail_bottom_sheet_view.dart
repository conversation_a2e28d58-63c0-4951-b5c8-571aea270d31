import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/fans/pages/fans_list_view.dart';
import 'package:biz/biz/live_room/component/fans/pages/fans_room_task_page.dart';
import 'package:biz/biz/live_room/component/fans/pages/host_benefits_view.dart';
import 'package:biz/biz/live_room/component/fans/pages/treasure_record_list_view.dart';
import 'package:biz/biz/live_room/component/fans/widget/room_club_level_widget.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:biz/global/widgets/styled_tab_indicator.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:service/global/widget/keep_wrapper.dart';
import 'package:service/global/widget/text_scroll.dart';

import 'room_fans_detail_bottom_sheet_logic.dart';

class RoomFansDetailBottomSheetPage extends StatefulWidget {
  RoomFansDetailBottomSheetPage({Key? key, required this.mainController, this.tabIndex}) : super(key: key);

  final RoomMainController mainController;
  final FansGroupTabType? tabIndex;

  static Future showBottomSheet(RoomMainController mainController, {FansGroupTabType? tabIndex}) {
    return dialog.showBottomSheet((context) {
      return RoomFansDetailBottomSheetPage(
        mainController: mainController,
        tabIndex: tabIndex,
      );
    }, routeSettings: RouteSettings(name: R_LIVE_ROOM_FANS_DETAIL_DIALOG));
  }

  @override
  State<RoomFansDetailBottomSheetPage> createState() => _RoomFansDetailBottomSheetPageState();
}

class _RoomFansDetailBottomSheetPageState extends State<RoomFansDetailBottomSheetPage>
    with GetStateBuilderMixin<RoomFansDetailBottomSheetPage, RoomFansDetailBottomSheetLogic> {

  @override
  bool isDart = true;

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return Container(
      width: 1.w,
      height: (getCtl.isFans.value ? 665 : 603) / 811 * 1.h,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          ..._buildFansList(),
          _buildFansTitleContainer(),
        ],
      ),
    );
  }

  @override
  RoomFansDetailBottomSheetLogic initCtl() {
    return RoomFansDetailBottomSheetLogic(initTabIndex: widget.tabIndex);
  }

  Widget _buildFansTitleContainer() {
    return PositionedDirectional(
      top: 0.pt,
      start: 13.pt,
      height: getCtl.isFans.value ? 138.pt : 76.pt,
      end: 13.pt,
      child: Obx(
        () => Stack(
          children: [
            // 矩形信息区域
            PositionedDirectional(
              start: 0,
              end: 0,
              bottom: 0,
              child: Container(
                alignment: Alignment.topLeft,
                height: getCtl.isFans.value ? 103.pt : 50.pt,
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: R.color.fansGradientColors,
                    ),
                    borderRadius: BorderRadius.circular(14.pt)),
                child: Column(
                  children: [
                    Row(
                      children: [
                        92.wSpace,
                        Expanded(child: _buildFansTitleName()),
                        _buildFansTitleEndImg(),
                      ],
                    ),
                    16.hSpace,
                    if (getCtl.isFans.value) _buildFansLevelProgress(),
                  ],
                ),
              ),
            ),
            // 头像区
            PositionedDirectional(
              start: 13.pt,
              child: UserAvatar(
                url: getCtl.roomClubInfo.value?.fansClubInfo.icon ?? "",
                size: 73.pt,
                borderColor: R.color.fansColor,
                borderWidth: 5.pt,
                userId: roomService.getCurrentRoom()?.roomOwner,
                onTap: ({String? rid}) {
                  if (roomService.getCurrentRoom()?.roomOwner?.isNotEmpty == true) {
                    getCtl.showUserInfoCard(roomService.getCurrentRoom()!.roomOwner!);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 标题区域的右边图片
  Widget _buildFansTitleEndImg() {
    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () => getCtl.onRightTopClick(context),
        child: FLImage.asset(
          getCtl.getRightTopRes(),
          width: 26.pt,
          height: 26.pt,
        ),
      );
    });
  }

  /// 标题区域的粉丝团名称
  Widget _buildFansTitleName() {
    return Row(
      children: [
        Flexible(
          child: Container(
            padding: EdgeInsets.only(top: 9.pt),
            child: TextScroll(
              getCtl.roomClubInfo.value?.fansClubInfo.name ?? "",
              style: TextStyle(
                fontWeight: FontWeightExt.heavy,
                fontStyle: FontStyle.italic,
                color: Colors.white,
                height: 1.1,
              ),
              pauseBetween: Duration(seconds: 3),
            ),
          ),
        ),
        if (roomService.isRoomOwner())
          GestureDetector(
            onTap: getCtl.onTapEditFansName,
            child: Padding(
              padding: EdgeInsetsDirectional.only(start: 5.pt, end: 5.pt, top: 9.pt),
              child: FLImage.asset(Res.commonUserIntroEdit, width: 19.pt, height: 19.pt, color: Colors.white),
            ),
          ),
      ],
    );
  }

  List<Widget> _buildFansList() {
    double bgHeight = 562 / 811 * 1.h;
    return [
      PositionedDirectional(
        bottom: 0,
        width: 1.w,
        height: bgHeight,
        child: ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(13.pt)),
          child: FLImage.asset(
            Res.roomFansGroupRoomFansClubChurchBg,
            width: 1.w,
            height: bgHeight,
            fit: BoxFit.cover,
          ),
        ),
      ),
      PositionedDirectional(
        start: 0,
        end: 0,
        bottom: 0,
        height: bgHeight,
        child: _buildRibbon(bgHeight),
      ),
      PositionedDirectional(
        start: 0,
        end: 0,
        bottom: 0,
        height: bgHeight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            40.hSpace,
            TabBar(
                isScrollable: true,
                controller: getCtl.tabController,
                unselectedLabelColor: Colors.white.withOpacity(0.5),
                unselectedLabelStyle: TextStyle(fontSize: 16.pt, fontStyle: FontStyle.italic),
                labelColor: Colors.white,
                labelStyle: TextStyle(fontSize: 16.pt, fontStyle: FontStyle.italic),
                indicatorSize: TabBarIndicatorSize.tab,
                indicator: StyledTabIndicator(width: 26.pt, height: 4.pt, fillColor: Colors.white),
                tabs: List.generate(getCtl.tabs.length, (index) => getCtl.tabs[index].getFansGroupTab())),
            Expanded(
              child: TabBarView(controller: getCtl.tabController, children: _tabs()),
            ),
          ],
        ),
      )
    ];
  }

  List<Widget> _tabs() {
    return [
      FansRoomTaskPage(mainController: widget.mainController),
      HostBenefitsPage(mainController: widget.mainController),
      FansListPage(),
      if (roomService.isRoomOwner()) TreasureRecordListPage(),
    ].map((e) => KeepWrapper(child: e)).toList();
  }

  Widget _buildFansLevelProgress() {
    return Container(
      height: 51.pt,
      margin: EdgeInsets.symmetric(horizontal: 11.pt),
      decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.41),
          borderRadius: BorderRadius.circular(11.pt),
          border: Border.all(color: Colors.white.withOpacity(0.21), width: 1.pt)),
      child: Row(
        children: [
          8.wSpace,
          UserAvatar(
            url: getCtl.roomClubInfo.value?.memberInfo.avatar,
            size: 35.pt,
            borderColor: Colors.white.withOpacity(0.5),
            borderWidth: 1.pt,
          ),
          8.wSpace,
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLevelProgress(),
                TextScroll(
                  velocity: const Velocity(pixelsPerSecond: Offset(50, 0)),
                  LocaleStrings.instance.fansGroupTips,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                  ),
                  pauseBetween: Duration(seconds: 3),
                ),
                // Text(
                //   LocaleStrings.instance.fansGroupTips,
                //   style: TextStyle(fontSize: 11, color: Colors.white.withOpacity(0.8)),
                // ),
              ],
            ),
          ),
          13.wSpace,
        ],
      ),
    );
  }

  _buildLevelProgress() {
    return RoomClubLevelWidget(
      progressPercent: getCtl.currentExpValue.value / getCtl.currentLevelMaxExpValue.value * 1.0,
      fansLevel: getCtl.roomClubInfo.value?.memberInfo.level ?? 0,
      fansLevelTotalExp: getCtl.currentLevelMaxExpValue.value,
      currentExp: getCtl.currentExpValue.value,
      maxLevel: getCtl.roomClubInfo.value?.levelCfgList.isNotEmpty == true
          ? (getCtl.roomClubInfo.value?.levelCfgList.last.level ?? 5)
          : 5,
    );
  }

  _buildRibbon(double height) {
    return Obx(() {
      bool show = getCtl.showChurchRibbon.value;
      Widget child;
      if (show) {
        child = FLImage.asset(
          Res.roomFansGroupRoomFansClubChurchRibbon,
          width: 1.w,
          height: height,
        );
      } else {
        child = SizedBox.shrink();
      }
      return AnimatedSwitcher(
        duration: Duration(milliseconds: 200),
        child: child,
      );
    });
  }
}
