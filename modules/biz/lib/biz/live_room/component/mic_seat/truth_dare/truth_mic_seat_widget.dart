import 'package:biz/biz.dart';
import 'package:biz/common/widgets/after_layout.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:biz/biz/live_room/component/mic_seat/truth_dare/truth_mic_seat_controller.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';

import 'package:biz/biz/live_room/component/mic_seat/truth_dare/circle_layout_delegate.dart';
import 'package:service/modules/live/mic_seat/model/mic_seat_model.dart';

import 'package:biz/biz/live_room/component/mic_seat/room_user/truth_dare/truth_dare_user_widget.dart';

import 'turntable/truth_turntable_widget.dart';

class TruthMicUserWidget extends StatefulWidget {
  final String getXTag;
  TruthMicUserWidget({
    Key? key,
    required this.getXTag,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TruthMicUserWidgetState();
}

class _TruthMicUserWidgetState extends State<TruthMicUserWidget>
    with TickerProviderStateMixin {
  late TruthMicSeatController _controller;

  @override
  void initState() {
    super.initState();
    RoomMainController mainController =
        Get.find<RoomMainController>(tag: widget.getXTag);
    _controller = TruthMicSeatController(mainController);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller.setupLayout(screenSize: MediaQuery.of(context).size);
    return GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
            init: _controller,
            autoRemove: false,
            global: false,
            builder: (controller) {
              return Container(
                width: 1.w,
                padding: EdgeInsets.only(top: 6.pt),
                alignment: Alignment.topCenter,
                child: _micWidget(),
              );
            }));
  }

  Widget _micWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    double width = screenWidth - 26.pt;

    double height = width / _controller.bgImageRadio;
    double turntableTop = 58 / 364 * height;

    List<Widget> micSeats = [];
    List<String> layoutIds = [];
    for (var item in _controller.mics) {
      int index = _controller.mics.indexOf(item);
      layoutIds.add('$index');
      micSeats.add(
          LayoutId(id: '$index', child: _seatUser(item: item, index: index)));
    }

    return Container(
      width: screenWidth - 26.pt,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(child: FLImage.asset(Res.roomTruthDareMicSeatBg)),
          PositionedDirectional(
            top: turntableTop,
            child: AfterLayout(
              callback: (ral) => _controller.onTruthTurntableLayout(),
              child: TruthTurntableWidget(
                isHost: _controller.isHost,
                controller: _controller.turntableAnimationController,
                initGameState: gameService.gameState,
              ),
            ),
          ),
          SizedBox(
            width: width,
            height: width,
            child: CustomMultiChildLayout(
              delegate: CircleLayoutDelegate(
                  customLayoutId: layoutIds,
                  center: Offset(width / 2, width / 2),
                  childSize: _controller.itemSize),
              children: micSeats,
            ),
          ),
          Positioned(top: 0, right: 0, child: _gameIntroduction()),
        ],
      ),
    );
  }

  Widget _seatUser({MicSeatItem? item, required int index}) {
    return TruthDareUserWidget(
      getXTag: widget.getXTag,
      itemSize: _controller.itemSize,
      avatarSize: _controller.avatarSize,
      seatItem: item,
      index: index + 1,
      showEmptyMicSeat: true,
      showMicSeatIndexLabel: true,
      onTap: () {
        _controller.onClickUser(item);
      },
    );
  }

  Widget _gameIntroduction() {
    return GestureDetector(
      onTap: _controller.onClickGameIntroduction,
      child: Container(
        width: 38.pt,
        height: 38.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
          Res.roomIntroduction,
          width: 18.pt,
          height: 18.pt,
        ),
      ),
    );
  }
}
