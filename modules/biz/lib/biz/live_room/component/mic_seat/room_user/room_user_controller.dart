import 'dart:async';
import 'dart:io';

import 'package:biz/biz/live_room/model/room_const.dart';
import 'package:biz/biz/live_room/model/truth_dare_selected_model.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:flutter/animation.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/modules/live/chat/model/chat_sticker_msg_content.dart';
import 'package:service/modules/live/mic_seat/model/gift_reward_statistics_model.dart';
import 'package:service/modules/live/mic_seat/model/mic_seat_model.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/service.dart';
class RoomUserController extends GetxController with AbsRoomModuleController, GetBindingMixin, GetEventMixin  {

  final RoomMainController mainController;
  RoomUserController(this.mainController);

  static bool _startShowFollowRoomBubbleTimer = false;

  final micEnable = false.obs;
  final enableSpeaking = false.obs;
  final enableApplyUpMicAnimating = false.obs;

  //真心话大冒险 游戏动画指定人 头像框
  final showGameAvatarFrame = false.obs;

  static const String micStickerUpdateKey = "mic_sticker_update";

  ChatStickerMsgContent? chatStickerMsg;

  final giftRewardStatistics = 0.obs;

  bool followRoomBubbleTip = false;

  String roomMode = RoomMode.CHAT;

  int? index;

  MicSeatItem? _seatItem;
  RoomUserInfoModel? currentUserInfo;
  AnimationController? animationController;
  AnimationController? emojiAnimationController;

  Timer? _stopTimer;

  Duration get transitionDuration => Duration(milliseconds: Platform.isAndroid ? 400 : 500);

  Timer? _followRoomBubbleTipTimer;
  bool showingFollowRoomBubbleTip = false;

  Timer? _localStickerTimer;

  static String getRoomUserTag({required String uid}) {
    return 'room_user_$uid';
  }

  @override
  void stateInit() async {
    super.stateInit();
    _initRx();
  }

  @override
  void stateDispose() {
    _stopTimer?.cancel();
    _stopTimer = null;
    _cancelLocalStickerTimer();
    _startShowFollowRoomBubbleTimer = false;
    _cancelFollowRoomBubbleTipTimer();
    animationController?.stop();
    super.stateDispose();
  }


  void _initRx() {
    listenRxEvent<List<String>>(RoomMicSeatEvent.volumeIndication, _onVolumeUpdate);
    listenRxEvent<bool>(RoomMicOptEvent.muteMic, _onMicMute);
    listenRxEvent<PbRoomUserNotice>(RoomMicSeatEvent.apply, _applyUpMic);
    listenRxEvent<ChatStickerMsgContent>(RoomMicStickerEvent.handleMsg, _handleMicSticker);
    listenRxEvent<GiftRewardStatisticsModel>(RoomMicSeatEvent.updateGiftRewardStatistics, _giftSendIncentiveNotice);
    if (roomMode == RoomMode.TRUTH_DARE) {
      listenRxEvent<TruthDareSelectedModel>(TruthDareBusinessEvent.avatarFrame, _truthDareGameEnd);
    }
  }

  String? currentSeatItemUid() {
    return _seatItem?.uid;
  }

  void updateSeatItem(MicSeatItem? seatItem) async {
    _seatItem = seatItem;
    currentUserInfo = await roomService.getCurrentUserInfo();
    micEnable.value = _seatItem?.isMuteMic == false;
    giftRewardStatistics.value = seatItem?.giftRewardStatistics ?? 0;

    if (_seatItem?.isShowAvatarFrame == true) {
      showGameAvatarFrame.value = true;
      _seatItem?.isShowAvatarFrame = false;
    } else {
      showGameAvatarFrame.value = false;
    }

    if (!_startShowFollowRoomBubbleTimer &&
        mainController.followRoomTargetId != null &&
        mainController.followRoomTargetId == _seatItem?.uid) {
      _startShowFollowRoomBubbleTimer = true;
      showingFollowRoomBubbleTip = true;
      startFollowRoomBubbleTipTimer();
    }
  }

  void startFollowRoomBubbleTipTimer() {
    _cancelFollowRoomBubbleTipTimer();
    _followRoomBubbleTipTimer = Timer(Duration(milliseconds: 3000), _autoHiddenFollowBubbleTip);
  }

  void _cancelFollowRoomBubbleTipTimer() {
    if (_followRoomBubbleTipTimer != null) {
      _followRoomBubbleTipTimer?.cancel();
      _followRoomBubbleTipTimer = null;
    }
  }

  void _onVolumeUpdate(List<String> uids) {
    if (_seatItem?.seatHadUser == true) {
      if (_seatItem?.uid == currentUserInfo?.uid && uids.contains('0')) {
        //当前是自己在说过
        _startSpeaking();
        return;
      }

      if (_seatItem?.uid?.isNotEmpty == true && uids.contains(_seatItem!.uid!)) {
        _startSpeaking();
      } else {
        _stopSpeaking();
      }
    } else {
      _stopSpeaking();
    }
  }

  void _startSpeaking() {
    _stopTimer?.cancel();
    _stopTimer = null;
    if (enableSpeaking.value != true) {
      enableSpeaking.value = true;
      animationController?.repeat();
    }
    _stopTimer = Timer(Duration(milliseconds: 1000), _stopSpeaking);
  }

  void _stopSpeaking() {
    if (getContext() != null) {
      enableSpeaking.value = false;
      animationController?.stop();
    }
    if (_stopTimer?.isActive == true) {
      _stopTimer?.cancel();
      _stopTimer = null;
    }
  }

  void _onMicMute(bool mute) {
    if (_seatItem?.seatHadUser == true) {
      if (_seatItem?.uid == currentUserInfo?.uid) {
        _seatItem?.muteMic = mute ? 1 : 0;
        micEnable.value = !mute;
      }
    }
  }

  void _applyUpMic(PbRoomUserNotice notice) {
    if (getContext() != null) {
      if (notice.user.user.uid == _seatItem?.uid && currentUserInfo?.isAdmin == true) {
        enableApplyUpMicAnimating.value = true;
        emojiAnimationController?.forward();
      }
    }
  }

  void stopEmojiAnimation() {
    if (getContext() != null) {
      emojiAnimationController?.reset();
      emojiAnimationController?.stop();
      enableApplyUpMicAnimating.value = false;
    }
  }

  void _truthDareGameEnd(TruthDareSelectedModel model) {
    if (model.uid != null && model.uid == _seatItem?.uid) {
      showGameAvatarFrame.value = model.showAvatarFrame;
    } else {
      showGameAvatarFrame.value = false;
    }
  }

  void _handleMicSticker(ChatStickerMsgContent stickerMsg) {
    if (_seatItem?.seatHadUser != true ||
        stickerMsg.chatUserInfo?.uid != _seatItem?.uid) {
      return;
    }
    _cancelLocalStickerTimer();

    chatStickerMsg = null;
    update([micStickerUpdateKey]);

    chatStickerMsg = stickerMsg;
    update([micStickerUpdateKey]);
  }

  void micStickerPlayCompletion() {
    chatStickerMsg = null;
    update([micStickerUpdateKey]);
  }

  void localMicStickerPlayCompletion() {
    _cancelLocalStickerTimer();
    _localStickerTimer = Timer(Duration(seconds: 3), (){
      _cancelLocalStickerTimer();
      chatStickerMsg = null;
      update([micStickerUpdateKey]);
    });
  }

  void _cancelLocalStickerTimer() {
    if (_localStickerTimer != null) {
      _localStickerTimer?.cancel();
      _localStickerTimer = null;
    }
  }

  void _autoHiddenFollowBubbleTip() {
    showingFollowRoomBubbleTip = false;
    mainController.resetFollowRoomTargetId();
    update();
  }

  void onClickFollowBubbleTip() async {
    if (_seatItem != null && _seatItem?.uid != null) {
      showingFollowRoomBubbleTip = false;
      UserInfo? userInfo = await userService.getUserInfo(_seatItem!.uid!);
      mainController.resetFollowRoomTargetId();
      rxUtil.send(RoomUserOptEvent.atUser, userInfo);
      update();
    }
  }

  void _giftSendIncentiveNotice(GiftRewardStatisticsModel statisticsModel) {
    if (_seatItem?.seatHadUser == true) {
      StatisticsValListItem? statisticsItem = statisticsModel.statisticsValMap?[_seatItem?.uid];
      if (statisticsItem != null) {
        _seatItem?.giftRewardStatistics = statisticsItem.curStatisticsVal;
        giftRewardStatistics.value = _seatItem?.giftRewardStatistics ?? 0;
      }
    }
  }
}