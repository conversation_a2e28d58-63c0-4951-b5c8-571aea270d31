
import 'dart:async';
import 'dart:math';
import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/mic_seat/room_user/base/base_mic_seat_user_widget.dart';
import 'package:biz/biz/live_room/component/mic_seat/room_user/room_user_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/live/mic_seat/const/enums.dart';
import 'package:service/modules/live/mic_seat/model/mic_seat_model.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
class ChatMicSeatController extends GetxController with AbsRoomModuleController, GetBindingMixin, GetEventMixin {

  static const String _micSeatTag = "ChatRoomMicSeat";
  final RoomMainController mainController;

  ChatMicSeatController(this.mainController);

  final int maxItemCount = 32;

  // 麦位 widget的高度
  double widgetHeight = 230.pt;
  Size itemSize = Size.zero;
  Size avatarSize = Size.zero;
  double itemSpace = 0;
  double topMargin = 0;
  double leftMargin = 10.pt;

  int rowMaxItemCount = 4;

  //麦位数据
  final List<MicSeatItem> _mics = [];

  //所有人列表，包含真实在麦位上的人
  List<MicSeatItem> dataSources = [];

  int _lastTapTime = 0;

  //所有人列表，快捷去重
  final Map<String?, MicSeatItem?> _micSeatListMap = {};

  bool _initData = false;

  bool _inMicRequestCompletion = false;

  Timer? _timer;

  int onlinePage = 0;

  int onlineTotalSize = 0;

  final showRemainMore = false.obs;
  final showShadow = false.obs;

  Map<String, GlobalKey<State<StatefulWidget>>> micSeatGlobalKeys = {};

  @override
  void stateInit() async {
    super.stateInit();
    _initRx();
  }

  @override
  void stateDispose() {
    _cancelTimer();
    _inMicRequestCompletion = false;
    _mics.clear();
    micSeatGlobalKeys.clear();
    dataSources.clear();
    _micSeatListMap.clear();
    _initData = false;
    super.stateDispose();
  }

  @override
  void firstFrame() {
    if (!_initData) {
      _initData = true;
      fetchRoomUser();
    }
  }

  void _initRx() {
    listenRxEvent<List<MicSeatItem>>(RoomMicSeatEvent.update, _micSeatUpdate);
    listenRxEvent<List<RoomUserInfoModel>?>(RoomEvent.updateEnterRoomOnLineUsers, _updateEnterRoomUserList);
    listenRxEvent<List<RoomUserInfoModel>?>(RoomEvent.updateLeaveRoomOnLineUsers, _updateLeaveRoomUserList);
    listenRxEvent<PbRoomUserNotice>(RoomMicSeatEvent.apply, _applyUpMic);
    listenRxEvent<PbRoomUserNotice>(RoomEvent.kickMic, _kickMic);
    listenRxEvent<int>(RoomEvent.updateOnLineNum, _updateRoomOnlineCount);
    listenRxEvent<PbRoomUser>(RoomUserEvent.addAdmin, _addAndRemoveUserAddAdmin);
    listenRxEvent<PbRoomUser>(RoomUserEvent.removeAdmin, _addAndRemoveUserAddAdmin);
  }

  void fetchRoomUser() async {
    _mics.clear();
    dataSources.clear();
    _micSeatListMap.clear();
    await micSeatService.fetchMicSeat(force: true);

    _timer = Timer.periodic(Duration(seconds: 300), (timer) {
      micSeatService.fetchMicSeat(force: true);
    });

    // Timer.periodic(Duration(seconds: 2), (timer) {
    //      addTestItem();
    // });
  }

  // void addTestItem() {
  //      RoomUserInfoModel info = RoomUserInfoModel();
  //      info.uid = '${Random().nextDouble() * (999999999 - 100000000) + 100000000}';
  //      info.avatar = "https://res.winker.chat/nova/user/avatar/avatar_code/female_23.jpg";
  //      info.nickname = "Winky Username";
  //      info.sexStr = "female";
  //      info.age = 24;
  //      UserIdentity identity =  UserIdentity();
  //      identity.type = 5;
  //      info.identity = identity;
  //      MicSeatItem? item = _packageRoomUserInfoToMicSeatItem(info);
  //      dataSources.add(item!);
  //      _updateList();
  // }

  void _cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  void getApplyUsers() async {
    var applyMicListResp = await micSeatService.applyMic(
        action: ApplyMicAction.list,
        roomId: roomService.getCurrentRoom()?.roomId
    );
    if (applyMicListResp.isSuccess == true) {
      if (applyMicListResp.data?.listUser?.isNotEmpty == true) {
        var roomUser = await roomService.getCurrentUserInfo();
        if (roomUser?.isAdmin == true) {
          rxUtil.send(RoomMicSeatEvent.applyNumUpdate, applyMicListResp.data?.listUser!.length);
        }
        _updateOtherUserList(applyMicListResp.data?.listUser, apply: true);
      }
      getOnlineUsers();
    } else {
      getOnlineUsers();
    }
  }

  void getOnlineUsers() async {

    // OnlineUserListModel listModel = OnlineUserListModel();
    // List<RoomUserInfoModel> roomUser = [];
    // for (int i = 0; i < 36; i++) {
    //      RoomUserInfoModel info = RoomUserInfoModel();
    //      info.uid = '${Random().nextDouble() * (999999999 - 100000000) + 100000000}';
    //      info.avatar = "https://res.winker.chat/nova/user/avatar/avatar_code/female_23.jpg";
    //      info.nickname = "Winky Username";
    //      info.sexStr = "female";
    //      info.age = 24;
    //      UserIdentity identity =  UserIdentity();
    //      identity.type = 5;
    //      info.identity = identity;
    //      roomUser.add(info);
    // }
    // listModel.roomUser = roomUser;
    // listModel.total = 36;
    // _updateOtherUserList(listModel.roomUser);
    var onlineListResp = await roomService.getRoomUserListByPage(page: onlinePage);
    if (onlineListResp?.isSuccess == true) {
      onlineTotalSize = onlineListResp?.data?.total ?? 0;
      roomService.mergeRoomInfo(roomUserCount: onlineListResp?.data?.total);
      rxUtil.send(RoomEvent.updateOnLineNum, onlineTotalSize);

      if (onlineListResp?.data?.roomUser?.isNotEmpty == true) {
        _updateOtherUserList(onlineListResp!.data!.roomUser);
      }
    }
  }

  void _updateOtherUserList(List<RoomUserInfoModel>? roomUsers, {bool apply = false}) async {
    if (roomUsers != null && roomUsers.isNotEmpty == true) {
      bool isNeedUpdate = false;
      for (var userInfoModel in roomUsers) {
        MicSeatItem? item = _packageRoomUserInfoToMicSeatItem(userInfoModel);
        if (item != null) {
          if (apply) {
            item.isApplyMic = true;
          }
          _micSeatListMap[item.uid] = item;
          dataSources.add(item);
          isNeedUpdate = true;
        }
      }
      if (isNeedUpdate) {
        _updateList();
      }
    }
  }

  MicSeatItem? _packageRoomUserInfoToMicSeatItem(RoomUserInfoModel userInfoModel) {
    if (userInfoModel.uid == null ||
        userInfoModel.uid == '0' ||
        userInfoModel.uid == '-1' ||
        userInfoModel.uid?.trim() == '') return null;

    if (_micSeatListMap.containsKey(userInfoModel.uid)) return null;

    MicSeatItem item = MicSeatItem();
    item.uid = userInfoModel.uid;
    item.nickname = userInfoModel.nickname;
    item.headImgUrl = userInfoModel.avatar;
    item.age = userInfoModel.age;
    item.gender = userInfoModel.sex == Sex.male ? 1 : 2;

    UserIdentity identity = UserIdentity();
    identity.type = userInfoModel.identity?.type;
    identity.icon = userInfoModel.identity?.icon;
    identity.userType = userInfoModel.identity?.userType;
    item.identity = identity;

    item.headWear = userInfoModel.headWear;

    return item;
  }

  void _updateList({bool adjustSort = true}) {
    if (adjustSort) {
      _filterSeatItemList();
    }
    _setupRoomUserLayout();
    update();
  }

  //配置麦位layout
  void _setupRoomUserLayout() {
    int itemCount = dataSources.length;
    if (itemCount <= 0) return;

    double screenWidth = MediaQuery.of(getContext()!).size.width;
    itemSpace = 10.pt;
    leftMargin = 10.pt;
    double itemWidth = 0;
    double itemHeight = 0;

    //头像与水波纹的大小
    double radio = 1 / kMicSeatAvatarSpeakingRadio;

    if (itemCount == 2) {
      itemSpace = 35.pt;
    }

    if (itemCount <= rowMaxItemCount) {
      showShadow.value = false;
      topMargin = 53.pt;
      itemWidth = (screenWidth - 2 * leftMargin - (itemCount - 1) * itemSpace) / itemCount;

      if (itemWidth > 125.pt) {
        itemWidth = 125.pt;
        leftMargin = ((screenWidth - itemCount * itemWidth - (itemCount - 1) * itemSpace) / 2).floorToDouble();
        if (leftMargin < 0) leftMargin = 10.pt;
      }
      double avatarWidth = (radio * itemWidth).floorToDouble();
      itemHeight = ((itemWidth - avatarWidth) / 2 + avatarWidth + 43.pt)
          .ceilToDouble();

      avatarSize = Size(avatarWidth, avatarWidth);
      itemSize = Size(itemWidth, itemHeight);
      widgetHeight = itemHeight * 2;
      if (itemCount <= 4) {
        widgetHeight = widgetHeight > 230.pt ? 230.pt : widgetHeight;
      }
      return;
    }

    topMargin = 6.pt;

    itemWidth = (screenWidth - 2 * leftMargin - (rowMaxItemCount - 1) * itemSpace) / rowMaxItemCount;

    double avatarWidth = (radio * itemWidth).floorToDouble();
    itemHeight = ((itemWidth - avatarWidth) / 2 + avatarWidth + 41.pt).ceilToDouble();

    avatarSize = Size(avatarWidth, avatarWidth);
    itemSize = Size(itemWidth, itemHeight);

    int row = (itemCount / rowMaxItemCount).ceil();
    widgetHeight = itemHeight * row + topMargin + 10.pt;
    if (row > 3) {
      widgetHeight = 3.4 * itemHeight;
      showShadow.value = true;
    } else {
      showShadow.value = false;
    }
  }

  void onClickUser(MicSeatItem? item) {
    /// 防止重复点击
    var time = DateTime.now().millisecondsSinceEpoch;
    if (time - _lastTapTime < 600) {
      return;
    }
    _lastTapTime = time;
    showUserCardDialog(targetId: item!.uid!);
  }

  void _micSeatUpdate(List<MicSeatItem> updates) async {
    // if (updates.isEmpty) {
    //   if (!_inMicRequestCompletion) {
    //     _inMicRequestCompletion = true;
    //     getApplyUsers();
    //   }
    //   return;
    // };

    final list = updates.where(
            (element) => element.hasUser)
        .toList();

    _mics.clear();
    if (list.isNotEmpty) {
      _mics.addAll(list);

      for (var element in _mics) {
        if (_micSeatListMap.containsKey(element.uid)) {
          dataSources.remove(_micSeatListMap[element.uid]);
          _micSeatListMap.remove(element.uid);
        }
        _micSeatListMap[element.uid] = element;
      }
    }

    for (var element in dataSources) {
      element.position = 0;
      element.isMic = 0;
      element.muteMic = 0;
    }

    dataSources.insertAll(0, _mics);
    _updateList();

    if (!_inMicRequestCompletion) {
      _inMicRequestCompletion = true;
      getApplyUsers();
    }
  }

  void _kickMic(PbRoomUserNotice notice) async {
    MicSeatItem? item = _micSeatListMap[notice.user.user.uid];
    if (item != null) {
      item.isMic = 0;
      item.muteMic = 0;
      _updateList();
    }
  }

  void _filterSeatItemList()  {
    List<MicSeatItem> inMicList = [];
    List<MicSeatItem> applyMicList = [];
    List<MicSeatItem> otherMicList = [];
    var uid = accountService.currentUidOfCache();
    for (var element in dataSources) {
      if (element.isOwner || (element.seatHadUser)) {
        inMicList.add(element);
      } else if (element.isApplyMic == true) {
        applyMicList.add(element);
      } else if (element.uid == uid && element.isMic != 1) {
        applyMicList.insert(0, element);
      } else {
        otherMicList.add(element);
      }
      micSeatGlobalKey(uid: element.uid!);
    }
    dataSources.clear();
    dataSources.addAll(inMicList);
    dataSources.addAll(applyMicList);
    dataSources.addAll(otherMicList);
    if (dataSources.length > maxItemCount) {
      dataSources = dataSources.sublist(0, maxItemCount);
    }
  }

  void _applyUpMic(PbRoomUserNotice notice) async {
    MicSeatItem? item = _micSeatListMap[notice.user.user.uid];
    if (item != null) {
      dataSources.remove(item);
      item.isApplyMic = true;
      dataSources.insert(_mics.length, item);

      if (dataSources.length > maxItemCount) {
        dataSources = dataSources.sublist(0, maxItemCount);
      }
    } else {

      PbUser noticeUser = notice.user.user;
      MicSeatItem item = MicSeatItem();
      item.uid = noticeUser.uid;
      item.nickname = noticeUser.nickname;
      item.headImgUrl = noticeUser.headimgurl;
      item.age = noticeUser.age;
      item.gender = noticeUser.sex == Sex.male ? 1 : 2;

      UserIdentity identity = UserIdentity();
      identity.type = notice.user.identity.type;
      identity.icon = notice.user.identity.icon;
      identity.userType = notice.user.identity.userType;
      item.identity = identity;

      item.headWear = UserHeadWear.fromPb(notice.user.headwearInfo);

      item.isApplyMic = true;
      dataSources.insert(_mics.length, item);

      if (dataSources.length > maxItemCount) {
        dataSources = dataSources.sublist(0, maxItemCount);
      }
    }
    _updateList();
  }

  void _updateEnterRoomUserList(List<RoomUserInfoModel>? onLineUsers) async {
    if (dataSources.length < maxItemCount && onLineUsers?.isNotEmpty == true) {
      bool isNeedUpdate = false;
      for (var element in onLineUsers!) {
        MicSeatItem? item = _packageRoomUserInfoToMicSeatItem(element);
        if (item != null) {
          _micSeatListMap[element.uid] = item;
          dataSources.add(item);
          isNeedUpdate = true;
        }
      }

      if (isNeedUpdate) {
        _updateList();
      }
    }
  }

  void _updateLeaveRoomUserList(List<RoomUserInfoModel>? onLineUsers) async {
    if (onLineUsers?.isNotEmpty == true) {
      for (var element in onLineUsers!) {
        MicSeatItem? item = _micSeatListMap[element.uid!];
        if (item != null) {
          _micSeatListMap.remove(item.uid);
          dataSources.remove(item);
          String tag =  RoomUserController.getRoomUserTag(uid: element.uid!);
          Get.delete<RoomUserController>(tag: tag);
          micSeatGlobalKeys.remove(tag);
          _updateList();
          break;
        }
      }

    }
  }

  void _updateRoomOnlineCount(int count) {
    if (count > maxItemCount) {
      if (showRemainMore.value != true) {
        showRemainMore.value = true;
      }
    } else {
      showRemainMore.value = false;
    }
  }

  void _addAndRemoveUserAddAdmin(PbRoomUser user) {
    MicSeatItem? item = _micSeatListMap[user.user.uid];
    if (item != null) {
      item.identity?.type = user.identity.type;
      item.identity?.icon = user.identity.icon;
      item.identity?.userType = user.identity.userType;
      update();
    }
  }

  GlobalKey<State<StatefulWidget>>? micSeatGlobalKey({required String uid}) {
    String keyString =  RoomUserController.getRoomUserTag(uid: uid);
    if (!micSeatGlobalKeys.containsKey(keyString)) {
      micSeatGlobalKeys[keyString] = GlobalKey();
    }
    return micSeatGlobalKeys[keyString];
  }
}