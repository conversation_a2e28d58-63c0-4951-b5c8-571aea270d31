import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/mic_seat/blind_date/blind_date_game_process_widget.dart';
import 'package:biz/biz/live_room/component/mic_seat/blind_date/blind_date_mic_seat_controller.dart';
import 'package:biz/biz/live_room/component/mic_seat/room_user/blind_date/blind_date_user_widget.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/modules/live/mic_seat/model/mic_seat_model.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/live/room_factory_type/blind_date/blind_date_model.dart';

import 'widget/bd_center_status_widget.dart';
import 'widget/bg_game_result_widget.dart';

class BlindDateMicSeatWidget extends StatefulWidget {
  final String getXTag;

  BlindDateMicSeatWidget({
    Key? key,
    required this.getXTag,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _BlindDateMicSeatWidgetState();
}

class _BlindDateMicSeatWidgetState extends State<BlindDateMicSeatWidget> {
  late BlindDateMicSeatController _controller;

  @override
  void initState() {
    super.initState();
    RoomMainController mainController = Get.find<RoomMainController>(tag: widget.getXTag);
    _controller = BlindDateMicSeatController(mainController);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller.setupLayout(screenSize: MediaQuery.of(context).size);
    return GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
            init: _controller,
            autoRemove: false,
            global: false,
            builder: (controller) {
              return Container(
                width: 1.w,
                height: _controller.micSeatHeight + 60.pt + 13.pt,
                child: _page(),
              );
            }));
  }

  Widget _page() {
    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        _topView(),
        // CpGameProcessWidget(),
        Positioned(
          top: 60.pt,
          child: _micSeatContainer(),
        ),
        Positioned(
          top: 30.pt,
          child: Container(
            height: _controller.micSeatHeight,
            child: Center(
              child: BlindDateCenterStatusWidget(_controller),
            ),
          ),
        ),
        PositionedDirectional(
          top: 90.pt,
          child: BlindDateGameResultWidget(
            controller: _controller,
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _onlineList(),
        )
      ],
    );
  }

  Widget _topView() {
    return Positioned(
      left: 13.pt,
      top: 0,
      right: 0,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _hostMicSeat(),
          SizedBox(width: 8.pt),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: (_controller.itemSize.width - _controller.avatarSize.width) / 2),
              child: BlindDateGameProcessWidget(
                stage: _controller.blindDateService.stage,
                stageName: _controller.blindDateService.data?.stageName ?? '',
              ),
            ),
          ),
          SizedBox(width: 27.pt),
          Padding(
            padding: EdgeInsets.only(
                top: (_controller.itemSize.width - _controller.avatarSize.width) / 2 +
                    _controller.avatarSize.height / 2 -
                    19.pt),
            child: _gameIntroduction(),
          ),
          SizedBox(width: 3.pt),
        ],
      ),
    );
  }

  Widget _hostMicSeat() {
    return _micSeatUserItem(index: 0);
  }

  Widget _micSeatContainer() {
    return Visibility(
      visible: _controller.blindDateService.stage != BlindDateStage.resultAnnouncement,
      child: Container(
        width: _controller.micSeatWidth,
        height: _controller.micSeatHeight,
        child: Stack(
          children: [
            Positioned(
              top: 24.pt,
              left: 0,
              right: 0,
              bottom: 0,
              child: FLImage.asset(Res.roomBlindDateMicSeatBg),
            ),
            Positioned(
              top: 0,
              left: 67.pt,
              child: _micSeatUserItem(index: 1),
            ),
            Positioned(
              top: 94.pt,
              left: 0,
              child: _micSeatUserItem(index: 2),
            ),
            Positioned(
              bottom: 43.pt,
              left: 67.pt,
              child: _micSeatUserItem(index: 3),
            ),
            Positioned(
              bottom: 43.pt,
              right: 67.pt,
              child: _micSeatUserItem(index: 4),
            ),
            Positioned(
              top: 94.pt,
              right: 0,
              child: _micSeatUserItem(index: 5),
            ),
            Positioned(
              top: 0,
              right: 67.pt,
              child: _micSeatUserItem(index: 6),
            ),
          ],
        ),
      ),
    );
  }

  Widget _micSeatUserItem({required int index}) {
    MicSeatItem? seatItem = _controller.getMicSeatItem(index: index);
    return BlindDateUserWidget(
      getXTag: widget.getXTag,
      itemSize: _controller.itemSize,
      avatarSize: _controller.avatarSize,
      seatItem: seatItem,
      index: index,
      showEmptyMicSeat: true,
      showMicSeatIndexLabel: index != 0,
      showAvatarBorder: true,
      avatarBorderWidth: 2,
      avatarBorderColor: Colors.white,
      blindDateService: _controller.blindDateService,
      onChooseUser: (String? uid) {
        _controller.interactBlindDateGame(uid: uid);
      },
      onTap: () {
        _controller.onClickUser(seatItem);
      },
    );
  }

  Widget _gameIntroduction() {
    return GestureDetector(
      onTap: _controller.onClickGameIntroduction,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: 38.pt,
        height: 38.pt,
        alignment: Alignment.center,
        child: FLImage.asset(
          Res.roomIntroduction,
          width: 18.pt,
          height: 18.pt,
        ),
      ),
    );
  }

  Widget _onlineList() {
    return GetBuilder(
        init: _controller,
        autoRemove: false,
        global: false,
        id: _controller.onlineUserTag,
        builder: (controller) {
          return Container(
            height: (36.pt * kUserAvatarFrameRadio).ceilToDouble(),
            child: _controller.onlineList.isEmpty == true
                ? SizedBox.shrink()
                : GridView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(horizontal: 13.pt),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 1, crossAxisSpacing: 10.pt, childAspectRatio: 1),
                    shrinkWrap: true,
                    itemCount: _controller.onlineList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _onlineItem(index);
                    }),
          );
        });
  }

  Widget _onlineItem(int index) {
    RoomUserInfoModel infoModel = _controller.onlineList[index];
    return UserAvatar(
      url: infoModel.avatar,
      avatarCode: infoModel.avatarCode,
      size: 36.pt,
      hasFrame: true,
      inRoom: true,
      userId: infoModel.uid,
      onTap: ({String? rid}) {
        _controller.onClickOnlineUser(infoModel.uid);
      },
    );
  }
}
