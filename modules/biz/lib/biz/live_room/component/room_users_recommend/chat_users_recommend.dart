// import 'package:biz/biz/live/room_helper.dart';
// import 'package:biz/biz/vip/widgets/vip_nick_widget.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/msg_list_statistics.g.dart';
import 'package:service/common/statistics/premium_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';
import 'package:service/modules/live/room/model/user_live_status.dart';
import 'package:service/service.dart';

import 'bloc/party_users_bloc.dart';

class ChatUsersRecommend extends StatelessWidget {
  ChatUsersRecommend(
      {required RoomUsersRecommendType type,
      String? groupId,
      this.showUserName = true,
      this.height,
      this.boxDecoration,
      this.margin,
      this.from})
      : _bloc = type == RoomUsersRecommendType.familyChat
            ? PartyUsersBloc(type: type, groupId: groupId, autoRefresh: true)
            : PartyUsersBloc(type: type, size: 15, autoRefresh: true);
  final PartyUsersBloc _bloc;

  final bool showUserName;

  final double? height;

  final BoxDecoration? boxDecoration;

  final EdgeInsets? margin;

  final String? from;

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<PartyUsersBloc, PartyUsersState>(
        bloc: _bloc,
        builder: (context, state) {
          return _body(context);
        });
  }

  ///列表
  Widget _body(BuildContext context) {
    if (_bloc.state.list?.isEmpty ?? true) {
      return SizedBox();
    }

    return Container(
      width: double.infinity,
      height: height ?? 100.pt,
      decoration: boxDecoration,
      alignment: Alignment.center,
      margin: margin ?? EdgeInsets.only(top: 6.pt, bottom: 6.pt),
      padding: EdgeInsets.only(right: 16.pt),
      child: SmartRefresher(
        scrollDirection: Axis.horizontal,
        controller: _bloc.refreshController,
        onLoading: () => _bloc.add(PartyUsersEventLoadMore()),
        enablePullDown: false,
        enablePullUp: _bloc.state.hasMore,
        physics: BouncingScrollPhysics(),
        footer: GlobalWidgets.refreshFoot(iconPos: IconPosition.top),
        child: _list(),
      ),
    );
  }

  Widget _list() {
    final list = _bloc.state.list ?? [];
    return ListView.builder(
      itemCount: list.length,
      shrinkWrap: false,
      scrollDirection: Axis.horizontal,
      cacheExtent: 360.pt,
      itemBuilder: (BuildContext context, int index) {
        return _item(list[index]);
      },
    );
  }

  Widget _item(RoomRecoUserInfo roomRecoUserInfo) {
    return ScaleTapWidget(
      onTap: () => _joinRoom(roomRecoUserInfo),
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 8.5.pt),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(1.5.pt),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(colors: [
                  Color(0xFF8100FF),
                  Color(0xFFB600FF),
                ], begin: Alignment.topLeft, end: Alignment.bottomRight),
              ),
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
                padding: EdgeInsets.all(2.pt),
                child: UserAvatar(url: roomRecoUserInfo.avatar, size: 37.pt),
              ),
            ),
            // SizedBox(height: 5.pt),
            // Visibility(
            //     visible: showUserName,
            //     child: UserNickWidget(
            //         child: ConstrainedBox(
            //           constraints: BoxConstraints(
            //             maxWidth: 62.pt,
            //           ),
            //           child: Text(
            //             roomRecoUserInfo.displayedName,
            //             maxLines: 1,
            //             overflow: TextOverflow.ellipsis,
            //             style: TextStyle(
            //               color: primaryColorText,
            //               fontSize: 12.sp,
            //             ),
            //           ),
            //         ),
            //         vipLevel: roomRecoUserInfo.vipInfo?.level ?? 0)),
          ],
        ),
      ),
    );
  }

  void _joinRoom(RoomRecoUserInfo user) async {
    MsgListStatistics.reportMsgUserGuideClick(
        roomId: user.roomId,
        toUid: user.uid,
        toGender: user.sexStr,
        from: from);
    final resp = await roomService.getUserInRoom(user.uid);
    if (resp.isSuccess) {
      String roomId = resp.data?.roomId ?? "";
      if (roomId.isEmpty) {
        toast(LocaleStrings.instance.userNotInRoom);
        roomService.updateUserRoomStatus(UserLiveStatusModel()
          ..uid = user.uid
          // ..liveState = false
          ..status = false);
        return;
      }
      LiveRoomHandler.joinRoom(roomId, from: from, followJoin: true);
      return;
    }

    /// 用户开启了防跟随
    if (resp.code == 1040) {
      PremiumStatistics.reportPreventFollowResultImp(
          toUid: user.uid, toGender: user.sexStr);
    }
    toast(resp.msg ?? LocaleStrings.instance.pleaseTryAgain);
  }
}
