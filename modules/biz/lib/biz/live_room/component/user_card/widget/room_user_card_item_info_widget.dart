import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/biz/user/title/widget/title_item_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_color_name_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/mall/model/user_title_model.dart';
import 'package:service/service.dart';

/// 房间个人卡片的部分信息：
/// 1.用户粉丝等级，昵称
/// 2.房主/管理员标识，性别年龄，城市，魅力财富等级
/// 3.用户称号
class RoomUserCardItemInfoWidget extends StatelessWidget {
  final String uid;
  final String? nickname;
  final int? fansLevel;
  final String? fansGroupName;
  final bool isAdmin;
  final bool isOwner;
  final Sex? sex;
  final int? age;
  final String? showAddress;

  final String? charmLabelIcon;
  final int? charmLevel;
  final String? wealthLabelIcon;
  final int? wealthLevel;
  final List<UserTitleModel>? userTitles;
  final GestureTapCallback? onTapTitleMore;

  const RoomUserCardItemInfoWidget({
    Key? key,
    required this.uid,
    required this.isAdmin,
    required this.isOwner,
    this.sex,
    this.nickname,
    this.fansLevel,
    this.fansGroupName,
    this.age,
    this.showAddress,
    this.charmLabelIcon,
    this.charmLevel,
    this.wealthLabelIcon,
    this.wealthLevel,
    this.userTitles,
    this.onTapTitleMore,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _userName(),
        _ageWidget(),
        _userTitles(context),
      ],
    );
  }

  Widget _userName() {
    return Padding(
      padding: EdgeInsets.only(left: 10.pt, right: 10.pt),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          UserFanLevelMetaWidget(
            level: fansLevel,
            fansGroupName: fansGroupName,
            padding: EdgeInsetsDirectional.only(end: 5.pt),
          ),
          UserColorNameWidget(
            style: TextStyle(
                color: Colors.white, fontSize: 18.pt, fontWeight: FontWeightExt.medium, fontStyle: FontStyle.italic),
            uid: uid,
            name: nickname ?? '',
            maxWidth: 180.pt,
          ),
        ],
      ),
    );
  }

  Widget _ageWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _roleTag(),
        _gender(),
        _cityTag(),
        CharmWealthLabelsWidget(
          charmLabelIcon: charmLabelIcon ?? "",
          charmLevel: charmLevel ?? 0,
          wealthLabelIcon: wealthLabelIcon ?? "",
          wealthLevel: wealthLevel ?? 0,
          padding: EdgeInsets.only(left: 5.pt),
        ),
      ],
    );
  }

  Widget _roleTag() {
    bool isVisible = isAdmin == true;
    return Visibility(
      visible: isVisible,
      child: Container(
        height: 14.pt,
        margin: EdgeInsets.only(right: 5.pt),
        padding: EdgeInsets.symmetric(horizontal: 6.pt),
        alignment: Alignment.center,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(7.pt), color: R.color.secondaryYellowColor),
        child: Text(
          isOwner == true ? LocaleStrings.instance.owner : LocaleStrings.instance.admin,
          style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.heavy),
        ),
      ),
    );
  }

  Widget _gender() {
    bool isFemale = sex == Sex.female;
    String genderRes = isFemale ? Res.profileFemale : Res.profileMale;

    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 3.pt),
      height: 14.pt,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7.pt), color: isFemale ? R.color.femaleColor : R.color.maleColor),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FLImage.asset(genderRes, color: Colors.white, width: 12.pt, height: 12.pt),
          Visibility(
              visible: age != null && age! > 0,
              child: Text(
                '$age',
                style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.medium),
              ))
        ],
      ),
    );
  }

  Widget _cityTag() {
    bool isVisible = showAddress?.isNotEmpty == true;
    return Visibility(
      visible: isVisible,
      child: Container(
          height: 14.pt,
          margin: EdgeInsets.only(left: 5.pt),
          padding: EdgeInsets.symmetric(horizontal: 6.pt),
          alignment: Alignment.center,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(7.pt), color: R.color.textColor4),
          child: Row(
            children: [
              FLImage.asset(Res.roomUserCardLocation, width: 7.pt, height: 9.pt),
              GlobalWidgets.spacingHorizontal(3.pt),
              Text(
                showAddress ?? "",
                style: TextStyle(color: Colors.white, fontSize: 8.sp, fontWeight: FontWeightExt.heavy),
              )
            ],
          )),
    );
  }

  Widget _userTitles(BuildContext context) {
    if (userTitles?.isEmpty ?? true) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 5.pt, left: 16.pt, right: 16.pt),
      child: Wrap(
        spacing: 3.pt,
        runSpacing: 3.pt,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: _userTitlesItem(context),
      ),
    );
  }

  List<Widget> _userTitlesItem(BuildContext context) {
    List<Widget> list = [];
    for (var element in userTitles!) {
      list.add(TitleItemWidget(
        titleModel: element,
        height: 24.pt,
      ));
    }
    list.add(_userTitlesMore(context));
    return list;
  }

  Widget _userTitlesMore(BuildContext context) {
    return GestureDetector(
      onTap: onTapTitleMore,
      child: Container(
        height: 18.pt,
        width: 18.pt,
        decoration: BoxDecoration(color: Colors.white.withOpacity(0.16), borderRadius: BorderRadius.circular(9.pt)),
        alignment: Alignment.center,
        child: FLImage.asset(Res.roomMoreWhite, width: 14.pt, height: 14.pt),
      ),
    );
  }
}
