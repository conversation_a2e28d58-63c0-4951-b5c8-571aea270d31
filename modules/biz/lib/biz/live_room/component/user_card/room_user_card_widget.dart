import 'package:biz/biz.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_animation_widget.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_banner_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_controller.dart';
import 'package:biz/biz/live_room/model/user_card_setting_item_model.dart';
import 'package:biz/biz/user/badge/widget/badge_display_widget.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/follow_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;

import '../gift_gallery/gift_gallery_widget.dart';
import 'widget/room_user_card_item_info_widget.dart';

double userCardAvatarSize = 78.pt;

enum MiniCardType { roomUser, familyChat }

Future<void> showUserCardDialog({required String targetId, MiniCardType type = MiniCardType.roomUser}) async {
  await dialog.showBottomSheet(
      (_) => RoomUserCardWidget(
            targetId: targetId,
            type: type,
          ),
      barrierDismissible: true,
      enableDrag: false,
      routeSettings: RouteSettings(name: R_USER_MINI_CARD));
}

class RoomUserCardWidget extends StatefulWidget {
  final String targetId;
  final MiniCardType type;

  RoomUserCardWidget({required this.targetId, required this.type});

  @override
  State<RoomUserCardWidget> createState() => _RoomUserCardWidgetState();
}

class _RoomUserCardWidgetState extends State<RoomUserCardWidget> {
  final RoomUserCardController _controller = RoomUserCardController();

  @override
  void initState() {
    _controller.targetId = widget.targetId;
    _controller.type = widget.type;
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    _controller.calItemSize(width: screenWidth);
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
          init: _controller,
          global: false,
          autoRemove: false,
          builder: (controller) {
            return WillPopScope(
              onWillPop: () async {
                _controller.showIntimacyAnimationWidget(isShow: false);
                Navigator.of(context).pop();
                return true;
              },
              child: Stack(
                children: [
                  GestureDetector(
                      onTap: () {
                        _controller.showIntimacyAnimationWidget(isShow: false);
                        Navigator.of(context).pop();
                      },
                      child: Container(width: 1.w, height: 1.h, color: Colors.transparent)),
                  _containerView(),
                  _intimacyAnimationWidget()
                ],
              ),
            );
          }),
    );
  }

  Widget _containerView() {
    return Positioned(
      bottom: 0,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          _settingContainerView(),
          _userProfileCard(),
          _avatar(),
        ],
      ),
    );
  }

  Widget _avatar() {
    return Positioned(
      top: 0,
      child: UserAvatar(
        url: _controller.targetUserInfo?.avatar ?? '',
        size: userCardAvatarSize,
        hasFrame: true,
        inRoom: true,
        userId: _controller.targetUserInfo?.uid,
        onTap: ({String? rid}) {
          _controller.goToProfile();
        },
      ),
    );
  }

  Widget _userProfileCard() {
    double bottom = _controller.bottomSpace;
    if (_controller.settings.isNotEmpty) {
      int row = (_controller.settings.length / 4).ceil();
      //用户卡片与屏幕底部的间距
      double bottomMargin = row * _controller.itemHeight + _controller.bottomSpace;
      bottom = bottomMargin;
    }

    return Container(
      margin: EdgeInsets.only(
          top: userCardAvatarSize * kUserAvatarFrameRadio / 2, left: 11.pt, right: 11.pt, bottom: bottom),
      width: _controller.screenWidth - 22.pt,
      decoration: BoxDecoration(color: R.color.textColor2, borderRadius: BorderRadius.circular(18.pt)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _topView(),
          18.hSpace,
          RoomUserCardItemInfoWidget(
            uid: widget.targetId,
            nickname: _controller.targetUserInfo?.nickname,
            isAdmin: _controller.roomUserInfo?.isAdmin == true,
            isOwner: _controller.roomUserInfo?.isOwner == true,
            fansLevel: _controller.roomUserInfo?.fansClubShow?.level,
            fansGroupName: _controller.roomUserInfo?.fansClubShow?.name,
            age: _controller.targetUserInfo?.age,
            sex: _controller.targetUserInfo?.sex,
            showAddress: _controller.targetUserInfo?.showAddress,
            charmLabelIcon: _controller.targetUserInfo?.charmInfo?.icon ?? "",
            charmLevel: _controller.targetUserInfo?.charmInfo?.level ?? 0,
            wealthLabelIcon: _controller.targetUserInfo?.wealthInfo?.icon ?? "",
            wealthLevel: _controller.targetUserInfo?.wealthInfo?.level ?? 0,
            userTitles: _controller.userTitles(),
            onTapTitleMore: _controller.goToProfile,
          ),
          SizedBox(height: 5.pt),
          _badges(),
          _intimacy(),
          _giftGallery(),
          _bottomBar(),
        ],
      ),
    );
  }

  Widget _badges() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        routerUtil.push(R_USER_BADGE,
            params: {P_UID: _controller.targetId, P_STATISTIC_FROM: StatisticPageFrom.liveRoomMiniProfile});
      },
      child: BadgeDisplayWidget(
          badges: _controller.targetUserInfo?.badges, size: Size(40.pt, 40.pt), horizontalPadding: 7.pt),
    );
  }

  bool isShowUserCardBottomBar() {
    bool isSelf = _controller.targetId == accountService.getAccountInfo()?.uid;
    return !isSelf || (_controller.roomUserSeatItem?.seatHadUser == true);
  }

  Widget _settingContainerView() {
    if (_controller.settings.isEmpty) return SizedBox.shrink();

    int row = (_controller.settings.length / 4).ceil();
    double height = row * _controller.itemHeight + _controller.settingsTopMargin + _controller.bottomSpace;

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(top: _controller.settingsTopMargin),
        height: height,
        width: double.infinity,
        decoration: BoxDecoration(
            color: _controller.settings.isNotEmpty ? R.color.backgroundColor1 : Colors.transparent,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(16.pt),
            )),
        child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 0,
                crossAxisSpacing: 0.pt,
                childAspectRatio: _controller.itemWidth / _controller.itemHeight),
            itemCount: _controller.settings.length,
            itemBuilder: (BuildContext context, int index) {
              return _settingItem(index);
            }),
      ),
    );
  }

  Widget _topView() {
    return Container(
      height: 41.pt,
      padding: EdgeInsets.only(top: 13.pt, left: 3.pt, right: 13.pt),
      child: Visibility(
        visible: accountService.getAccountInfo()?.uid != _controller.targetId,
        child: Row(
          children: [_atButton(), Spacer(), _reportButton()],
        ),
      ),
    );
  }

  Widget _atButton() {
    return GestureDetector(
      onTap: _controller.onAtUser,
      child: Container(
        height: 25.pt,
        padding: EdgeInsets.symmetric(horizontal: 11.pt),
        child: Text(
          '@',
          style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeightExt.heavy,
              color: R.color.primaryLightColor,
              fontStyle: FontStyle.italic),
        ),
      ),
    );
  }

  Widget _reportButton() {
    return GestureDetector(
      onTap: _controller.onReportUser,
      child: Container(
        height: 25.pt,
        child: FLImage.asset(Res.roomProfileCardReport, width: 25.pt, height: 25.pt),
      ),
    );
  }

  Widget _settingItem(int index) {
    UserCardSettingItemModel itemModel = _controller.settings[index];
    return GestureDetector(
      onTap: () {
        _controller.onItemClick(itemModel);
      },
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 13.pt),
            FLImage.asset(itemModel.icon ?? '', width: 35.pt, height: 35.pt),
            SizedBox(height: 6.pt),
            Expanded(
              child: Text(
                itemModel.name ?? '',
                softWrap: true,
                textAlign: TextAlign.center,
                style: TextStyle(color: R.color.textColor4, fontSize: 11.sp, fontWeight: FontWeightExt.medium),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _bottomBar() {
    bool isSelf = _controller.targetId == accountService.getAccountInfo()?.uid;
    bool isShowFollowBtn = _controller.targetUserInfo?.isFollowing == false;

    List<Widget> childs = [];

    if (isSelf) {
      if (_controller.roomUserSeatItem?.seatHadUser == true) {
        childs.addAll([
          Expanded(flex: 1, child: _downMicButton()),
        ]);
      } else {
        return SizedBox(height: 15.pt);
      }
    } else {
      if (isShowFollowBtn) {
        childs.addAll([
          Expanded(flex: 1, child: _chatButton()),
          GlobalWidgets.spacingHorizontal(13.pt),
          _followingBtn(),
        ]);
      } else {
        childs.addAll([
          Expanded(flex: 1, child: _chatButton()),
        ]);
      }

      childs.add(_giftButton());
    }
    return Container(
      padding: EdgeInsets.only(top: 15.pt, left: 16.pt, right: 16.pt, bottom: 15.pt),
      child: Row(
        children: childs,
      ),
    );
  }

  Widget _chatButton() {
    return GestureDetector(
      onTap: _controller.goToChat,
      child: Container(
        height: 46.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(23.pt), color: R.color.primaryColor),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FLImage.asset(Res.profileIconChat, width: 24.pt, height: 24.pt),
            SizedBox(width: 5.pt),
            Text(
              LocaleStrings.instance.chat,
              style: TextStyle(fontWeight: FontWeightExt.heavy, color: Colors.white, fontSize: 15.sp),
            )
          ],
        ),
      ),
    );
  }

  Widget _followingBtn() {
    return Expanded(
      flex: 1,
      child: FollowWidget(
        height: 46.pt,
        targetUid: _controller.targetUserInfo?.uid ?? '',
        toastFollowSuccess: LocaleStrings.instance.followSuccess,
        initHasFollowed: _controller.targetUserInfo?.isFollowed,
        followed: _bottomBtn(
          LocaleStrings.instance.unFollow2,
          Res.commonFollow,
          backGroundColor: primaryColorBlue,
          iconSize: 24.pt,
        ),
        unfollow: _bottomBtn(LocaleStrings.instance.follow, Res.commonFollowBlue,
            backGroundColor: Colors.white, iconSize: 24.pt, childColor: R.color.primaryColor),
        showUnfollowConfirm: true,
        onFollowClickChanged: _handleFollowStastics,
        onFollowStatusChanged: _controller.updateFollowingStatus,
        relationPage: StatisticPageFrom.liveRoomMiniProfile,
      ),
    );
  }

  Widget _bottomBtn(String label, String icon,
      {Function()? onTab, Color? backGroundColor, Gradient? gradient, Color? childColor, double? iconSize}) {
    return GeneralBtn.roundRadius(
      width: double.infinity,
      height: 46.pt,
      onTap: onTab,
      title: label,
      fontSize: 15.sp,
      iconAsset: icon,
      iconSize: iconSize ?? 29.pt,
      childColor: childColor,
      gradient: gradient,
      backgroundColor: backGroundColor,
      fontWeight: FontWeightExt.heavy,
    );
  }

  Widget _downMicButton() {
    return GestureDetector(
      child: Container(
        height: 46.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(23.pt)),
        child: Text(
          LocaleStrings.instance.downMic,
          style: TextStyle(color: R.color.primaryColor, fontSize: 18.sp, fontWeight: FontWeightExt.heavy),
        ),
      ),
      onTap: _controller.downMic,
    );
  }

  Widget _giftButton() {
    return GestureDetector(
      onTap: _controller.onClickGiftButton,
      child: Container(
          width: 46.pt,
          height: 46.pt,
          margin: EdgeInsets.only(left: 13.pt),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.12),
            borderRadius: BorderRadius.circular(23.pt),
          ),
          alignment: Alignment.center,
          child: FLImage.asset(Res.roomGift, width: 36.pt, height: 36.pt)),
    );
  }

  Widget _intimacy() {
    if (_controller.targetUserInfo != null) {
      return Container(
          height: 65.pt + 8.pt,
          width: double.infinity,
          margin: EdgeInsets.only(top: 5.pt, left: 16.pt, right: 16.pt),
          child: IntimacyBannerWidget(
            targetUserInfo: _controller.targetUserInfo!,
            intimacyInfo: _controller.intimacyInfo(),
            isDark: true,
            from: StatisticPageFrom.liveRoomMiniProfile,
          ));
    }
    return SizedBox.shrink();
  }

  Widget _intimacyAnimationWidget() {
    return Obx(() => _controller.showIntimacyAnimation.value
        ? IntimacyAnimationWidget(
            insets: EdgeInsets.only(top: 200.pt),
            intimacyLevel: _controller.intimacyInfo()?.level ?? 0,
            targetUser: _controller.targetUserInfo,
            intimacyUser: _controller.intimacyInfo()?.userInfo,
            onComplete: () {
              _controller.showIntimacyAnimationWidget(isShow: false);
            },
          )
        : SizedBox.shrink());
  }

  Widget _giftGallery() {
    if (_controller.giftWall() != null) {
      return GestureDetector(
        onTap: _controller.onClickGiftGallery,
        child: GiftGalleryWidget(
          giftWall: _controller.giftWall()!,
        ),
      );
    }
    return SizedBox.shrink();
  }

  /// 关注事件上报
  void _handleFollowStastics(bool isFollow) {}
}
