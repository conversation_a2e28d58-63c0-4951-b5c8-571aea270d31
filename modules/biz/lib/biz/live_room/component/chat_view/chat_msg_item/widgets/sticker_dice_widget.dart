import 'package:biz/biz.dart';
import 'package:flutter/cupertino.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/sticker_dice_model.dart';

class StickerDiceWidget extends StatefulWidget {

  final StickerDiceModel dice;
  Size size;
  VoidCallback? playCompletion;
  StickerDiceWidget({
    required this.dice,
    this.size = Size.zero,
    this.playCompletion
  });

  @override
  State<StatefulWidget> createState() => _StickerDiceWidgetState();
}

class _StickerDiceWidgetState extends State<StickerDiceWidget> {
  bool _showDiceSvga = true;
  final SvgaController _svgaController = SvgaController();
  late SvgaInfo _svgaInfo;

  @override
  void initState() {
    _showDiceSvga =
        DateTime.now().millisecondsSinceEpoch - widget.dice.startTime <= 1000;
    _svgaController.end = (info) {
      _showDiceSvga = false;
      widget.playCompletion?.call();
      if (mounted) {
        setState(() {});
      }
    };
    _svgaInfo = SvgaInfo(assetUrl: Res.roomSvgaStickerDice, times: 2);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StickerDiceWidget oldWidget) {
    _showDiceSvga =
        DateTime.now().millisecondsSinceEpoch - widget.dice.startTime <= 1000;
    _svgaController.stopSvga();
    _svgaController.startSvga(_svgaInfo);
    _svgaController.end = _svgaController.end;
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.playCompletion?.call();
    _svgaController.stopSvga();
    _svgaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_showDiceSvga) {
      return Container(
        width: widget.size.width > 0 ? widget.size.width : 62.pt,
        height: widget.size.height > 0 ? widget.size.height : 62.pt,
        child: SvgaWidget(
          controller: _svgaController,
          svgaInfo: _svgaInfo,
        ),
      );
    } else {
      return FLImage.asset("res/room/sticker/sticker_dice_${widget.dice.num}.png",
          width: widget.size.width > 0 ? widget.size.width : 62.pt, height: widget.size.height > 0 ? widget.size.height : 62.pt);
    }
  }
}
