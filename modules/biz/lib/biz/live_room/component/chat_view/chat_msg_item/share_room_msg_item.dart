import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/chat_view/room_msg_factory.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/live/chat/model/room_share_msg_content.dart';

import 'widgets/chat_at_username.dart';

/// 默认分享了房间的消息
class ShareRoomMsgItem extends StatelessWidget {
  final RoomShareMsgContent msgContent;

  const ShareRoomMsgItem({super.key, required this.msgContent});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: roomTheme.theme.msgDecorate,
      constraints: BoxConstraints(
        maxWidth: RoomMsgFactory.contentMaxWidth(context),
      ),
      padding: EdgeInsetsDirectional.only(
        start: 11.pt,
        end: 11.pt,
        top: 11.pt,
        bottom: 11.pt,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          UserAvatar(
            url: msgContent.chatUserInfo?.avatar,
            size: 20.pt,
          ),
          SizedBox(width: 5.pt),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _fans(),
                  Flexible(
                    child: ChatAtUserName(
                      name: msgContent.chatUserInfo?.nickname ?? '',
                      uid: msgContent.chatUserInfo?.uid,
                    ),
                  ),
                  SizedBox(width: 3.pt),
                  Text(
                    LocaleStrings.instance.shareTheRoom,
                    style: roomTheme.theme.msgGrayTextStyle,
                  ),
                ],
              ),
              CharmWealthLabelsWidget(
                charmLabelIcon: msgContent.chatUserInfo?.charmInfo?.icon ?? "",
                charmLevel: msgContent.chatUserInfo?.charmInfo?.level ?? 0,
                wealthLabelIcon: msgContent.chatUserInfo?.wealthInfo?.icon ?? "",
                wealthLevel: msgContent.chatUserInfo?.wealthInfo?.level ?? 0,
              )
            ],
          )),
        ],
      ),
    );
  }

  Widget _fans() {
    return UserFanLevelMetaWidget(
      level: msgContent.chatUserInfo?.fansClubShow?.level,
      fansGroupName: msgContent.chatUserInfo?.fansClubShow?.name,
      padding: EdgeInsetsDirectional.only(end: 3.pt),
    );
  }
}
