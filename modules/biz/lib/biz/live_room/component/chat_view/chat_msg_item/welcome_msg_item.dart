import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import 'widgets/chat_at_username.dart';

class WelcomeMsgItem extends StatefulWidget {
  final ChatWelcomeMsgContent msgContent;
  final Function(ChatWelcomeMsgContent? msgContent)? onClickInvite;
  final Function(ChatWelcomeMsgContent? msgContent)? onClickWelcome;

  WelcomeMsgItem({required this.msgContent, this.onClickWelcome, this.onClickInvite});

  @override
  State<StatefulWidget> createState() => _WelcomeMsgItemState();
}

class _WelcomeMsgItemState extends State<WelcomeMsgItem> {
  bool _hasClickInvite = false;
  bool _hasClickWelcome = false;

  @override
  void initState() {
    _hasClickInvite = widget.msgContent.clickInvite;
    _hasClickWelcome = widget.msgContent.clickWelcome;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant WelcomeMsgItem oldWidget) {
    if (oldWidget.msgContent.messageId != widget.msgContent.messageId) {
      _hasClickInvite = widget.msgContent.clickInvite;
      _hasClickWelcome = widget.msgContent.clickWelcome;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // constraints: BoxConstraints(maxWidth: RoomMsgFactory.contentMaxWidth(context)),
      decoration: BoxDecoration(
        color: Color(0xFF000000).withOpacity(0.85),
        borderRadius: BorderRadius.all(
          Radius.circular(9.pt),
        ),
      ),
      padding: EdgeInsetsDirectional.only(start: 11.pt, end: 11.pt),
      margin: EdgeInsets.symmetric(horizontal: 5.pt),
      child: Row(
        children: [
          Expanded(child: _user()),
          SizedBox(width: 20.pt),
          _tail(),
        ],
      ),
    );
  }

  Widget _user() {
    return GestureDetector(
      onTap: () => showUserCardDialog(
        targetId: widget.msgContent.otherUser?.uid ?? "",
      ),
      behavior: HitTestBehavior.opaque,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 7.pt),
          Row(
            children: [
              // UserAvatar(
              //     url: widget.msgContent.otherUser?.headimgurl, size: 20.pt),
              SizedBox(width: 5.pt),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        _fans(),
                        Flexible(
                          child: ChatAtUserName(
                            name: widget.msgContent.otherUser?.nickname.fixAutoLines() ?? "",
                            uid: widget.msgContent.otherUser?.uid ?? "",
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        // CharmWealthLabelsWidget(
                        //   charmLabelIcon: widget.msgContent.chatUserInfo?.charmInfo?.icon ?? "",
                        //   charmLevel:  widget.msgContent.chatUserInfo?.charmInfo?.level ?? 0,
                        //   wealthLabelIcon:  widget.msgContent.chatUserInfo?.wealthInfo?.icon ?? "",
                        //   wealthLevel:  widget.msgContent.chatUserInfo?.wealthInfo?.level ?? 0,
                        // ),
                        // SizedBox(width: 5.pt),
                        Visibility(
                          visible: widget.msgContent.isNewbie,
                          child: Padding(
                            padding: EdgeInsetsDirectional.only(end: 8.pt),
                            child: FLImage.asset(Res.roomIconWelcomeNew, height: 16.pt, fit: BoxFit.cover),
                          ),
                        ),
                        _textWidget(),
                      ],
                    )
                  ],
                ),
              ),

              // SizedBox(height: 1.pt),
            ],
          ),
          SizedBox(height: 6.pt),
        ],
      ),
    );
  }

  Widget _textWidget() {
    return Text(
      LocaleStrings.instance.enteredTheRoom,
      style: TextStyle(
        color: Colors.white,
        fontSize: 12.sp,
        fontWeight: FontWeightExt.medium,
        height: 1.4,
      ),
    );
  }

  Widget _tail() {
    bool isManager = roomService.getCurrentRoom()?.isOwner == true;
    return Row(
      children: [
        if (isManager) _inviteMicWidget(),
        SizedBox(width: 11.pt),
        _welcomeWidget(),
      ],
    );
  }

  Widget _inviteMicWidget() {
    return _btnWidget(childAsset: Res.roomMsgInviteMic, hasClick: _hasClickInvite, onTap: _inviteMic);
  }

  Widget _welcomeWidget() {
    return _btnWidget(childAsset: Res.roomMsgWelcomeHand, hasClick: _hasClickWelcome, onTap: _welcomeTap);
  }

  Widget _btnWidget({required String childAsset, required bool hasClick, Function? onTap}) {
    return GestureDetector(
      onTap: () => {
        if (!hasClick) {onTap?.call()}
      },
      child: Opacity(
        opacity: hasClick ? 0.5 : 1.0,
        child: Container(
          width: 44.pt,
          height: 28.pt,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(7.pt)),
              border: Border.all(color: Color(0xFF9F59B9), width: 1.pt)),
          child: FLImage.asset(childAsset, width: 20.pt, fit: BoxFit.cover),
        ),
      ),
    );
  }

  /// 欢迎
  void _welcomeTap() async {
    if (widget.msgContent.otherUser == null) return;
    _hasClickWelcome = true;
    var user = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomWelcomeClick(
        act: 'welcome',
        status: user?.identity?.type?.toString(),
        roomId: roomService.getCurrentRoom()?.roomId,
        type: roomService.getCurrentRoom()?.mode);

    UserInfo? info = UserInfo.fromPb(widget.msgContent.otherUser!);
    roomChatService.sendEnterRoomMsg(isWelcome: true, atUser: info);
    if (mounted) {
      setState(() {});
    }

    rxUtil.send(RoomUserOptEvent.welCome, widget.msgContent.messageId);
  }

  /// 邀麦
  void _inviteMic() async {
    if (widget.msgContent.otherUser == null) return;
    _hasClickInvite = true;
    var response = await micSeatService.inviteMic(uid: widget.msgContent.otherUser?.uid ?? "");
    RoomStatistics.reportRoomInvitedMic(
        roomType: roomService.getCurrentRoom()?.roomType,
        toUid: widget.msgContent.otherUser?.uid,
        toGender: widget.msgContent.otherUser?.sex.value == 1 ? sexMale : sexFemale,
        type: roomService.getCurrentRoom()?.mode,
        roomId: roomService.getCurrentRoom()?.roomId,
        act: '1',
        from: LiveRoomHandler.from);
    var user = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomWelcomeClick(
        act: 'invite',
        status: user?.identity?.type?.toString(),
        roomId: roomService.getCurrentRoom()?.roomId,
        type: roomService.getCurrentRoom()?.mode);
    if (response.isSuccess) {
      rxUtil.send(RoomUserOptEvent.inviteMic, widget.msgContent);
    } else {
      toast(response.msg ?? LocaleStrings.instance.pleaseTryAgain, isDark: true);
    }
    if (mounted) {
      setState(() {});
    }
  }

  Widget _fans() {
    return UserFanLevelMetaWidget(
      level: widget.msgContent.chatUserInfo?.fansClubShow?.level,
      fansGroupName: widget.msgContent.chatUserInfo?.fansClubShow?.name,
      padding: EdgeInsetsDirectional.only(end: 3.pt),
    );
  }

/*Widget _medalWidget() {
    return FutureBuilder<UserInfo?>(
      future: widget.msgContent.chatUserInfo?.baseInfo,
      builder: (BuildContext context, AsyncSnapshot<UserInfo?> snapshot) {
        List<UserMedal> medals = snapshot.data?.medals ?? [];
        List<Widget> childList() {
          return List.generate(
              medals.length,
              (index) => Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CachedNetworkImage(
                        imageUrl: medals[index].icon ?? "",
                        errorWidget: (context, url, value) =>
                            GlobalWidgets.imageError(size: 20.pt),
                        placeholder: (context, url) =>
                            GlobalWidgets.imagePlaceholder(),
                        height: 20.pt,
                        width: 20.pt,
                      ),
                    ],
                  ));
        }

        return Visibility(
            visible: medals.isNotEmpty,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(mainAxisSize: MainAxisSize.min, children: childList()),
              ],
            ));
      },
    );
  }*/
}
