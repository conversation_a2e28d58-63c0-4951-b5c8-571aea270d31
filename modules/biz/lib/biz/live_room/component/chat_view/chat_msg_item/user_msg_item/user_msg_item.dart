import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/chat_view/chat_msg_item/user_msg_gift_item.dart';
import 'package:biz/biz/live_room/component/dialog/action_menu/msg_item_action_menu.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/biz/user/title/widget/title_item_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/global/widgets/user_color_name_widget.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:flutter/services.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/modules/live/chat/model/abs/abs_live_msg_content.dart';
import 'package:service/modules/live/chat/model/abs/abs_live_msg_content_with_user.dart';
import 'package:service/modules/live/chat/model/chat_reward_st_end_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_stay_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_sticker_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_text_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/chat/model/gift_msg_content.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/utils/color_util.dart';

import '../../../../../chat/chat_details/widgets/msg_bubble.dart';
import '../../../../../user/badge/widget/badge_display_widget.dart';
import '../reward_st_end_item.dart';
import '../stay_remind_owner_item.dart';
import '../user_msg_sticker_item.dart';
import '../user_msg_text_item.dart';
import '../user_msg_welcome_item.dart';
import 'user_msg_controller.dart';

/// 包裹用户头像/气泡框 widget
class UserMsgItem extends StatefulWidget {
  final AbsLiveMsgContentWithUser msgContent;
  final RoomMainController mainController;

  /// 是否显示聊天对话背景
  final bool showBg;

  final bool showChatFrame;

  final bool isKeyboardVisible;

  final double? maxContentWidth;

  UserMsgItem(
      {Key? key,
      required this.msgContent,
      required this.mainController,
      this.showBg = true,
      this.showChatFrame = true,
      this.isKeyboardVisible = false,
      this.maxContentWidth});

  @override
  State<StatefulWidget> createState() => _UserMsgItemState();
}

class _UserMsgItemState extends State<UserMsgItem> {
  late UserMsgController _controller;

  String get _getTag => '$hashCode';

  @override
  void initState() {
    super.initState();
    _controller = Get.put<UserMsgController>(UserMsgController(), tag: _getTag);
    _controller.initData(showChatFrame: widget.showChatFrame, msgContent: widget.msgContent);
  }

  @override
  void dispose() {
    Get.delete<UserMsgController>(tag: _getTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder<UserMsgController>(
        init: _controller,
        global: false,
        autoRemove: false,
        tag: _getTag,
        assignId: false,
        builder: (_) {
          final userInfo = _controller.userInfo;
          return _body(userInfo);
        },
      ),
    );
  }

  Widget _body(UserInfo? userInfo) {
    if (userInfo == null) {
      return SizedBox();
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _userAvatarWidget(userInfo),
            SizedBox(width: 2.pt),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: widget.maxContentWidth ?? roomTheme.theme.chatListNormalWidth,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _userInfoWidget(userInfo),
                  _buildLabels(userInfo),
                ],
              ),
            )
          ],
        ),
        _contentWidget(),
      ],
    );
  }

  Widget _itemDetail(AbsLiveMsgContent content) {
    var color = _controller.chatFrameInfo?.backgroundColor?.isNotEmpty == true && !(_controller.showChatFrameError)
        ? colorFromHex(_controller.chatFrameInfo!.backgroundColor!)
        : null;
    switch (content.runtimeType) {
      case ChatTextMsgContent:
        return UserMsgTextItem(msgContent: content as ChatTextMsgContent, color: color, isMe: _controller.isMe);
      case ChatStickerMsgContent:
        return UserMsgStickerItem(msgContent: content as ChatStickerMsgContent);
      case ChatWelcomeMsgContent:
        return UserMsgWelcomeItem(msgContent: content as ChatWelcomeMsgContent, color: color);
      case ChatStayMsgContent:
        return StayRemindOwnerItem(
          msgContent: content as ChatStayMsgContent,
          mainController: widget.mainController,
        );
      case GiftMsgContent:
        return UserMsgGiftItem(giftContent: content as GiftMsgContent);
      case ChatRewardStEndMsgContent:
        return RewardStEndItem(content as ChatRewardStEndMsgContent);
      default:
        return SizedBox.shrink();
    }
  }

  /// 消息内容
  Widget _contentWidget() {
    var showFrame =
        (_controller.chatFrameInfo?.backgroundImg?.isNotEmpty ?? false) && !(_controller.showChatFrameError ?? false);
    var showBg = widget.showBg && !showFrame;
    var widgetChild = Container(
      decoration: showBg
          ? BoxDecoration(color: Color(0x66000000), borderRadius: BorderRadius.all(Radius.circular(8.pt)))
          : null,
      padding: widget.showBg || showFrame ? (showFrame ? EdgeInsets.zero : EdgeInsets.all(10.pt)) : null,
      child: _itemDetail(widget.msgContent),
    );

    final child = LayoutBuilder(builder: (context, constraints) {
      return GestureDetector(
        onLongPress: () {
          if (widget.isKeyboardVisible) return;
          if (widget.msgContent is ChatTextMsgContent || widget.msgContent is ChatWelcomeMsgContent) {
            final renderBox = context.findRenderObject() as RenderBox;
            final offset = renderBox.localToGlobal(Offset.zero);
            final size = renderBox.size;
            showDialog(
                (_) => MsgItemActionMenu(
                      actions: [
                        LocaleStrings.instance.copy,
                        if (widget.msgContent.chatUserInfo?.uid != roomService.getCurrentUid())
                          LocaleStrings.instance.report
                      ],
                      offset: offset,
                      size: size,
                      onTab: (index) {
                        switch (index) {
                          case MsgActionMenu.copy:
                            Clipboard.setData(ClipboardData(text: _getContent()));
                            break;
                          case MsgActionMenu.report:
                            String targetUid = widget.msgContent.chatUserInfo?.uid ?? "";
                            String roomId = roomService.getCurrentRoomId() ?? "";
                            routerUtil.push(R_SETTINGS_REPORT, params: {
                              P_DATA: RoomUserReport(targetUid: targetUid, roomId: roomId),
                              P_STATISTIC_FROM: R_LIVE_LIVE_ROME
                            });
                            break;
                        }
                      },
                    ),
                barrierColor: Colors.transparent);
          }
        },
        behavior: HitTestBehavior.opaque,
        child: _chatFrame(child: widgetChild),
      );
    });
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
      ],
    );
  }

  /// 携带聊天气泡框
  Widget _chatFrame({required Widget child}) {
    var chatFrameInfo = _controller.chatFrameInfo;

    var showFrame =
        (_controller.chatFrameInfo?.backgroundImg?.isNotEmpty ?? false) && !(_controller.showChatFrameError);
    return Container(
      margin: EdgeInsetsDirectional.only(
        start: roomTheme.theme.chatBubbleStartMargin,
        end: roomTheme.theme.roomAdBannerWidth + 2 * roomTheme.theme.roomAdBannerMargin,
      ),
      child: Stack(
        children: [
          Visibility(visible: !showFrame, child: child),
          Visibility(
              visible: showFrame,
              child: MsgBubble(
                fromSelf: true,
                child: child,
                hasFrame: true,
                chatFrameInfo: chatFrameInfo,
                // maxWidth: 262.pt,
                // onLoadFrameError: () => _bloc?.add(ChatFrameErrorEvent(true)),
              )),
        ],
      ),
    );
  }

  Widget _itemImg(String url) {
    return CachedNetworkImage(
      matchTextDirection: true,
      imageUrl: url,
      width: 20.pt,
      height: 36.pt,
    );
  }

  /// 用户信息
  Widget _userInfoWidget(UserInfo userInfo) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _fans(),
        _name(userInfo),
        _identity(),
      ],
    );
  }

  /// 头像
  Widget _userAvatarWidget(UserInfo userInfo) {
    return GestureDetector(
      onLongPress: _atUser,
      onTap: _gotoUserCard,
      child: UserAvatar(
        userId: userInfo.uid,
        url: userInfo.avatar,
        avatarCode: userInfo.avatarCode,
        size: roomTheme.theme.avatarSize,
        hasFrame: true,
        inRoom: true,
      ),
    );
  }

  /// 昵称
  Widget _name(UserInfo userInfo) {
    return GestureDetector(
      onTap: _gotoUserCard,
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 95.pt),
        child: UserColorNameWidget(
          style: TextStyle(
            fontSize: 13.sp,
            color: roomTheme.theme.chatUserName,
            fontWeight: FontWeightExt.heavy,
            height: 1.1,
          ),
          name: userInfo.displayedName,
          // name: userInfo.displayedName,
          maxLines: 1,
          uid: userInfo.uid,
        ),
      ),
    );
  }

  Widget _fans() {
    return UserFanLevelMetaWidget(
      level: widget.msgContent.chatUserInfo?.fansClubShow?.level,
      fansGroupName: widget.msgContent.chatUserInfo?.fansClubShow?.name,
      padding: EdgeInsetsDirectional.only(end: 3.pt),
    );
  }

  /// 身份
  Widget _identity() {
    return Visibility(
      visible: widget.msgContent.chatUserInfo?.identity?.icon?.isNotEmpty == true,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: 6.pt),
          CachedNetworkImage(
              imageUrl: widget.msgContent.chatUserInfo?.identity?.icon ?? "",
              errorWidget: (context, url, value) => GlobalWidgets.imageError(size: 14.pt),
              placeholder: (context, url) => GlobalWidgets.imagePlaceholder(),
              height: 14.pt,
              fit: BoxFit.fitHeight)
        ],
      ),
    );
  }

  void _atUser() async {
    if (widget.msgContent.chatUserInfo?.uid == roomService.getCurrentUid()) {
      return;
    }
    final userInfo = await widget.msgContent.chatUserInfo?.baseInfo;
    rxUtil.send(RoomUserOptEvent.atUser, userInfo);
  }

  String _getContent() {
    String? content;
    if (widget.msgContent is ChatTextMsgContent) {
      final msg = widget.msgContent as ChatTextMsgContent;
      content = msg.atUid?.isNotEmpty == true ? '@${msg.atNickname} ${msg.content}' : msg.content;
    } else if (widget.msgContent is ChatWelcomeMsgContent) {
      final msg = widget.msgContent as ChatWelcomeMsgContent;
      content = '@${msg.otherUser?.nickname} ${LocaleStrings.instance.welcomeJoinRoom}';
    }
    return content ?? '';
  }

  void _gotoUserCard() {
    showUserCardDialog(
      targetId: widget.msgContent.chatUserInfo?.uid ?? "",
    );
  }

  Widget _buildLabels(UserInfo userInfo) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        TitleItemWidget(
          height: 18.pt,
          uid: userInfo.uid,
          margin: EdgeInsetsDirectional.only(end: 5.pt),
        ),
        CharmWealthLabelsWidget(
          charmLabelIcon: userInfo.charmInfo?.icon ?? "",
          charmLevel: userInfo.charmInfo?.level ?? 0,
          wealthLabelIcon: userInfo.wealthInfo?.icon ?? "",
          wealthLevel: userInfo.wealthInfo?.level ?? 0,
        ),
        BadgeDisplayWidget(badges: userInfo.badges, size: Size(22.pt, 22.pt), horizontalPadding: 3.pt, maxDisplayCount: 3)
      ],
    );
  }
}
