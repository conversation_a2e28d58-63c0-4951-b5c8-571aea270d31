import 'package:biz/biz/live_room/component/chat_view/room_msg_factory.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import 'widgets/chat_at_username.dart';

class EnterMsgItem extends StatefulWidget {
  final ChatWelcomeMsgContent msgContent;
  final Function(ChatWelcomeMsgContent? msgContent)? onClickInvite;
  final Function(ChatWelcomeMsgContent? msgContent)? onClickWelcome;

  EnterMsgItem(
      {required this.msgContent, this.onClickWelcome, this.onClickInvite});

  @override
  State<StatefulWidget> createState() => _EnterMsgState();
}

class _EnterMsgState extends State<EnterMsgItem> {
  bool _hasClickInvite = false;
  bool _hasClickWelcome = false;

  @override
  void initState() {
    _hasClickInvite = widget.msgContent.clickInvite;
    _hasClickWelcome = widget.msgContent.clickWelcome;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant EnterMsgItem oldWidget) {
    if (oldWidget.msgContent.messageId != widget.msgContent.messageId) {
      _hasClickInvite = widget.msgContent.clickInvite;
      _hasClickWelcome = widget.msgContent.clickWelcome;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxWidth: RoomMsgFactory.contentMaxWidth(context)),
      decoration: roomTheme.theme.msgDecorate,
      padding: EdgeInsetsDirectional.only(start: 11.pt, end: 11.pt),
      child: Row(
        children: [
          Expanded(child: _user()),
          SizedBox(width: 20.pt),
          _tail(),
        ],
      ),
    );
  }

  Widget _user() {
    return GestureDetector(
      onTap: () => showUserCardDialog(
        targetId: widget.msgContent.otherUser?.uid ?? "",
      ),
      behavior: HitTestBehavior.opaque,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 11.pt),
          Row(
            children: [
              UserAvatar(
                  url: widget.msgContent.otherUser?.headimgurl, size: 20.pt),
              SizedBox(width: 5.pt),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        _fans(),
                        Flexible(
                          child: ChatAtUserName(
                            name: widget.msgContent.otherUser?.nickname.fixAutoLines() ?? "",
                            uid: widget.msgContent.otherUser?.uid ?? "",
                          ),
                        ),
                      ],
                    ),
                    CharmWealthLabelsWidget(
                      charmLabelIcon: widget.msgContent.chatUserInfo?.charmInfo?.icon ?? "",
                      charmLevel:  widget.msgContent.chatUserInfo?.charmInfo?.level ?? 0,
                      wealthLabelIcon:  widget.msgContent.chatUserInfo?.wealthInfo?.icon ?? "",
                      wealthLevel:  widget.msgContent.chatUserInfo?.wealthInfo?.level ?? 0,
                    ),
                  ],
                ),
              ),

              // SizedBox(height: 1.pt),
            ],
          ),
          SizedBox(height: 5.pt),
          _textWidget(),
          SizedBox(height: 11.pt),
        ],
      ),
    );
  }

  Widget _textWidget() {
    return Text(
      LocaleStrings.instance.enteredTheRoom,
      style: roomTheme.theme.msgGrayTextStyle,
    );
  }

  Widget _tail() {
    bool isManager = widget.msgContent.isManager;
    return Row(
      children: [
        if (isManager) _inviteMicWidget(),
        SizedBox(width: 11.pt),
        _welcomeWidget(),
      ],
    );
  }

  Widget _inviteMicWidget() {
    return _btnWidget(
        childAsset: Res.roomMsgInviteMic,
        hasClick: _hasClickInvite,
        onTap: _inviteMic);
  }

  Widget _welcomeWidget() {
    return _btnWidget(
        childAsset: Res.roomMsgWelcomeHand,
        hasClick: _hasClickWelcome,
        onTap: _welcomeTap);
  }

  Widget _btnWidget(
      {required String childAsset, required bool hasClick, Function? onTap}) {
    return GestureDetector(
      onTap: () => {
        if (!hasClick) {onTap?.call()}
      },
      child: Opacity(
        opacity: hasClick ? 0.5 : 1.0,
        child: Container(
          width: 44.pt,
          height: 28.pt,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(7.pt)),
              border: Border.all(color: Color(0xFF9F59B9), width: 1.pt)
          ),
          child: FLImage.asset(childAsset, width: 20.pt, fit: BoxFit.cover),
        ),
      ),
    );
  }

  /// 欢迎
  void _welcomeTap() async {
    if (widget.msgContent.otherUser == null) return;
    _hasClickWelcome = true;
    var user = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomWelcomeClick(
        act: 'welcome',
        status: user?.identity?.type?.toString(),
        roomId: roomService.getCurrentRoom()?.roomId,
        type: roomService.getCurrentRoom()?.mode);

    UserInfo? info = UserInfo.fromPb(widget.msgContent.otherUser!);
    roomChatService.sendEnterRoomMsg(isWelcome: true, atUser: info);
    if (mounted) {
      setState(() {});
    }

    rxUtil.send(RoomUserOptEvent.welCome, widget.msgContent.messageId);
  }

  /// 邀麦
  void _inviteMic() async {
    if (widget.msgContent.otherUser == null) return;
    _hasClickInvite = true;
    var response = await micSeatService.inviteMic(
        uid: widget.msgContent.otherUser?.uid ?? "");
    RoomStatistics.reportRoomInvitedMic(
        roomType: roomService.getCurrentRoom()?.roomType,
        toUid: widget.msgContent.otherUser?.uid,
        toGender:
        widget.msgContent.otherUser?.sex.value == 1 ? sexMale : sexFemale,
        type: roomService.getCurrentRoom()?.mode,
        roomId: roomService.getCurrentRoom()?.roomId,
        act: '1',
        from: LiveRoomHandler.from);
    var user = await roomService.getCurrentUserInfo();
    RoomStatistics.reportRoomWelcomeClick(
        act: 'invite',
        status: user?.identity?.type?.toString(),
        roomId: roomService.getCurrentRoom()?.roomId,
        type: roomService.getCurrentRoom()?.mode);
    if (response.isSuccess) {
      rxUtil.send(RoomUserOptEvent.inviteMic, widget.msgContent);
    } else {
      toast(response.msg ?? LocaleStrings.instance.pleaseTryAgain, isDark: true);
    }
    if (mounted) {
      setState(() {});
    }
  }

  Widget _fans() {
    return Visibility(
      visible: widget.msgContent.chatUserInfo?.showFansLevel == true,
      child: Padding(
        padding: EdgeInsetsDirectional.only(end: 3.pt),
        child: UserFanLevelWidget(level: widget.msgContent.chatUserInfo?.fansClubLevel ?? 0),
      ),
    );
  }

/*Widget _medalWidget() {
    return FutureBuilder<UserInfo?>(
      future: widget.msgContent.chatUserInfo?.baseInfo,
      builder: (BuildContext context, AsyncSnapshot<UserInfo?> snapshot) {
        List<UserMedal> medals = snapshot.data?.medals ?? [];
        List<Widget> childList() {
          return List.generate(
              medals.length,
              (index) => Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CachedNetworkImage(
                        imageUrl: medals[index].icon ?? "",
                        errorWidget: (context, url, value) =>
                            GlobalWidgets.imageError(size: 20.pt),
                        placeholder: (context, url) =>
                            GlobalWidgets.imagePlaceholder(),
                        height: 20.pt,
                        width: 20.pt,
                      ),
                    ],
                  ));
        }

        return Visibility(
            visible: medals.isNotEmpty,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(mainAxisSize: MainAxisSize.min, children: childList()),
              ],
            ));
      },
    );
  }*/
}
