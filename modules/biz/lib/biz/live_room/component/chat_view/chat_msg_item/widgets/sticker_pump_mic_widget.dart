import 'package:flutter/cupertino.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/room/model/sticker_dice_model.dart';
import 'package:service/service.dart';

class StickerPumpWidget extends StatefulWidget {
  final StickerDiceModel dice;

  Size size;
  VoidCallback? playCompletion;

  StickerPumpWidget({
    required this.dice,
    this.size = Size.zero,
    this.playCompletion
  });

  @override
  State<StatefulWidget> createState() => _StickerPumpWidgetState();
}

class _StickerPumpWidgetState extends State<StickerPumpWidget>
    with SingleTickerProviderStateMixin {
  bool _showDiceSvga = true;
  final SvgaController _svgaController = SvgaController();
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  late SvgaInfo _svgaInfo;

  @override
  void initState() {
    _animationController = AnimationController(
        vsync: this, duration: Duration(milliseconds: 1200));
    _animation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 1.8, end: 1), weight: 2),
      TweenSequenceItem(tween: ConstantTween(1), weight: 15)
    ]).animate(_animationController);
    _showDiceSvga =
        DateTime.now().millisecondsSinceEpoch - widget.dice.startTime <= 1000;
    _svgaController.end = (info) {
      _showDiceSvga = false;
      _animationController.forward(from: 0);
      widget.playCompletion?.call();
      if (mounted) {
        setState(() {});
      }
    };
    _svgaInfo = SvgaInfo(assetUrl: Res.roomSvgaStickerPumpMic, times: 1);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StickerPumpWidget oldWidget) {
    _showDiceSvga =
        DateTime.now().millisecondsSinceEpoch - widget.dice.startTime <= 1000;
    _svgaController.stopSvga();
    _svgaController.startSvga(_svgaInfo);
    _svgaController.end = _svgaController.end;
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.playCompletion?.call();
    _svgaController.stopSvga();
    _svgaController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_showDiceSvga) {
      return Container(
        width: widget.size.width > 0 ? widget.size.width : 62.pt,
        height: widget.size.height > 0 ? widget.size.height : 62.pt,
        child: SvgaWidget(
          controller: _svgaController,
          svgaInfo: _svgaInfo,
        ),
      );
    } else {
      if (_animationController.status == AnimationStatus.dismissed) {
        return FLImage.asset(
            "res/room/sticker/sticker_pump_mic_${widget.dice.num}.png",
            width: widget.size.width > 0 ? widget.size.width : 62.pt,
            height: widget.size.height > 0 ? widget.size.height : 62.pt);
      }
      return AnimatedBuilder(
        animation: _animation,
        builder: (BuildContext context, Widget? child) {
          return ScaleTransition(
            scale: _animation,
            child: FLImage.asset(
                "res/room/sticker/sticker_pump_mic_${widget.dice.num}.png",
                width: widget.size.width > 0 ? widget.size.width : 62.pt,
                height: widget.size.height > 0 ? widget.size.height : 62.pt),
          );
        },
      );
    }
  }
}
