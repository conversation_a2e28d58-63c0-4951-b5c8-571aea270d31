import 'package:biz/biz/live_room/component/chat_view/room_msg_factory.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/live/chat/model/style/style_user_text_btn_msg_content.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';

import '../widgets/chat_at_username.dart';
import '../widgets/msg_text_btn.dart';

/// 样式为 以用户Avatar + 用户名
/// 气泡部分， 左侧文案，右侧按钮样式
class StyleUserTextBtnItem extends StatelessWidget {
  final StyleUserTextBtnMsgContent content;
  final RoomMainController mainController;

  const StyleUserTextBtnItem(this.content, {super.key, required this.mainController});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxWidth: RoomMsgFactory.contentMaxWidth(context)),
      decoration: content.decoration ?? roomTheme.theme.msgDecorate,
      padding: EdgeInsetsDirectional.only(start: 11.pt, end: 11.pt),
      child: Row(
        children: [
          Expanded(child: _user()),
          _btn(),
        ],
      ),
    );
  }

  Widget _user() {
    return GestureDetector(
      onTap: () => showUserCardDialog(
        targetId: content.chatUserInfo?.uid ?? "",
      ),
      behavior: HitTestBehavior.opaque,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 11.pt),
          Row(
            children: [
              UserAvatar(url: content.chatUserInfo?.avatar, size: 20.pt),
              SizedBox(width: 5.pt),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        _fans(),
                        Flexible(
                          child: ChatAtUserName(
                            name: content.chatUserInfo?.nickname?.fixAutoLines() ?? "",
                            uid: content.chatUserInfo?.uid ?? "",
                          ),
                        ),
                      ],
                    ),
                    CharmWealthLabelsWidget(
                      charmLabelIcon: content.chatUserInfo?.charmInfo?.icon ?? "",
                      charmLevel: content.chatUserInfo?.charmInfo?.level ?? 0,
                      wealthLabelIcon: content.chatUserInfo?.wealthInfo?.icon ?? "",
                      wealthLevel: content.chatUserInfo?.wealthInfo?.level ?? 0,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 5.pt),
          _textWidget(),
          SizedBox(height: 11.pt),
        ],
      ),
    );
  }

  Widget _textWidget() {
    return Text(
      '${content.text}',
      style: roomTheme.theme.msgGrayTextStyle.copyWith(
        color: content.msgColor
      ),
    );
  }

  _btn() {
    if (content.hideBtn == true) {
      return SizedBox.shrink();
    }
    return GestureDetector(
      onTap: () {
        if (content.deepLink?.isNotEmpty == true) {
          DeepLink.jump(content.deepLink!, from: mainController.from);
        } else if (content.onTapBtn != null) {
          content.onTapBtn?.call();
        }
      },
      child: Padding(padding: EdgeInsetsDirectional.only(start: 10.pt), child: MsgTextBtn(text: '${content.btnText}')),
    );
  }

  Widget _fans() {
    return UserFanLevelMetaWidget(
      level: content.chatUserInfo?.fansClubShow?.level,
      fansGroupName: content.chatUserInfo?.fansClubShow?.name,
      padding: EdgeInsetsDirectional.only(end: 3.pt),
    );
  }
}
