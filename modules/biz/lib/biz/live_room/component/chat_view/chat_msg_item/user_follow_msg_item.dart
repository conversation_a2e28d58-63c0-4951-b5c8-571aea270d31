import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/component/user_card/room_user_card_widget.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/live/chat/model/chat_user_follow_msg_content.dart';
import 'package:service/service.dart';

import '../room_msg_factory.dart';
import 'widgets/chat_at_username.dart';
import 'widgets/msg_text_btn.dart';

/// 用户关注的消息样式
class UserFollowMsgItem extends StatefulWidget {
  final ChatUserFollowMsgContent msgContent;
  final String? from;

  const UserFollowMsgItem({Key? key, required this.msgContent, this.from}) : super(key: key);

  @override
  State<UserFollowMsgItem> createState() => _UserFollowMsgItemState();
}

class _UserFollowMsgItemState extends State<UserFollowMsgItem> {
  Throttle? _onTapThrottle;

  @override
  void initState() {
    super.initState();
    _onTapThrottle = Throttle(duration: const Duration(milliseconds: 1500));
  }

  @override
  void dispose() {
    _onTapThrottle?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isChat = widget.msgContent.reFollow || widget.msgContent.isFollowing;
    bool isFollowing = widget.msgContent.isFollowing;

    return Container(
      decoration: roomTheme.theme.msgDecorate,
      constraints: BoxConstraints(maxWidth: RoomMsgFactory.contentMaxWidth(context)),
      padding: EdgeInsetsDirectional.only(start: 11.pt, end: 11.pt, top: 11.pt, bottom: 11.pt),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAvatarName(),
                SizedBox(height: 5.pt),
                _buildTextContent(isFollowing),
              ],
            ),
          ),
          SizedBox(width: 29.pt),
          GestureDetector(
            onTap: () {
              _onTapThrottle?.call(() async {
                await _onTap(isChat, context);
              });
            },
            child: MsgTextBtn(
              text: isChat ? LocaleStrings.instance.chat : LocaleStrings.instance.follow,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextContent(bool isChat) {
    return Text(
      isChat ? LocaleStrings.instance.hasFollowedYou : LocaleStrings.instance.followedYou,
      style: roomTheme.theme.msgTextStyle,
    );
  }

  Widget _buildAvatarName() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            showUserCardDialog(
              targetId: widget.msgContent.chatUserInfo?.uid ?? "",
            );
          },
          child: UserAvatar(
            url: widget.msgContent.chatUserInfo?.avatar,
            userId: widget.msgContent.chatUserInfo?.uid,
            hasFrame: true,
            inRoom: true,
            size: 20.pt,
          ),
        ),
        SizedBox(width: 5.pt),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _fans(),
                  Flexible(
                    child: ChatAtUserName(
                      name: '${widget.msgContent.chatUserInfo?.nickname}',
                      uid: widget.msgContent.chatUserInfo?.uid,
                    ),
                  ),
                ],
              ),
              CharmWealthLabelsWidget(
                charmLabelIcon: widget.msgContent.chatUserInfo?.charmInfo?.icon ?? "",
                charmLevel: widget.msgContent.chatUserInfo?.charmInfo?.level ?? 0,
                wealthLabelIcon: widget.msgContent.chatUserInfo?.wealthInfo?.icon ?? "",
                wealthLevel: widget.msgContent.chatUserInfo?.wealthInfo?.level ?? 0,
              ),
            ],
          ),
        ),
        SizedBox(width: 3.pt),
      ],
    );
  }

  Future<void> _onTap(bool isChat, BuildContext context) async {
    var uid = widget.msgContent.chatUserInfo?.uid;
    if (isChat) {
      RoomMsgFactoryState.of(context).mainController.goChat(context: context, uid: uid);
    } else {
      // 关注
      bool success = await RoomMsgFactoryState.of(context)
          .mainController
          .followUser(context: context, uid: uid, from: 'room_public_messages');
      if (success) {
        widget.msgContent.reFollow = true;
        refresh();
      }
    }
  }

  Widget _fans() {
    return UserFanLevelMetaWidget(
      level: widget.msgContent.chatUserInfo?.fansClubShow?.level,
      fansGroupName: widget.msgContent.chatUserInfo?.fansClubShow?.name,
      padding: EdgeInsetsDirectional.only(end: 3.pt),
    );
  }
}
