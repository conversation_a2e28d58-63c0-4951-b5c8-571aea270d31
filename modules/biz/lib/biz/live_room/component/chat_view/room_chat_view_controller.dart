import 'dart:developer';

import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/modules/live/chat/abs_room_chat_service.dart';
import 'package:service/modules/live/chat/model/abs/abs_live_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_apply_mic_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/service.dart';

import '../../page/room/room_main_controller.dart';

const String _tag = 'RoomChat';
const bool _debug = true;

void chatPrint(String Function() log) {
  if (_debug) {
    Log.d(_tag, log());
  }
}

class RoomChatViewController extends GetxController
    with
        AbsRoomModuleController,
        GetBindingMixin,
        GetEventMixin,
        RoomMsgRefreshMixin {
  @override
  late RoomMainController mainController;

  RoomChatViewController(this.mainController);

  /// 公屏消息最大的数量
  final int maxMsgCount = 500;

  /// 达到容量后删除的最小数量
  final int leastDeleteCount = 200;

  /// 是否有新消息
  bool _hasNewMsg = false;
  bool _preKeyboardShow = false;

  final ItemScrollController msgController = ItemScrollController();
  final ItemPositionsListener msgPositionListener =
      ItemPositionsListener.create();
  static const Duration _scrollTime = Duration(milliseconds: 450);

  /// 是否可以滑动，但列表处于最后两条时可以滑动
  bool _canScroll = true;
  bool _isUserScrolling = false;
  bool _closePanelVisibility = false;

  List<AbsLiveMsgContent> msgList = [];

  int get msgCount => msgList.length;
  RxBool showMsgNew = RxBool(false);
  bool isKeyboardVisible = false;

  /// 当前视图可见的上边界
  int? _listUpIndex;

  /// 当前视图可见的下边界
  int? _listBottomIndex;

  bool? _firstLayout;

  @override
  void stateInit() {
    super.stateInit();
    roomChatService.addMsgRefreshMixin(this);
    listenRxEvent<bool?>(RoomSendMsgEvent.sendMsg, (_) {
      _canScroll = true;
    });
    // listenRxEvent<bool>(RoomOperationEvent.keyboardChange, (show) {
    //   _canScroll = true;
    //   _isUserScrolling = false;
    //   handleShowMsgNew(false);
    //   if (_preKeyboardShow != show) {
    //     _preKeyboardShow = show;
    //     Future.delayed(Duration(milliseconds: 600), () {
    //       if (!isPageValid) return;
    //       _scrollBottom();
    //     });
    //   }
    // });
    listenRxEvent<bool>(RoomOperationEvent.keyboardChange, _onKeyboardVisible);

    listenRxEvent<int>(RoomUserOptEvent.welCome, _welcome);

    listenRxEvent<ChatWelcomeMsgContent>(
        RoomUserOptEvent.inviteMic, _inviteMic);

    listenRxEvent(RoomUserOptEvent.approveMic, _approveMic);

    listenRxEvent<bool>(
        RoomOperationEvent.changeClosePanelStatus, _changeClosePanel);

    listenRxEvent<String>(RoomEvent.joinRoom, _onJoinRoom);

    msgPositionListener.itemPositions.addListener(_onListPositionListener);
  }

  void onScrollListLayout() {
    if (_firstLayout == true) {
      return;
    }
    _firstLayout = true;
    scrollBottom();
  }

  void _onListPositionListener() {
    var positions = msgPositionListener.itemPositions.value;
    if (positions.isNotEmpty) {
      _listUpIndex = positions
          .where((ItemPosition position) => position.itemTrailingEdge > 0)
          .reduce((ItemPosition min, ItemPosition position) =>
              position.itemTrailingEdge < min.itemTrailingEdge ? position : min)
          .index;
      _listBottomIndex = positions
          .where((ItemPosition position) => position.itemLeadingEdge < 1)
          .reduce((ItemPosition max, ItemPosition position) =>
              position.itemLeadingEdge > max.itemLeadingEdge ? position : max)
          .index;
    }
    // chatPrint(() => 'listView min=$_listUpIndex,max=$_listBottomIndex');
  }

  void _onJoinRoom(String rid) {
    chatPrint(() => '_onJoinRoom $rid');
    initData();
    scrollBottom();
  }

  @override
  void stateDispose() {
    _firstLayout = null;
    roomChatService.removeMsgRefreshMixin(this);
    msgPositionListener.itemPositions.removeListener(_onListPositionListener);
    super.stateDispose();
  }

  @override
  void firstFrame() {
    super.firstFrame();
    chatPrint(() => 'firstFrame ');
    initData();
  }

  void initData() async {
    List<AbsLiveMsgContent>? list = roomChatService.getMsgs();
    chatPrint(() => 'initData list.size=${list.length}');
    if (list.isNotEmpty == true) {
      msgList.clear();
      msgList.addAll(list);
      update();
    }
  }

  void scrollBottom() {
    chatPrint(() => '_scrollBottom enter');
    if (!_canScroll && _hasNewMsg && _isUserScrolling) {
      handleShowMsgNew(true);
      return;
    }
    if (getContext() != null &&
        msgController.isAttached &&
        msgList.isNotEmpty) {
      try {
        final routes = FLRouter.routeObserver.getRoutes();
        if (routes.isEmpty) return;
        var beginIndex = _listUpIndex;
        var bottomIndex = _listBottomIndex;
        if (beginIndex == null || bottomIndex == null) {
          return;
        }
        chatPrint(() =>
            '_scrollBottom view[$beginIndex,$bottomIndex],msg.size=${msgList.length}');
        // 不满一屏的时候不滑动
        if ((bottomIndex - beginIndex + 1) == msgList.length) {
          return;
        }
        int last = msgList.length - 1;
        // -1 for 部分机型最后条目未显示bug
        if (bottomIndex - 1 >= last) {
          return;
        }

        /// 显示关闭面板时或者不在栈顶时,直接跳转到最后一条
        if (_closePanelVisibility ||
            routes.last.settings.name != R_LIVE_LIVE_ROME) {
          chatPrint(() => '_scrollBottom jumpTo');
          msgController.jumpTo(index: last);
        } else {
          msgController.scrollTo(index: last, duration: _scrollTime);
          chatPrint(() => '_scrollBottom scrollTo');
        }
      } on Exception catch (e) {
        log('ChatViewBloc: $e');
      }
    }
  }

  void scrollStateUpdate(bool isScrolling, {bool isUser = false}) {
    if (isScrolling) {
      if (isUser) {
        _isUserScrolling = true;
      }
    } else {
      if (_isUserScrolling) {
        int count = msgCount;
        _canScroll =
            msgPositionListener.itemPositions.value.last.index >= (count - 2);
        if (_canScroll) {
          _isUserScrolling = false;
          _hasNewMsg = false;
          showMsgNew.value = false;
        }
      }
    }
  }

  void handleShowMsgNew(bool show) {
    if (show) {
      showMsgNew.value = true;
    } else {
      _canScroll = true;
      _isUserScrolling = false;
      if (!isPageValid) return;
      scrollBottom();
      _hasNewMsg = false;
      showMsgNew.value = false;
    }
  }

  @override
  void onMsgCleanAll({AbsLiveMsgContent? cleanMsg}) {
    chatPrint(() => 'onMsgCleanAll');
    _cleanMsgList(cleanMsg);
  }

  @override
  void onMsgRefresh(List<AbsLiveMsgContent> msgs) {
    chatPrint(() => 'onMsgRefresh msgs.size=${msgs.length}');
    _addData(msgs);
  }

  void _onKeyboardVisible(bool isKeyboardVisible) async {
    if (this.isKeyboardVisible == isKeyboardVisible) return;
    this.isKeyboardVisible = isKeyboardVisible;
    update();
  }

  void _welcome(int msgId) async {
    /// 刷新数据
    var list = msgList;
    for (var element in list) {
      if (element is ChatWelcomeMsgContent && element.messageId == msgId) {
        element.clickWelcome = true;
      }
    }
  }

  void _inviteMic(ChatWelcomeMsgContent msgContent) async {
    /// 刷新数据
    var list = msgList;
    for (var element in list) {
      if (element is ChatWelcomeMsgContent &&
          element.messageId == msgContent.messageId) {
        element.clickInvite = true;
      }
    }
  }

  void _approveMic(dynamic value) async {
    String uid = "";

    if (value is ChatApplyMicMsgContent) {
      uid = value.chatUserInfo?.uid ?? "";
    } else if (value is String) {
      uid = value;
    }

    if (uid.isEmpty) return;

    /// 刷新数据
    var list = msgList;
    for (var element in list) {
      if (element is ChatApplyMicMsgContent && element.messageId == uid) {
        element.hasAgree = true;
      }
    }
  }

  void _changeClosePanel(bool value) {
    _closePanelVisibility = value;
  }

  void _addData(List<AbsLiveMsgContent> newList) {
    List<AbsLiveMsgContent> old = msgList;
    var result = old + newList;
    if (result.length > maxMsgCount) {
      var more = result.length - maxMsgCount;
      if (more <= leastDeleteCount) {
        /// 移除所有部分数据，保留500条
        result.removeRange(
            0, result.length - maxMsgCount + leastDeleteCount - more);
      } else {
        /// 移除所有部分数据，保留500条
        result.removeRange(0, result.length - maxMsgCount);
      }
    }
    _hasNewMsg = true;
    msgList = result;
    update();

    // wait for _onListPositionListener to be call first
    Future.delayed(Duration(milliseconds: 100), () {
      if (!isPageValid) return;
      scrollBottom();
    });
  }

  void _cleanMsgList(AbsLiveMsgContent? msg) {
    List<AbsLiveMsgContent> newMsgList = [];
    if (msg != null) {
      newMsgList.add(msg);
    }
    _canScroll = true;
    _isUserScrolling = false;
    msgList = newMsgList;
    showMsgNew.value = false;
    update();
  }
}
