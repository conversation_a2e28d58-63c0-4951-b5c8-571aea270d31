import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/chat_view/chat_msg_item/reward_probability_gift_item.dart';
import 'package:biz/biz/live_room/page/room/room_main_controller.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/live/chat/model/abs/abs_live_msg_content.dart';
import 'package:service/modules/live/chat/model/abs/abs_live_msg_content_with_user.dart';
import 'package:service/modules/live/chat/model/chat_apply_mic_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_follow_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_image_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_join_fans_club_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_reward_st_end_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_room_name_content.dart';
import 'package:service/modules/live/chat/model/chat_stay_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_sticker_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_text_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_user_follow_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/chat/model/clean_chat_msg_content.dart';
import 'package:service/modules/live/chat/model/gift_msg_content.dart';
import 'package:service/modules/live/chat/model/invite_share_msg_content.dart';
import 'package:service/modules/live/chat/model/notice_msg_content.dart';
import 'package:service/modules/live/chat/model/red_pack_result_msg_content.dart';
import 'package:service/modules/live/chat/model/red_pack_tip_msg_content.dart';
import 'package:service/modules/live/chat/model/reward_probability_gift_content.dart';
import 'package:service/modules/live/chat/model/room_event_msg_content.dart';
import 'package:service/modules/live/chat/model/room_share_msg_content.dart';
import 'package:service/modules/live/chat/model/style/style_user_text_btn_msg_content.dart';
import 'package:service/modules/live/chat/model/unsupport_msg_content.dart';

import 'chat_msg_item/apply_mic_msg_item.dart';
import 'chat_msg_item/enter_msg_item.dart';
import 'chat_msg_item/follow_room_msg_item.dart';
import 'chat_msg_item/notice_msg_item.dart';
import 'chat_msg_item/red_pack_result_msg_item.dart';
import 'chat_msg_item/red_pack_tip_msg_item.dart';
import 'chat_msg_item/room_chat_clean_msg_item.dart';
import 'chat_msg_item/room_event_msg_item.dart';
import 'chat_msg_item/room_name_msg_item.dart';
import 'chat_msg_item/share_invite_msg_item.dart';
import 'chat_msg_item/share_room_msg_item.dart';
import 'chat_msg_item/style/style_user_text_btn_item.dart';
import 'chat_msg_item/system_msg_item.dart';
import 'chat_msg_item/unsupport_msg_item.dart';
import 'chat_msg_item/user_follow_msg_item.dart';
import 'chat_msg_item/user_join_fans_club_item.dart';
import 'chat_msg_item/user_msg_item/user_msg_item.dart';

class RoomMsgFactory extends StatefulWidget {
  final AbsLiveMsgContent msgContent;
  final RoomMainController mainController;

  /// 来源
  final String? from;
  final bool isKeyboardVisible;
  final bool canScroll;

  RoomMsgFactory({
    Key? key,
    required this.msgContent,
    required this.mainController,
    this.from,
    this.isKeyboardVisible = false,
    this.canScroll = true,
  });

  @override
  State<StatefulWidget> createState() => RoomMsgFactoryState();

  static double contentMaxWidth(BuildContext context, {bool userMsgItem = false}) {
    double size = MediaQuery.of(context).size.width -
        roomTheme.theme.chatListMargin -
        roomTheme.theme.roomAdBannerWidth -
        2 * roomTheme.theme.roomAdBannerMargin;
    if (userMsgItem == true) {
      size = size - roomTheme.theme.chatBubbleStartMargin;
    }
    return size;
  }
}

class RoomMsgFactoryState extends State<RoomMsgFactory> {
  RoomMainController get mainController => widget.mainController;
  static RoomMsgFactoryState of(BuildContext context) {
    return context.findAncestorStateOfType<RoomMsgFactoryState>()!;
  }

  @override
  Widget build(BuildContext context) {
    return _itemDetail(widget.msgContent);
  }

  Widget _itemDetail(AbsLiveMsgContent content) {
    switch (content.runtimeType) {
      case ChatTextMsgContent:
        var msgContent = content as ChatTextMsgContent;
        if (msgContent.chatUserInfo == null) {
          return SystemMsgItem(msgContent: msgContent);
        }
        return _wrapUserItem(
            msgContent: content, isKeyboardVisible: widget.isKeyboardVisible);
      case ChatImageMsgContent:
        return _wrapUserItem(
            msgContent: content as ChatImageMsgContent,
            showBg: false,
            showChatFrame: false);
      case InviteShareMsgContent:
        return ShareInviteMsgItem(msgContent: content as InviteShareMsgContent);
      case RoomShareMsgContent:
        return ShareRoomMsgItem(msgContent: content as RoomShareMsgContent);
      case NoticeMsgContent:
        return NoticeMsgItem(msgContent: content as NoticeMsgContent);
      case UnSupportMsgContent:
        return UnsupportMsgItem(msgContent: content as UnSupportMsgContent);
      case ChatWelcomeMsgContent:
        ChatWelcomeMsgContent msgContent = content as ChatWelcomeMsgContent;
        if (msgContent.type == 1) {
          return EnterMsgItem(msgContent: msgContent);
        } else {
          return _wrapUserItem(
              msgContent: msgContent,
              isKeyboardVisible: widget.isKeyboardVisible);
        }
      case ChatFollowMsgContent:
        ChatFollowMsgContent msgContent = content as ChatFollowMsgContent;
        return FollowRoomMsgItem(
          msgContent: msgContent,
          from: widget.from,
        );

      case ChatUserFollowMsgContent:
        return UserFollowMsgItem(
          msgContent: content as ChatUserFollowMsgContent,
          from: widget.from,
        );

      case GiftMsgContent:
        return _wrapUserItem(
            showBg: false,
            msgContent: content as AbsLiveMsgContentWithUser,
            showChatFrame: false);

      case ChatStickerMsgContent:
        return _wrapUserItem(
            msgContent: content as AbsLiveMsgContentWithUser,
            showBg: false,
            showChatFrame: false);

      case ChatApplyMicMsgContent:
        ChatApplyMicMsgContent msgContent = content as ChatApplyMicMsgContent;
        return ApplyMicRoomMsgItem(
          msgContent: msgContent,
          from: widget.from,
        );

      /// 房间活动
      case RoomEventMsgContent:
        RoomEventMsgContent msgContent = content as RoomEventMsgContent;
        return RoomEventMsgItem(
          msgContent: msgContent,
        );

      case CleanChatMsgContent:
        return RoomChatCleanMsgItem(msgContent: content as CleanChatMsgContent);

      case ChatStayMsgContent:
        return _wrapUserItem(
            msgContent: content as ChatStayMsgContent, showChatFrame: false);

      case ChatRoomNameContent:
        return RoomNameMsgItem(content: content as ChatRoomNameContent);
      case RewardProbabilityGiftContent:
        RewardProbabilityGiftContent msgContent = content as RewardProbabilityGiftContent;
        return RewardProbabilityGiftItem(msgContent: msgContent);
      case ChatRewardStEndMsgContent:
        return _wrapUserItem(
            msgContent: content as ChatRewardStEndMsgContent, showChatFrame: false);
      case ChatJoinFansClubMsgContent:
        return UserJoinFansClubItem(content as ChatJoinFansClubMsgContent, mainController: mainController);
      case StyleUserTextBtnMsgContent:
        return StyleUserTextBtnItem(content as StyleUserTextBtnMsgContent, mainController: mainController);
      case RedPackTipMsgContent:
        return RedPackTipMsgItem(content as RedPackTipMsgContent);
      case RedPackResultMsgContent:
        return RedPackResultMsgItem(content as RedPackResultMsgContent);
      default:
        return SizedBox.shrink();
    }
  }

  Widget _wrapUserItem(
      {required AbsLiveMsgContentWithUser msgContent,
      bool showBg = true,
      bool showChatFrame = true,
      bool isKeyboardVisible = false}) {
    return UserMsgItem(
      maxContentWidth: widget.canScroll ? RoomMsgFactory.contentMaxWidth(context, userMsgItem: true) : 228.pt,
      msgContent: msgContent,
      showBg: showBg,
      showChatFrame: showChatFrame,
      isKeyboardVisible: isKeyboardVisible,
      mainController: mainController,
    );
  }
}
