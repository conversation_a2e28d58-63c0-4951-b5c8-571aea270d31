import 'dart:async';
import 'dart:ui' as ui;

import 'package:biz/biz/live_room/component/profile/room_profile_dialog.dart';
import 'package:biz/biz/live_room/page/room_party/widget/room_level_widget.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/text_scroll.dart';
import 'package:service/modules/live/room/event/event.dart';

import '../../../../biz.dart';
import '../../page/room/room_main_controller.dart';

final GlobalKey followButtonKey = GlobalKey();

/// 顶部房主的信息+关注
class TopOwnerWidget extends StatefulWidget {
  final String getXTag;

  const TopOwnerWidget({Key? key, required this.getXTag}) : super(key: key);

  @override
  State<TopOwnerWidget> createState() => _TopOwnerWidgetState();
}

class _TopOwnerWidgetState extends State<TopOwnerWidget> {
  late RoomMainController _controller;
  int _level = 0;

  StreamSubscription? _upgradeSubscription;
  StreamSubscription? _roomInfoUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<RoomMainController>(tag: widget.getXTag);
    _level = _controller.roomLevel.value ?? 0;

    _upgradeSubscription = rxUtil.observer<int>(RoomEvent.roomLevelUpdate).listen((level) {
      if (mounted) {
        setState(() {
          _level = level;
        });
      }
    });
    _roomInfoUpdateSubscription = rxUtil.observer(RoomEvent.roomInfoUpdate).listen((_) {
      if (mounted) {
        setState(() {});
        WidgetUtils.post((duration) {
          getFollowButtonPosition();
        });
      }
    });

    Future.delayed(Duration(milliseconds: 300), () {
      if (!mounted) return;
      WidgetUtils.post((duration) {
        if (mounted) getFollowButtonPosition();
      });
    });
  }

  @override
  void dispose() {
    _upgradeSubscription?.cancel();
    _roomInfoUpdateSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _jumpOwnerCardDialog,
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Container(
      height: 37.pt,
      decoration: BoxDecoration(
        gradient: LinearGradient(
            colors: [roomTheme.theme.topContainerBg, Colors.transparent],
            begin: Alignment.centerRight,
            end: Alignment.centerLeft),
        borderRadius: BorderRadius.circular(22.pt),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          4.wSpace,
          _buildAvatar(),
          5.wSpace,
          Flexible(child: _buildRoomNameID()),
          4.wSpace,
          RoomLevelWidget(level: _level, isLv: true),
          4.wSpace,
          _buildFollow(),
          4.wSpace,
        ],
      ),
    );
  }

  Offset? getFollowButtonPosition() {
    final RenderBox? renderBox = followButtonKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return null;
    final offset = renderBox.localToGlobal(Offset.zero);
    rxUtil.send(RoomOperationEvent.roomFollowAvailable, offset);
    return offset;
  }

  Widget _buildFollow() {
    if (roomService.getCurrentRoom()?.isOwner == true || roomService.getCurrentRoom()?.isFollowed == 1) {
      return SizedBox.shrink();
    }
    return GestureDetector(
      onTap: () {
        _controller.onTapFollowRoom();
      },
      child: Container(
        key: followButtonKey,
        width: 28.5.pt,
        height: 28.5.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: [
            Color(0xFF7D0AFE),
            Color(0xFFB91EFD),
          ]),
          borderRadius: BorderRadius.circular(50.pt),
        ),
        child: FLImage.asset(Res.commonAdd3, width: 18.5.pt),
      ),
    );
  }

  void _jumpOwnerCardDialog({bool isTapAvatar = false}) {
    final isFamilyRoom = roomService.getCurrentRoom()?.isFamilyRoom ?? false;
    if (isFamilyRoom && isTapAvatar) {
      routerUtil.push(R_FAMILY_DETAIL, params: {P_ID: roomService.getCurrentRoom()?.roomId});
    } else {
      showRoomProfileDialog(_controller, context: context);
    }
    // RoomFansDetailBottomSheetPage.showBottomSheet(_controller);
    // roomService.fansService.fansClubClickTopDot();
  }

  _buildAvatar() {
    return Obx(() {
      String? avatarCode = _controller.roomOwnerAvatarCode.value;
      String? avatarUrl = _controller.roomOwnerAvatar.value;
      final cover = _controller.cover.value ?? '';
      final isNet = cover.contains('http');
      final isOnlyFamilyCanEnter = roomService.getCurrentRoom()?.isOnlyFamilyCanEnter ?? false;
      return GestureDetector(
        onTap: () => _jumpOwnerCardDialog(isTapAvatar: true),
        child: Stack(
          children: [
            isNet
                ? Container(
                    width: 37.pt,
                    height: 37.pt,
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(2.pt)),
                    child: GlobalWidgets.cachedNetImage(imageUrl: cover, width: 30.pt, fit: BoxFit.cover),
                  )
                : UserAvatar(
                    url: avatarUrl ?? '',
                    size: 37.pt,
                    avatarCode: avatarCode,
                  ),
            if (isOnlyFamilyCanEnter)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(1.pt),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(2.pt), color: Colors.black.withOpacity(0.78)),
                  child: FLImage.asset(Res.roomSettingsIcAccessPermissonLock, width: 12.pt, fit: BoxFit.cover),
                ),
              )
          ],
        ),
      );
    });
  }

  Widget _buildRoomNameID() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildRoomName(),
        _buildRoomID(),
      ],
    );
  }

  Widget _buildRoomName() {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 101.pt),
      child: Obx(() {
        var name = _controller.roomName.value;
        return TextScroll(
          '\u{00A0} $name \u{00A0}', // 两边留白防止阴影
          mode: TextScrollMode.endless,
          velocity: Velocity(pixelsPerSecond: Offset(20, 0)),
          delayBefore: Duration(milliseconds: 1000),
          pauseBetween: Duration(milliseconds: 1000),
          numberOfReps: 2,
          style: TextStyle(
            color: roomTheme.theme.textColor,
            fontSize: 13.pt,
            fontWeight: FontWeightExt.heavy,
            height: 1.1,
          ),
          textAlign: TextAlign.right,
          fadedBorder: true,
        );
      }),
    );
  }

  Widget _buildRoomID() {
    return Text(
      '${LocaleStrings.instance.id}:${_controller.roomShowID.value}',
      style: TextStyle(
        color: roomTheme.theme.textColor2,
        fontWeight: FontWeightExt.medium,
        fontSize: 11.pt,
      ),
    );
  }
}

// 自定义绘制三角形
class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        colors: [Color(0xFF7D0AFE), Color(0xFFB91EFD)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    final path = ui.Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
