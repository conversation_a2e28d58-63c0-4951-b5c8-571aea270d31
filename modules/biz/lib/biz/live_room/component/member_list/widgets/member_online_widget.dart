import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/page/room_rank/room_rank_helper.dart';
import 'package:biz/biz/live_room/super_admin/punish_dialog.dart';
import 'package:biz/biz/live_room/theme/room_theme.dart';
import 'package:biz/biz/live_room/component/fans/widget/user_fans_widget.dart';
import 'package:biz/biz/live_room/widgets/level_info_label_widget.dart';
import 'package:biz/biz/user/badge/widget/badge_display_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/global/widgets/user_color_name_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/live/room/model/room_user_info_model.dart';

import '../member_tab_controller.dart';

class MemberOnlineWidget extends StatelessWidget {
  final int index;
  final RoomUserInfoModel model;

  MemberOnlineWidget({required this.index, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          _avatar(model, context),
          Expanded(child: _profileContainer(model)),
          _action(context)
        ],
      ),
    );
  }

  _buildNum() {
    return Container(
      width: 44.pt,
      child: Center(
        child: Text(
          '${index + 1}',
          style: TextStyle(
            color: rankIndexColor(index),
            fontSize: 21.pt,
            fontWeight: FontWeightExt.heavy,
            height: 1.2,
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }

  Widget _avatar(RoomUserInfoModel model, BuildContext context) {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.only(top: 3.pt, bottom: 3.pt, right: 3.pt),
        child: UserAvatar(
          url: model.avatar ?? '',
          size: 42.pt,
          hasFrame: true,
          inRoom: true,
          userId: model.uid,
        ),
      ),
      onTap: () {
        routerUtil.pop(context: context);
        gotoUserCard(model.uid);
      },
    );
  }

  Widget _profileContainer(RoomUserInfoModel model) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _profile(model),
        _applyMicTag(model),
      ],
    );
  }

  Widget _profile(RoomUserInfoModel model) {
    bool isOther = model.uid != accountService.getAccountInfo()?.uid;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
          child: Padding(
            padding: EdgeInsetsDirectional.only(bottom: 2.pt),
            child: UserColorNameWidget(
              style: TextStyle(
                color: R.color.textBlueColor3,
                fontSize: 14.sp,
                fontWeight: FontWeightExt.medium,
              ),
              name: model.nickname,
              uid: model.uid,
            ),
            // child: UserNameWidget(name: model.nickname),
          ),
        ),
        10.wSpace,
        _gender(model),
        10.wSpace,
        _ownerAndAdminTag(model),
        isOther ? (model.isFollowed ? _followYouTag(model) : _distanceTag(model)) : SizedBox.shrink(),
      ],
    );
  }

  Widget _gender(RoomUserInfoModel model) {
    bool isFemale = model.sex == Sex.female;
    String genderRes = isFemale ? Res.profileFemale : Res.profileMale;

    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 3.pt),
      height: 14.pt,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7.pt), color: isFemale ? R.color.femaleColor : R.color.maleColor),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FLImage.asset(genderRes, color: Colors.white, width: 14.pt, height: 14.pt),
          Visibility(
              visible: model.age != null && model.age! > 0,
              child: Text(
                '${model.age}',
                style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.medium),
              ))
        ],
      ),
    );
  }

  Widget _applyMicTag(RoomUserInfoModel model) {
    return Padding(
      padding: EdgeInsets.only(top: 2.pt),
      child: Row(
        children: [
          Visibility(
            visible: model.showFansLevel,
            child: Padding(
              padding: EdgeInsetsDirectional.only(end: 8.pt),
              child: UserFanLevelWidget(level: model.fansClubLevel ?? 0),
            ),
          ),
          CharmWealthLabelsWidget(
            charmLabelIcon: model.charmInfo?.icon ?? "",
            charmLevel: model.charmInfo?.level ?? 0,
            wealthLabelIcon: model.wealthInfo?.icon ?? "",
            wealthLevel: model.wealthInfo?.level ?? 0,
            padding: EdgeInsets.only(right: 5.5.pt),
          ),
          BadgeDisplayWidget(badges: model.badges, size: Size(22.pt, 22.pt), horizontalPadding: 3.pt, maxDisplayCount: 3)
        ],
      ),
    );
  }

  Widget _ownerAndAdminTag(RoomUserInfoModel model) {
    bool isVisible = model.isAdmin;
    return Visibility(
      visible: isVisible,
      child: Container(
        height: 14.pt,
        margin: EdgeInsets.only(right: 5.pt),
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 5.pt),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.pt),
            color: model.isOwner ? R.color.secondaryPurpleColor : R.color.secondaryYellowColor),
        child: Text(
          model.isOwner ? LocaleStrings.instance.owner : LocaleStrings.instance.admin,
          style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.heavy),
        ),
      ),
    );
  }

  Widget _followYouTag(RoomUserInfoModel model) {
    return Visibility(
      visible: model.isFollowed,
      child: Container(
        height: 14.pt,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 5.pt),
        margin: EdgeInsets.only(left: 3.pt),
        decoration:
            BoxDecoration(borderRadius: BorderRadius.circular(5.pt), color: R.color.textColor4.withOpacity(0.4)),
        child: Text(
          LocaleStrings.instance.followYou,
          style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.heavy),
        ),
      ),
    );
  }

  Widget _distanceTag(RoomUserInfoModel model) {
    return Visibility(
      visible: model.distance?.isNotEmpty == true,
      child: Container(
        height: 14.pt,
        margin: EdgeInsets.only(left: 3.pt),
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 5.pt),
        decoration:
            BoxDecoration(borderRadius: BorderRadius.circular(5.pt), color: R.color.textColor4.withOpacity(0.4)),
        child: Text(
          LocaleStrings.instance.distanceTag(model.distance ?? ''),
          style: TextStyle(color: Colors.white, fontSize: 9.sp, fontWeight: FontWeightExt.heavy),
        ),
      ),
    );
  }

  Widget _action(BuildContext context) {
    return Visibility(
      visible: accountService.isSuperAdmin(),
      child: GestureDetector(
        onTap: () {
          if (model.uid?.isNotEmpty == true) {
            showPunishDialog(context: context, targetUid: model.uid!);
          }
        },
        child: Container(
          height: 26.pt,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 11.pt),
          margin: EdgeInsets.only(right: 10.pt),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(13.pt), color: R.color.warningColor),
          child: Text(
            LocaleStrings.instance.punish,
            style: TextStyle(color: Colors.white, fontSize: 11.sp, fontWeight: FontWeightExt.heavy),
          ),
        ),
      ),
    );
  }

  Widget _buildScore() {
    if (model.contribution == null) {
      return SizedBox.shrink();
    }
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 8.pt, end: 18.pt),
      child: Text(
        '${model.contribution}',
        style: TextStyle(
          color: roomTheme.theme.dialogTextGray,
          fontSize: 13.pt,
          fontWeight: FontWeightExt.medium,
          height: 1.3,
        ),
      ),
    );
  }
}
