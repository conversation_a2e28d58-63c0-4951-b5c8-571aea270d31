import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/global/widgets/bubble/bubble_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/finance/model/starlight_flow_tab_model.dart';
import 'package:service/modules/user/const/events.dart';

import '../controller/starlight_exchange_controller.dart';
import 'event/refresh_event.dart';

class IncomePageController extends AbsGetController with GetStatusCtlMix, StarlightExchangeController {
  static const String _agencyBubbleTipTag = 'agency_tip_bubble_tip';

  final TickerProvider tickerProvider;
  TabController? tabController;

  List<StarlightTabItem> tabList = [];

  IncomePageController(this.tickerProvider);

  bool _isShowingAgencyTipBubble = false;

  RefreshController refreshController = RefreshController();

  @override
  void stateInit() {
    super.stateInit();
    listenRxEvent<int>(AgencyEvent.applySubmit, updateCanSubmitApply);
    loadData();
  }

  @override
  void stateDispose() {
    tabController?.dispose();
    super.stateDispose();
  }

  Future<void> loadData({bool showLoading = true}) async {
    if (showLoading) {
      viewStatus = GetStatusView.loading;
    }
    var resp = await loadStarlightInfo();
    if (resp.isSuccess) {
      var tabResp = await financeService.starlightFlowTab();
      if (tabResp.isSuccess && tabResp.data?.tabList != null) {
        tabList = tabResp.data!.tabList;
        int index = tabController?.index ?? 0;
        tabController?.dispose();
        tabController = TabController(length: tabList.length, vsync: tickerProvider, initialIndex: index);
      }

      if (viewStatus == GetStatusView.content) {
        update();
      } else {
        viewStatus = GetStatusView.content;
      }
    } else {
      errMsg = resp.msg ?? LocaleStrings.instance.defaultError;
      viewStatus = GetStatusView.error;
    }

    WidgetUtils.post((duration) {
      rxUtil.send(RefreshEvent.pullDownRefresh, 1);
    });
  }

  void updateCanSubmitApply(int applied) {
    starlightModel?.agencyInfo.canSubmitApplyAgency = !applied.isTrue();
  }

  void applyJoin({bool? forceShow}) {
    // if (starlightModel?.isNewUser == true) {
      if (starlightModel?.agencyInfo.canSubmitApplyAgency ?? false) {
        routerUtil.push(R_APPLY_AGENCY, context: getContext());
      } else {
        toast(LocaleStrings.instance.applyAgencySubmit);
      }
    // }
  }

  void showAgencyBubbleTip(BuildContext context) async {
    if (_isShowingAgencyTipBubble) {
      SmartDialog.dismiss(tag: _agencyBubbleTipTag);
      return;
    }
    _isShowingAgencyTipBubble = true;
    await SmartDialog.showAttach(
      targetContext: context,
      usePenetrate: false,
      tag: _agencyBubbleTipTag,
      maskColor: Colors.transparent,
      backDismiss: true,
      alignment: Alignment.bottomLeft,
      bindPage: true,
      clickMaskDismiss: true,
      builder: (_) => BubbleBox(
        backgroundColor: Color(0xE6000000),
        margin: EdgeInsetsDirectional.only(end: 24.pt),
        shape: BubbleShapeBorder(
          direction: BubbleDirection.top,
          arrowAngle: 4,
          arrowHeight: 4,
          arrowQuadraticBezierLength: 1,
          radius: BorderRadius.circular(7.pt),
          position: BubblePosition.end(30.pt),
        ),
        padding: EdgeInsets.symmetric(horizontal: 11.pt, vertical: 11.pt),
        child: Container(
          width: 270.pt,
          child: Text(
            '${starlightModel?.agencyInfo.starlightSettleDesc}',
            style: TextStyle(
              color: Colors.white,
              fontSize: 11.sp,
              fontWeight: FontWeightExt.medium,
              height: 1.45,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    _isShowingAgencyTipBubble = false;
  }
}
