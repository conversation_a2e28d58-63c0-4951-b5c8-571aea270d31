import 'dart:io';

import 'package:biz/biz.dart';

import 'package:biz/biz/login/bloc/login_bloc.dart';
import 'package:biz/biz/login/model/third_login_btn.dart';
import 'package:biz/biz/login/widget/third_login_btn.dart';
import 'package:biz/biz/login/widget/user_avatar_wall_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:service/global/widget/rich_text/light_text_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:get/get.dart';
import 'package:service/modules/account/model/login_options.dart';

import 'bloc/login_option_controller.dart';

@FRoute(url: R_LOGIN, desc: "登录")
class LoginPage extends StatelessWidget {
  final _bloc = LoginBloc();

  final _showCsLogin = false;

  LoginOptionsController? loginOptionLogic;

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    return LifecycleBlocBuilder<LoginBloc, LoginState>(
        bloc: _bloc,
        builder: (context, state) {
          return StreamBuilder(

              /// 单位长度更新
              stream: rxUtil.observer(ResolutionEvent.update),
              builder: (context, snapShot) {
                return Scaffold(
                  body: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Positioned.fill(
                        child: FLImage.asset(Res.loginBg, fit: BoxFit.cover),
                      ),
                      _userAvatarWall(context),
                      _logoAndSlogan(),
                      _test(),
                      _loginButtons(),
                      Positioned(
                        bottom: 66.pt,
                        left: 0,
                        right: 0,
                        child: SafeArea(child: _checkboxPolicy()),
                      ),
                      _feedback(),
                    ],
                  ),
                );
              });
        });
  }

  Widget _userAvatarWall(BuildContext context) {
    final top = MediaQuery.of(context).padding.top;
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Stack(
        children: [
          UserAvatarWallWidget(),
          Container(
            width: 1.w,
            height: 1.h,
            color: Colors.black.withOpacity(0.8),
          )
        ],
      ),
    );
  }

  List<Widget> _initLoginBtnData(LoginOptions options) {
    getMainStyleBtn(e) {
      if (e.enable) {
        return _buildMainStyleBtn(e.name);
      } else {
        return null;
      }
    }

    var _listBtnModel = <Widget>[];
    _listBtnModel.addAll(options.mainLogin.map(getMainStyleBtn).whereType<Widget>());
    if (options.otherLoginEnableSize() > 0) {
      if (_bloc.state.showMoreWay) {
        _listBtnModel.addAll(options.otherLogin.map(getMainStyleBtn).whereType<Widget>());
      } else {
        _listBtnModel.add(_moreWayBtn());
      }
    }

    if (Platform.isIOS) {
      _listBtnModel.insert(
          0,
          FutureBuilder<bool?>(
              future: AppleAdapter().isSupported(),
              builder: (context, snapShot) {
                return Visibility(
                  visible: snapShot.data == true,
                  child: ThirdLoginBtnWidget(
                    thirdLoginBtn: ThirdLoginBtn(
                        backGround: Colors.white,
                        title: LocaleStrings.instance.signInApple,
                        titleColor: R.color.textColor1,
                        iconRes: Res.loginLogoApple,
                        platform: Apple()),
                    onTap: (thirdLoginBtn) {
                      _bloc.add(ThirdLogin(platform: thirdLoginBtn.platform));
                    },
                  ),
                );
              }));
    }

    return _listBtnModel;
  }

  Widget? _buildMainStyleBtn(String name) {
    switch (name) {
      case 'google':
        return _buildGoogleBtn();
      case 'facebook':
        return _buildFacebookBtn();
      case 'phone':
        return phoneLoginBtn();
    }
    return null;
  }

  ThirdLoginBtnWidget _buildFacebookBtn() {
    return ThirdLoginBtnWidget(
      thirdLoginBtn: ThirdLoginBtn(
          backGround: Color(0xFF2B54B2),
          title: LocaleStrings.instance.signInFacebook,
          titleColor: Colors.white,
          iconRes: Res.commonLogoFacebook,
          platform: Facebook()),
      onTap: (thirdLoginBtn) {
        _bloc.add(ThirdLogin(platform: thirdLoginBtn.platform));
      },
    );
  }

  ThirdLoginBtnWidget _buildGoogleBtn() {
    return ThirdLoginBtnWidget(
      thirdLoginBtn: ThirdLoginBtn(
          backGround: Color(0xFF327FFF),
          title: LocaleStrings.instance.signInFaceGoogle,
          titleColor: Colors.white,
          iconRes: Res.commonLogoGoogle,
          platform: Google()),
      onTap: (thirdLoginBtn) {
        _bloc.add(ThirdLogin(platform: thirdLoginBtn.platform));
      },
    );
  }

  Widget _logoAndSlogan() {
    return Positioned(
      top: 0.2.h,
      left: 20.pt,
      right: 20.pt,
      child: GestureDetector(
        onPanDown: (details) {
          loginOptionLogic?.startTimer();
        },
        onPanEnd: (details) {
          loginOptionLogic?.endTimer();
        },
        onPanCancel: () {
          loginOptionLogic?.endTimer();
        },
        child: LoginPage.logoAndSlogan(),
      ),
    );
  }

  Widget _test() {
    if (kDebugMode || debugEnv) {
      return Positioned(
          top: 75.pt,
          left: 50.pt,
          right: 50.pt,
          child: HfButton(
            onPressed: () {
              _bloc.add(TestLoginEvent());
            },
            text: "Test Login",
            color: Colors.white,
            height: 30.pt,
          ));
    } else {
      return Positioned(top: 100.pt, left: 50.pt, right: 50.pt, child: SizedBox());
    }
  }

  Widget _loginButtons() {
    return Positioned(
      top: 0.47.h,
      right: 0,
      left: 0,
      child: GetBuilder<LoginOptionsController>(
        assignId: true,
        builder: (LoginOptionsController controller) {
          loginOptionLogic = controller;
          var rest = <Widget>[];
          rest.addAll(_initLoginBtnData(controller.loginOptions.value));

          if (_showCsLogin) {
            rest.add(
              HfButton(
                height: 40.pt,
                color: Colors.grey,
                width: 300.pt,
                onPressed: () => routerUtil.push(R_LOGIN_CS),
                text: 'CS Login',
              ),
            );
          }
          // rest.add(_privacyLink());
          return Column(
            children: rest,
          );
        },
      ),
    );
  }

  Widget _checkboxPolicy() {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => _bloc.add(CheckAgreePrivacy(true)),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                FLImage.asset(_bloc.state.agreePrivacy ? Res.loginChecked : Res.loginUnchecked,
                    width: 15.pt, height: 15.pt),
                SizedBox(width: 5.pt),
              ],
            ),
          ),
          Flexible(
            child: LightTextWidget(
              content: LocaleStrings.instance.agreeToTermAndPolicy,
              normalTextStyle: TextStyle(color: Colors.white, fontSize: 11.sp),
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.center,
              formats: [
                RichFormat(
                    router: '',
                    keyword: LocaleStrings.instance.agreeTo,
                    onTap: () => _bloc.add(CheckAgreePrivacy(true))),
                RichFormat(
                    router: '',
                    keyword: LocaleStrings.instance.termOfUse,
                    textStyle: TextStyle(
                      color: R.color.primaryLightColor,
                      fontSize: 11.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: R.color.primaryLightColor,
                    ),
                    onTap: () => _bloc.add(Terms())),
                RichFormat(
                    router: '',
                    keyword: LocaleStrings.instance.privacyPolicy,
                    textStyle: TextStyle(
                      color: R.color.primaryLightColor,
                      fontSize: 11.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: R.color.primaryLightColor,
                    ),
                    onTap: () => _bloc.add(Privacy())),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///三方登陆按钮
  Widget loginBtnItem({required ThirdLoginBtn thirdLoginBtn}) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 45.pt, vertical: 12.pt),
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            HfButton(
              onPressed: () {
                _bloc.add(ThirdLogin(platform: thirdLoginBtn.platform));
              },
              width: double.infinity,
              height: 46.pt,
              text: thirdLoginBtn.title,
              textStyle: TextStyle(color: thirdLoginBtn.titleColor, fontSize: 15.sp, fontWeight: FontWeight.bold),
              color: thirdLoginBtn.backGround,
              startSpacing: 38.pt,
              borderRadius: BorderRadius.all(Radius.circular(23.pt)),
            ),
            Positioned(
              left: 16.pt,
              child: FLImage.asset(thirdLoginBtn.iconRes, width: 22.pt, height: 22.pt),
            )
          ],
        ));
  }

  ///更多登录方式按钮
  Widget _moreWayBtn() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _bloc.add(ShowMoreWaysEvent());
      },
      child: Padding(
        padding: EdgeInsets.only(top: 12.pt, bottom: 20.pt),
        child: Text(
          LocaleStrings.instance.moreWaysToLogIn,
          style: TextStyle(fontSize: 14.sp, color: Colors.white, fontWeight: FontWeightExt.heavy, shadows: [
            Shadow(color: Colors.black12, offset: Offset(0, 1), blurRadius: 2),
          ]),
        ),
      ),
    );
  }

  ///手机登陆按钮
  Widget phoneLoginBtn() {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 45.pt, vertical: 12.pt),
        child: HfButton(
          onPressed: () => _bloc.add(PhoneLoginEvent()),
          width: double.infinity,
          height: 46.pt,
          child: Row(
            textDirection: TextDirection.ltr,
            children: [
              SizedBox(width: 16.pt),
              FLImage.asset(Res.loginIconLoginButtonPhone, width: 22.pt, height: 22.pt),
              Expanded(
                  child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 220.pt),
                  child: Text(
                    LocaleStrings.instance.signInWithPhoneNumber,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.white, fontSize: 15.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              )),
              SizedBox(width: 32.pt),
            ],
          ),
          color: R.color.primaryColor,
          borderRadius: BorderRadius.all(Radius.circular(23.pt)),
        ));
  }

  static Widget logoAndSlogan() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 84.pt,
          height: 84.pt,
          child: FLImage.asset(Res.commonLogo),
        ),
        SizedBox(height: 8.pt),
        SizedBox(
          width: 94.pt,
          child: FLImage.asset(Res.loginLoginBlackName),
        ),
      ],
    );
  }

  Widget _feedback() {
    return Positioned(
      top: 0.pt,
      right: 15.pt,
      child: SafeArea(
        child: HfButton(
          onPressed: () {
            routerUtil.push(R_FEEDBACK);
          },
          start: FLImage.asset(Res.loginIconFeedback, width: 11.pt, fit: BoxFit.cover),
          padding: EdgeInsets.symmetric(horizontal: 5.pt),
          borderRadius: BorderRadius.all(Radius.circular(5.pt)),
          border: Border.all(color: const Color(0xFFA670F1), width: 1.pt),
          text: LocaleStrings.instance.feedback,
          textStyle: TextStyle(fontSize: 11.sp, color: const Color(0xFFD7AFFF)),
          color: Colors.transparent,
          height: 20.pt,
        ),
      ),
    );
  }
}
