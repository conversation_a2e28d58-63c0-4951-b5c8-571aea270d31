import 'dart:async';

import 'package:biz/biz/feedback/const/feedback_categories.dart';
import 'package:biz/biz/settings/delete/delete_account/bloc/delete_account_bloc.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/delete_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/modules/alarm/abs_alarm_service.dart';
import 'package:service/modules/social/model/contact_group.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';

part 'assets_confirm_event.dart';

part 'assets_confirm_state.dart';

class AssetsConfirmBloc
    extends BaseBloc<AssetsConfirmEvent, AssetsConfirmState> {
  AssetsConfirmBloc() : super(AssetsConfirmState()) {
    on<DeleteEvent>(_deleteAccount);
    on<InitDataEvent>(_initData);
  }

  String get _type => getArgument<String>(P_TYPE) ?? "";

  String get _content => getArgument<String>(P_CONTENT) ?? "";

  @override
  void initBloc() {
    super.initBloc();
    add(InitDataEvent());
  }

  void _initData(InitDataEvent event, Emitter emitter) async {
    var followers = 0;
    var friends = 0;
    final contacts = await socialService.getContactGroups();
    contacts?.forEach((element) {
      if (element.name == groupFriends) {
        friends = element.uids.length;
      } else if (element.name == groupFollower) {
        followers = element.uids.length;
      }
    });
    emitter.call(state.clone(followers: followers, friends: friends));
  }

  void _deleteAccount(DeleteEvent event, Emitter emitter) {
    showAlertDialog(
      context: FLRouter.routeObserver.getLastContext(),
      content: LocaleStrings.instance.accountDataCantBeRecovered,
      cancelText: LocaleStrings.instance.no,
      cancelTextColor: R.color.primaryColor,
      confirmText: LocaleStrings.instance.yes,
      confirmTextColor: Color(0xFF121332),
      cancelIsLeft: false,
      onConfirm: () async {
        _deleteAccountRequest();
      },
    );
  }

  Future<bool> _submitFeedbackRequest() async {
    final feedback = getDeleteAccountFeedback(_type);
    final result = await userService.sendFeedback(
      categoryId: feedback.id,
      desc: _content,
      screenshotUrls: null,
    );
    return result != null;
  }

  void _deleteAccountRequest() async {
    showLoading();
    if (await _submitFeedbackRequest() && await userService.deleteAccount()) {
      hideLoading();
      _reportDeleteConfirm();
      accountService.logout();
      cancelPush();
      _showDeleteSuccessDialog();
    } else {
      hideLoading();
      toast(LocaleStrings.instance.requestTimeout);
    }
  }

  void _showDeleteSuccessDialog() {
    showAlertDialog(
        context: FLRouter.routeObserver.getLastContext(),
        title: LocaleStrings.instance.accountDeleted,
        content: LocaleStrings.instance.yourAccountWasDeleted,
        styleContent: TextStyle(fontSize: 15.sp, color: primaryColorTextDetail),
        styleTitle: TextStyle(
            fontSize: 16.sp,
            color: R.color.textColor1,
            fontWeight: FontWeight.bold),
        confirmTextColor: R.color.primaryColor,
        confirmText: LocaleStrings.instance.okay,
        showCancel: false);
  }

  String _getReason(String type) {
    String res = "";
    if (type == DeleteAccountEvent.doNotLike.name) {
      res = LocaleStrings.instance.iDontLikeWinker;
    } else if (type == DeleteAccountEvent.privacyProblem.name) {
      res = LocaleStrings.instance.privacyProblem;
    } else if (type == DeleteAccountEvent.broken.name) {
      res = LocaleStrings.instance.somethingIsBroken;
    } else if (type == DeleteAccountEvent.harassment.name) {
      res = LocaleStrings.instance.harassment;
    } else if (type == DeleteAccountEvent.other.name) {
      res = LocaleStrings.instance.other;
    }
    return res;
  }

  /// 由于该上报需要传uid和性别，必须在logout之前上报
  void _reportDeleteConfirm() async {
    final user = await userService.getCurrentUserInfo();
    DeleteStatistics.reportDeleteAccountConfirm(
        uid: user?.uid, gender: user?.sexStr);
  }

  /// 删除账号后需要取消本地推送
  Future<void> cancelPush() async {
    getService<AbsAlarmService>()?.cancelPush();
  }
}
