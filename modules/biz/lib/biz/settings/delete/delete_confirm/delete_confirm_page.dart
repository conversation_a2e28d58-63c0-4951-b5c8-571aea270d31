import 'package:biz/biz.dart';
import 'package:biz/biz/settings/delete/delete_confirm/bloc/delete_confirm_bloc.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';

@FRoute(desc: '删除账号的确认界面', url: R_SETTINGS_DELETE_CONFIRM)
class DeleteConfirmPage extends StatelessWidget {
  final _bloc = DeleteConfirmBloc();

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    return LifecycleBlocBuilder<DeleteConfirmBloc, DeleteConfirmState>(
      bloc: _bloc,
      builder: (_, state) => _page(),
    );
  }

  Widget _page() {
    return Scaffold(
      appBar: CommonAppBar(),
      body: _body(),
    );
  }

  Widget _body() {
    return Stack(
      children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.pt, vertical: 12.pt),
            child: Column(
              children: [_scrollWidget(), _deleteWidget()],
            )),
      ],
    );
  }

  Widget _scrollWidget() {
    return Expanded(
        child: SingleChildScrollView(
            child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleWidget(),
        SizedBox(height: 16.pt),
        _descriptionWidget(),
        _inputWidget(),
      ],
    )));
  }

  Widget _titleWidget() {
    return Text(_bloc.state.title ?? "",
        style: TextStyle(
            fontSize: 24.sp,
            color: R.color.textColor1,
            fontWeight: FontWeight.bold));
  }

  Widget _descriptionWidget() {
    return RichText(
        text: TextSpan(
            text: LocaleStrings.instance.description,
            style: TextStyle(
                fontSize: 14.pt,
                fontWeight: FontWeight.w600,
                color: R.color.textColor1),
            children: [
          TextSpan(
              text: " *",
              style: TextStyle(
                  fontSize: 14.pt,
                  fontWeight: FontWeight.w600,
                  color: momentColorFailed))
        ]));
  }

  Widget _inputWidget() {
    return Stack(
      children: [
        _inputTextField(),
        Positioned(
          child: _inputCountWidget(),
          bottom: 10.pt,
          left: 0.pt,
          right: 0.pt,
        ),
      ],
    );
  }

  Widget _inputTextField() {
    return Container(
        constraints: BoxConstraints(minHeight: 136.pt),
        margin: EdgeInsets.only(bottom: 5.pt, top: 12.pt),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(6.pt)),
            color: Color(0x1AA3AFC2)),
        child: TextField(
            style: TextStyle(color: Color(0xff333C4F), fontSize: 15.sp),
            maxLines: null,
            controller: _bloc.descriptionController,
            focusNode: _bloc.focus,
            decoration: InputDecoration(
              hintText: LocaleStrings.instance.typeTourSuggestionsToUs,
              counterText: "",
              contentPadding: EdgeInsets.all(12.pt),
              hintStyle: TextStyle(color: Color(0xFFBEC6D4), fontSize: 15.sp),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(23.pt),
                borderSide: BorderSide.none,
              ),
              fillColor: Colors.transparent,
            )));
  }

  Widget _inputCountWidget() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(child: Container()),
        RichText(
            text: TextSpan(children: [
          TextSpan(
              text: "${_bloc.state.count}",
              style: TextStyle(
                  fontSize: 14.sp,
                  color: _bloc.state.count > 0
                      ? R.color.primaryColor
                      : colorSubtitle)),
          TextSpan(
              text: "/${_bloc.state.maxCount}",
              style: TextStyle(fontSize: 14.sp, color: colorSubtitle)),
        ])),
        SizedBox(width: 10.pt)
      ],
    );
  }

  Widget _deleteWidget() {
    return Padding(
        padding: EdgeInsets.only(bottom: 48.pt, left: 16.pt, right: 16.pt),
        child: Opacity(
          opacity: _bloc.state.enableDelete ? 1 : 0.3,
          child: GeneralBtn(
              width: double.infinity,
              height: 48.pt,
              onTap: () {
                if (!_bloc.state.enableDelete) {
                  return;
                }
                _bloc.add(DeleteConfirmEvent());
              },
              title: LocaleStrings.instance.continueToDelete,
              fontSize: 17.sp),
        ));
  }
}
