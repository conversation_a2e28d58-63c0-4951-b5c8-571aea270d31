part of 'delete_confirm_bloc.dart';

@immutable
class DeleteConfirmState {
  final String? title;
  final String? description;
  final bool enableDelete;
  final int count;
  final int minCount;
  final int maxCount;

  DeleteConfirmState(
      {this.title,
      this.description,
      this.enableDelete = false,
      this.count = 0,
      this.minCount = 5,
      this.maxCount = 100});

  DeleteConfirmState clone(
      {String? title,
      String? description,
      bool? enableDelete,
      int? count,
      int? minCount,
      int? maxCount}) {
    return DeleteConfirmState(
        title: title ?? this.title,
        description: description ?? this.description,
        enableDelete: enableDelete ?? this.enableDelete,
        count: count ?? this.count,
        minCount: minCount ?? this.minCount,
        maxCount: maxCount ?? this.maxCount);
  }
}
