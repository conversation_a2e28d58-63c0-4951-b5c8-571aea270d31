import 'package:biz/biz.dart';
import 'package:biz/biz/settings/delete/delete_account/bloc/delete_account_bloc.dart';
import 'package:biz/common/widgets/controller/text_field_controller.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:flutter/cupertino.dart';

part 'delete_confirm_event.dart';
part 'delete_confirm_state.dart';

class DeleteConfirmBloc
    extends BaseBloc<DeleteConfirmEvent, DeleteConfirmState> {
  DeleteConfirmBloc() : super(DeleteConfirmState());

  CustomizedTextEditController descriptionController =
      CustomizedTextEditController();
  FocusNode focus = FocusNode();

  ///记录输入结果
  String lastInput = "";

  String get _type => getArgument<String>(P_TYPE) ?? "";

  @override
  void dispose() {
    descriptionController.dispose();
    super.dispose();
  }

  @override
  void initBloc() {
    super.initBloc();
    emit(state.clone(title: _getReason(_type)));

    descriptionController.addListener(() {
      if (lastInput != descriptionController.completeText) {
        lastInput = descriptionController.completeText;
        if (lastInput.length > state.maxCount) {
          String text = lastInput;
          while (text.length > state.maxCount) {
            text = text.characters.skipLast(1).string;
          }
          descriptionController.text = text;
          descriptionController.selection = TextSelection.fromPosition(
              TextPosition(offset: descriptionController.text.length));
        }
        emit(state.clone(
            description: descriptionController.text,
            count: descriptionController.text.length,
            enableDelete: descriptionController.text.length >= state.minCount));
      }
    });
  }

  @override
  Stream<DeleteConfirmState> mapEventToState(DeleteConfirmEvent event) async* {
    _deleteAccount();
  }

  void _deleteAccount() {
    focus.unfocus();
    routerUtil.popAndPush(R_SETTINGS_DELETE_CONFIRM_ASSETS,
        params: {P_TYPE: _type, P_CONTENT: descriptionController.text});
  }

  String _getReason(String type) {
    String res = "";
    if (type == DeleteAccountEvent.doNotLike.name) {
      res = LocaleStrings.instance.iDontLikeWinker;
    } else if (type == DeleteAccountEvent.privacyProblem.name) {
      res = LocaleStrings.instance.privacyProblem;
    } else if (type == DeleteAccountEvent.broken.name) {
      res = LocaleStrings.instance.somethingIsBroken;
    } else if (type == DeleteAccountEvent.harassment.name) {
      res = LocaleStrings.instance.harassment;
    } else if (type == DeleteAccountEvent.other.name) {
      res = LocaleStrings.instance.other;
    }
    return res;
  }

}
