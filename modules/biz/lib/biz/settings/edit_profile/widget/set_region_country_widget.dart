import 'package:biz/global/widgets/empty_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/region.dart';
import 'package:service/service.dart';
import 'set_region_controller.dart';
import 'package:azlistview_plus/azlistview_plus.dart';

class RegionCountryWidget extends StatefulWidget {
  final SetRegionController controller;

  const RegionCountryWidget({super.key, required this.controller});

  @override
  State<RegionCountryWidget> createState() => _RegionCountryWidgetState();
}

class _RegionCountryWidgetState extends State<RegionCountryWidget> {
  SetRegionController get _controller => widget.controller;

  @override
  void initState() {
    super.initState();
    widget.controller;
  }

  final TextStyle _itemStyle = TextStyle(
    fontSize: 16.sp,
    color: R.color.textColor1,
    fontWeight: FontWeightExt.heavy,
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchWidget(),
        if (_controller.selectedRegion != null)
          _buildSelectedRegion(_controller.selectedRegion!),
        _buildListRegion(),
      ],
    );
  }

  Widget _buildSearchWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 18.pt, vertical: 16.pt),
      child: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 34.pt),
        child: _textField(),
      ),
    );
  }

  Widget _textField() {
    return CupertinoTextField(
      textAlign: TextAlign.left,
      controller: _controller.textEditingController,
      maxLines: 1,
      onChanged: _controller.search,
      autofocus: false,
      prefix: Padding(
        padding: EdgeInsets.only(left: 16.pt),
        child: FLImage.asset(
          Res.commonSearch,
          width: 24.pt,
          height: 24.pt,
          color: colorSubtitle,
        ),
      ),
      suffixMode: OverlayVisibilityMode.editing,
      placeholder: LocaleStrings.instance.search,
      placeholderStyle: TextStyle(fontSize: 14.sp, color: colorSubtitle),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(23.pt),
      ),
      style: TextStyle(fontSize: 14.sp, color: R.color.textColor1),
      cursorColor: R.color.primaryLightColor,
      cursorHeight: 20.pt,
      cursorWidth: 2.pt,
      padding: EdgeInsets.fromLTRB(6.pt, 9.pt, 16.pt, 9.pt),
    );
  }

  Widget _buildSelectedRegion(Region region) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.pt, vertical: 5.pt),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleStrings.instance.selected,
            style: TextStyle(
              color: R.color.textBlueColor2,
              fontSize: 13.sp,
              fontWeight: FontWeightExt.heavy,
            ),
          ),
          SizedBox(height: 5.pt),
          Row(
            children: [
              FLImage.asset(Res.commonLocation, width: 32.pt, height: 32.pt),
              SizedBox(width: 5.pt),
              Text(
                region.name,
                style: _itemStyle,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildListRegion() {
    return Expanded(
      child: _controller.originCountryList.isNotEmpty &&
              _controller.dataList.isEmpty
          ? Center(
              child: EmptyWidget(
                detail: LocaleStrings.instance.noResult,
                logoAsset: Res.emptyEmptySearch,
              ),
            )
          : LayoutBuilder(
              builder: (context, constraints) {
                var barHeight = constraints.maxHeight / 26;
                return AzListView(
                  data: _controller.dataList,
                  indexBarItemHeight: barHeight,
                  indexBarAlignment: Alignment.centerRight,
                  physics: AlwaysScrollableScrollPhysics(),
                  itemCount: _controller.dataList.length,
                  itemBuilder: (BuildContext context, int index) {
                    if (index >= _controller.dataList.length) return SizedBox();
                    Region model = _controller.dataList[index];
                    return _buildListItem(model);
                  },
                  itemScrollController: _controller.itemScrollController,
                  susItemBuilder: (BuildContext context, int index) {
                    if (index >= _controller.dataList.length) return SizedBox();
                    Region model = _controller.dataList[index];
                    return _buildSusItem(context, model.getSuspensionTag());
                  },
                  indexBarData:
                      SuspensionUtil.getTagIndexList(_controller.dataList),
                  indexBarOptions: _indexBarOption(),
                );
              },
            ),
    );
  }

  IndexBarOptions _indexBarOption() {
    return IndexBarOptions(
      needRebuild: true,
      textStyle: TextStyle(
        fontSize: 10.sp,
        color: R.color.textColor1,
        fontWeight: FontWeightExt.medium,
      ),
      selectTextStyle: TextStyle(
        fontSize: 10.sp,
        color: R.color.primaryLightColor,
        fontWeight: FontWeightExt.medium,
      ),
      selectItemDecoration: BoxDecoration(shape: BoxShape.circle),
      indexHintWidth: 0,
      indexHintHeight: 0,
    );
  }

  Widget _buildSusItem(BuildContext context, String tag) {
    return Container(
      color: Color(0x33B8C4CE),
      child: Container(
        height: 23.pt,
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.symmetric(horizontal: 18.pt),
        alignment: Alignment.centerLeft,
        child: Text(
          tag,
          softWrap: false,
          style: TextStyle(
            fontSize: 13.sp,
            color: R.color.textBlueColor2,
          ),
        ),
      ),
    );
  }

  Widget _buildListItem(Region model) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 18.pt),
      child: ListTile(
        dense: true,
        hoverColor: Colors.white,
        trailing: model.code == _controller.selectedRegion?.code
            ? Padding(
                padding: EdgeInsets.only(left: 15.pt, right: 15.pt),
                child: FLImage.asset(
                  Res.commonChecked,
                  width: 18.pt,
                  height: 14.pt,
                  color: R.color.primaryLightColor,
                ),
              )
            : SizedBox(),
        contentPadding: EdgeInsets.symmetric(vertical: 1.pt),
        shape: Border(bottom: BorderSide(color: Colors.red, width: 0.5.pt)),
        title: Text(
          model.name,
          style: _itemStyle,
        ),
        onTap: () => _controller.selectedRegion = model,
      ),
    );
  }
}
