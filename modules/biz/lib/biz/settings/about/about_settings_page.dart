import 'package:biz/biz.dart';
import 'package:biz/biz/settings/about/bloc/about_settings_bloc.dart';
import 'package:biz/biz/settings/model/setting_item.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';

@FRoute(desc: '关于设置页面', url: R_SETTINGS_ABOUT)
class AboutSettingsPage extends StatelessWidget {
  final _bloc = AboutSettingsBloc();

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<AboutSettingsBloc, AboutSettingsState>(
      bloc: _bloc,
      builder: (_, state) => _page(state),
    );
  }

  Widget _page(AboutSettingsState state) {
    return Scaffold(
      appBar: CommonAppBar(title: LocaleStrings.instance.about),
      body: _body(),
    );
  }

  Widget _body() {
    return Stack(
      clipBehavior: Clip.none,
      children: [_settingList(), _copyright()],
    );
  }

  Widget _copyright() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Padding(
        padding: EdgeInsets.only(bottom: 24.pt),
        child: Center(
          child: EasterEgg(
            onTriggered: () => _bloc.add(AboutSettingsEvent.easterEgg),
            child: Text(
              LocaleStrings.instance.copyright,
              style: TextStyle(fontSize: 14.sp, color: colorSubtitle),
            ),
          ),
        ),
      ),
    );
  }

  Widget _logo() {
    return FLImage.asset(
      Res.commonLogo,
      width: 120.pt,
      height: 120.pt,
      fit: BoxFit.contain,
    );
  }

  Widget _version() {
    String versionName = _bloc.state.versionName ?? "";
    String versionCode = "";
    if ((_bloc.state.buildNumber?.isNotEmpty ?? false) &&
        _bloc.state.buildNumber!.length >= 3) {
      String code = _bloc.state.buildNumber!;
      versionCode = "(${code.characters.takeLast(3)})";
    }

    return Center(
      child: Text(
          "${LocaleStrings.instance.currentVersion} v$versionName$versionCode",
          style: TextStyle(fontSize: 14.sp, color: primaryColorTextDetail)),
    );
  }

  Widget? _debugInfo() {
    String info = "";

    if (_bloc.state.environment?.isNotEmpty ?? false) {
      // 以下是调试信息
      info += "Env: ${_bloc.state.environment}";
    }

    if (info.isEmpty) {
      return null;
    }
    return Center(
      child: Text(info,
          style: TextStyle(fontSize: 14.sp, color: primaryColorTextDetail)),
    );
  }

  Widget _settingList() {
    final settings = _bloc.state.settings;
    if (settings == null || settings.isEmpty) {
      return GlobalWidgets.pageLoading();
    }
    return ListView(
      children: _settings(),
    );
  }

  List<Widget> _settings() {
    var rest = <Widget>[];

    rest.add(SizedBox(height: 55.pt));

    rest.add(_logo());
    rest.add(SizedBox(height: 10.pt));
    rest.add(_version());
    final info = _debugInfo();
    if (info != null) {
      rest.add(info);
    }
    rest.add(SizedBox(height: 40.pt));
    rest.addAll(_bloc.state.settings!
        .map((e) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [_settingItem(e), _separator()],
            ))
        .toList());

    return rest;
  }

  Widget _settingItem(SettingItem item) {
    return HfListTile(
      onTap:
          item.event is AboutSettingsEvent ? () => _bloc.add(item.event) : null,
      contentPadding: EdgeInsets.symmetric(horizontal: 24.pt, vertical: 23.pt),
      leading: FLImage.asset(item.icon!, width: 24.pt, height: 24.pt),
      leadingSpacing: 16.pt,
      title: Text(
        item.label,
        style: TextStyle(color: R.color.textColor1, fontSize: 16.pt),
      ),
      trailing: FLImage.asset(
        Res.commonArrowRight,
        width: 16.pt,
        height: 16.pt,
        matchTextDirection: true,
      ),
      trailingSpacing: 10.pt,
    );
  }

  Widget _separator() {
    return Container(
      constraints: BoxConstraints.expand(height: 0.5),
      padding: EdgeInsetsDirectional.only(start: 25.pt),
      child: Container(color: colorDivider),
    );
  }
}
