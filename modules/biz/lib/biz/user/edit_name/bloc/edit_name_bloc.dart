import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/settings/edit_profile/user_info_improve_manager.dart';
import 'package:biz/common/widgets/controller/text_field_controller.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:meta/meta.dart';
import 'package:service/common/statistics/account_info_statistics.g.dart';
import 'package:service/common/statistics/relationship_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/modules/user/const/enums.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';
import 'package:service/utils/text_util.dart';

part 'edit_name_event.dart';

part 'edit_name_state.dart';

class EditNameBloc extends BaseBloc<EditNameEvent, EditNameState> {
  EditNameBloc() : super(EditNameState());

  String get type => getArgument<String>(P_TYPE) ?? NameTypeNick;

  String get _uid => getArgument<String>(P_UID) ?? "";

  String get page => getArgument<String>(P_PAGE) ?? "";

  CustomizedTextEditController nickNameController =
      CustomizedTextEditController();
  FocusNode nickNameFocusNode = FocusNode();

  ///记录输入结果
  String lastInput = "";

  @override
  void initBloc() {
    super.initBloc();
    initData();
  }

  bool get isSelf => _uid == accountService.currentUid();

  Future<void> initData() async {
    _defaultName().then(_setDefaultName);
  }

  Future<String?> _defaultName() async {
    switch (type) {
      case NameTypeNick:
        return (await userService
                .getUserInfo(accountService.getAccountInfo()?.uid ?? ""))
            ?.displayedName;

      case NameTypeAlias:
        return socialService.getAlias(targetUid: _uid) ??
            (await userService.getUserInfo(_uid))?.displayedName;

      case NameTypeBottle:
        return (await userService.getCurrentUserInfo())?.bottleInfo?.name ?? "";
    }
    return null;
  }

  void _setDefaultName(String? name) {
    nickNameController.addListener(() {
      if (lastInput != nickNameController.completeText) {
        lastInput = nickNameController.completeText;
        if (lastInput.length > state.maxLength) {
          String text = lastInput;
          while (text.length > state.maxLength) {
            text = text.characters.skipLast(1).string;
          }
          nickNameController.text = text;
          nickNameController.selection = TextSelection.fromPosition(
              TextPosition(offset: nickNameController.text.length));
        }
        emit(state.clone());
      }
    });

    if (name?.isNotEmpty ?? false) {
      nickNameController.text = name ?? "";
    }
  }

  @override
  Stream<EditNameState> mapEventToState(EditNameEvent event) async* {
    switch (event.runtimeType) {
      case EditNameEventChange:
        break;
      case EditNameEventCommit:
        yield await _commit(event as EditNameEventCommit);
        break;
      case EditNameEventSkip:
        break;
    }
  }

  Future<EditNameState> _commit(EditNameEventCommit eventCommit) async {
    var text = nickNameController.text.trim();
    if (text.isEmpty && type == NameTypeNick) return state;

    final tooShort = text.length < 2;
    final invalid = text.contains(r'\') || !isValidString(text) || tooShort;

    if (invalid) {
      showAlertDialog(
        content: tooShort ? LocaleStrings.instance.theNicknameYouEnteredIsTooShort : LocaleStrings.instance.containInvaildCharacters,
        confirmTextColor: R.color.primaryDeepColor,
        confirmText: LocaleStrings.instance.ok,
        showCancel: false,
      );
      return state;
    }

    switch (type) {
      case NameTypeNick:
        _commitNick(text);
        break;
      case NameTypeAlias:
        showLoading();
        var result = await _commitAlias(text);
        if (result == true) _reportSetRemark(_uid, text);
        routerUtil.pop(params: {"result": result});
        hideLoading();
        break;
      case NameTypeBottle:
        _commitBottle(text);
        break;
    }

    return state;
  }

  Future<bool> _commitAlias(String text) async {
    if (_uid?.isEmpty ?? true) return false;
    return await socialService.setAlias(
        targetUid: _uid!, alias: text.isEmpty ? null : text);
  }

  void _commitNick(String text) async {
    if (UserInfoImproveManager.instance().hasUpdateNickToday()) {
      return;
    }

    showAlertDialog(
      context: getContext()!,
      content: LocaleStrings.instance.editNickNameAlert,
      confirmText: LocaleStrings.instance.ok,
      cancelText: LocaleStrings.instance.cancel,
      onConfirm: () async {
        showLoading();
        AccountInfoStatistics.reportAccountNameSubmit();
        var resp = await userService.updateUserInfo(nickName: text);
        hideLoading();

        if (!resp.isSuccess) {
          toast(resp.msg ?? "");
        } else {
          UserInfoImproveManager.instance().updateNickTime();
        }
      },
    );
  }

  void _commitBottle(String text) async {
    if (bottleService.hasUpdateNickToday()) {
      return;
    }

    showAlertDialog(
      context: getContext()!,
      content: LocaleStrings.instance.editNickNameAlert,
      confirmText: LocaleStrings.instance.ok,
      cancelText: LocaleStrings.instance.cancel,
      onConfirm: () async {
        showLoading();
        var resp = await bottleService.updateInfo(name: text);
        hideLoading();

        if (!(resp?.isSuccess ?? false)) {
          toast(resp?.msg ?? LocaleStrings.instance.failedToSubmit);
          return;
        } else {
          routerUtil.pop(params: {"result": true});
        }
      },
    );
  }

  ///上报打点：给某个用户设置备注名成功时
  Future<void> _reportSetRemark(String? targetUid, String text) async {
    if (targetUid?.isEmpty ?? true) return;
    final targetUser = await userService.getUserInfo(targetUid!);
    RelationshipStatistics.reportRemarkUserSucc(
      toUid: targetUser?.uid,
      toGender: targetUser?.sexStr,
      page: page,
      isFollowed: targetUser?.isFollowed.toReportString(),
      isFollowing: targetUser?.isFollowing.toReportString(),
      content: text,
    );
  }
}
