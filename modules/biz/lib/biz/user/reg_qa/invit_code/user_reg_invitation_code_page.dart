import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/utils/locale_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/service.dart';

import 'user_reg_invitation_code_logic.dart';

@FRoute(url: R_USER_REG_INVITATION_CODE, desc: "用户注册邀请码页面")
class UserRegInvitationCodePage extends StatelessWidget {
  UserRegInvitationCodePage({Key? key}) : super(key: key);

  final UserRegInvitationCodeLogic _logic =
      Get.put<UserRegInvitationCodeLogic>(UserRegInvitationCodeLogic());

  @override
  Widget build(BuildContext context) {
    return GetBuilder<UserRegInvitationCodeLogic>(
      assignId: true,
      builder: (logic) {
        return Scaffold(
          backgroundColor: Color(0xFF404040),
          appBar: CommonAppBar(
            height: 45.pt,
            backgroundColor: Color(0xFF404040),
            title: _title(),
            titleColor: Colors.white,
            backIconColor: Colors.white,
            elevation: 0.0,
            systemOverlayStyle: SystemUiOverlayStyle.light,
          ),
          body: _buildBody(context),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_logic.isLoading) {
      return GlobalWidgets.pageLoading();
    }
    if (_logic.isEmpty) {
      return EmptyWidget();
    }
    if (_logic.errMsg != null) {
      return GlobalWidgets.netFailedWidget(
          btnAction: () => _logic.loadData(refresh: true));
    }
    return _buildContent(context);
  }

  Widget _buildBg() {
    return Container(color: Color(0xFFE6E4E0));
  }

  Widget _buildContent(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Positioned(left: 0, right: 0, top: -1, bottom: 0, child: _buildBg()),
        Column(
          children: [
            _buildStamp(context),
            SizedBox(height: 24.pt),
            _buildCopyCode(context),
            SizedBox(height: 26.pt),
            _buildScreenShot(context),
          ],
        ),
      ],
    );
  }

  String _title() {
    return "${LocaleStrings.instance.regMyInvitationCode} ${_logic.invitationCodeSum}";
  }

  Widget _buildStamp(BuildContext context) {
    return RepaintBoundary(
      key: _logic.stamp2ShareKey,
      child: Stack(
        children: [
          FLImage.asset(
            Res.profileBgInvitationScreenshot,
            width: 1.w,
            height: 380.pt,
          ),
          Column(
            children: [
              Container(
                margin: EdgeInsetsDirectional.only(
                    start: 16.pt, end: 16.pt, top: 18.pt, bottom: 22.pt),
                width: 343.pt,
                height: 293.pt,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Res.profileBgInvitationStamp),
                  ),
                ),
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    PositionedDirectional(
                      start: 39.pt,
                      top: 15.pt,
                      child: _buildFromToText(LocaleStrings.instance.from),
                    ),
                    PositionedDirectional(
                      end: 39.pt,
                      top: 15.pt,
                      child: _buildFromToText(LocaleStrings.instance.to),
                    ),
                    PositionedDirectional(
                        top: 20.pt,
                        child: FLImage.asset(
                          Res.profileInvitationFly,
                          width: 26.pt,
                          height: 15.pt,
                        )),
                    PositionedDirectional(
                      top: 37.pt,
                      child: _anyFlyWinker(),
                    ),
                    PositionedDirectional(
                      top: 62.pt,
                      child: _buildPrivateFlights(),
                    ),
                    PositionedDirectional(
                      top: 127.pt,
                      width: 105.pt,
                      height: 105.pt,
                      child: _buildQRCode(),
                    ),
                    PositionedDirectional(
                      bottom: 11.pt,
                      child: _buildCode(),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.only(start: 13.pt, end: 13.pt),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FLImage.asset(
                        Res.commonLogo,
                        width: 26.pt,
                      ),
                      SizedBox(width: 11.pt),
                      Flexible(
                        child: Text(
                          '$APP_NAME - ${LocaleStrings.instance.loginSlogan}',
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          style: TextStyle(
                              color: Color(0xFF868483),
                              fontSize: 16.pt,
                              fontWeight: FontWeightExt.medium),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCode() {
    return Column(
      children: [
        Text(
          LocaleStrings.instance.invitationCode,
          style: TextStyle(
              color: Color(0xFF878484),
              fontSize: 13.pt,
              fontStyle: FontStyle.italic,
              fontWeight: FontWeightExt.medium),
        ),
        Text(
          _logic.invitationCode.value!.code,
          style: TextStyle(
              fontFamily: 'Supply',
              color: R.color.textColor1,
              fontSize: 21.pt,
              fontWeight: FontWeightExt.medium),
        ),
      ],
    );
  }

  Widget _buildQRCode() {
    return Stack(
      alignment: Alignment.center,
      children: [
        FLImage.asset(
          Res.profileInvitationQrCode,
          width: 105.pt,
          height: 105.pt,
        ),
        if (_logic.userInfo != null)
          UserAvatar(
            url: _logic.userInfo?.avatar ?? "",
            avatarCode: _logic.userInfo?.avatarCode,
            borderColor: Colors.white,
            borderWidth: 2.pt,
            size: 26.pt,
            userId: _logic.userInfo?.uid,
          ),
      ],
    );
  }

  Widget _buildPrivateFlights() {
    return Container(
      padding: EdgeInsetsDirectional.only(
          start: 8.pt, end: 8.pt, top: 4.pt, bottom: 4.pt),
      decoration: BoxDecoration(
          color: Color(0xFFBBAE94).withOpacity(0.2),
          borderRadius: BorderRadius.circular(9.pt)),
      child: Text(
        LocaleStrings.instance.privateFlights,
        style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Color(0xFF878484),
            fontWeight: FontWeightExt.medium,
            fontSize: 13.pt),
      ),
    );
  }

  Widget _anyFlyWinker() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        FLImage.asset(
          _getLanIconAnywhere(),
          height: 28.pt,
          width: 100.pt,
        ),
        SizedBox(width: 4.pt),
        FLImage.asset(
          Res.profileInvitationFlyLine,
          height: 8.pt,
          width: 124.pt,
        ),
        SizedBox(width: 9.pt),
        Padding(
          padding: EdgeInsetsDirectional.only(bottom: 2.pt),
          child: FLImage.asset(
            Res.profileInvitationLogo,
            height: 24.pt,
            width: 23.pt,
          ),
        ),
        SizedBox(width: 4.pt),
        FLImage.asset(
          Res.profileInvitationWinker,
          width: 59.pt,
          height: 24.pt,
        ),
      ],
    );
  }

  String _getLanIconAnywhere() {
    // TODO 优化图片的多语言方式
    var lan = LocaleUtil.getCurrentLocale();
    if (lan == 'id') {
      return Res.profileInvitationAnywhereId;
    }
    return Res.profileInvitationAnywhere;
  }

  Widget _buildFromToText(String text) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: 'Supply',
        color: Color(0xFF030403).withOpacity(0.3),
        fontSize: 16.pt,
        fontWeight: FontWeightExt.medium,
      ),
    );
  }

  Widget _buildCopyCode(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: _logic.copyClipboard,
      child: Container(
        height: 50.pt,
        width: 312.pt,
        decoration: BoxDecoration(
            color: Color(0xFFBBAE94),
            borderRadius: BorderRadius.circular(25.pt)),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FLImage.asset(
                Res.profileCopy,
                width: 21.pt,
                height: 21.pt,
              ),
              SizedBox(width: 5.pt),
              Text(
                LocaleStrings.instance.copyInvitationCode,
                style: TextStyle(
                    fontSize: 18.pt,
                    fontWeight: FontWeightExt.heavy,
                    color: Colors.white),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScreenShot(BuildContext context) {
    return GestureDetector(
      onTap: () async => await _logic.saveGallerySaver(context),
      child: Container(
        height: 50.pt,
        width: 312.pt,
        decoration: BoxDecoration(
            color: Colors.black, borderRadius: BorderRadius.circular(25.pt)),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FLImage.asset(
                Res.profileIcScreenshot,
                width: 21.pt,
                height: 21.pt,
              ),
              SizedBox(width: 5.pt),
              Text(
                LocaleStrings.instance.ScreenshotShare,
                style: TextStyle(
                    fontSize: 18.pt,
                    fontWeight: FontWeightExt.heavy,
                    color: Colors.white),
              )
            ],
          ),
        ),
      ),
    );
  }
}
