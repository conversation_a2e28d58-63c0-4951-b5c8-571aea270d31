import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'package:service/service.dart';

import 'model/reg_list_model.dart';
import 'user_reg_qa_logic.dart';
import 'widget/reg_common_textfield_widget.dart';
import 'widget/reg_enter_home_widget.dart';
import 'widget/reg_invite_code_textfield_widget.dart';
import 'widget/reg_qa_widgets.dart';

@FRoute(url: R_USER_REG_QA, desc: "注册用户问答")
class UserRegQAPage extends StatefulWidget {
  UserRegQAPage({Key? key}) : super(key: key);

  @override
  State<UserRegQAPage> createState() => _UserRegQAPageState();
}

class _UserRegQAPageState extends State<UserRegQAPage> {
  final double titleHeight = 26.pt;

  final double titlePadding = 12.pt;

  late final UserRegQaLogic _logic;

  final Throttle _throttleTap = Throttle(duration: Duration(seconds: 3));

  String get _getXTag => 'user_reg_$hashCode';

  @override
  void initState() {
    super.initState();
    _logic = Get.put<UserRegQaLogic>(UserRegQaLogic(),tag: _getXTag);
  }

  @override
  Widget build(BuildContext context) {
    double statusBar = MediaQuery.of(context).padding.top;
    double titleArea = statusBar + titleHeight + titlePadding * 2;

    return WillPopScope(
      onWillPop: () {
        _logic.onBackTap();
        return Future.value(false);
      },
      child: AnnotatedRegion(
        value: SystemUiOverlayStyle.light,
        child: Scaffold(
          body: Stack(
            children: [
              Positioned.fill(
                child: _buildBg(),
              ),
              Positioned.fill(
                child: SafeArea(
                  top: false,
                  child: Column(
                    children: [
                      Expanded(
                        child: GetBuilder<UserRegQaLogic>(
                          init: _logic,
                          global: false,
                          assignId: true,
                          tag: _getXTag,
                          builder: (_) =>
                              _buildBody(context, _logic, titleArea),
                        ),
                      ),
                      Obx(() {
                        return SizedBox(height: _logic.listBottomPadding.value);
                      }),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTitle(statusBar + titlePadding),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Obx(
                    () => _buildBottomTextField(_logic.bottomViewType.value)),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBg() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF363636), Color(0xFF171717)]),
      ),
    );
  }

  Widget _buildBody(
      BuildContext context, UserRegQaLogic logic, double topPadding) {
    return Align(
      alignment: Alignment.topCenter,
      child: ListView.separated(
        physics: BouncingScrollPhysics(),
        controller: logic.listController,
        padding:
            EdgeInsetsDirectional.only(top: 10.pt + topPadding, bottom: 10.pt),
        reverse: true,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          RegQaListModel item = logic.list.reversed.toList()[index];
          if (item.role == RegListRole.self) {
            return RegUserListItemWidget(model: item);
          } else {
            return RegOfficialListItemWidget(
              model: item,
              onTapHandler: (RegQaOptions option) {
                _throttleTap.call(() {
                  logic.onTapOption(item, option);
                });
              },
              onTextAnimatedFinish: () {
                logic.onTextAnimationFinish(item);
              },
            );
          }
        },
        separatorBuilder: (context, index) =>
            GlobalWidgets.spacingVertical(13.pt),
        itemCount: logic.list.length,
      ),
    );
  }

  Widget _buildTitle(double top) {
    return ClipRRect(
      child: BackdropFilterWrapper(
        blurRadius: 4,
        child: Container(
          color: Color(0xFF343434).withOpacity(0.9),
          child: Padding(
            padding: EdgeInsets.only(top: top, bottom: titlePadding),
            child: FLImage.asset(Res.commonAppName, height: 26.pt),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomTextField(int bottomViewType) {
    switch (bottomViewType) {
      case UserRegQaLogic.bottomViewTypeInviteCode:
        return _buildInviteCodeTextField();
      case UserRegQaLogic.bottomViewTypeNickname:
        return _buildCommonTextField(LocaleStrings.instance.regNickInputHint);
      case UserRegQaLogic.bottomViewTypeEnterHome:
        return _buildEnterHomeBtn();
    }
    return SizedBox.shrink();
  }

  /// 输邀请码的底部输入框
  Widget _buildInviteCodeTextField() {
    return RegInviteCodeTextFieldWidget(logic: _logic);
  }

  Widget _buildCommonTextField(String hitText) {
    return RegCommonTextFieldWidget(
      logic: _logic,
      hintText: hitText,
    );
  }

  Widget _buildEnterHomeBtn() {
    return RegEnterHomeWidget(onTap: _logic.onEnterHome);
  }
}
