
import 'package:biz/biz.dart';
import 'package:biz/biz/moment/widgets/moment_item_header.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../family/family_detail/widgets/family_role_icon_widget.dart';

class UserTopView extends StatefulWidget {
  
  String? targetId;
  String? avatar;
  String? avatarCode;
  String? nickname;
  String? sex;
  int? age;
  String? from;
  bool? isVoiceAuth;
  bool? following;
  int? familyRole;

  UserTopView({
    Key? key, 
    this.targetId,
    this.avatar,
    this.avatarCode,
    this.nickname,
    this.sex,
    this.age,
    this.following,
    this.isVoiceAuth,
    this.from,
    this.familyRole,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _UserTopViewState();
  }
}

class _UserTopViewState extends State<UserTopView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _avatar(),
          GlobalWidgets.spacingHorizontal(4.pt),
          _nameAndGender(),
          GlobalWidgets.spacingHorizontal(10.pt),
          _actionButton(),
        ],
      ),
    );
  }

  Widget _avatar() {
    return Stack(
      children: [
        UserAvatar(
          url: widget.avatar ?? '',
          avatarCode: widget.avatarCode ?? '',
          size: 49.pt,
          hasFrame: true,
          userId: widget.targetId,
        ),
        if (widget.familyRole != null)
          Positioned(
            bottom: 8.pt,
            right: 8.pt,
            child: FamilyRoleIconWidget(role: widget.familyRole ?? 0),
          )
      ],
    );
  }

  Widget _nameAndGender() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _name(),
          11.hSpace,
          Row(
            children: [
              _gender(),
              7.5.wSpace,
              _familyRole()
            ],
          ),
        ],
      ),
    );
  }

  Widget _name() {
    if (widget.targetId == null) {
      return SizedBox.shrink();
    }
    return UserInfoLineWidget(uid: widget.targetId ?? "");
  }

  Widget _gender() {
    String gender = widget.sex == 'male' ? Res.profileIconMale : Res.profileIconFemale ;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.pt),
      height: 16.pt,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.pt),
          color: Color(widget.sex == 'male' ? 0xFF4591FF : 0xFFFB409B)
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FLImage.asset(gender, width: 10.pt, height: 10.pt),
          1.5.wSpace,
          Visibility(
              visible: widget.age != null && widget.age! > 0,
              child: Text(
                '${widget.age}',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 11.sp,
                    fontWeight: FontWeightExt.heavy
                ),
              )
          )
        ],
      ),
    );
  }

  Widget _familyRole() {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 7.5.pt),
      height: 16.pt,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.pt),
        color: _getFamilyRoleBackgroundColor()
      ),
      child:Text(_getFamilyRoleName(), style: TextStyle(fontSize: 10.sp, fontWeight: FontWeightExt.medium, color: _getFamilyRoleColor())),
    );
  }

  String _getFamilyRoleName() {
    if (widget.familyRole == 1) {
      return LocaleStrings.instance.patriarch;
    } else if (widget.familyRole == 2) {
      return LocaleStrings.instance.vicePatriarch;
    } else if (widget.familyRole == 3) {
      return LocaleStrings.instance.elders;
    } else {
      return LocaleStrings.instance.member;
    }
  }

  Color _getFamilyRoleColor() {
    if (widget.familyRole == 1) {
      return Color(0xFFFD9704);
    } else if (widget.familyRole == 2) {
      return Color(0xFF8901FF);
    } else if (widget.familyRole == 3) {
      return Color(0xFF0C82F0);
    } else {
      return Color(0xFFB2B2B2);
    }
  }

  Color _getFamilyRoleBackgroundColor() {
    if (widget.familyRole == 1) {
      return Color(0xFFFFF4E5);
    } else if (widget.familyRole == 2) {
      return Color(0xFFF3E5FF);
    } else if (widget.familyRole == 3) {
      return Color(0xFFE6F2FD);
    } else {
      return Color(0xFFF7F7F7);
    }
  }

  Widget _actionButton() {
    var selfUid = accountService.currentUidOfCache();
    if (selfUid == widget.targetId) {
      return SizedBox.shrink();
    }
    bool following = widget.following == true;
    return following ?  _chatButton() : _followButton();
  }

  Widget _followButton() {
    return GestureDetector(
      child: Container(
        width: 50.pt,
        height: 34.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: R.color.primaryColor,
          borderRadius: BorderRadius.circular(17.pt),
        ),
        child: FLImage.asset(Res.commonFollow, width: 24.pt, height: 24.pt, fit: BoxFit.cover),
      ),
      onTap: () {
        _checkAndOprFollow(true);
      },
    );
  }

  Widget _chatButton() {
    return GestureDetector(
      child: Container(
        height: 34.pt,
        width: 50.pt,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25.pt),
          border: Border.all(color: R.color.primaryLightColor, width: 0.5),
        ),
        child: FLImage.asset(Res.profileIconChatBlue,
            width: 24.pt, height: 24.pt, fit: BoxFit.cover),
      ),
      onTap: () {
        routerUtil.push(R_CHAT, params: {
          P_TARGET_ID: widget.targetId,
          P_CHECK_PRE_CHAT: false,
          P_CONVERSATION_TYPE: RCIMIWConversationType.private,
          P_STATISTIC_FROM: widget.from,
          P_STATISTIC_MATCH_TYPE: widget.from

        });
      },
    );
  }

  void _checkAndOprFollow(bool isFollow) {
    if (!isFollow) {
      showAlertDialog(
        context: context,
        content: LocaleStrings.instance.unFollowConfirm,
        confirmText: LocaleStrings.instance.unFollow,
        confirmTextColor: R.color.primaryLightColor,
        onConfirm: () async {
          _handleFollow(isFollow);
        },
      );
      return;
    }
    _handleFollow(isFollow);
  }

  void _handleFollow(bool isFollow) async {
    ///是否被封禁
    if ((await hasBeRestricted(context))) {
      return;
    }

    var resp = await socialService.oprFollow(
        targetUid: widget.targetId ?? '',
        isFollow: isFollow,
        page: widget.from!);
    if (resp.isSuccess) {
      if (isFollow) {
        widget.following = true;
        toast(LocaleStrings.instance.followSuccess);
        setState(() {

        });
      } else {
        widget.following = false;
      }

      return;
    }
    toast(resp.msg ?? "");
  }
}