import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/me/private_photo/event.dart';
import 'package:biz/biz/settings/edit_profile/user_info_improve_manager.dart';
import 'package:biz/biz/share/share_guid_dialog.dart';
import 'package:biz/biz/share/share_guid_handle.dart';
import 'package:biz/biz/user/user_info/widgets/user_info_bar/user_Info_scroll_controller.dart';
import 'package:biz/biz/user/utils/user_info_guide_manager.dart';
import 'package:biz/biz/user/utils/user_info_helper.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/biz/voice_verify/utils/voice_verify_review_manager.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/statistic/bloc/page_duration_statistic_mixin.dart';
import 'package:biz/global/statistic/moment_imp_report_mixin.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:flutter/material.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/profile_statistics.g.dart';
import 'package:service/common/statistics/userpage_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/modules/badge/const/events.dart';
import 'package:service/modules/badge/model/badge_model.dart';
import 'package:service/modules/evaluate/const/enums.dart';
import 'package:service/modules/im/model/refer_multimedia_msg_content.dart';
import 'package:service/modules/intimacy/model/intimacy_config_model.dart';
import 'package:service/modules/intimacy/model/intimacy_info_model.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/user/const/enums.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_home_data.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import '../../../report/report_success_dialog.dart';
import '../model/user_update_info.dart';

part 'user_info_event.dart';

part 'user_info_state.dart';

class UserInfoBloc extends PageVisibilityBloc<UserInfoEvent, UserInfoState>
    with PageTimingMixin, PageDurationStatisticMixin, ItemImpReport {
  UserInfoBloc() : super(UserInfoState());

  final _updateInfo = UserUpdateInfo();

  String? get getUId => getArgument<String>(P_UID) ?? accountService.getAccountInfo()?.uid;

  final ScrollController scrollController = ScrollController();

  String? get from => getArgument(P_STATISTIC_FROM);

  String? get _matchType => getArgument(P_STATISTIC_MATCH_TYPE);

  String? get _source => getArgument(P_SOURCE);

  int? get defaultTabIndex => getArgument<int>(P_INDEX) ?? 0;

  String? get fromPage => getArgument(P_PAGE);

  TabController? tabController;

  bool _sendGiftChat = false;

  @override
  void initBloc() {
    super.initBloc();
    _initData();
    _initRx();
    _startSendingStatus();
    _checkVoiceVerify();

    ///添加评分卡片条件记录：看过用户的主页
    if (getUId != accountService.getAccountInfo()?.uid) {
      evaluateService.addRecord(EvaluateType.userProfile);
      shareGuidHandle.addShareSp(ShowType.seeUserProfile);
    }

    if (getUId != null) {
      Get.put(UserInfoScrollController(uid: getUId!), tag: getUId);
    }
    scrollController.addListener(() {
      if (!isPageValid) return;

      final uid = getUId;
      if (uid == null) return;
      final isRegistered = Get.isRegistered<UserInfoScrollController>(tag: uid);
      if (isRegistered) {
        Get.find<UserInfoScrollController>(tag: uid).onOffsetChanged(pageUId: uid, pageOffset: scrollController.offset);
      }
    });
  }

  @override
  void dispose() {
    tabController?.dispose();
    if (getUId != null) {
      Get.put(UserInfoScrollController(uid: getUId!), tag: getUId);
    }
    super.dispose();
  }

  @override
  void onResume() {
    super.onResume();
    _onRefresh();
    rxUtil.send(PrivatePhotoEvent.refreshList, 1);
  }

  void _onRefresh() {
    /// 拉黑
    if (_updateInfo.block) {
      routerUtil.pop();
      return;
    }

    if (_updateInfo.followUpdate) {
      _giftIcon().then((value) {
        emit(state.clone()..showGiftIcon = value);
      });
    }
    _updateInfo.clean();
  }

  void _initRx() {
    listenRxEvent<String>(UserRelationEvent.unFollow, (uid) async {
      if (uid != getUId) return;
      if (isPageResume) {
        emit(state.clone()
          ..isFollowing = false
          ..showGiftIcon = await _giftIcon());
      } else {
        state.isFollowing = false;
        _updateInfo.followUpdate = true;
      }
    });

    listenRxEvent<String>(UserRelationEvent.follow, (uid) async {
      if (uid != getUId) return;
      if (isPageResume) {
        emit(state.clone()
          ..isFollowing = true
          ..showGiftIcon = await _giftIcon());
      } else {
        state.isFollowing = true;
        _updateInfo.followUpdate = true;
      }
    });

    listenRxEvent<String>(UserRelationEvent.block, (String targetId) {
      if (targetId == getUId) {
        if (isPageResume) {
          routerUtil.pop();
        } else {
          _updateInfo.block = true;
        }
      }
    });

    listenRxEvent<String>(UserBioEvent.close, (value) => _closeBio());

    listenRxEvent<List<WearingBadgeDetail>>(
      BadgeChangeEvent.update,
      (value) => _initUserInfo(fromServer: true, report: false),
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      /// 移除其他profile页
      routerUtil.removeAllExceptLast(R_USER_INFO);
    });
  }

  void _initData() async {
    if (getUId?.isEmpty ?? true) {
      return;
    }
    needCollect = true;

    UserInfoHomeData? userInfoHomeData = getArgument<UserInfoHomeData>("userInfoHomeData");

    if (userInfoHomeData != null) {
      emit(state.clone()..userInfoHomeData = userInfoHomeData);
    }

    await _initUserInfo(fromServer: true);
    await _getInitUserInfoHomeData(getUId!);

    IntimacyConfigItem? intimacyItem =
        await intimacyService.getSpaceConfig(state.userInfoHomeData?.intimacyInfo?.level ?? 0);
    if (state.userInfoHomeData?.intimacyInfo?.fuid.isNotEmpty == true &&
        intimacyItem?.spaceConfig.hasAnimation == true) {
      emit(state.clone()
        ..showIntimacyAnimation = true
        ..intimacyInfo = state.userInfoHomeData?.intimacyInfo);
    }
  }

  Future<UserInfoHomeData?> _getInitUserInfoHomeData(String uid) async {
    if (uid != getUId) return null;
    var value = await _getUserInfoHomeData(uid);

    ///用户主页展示上报
    UserpageStatistics.reportUserpageImp(
        from: from, autherId: value?.userInfo?.uid, autherGender: value?.userInfo?.sexStr);

    emit(state.clone()..userInfoHomeData = value);
    return value;
  }

  Future<void> _initUserInfo({bool? fromServer, bool report = true}) async {
    var userInfo = await userService.getUserInfo(getUId ?? "", fromServer: fromServer ?? false);

    // 永久封禁
    if (userInfo?.status == 2) {
      _showDeleteDialog(LocaleStrings.instance.thisUserHadBanned);
      return;
    }
    // 判断账号是否删除，删除则弹出弹窗
    if (userInfo?.status == 4) {
      _showDeleteDialog(LocaleStrings.instance.thisAccountHadDeleted);
      return;
    }

    if ((userInfo?.passionsTags?.length ?? 0) > 10) {
      userInfo?.passionsTags = userInfo.passionsTags?.sublist(0, 10);
    }

    if (report) {
      ProfileStatistics.reportUserProfileExport(
          type: from, autherId: userInfo?.uid, autherAge: '${userInfo?.age ?? 0}', autherGender: userInfo?.sexStr);
    }

    final isTmpBaned = (await userInfo?.isTmpBaned) ?? false;

    emit(state.clone()
      ..userInfo = userInfo
      ..cover = userInfo?.avatar
      ..hasFriendRequest = userInfo?.friendVerify == 1
      ..isTmpBaned = isTmpBaned);
    double bioProgress = 1.0;

    if (getUId == accountService.getAccountInfo()?.uid) {
      bioProgress = UserInfoImproveManager.instance().completeProgress(userInfo);
    }

    bool showAboutEnter = true;
    // if (userInfo?.passionsTags?.isNotEmpty == true ||
    //     userInfo?.intro?.isNotEmpty == true) {
    //   showAboutEnter = true;
    // }

    emit(state.clone()
      ..progress = bioProgress
      ..showAboutEnter = showAboutEnter
      ..showGiftIcon = await _giftIcon());
    // _seeOthersBio();
  }

  Future<UserInfoHomeData?> _getUserInfoHomeData(String uid) async {
    return await userService.getUserInfoHomeData(uid);
  }

  @override
  void onPageShow() {
    super.onPageShow();
    _startSendingStatus();
  }

  @override
  void onPageHide() {
    super.onPageHide();
  }

  void _startSendingStatus() {
    final targetId = getUId;
    if (targetId == null) return;
  }

  /// 第一次打开自己的主页时弹1次声音验证
  void _checkVoiceVerify() async {
    final isMe = getUId == accountService.getAccountInfo()?.uid;
    if (!isMe) return;

    final flag = await VoiceVerifyReviewManager.instance().checkMineProfileVerify();
    if (flag) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (getContext() == null) return;

        routerUtil.push(R_VOICE_VERIFY_POPUP);
        VoiceVerifyReviewManager.instance().setMineProfileVerify();
      });
    }
  }

  @override
  Stream<UserInfoState> mapEventToState(UserInfoEvent event) async* {
    switch (event.runtimeType) {
      case UserInfoEventBlock:
        yield await _block(event as UserInfoEventBlock);
        break;

      case UserInfoEventReport:
        yield await _report();
        break;

      case UserSetRemarks:
        yield await _setRemarks();
        break;

      case UserMore:
        _more();
        break;

      case UserGoToChat:
        yield await _gotToChat(event as UserGoToChat);
        break;

      case UnFollowEvent:
        _unFollow();
        break;
      case UserInfoIntimacyAnimationEvent:
        UserInfoIntimacyAnimationEvent mEvent = event as UserInfoIntimacyAnimationEvent;
        emit(state.clone()..showIntimacyAnimation = mEvent.showIntimacyAnimation);
        break;
    }
  }

  Future<UserInfoState> _block(UserInfoEventBlock event) async {
    blockWithConfirm(getContext()!, uid: getUId ?? "", cancel: event.cancel, blockCallback: (result) {
      if (result) {
        toast(event.cancel ? LocaleStrings.instance.unblockSuccessfully : LocaleStrings.instance.blockSuccessfully);
        _initData();
      }
    }, page: R_USER_INFO);
    _reportUserPageBlock(event.cancel);
    return state;
  }

  Future<UserInfoState> _report() async {
    routerUtil.push(R_SETTINGS_REPORT, params: {
      P_DATA: UserReport(getUId ?? "", resultCallback: (bool result, ReportTemplate template) {
        if (!result) return;
        showReportSuccessDialog(template.targetUid,
            page: R_USER_INFO,
            type: template.statisticType,
            toContent: template.commentId ?? template.itemId ?? "",
            cancelCallback: _removePage);
      }),
      P_STATISTIC_FROM: R_USER_INFO,
    });

    _reportUserPageReport();
    return state;
  }

  Future<UserInfoState> _setRemarks() async {
    _reportUserPageRemark();
    routerUtil
        .push(R_USER_EDIT_NAME, params: {P_TYPE: NameTypeAlias, P_UID: getUId, P_PAGE: R_USER_INFO}).then((value) {
      if (value?["result"] == true) {
        emit(state.clone());
      }
    });
    return state;
  }

  Future<void> _more() async {
    final context = getContext();
    if (context == null) return;

    var userInfo = await userService.getUserInfo(getUId ?? "");
    UserpageStatistics.reportUserpageMore(from: from, autherGender: userInfo?.sexStr, autherId: getUId);
    bool isOfficial = userInfo?.isOfficial ?? false;
    List<SheetAction> actions = [];
    if (!isOfficial) {
      actions = [
        SheetAction(
            title: LocaleStrings.instance.reportUserTitle,
            onAction: () {
              add(UserInfoEventReport());
            }),
      ];
    }
    actions.addAll([
      SheetAction(
          title:
              (userInfo?.isBlocked ?? false) ? LocaleStrings.instance.unblock : LocaleStrings.instance.blockUserTitle,
          onAction: () {
            add(UserInfoEventBlock(userInfo?.isBlocked ?? false));
          }),
      if (state.isFollowing == true)
        SheetAction(
            title: LocaleStrings.instance.unFollow2,
            onAction: () {
              add(UnFollowEvent());
            }),
      SheetAction(
          title: LocaleStrings.instance.setRemarks,
          onAction: () {
            add(UserSetRemarks());
          })
    ]);
    showBottomSheetDialog(context: context, actions: actions);
  }

  // bool get needGiftToChat =>
  //     state.userInfoHomeData?.socialDetail?.needGiftToChat ?? true;
  bool get needGiftToChat => false;

  Future<UserInfoState> _gotToChat(UserGoToChat event) async {
    var targetId = getUId;
    if (targetId?.isEmpty ?? true) return state;
    if (state.userInfo == null) return state;

    var from = this.from ?? StatisticPageFrom.userPageChat;

    /// 用户主页，点击chat按钮上报
    UserpageStatistics.reportUserpageChat(
        from: from, autherId: targetId, autherGender: state.userInfoHomeData?.userInfo?.sexStr);

    var refData;
    if (event.clickFrom == clickFromUserState) {
      refData = RefMultiMediaMsgContent.froUserPageUserState(userInfo: state.userInfo!);
    }
    refData ??= getArgument(P_CHAT_REFER_DATA);

    routerUtil.push(R_CHAT, params: {
      P_TARGET_ID: getUId,
      P_CONVERSATION_TYPE: RCIMIWConversationType.private,
      P_STATISTIC_FROM: from,
      P_USER_INFO: state.userInfo!,
      // P_UNLOCK_GIFT: needSendGiftUnlock ? "1" : "0",
      P_CHECK_PRE_CHAT: needGiftToChat,
      P_STATISTIC_MATCH_TYPE: _matchType,
      P_CHAT_REFER_DATA: refData,
      P_SOURCE: _source,
    });

    return state;
  }

  Future<bool> _giftIcon() async {
    return needGiftToChat;

    /// 非匹配场景下点击聊天需送礼
    // if (getUId?.isEmpty ?? true) return false;
    // if (getUId == accountService.getAccountInfo()?.uid) return false;
    // if (state.userInfo?.isOfficial ?? false) return false;
    //
    // /// Quiz每日推荐的用户
    // /// 匹配场景
    // var list = [
    //   StatisticPageFrom.constellation,
    // ]..addAll(StatisticPageFrom.mathList);
    //
    // if ((_from?.isNotEmpty ?? false) && list.contains(_from)) return false;
    //
    // /// 互关状态
    // final relation = await userService.getRelation(targetUid: getUId ?? "");
    // if (relation.isFriend) return false;
    //
    // final chat = await imChatService.getChat(getUId!);
    //
    // if (chat?.hasRealMsg ?? false) return false;
    //
    // return true;
  }

  void _showDeleteDialog(String title) {
    var context = FLRouter.routeObserver.getLastContext();
    showAlertDialog(
        context: context,
        content: title,
        confirmTextColor: R.color.primaryColor,
        confirmText: LocaleStrings.instance.iKnow,
        showCancel: false,
        barrierDismissible: false,
        allowBack: false,
        onConfirm: () {
          Navigator.of(context).pop();
        });
  }

  void _closeBio() {
    if (!_sendGiftChat) return;
    _sendGiftChat = false;
    UserInfoGuideManager.instance().closeBioInGiftChat();
  }

  @override
  String get pageDurationKey => "user_page";

  // void setTabController(TabController tabController) {
  //   tabController = tabController;
  // }

  /// 用户主页时长上报
  @override
  void endTimingAndReport() async {
    var userInfo = await userService.getUserInfo(getUId ?? "");
    stopTiming(key: pageDurationKey);
    UserpageStatistics.reportUserpageDuration(
        from: from, autherId: getUId, autherGender: userInfo?.sexStr, duration: getOnTime(key: pageDurationKey));

    userService.reportUserPagePV(fuid: getUId!, stayTime: getOnTime(key: pageDurationKey));

    removeTimer(key: pageDurationKey);
  }

  /// 更多弹窗点击举报
  void _reportUserPageReport() async {
    var userInfo = await userService.getUserInfo(getUId ?? "");
    UserpageStatistics.reportUserpageMoreReport(from: from, autherId: getUId, autherGender: userInfo?.sexStr);
  }

  /// 更多block上报
  void _reportUserPageBlock(bool cancel) async {
    if (!cancel) {
      var userInfo = await userService.getUserInfo(getUId ?? "");
      UserpageStatistics.reportUserpageMoreBlock(from: from, autherId: getUId, autherGender: userInfo?.sexStr);
    } else {
      _reportUserPageUnBlock();
    }
  }

  /// 取消block 上报
  void _reportUserPageUnBlock() async {
    var userInfo = await userService.getUserInfo(getUId ?? "");
    UserpageStatistics.reportUserpageMoreUnblock(from: from, autherId: getUId, autherGender: userInfo?.sexStr);
  }

  /// 点击remark 上报
  void _reportUserPageRemark() async {
    var userInfo = await userService.getUserInfo(getUId ?? "");
    UserpageStatistics.reportUserpageMoreRemark(from: from, autherId: getUId, autherGender: userInfo?.sexStr);
  }

  /// 点击展开上报
  void reportUserPageExpand() async {
    var userInfo = await userService.getUserInfo(getUId ?? "");
    UserpageStatistics.reportUserpageBioMore(from: from, autherId: getUId, autherGender: userInfo?.sexStr);
  }

  void _removePage() {
    final context = getContext();
    if (context == null) return;
    routerUtil.remove((route) => route == ModalRoute.of(context));
  }

  void _unFollow() {
    showAlertDialog(
      context: getContext()!,
      content: LocaleStrings.instance.unFollowConfirm,
      confirmText: LocaleStrings.instance.unFollow,
      confirmTextColor: R.color.primaryLightColor,
      onConfirm: () async {
        _handleFollow(false, getContext()!);
      },
    );
  }

  void _handleFollow(bool isFollow, BuildContext context) async {
    ///是否被封禁
    if ((await hasBeRestricted(context))) {
      return;
    }

    var resp = await socialService.oprFollow(targetUid: getUId!, isFollow: isFollow, page: R_USER_INFO);
    if (resp.isSuccess) {
      if (isFollow) {
        toast(LocaleStrings.instance.followSuccess);
      }
      return;
    }
    toast(resp.msg ?? "");
  }
}
