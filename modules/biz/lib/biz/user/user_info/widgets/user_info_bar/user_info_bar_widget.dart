import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/user/user_info/widgets/user_info_bar/user_Info_scroll_controller.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/app_bar/icon_btns.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/user_info.dart';

class UserInfoCustomBarWidget extends StatelessWidget {
  final UserInfo? userInfo;
  final double paddingTop;
  final double barHeight;
  final bool showMore;
  final bool shoeEdit;
  final VoidCallback? onMoreTap;
  final VoidCallback? onEditTap;

  UserInfoCustomBarWidget({
    required this.userInfo,
    required this.paddingTop,
    required this.barHeight,
    this.showMore = true,
    this.shoeEdit = false,
    this.onMoreTap,
    this.onEditTap,
  });

  @override
  Widget build(BuildContext context) {
    if (userInfo == null || (userInfo?.uid.isEmpty ?? true)) return const SizedBox.shrink();
    return GetBuilder<UserInfoScrollController>(
      tag: userInfo?.uid,
      init: UserInfoScrollController(uid: userInfo?.uid ?? ""),
      builder: (controller) {
        return Container(
          padding: EdgeInsets.only(top: paddingTop),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(min(max(controller.blurOpacity, 0), 1)),
            border: Border(
              bottom:
                  controller.blurOpacity > 0.7 ? BorderSide(color: Color(0xFFF5F7F9), width: 0.5.pt) : BorderSide.none,
            ),
          ),
          child: Row(
            children: [
              backBtn(
                iconColor: controller.blurOpacity >= 0.7 ? R.color.color20 : Colors.white,
                bgDecoration: controller.blurOpacity >= 0.7
                    ? null
                    : BoxDecoration(color: Color(0x66000000), shape: BoxShape.circle),
              ),
              Spacer(flex: 1),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 180.pt),
                child: Text(
                  userInfo?.nickname ?? "",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: controller.blurOpacity >= 0.7 ? Colors.black : Colors.white,
                    fontSize: 17.sp,
                    fontWeight: FontWeightExt.heavy,
                  ),
                ),
              ),
              Spacer(flex: 1),
              Row(
                children: [
                  Visibility(
                    visible: showMore,
                    child: moreBtn(
                      onTap: () => onMoreTap?.call(),
                      color: controller.blurOpacity >= 0.7 ? R.color.color20 : Colors.white,
                      bgDecoration: controller.blurOpacity >= 0.7
                          ? null
                          : BoxDecoration(color: Color(0x66000000), shape: BoxShape.circle),
                    ),
                  ),
                  Visibility(
                    visible: shoeEdit,
                    child: IconBtn(
                      icon: Res.profileEdit,
                      widgetSize: Size(33.pt, 33.pt),
                      iconSize: Size(15.pt, 15.pt),
                      margin: EdgeInsets.symmetric(horizontal: 6.5.pt),
                      onTap: () => onEditTap?.call(),
                      bgDecoration: controller.blurOpacity >= 0.7
                          ? null
                          : BoxDecoration(color: iconBtnBg, shape: BoxShape.circle),
                      iconColor: controller.blurOpacity >= 0.7 ? R.color.color20 : Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
