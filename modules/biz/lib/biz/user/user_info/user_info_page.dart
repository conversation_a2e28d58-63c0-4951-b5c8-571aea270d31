import 'package:biz/biz.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_animation_widget.dart';
import 'package:biz/biz/user/user_info/bloc/user_info_bloc.dart';
import 'package:biz/biz/user/user_info/widgets/chat_button.dart';
import 'package:biz/biz/user/user_info/widgets/user_info_bar/user_info_bar_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/follow_widget.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/handler/moment_more_statistic_handler.dart';
import 'package:service/common/statistics/handler/user_info_statistic_handler.dart';
import 'package:service/common/statistics/tab_me_statistics.g.dart';
import 'package:service/common/statistics/userpage_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/model/moment.dart';

import 'tab_widgets/user_moments_tab_widget.dart';
import 'tab_widgets/user_profile_tab_widget.dart';
import 'widgets/user_info_header_widget.dart';

@FRoute(url: R_USER_INFO, desc: "用户信息")
class UserInfoPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _UserInfoPageState();
  }
}

class _UserInfoPageState extends State<UserInfoPage> with SingleTickerProviderStateMixin {
  final _bloc = UserInfoBloc();

  final Throttle _click2Chat = Throttle(duration: Duration(milliseconds: 800));

  @override
  void initState() {
    super.initState();
    _bloc.tabController = TabController(initialIndex: 0, length: 1, vsync: this);
  }

  @override
  void dispose() {
    _click2Chat.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    return LifecycleBlocBuilder<UserInfoBloc, UserInfoState>(
      bloc: _bloc,
      builder: (context, state) {
        return _page(context);
      },
    );
  }

  Widget _page(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          backgroundColor: Colors.white,
          bottomNavigationBar: Visibility(
            visible: _bloc.getUId != accountService.getAccountInfo()?.uid,
            child: _bottomBar(),
          ),
          body: Stack(
            children: [
              _bodyContent(context),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _customBar(),
              ),
            ],
          ),
        ),
        Visibility(
          visible: _bloc.state.isTmpBaned ?? false,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => routerUtil.pop(context: context),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              alignment: Alignment.center,
              color: Colors.white.withOpacity(0.7),
              padding: EdgeInsets.symmetric(horizontal: 50.pt),
              child: Text(
                LocaleStrings.instance.userTmpBanned,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: R.color.color20,
                  fontSize: 15.sp,
                  fontWeight: FontWeightExt.medium,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _customBar() {
    final data = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single);
    return UserInfoCustomBarWidget(
      userInfo: _bloc.state.userInfo,
      paddingTop: data.padding.top,
      barHeight: 45.pt,
      showMore: accountService.getAccountInfo()?.uid != _bloc.getUId,
      shoeEdit: accountService.getAccountInfo()?.uid == _bloc.getUId,
      onEditTap: () {
        routerUtil.push(R_SETTINGS_ACCOUNT);
        TabMeStatistics.reportTabMeAvatarEdit(type: _bloc.state.progress == 1 ? "1" : "0");
      },
      onMoreTap: () {
        _bloc.add(UserMore());
      },
    );
  }

  Widget _bodyContent(BuildContext context) {
    return ExtendedNestedScrollView(
      controller: _bloc.scrollController,
      onlyOneScrollInBody: true,
      headerSliverBuilder: (context, boxIsScrolled) {
        return [
          SliverOverlapAbsorber(
            handle: ExtendedNestedScrollView.sliverOverlapAbsorberHandleFor(context),
            sliver: SliverToBoxAdapter(
              child: _headerView(),
            ),
          )
        ];
      },
      body: _content(),
    );
  }

  Widget _headerView() {
    String? uid = _bloc.getUId ?? '';
    if (uid.isEmpty || _bloc.state.userInfo == null) return SizedBox.shrink();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        UserInfoHeaderWidget(
          uid: uid,
          progress: _bloc.state.progress,
          userInfo: _bloc.state.userInfo,
          userInfoHomeData: _bloc.state.userInfoHomeData,
          showAboutEnter: _bloc.state.showAboutEnter,
          cover: _bloc.state.cover,
          gotoChat: () {
            _click2Chat.call(() => _bloc.add(UserGoToChat(clickFrom: clickFromUserState)));
          },
          userAvatarTab: (value) {
            if (uid == accountService.getAccountInfo()?.uid) {
              routerUtil.push(R_SETTINGS_ACCOUNT);
              TabMeStatistics.reportTabMeAvatarEdit(type: value == 1 ? "1" : "0");
            }
          },
          from: _bloc.getArgument(P_STATISTIC_FROM),
        ),
        Container(
          color: Colors.white,
          constraints: BoxConstraints(
            minHeight: 0,
            maxHeight: 2000,
          ),
          margin: EdgeInsets.zero,
          child: UserProfileTabWidget(
            userInfoHomeData: _bloc.state.userInfoHomeData,
            userInfo: _bloc.state.userInfo,
            from: _bloc.from,
            matchType: _bloc.getArgument(P_STATISTIC_MATCH_TYPE),
            showAboutEnter: _bloc.state.showAboutEnter,
          ),
        ),
        _momentTopView(),
      ],
    );
  }

  Widget _content() {
    final uid = _bloc.state.userInfo?.uid ?? '';
    if (uid.isEmpty) return const SizedBox.shrink();
    return ColoredBox(
        color: Colors.white,
        child: UserMomentsTabWidget(
          from: _bloc.from,
          isFromMeTab: _bloc.fromPage == R_USER_MEPAGE,
          targetUserId: uid,
        ));
  }

  Widget _momentTopView() {
    return Container(
      color: Colors.white,
      height: 50.pt,
      child: Row(
        children: [
          17.wSpace,
          FLImage.asset(Res.profileMineInfoMomentsIc, width: 21.pt, fit: BoxFit.cover),
          11.wSpace,
          Expanded(
            child: Text(
              LocaleStrings.instance.moment,
              softWrap: true,
              style: TextStyle(
                color: R.color.textColor1,
                fontSize: 14.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _bottomBtn(String label, String icon,
      {Function()? onTab, Color? backGroundColor, Gradient? gradient, Color? childColor, double? iconSize}) {
    return GeneralBtn.roundRadius(
      width: double.infinity,
      height: 44.pt,
      onTap: onTab,
      title: label,
      fontSize: 17.sp,
      iconAsset: icon,
      iconSize: iconSize ?? 18.pt,
      childColor: childColor,
      gradient: gradient,
      backgroundColor: backGroundColor,
      fontWeight: FontWeight.w600,
    );
  }

  Widget _followingBtn() {
    bool? isFollowing = _bloc.state.isFollowing;
    if (isFollowing == null || isFollowing) {
      return SizedBox();
    }
    return Expanded(
        flex: 1,
        child: FollowWidget(
          height: 50.pt,
          width: double.infinity,
          targetUid: _bloc.getUId ?? "",
          toastFollowSuccess: LocaleStrings.instance.followSuccess,
          initHasFollowed: _bloc.state.userInfoHomeData?.socialDetail?.isFollowing == 1,
          followed: Opacity(
              opacity: 0.3,
              child: _bottomBtn(
                LocaleStrings.instance.unFollow2,
                Res.commonFollow,
                backGroundColor: primaryColorBlue,
                iconSize: 29.pt,
              )),
          unfollow: _bottomBtn(
            LocaleStrings.instance.follow,
            Res.commonFollow,
            backGroundColor: R.color.btnNormalColor,
            childColor: R.color.primaryColor,
            iconSize: 29.pt,
          ),
          showUnfollowConfirm: true,
          onFollowClickChanged: _handleFollowStastics,
          relationPage: R_USER_INFO,
        ));
  }

  Widget _bottomBar() {
    if (_bloc.state.userInfoHomeData == null) {
      return SizedBox.shrink();
    }
    bool isOfficial = _bloc.state.userInfoHomeData?.userInfo?.isOfficial ?? false;
    return SafeArea(
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 12.pt, horizontal: 16.pt),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _followingBtn(),
            if (!isOfficial) SizedBox(width: 11.pt),
            if (!isOfficial)
              Expanded(
                flex: 1,
                child: ChatButton(
                  userInfo: _bloc.state.userInfo,
                  showGiftIcon: _bloc.state.showGiftIcon,
                  height: 50.pt,
                  iconSize: 29.pt,
                  gotoChat: () {
                    _click2Chat.call(() => _bloc.add(UserGoToChat()));
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _intimacyWidget() {
    if (_bloc.state.showIntimacyAnimation) {
      return Positioned.fill(
        child: IntimacyAnimationWidget(
          insets: EdgeInsets.only(top: 0),
          intimacyLevel: _bloc.state.intimacyInfo?.level ?? 0,
          targetUser: _bloc.state.userInfoHomeData?.userInfo,
          intimacyUser: _bloc.state.intimacyInfo?.userInfo,
          onComplete: () {
            _bloc.add(UserInfoIntimacyAnimationEvent(showIntimacyAnimation: false));
          },
        ),
      );
    }
    return SizedBox(width: 1.w, height: 0);
  }

  /// TODO ALLEN 用户页面动态事件处理
  void _handleStatistics(MomentStatisticsType type, Moment moment, {String? act}) {
    handleUserMomentStatistic(type, moment, _bloc.getArgument(P_STATISTIC_FROM), act);
  }

  /// 动态更多弹窗相关上报事件
  void _handleMoreStatistics(MoreStatisticsType type, Moment moment, ShareApps? shareType) {
    handleMomentMoreStatistic(type, moment, MomentStatisticsPage.mine, shareType);
  }

  /// 关注事件上报
  void _handleFollowStastics(bool isFollow) {
    /// true 关注 false 取消关注
    if (isFollow) {
      /// 用户主页，点击follow按钮上报
      UserpageStatistics.reportUserpageFollow(
          from: _bloc.getArgument(P_STATISTIC_FROM),
          autherId: _bloc.state.userInfoHomeData?.userInfo?.uid,
          autherGender: _bloc.state.userInfoHomeData?.userInfo?.sexStr);
    } else {
      /// 用户主页-更多弹窗，点击unfollow上报
      UserpageStatistics.reportUserpageMoreUnfollow(
          from: _bloc.getArgument(P_STATISTIC_FROM),
          autherId: _bloc.state.userInfoHomeData?.userInfo?.uid,
          autherGender: _bloc.state.userInfoHomeData?.userInfo?.sexStr);
    }
  }
}
