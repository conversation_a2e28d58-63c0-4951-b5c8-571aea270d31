import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_banner_widget.dart';
import 'package:biz/biz/live_room/component/gift_gallery/user_gift_gallery_widget.dart';
import 'package:biz/biz/me/private_photo/private_photo_setting_widget.dart';
import 'package:biz/biz/settings/edit_profile/edit_profile_controller.dart';
import 'package:biz/biz/settings/edit_profile/user_info_improve_manager.dart';
import 'package:biz/biz/user/title/title_container_widget.dart';
import 'package:biz/global/widgets/sex_age_chip.dart';
import 'package:biz/global/widgets/user_interest_tag_widget.dart';
import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/user/model/interest_tag.dart';
import 'package:service/modules/user/model/user_home_data.dart';
import 'package:service/modules/user/model/user_info.dart';

import '../../badge/widget/badge_display_widget.dart';
import 'user_profile_tab_controller.dart';

class UserProfileTabWidget extends StatefulWidget {
  final UserInfoHomeData? userInfoHomeData;
  final UserInfo? userInfo;
  final String? from;
  final String? matchType;
  final bool? showAboutEnter;

  const UserProfileTabWidget({
    super.key,
    this.userInfoHomeData,
    this.userInfo,
    this.from,
    this.showAboutEnter,
    this.matchType,
  });

  @override
  State<UserProfileTabWidget> createState() => _UserProfileTabWidgetState();
}

class _UserProfileTabWidgetState extends State<UserProfileTabWidget>
    with GetStateBuilderMixin<UserProfileTabWidget, UserProfileTabController> {
  @override
  void initState() {
    super.initState();
    getCtl.userInfo = widget.userInfo;
    getCtl.userInfoHomeData = widget.userInfoHomeData;
    getCtl.from = widget.from;
    getCtl.matchType = widget.matchType;
  }

  @override
  UserProfileTabController initCtl() {
    return UserProfileTabController();
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TitleContainerWidget(
          targetUserInfo: widget.userInfoHomeData?.userInfo,
          userTitles: widget.userInfoHomeData?.userTitles ?? [],
          needDivider: true,
        ),
        _badge(),
        _privatePhoto(),
        _giftGallery(),
        _intimacy(),
        _infoList(),
      ],
    );
  }

  Widget _badge() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        routerUtil.push(R_USER_BADGE,
            params: {P_UID: widget.userInfo?.uid, P_STATISTIC_FROM: StatisticPageFrom.userInfoPage});
      },
      child: BadgePanelWidget(
        badgeCount: widget.userInfo?.badgeCount ?? 0,
        badges: widget.userInfo?.badges,
      ),
    );
  }

  Widget _giftGallery() {
    if (widget.userInfoHomeData?.giftWall != null) {
      return GestureDetector(
        onTap: getCtl.onClickGiftGallery,
        child: UserGiftGalleryWidget(
          giftWall: widget.userInfoHomeData!.giftWall!,
        ),
      );
    }
    return SizedBox.shrink();
  }

  Widget _privatePhoto() {
    if (widget.userInfo?.uid == null) return SizedBox.shrink();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        PrivatePhotoSettingWidget(
          targetUid: widget.userInfo?.uid ?? '',
          assetPath: Res.profileMineInfoPrivatePhoto,
          title: LocaleStrings.instance.privatePhotos,
          from: widget.from,
          matchType: widget.matchType,
          bottomLine: Divider(
            color: R.color.textColor1.withOpacity(0.1),
            endIndent: 20.pt,
            indent: 20.pt,
            thickness: 0.5.pt,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _infoList() {
    var rest = <Widget>[];
    if (!getCtl.isSelf) {
      ///亲密值
      rest.add(SizedBox(height: 6.pt));
    }

    ///简介
    rest.add(_buildAboutJump());
    rest.add(_userIntro());

    if (!getCtl.isSelf && widget.userInfoHomeData?.socialDetail?.isFollowed == 1) {
      rest.add(_buildFollowing());
    }

    /// 性别年龄、国家地区,三方视角粉丝数
    if (widget.userInfo != null) {
      rest.add(Padding(
        padding: EdgeInsets.only(left: 17.pt, right: 17.pt, top: 8.pt, bottom: 12.pt),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Wrap(
              alignment: WrapAlignment.start,
              spacing: 11.pt,
              runSpacing: 11.pt,
              children: [
                SexAgeChip(sex: widget.userInfo!.sex, age: widget.userInfo!.age, fontSize: 12.sp, iconSize: 12.pt),
                _buildRegion(),
                _buildFollowersNum(),
              ].whereType<Widget>().toList(),
            ),
            if (widget.userInfo?.userTagList?.isNotEmpty == true)
              Padding(
                padding: EdgeInsetsDirectional.only(top: 11.pt),
                child: Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 11.pt,
                  runSpacing: 11.pt,
                  children: _buildInterestTags(widget.userInfo!.userTagList!).whereType<Widget>().toList(),
                ),
              ),
          ],
        ),
      ));

      rest.add(Divider(
        color: R.color.divideLineColor,
        endIndent: 17.pt,
        indent: 17.pt,
        thickness: 0.5.pt,
        height: 1.pt,
      ));
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rest,
    );
  }

  Widget _userIntro() {
    var child;
    if (getCtl.isSelf && (widget.userInfo?.intro?.isEmpty ?? true)) {
      child = GestureDetector(
        onTap: () {
          UserInfoImproveManager.instance().jumpToImprove(
            context: context,
            initStep: EditProfileStep.intro,
          );
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                LocaleStrings.instance.userIntroEmptyTip,
                style: TextStyle(fontSize: 13.pt, color: Color(0xFFB8C4CE)),
              ),
            ),
            SizedBox(width: 5.pt),
            FLImage.asset(
              Res.commonUserIntroEdit,
              width: 16.pt,
              height: 16.pt,
            )
          ],
        ),
      );
    } else if (widget.userInfo?.intro?.isEmpty ?? true) {
      // 他人
      return SizedBox.shrink();
    }

    if (child == null) {
      child = ExpandableText(
        widget.userInfo?.intro ?? "",
        style: TextStyle(
          color: Color(0xFF909090),
          fontSize: 12.sp,
        ),
        maxLines: 2,
        expandText: LocaleStrings.instance.more,
        linkStyle: TextStyle(
          color: Color(0xFF061029),
          fontSize: 13.sp,
          fontWeight: FontWeightExt.heavy,
        ),
        onLinkTap: getCtl.gotoUserAbout,
      );
    }

    return Padding(
      padding: EdgeInsets.only(top: 13.pt, left: 18.pt, right: 18.pt, bottom: 5.pt),
      child: child,
    );
  }

  Widget _buildAboutJump() {
    if (widget.showAboutEnter != true) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 5.pt, left: 18.pt, right: 18.pt, bottom: 5.pt),
      child: GestureDetector(
        onTap: getCtl.gotoUserAbout,
        child: Row(
          children: [
            FLImage.asset(Res.profileAboutMe, width: 21.pt, fit: BoxFit.cover),
            11.wSpace,
            Text(
              getCtl.isSelf
                  ? LocaleStrings.instance.aboutMe
                  : LocaleStrings.instance.aboutOtherInformation(widget.userInfo?.sex.hisOrHer ?? ''),
              style: TextStyle(
                color: Colors.black,
                fontSize: 13.pt,
                fontWeight: FontWeightExt.medium,
              ),
            ),
            SizedBox(width: 5.pt),
            FLImage.asset(
              Res.commonArrowForward,
              color: R.color.textColor1,
              width: 5.pt,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowing() {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 18.pt, end: 18.pt, top: 3.pt, bottom: 3.pt),
      child: Row(
        children: [
          FLImage.asset(
            Res.commonFollowed,
            width: 18.pt,
            height: 18.pt,
          ),
          SizedBox(width: 3.pt),
          Text(
            widget.userInfo?.sex == Sex.male ? LocaleStrings.instance.heFollowYou : LocaleStrings.instance.sheFollowYou,
            style: TextStyle(color: R.color.textBlueColor2, fontSize: 12.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildRegion() {
    String region;
    if (widget.userInfo?.showAddress?.isNotEmpty == true) {
      region = widget.userInfo!.showAddress!;
    } else {
      region = LocaleStrings.instance.Unknown;
    }
    return Container(
      decoration: BoxDecoration(color: Color(0xFFF5F7F9), borderRadius: BorderRadius.circular(23.pt)),
      padding: EdgeInsetsDirectional.only(start: 8.pt, end: 8.pt, top: 2.pt, bottom: 2.pt),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FLImage.asset(
            Res.profileIconLocation,
            width: 10.pt,
            height: 11.5.pt,
          ),
          SizedBox(width: 8.pt),
          Text(
            region,
            style: TextStyle(
              color: R.color.colorB2,
              fontSize: 13.sp,
              fontWeight: FontWeightExt.medium,
            ),
          )
        ],
      ),
    );
  }

  _buildFollowersNum() {
    if (getCtl.isSelf) {
      return null;
    }
    var num = widget.userInfoHomeData?.socialNun?.followedNum ?? 0;
    if (num >= 0) {
      return Container(
        padding: EdgeInsetsDirectional.only(start: 10.pt, end: 10.pt, top: 2.pt, bottom: 2.pt),
        decoration: BoxDecoration(
          color: Color(0xFFF5F7F9),
          borderRadius: BorderRadius.circular(23.pt),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FLImage.asset(Res.commonVisibleFriends, width: 10.pt, color: R.color.colorB2),
            SizedBox(width: 2.pt),
            Text(
              '$num${LocaleStrings.instance.followers}',
              style: TextStyle(
                color: R.color.colorB2,
                fontSize: 13.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ],
        ),
      );
    }
  }

  List<Widget> _buildInterestTags(List<UserTagModel> list) {
    var showList = list.sublist(0, min(list.length, 3));
    List<Widget> widgetList = [];
    widgetList.addAll(showList.map((e) => UserInterestTagWidget(item: e)));
    // if (list.length > 3) {
    //   widgetList.add(GestureDetector(
    //     onTap: getCtl.gotoUserAbout,
    //     child: UserInterestTagMoreWidget(),
    //   ));
    // }
    return widgetList;
  }

  Widget _intimacy() {
    if (widget.userInfoHomeData?.userInfo != null) {
      return Column(
        children: [
          Container(
              height: 65.pt + 8.pt,
              width: 1.w,
              color: Colors.white,
              margin: EdgeInsets.only(top: 8.pt, left: 15.pt, right: 15.pt),
              child: IntimacyBannerWidget(
                targetUserInfo: widget.userInfoHomeData!.userInfo!,
                intimacyInfo: widget.userInfoHomeData?.intimacyInfo,
                from: StatisticPageFrom.userInfoPage,
              )),
          SizedBox(height: 12.pt),
          Divider(
            color: R.color.divideLineColor,
            endIndent: 17.pt,
            indent: 17.pt,
            thickness: 0.5.pt,
            height: 1.pt,
          )
        ],
      );
    }

    return SizedBox.shrink();
  }
}
