

import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/component/gift_gallery/gift_gallery_list_dialog.dart';
import 'package:biz/biz/me/private_photo/event.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/tab_me_statistics.g.dart';
import 'package:service/modules/user/model/user_home_data.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/modules/user/model/user_private_photo_list.dart';

class UserProfileTabController extends AbsGetController with GetStatusCtlMix {
   UserInfoHomeData? userInfoHomeData;
   UserInfo? userInfo;
   String? from;
   String? matchType;
   List<UserPrivatePhotoItem> privatePhotoList = [];

   bool get isSelf => userInfo?.uid == accountService.getAccountInfo()?.uid;

   @override
  void stateInit() {
    super.stateInit();
    _initData();
  }

   void _initData() async {
     viewStatus = GetStatusView.content;
  }

   void gotoUserAbout() {
     if (userInfo != null) {
       TabMeStatistics.reportMeAboutmeClick();
       routerUtil.push(R_ABOUT, params: {P_USER_INFO: userInfo!, P_STATISTIC_FROM: from});
     }
   }

   void onClickGiftGallery() {
     showGiftGalleryListDialog(targetUid: userInfo!.uid, from: StatisticPageFrom.userInfoPage);
   }

}