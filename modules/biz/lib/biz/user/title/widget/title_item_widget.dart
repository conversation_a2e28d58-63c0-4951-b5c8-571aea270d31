
import 'package:biz/biz.dart';
import 'package:flutter/material.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/text_scroll.dart';
import 'package:service/modules/mall/model/user_title_model.dart';
import 'package:get/get.dart';

import 'title_item_controller.dart';

class TitleItemWidget extends StatefulWidget {
  final UserTitleModel? titleModel;
  final double? height;
  final String? uid;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;

  TitleItemWidget({
    Key? key,
    this.uid,
    this.titleModel,
    this.height,
    this.onTap,
    this.margin,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TitleItemWidgetState();
}

class _TitleItemWidgetState extends State<TitleItemWidget> {


  final TitleItemController _controller = TitleItemController();

  @override
  void initState() {
    super.initState();
    _controller.titleModel = widget.titleModel;
    _controller.uid = widget.uid;
    _controller.itemHeight = widget.height;
  }

  @override
  void didUpdateWidget(covariant TitleItemWidget oldWidget) {

    if (widget.titleModel != oldWidget.titleModel ||
        widget.uid != oldWidget.uid ||
        widget.height != oldWidget.height) {
      _controller.titleModel = widget.titleModel;
      _controller.uid = widget.uid;
      _controller.itemHeight = widget.height;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    _controller.layoutItemSize(screenWidth: screenWidth);

    return GetBindingBuilder(
      controller: _controller,
      child: GetBuilder(
          init: _controller,
          global: false,
          autoRemove: false,
          builder: (_) {
            bool canShow = _controller.canShow();
            if (!canShow) {
              return SizedBox.shrink();
            }

            return Material(
              color: Colors.transparent,
              child: GestureDetector(
                onTap: widget.onTap,
                child: Container(
                  width: _controller.itemSize.width,
                  height: _controller.itemSize.height,
                  margin: widget.margin,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      _bg(),
                      Positioned(
                          left: 0.3 * _controller.itemSize.width,
                          right: 0.12 * _controller.itemSize.width,
                          child: _name()
                      )
                    ],
                  ),
                ),
              )
            );
          }
      ),
    );
  }

  Widget _bg() {
    return CachedNetworkImage(
      imageUrl: _controller.titleModel?.svg ?? '',
      fit: BoxFit.fill,
      errorWidget: (_,__,___) => const SizedBox.shrink(),
    );
  }

  Widget _name() {
    return TextScroll(
      "${_controller.titleModel?.titleName}",
      mode: TextScrollMode.endless,
      velocity: Velocity(pixelsPerSecond: Offset(20, 0)),
      delayBefore: Duration(milliseconds: 1000),
      pauseBetween: Duration(milliseconds: 1000),
      numberOfReps: 2,
      style: TextStyle(
        color: Colors.white,
        fontSize: _controller.fontSize,
        fontWeight: FontWeightExt.black,
        fontStyle: FontStyle.italic
      ),
      textAlign: TextAlign.center,
      fadedBorder: false,
    );
  }

}