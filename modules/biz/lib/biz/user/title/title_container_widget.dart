import 'package:biz/biz.dart';
import 'package:biz/biz/user/title/widget/title_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/mall/model/user_title_model.dart';
import 'package:service/modules/user/model/user_info.dart';

import 'widget/title_list_dialog.dart';

class TitleContainerWidget extends StatefulWidget {
  final UserInfo? targetUserInfo;
  final List<UserTitleModel>? userTitles;
  final bool needDivider;

  TitleContainerWidget({
    Key? key,
    this.targetUserInfo,
    this.userTitles,
    this.needDivider = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TitleContainerWidgetState();
}

class _TitleContainerWidgetState extends State<TitleContainerWidget> {
  double _itemHeight = 0;

  double _getHeight() {
    if (widget.userTitles?.isEmpty == true) return 0;
    double screenWidth = MediaQuery.of(context).size.width;
    double itemWidth = ((screenWidth - 2 * 15.pt - 2 * 4.pt) / 3).floorToDouble();
    double itemHeight = itemWidth * 32.pt / 114.pt;
    _itemHeight = itemHeight;
    int row = (widget.userTitles!.length / 3).ceil();
    double height = row * itemHeight + (row - 1) * 4.pt + 3.pt;
    return height.ceilToDouble();
  }

  @override
  Widget build(BuildContext context) {
    double height = _getHeight();

    if (height <= 0) {
      return Padding(
        padding: EdgeInsets.only(top: 10.pt),
        child: Divider(
          color: R.color.divideLineColor,
          endIndent: 17.pt,
          indent: 17.pt,
          thickness: 0.5.pt,
          height: 1.pt,
        ),
      );
    }

    return Container(
      color: Colors.white,
      height: _getHeight() + 20.pt,
      padding: EdgeInsets.only(left: 15.pt, right: 15.pt, top: 8.pt),
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Text(
          //   LocaleStrings.instance.title,
          //   style: TextStyle(
          //     fontSize: 18.sp,
          //     color: Colors.white,
          //     fontWeight: FontWeightExt.heavy,
          //   ),
          // ),
          Wrap(
            spacing: 4.pt,
            runSpacing: 4.pt,
            children: _itemList(),
          ),
          SizedBox(height: 12.pt),
          Divider(
            color: R.color.divideLineColor,
            endIndent: 2.pt,
            indent: 2.pt,
            thickness: 0.5.pt,
            height: 1.pt,
          )
        ],
      ),
    );
  }

  List<Widget> _itemList() {
    List<Widget> list = [];
    for (var element in widget.userTitles!) {
      list.add(TitleItemWidget(
        titleModel: element,
        height: _itemHeight,
        onTap: () {
          showTitleListDialog(
              targetUserInfo: widget.targetUserInfo, gotList: widget.userTitles!, onTapTitleModel: element);
        },
      ));
    }
    return list;
  }
}
