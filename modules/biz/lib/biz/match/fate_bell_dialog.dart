import 'package:biz/biz/match/match_manager.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/match_statistics.g.dart';
import 'package:service/extension/string.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/modules/home/<USER>/home_wink_source_handler.dart';
import 'package:service/modules/match/model/match_model.dart';
import 'package:service/pb/local/pb_local_enum.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/global_audio_player.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

bool isShowingFateBell = false;

const _transitionDuration = 250;

Future<void> showFateBellDialog(MatchUser matchUser) async {
  isShowingFateBell = true;
  await showDialog(
    (context) => _FateBellDialogWidget(matchUser: matchUser),
    barrierDismissible: false,
    barrierColor: Colors.transparent,
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return SlideTransition(
        child: child,
        position: animation.drive(
          Tween(begin: Offset(0, -1), end: Offset.zero).chain(
            CurveTween(curve: Curves.ease),
          ),
        ),
      );
    },
    transitionDuration: Duration(milliseconds: _transitionDuration),
  );
  isShowingFateBell = false;
}

class _FateBellDialogWidget extends StatefulWidget {
  final MatchUser matchUser;

  const _FateBellDialogWidget({Key? key, required this.matchUser})
      : super(key: key);

  @override
  State<_FateBellDialogWidget> createState() => _FateBellDialogWidgetState();
}

class _FateBellDialogWidgetState extends State<_FateBellDialogWidget> {
  MatchUser get matchUser => widget.matchUser;

  @override
  void initState() {
    super.initState();
    MatchStatistics.reportFatebellPopImp(
        autherId: matchUser.uid,
        autherAge: '${matchUser.age}',
        autherGender: matchUser.sex,
        distance: matchUser.distance,
        type: matchUser.fromPush ? '0' : '1');
    /// 上报后端弹窗信息，方便其执行后续逻辑
    noticeService.popReport(
        "fatebell",
        uid: accountService.currentUid(),
        fuid: matchUser.uid
    );
  }

  void _closeAction() {
    Navigator.of(context).pop();
    isShowingFateBell = false;
    Future.delayed(Duration(milliseconds: _transitionDuration + 100))
        .then((value) => MatchManager.inst.popQueueDialog());
  }

  @override
  void dispose() {
    isShowingFateBell = false;
    super.dispose();
  }

  void _onChat(MatchUser model) {
    if (model.uid.isNotEmpty) {
      routerUtil.push(R_CHAT, params: {
        P_TARGET_ID: model.uid,
        P_CONVERSATION_TYPE: RCIMIWConversationType.private,
        P_IS_MATCH: '1',
        P_CHECK_PRE_CHAT: false,
        P_SOURCE: ChatSourceType.fateBell,
        P_STATISTIC_FROM: StatisticPageFrom.fateBell,
        P_STATISTIC_MATCH_TYPE: 'fate_bell_pop',
        // P_NEED_AUTO_SEND_MSG: "true",
        // P_RECOMMEND_TYPE: recommendType,
      });

      MatchStatistics.reportFatebellPopClick(
        autherId: model.uid,
        autherAge: '${model.age}',
        autherGender: model.sex,
        distance: model.distance,
        type: model.fromPush ? '0' : '1',
      );
    }
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        _closeAction();
        return Future.value(false);
      },
      child: Material(
        color: Colors.transparent,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              color: Colors.transparent,
              width: double.infinity,
              height: double.infinity,
            ),
            PositionedDirectional(
              start: 14.pt,
              end: 14.pt,
              top: 11.pt + statusBarHeight,
              child: _MatchDialogWidget(
                model: matchUser,
                onClose: _closeAction,
                onChat: _onChat,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MatchDialogWidget extends StatefulWidget {
  final MatchUser model;
  final VoidCallback onClose;
  final void Function(MatchUser model) onChat;

  const _MatchDialogWidget({
    Key? key,
    required this.model,
    required this.onClose,
    required this.onChat,
  }) : super(key: key);

  @override
  State<_MatchDialogWidget> createState() => _MatchDialogWidgetState();
}

class _MatchDialogWidgetState extends State<_MatchDialogWidget>
    with TickerProviderStateMixin {
  late final AnimationController _ringController;
  bool _showAvatar = false;
  late String _centerTxt;

  late Animation<double> _avatarScaleAnimation;
  late Animation<double> _avatarOpacityAnimation;
  late Animation<AlignmentGeometry> _avatarAlignAnimation;
  late AnimationController _avatarController;

  bool _isClosing = false;

  @override
  void initState() {
    super.initState();
    _centerTxt = LocaleStrings.instance.fateNewMatch;

    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _avatarScaleAnimation = Tween(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(parent: _avatarController, curve: Curves.easeIn));

    _avatarOpacityAnimation = Tween(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _avatarController, curve: Curves.easeIn));

    _avatarAlignAnimation = Tween<AlignmentGeometry>(
            begin: FractionalOffset(0, 0.3), end: FractionalOffset(0, 0))
        .animate(
            CurvedAnimation(parent: _avatarController, curve: Curves.easeIn));

    _ringController = AnimationController(vsync: this);
    _ringController.addStatusListener((status) {
      if (mounted && status == AnimationStatus.completed) {
        setState(() {
          _showAvatar = true;
          _centerTxt = widget.model.matchContent;
        });
        _avatarController.forward();
      }
    });

    Vibration.vibrate(PbLocalVibratorType.PbLocalVibratorType_CONTINUOUS);

    /// 匹配成功音效
    GlobalAudioPlayer.instance()
        .play(PlayItem(localPath: Assets.aachFateBell, flutterAsset: true));
  }

  @override
  void dispose() {
    _avatarController.dispose();
    _ringController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragEnd: (detail) {
        if (!_isClosing &&
            null != detail.primaryVelocity &&
            detail.primaryVelocity! < -200) {
          _isClosing = true;
          widget.onClose.call();
        }
      },
      child: SizedBox(
        width: 340.pt,
        height: 250.pt,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            _buildBg(),
            _buildBg2(),
            _buildRingAni(),
            _buildAvatar(),
            _buildMatchScore(),
            _buildLeftTop(),
            _buildClose(),
            _buildCenterText(),
            _buildBtn(),
          ],
        ),
      ),
    );
  }

  _buildBg() {
    return FLImage.asset(Res.matchMatchBg, width: 340.pt, height: 250.pt);
  }

  _buildBg2() {
    if (_showAvatar) {
      return FLImage.asset(Res.matchMatchBg2, width: 340.pt, height: 250.pt);
    } else {
      return SizedBox.shrink();
    }
  }

  _buildLeftTop() {
    return PositionedDirectional(
      start: 15.pt,
      top: 15.pt,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FLImage.asset(Res.matchMatchRing, width: 15.pt, height: 18.pt),
          SizedBox(width: 3.pt),
          Text(
            LocaleStrings.instance.fateBell,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0x80FFFFFF),
              fontWeight: FontWeightExt.heavy,
              fontStyle: FontStyle.italic,
            ),
          )
        ],
      ),
    );
  }

  _buildClose() {
    return PositionedDirectional(
      top: 0,
      end: 0,
      child: GestureDetector(
        onTap: () {
          widget.onClose.call();
        },
        child: Padding(
          padding: EdgeInsetsDirectional.only(
              top: 13.pt, end: 16.pt, start: 16.pt, bottom: 16.pt),
          child: FLImage.asset(Res.commonClose4, width: 21.pt, height: 21.pt),
        ),
      ),
    );
  }

  _buildRingAni() {
    if (_showAvatar) {
      return SizedBox.shrink();
    }
    return PositionedDirectional(
      top: 17.pt,
      child: Lottie.asset(
        Res.matchFatebellRingLottie,
        controller: _ringController,
        width: 180.pt,
        height: 180.pt,
        onLoaded: (composition) {
          _ringController
            ..duration = composition.duration
            ..forward();
        },
      ),
    );
  }

  _buildCenterText() {
    return PositionedDirectional(
      top: 159.pt,
      child: Text(
        _centerTxt,
        style: TextStyle(
          color: Color(0xE6FFFFFF),
          fontSize: 13.sp,
          fontWeight: FontWeightExt.medium,
        ),
      ),
    );
  }

  _buildBtn() {
    return PositionedDirectional(
      bottom: 16.pt,
      child: GestureDetector(
        onTap: () {
          widget.onChat.call(widget.model);
        },
        child: Container(
          width: 208.pt,
          height: 42.pt,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25.pt),
          ),
          child: Center(
            child: Text(
              LocaleStrings.instance.chatNow.firstCharToUpperCaseWithSpace,
              style: TextStyle(
                color: Color(0xFF880EE5),
                fontSize: 16.pt,
                fontWeight: FontWeightExt.heavy,
              ),
            ),
          ),
        ),
      ),
    );
  }

  _buildMatchScore() {
    if (_showAvatar) {
      return SizedBox.shrink();
    }
    return PositionedDirectional(
      top: 48.pt,
      start: 197.pt,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(
            Res.matchMatchDegreeBg,
            width: 32.pt,
            height: 32.pt,
          ),
          PositionedDirectional(
            top: 9.pt,
            child: Text(
              '${widget.model.matchScore}%',
              style: TextStyle(
                color: Color(0xFF880EE5),
                fontSize: 11.sp,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          )
        ],
      ),
    );
  }

  _buildAvatar() {
    if (_showAvatar) {
      return PositionedDirectional(
        top: 68.pt,
        child: FadeTransition(
          opacity: _avatarOpacityAnimation,
          child: ScaleTransition(
            scale: _avatarScaleAnimation,
            child: AlignTransition(
              alignment: _avatarAlignAnimation,
              widthFactor: 1,
              heightFactor: 2,
              child: UserAvatar(
                url: widget.model.avatar,
                avatarCode: widget.model.avatarCode,
                borderWidth: 2,
                borderColor: Color(0xFFFF7FD5),
                userId: widget.model.uid,
                size: 74.pt,
              ),
            ),
          ),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
