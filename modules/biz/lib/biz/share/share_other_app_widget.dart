import 'package:biz/biz.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/utils/connectivity_util.dart';

class ShareOtherAppWidget extends StatefulWidget {
  final Function(ShareApps)? callback;
  final OtherAppInfo? extra;

  /// 分享图标是否是可滚动列表，否：等距分隔
  final bool isListView;

  final double? spacing;

  final double? maxWidth;
  final bool? showIns;

  ShareOtherAppWidget({this.callback, this.extra, this.isListView = true, this.spacing, this.maxWidth, this.showIns});

  @override
  _ShareOtherAppState createState() => _ShareOtherAppState();
}

class OtherAppInfo {
  String icon;
  String name;
  ShareApps shareApps;
  VoidCallback? extraAppCallback;

  OtherAppInfo(this.name, this.icon, this.shareApps, {this.extraAppCallback});
}

class _ShareOtherAppState extends State<ShareOtherAppWidget> {
  final List<OtherAppInfo> _apps = [
    // OtherAppInfo(LocaleStrings.instance.facebook, Res.iconHFacebookH,
    //     ShareApps.facebook),

    OtherAppInfo(LocaleStrings.instance.whatsApp, Res.commonLogoWhatsapp, ShareApps.whatsapp),
    // _AppInfo(
    //     LocaleStrings.instance.snapchat, Res.iconHSnapchat, ShareApps.snapchat)
  ];

  @override
  void initState() {
    super.initState();

    if (widget.showIns ?? true) {
      _apps.insert(1, OtherAppInfo(LocaleStrings.instance.instagram, Res.commonLogoIns, ShareApps.instagram));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _otherApps(context),
      ],
    );
  }

  Widget _otherApps(BuildContext context) {
    return Container(
        height: 105.pt,
        child: widget.isListView
            ? ListView(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.only(top: 20.pt, left: 15.pt),
                shrinkWrap: true,
                children: _list(context),
              )
            : Row(
                children: _list(context),
              ));
  }

  List<Widget> _list(BuildContext context) {
    var rest = <Widget>[];
    if (widget.extra != null) {
      rest.add(_itemOtherApp(context, widget.extra!));
    }
    rest.addAll(_apps.map((e) => _itemOtherApp(context, e)).toList());
    rest.add(_more(callback: () async {
      ///是否被封禁
      if ((await hasBeRestricted(context))) {
        return;
      }
      Navigator.of(context).pop();
      widget.callback?.call(ShareApps.system);
    }));
    return rest;
  }

  Widget _itemWidget({required Widget child, required String title}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.spacing ?? 10.pt),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          child,
          SizedBox(height: 6.pt),
          ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: widget.maxWidth ?? 80.pt,
            ),
            child: Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 13.sp,
                color: R.color.color20,
                fontWeight: FontWeightExt.medium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _more({VoidCallback? callback}) {
    return ScaleTapWidget(
      onTap: callback,
      scale: 0.8,
      child: _itemWidget(
        child: FLImage.asset(Res.shareMore, height: 50.pt, fit: BoxFit.cover),
        title: LocaleStrings.instance.more,
      ),
    );
  }

  Widget _itemOtherApp(BuildContext context, OtherAppInfo _appInfo) {
    Widget btn = ScaleTapWidget(
        onTap: () async {
          if (connectivityUtil.isNoNet()) {
            toast(LocaleStrings.instance.checkNetWork);
            return;
          }

          ///是否被封禁
          if ((await hasBeRestricted(context))) {
            return;
          }
          Navigator.of(context).pop();
          widget.callback?.call(_appInfo.shareApps);
        },
        child: _itemWidget(
            child: FLImage.asset(
              _appInfo.icon,
              width: 50.pt,
              height: 50.pt,
            ),
            title: _appInfo.name));
    return widget.isListView ? btn : Expanded(child: btn);
  }
}
