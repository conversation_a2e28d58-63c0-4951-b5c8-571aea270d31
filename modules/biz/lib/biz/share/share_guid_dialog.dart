import 'dart:async';
import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/share/handler/share_app_handle.dart';
import 'package:biz/biz/share/share_other_app_widget.dart';
import 'package:biz/common/widgets/icon_button.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/utils/avatar_util.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/common/statistics/share_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/remote_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/share/const/events.dart';
import 'package:service/modules/share/model/user_total_count.dart';
import 'package:service/modules/user/model/user_info.dart';

enum ShowType {
  /// 看他人主页
  seeUserProfile,

  /// 查看用户回复后
  userReply,

  /// 个人测试Quiz
  quiz,

  /// 关注n次后
  followCount,

  /// 连续打开多少天
  openCount,
}

/// 用户手动点击分享触发的分享app弹窗，非被动触发
List<String> userClickShare = ["setting", "bio", "task"];

/// 分享引导对话框
void showShareGuidDialog(BuildContext? context, UserInfo? userInfo, {String? from}) {
  _reportSharePopoutImp(from);
  DialogScheduler.instance().schedule(
    () => showDialog(
      (_) => ShareGuidDialog(from, userInfo),
      barrierDismissible: true,
      context: context,
      routeSettings: RouteSettings(name: R_SHARE_DIALOG),
    ),
    priority: 1,
    context: context,
  );
}

void _reportSharePopoutImp(String? from) {
  if (userClickShare.contains(from)) {
    ShareStatistics.reportShareAppImp(from: from);
  } else {
    ShareStatistics.reportSharePopoutImp(from: from);
    ShareStatistics.reportShareAppImp(from: "popout");
  }
}

class ShareGuidDialog extends StatefulWidget {
  ShareGuidDialog(this.from, this.userInfo, {Key? key}) : super(key: key);

  final String? from;

  final UserInfo? userInfo;

  final ShareTemplate shareTemplate = AppShare(content: LocaleStrings.instance.shareAppContent, link: urlHome);

  @override
  State<StatefulWidget> createState() => _ShareGuidDialogState();
}

class _ShareGuidDialogState extends State<ShareGuidDialog> with SingleTickerProviderStateMixin {
  int _userTotalCount = 0;
  int _countTimestamp = 0;
  final _dataFormat = NumberFormat('0,000');

  /// 计算评论区域相关信息
  int _commentIndex = 0;
  final _avatarMaxCount = 10;
  final _nameMaxCount = 46;
  final _commentMaxCount = 20;

  late AnimationController _controller;
  late Animation<Offset> _animation;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _initAnimation();
    _initData();

    /// 开启计数
    _calculateCount();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _controller.dispose();
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(vsync: this, duration: Duration(milliseconds: 3000));
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _commentIndex = _commentIndex + 1;
        globalSp.setInt(spShareCommentIndex, _commentIndex);
        _controller.forward(from: 0);
        if (mounted) {
          setState(() {});
        }
      }
    });
    _animation = TweenSequence<Offset>([
      TweenSequenceItem(tween: Tween(begin: Offset(0, 1.3), end: Offset.zero), weight: 1),
      TweenSequenceItem(tween: ConstantTween(Offset.zero), weight: 1),
      TweenSequenceItem(tween: Tween(begin: Offset.zero, end: Offset(0, -1.3)), weight: 1)
    ]).animate(_controller);
    _controller.forward(from: 0);
  }

  void _initData() async {
    final section = await getService<AbsRemoteConfigService>()?.getSection(sectionAppUi);
    var value = section?.getValue(functionUserTotalCount)?.getAll();
    if (value != null) {
      UserTotalCount totalCount = UserTotalCount.fromJson(value);
      _countTimestamp = (DateTime(2022, 8, 1).millisecondsSinceEpoch / 1000).round();
      _userTotalCount = totalCount.count ?? _userTotalCount;
    }

    /// 计算当前的开始计算值
    final timeFix = await getService<AbsStimeService>()?.getTimeFix() ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch / 1000 + timeFix;
    final dt = (now - _countTimestamp) / 5;
    _userTotalCount = (_userTotalCount + dt).round();

    /// 获取本地保存的值，进行矫正，防止误差太大
    final spTime = globalSp.getInt(spTotalUserCount, defaultValue: 0) ?? 0;
    if (spTime > _userTotalCount && (spTime - _userTotalCount) <= 100) {
      _userTotalCount = spTime;
    }

    /// 计算评论相关下标
    final spIndex = globalSp.getInt(spShareCommentIndex, defaultValue: 0) ?? 0;
    _commentIndex = spIndex;
    if (_commentIndex > 1000) {
      _commentIndex = 0;
    }

    if (mounted) {
      setState(() {});
    }
  }

  void _calculateCount() {
    if (_timer == null || _timer?.isActive == false) {
      final delay = Random().nextInt(2000) + 1500;
      _timer = Timer(Duration(milliseconds: delay), () {
        _userTotalCount = _userTotalCount + 1;
        globalSp.setInt(spTotalUserCount, _userTotalCount);
        if (mounted) {
          setState(() {});
          _calculateCount();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _close(),
            SizedBox(
              height: 34.pt,
            ),
            Container(
              width: 320.pt,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.pt),
              ),
              child: _body(context),
            )
          ],
        ),
      ),
    );
  }

  Widget _close() {
    return Row(
      children: [
        Expanded(child: SizedBox()),
        FIconButton(
          onTap: () => routerUtil.pop(),
          icon: Res.commonClose3,
          iconSize: Size(24.pt, 24.pt),
        ),
        SizedBox(
          width: 16.pt,
        )
      ],
    );
  }

  Widget _body(context) {
    return Stack(
      children: [
        _bg(),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.only(top: 126.pt),
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  FLImage.asset(
                    Res.commonLogo,
                    width: 50.pt,
                    height: 50.pt,
                  ),
                  SizedBox(width: 6.pt),
                  FLImage.asset(
                    Res.commonAppNameWhite,
                    height: 27.pt,
                  ),
                ],
              ),
            ),
            SizedBox(height: 23.pt),
            Text(
              _userTotalCount == 0 ? '' : _dataFormat.format(_userTotalCount),
              style: TextStyle(color: Color(0xFF25E4A8), fontSize: 28.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 5.pt),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18),
              child: Text(
                widget.userInfo?.sex == Sex.male
                    ? LocaleStrings.instance.maleShareGuideContent
                    : LocaleStrings.instance.femaleShareGuideContent,
                style: TextStyle(color: Colors.white, fontSize: 18.sp),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 34.pt),
            Container(
              padding: EdgeInsets.only(top: 20.pt, left: 10.pt, right: 10.pt, bottom: 10.pt),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.pt),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.pt),
                    child: _commentWidget(),
                  ),
                  SizedBox(height: 13.pt),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.pt),
                    child: Text(
                      LocaleStrings.instance.shareToYouFriends,
                      style: TextStyle(color: R.color.textColor1, fontSize: 16.sp, fontWeight: FontWeightExt.medium),
                    ),
                  ),
                  ShareOtherAppWidget(
                    callback: _shareToApps,
                    isListView: false,
                  ),
                ],
              ),
            )
          ],
        )
      ],
    );
  }

  Widget _bg() {
    return Container(
      width: 321.pt,
      height: 325.pt,
      decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(16.pt), topRight: Radius.circular(16.pt)),
          image: DecorationImage(
              fit: BoxFit.fill,
              matchTextDirection: true,
              image: AssetImage(
                Res.commonShareGuideBg,
              ))),
    );
  }

  Widget _commentWidget() {
    var avatarCode = _commentIndex % _avatarMaxCount;
    if (_commentIndex % 2 != 0) {
      avatarCode = avatarCode + _avatarMaxCount;
    }
    return Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Color(0xFFF2F4F6), borderRadius: BorderRadius.circular(10.pt)),
        padding: EdgeInsets.symmetric(vertical: 11.pt, horizontal: 13.pt),
        child: SlideTransition(
          position: _animation,
          child: Row(
            children: [
              UserAvatar(url: '', avatarCode: avatarCodeStr(avatarCode)),
              SizedBox(width: 8.pt),
              Expanded(
                  child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(ShareComment.arNameList[avatarCode],
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 12.sp, color: Color(0x80010715))),
                  Text(ShareComment.commentList[_commentIndex % _commentMaxCount],
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 12.sp, color: Color(0xFF454A56))),
                ],
              ))
            ],
          ),
        ));
  }

  void _shareToApps(ShareApps type) {
    switch (type) {
      case ShareApps.facebook:
        shareAppHandle.shareToFacebook(widget.shareTemplate, guidFrom: widget.from);
        break;
      case ShareApps.instagram:
        shareAppHandle.shareToInstagram(widget.shareTemplate, guidFrom: widget.from);
        break;
      case ShareApps.whatsapp:
        shareAppHandle.shareToWhatApp(widget.shareTemplate, guidFrom: widget.from);
        break;
      case ShareApps.system:
        shareAppHandle.shareMore(widget.shareTemplate, guidFrom: widget.from);
        break;
      case ShareApps.snapchat:
        // TODO: Handle this case.
        break;
      default:
        break;
    }
    ShareStatistics.reportSharePopoutClick(from: widget.from);
    
    taskService.taskAction(action: 'shares_times');
  }
}
