import 'package:biz/biz.dart';
import 'package:biz/biz/share/bloc/share_bloc.dart';
import 'package:biz/biz/share/model/const.dart';
import 'package:biz/biz/share/share_actions_widget.dart';
import 'package:biz/biz/share/share_friends_widget.dart';
import 'package:biz/biz/share/share_other_app_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:flutter/material.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import 'contacts/model/share_item_model.dart';
import 'contacts/share_contacts_widget.dart';

void showShareDialog(
    {required BuildContext context,
    required ShareTemplate template,
    bool? showFriends,
    bool? showApps,
    String? title,
    OtherAppInfo? extraApp,
    Function(MoreStatisticsType, Moment, ShareApps?)? itemMoreStatisticCallback,
    String? guidFrom,
    Function(ShareToType? type, bool success, String? target)? shareResultCallback,
    Function(MoreStatisticsType, ShareApps?, UserInfo?)? shareStatisticCallback,
    String? from,
    bool? showBlock,
    RouteSettings? routeSettings,
    EdgeInsets? margin,
    bool? showIns,
    bool? oppositeSex,
    bool? filterFamily,
    bool? includeGroupChat,
    String? contactBtnTitle,
    Function(UserInfo)? contactSelectCallback,
    bool? contactSelectWithClose,
    ShareWidgetType shareWidgetType = ShareWidgetType.normal,
      Function(String buttonTitle)? clickItemCallback}) {
  showBottomSheetDialog(
      context: context,
      priority: 100,
      margin: margin,
      routeSettings: routeSettings,
      child: ShareWidget(
          shareResultCallback: shareResultCallback,
          shareTemplate: template,
          shareWidgetType: shareWidgetType,
          showFriends: showFriends ?? true,
          showApps: showApps ?? true,
          showBlock: showBlock ?? false,
          extraApp: extraApp,
          page: ModalRoute.of(context)?.settings.name ?? "",
          itemMoreStatisticCallback: itemMoreStatisticCallback,
          shareStatisticCallback: shareStatisticCallback,
          guidFrom: guidFrom,
          showIns: showIns,
          oppositeSex: oppositeSex ?? false,
          filterFamily: filterFamily ?? false,
          includeGroupChat: includeGroupChat ?? false,
          contactSelectCallback: contactSelectCallback,
          contactBtnTitle: contactBtnTitle,
          contactSelectWithClose: contactSelectWithClose ?? true,
          from: from,
          clickItemCallback: clickItemCallback,
      ),
      title: title ?? LocaleStrings.instance.shareToFriends);
}

class ShareWidget extends StatelessWidget {
  final ShareTemplate shareTemplate;
  final bool showFriends;
  final bool showApps;
  final bool showSocialActions;
  final bool showBlock;
  final OtherAppInfo? extraApp;
  final String? page;
  final String? guidFrom;
  final String? from;
  final bool? showIns;
  final String? contactBtnTitle;

  final ShareWidgetType shareWidgetType;

  /// 是否筛选异性
  final bool oppositeSex;

  /// 是否筛选家族成员
  final bool filterFamily;

  final bool includeGroupChat;

  /// 处理Moment_more打点回调
  final Function(MoreStatisticsType, Moment, ShareApps?)?
      itemMoreStatisticCallback;

  /// 处理Moment_more打点回调
  final Function(MoreStatisticsType, ShareApps?, UserInfo?)?
      shareStatisticCallback;

  final Function(UserInfo)? contactSelectCallback;

  /// 选择联系人并关闭当前窗口
  final bool contactSelectWithClose;

  Function(String buttonTitle)? clickItemCallback;

  ShareWidget({
    required this.shareTemplate,
    required this.shareWidgetType,
    this.showFriends = true,
    this.showApps = true,
    this.showBlock = false,
    this.showSocialActions = false,
    this.extraApp,
    this.itemMoreStatisticCallback,
    this.shareStatisticCallback,
    this.contactBtnTitle,
    this.page,
    this.guidFrom,
    this.from,
    this.showIns,
    Function(ShareToType? type, bool success, String? target)? shareResultCallback,
    this.oppositeSex = false,
    this.filterFamily = false,
    this.includeGroupChat = false,
    this.contactSelectCallback,
    this.contactSelectWithClose = true,
    this.clickItemCallback
  }) : _bloc = ShareBloc(
            shareTemplate, showFriends, itemMoreStatisticCallback, guidFrom,
            shareResultCallback: shareResultCallback,
            from: from,
            shareStatisticCallback: shareStatisticCallback);
  final ShareBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<ShareBloc, ShareState>(
      bloc: _bloc,
      builder: (_, state) => Container(
        width: double.infinity,
        child: _body(context),
      ),
    );
  }

  Widget _body(BuildContext context) {
    switch (shareWidgetType) {
      case ShareWidgetType.normal:
        return _normalList(context);
      case ShareWidgetType.withContact:
        return _withContacts(context);
    }
  }

  Column _normalList(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        if (_bloc.showFriends)
          ShareFriendsWidget(
            selectFriendCallback: (userInfo, groupInfo) => _selectFriend(
                ShareFriendItemModel(userInfo: userInfo, chatGroup: groupInfo), context),
            selectMoreCallback: _friendMore,
          ),
        if (showApps)
          ShareOtherAppWidget(
            spacing: 30.pt,
              callback: _shareToApps, extra: extraApp, showIns: showIns),
        if (showApps)
          _divider(),
        if ((_bloc.shareTemplate.targetUid?.isNotEmpty ?? false))
          ShareActionsWidget(
              targetUid: _bloc.shareTemplate.targetUid,
              callbackShareType: _callbackMoreActionType,
              moment: _bloc.shareTemplate.moment,
              page: page,
              showBlock: showBlock,
          clickItemCallback: clickItemCallback),
      ],
    );
  }

  Column _withContacts(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        if (showApps)
          Center(
            child: ShareOtherAppWidget(
                callback: _shareToApps,
                extra: extraApp,
                spacing: 30.pt,
                maxWidth: 65.pt,
                showIns: showIns),
          ),
        // if (showApps) _divider(),
        ShareContactWidget(
            oppositeSex: oppositeSex,
            filterFamily: filterFamily,
            includeGroupChat: includeGroupChat,
            btnTitle: contactBtnTitle,
            share: (value) => _selectFriend(value, context, pop: false))
      ],
    );
  }

  Future<void> _selectFriend(ShareFriendItemModel value, BuildContext context,
      {bool pop = true}) async {
    if (contactSelectCallback != null) {
      if (value.userInfo == null) return;
      if (contactSelectWithClose) {
        Navigator.of(context).pop();
      }
      contactSelectCallback?.call(value.userInfo!);
      return;
    }

    _bloc.add(ShareToFriendsEvent(value, pop: pop));
  }

  void _friendMore() {}

  void _shareToApps(ShareApps type) {
    switch (type) {
      case ShareApps.facebook:
        _bloc.add(ShareToFacebook());
        break;
      case ShareApps.instagram:
        _bloc.add(ShareToIns());
        break;
      case ShareApps.whatsapp:
        _bloc.add(ShareToWhatApp());
        break;
      case ShareApps.system:
        _bloc.add(ShareToMore());
        break;
      case ShareApps.snapchat:
        // TODO: Handle this case.
        break;
      case ShareApps.extra:
        extraApp?.extraAppCallback?.call();
        break;
      default:
        break;
    }
    _bloc.callBackStatistics(MoreStatisticsType.snsShare, type);
  }

  Widget _divider() {
    return Padding(padding: EdgeInsets.symmetric(horizontal: 17.5.pt), child: Divider(
      color: Color(0xff121332).withOpacity(0.08),
      height: 15.pt,
    ));
  }

  /// 更多：关注举报，私聊
  void _callbackMoreActionType(MoreStatisticsType type) {
    if (itemMoreStatisticCallback != null && shareTemplate.moment != null) {
      itemMoreStatisticCallback?.call(type, shareTemplate.moment!, null);
    }
    shareStatisticCallback?.call(type, null, null);
  }
}
