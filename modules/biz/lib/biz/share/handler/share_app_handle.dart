import 'dart:developer';

import 'package:biz/biz/share/model/const.dart';
import 'package:flutter/services.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:flutter_social_content_share/flutter_social_content_share.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/share_statistics.g.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/sticker/const/task_action_code.dart';
import 'package:service/service.dart';

ShareAppHandle shareAppHandle = ShareAppHandle._();

class ShareAppHandle {
  bool initSuccess = false;
  final String _success = "Success";
  final _shareContent = LocaleStrings.instance.shareAppContent;
  final _shareLink = AppConfig.instance.webBaseUrl;

  ShareAppHandle._() {
    _initShare();
  }

  void _initShare() {
    try {
      FlutterSocialContentShare.platformVersion;
      initSuccess = true;
    } on PlatformException {
      log("init share fail");
      initSuccess = false;
    }
  }

  void shareToFacebook(ShareTemplate shareTemplate,
      {String? guidFrom,
      Function(ShareToType? type, bool success, String? target)? shareResultCallback,
      String? from}) async {
    String? result = await FlutterSocialContentShare.share(
        type: ShareType.facebook,
        url: facebookShareLink,
        imageUrl: shareTemplate.image,
        quote: _getShareContent(shareTemplate));
    log("share->$result");
    if (guidFrom?.isNotEmpty == true) {
      ShareStatistics.reportSharePopoutClickSns(
          act: ShareTypeStatistic.fb, from: guidFrom);
    }
    _shareAppItemClickReport(shareTemplate,
        act: ShareTypeStatistic.fb, from: from);
    shareResultCallback?.call(ShareToType.facebook, result == _success, null);

    if ((result?.isNotEmpty ?? false) && result != _success) {
      toast(result!);
      return;
    }
  }

  Future<void> shareToInstagram(ShareTemplate shareTemplate,
      {String? guidFrom,
      Function(ShareToType? type, bool success, String? target)? shareResultCallback,
      String? from}) async {
    String? result = await FlutterSocialContentShare.share(
        type: ShareType.instagram,
        url: _getShareLink(shareTemplate),
        imageUrl: (shareTemplate.image?.isNotEmpty ?? false)
            ? shareTemplate.image
            : shareTemplate.getLogo(),
        quote: _getShareContent(shareTemplate));
    log("share->$result");
    if (guidFrom?.isNotEmpty == true) {
      ShareStatistics.reportSharePopoutClickSns(
          act: ShareTypeStatistic.ins, from: guidFrom);
    }
    _shareAppItemClickReport(shareTemplate,
        act: ShareTypeStatistic.ins, from: from);
    shareResultCallback?.call(ShareToType.ins, result == _success, null);

    if ((result?.isNotEmpty ?? false) && result != _success) {
      toast(result!);
      return;
    }
  }

  Future<void> shareToWhatApp(ShareTemplate shareTemplate,
      {String? guidFrom,
      Function(ShareToType? type, bool success, String? target)? shareResultCallback,
      String? from}) async {
    String? result = await FlutterSocialContentShare.share(
      type: ShareType.whatsapp,
      imageUrl: shareTemplate.image,
      number: "",
      text:
          "${_getShareContent(shareTemplate)}\n${_getShareLink(shareTemplate)}",
    );
    log("share result->$result");
    if (guidFrom?.isNotEmpty == true) {
      ShareStatistics.reportSharePopoutClickSns(
          act: ShareTypeStatistic.whatsapp, from: guidFrom);
    }
    _shareAppItemClickReport(shareTemplate,
        act: ShareTypeStatistic.whatsapp, from: from);
    shareResultCallback?.call(ShareToType.whatApp, result == _success, null);

    if ((result?.isNotEmpty ?? false) && result != _success) {
      toast(result!);
      return;
    }
  }

  Future<void> shareMore(ShareTemplate shareTemplate,
      {String? guidFrom,
      Function(ShareToType? type, bool success, String? target)? shareResultCallback,
      String? from}) async {
    bool? result = await FlutterShare.share(
        title: LocaleStrings.instance.share,
        text: _getShareContent(shareTemplate),
        linkUrl: _getShareLink(shareTemplate),
        chooserTitle: '');
    if (guidFrom?.isNotEmpty == true) {
      ShareStatistics.reportSharePopoutClickSns(
          act: ShareTypeStatistic.more, from: guidFrom);
    }
    _shareAppItemClickReport(shareTemplate,
        act: ShareTypeStatistic.more, from: from);
    shareResultCallback?.call(ShareToType.more, result == true, null);

    log("share result->$result");
  }

  String _getShareContent(ShareTemplate shareTemplate) {
    if (shareTemplate.content?.isNotEmpty ?? false) {
      return shareTemplate.content!;
    }
    return _shareContent;
  }

  String _getShareLink(ShareTemplate shareTemplate) {
    if (shareTemplate.link?.isNotEmpty ?? false) {
      return shareTemplate.link!;
    }
    return _shareLink;
  }

  void _shareAppItemClickReport(ShareTemplate shareTemplate,
      {required String act, String? from}) {
    if (shareTemplate is AppShare) {
      ShareStatistics.reportShareAppSns(from: from ?? "popout", act: act);
    }
  }

  Future<void> shareToWhatsApp(ShareTemplate shareTemplate,
      {String? guidFrom,
        Function(ShareToType? type, bool success)?
        shareResultCallback,
        String? from}) async {
    String? result = await FlutterSocialContentShare.share(
      type: ShareType.whatsapp,
      imageUrl: shareTemplate.image,
      number: "",
      text:
      "${_getShareContent(shareTemplate)}\n${_getShareLink(shareTemplate)}",
    );
    log("share result->$result");
    shareResultCallback?.call(ShareToType.whatApp, result == _success);

    if ((result?.isNotEmpty ?? false) && result != _success) {
      toast(result!);
      return;
    }
    _submitTaskAction(shareTemplate);
  }

  void _submitTaskAction(ShareTemplate shareTemplate) {
    if (shareTemplate is AppShare) {
      /// 上报任务行为：分享app
      taskService.submitTaskAction(TaskActionCodes.shareApp);
    } else if (shareTemplate is QuizShare) {
      /// 上报任务行为：分享quiz
      taskService.submitTaskAction(TaskActionCodes.shareQuiz);
    }
  }
}
