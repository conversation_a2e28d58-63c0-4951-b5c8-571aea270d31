import 'dart:io';
import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/chat/dialog/at_user_dialog.dart';
import 'package:biz/biz/evaluate/evaluate_dialog.dart';
import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge/recharge_dialog.dart';
import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge_succeeds_dialog.dart';
import 'package:biz/biz/game/game_result/game_result_dialog.dart';
import 'package:biz/biz/live_room/component/gift/float_screen/gift_float_screen_widget.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/main/star_list/task/activity_task.dart';
import 'package:biz/biz/match/fate_bell_dialog.dart';
import 'package:biz/biz/match/room_match_dialog.dart';
import 'package:biz/biz/settings/edit_profile/user_info_improve_manager.dart';
import 'package:biz/biz/task/newbie/newbie_dialog.dart';
import 'package:biz/biz/task/sign_in/sign_in_controller.dart';
import 'package:biz/biz/task/sign_in/sign_in_suc_dialog.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/spannable_text.dart';
import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/permission/request_permission_dialog.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/badge/model/badge_model.dart';
import 'package:service/modules/channel_msg/abs_channel_msg_service.dart';
import 'package:service/modules/evaluate/model/evaluate.dart';
import 'package:service/modules/finance/const/enums.dart';
import 'package:service/modules/home/<USER>/main_notification_model.dart';
import 'package:service/modules/im/const/enums.dart';
import 'package:service/modules/im/model/activity_msg_content.dart';
import 'package:service/modules/im/model/pb_msg_content.dart';
import 'package:service/modules/live/chat/model/chat_welcome_msg_content.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room/model/room_info_model.dart';
import 'package:service/modules/match/model/match_model.dart';
import 'package:service/modules/match/model/room_no_action_match.dart';
import 'package:service/modules/notification/abs_notification_service.dart';
import 'package:service/modules/overlay/abs_overlay_service.dart';
import 'package:service/modules/task/model/task_list_entity.dart';
import 'package:service/modules/task/model/task_sign_in_model.dart';
import 'package:service/modules/test/test_api.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/local/pb_local.pb.dart';
import 'package:service/pb/local/pb_local_enum.pb.dart';
import 'package:service/pb/local/pb_local_req.pb.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/pb/net/pb_game.pb.dart';
import 'package:service/pb/net/pb_pay.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_room.pb.dart';
import 'package:service/pb/net/pb_user.pb.dart';
import 'package:service/router/page_visibility_observer.dart';
import 'package:service/utils/deep_link.dart';

import '../../global/widgets/play_animation_dialog/play_animation_dialog.dart';
import '../chat/chat_details/widgets/msg_content/msg_content_family.dart';
import '../chat/chat_details/widgets/msg_item/activity_msg_item.dart';
import '../live_room/component/chat/room_welcome_msg_widget.dart';
import '../live_room/component/dialog/room_send_gift_guide_dialog.dart';
import '../live_room/component/gift/float_screen/gift_float_model.dart';
import 'test_cocos_game_page.dart';

class TestModel {
  final String tag;

  TestModel(this.tag);
}

@FRoute(url: R_TEST, desc: "开发自测页面")
class TestPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _TestPage();
  }
}

class _TestPage extends State<TestPage> with PageVisibilityObserver {
  int currentIndex = 1;

  UserInfo? _userInfo;

  String _did = '';

  String? _latitude;
  String? _longitude;
  late int _testConfig;

  int toastIndex = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    addPageVisibilityObserver(ModalRoute.of(context));
  }

  @override
  void initState() {
    super.initState();
    userService.getCurrentUserInfo().then(_changeUser);
    getService<AbsChannelService>()?.invokeMethod<String>('nova_app_info', arguments: "did").then((value) {
      _did = value ?? '';
      if (mounted) {
        setState(() {});
      }
    });
    _testConfig = getCachedTestConfig();
    WidgetUtils.post((duration) {
      getService<AbsOverlayService>()?.hide(GlobalOverlayType.debug);
    });
  }

  @override
  void dispose() {
    super.dispose();
    removePageVisibilityObserver();
    WidgetUtils.post((duration) {
      getService<AbsOverlayService>()?.show(GlobalOverlayType.debug);
    });
  }

  final GlobalKey _testKey = GlobalKey();
  final _urlController = TextEditingController();
  final _roomController = TextEditingController();
  final _magicController = TextEditingController();

  final Preferences _preferences = Preferences.newInstance('account');

  void _changeUser(UserInfo? userinfo) {
    setState(() {
      _userInfo = userinfo;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Test',
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildBtn("badge achieved Dialog", () {
              List<BadgeDetail> badgeList =  [
                BadgeDetail()..key = 'growth_medal_generous'..name = 'Signing Arrival (Bronze) Medal' ..currentState = 1 .. cuImg = 'https://res.arabicplaymate.com/pm/user/badge/growth_medal_superstar_01.png',
              ];

              /// 显示勋章获得弹窗
              rxUtil.send(BadgeEvent.achieved, badgeList);
            }),
            _buildBtn("Sign in success Dialog", () {
              final signInModel = [
                TaskListNewbieRewardsEntity()
                  ..icon = 'https://res.winker.chat/nova/common/currency/diamond_2_2x.png?x-oss-process=style/m'
                  ..title = 'diamond'
                  ..amount = 25
                  ..expireUnit = '',
                TaskListNewbieRewardsEntity()
                  ..icon =
                      'https://res.winker.chat/nova/common/materials/d2/ee/28/e9ed587eebdcaf81679d7a5d46d2ee28/37877.png'
                  ..title = 'Seaside Holiday'
                  ..amount = 1
                  ..expireUnit = 'Days',
              ];
              showSignInSuccessDialog(1, signInModel);
            }),
            GiftFloatScreenWidget(type: GiftFloatType.home),
            _buildBtn("h5 play mp4", () {
              playAnimationDialog(
                  vapUrl: "https://res.winker.chat/nova/common/materials/ac/a0/f4/6b3ddd1915f4aa770c866c0b6caca0f4/612325.mp4", vapVersion: 1, showCloseBtn: true);
            }),
            _buildBtn("activityDialog", () {
              ActivityTask().run();
            }),
            _buildBtn("FloatScreen-LuckyBag", () async {
              Future.delayed(Duration(seconds: 1), () async {
                final currentUser = await userService.getCurrentUserInfo();
                final notice = PbRoomFloatScreenNotice();
                final item = PbRoomFloatScreenNoticeItem(
                    icon: "https://res.halamate.com/hala/common/materials/e4/73/ac/e67a84f062e2112f4b0a9354d4e473ac/21651.png",
                    content: "Family Name",
                    router: "winker://live/room_join?id=338462496",
                    uiType: 1,
                    check_5: 0,
                    theme: GiftFloatTheme.luckyBag.index,
                    scene: 0, // 全部场景
                    user: PbUser(
                      uid: currentUser?.uid ?? "123456789",
                      showUid: currentUser?.showUid ?? "123456",
                      nickname: currentUser?.nickname ?? "Test User",
                      headimgurl: currentUser?.avatar ?? "https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg",
                      avatarCode: currentUser?.avatarCode ?? "male_11",
                    ),
                    );
                // 添加ext字段
                item.ext['family_id'] = 'family_12345';
                item.ext['family_name'] = 'Test Family';
                notice.list.add(item);
                rxUtil.send(FullScreenBroadcast.generalFloat, notice);
              });
            }),
            _buildBtn("FloatScreen-BcGame", () async {
              Future.delayed(Duration(seconds: 1), () async {
                final currentUser = await userService.getCurrentUserInfo();
                final notice = PbRoomFloatScreenNotice();
                final item = PbRoomFloatScreenNoticeItem(
                    icon: "https://res.halamate.com/hala/common/materials/e4/73/ac/e67a84f062e2112f4b0a9354d4e473ac/21651.png",
                    content: "x45 the jackpot Win 100",
                    router: "winker://www.winker.chat/service?action=open_bc_game",
                    uiType: 1,
                    check_5: 1, // 需要检查BC权限
                    theme: GiftFloatTheme.bcGame.index,
                    scene: 0, // 全部场景
                    user: PbUser(
                      uid: currentUser?.uid ?? "123456789",
                      showUid: currentUser?.showUid ?? "123456",
                      nickname: currentUser?.nickname ?? "Test User",
                      headimgurl: currentUser?.avatar ?? "https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg",
                      avatarCode: currentUser?.avatarCode ?? "male_11",
                    ),
                    );
                // 添加ext字段
                item.ext['game_name'] = 'BC Game';
                item.ext['multiple'] = '45';
                item.ext['prize'] = '100';
                item.ext['currency_type'] = 'diamond';
                notice.list.add(item);
                rxUtil.send(FullScreenBroadcast.generalFloat, notice);
              });
            }),
            _buildBtn("FloatScreen-Gift", () async {
              Future.delayed(Duration(seconds: 1), () async {
                final currentUser = await userService.getCurrentUserInfo();
                final notice = PbRoomFloatScreenNotice();
                final item = PbRoomFloatScreenNoticeItem(
                    icon: "https://res.halamate.com/hala/common/materials/e4/73/ac/e67a84f062e2112f4b0a9354d4e473ac/21651.png",
                    content: "999", // 礼物数量
                    router: "winker://live/room_join?id=338462496",
                    uiType: 1,
                    check_5: 0,
                    theme: GiftFloatTheme.gift.index,
                    scene: 0, // 全部场景
                    user: PbUser(
                      uid: currentUser?.uid ?? "123456789",
                      showUid: currentUser?.showUid ?? "123456",
                      nickname: currentUser?.nickname ?? "Test User",
                      headimgurl: currentUser?.avatar ?? "https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg",
                      avatarCode: currentUser?.avatarCode ?? "male_11",
                    ),
                    user2: PbUser(
                      uid: "987654321",
                      showUid: "654321",
                      nickname: "Receiver User",
                      headimgurl: "https://res.winker.chat/nova/user/avatar/avatar_code/female_39.jpg",
                      avatarCode: "female_39",
                    ),
                    );
                // 添加ext字段
                item.ext['gift_count'] = '999';
                item.ext['gift_name'] = 'Diamond Gift';
                notice.list.add(item);
                rxUtil.send(FullScreenBroadcast.generalFloat, notice);
              });
            }),
            _buildBtn("FloatScreen-RedBag", () async {
              Future.delayed(Duration(seconds: 1), () async {
                final currentUser = await userService.getCurrentUserInfo();
                final notice = PbRoomFloatScreenNotice();
                final item = PbRoomFloatScreenNoticeItem(
                    icon: "https://res.halamate.com/hala/common/materials/e4/73/ac/e67a84f062e2112f4b0a9354d4e473ac/21651.png",
                    content: "Casili", // 家族名称
                    router: "winker://live/room_join?id=338462496",
                    uiType: 1,
                    check_5: 0,
                    theme: GiftFloatTheme.red_bag.index,
                    scene: 0, // 全部场景
                    user: PbUser(
                      uid: currentUser?.uid ?? "123456789",
                      showUid: currentUser?.showUid ?? "123456",
                      nickname: currentUser?.nickname ?? "Test User",
                      headimgurl: currentUser?.avatar ?? "https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg",
                      avatarCode: currentUser?.avatarCode ?? "male_11",
                    ),
                    );
                // 添加ext字段
                item.ext['room_cover'] = 'https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg';
                item.ext['room_name'] = 'Test Room';
                notice.list.add(item);
                rxUtil.send(FullScreenBroadcast.generalFloat, notice);
              });
            }),
            Row(
              children: [
                SizedBox(width: 10.pt),
                Expanded(
                  child: TextField(
                    controller: _urlController,
                    textAlign: TextAlign.center,
                    onSubmitted: DeepLink.jump,
                    decoration: InputDecoration(hintText: 'Input web url or app url'),
                  ),
                ),
                TextButton(onPressed: () => DeepLink.jump(_urlController.text), child: Text('Go')),
                SizedBox(width: 5.pt),
              ],
            ),
            _buildActivityMessages(),
            _buildBtn("FCMToken(click copy): ${_preferences.getString('fcm_token')}", () {
              if (_did.isNotEmpty) {
                Clipboard.setData(ClipboardData(text: (_preferences.getString('fcm_token') ?? "")));
                toast("${LocaleStrings.instance.copySuccess}");
              }
            }),
            _buildBtn('room upgrade', () {
              getService<AbsOverlayService>()?.show(GlobalOverlayType.roomUpgrade, model: PbRoomLevelNotice());
            }),
            RoomWelcomeMsgWidget(),
            _buildBtn(
              'Send Welcome Message',
              () async {
                final welcomeMsg = ChatWelcomeMsgContent(
                    type: 2,
                    content: _getRandomWelcomeMessage(),
                    isManager: false,
                    otherUser: PbUser(
                        showUid: _getRandomShowUid(),
                        nickname: _getRandomNickname(),
                        uid: _getRandomShowUid(),
                        headimgurl: (await userService.getCurrentUserInfo())?.avatar));
                rxUtil.send(RoomUserEvent.enterTheRoom, welcomeMsg);
              },
            ),
            _buildBtn("room_invite", () async {
              final push = PbBizPush(
                  pushEvent: PbPushEvent.PbPushEvent_ROOM_INVITE,
                  data: PbRoomInviteNotice(
                          roomId: "123456789",
                          mode: "chat",
                          fuidInfo: PbUser(
                              showUid: "12345",
                              nickname: "12345",
                              uid: "123",
                              headimgurl: (await userService.getCurrentUserInfo())?.avatar),
                          roomInfo: PbRoomInfo(
                              id: "123", cover: (await userService.getCurrentUserInfo())?.avatar, name: "123"),
                          pos: 1,
                          duration: 10)
                      .writeToBuffer());
              notificationService.test(push);
            }),
            _buildBtn("game_invite", () async {
              final push = PbBizPush(
                  pushEvent: PbPushEvent.PbPushEvent_ROOM_INVITE,
                  data: PbRoomInviteNotice(
                    roomId: "123456789",
                    mode: "game_ludo",
                    fuidInfo: PbUser(
                        showUid: "12345",
                        nickname: "12345",
                        uid: "123",
                        headimgurl: (await userService.getCurrentUserInfo())?.avatar),
                    roomInfo:
                        PbRoomInfo(id: "123", cover: (await userService.getCurrentUserInfo())?.avatar, name: "123"),
                    pos: 1,
                    duration: 10,
                  ).writeToBuffer());
              notificationService.test(push);
            }),
            _buildBtn("SendGiftGuide", () async {
              final userInfo = await userService.getCurrentUserInfo();
              showRoomSendGiftGuideDialog(ownerInfo: userInfo!, userInfo: userInfo, giftInfo: null);
            }),
            _buildBtn("newbie", () {
              showNewbieDialog(model: TaskListItemEntity());
            }),
            _buildBtn("game_result", () async {
              showGameResultDialog(
                  close: () {
                    Log.i("Test", "game result close");
                  },
                  onWillPop: () {
                    Log.i("Test", "game onWillPop");
                  },
                  result: PbGameResultNotice(items: [
                    PbGameResultItem(
                      user: PbRoomUser(
                          user: PbUser(
                              nickname: "1234",
                              headimgurl: (await userService.getCurrentUserInfo())?.avatar,
                              uid: accountService.currentUid())),
                      text: "1234",
                    ),
                    PbGameResultItem(
                      user: PbRoomUser(user: PbUser(nickname: "1234")),
                      text: "1234",
                    ),
                    PbGameResultItem(
                      user: PbRoomUser(user: PbUser(nickname: "1234")),
                      text: "1234",
                    ),
                    PbGameResultItem(
                      user: PbRoomUser(user: PbUser(nickname: "1234")),
                      text: "1234",
                    ),
                  ]),
                  gameType: GameType.game_ludo,
                  again: () {});
            }),
            _buildBtn("recharge_succ", () async {
              final push = PbBizPush(
                  pushEvent: PbPushEvent.PbPushEvent_RECHARGE_SUC,
                  data: PbRechargeSucNotice(
                    source: RechargeSuccSource.recharge,
                  ).writeToBuffer());
              notificationService.test(push);
            }),
            _buildBtn("Did(click copy): $_did", () {
              if (_did.isNotEmpty) {
                Clipboard.setData(ClipboardData(text: _did));
                toast("${LocaleStrings.instance.copySuccess}");
              }
            }),
            _buildBtn("testApns", () {
              notificationService.onReceived(
                  'CAMSgwkIARL+CNin2YbYqiDYp9mE2KfZhiDYp9i12KjYrdiqINmF2YYg2LnYp9im2YTYqSBIYWxhTWUg2KfZhNmD2KjZitix2Knwn6Wz8J+Sni4gCtmI2YTYo9mG2YHYs9mD2YUg2YTZhNit2LjYsSDZhdmGINin2YTYqti32KjZitmCLiDYsdis2KfYoSDZhdmGINiz2YrYp9iv2KrZg9mFINi52K/ZhSDZhti02LEg2KPZiiDZhdit2KrZiNmJINis2YbYs9mKINij2Ygg2LrZitixINmE2KfYptmCINi52YTZiSDYp9mE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqSDZiNil2YTYpyDYs9mK2KrZhSDYp9mE2KXYqNmE2KfYuiDZiNmB2Yog2YfYsNmHINin2YTYrdin2YTYqSDYs9mK2YPZiNmGINmF2YYg2KfZhNmF2YXZg9mGINit2LjYsSDYrdiz2KfYqNmD2YUg2YXZhiDYp9mE2KrYt9io2YrZgi4g2K3Yp9mB2LjZiNinINi52YTZiSDYp9mE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqSDZhdiq2K3Yttix2Kkg2K3YqtmJINmE2Kcg2YrYqti22KfZitmCINij2Yog2KPYrdivLiDZhtiq2YXZhtmJINij2YYg2YrZgti22Yog2KfZhNis2YXZiti5INmI2YLYqiDYsdin2KbYuSDZgdmKIEhhbGFNZS4=');
            }),
            _buildBtn("testTextSpannale", () {
              final route = PageRouteBuilder(
                pageBuilder: (_, __, ___) {
                  return Scaffold(
                    body: SafeArea(
                      child: Column(
                        children: [
                          SpannableText(labels: [
                            LabelBean.img(
                                "https://www.google.com/url?sa=i&url=https%3A%2F%2Fch.pinterest.com%2Fpin%2F519813981975982818%2F&psig=AOvVaw0J9ZN8CkRWO5ots973n1WX&ust=1713933188925000&source=images&cd=vfe&opi=89978449&ved=0CBAQjRxqFwoTCIDBgPDA14UDFQAAAAAdAAAAABAE"),
                            LabelBean.text("正常文本"),
                            LabelBean.text("红色文本", fontSize: 15.pt, fontColor: "#ff0000"),
                            LabelBean.text("蓝色文本带下划线", fontSize: 13.pt, fontColor: "#0000ff", link: true),
                          ])
                        ],
                      ),
                    ),
                  );
                },
              );
              Navigator.of(context).push(route);
            }),

            // 服务器地址
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildBtn(
                  "Service address ${_envName()}",
                  () async {
                    int? value = await showDialog<int>(
                        context: context,
                        builder: (context) {
                          return SimpleDialog(
                            title: Text('选择服务器'),
                            children: <Widget>[
                              RadioListTile<int>(
                                title: Text('测试服务器'),
                                subtitle: Text('${AppConfig.createDebug().baseUrl}'),
                                value: EnvConfig.test.index,
                                groupValue: _testConfig,
                                onChanged: (value) async {
                                  await setTestConfig(EnvConfig.test);
                                  Navigator.pop(context, EnvConfig.test.index);
                                },
                              ),
                              RadioListTile<int>(
                                title: Text('正式服务器'),
                                subtitle: Text('${AppConfig.createRelease().baseUrl}'),
                                value: EnvConfig.prod.index,
                                groupValue: _testConfig,
                                onChanged: (value) async {
                                  await setTestConfig(EnvConfig.prod);
                                  Navigator.pop(context, EnvConfig.prod.index);
                                },
                              ),
                            ],
                          );
                        });
                    Log.d("test", 'service address $value');
                    if (value != null && _testConfig != value) {
                      _testConfig = value;
                      if (mounted) {
                        setState(() {});
                      }
                      toast('Killing App soon...');
                      Future.delayed(Duration(milliseconds: 1000), () => exit(0));
                    }
                  },
                ),
              ],
            ),

            _buildBtn("Change Language", () {
              routerUtil.push(R_SETTINGS_LANGUAGE);
            }),

            _buildBtn("SignIn Dialog", showSignInBoard),

            _buildBtn("jsBridgeDemo", _gotoJsBridgeDemo),
            _buildBtn("Cocos Game", _gotoCocosGame),

            _buildBtn("IntimacyRankPage", () {
              routerUtil.push(R_INTIMATE_RANKING, params: {P_ID: accountService.currentUidOfCache()});
            }),

            _buildBtn("IncomeTasksPage", () {
              routerUtil.push(R_USER_INCOME_TASK);
            }),

            _buildBtn("TaskCompleteOverlayType", () {
              // var pushTask = PbUserTaskNotice(tid: 1, taskType: '2', content: 'test', deeplink: '/user/income_task');
              var pushTask = PbTaskProgressNotice(
                  code: 'daily_6_chat_intimacy',
                  progress: $fixnum.Int64(20),
                  total: $fixnum.Int64(20),
                  content: 'content',
                  deeplink: 'winker://task/list',
                  rewardInfo: [
                    ///        element.statusObs.value = pb.status;
                    //         element.status = pb.status;
                    //         element.progress = toInt(pb.progress);
                    //         element.total = toInt(pb.total);
                    PbUserTaskRewardInfo(
                      rewardNum: '500',
                      rewardType: 'diamond',
                    )
                  ]);

              getService<AbsOverlayService>()?.show(GlobalOverlayType.taskComplete, model: pushTask);
            }),

            _buildBtn(
              "RoomMatch Dialog",
              () => showRoomMatchDialog(
                model: RoomNoActionMatchModel(
                  route: 'winker://live/room_join?id=337704368',
                  showContent: 'Distance 1.1 km',
                  userInfo: UserInfo(
                    uid: '338943306',
                    age: 29,
                    avatar: 'https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg',
                    avatarCode: 'male_11',
                    nickname: 'ZARA',
                    sexStr: 'male',
                  ),
                  distance: 1.1,
                  roomInfo: RoomInfoModel()
                    ..roomId = '337704368'
                    ..cover = 'https://res.winker.chat/nova/user/avatar/avatar_code/male_11.jpg'
                    ..name = 'ZARA',
                ),
                from: 'stay',
              ),
            ),

            _buildBtn("Recharge Dialog", () {
              showRechargeDialog(context: context, from: 'test');
            }),

            _buildBtn("Recharge Succeed Dialog", () {
              showRechargeSucceedDialog(
                  balance: 100, icon: 'https://res.winker.chat/nova/common/currency/diamond.png', source: 1);
            }),

            _buildBtn("Cancel Notification", () {
              getService<AbsNotificationService>()?.cancelNotification(cancelAll: true);
            }),

            Row(
              children: [
                SizedBox(width: 10.pt),
                Expanded(
                  child: TextField(
                    controller: _roomController,
                    textAlign: TextAlign.center,
                    onSubmitted: LiveRoomHandler.joinRoom,
                    decoration: InputDecoration(hintText: 'Input Room ID'),
                  ),
                ),
                TextButton(onPressed: () => LiveRoomHandler.joinRoom(_roomController.text), child: Text('Room')),
                SizedBox(width: 5.pt),
              ],
            ),

            _buildBtn("User Reg QA", () {
              routerUtil.push(R_USER_REG_QA);
            }),
            Row(
              children: [
                SizedBox(width: 10.pt),
                Expanded(
                  child: TextField(
                    textAlign: TextAlign.center,
                    controller: TextEditingController(text: _longitude),
                    decoration: InputDecoration(hintText: 'longitude'),
                    onChanged: (text) {
                      _longitude = text;
                    },
                  ),
                ),
                SizedBox(width: 10.pt),
                Expanded(
                  child: TextField(
                    textAlign: TextAlign.center,
                    controller: TextEditingController(text: _latitude),
                    decoration: InputDecoration(hintText: 'latitude'),
                    onChanged: (text) {
                      _latitude = text;
                    },
                  ),
                ),
              ],
            ),
            _buildBtn("release", () async {
              // rxUtil.send(MainPageShowEvent.release, 1);
              // DefaultCacheManager().store.emptyMemoryCache();

              final total = await commonService.getTotalMemory();
              final free = await commonService.getFreeMemory();

              Log.i("test", 'total -> $total, free -> $free');
            }),
            _buildBtn('ع͟ب͟د͟ا͟ل͟ق͟ا͟د͟'.replaceAll('͟', ''), () async {}),
            _buildBtn("😶‍🌫️🧔🏻‍♀️🧑🏿‍❤️‍🧑🏾🥲🥷🏿🐻‍❄️🧑🏻‍🦰🧑🏿‍🦯👩🏻‍🤝‍👩🏼🦩🦻🏿👩🏼‍🤝‍👩🏻", () {}),
            _buildBtn("familyGroupAtUserDialog", () async {
              showAtUserDialog(context: context, targetId: 'family_75420626');
            }),
            _buildBtn("toast", () async {
              toast(
                  'Test toast ${toastIndex++} @ This is a long text, used to test the scenario where the text is too long');
            }),
            _buildBtn("push room", () {
              DeepLink.jump('winker://live/room_join?id=34550893');
            }),
            _buildBtn("test api timeout", () async {
              var start = DateTime.now();
              var resp = await testApi.timeOut();
              var cost = DateTime.now().difference(start);
              Log.d('test', 'timeout cost=$cost return $resp');
              if (resp.isSuccess) {
                toast(LocaleStrings.instance.successfully);
              } else {
                toast(resp.msg ?? LocaleStrings.instance.defaultError);
              }
            }),
            _buildBtn("netWork error",
                () => getService<AbsOverlayService>()?.show(GlobalOverlayType.netWorkDisconnect, model: 0)),
            _buildBtn("match dialog", () async {
              var notice = MatchUser(
                uid: "33684012",
                avatarCode: "female_39",
                avatar: "https://novan.oss-ap-southeast-1.aliyuncs.com/nova/user/avatar/avatar_code/female_39.jpg",
                nickname: "wiee",
                matchContent: "test match",
              );
              showFateBellDialog(notice);
            }),

            _buildBtn("Live Msg", () async {
              var model = MainNotificationModel(
                type: MainNotificationType.imNewMsg,
                targetId: '336181295',
                push: PbBizPush(
                  pushEvent: PbPushEvent.PbPushEvent_JUMP,
                  data: PbPushDataNotice(
                    uiType: 1,
                    route: 'winker://live/room_join?id=338400101',
                    content: 'nice to meet you',
                    buttonContent: 'gogo',
                    fuserInfo: PbPushDataFUserInfo(
                        uid: "33684012",
                        avatarCode: "female_39",
                        avatar:
                            "https://novan.oss-ap-southeast-1.aliyuncs.com/nova/user/avatar/avatar_code/female_39.jpg",
                        nickname: "wiee",
                        onlineRoom: true,
                        relation: 'Friend'),
                  ).writeToBuffer(),
                ),
              );

              getService<AbsOverlayService>()?.show(GlobalOverlayType.imMsg, model: model);
            }),

            _buildBtn("test crash", () {
              var req = PbLocalBuglyLogReq.create()..level = PbLocalBuglyLogLevel.PbLocalBuglyLogLevel_TEST_CRASH;
              getService<AbsChannelMsgService>()?.send(PbLocalMsgType.PbLocalMsgType_BUGLY_LOG, req: req);
            }),
            _buildBtn("test flutter error", () {
              List? list;
              debugPrint("list 4-> ${list!.first}");
            }),
            _buildBtn("pop room", () {
              routerUtil.popUntil(R_MAIN);
            }),
            _buildBtn("permission dialog", () async {
              showRequestPermissionDialog(context: context, text: LocaleStrings.instance.micPermission);
            }),
            _buildBtn("webView outside", () => DeepLink.jump('https://www.baidu.com?jtype=out')),
            _buildBtn("quiz guide", () async {
              routerUtil.push(R_QUIZ_GUIDE);
            }),
            _buildBtn("userinfo", () async {
              routerUtil.push(R_USER_INFO, params: {P_UID: '135584078'});
            }),
            _buildBtn("improve userInfo", () async {
              UserInfoImproveManager.instance().jumpToImprove();
            }),
            _buildBtn("get all chat", () async {
              var chats = await imChatService.getChatList(types: [ChatType.group, ChatType.private]);
              var other = await imChatService.getConversationsFromTagByPage(imChatService.bottleTagId);
              other.length;
            }),
            _buildBtn("show evaluate", () {
              showEvaluateDialog(FLRouter.routeObserver.getLastContext(), EvaluateTrigger("view_userpage"));
            }),
            _buildBtn("test dialog", _testDialog),
            _buildBtn("send expression", () {
              var expression = PbImSticker(
                  id: "0",
                  name: "welcome",
                  categoryId: "0",
                  iconUrl:
                      "https://media0.giphy.com/media/XSymIYoe3xXSfea7BP/giphy.gif?cid=ecf05e47z2vu4cctetkzxeyjlloi36j2a952355ct18i0wct&rid=giphy.gif&ct=s",
                  oriUrl:
                      "https://media0.giphy.com/media/XSymIYoe3xXSfea7BP/giphy.gif?cid=ecf05e47z2vu4cctetkzxeyjlloi36j2a952355ct18i0wct&rid=giphy.gif&ct=s",
                  height: 480,
                  width: 480,
                  size: 3070089,
                  desc: "wel");

              imMsgService.sendMsg(
                  content: PbMsgContent(
                      PbCustomIm(type: PbCustomImType.PbCustomImType_STICKER, data: expression.writeToBuffer())),
                  targetId: "88204899");
            }),
            _buildBtn("voice vertify", () {
              routerUtil.push(R_VOICE_VERIFY);
            }),
            _buildBtn("nearby page", () {
              routerUtil.push(R_NEARBY);
            }),
            _buildBtn("vibrate transient", () async {
              await Vibration.vibrate(PbLocalVibratorType.PbLocalVibratorType_TRANSIENT);
            }),
            _buildBtn("vibrate continuous", () async {
              await Vibration.vibrate(PbLocalVibratorType.PbLocalVibratorType_CONTINUOUS);
            }),
            _buildSwitch(),
            EasterEgg(
              child: Text('EasterEgg'),
              onTriggered: () => routerUtil.push(R_EASTER_EGG),
            ),
            _buildBtn("Create Family", () {
              routerUtil.push(R_FAMILY_CREATE);
            }),
            _buildBtn("Family Messages", () {
              _showFamilyMessages();
            }),
            _buildBtn("Family Treasury", () {
              routerUtil.push(R_FAMILY_TREASURY);
            }),
            _buildBtn("Family Task", () {
              routerUtil.push(R_FAMILY_TASK);
            }),
            _buildBtn("Family Level", () {
              routerUtil.push(R_FAMILY_LEVEL);
            }),
            SizedBox(height: 30.pt),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityMessages() {
    final testActivities = [
      ActivityMsgContent(
        title: 'New Event Launch 🎉',
        coverUrl: 'https://picsum.photos/800/400',
        text: 'Join our exciting new event with amazing rewards!',
        link: 'https://example.com/event1',
        id: '1',
        activityId: 'act_001',
      ),
      ActivityMsgContent(
        title: null,
        // Testing without title
        coverUrl: 'https://picsum.photos/800/400',
        text: 'Special promotion without title',
        link: 'https://example.com/event2',
        id: '2',
        activityId: 'act_002',
      ),
      ActivityMsgContent(
        title: 'No Cover Image Test',
        coverUrl: '',
        // Testing without cover image
        text: 'This message tests the layout without a cover image',
        link: 'https://example.com/event3',
        id: '3',
        activityId: 'act_003',
      ),
      ActivityMsgContent(
        coverUrl: '',
        // Testing without cover image
        text: 'This message tests the layout without a cover image',
        link: 'https://example.com/event3',
        id: '3',
        activityId: 'act_003',
      ),
      ActivityMsgContent(
        coverUrl: '',
        // Testing without cover image
        text: 'This message tests the layout without a cover image',
        id: '3',
        link: '',
        activityId: 'act_003',
      ),
      ActivityMsgContent(
        title: 'No Cover Image Test',
        coverUrl: '',
        // Testing without cover image
        text: 'This message tests the layout without a cover image',
        link: '',
        id: '3',
        activityId: 'act_003',
      ),
      ActivityMsgContent(
        title: 'Long Content Test with Multiple Lines',
        coverUrl: 'https://picsum.photos/800/400',
        text: 'This is a very long description that should wrap to multiple '
            'lines to test how the widget handles longer content. It includes '
            'details about an imaginary event that has lots of information.',
        link: 'https://example.com/event4',
        id: '4',
        activityId: 'act_004',
      ),
    ];

    return Padding(
      padding: EdgeInsets.all(16.pt),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity Message Samples',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 16.pt),
          ...testActivities.map((activity) => Padding(
                padding: EdgeInsets.only(bottom: 16.pt),
                child: ActivityMsgItem(content: activity),
              )),
        ],
      ),
    );
  }

  Widget _buildBtn(String text, VoidCallback action) {
    return TextButton(onPressed: action, child: Text(text));
  }

  Widget _buildSwitch() {
    var spKey = "discount_test";
    bool open = globalSp.getBool(spKey, defaultValue: false) ?? false;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.pt, vertical: 25.pt),
          child: Text("discount test"),
        ),
        FlutterSwitch(
          width: 46.pt,
          height: 25.pt,
          activeColor: R.color.primaryColor,
          inactiveColor: const Color(0x63BAC4CD),
          toggleSize: 25.pt,
          value: open,
          onToggle: (value) {
            globalSp.setBool(spKey, value);
            if (mounted) {
              setState(() {});
            }
          },
        ),
      ],
    );
  }

  void _testDialog() {
    showAlertDialog(
        context: context,
        title: "test",
        content: "body",
        confirmText: 'OK',
        onConfirm: () {
          routerUtil.pop(context: context);
        });

    showAlertDialog(title: "test2", content: "body", confirmText: 'OK', onConfirm: () {});

    showAlertDialog(title: "test3", content: "body", confirmText: 'OK', onConfirm: () {});

    showAlertDialog(title: "test4", content: "body", confirmText: 'OK', onConfirm: () {});
  }

  String _envName() {
    if (_testConfig >= 0) {
      return '[${EnvConfig.values[_testConfig].name}]';
    }
    return '';
  }

  void _gotoCocosGame() async {
    Navigator.push(context, MaterialPageRoute(builder: (context) => TestCocosWebPage()));
  }

  /// 测试JsBridge 网页，放开yaml中assets/test/目录
  void _gotoJsBridgeDemo() async {
    String path = 'assets/test/jsbridge_demo.html';
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return TestCocosWebPage(url: path);
        },
      ),
    );
  }

  String _getRandomWelcomeMessage() {
    final messages = [
      'Welcome to the live stream!',
      'Warm welcome to our new friend!',
      'Welcome to our live stream family!',
      'Nice to meet you! Welcome!',
      'Here comes a new friend, welcome!',
      'Welcome to the live room!',
    ];
    return messages[Random().nextInt(messages.length)];
  }

  String _getRandomNickname() {
    final nicknames = [
      'Happy Traveler',
      'Sunshine Cutie',
      'Starwalker',
      'Smiling Angel',
      'Dream Chaser',
      'Happy Soul',
    ];
    return nicknames[Random().nextInt(nicknames.length)];
  }

  String _getRandomShowUid() {
    return (10000 + Random().nextInt(90000)).toString();
  }

  void _showFamilyMessages() {
    final examples = [
      // 1. 收到邀请
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 1,
        user: PbUser(
          uid: "123",
          nickname: "John",
          headimgurl: "https://picsum.photos/200"
        ),
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 2. 拒绝邀请
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 2,
        user: PbUser(
          uid: "124",
          nickname: "Mary",
          headimgurl: "https://picsum.photos/200"
        ),
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 3. 加入家族申请
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 3,
        user: PbUser(
          uid: "125",
          nickname: "Peter",
          headimgurl: "https://picsum.photos/200"
        ),
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 4. 创建家族成功
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 4,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 5. 通过家族审核加入家族
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 5,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 6. 拒绝家族审核
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 6,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),

      // 7. 逐出家族
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 7,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200",
      )),

      // 8. 任命职务
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 8,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200",
        // position: "Vice Leader"
      )),

      // 9. 家族升级
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 9,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200",
        level: 2
      )),

      // 10. 家族解散
      PbMsgContent(null, dataMsg: PbImFamilyAssistantNotify(
        type: 10,
        familyId: "family_1",
        familyName: "Happy Family",
        familyCover: "https://picsum.photos/200"
      )),
    ];

    showDialog(
      context: context,
      builder: (context) => Dialog(
        insetPadding: EdgeInsets.zero,
        child: Container(
          width: 1.w,
          height: 1.h,
          child: ListView.builder(
            itemCount: examples.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 8.pt),
                child: MsgContentFamily(
                  content: examples[index],
                  msgUid: "test_$index",
                  targetUser: UserInfo(
                    uid: "123",
                    nickname: "Test User",
                    avatar: "https://picsum.photos/200"
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
