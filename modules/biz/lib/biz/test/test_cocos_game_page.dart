import 'package:flutter/material.dart';
import 'package:service/modules/cocos_game/cocos_game_api.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

class TestCocosWebPage extends StatefulWidget {
  final String? url;

  const TestCocosWebPage({super.key, this.url});

  @override
  State<TestCocosWebPage> createState() => _TestCocosWebPageState();
}

class _TestCocosWebPageState extends State<TestCocosWebPage> {
  bool _soundSwitchSelected = true;

  final _controller = CocosViewController();

  int get camp => 1;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('WebJsPage')),
      body: _buildBody(),
    );
  }

  _buildBody() {
    return Column(
      children: [
        // Expanded(
        //   child: CocosGameWebView(
        //     url: widget.url ?? 'http://192.168.110.33:8080/emp/',
        //     controller: _controller,
        //     gameId: '100',
        //     camp: camp,
        //     backgroundColor: Colors.red,
        //     version: 1,
        //   ),
        // ),
        Wrap(
          spacing: 4,
          alignment: WrapAlignment.start, //沿主轴方向居中
          children: <Widget>[
            OutlinedButton(
              child: const Text("Join"),
              onPressed: _joinGame,
            ),
            OutlinedButton(
              child: const Text("Ready"),
              onPressed: () {
                _controller.readyGame(isReady: true);
              },
            ),
            OutlinedButton(
              child: const Text("cancelReady"),
              onPressed: () {
                _controller.readyGame(isReady: false);
              },
            ),
            OutlinedButton(
              child: const Text("Start"),
              onPressed: _controller.startGame,
            ),
            OutlinedButton(
              child: const Text("Leave"),
              onPressed: _controller.leaveGame,
            ),
            OutlinedButton(
              child: const Text("destroy"),
              onPressed: _controller.destroyGame,
            ),
            OutlinedButton(
              child: const Text("SoundStatus"),
              onPressed: () async {
                var status = await _controller.getGameSoundStatus();
              },
            ),
            OutlinedButton(
              child: const Text("ReLoad"),
              onPressed: _controller.reloadGame,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Switch(
                  value: _soundSwitchSelected, //当前状态
                  onChanged: (value) {
                    // _controller.setGameSoundStatus(value);
                    setState(() {
                      _soundSwitchSelected = value;
                    });
                  },
                ),
                const Text('Sound'),
              ],
            ),
          ],
        )
      ],
    );
  }

  /// 确保初始化完成后调用
  void _joinGame() async {
    var user1 = await userService.getUserInfo('339192390');
    var user2 = await userService.getUserInfo('338166672');

    GameUser? toGameUser(UserInfo? user) {
      if (user == null) return null;
      return GameUser(
        uid: user.uid,
        userName: user.displayedName,
        avatarUrl: user.avatar ?? '',
      );
    }

    var params = JoinGameParams(
      host1: toGameUser(user1),
      host2: toGameUser(user2),
    );

    _controller.joinGame(
      params: params,
      listener: (entity) {
        if (!mounted) return;
        if (entity.isSuccess) {
          LogExt.largeD(cocosTag, () => ' join game success! $params');
          _controller.joinCamp(
            camp: camp,
            listener: (entity) {
              if (!mounted) return;
              if (entity.isSuccess) {
                Log.d(cocosTag, ' joinCamp [$camp] success!');
              } else if (entity.code == GameErrorCode.SWITCH_CAMP_ERROR) {
                toast(LocaleStrings.instance.changeCampFailed, isDark: true);
              }
            },
          );
        }
      },
    );
  }
}
