import 'package:biz/biz.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/main/event.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/utils/time_range.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/remote_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/modules/task/model/task_list_entity.dart';
import 'package:service/common/statistics/newbie_pack_statistics.g.dart';
import 'package:service/utils/deep_link.dart';

const String _newbieDialogRouteName = "dialog/newbie_dialog";

/// 新手礼包
void showNewbieDialog({
  required TaskListItemEntity model,
  BuildContext? context,
  VoidCallback? onClosed,
  VoidCallback? onReceived,
}) {
  DialogScheduler.instance().schedule(() {
    return showDialog(
      (_) => _NewbieDialogWidget(
        model: model,
        onReceived: onReceived,
        onClosed: onClosed,
      ),
      barrierDismissible: false,
      context: context,
      routeSettings: dialogRouteSettings(_newbieDialogRouteName),
    );
  }, priority: 9999, uniqueKey: _newbieDialogRouteName);

  NewbiePackStatistics.reportNewbiePackImp();
}

class _NewbieDialogWidget extends StatelessWidget {
  final VoidCallback? onClosed;
  final VoidCallback? onReceived;
  final TaskListItemEntity model;

  const _NewbieDialogWidget({
    required this.model,
    this.onClosed,
    this.onReceived,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: Container(
          width: 315.pt,
          height: 565.pt,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                width: 315.pt,
                height: 447.pt,
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(19.pt),
                ),
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    FLImage.asset(Res.taskBgNewbieGradient, width: double.infinity, fit: BoxFit.cover),
                    Padding(
                      padding: EdgeInsets.fromLTRB(35.pt, 45.pt, 35.pt, 0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            LocaleStrings.instance.newbieTitle,
                            style: TextStyle(
                              color: R.color.color20,
                              fontSize: 13.sp,
                              fontWeight: FontWeightExt.book,
                            ),
                          ),
                          SizedBox(
                            height: 275.pt,
                            child: _giftGridView(),
                          ),
                          _buildReceivedButton(context),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 0.5,
                child: _headerWidget(),
              ),
              Positioned(
                top: 78,
                right: 0,
                child: _buildClose(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _headerWidget() {
    return SizedBox(
      height: (113 + 42).pt,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          FLImage.asset(Res.taskImgNewbieHeader, height: 113.pt, fit: BoxFit.cover),
          Positioned(
            bottom: 1,
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                FLImage.asset(Res.taskBgNewbieTitle, height: 42.pt, fit: BoxFit.cover),
                Positioned(
                  top: 5.pt,
                  child: Text(
                    LocaleStrings.instance.welcomeToWinker,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18.sp,
                      fontWeight: FontWeightExt.black,
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildReceivedButton(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        NewbiePackStatistics.reportNewbiePackClick();
        onReceived?.call();
        _dismiss(context);
        final flag = await _toOfficialRoom();
        // 无法跳转则到房间列表页
        if (!flag) _toRoomList();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          FLImage.asset(Res.taskBgNewbieRecevied, width: double.infinity, fit: BoxFit.cover),
          Text(
            LocaleStrings.instance.received,
            style: TextStyle(
              color: Colors.white,
              fontSize: 21.sp,
              fontWeight: FontWeightExt.heavy,
            ),
          ),
        ],
      ),
    );
  }

  Widget _giftGridView() {
    final rewards = model.rewards ?? [];
    return GridView.builder(
      padding: EdgeInsets.only(top: 10.pt),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 25.pt,
        childAspectRatio: 0.86,
      ),
      itemCount: rewards.length,
      itemBuilder: (context, index) {
        final item = rewards[index];
        return _giftItemWidget(item);
      },
    );
  }

  Widget _giftItemWidget(TaskListNewbieRewardsEntity model) {
    final expireTime = model.expireTime ?? 0;
    final day = expireTime != 0 ? '$expireTime ${model.expireUnit ?? ''}' : '';
    return Column(
      children: [
        Container(
          width: 110.pt,
          decoration: BoxDecoration(
            color: const Color(0xFFF2EEFF),
            borderRadius: BorderRadius.circular(10.pt),
            border: Border.all(color: const Color(0xFFC2ADFF), width: 1.pt),
          ),
          child: Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.antiAlias,
            children: [
              Column(
                children: [
                  GlobalWidgets.cachedNetImage(
                    imageUrl: model.icon ?? '',
                    width: 70.pt,
                    height: 70.pt,
                    fit: BoxFit.contain,
                    errorImage: SizedBox(width: 70.pt, height: 70.pt),
                  ),
                  Text(
                    'x${model.amount ?? 0}',
                    style: TextStyle(
                      color: R.color.color1F,
                      fontSize: 12.sp,
                      fontWeight: FontWeightExt.heavy,
                    ),
                  ),
                  3.hSpace
                ],
              ),
              Visibility(
                visible: day.isNotEmpty,
                child: Positioned(
                  top: -1.pt,
                  right: -1.pt,
                  child: _dateWidget(day),
                ),
              ),
            ],
          ),
        ),
        5.hSpace,
        Text(
          '${model.title ?? ''}',
          style: TextStyle(
            color: const Color(0xFF7C5DF6),
            fontSize: 13.sp,
            fontWeight: FontWeightExt.medium,
          ),
        ),
      ],
    );
  }

  Widget _dateWidget(String day) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.pt),
      decoration: BoxDecoration(image: DecorationImage(image: AssetImage(Res.taskBgNewbieDate), fit: BoxFit.contain)),
      child: Text(
        day,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12.sp,
          fontWeight: FontWeightExt.medium,
        ),
      ),
    );
  }

  Widget _buildClose(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _dismiss(context);
      },
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: EdgeInsets.all(7.pt),
        child: IgnorePointer(
          child: FLImage.asset(Res.taskIconNewbieClose, height: 15.pt, width: 15.pt),
        ),
      ),
    );
  }

  void _dismiss(BuildContext context) {
    onClosed?.call();
    routerUtil.pop(context: context);
  }

  void _toRoomList() {
    routerUtil.push(R_MAIN, params: {P_TAB_NAME: MainPageName.live.name});
  }

  Future<bool> _toOfficialRoom() async {
    final session = await getService<AbsRemoteConfigService>()?.getSection(sectionBase);
    if (session == null) return false;

    final map = session.getValue(functionNewbieGiftConfig)?.getAll();
    if (map == null) return false;

    final model = NwbieConfig.fromJson(map);

    final timeRange = model.registerGift?.timeRange;
    if (timeRange == null) return false;

    final officialRoomId = model.registerGift?.officialRoomId ?? '';
    if (officialRoomId.isEmpty) return false;

    final range = parseTimeRange(timeRange);
    if (range == null) return false;

    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final timeFix = await getService<AbsStimeService>()?.getTimeFix() ?? 0;
    final date = DateTime.fromMillisecondsSinceEpoch((now + timeFix) * 1000);

    final inRange = isTimeInRange(date, range);
    if (inRange == false) return false;

    return LiveRoomHandler.joinRoom(officialRoomId, from: "newbie_pack");
  }
}
