import 'dart:async';
import 'dart:convert';

import 'package:biz/biz.dart';
import 'package:biz/biz/share/model/const.dart';
import 'package:biz/biz/share/share_widget.dart';
import 'package:biz/biz/web/js/model/share_model.dart';
import 'package:biz/biz/web/js/module/abs_module.dart';
import 'package:biz/biz/web/js/module/data_module.dart';
import 'package:biz/biz/web/js/module/ui_module.dart';
import 'package:biz/biz/web/page/bloc/web_bloc.dart';
import 'package:biz/biz/web/widget/web_app_bar.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/common/widgets/ios_ignore_wrapper.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/play_animation_dialog/play_animation_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/live/room/model/room_video_model.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/value_parsers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../moment/widgets/moment_helper.dart';
import '../../share/share_other_app_widget.dart';
import 'report_model.dart';

class WebPageJsController {
  void Function(String module, String method, Map<String, dynamic>? data,
      void Function(dynamic data)? callback)? _onCallJs;

  void callJs(String module, String method, Map<String, dynamic>? data,
      void Function(dynamic data)? callback) {
    _onCallJs?.call(module, method, data, callback);
  }
}

@FRoute(desc: 'h5页面', url: R_WEB)
class WebPage extends StatefulWidget {
  final String? url;
  final bool? hideTitleBar;
  final bool? showRefresh;
  final VoidCallback? finishLoading;
  final List<IJSModule>? jsModule;
  final WebPageJsController? jsController;
  final Color? backgroundColor;
  final Color? titleBarColor;
  final Color? titleColor;
  final Color? backIconColor;

  /// 是否拦截Dismiss方法
  final bool? shouldInterceptDismiss;

  /// 拦截Dismiss方法后的回调
  final VoidCallback? onDismissIntercept;

  final bool isYoutube;

  final bool isDialog;

  const WebPage({
    Key? key,
    this.url,
    this.hideTitleBar,
    this.finishLoading,
    this.jsModule,
    this.jsController,
    this.isYoutube = false,
    this.showRefresh,
    this.shouldInterceptDismiss,
    this.onDismissIntercept,
    this.backgroundColor,
    this.titleBarColor,
    this.titleColor,
    this.backIconColor,
    this.isDialog = false,
  }) : super(key: key);

  @override
  State<WebPage> createState() => _WebPageState();
}

class _WebPageState extends State<WebPage> implements UIModuleCallBack {
  final _bloc = WebBloc();

  @override
  void initState() {
    super.initState();
    widget.jsController?._onCallJs = (String module,
        String method,
        Map<String, dynamic>? data,
        void Function(dynamic data)? callback) async {
      final resp = await _bloc.callJs(module, method, data);
      callback?.call(resp);
    };
    _bloc.jsBridge?.addJsModule(DataModule());
    _bloc.jsBridge?.addJsModule(UIModule(context, this));
    widget.jsModule?.forEach((element) {
      _bloc.jsBridge?.addJsModule(element);
    });

    /// 允许Web游戏播放声音
    muteGame(context, false, null);
  }

  @override
  Widget build(BuildContext context) {
    final url = widget.url ?? _formatUrl(context);
    final hideTitleBar = widget.hideTitleBar ?? _formatHideTitleBar(context);
    final showRefresh = widget.showRefresh ?? _showRefresh(context);
    final hideCloseButton = url.contains("hide_close=1");

    return LifecycleBlocBuilder<WebBloc, WebState>(
        bloc: _bloc,
        builder: (context, state) {
          return Scaffold(
            backgroundColor:
            hideTitleBar ? Colors.transparent : colorBackground,
            appBar: hideTitleBar
                ? (widget.isDialog
                ? CommonAppBar(
              backgroundColor: Colors.transparent,
              leading: SizedBox(),
              leadingWidth: 0,
              height: 1,
            )
                : null)
                : WebAppBar(
              backgroundColor: _titleBarColor(hideTitleBar),
              titleColor: _titleColor(),
              backIconColor: _backIconColor(),
              controller: _bloc.barController,
              webViewController: _bloc.webViewController,
              onBack: () => _onBack(context, hideCloseButton),
              actionRes: showRefresh ? Res.commonRefresh : null,
              // hideCloseButton: hideCloseButton,
              onMoreAction: showRefresh
                  ? () {
                _bloc.webViewController?.reload();
              }
                  : null,
            ),
            body: _body(context, url, state, hideTitleBar),
          );
        });
  }

  Widget _body(
      BuildContext context, String url, WebState state, bool hideTitleBar) {
    return Container(
      child: Stack(
        children: [
          Positioned.fill(
              child: _buildWebView(context, url, state, hideTitleBar)),
          Positioned(
            bottom: 16.pt,
            left: 40.pt,
            right: 40.pt,
            child: Visibility(
              visible: !widget.isYoutube && state.videoId?.isNotEmpty == true,
              child: SafeArea(
                child: GeneralBtn(
                  height: 50.pt,
                  backgroundColor: R.color.primaryColor,
                  title: LocaleStrings.instance.playThisVideo,
                  fontSize: 16.sp,
                  fontWeight: fontWeightBold,
                  onTap: () {
                    // routerUtil.pop(context: context, params: {
                    //   'type': '3',
                    //   'url':
                    //   'http://ozadj95fviptne.obs.ap-southeast-3.myhuaweicloud.com/tmp/recycle/1m/crawler/third_party/ok_855944071814/ok_855944071814.mp4'
                    // });
                    Log.d('test', 'video url -> ${state.videoId}');
                    routerUtil.pop(
                        context: context,
                        params: {'type': '1', 'url': state.videoId});
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _bloc.close();
    _bloc.dispose();
    Log.i("voice-match-task", "WebPage dispose");
    super.dispose();
  }

  void _onBack(BuildContext context, bool hideCloseButton) async {
    if (_bloc.isFromFile) {
      routerUtil.pop(context: context);
    } else if (true == await _bloc.webViewController?.canGoBack()) {
      if (hideCloseButton) {
        /// 前端需求：隐藏关闭按钮时，点击返回按钮关闭页面
        routerUtil.pop(context: context);
      } else {
        await _bloc.webViewController?.goBack();
      }
    } else {
      routerUtil.pop(context: context);
    }
  }

  /// 从穿参中取出url
  String _formatUrl(BuildContext context) {
    final args = Get.arguments;

    String url = '';

    if (args is Map && args[P_URL] is String) {
      url = args[P_URL] as String;
    }

    return url;
  }

  /// 是否隐藏标题栏
  bool _formatHideTitleBar(BuildContext context) {
    final args = Get.arguments;

    bool hide = false;
    if (args is Map && args[P_HIDE_TITLE] is String) {
      hide = args[P_HIDE_TITLE] == "true";
    }

    return hide;
  }

  /// 是否显示刷新
  bool _showRefresh(BuildContext context) {
    final args = Get.arguments;

    bool show = false;
    if (args is Map && args[P_SHOW_REFRESH] is String) {
      show = args[P_SHOW_REFRESH] == "true";
    }

    return show;
  }

  /// 视频选择
  bool _isVideo(BuildContext context) {
    final args = Get.arguments;

    bool show = false;
    if (args is Map && args[P_IS_VIDEO] is String) {
      show = args[P_IS_VIDEO] == "1";
    }

    return show;
  }

  @override
  void playRoomVideo(BuildContext context, String url, int type, String? title,
      List<RoomVideoItem> list) {
    routerUtil.pop(context: context, params: {
      'url': url,
      'type': type.toString(),
      'title': title ?? '',
      'list': list
    });
  }

  Future<void> _addAlert() async {
    try {
      var javascript = '''
      window.alert = function (e){
        Alert.postMessage(e);
      }
    ''';
      await _bloc.webViewController?.runJavascript(javascript);
    } catch (_) {}
  }

  /// webView
  Widget _buildWebView(
      BuildContext context, String url, WebState state, bool hideTitleBar) {
    bool isNet = url.toLowerCase().startsWith('http');
    final theUrl = isNet ? formatWebUrl(url) : '';
    return IosIgnoreWrapper(
      child: WebView(
        allowsInlineMediaPlayback: true,
        backgroundColor: _backgroundColor(hideTitleBar),
        initialUrl: theUrl,
        javascriptMode: JavascriptMode.unrestricted,
        onPageFinished: (url) async {
          _reportWebFinish(url);
          _bloc.recordLoadSuccess(theUrl);
          if (debugEnv) {
            await _addAlert();
          }
          if (mounted) _bloc.add(WebLoadFinishEvent(url));

          widget.finishLoading?.call();
        },
        onWebResourceError: (error) {
          _reportWebError(error);
          _bloc.recordLoadError(theUrl, error.description);

          _bloc.add(WebResourceErrorEvent(error));
        },
        onPageStarted: (ss) async {
          _bloc.recordLoadStart(ss);
          if (ss.isNotEmpty) {
            _reportWebBegin(ss);
          }
          Log.d('WebPage', 'url -> $ss');
        },
        navigationDelegate: (NavigationRequest request) {
          /// 对于需要拦截的操作 做判断
          if (_bloc.shouldInterceptNavigationRequest(request.url)) {
            DeepLink.jump(request.url);
            return NavigationDecision.prevent;
          }
          // else if (request.url.contains('youtube') && _isVideo(context) && Platform.isIOS){
          //
          //   Future.delayed(Duration(milliseconds: 300), (){
          //     if (mounted) {
          //       jump(context, request.url);
          //     }
          //   });
          //
          //   // DeepLink.jump(request.url);
          //   // routerUtil.push(R_WEB, params: {P_URL:request.url, P_SHOW_REFRESH: "true"}).then((value) {
          //   //   routerUtil.pop(context: context, params: value);
          //   // });
          //   return NavigationDecision.prevent;
          // }
          _bloc.add(WebOnProgressEvent(request.url));

          /// 不需要拦截的操作
          return NavigationDecision.navigate;
        },
        javascriptChannels: _bloc.jsSet,
        onProgress: (progress) async {
          _bloc.add(
              WebOnProgressEvent(await _bloc.webViewController?.currentUrl()));
        },
        onWebViewCreated: (controller) {
          _bloc.webViewController = controller;

          /// 清空缓存
          /// controller.clearCache();
          if (!isNet) {
            _loadHtmlFromAssets(url);
          }
        },
      ),
    );
  }

  /// 背景颜色
  Color? _backgroundColor(bool hideTitleBar) {
    if (widget.backgroundColor != null) return widget.backgroundColor;

    final args = Get.arguments;
    if (args is Map && args[P_BACKGROUND] is String) {
      final colorInt = int.parse(dynamicToString(args[P_BACKGROUND]));
      return Color(colorInt);
    }
    return hideTitleBar ? const Color(0xFF120C33).withOpacity(0.9) : null;
  }

  Color? _titleBarColor(bool hideTitleBar) {
    if (widget.titleBarColor != null) return widget.titleBarColor;
    final args = Get.arguments;
    if (args is Map && args[P_TITLE_BAR_COLOR] is String) {
      final colorInt = int.parse(dynamicToString(args[P_TITLE_BAR_COLOR]));
      return Color(colorInt);
    }
    return hideTitleBar ? const Color(0xFF120C33).withOpacity(0.9) : null;
  }

  Color? _titleColor() {
    if (widget.titleColor != null) return widget.titleColor;
    final args = Get.arguments;
    if (args is Map && args[P_TITLE_COLOR] is String) {
      final colorInt = int.parse(dynamicToString(args[P_TITLE_COLOR]));
      return Color(colorInt);
    }
    return null;
  }

  Color? _backIconColor() {
    if (widget.backIconColor != null) return widget.backIconColor;
    final args = Get.arguments;
    if (args is Map && args[P_BACK_ICON_COLOR] is String) {
      final colorInt = int.parse(dynamicToString(args[P_BACK_ICON_COLOR]));
      return Color(colorInt);
    }
    return null;
  }

  /// UIModuleCallBack
  @override
  void dismiss(BuildContext context, String? url) {
    if (widget.shouldInterceptDismiss ?? false) {
      widget.onDismissIntercept?.call();
      return;
    }

    String? jumpUrl;
    Map<String, dynamic>? jumpParams;
    if (url != null && url.isNotEmpty) {
      if (url.startsWith(DeepLink.deepLinkProto)) {
        try {
          /// 取出url中携带的参数，传递到WebView
          Uri uri = Uri.parse(url);
          if (uri.queryParameters.isNotEmpty) {
            jumpParams = {};
            for (String key in uri.queryParameters.keys) {
              jumpParams[key] = uri.queryParameters[key];
            }
            jumpUrl = "/${uri.host}${uri.path}";
          } else {
            jumpUrl = url.replaceFirst(DeepLink.deepLinkProto, '/');
          }
        } on Exception catch (e) {
          Log.e('UIModuleCallBack', "uri queryParameters error: $e");
          jumpUrl = url.replaceFirst(DeepLink.deepLinkProto, '/');
        }
      } else if (url.toLowerCase().startsWith('http')) {
        /// 判断跳外部
        if (url.toLowerCase().contains('jtype=out')) {
          launch(url);
        } else {
          jumpUrl = R_WEB;
          jumpParams = {P_URL: url};
        }
      } else if (url.startsWith('/')) {
        jumpUrl = url;
      }
    }

    if (jumpUrl != null && jumpUrl.isNotEmpty) {
      if (jumpUrl.contains(R_MAIN)) {
        /// 跳转到主页使用popAndPush会导致底部栏按钮没刷新
        routerUtil.push(jumpUrl, params: jumpParams, context: context);
      } else {
        routerUtil.popAndPush(jumpUrl, params: jumpParams, context: context);
      }
    } else {
      routerUtil.pop(context: context);
    }

    /// 关闭Web游戏时将游戏静音
    muteGame(context, true, null);
  }

  @override
  void share(BuildContext context, ShareModel share, void Function(bool reslut, String type, String? target) callback) {
    showShareDialog(
      title: LocaleStrings.instance.share,
      context: context,
      showFriends: false,
      extraApp: share.showMoment == true
          ? OtherAppInfo(LocaleStrings.instance.moment, Res.commonMoment, ShareApps.extra, extraAppCallback: () async {
              bool shareMomentSuc = await shareMomentFromShareModel(share);
              callback.call(shareMomentSuc, "moment", null);
            })
          : null,
      shareWidgetType: share.showContacts == true ? ShareWidgetType.withContact : ShareWidgetType.normal,
      shareResultCallback: (type, result, target) {
        String resultType = '';

        /// fb/ins/whatsapp/more/im
        switch (type) {
          case ShareToType.facebook:
            resultType = 'fb';
            break;
          case ShareToType.ins:
            resultType = 'ins';
            break;
          case ShareToType.whatApp:
            resultType = 'whatsapp';
            break;
          case ShareToType.more:
            resultType = 'more';
            break;
          case ShareToType.friends:
            resultType = 'im';
            break;
          default:
            break;
        }

        callback.call(result, resultType, target);
      },
      template: CommandShare(
          content: share.title ?? '',
          link: share.url ?? '',
          icon: share.deepLinkIcon ?? share.picture ?? '',
          image: share.picture,
          deepLink: share.deepLink),
    );
  }

  @override
  void jump(BuildContext _context, String url) {
    if (_isVideo(context) && url.contains('youtube')) {
      _bloc.webViewController?.loadUrl(url);
      // routerUtil.push(R_WEB, params: {P_URL:url}).then((resp) {
      //   if (resp is Map<String, dynamic>) {
      //     final type = resp['type'];
      //     final url = resp['url'];
      //     final title = resp['title'];
      //     final list = resp['list'];
      //
      //     Log.i("video_chose", 'type-> $type  url-> $url list-> $list');
      //
      //     /// 有效的选择
      //     if (type is String && (url is String || url == null)) {
      //       if (mounted) {
      //         routerUtil.pop(context: context, params: resp);
      //       }
      //     }
      //   }
      // });
    } else {
      DeepLink.jump(url);
    }
  }

  _loadHtmlFromAssets(String url) async {
    _bloc.isFromFile = true;
    String fileHtmlContents = await rootBundle.loadString(url);

    _bloc.webViewController?.loadUrl(Uri.dataFromString(fileHtmlContents,
        mimeType: 'text/html', encoding: Encoding.getByName('utf-8'))
        .toString());
  }

  @override
  void playMp4(
      BuildContext _context, String url, int vapVersion, bool showCloseBtn) {
    playAnimationDialog(
        vapUrl: url, vapVersion: vapVersion, showCloseBtn: showCloseBtn);
  }

  @override
  void playSvga(BuildContext _context, String url, bool showCloseBtn) {
    playAnimationDialog(svgaUrl: url, showCloseBtn: showCloseBtn);
  }

  @override
  void getContactUser(Function(UserInfo?) selectCallback, bool oppositeSex, String? btnTitle) {
    /// 分享窗
    showShareDialog(
      context: FLRouter.routeObserver.getLastContext(),
      title: "",
      contactBtnTitle: btnTitle,
      showFriends: false,
      showApps: false,
      shareWidgetType: ShareWidgetType.withContact,
      margin: EdgeInsets.symmetric(horizontal: 15.pt),
      oppositeSex: oppositeSex,
      contactSelectCallback: selectCallback,
      template: CommandShare(content: ''),
    );
  }

  @override
  void playSound(BuildContext _context, String url, int times, bool isPlay) {
    mediaService.playEffect(isPlay, url ?? '', times);
  }

  @override
  void muteGame(BuildContext _context, bool mute, String? gameName) {
    mediaService.setGameMute(mute, gameName: gameName);
  }

  @override
  void setTitle(BuildContext _context, String title) {
    _bloc.barController.setTitle(title);
  }

  /// 性能打点
  ReportModel? _reportModel;
  void _reportWebBegin(String url) {
    _reportModel = ReportModel(url, DateTime.now().millisecondsSinceEpoch);
  }

  void _reportWebError(WebResourceError error) {
    // ft_web_load
    // url=H5网址
    // duration=从加载到load结束的时长
    // code=0：成功||其他：错误码
    // msg=错误信息
    if (_reportModel != null) {
      getService<AbsStatisticsService>()?.report('ft_web_load', params: {
        'url': _reportModel!.url,
        'code': '${error.errorCode}',
        'msg': error.description,
        'duration':
        '${DateTime.now().millisecondsSinceEpoch - _reportModel!.time}',
      });
      _reportModel = null;
    }
  }

  void _reportWebFinish(String url) {
    if (_reportModel != null) {
      getService<AbsStatisticsService>()?.report('ft_web_load', params: {
        'url': url,
        'code': '0',
        'duration':
        '${DateTime.now().millisecondsSinceEpoch - _reportModel!.time}',
      });
      _reportModel = null;
    }
  }
}
