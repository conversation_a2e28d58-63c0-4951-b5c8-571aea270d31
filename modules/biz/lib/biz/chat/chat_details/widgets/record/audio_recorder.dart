import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/record/record_btn.dart';
import 'package:biz/biz/chat/chat_details/widgets/record/recorder_mixin.dart';
import 'package:biz/biz/chat/chat_details/widgets/record/wave_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/permission/permission_util.dart';
import 'package:service/modules/call/const/events.dart';
import 'package:service/service.dart';
import 'package:service/utils/global_audio_player.dart';

class AudioRecorder extends StatefulWidget {
  const AudioRecorder({
    Key? key,
    this.onRecordFinished,
    required this.fakeRecordBtnController,
    this.recordClick,
  }) : super(key: key);
  final Function(String?, int, bool)? onRecordFinished;
  final FakeRecordBtnController fakeRecordBtnController;
  final VoidCallback? recordClick;

  @override
  _AudioRecorderState createState() => _AudioRecorderState();
}

class _AudioRecorderState extends State<AudioRecorder> with RecorderMixin {
  static const String _tag = 'AudioRecorder';
  static final double _sectorHeight = 48.pt;
  final GlobalKey _btnKey = GlobalKey();
  bool _isCancel = false;
  bool _isPermissionGranted = false;
  StreamSubscription? _callStatusSub;

  double get _panelHeight {
    if (mounted) {
      return 190.pt + MediaQuery.of(context).padding.bottom;
    }
    return 190.pt;
  }

  @override
  void onCountdownFinished() {
    super.onCountdownFinished();
    _finishRecord(false);
  }

  void _onLongPress() async {
    GlobalAudioPlayer.instance().stop();
    if (!_isPermissionGranted) return;
    if (isWaiting) return;
    if (await callService.checkIsInCall()) {
      toast(LocaleStrings.instance.inCallNotice);
    } else {
      startRecording();
      widget.recordClick?.call();
    }
  }

  void _onPointerUp(PointerUpEvent event) {
    _finishRecord(!_isHoverPanel(event.position));
  }

  void _finishRecord(bool cancel) {
    if (isRecording) {
      stopRecording(cancel);
    }
  }

  void _onPointerCancel(_) {
    _finishRecord(true);
  }

  void _onPointerMove(PointerMoveEvent event) {
    if (isRecording) {
      if (mounted) {
        setState(() {
          _isCancel = !_isHoverPanel(event.position);
        });
      }
    }
  }

  bool _isHoverPanel(Offset position) {
    return !_isHover(
      position: position,
      start: Offset.zero,
      end: Offset(1.w, 1.h - _panelHeight + _sectorHeight / 2),
    );
  }

  bool _isHover({
    required Offset position,
    required Offset start,
    required Offset end,
  }) {
    return position.dx >= start.dx &&
        position.dx <= end.dx &&
        position.dy >= start.dy &&
        position.dy <= end.dy;
  }

  void _onBtnPressing(bool isPressing) {
    widget.fakeRecordBtnController.setPressing(isPressing);
    if (isPressing && !_isPermissionGranted) _requestPermission();
  }

  Future<bool> _requestPermission() async {
    String content = ReportAuthType.microphone;
    String page = ReportAuthPage.chatVoice;

    var result = await requestPermission(context,
        type: PermissionType.microphone, authContent: content, authPage: page);
    _isPermissionGranted = result;
    return result;
  }

  @override
  void initState() {
    super.initState();
    _callStatusSub =
        rxUtil.observer(CallStatusEvent.ringing).listen(_onRinging);
    _preloadPermissionStatus();
  }

  void _preloadPermissionStatus() async {
    _isPermissionGranted = await Permission.microphone.isGranted;
  }

  void _onRinging(_) {
    Log.i(_tag, 'cancel recording');
    _finishRecord(true);
  }

  @override
  void dispose() {
    _callStatusSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      behavior: HitTestBehavior.opaque,
      // onPointerDown: _onPointerDown,
      onPointerUp: _onPointerUp,
      onPointerMove: _onPointerMove,
      onPointerCancel: _onPointerCancel,
      child: Opacity(
        opacity: isRecording ? 1 : 0,
        child: Material(
          color: const Color(0xB3121332),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [_cancelMask(), _recordPanel()],
          ),
        ),
      ),
    );
  }

  Widget _cancelMask() {
    if (!isRecording) return SizedBox.shrink();
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _countdownText(),
          GlobalWidgets.spacingVertical(15.pt),
          _waveChip(),
          GlobalWidgets.spacingVertical(70.pt),
          _deleteBtn(),
          GlobalWidgets.spacingVertical(36.pt),
        ],
      ),
    );
  }

  Widget _countdownText() {
    if (!showCountdown) return SizedBox.shrink();
    final s = LocaleStrings.instance;
    return RichText(
      text: TextSpan(
        style: TextStyle(color: colorTextLight, fontSize: 15.pt),
        children: [
          TextSpan(text: '${s.recordingWillStop} '),
          TextSpan(text: '$countdown', style: TextStyle(fontSize: 20.pt)),
        ],
      ),
    );
  }

  Widget _waveChip() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7.pt, horizontal: 40.pt),
      decoration: BoxDecoration(
        color: R.color.primaryColor,
        borderRadius: BorderRadius.circular(27.pt),
      ),
      child: WaveWidget(
        size: Size(114.pt, 40.pt),
        amplitudePercents: amplitudes,
      ),
    );
  }

  Widget _deleteBtn() {
    return Container(
      decoration: BoxDecoration(
        color: _isCancel ? Colors.white : const Color(0x61121332),
        shape: BoxShape.circle,
      ),
      padding: EdgeInsets.all(13.pt),
      child: FLImage.asset(
        Res.commonDelete,
        width: 24.pt,
        height: 24.pt,
        color: _isCancel ? const Color(0xFF121332) : Colors.white,
      ),
    );
  }

  Widget _recordPanel() {
    return ConstrainedBox(
      constraints: BoxConstraints.expand(height: _panelHeight),
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          _panelBg(),
          RealRecordBtn(
            btnKey: _btnKey,
            text: LocaleStrings.instance.swipeUpToCancel,
            onLongPress: _onLongPress,
            onPressing: _onBtnPressing,
          ),
        ],
      ),
    );
  }

  Widget _panelBg() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FLImage.asset(
          Res.chatSector,
          width: 1.w,
          height: _sectorHeight,
          fit: BoxFit.fill,
        ),
        Container(
          constraints: BoxConstraints.expand(
            height: _panelHeight - _sectorHeight,
          ),
          color: Colors.white,
        ),
      ],
    );
  }
}
