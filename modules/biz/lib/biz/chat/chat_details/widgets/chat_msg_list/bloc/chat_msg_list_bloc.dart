import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:biz/biz/chat/chat_details/bloc/chat_bloc.dart';
import 'package:biz/biz/chat/chat_details/widgets/msg_content/msg_extra_earnings_widget.dart';
import 'package:biz/biz/chat/chat_details/widgets/punch_game/punch_game_controller.dart';
import 'package:biz/biz/share/share_guid_dialog.dart';
import 'package:biz/biz/share/share_guid_handle.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:rect_getter/rect_getter.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/chat_detail_statistic_handler.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/remote_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/global/db/db_util.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/bottle/bottle_utils.dart';
import 'package:service/modules/im/const/const.dart';
import 'package:service/modules/im/const/enums.dart';
import 'package:service/modules/im/const/events.dart' as im;
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/bottle_msg_content.dart';
import 'package:service/modules/im/model/db_msg.dart';
import 'package:service/modules/im/model/group_info.dart';
import 'package:service/modules/im/model/icebreaker_msg_content.dart';
import 'package:service/modules/im/model/im_gift_message_content.dart';
import 'package:service/modules/im/model/msg.dart';
import 'package:service/modules/im/model/msg_content.dart';
import 'package:service/modules/im/model/system_msg_content.dart';
import 'package:service/modules/im/rong_cloud/convert/const_converter.dart';
import 'package:service/modules/im/rong_cloud/model/msg_send_status.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/pb/net/pb_system.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/value_parsers.dart';

import '../../emoji_rain_widget.dart';
import '../../screenshot_alert_dialog.dart';
import 'chat_msg_list_report_mixin.dart';

part 'chat_msg_list_event.dart';
part 'chat_msg_list_state.dart';

class ChatMsgListBloc extends PageVisibilityBloc<ChatMsgListEvent, ChatMsgListState>
    with ChatMsgListReportMixin, PageTimingMixin {
  ChatMsgListBloc(this.chatBloc, this.targetId, this.matchType, this.from, this.scrollController,
      this.emojiRainWidgetController, this.keyword, this.highlightMsg,
      {this.onHasValidMsgChange,
      required this.getxTag,
      this.chatSendMsg,
      this.isBottleChat = false,
      this.isGroupChat = false,
      this.needSendGiftUnlock = false,
      this.roomId,
      this.roomOwnerId})
      : super(ChatMsgListState(), needClose: false) {
    on<LoadMsgListEvent>(_onLoadMsgList);
    on<RefreshChatMsgListEvent>(_onRefreshChatMsgList);
    on<ChatEventResend>(_onResendMsg);
    on<RefreshPageEvent>(_onRefreshPage);
    on<ChatListScrollEndEvent>(_onListScrollEnd);
    on<ChatEventUnreadMsgNavTap>(_onNavToFirstUnreadMsg);
    on<ChatLongPressMsgEvent>(_onChatLongPressMsg);
    on<NoMoreMsgEvent>(_onNoMoreMsg);
  }

  static const Duration _scrollTime = Duration(milliseconds: 200);
  final String _tag = "ChatMsgListBloc";

  final ChatBloc chatBloc;
  final String targetId;
  final String matchType;
  final String from;
  final bool isBottleChat;
  final bool isGroupChat;
  final AutoScrollController scrollController;
  final EmojiRainWidgetController emojiRainWidgetController;
  final String getxTag;
  final Function(bool)? onHasValidMsgChange;
  final Function(MsgContent, {String? pushContent})? chatSendMsg;
  bool needSendGiftUnlock;
  String? roomId;
  String? roomOwnerId;

  bool get isAssistance => targetId.length == assistanceLength;
  bool _insertingIcebreaker = false;

  /// 一页消息数
  final int size = 20;

  /// 最大消息数
  final int maxMsgSize = 500;

  final List<Msg> _missedStatusMsgList = [];
  final Map<int, RectGetter> rectGetters = {};

  /// 在初始化过程中接收到的消息可能会无法显示，需要处理
  /// 初始化过程中接收到的消息
  final List<Msg> _missedMsgList = [];

  /// 高亮的关键词
  final String? keyword;

  /// 高亮的消息
  final Msg? highlightMsg;

  /// 群聊第一条未读消息
  Msg? firstUnreadMsg;

  ///用于存im消息item key
  final Map<int, GlobalKey<RectGetterState>> valueKeys = {};

  bool _isInitList = true;

  /// 融云消息已初始化查询
  bool _rcMsgInitLoaded = false;

  ChatType get chatType => isGroupChat ? ChatType.group : ChatType.private;

  /// 打点参数
  String get chatFromType => isBottleChat ? "bottle" : "im";

  /// 已经检查插入完善bio消息
  bool hasCheckInsertCompleteBio = false;

  int _maxFollowGuideCount = 0;

  final Set _msgBlockedSet = {};

  PunchGameController get punchGameController => Get.find<PunchGameController>(tag: getxTag);

  final _pref = Preferences.newInstance(spChat);

  bool hasShowGiftGuide = false;

  /// 群聊新入群成员最多查看多久前的历史消息
  final _defaultInterval = 3 * 24 * 3600;
  int joinGroupMaxHistoryMsgInterval = 0;
  /// 云控配置是否需要做限制
  bool needLimit = false;

  /// 获取历史消息一页的数量，如果是群聊新入群成员最多查看100条历史消息
  int pageCount(GroupInfoModel? info) {
    if (!isGroupChat) {
      return size;
    }

    if (needLimit == false) return size;

    final groupInfo = info ?? state.groupInfo;
    int joinTime = (groupInfo?.joinTime ?? 0) * 1000;
    if (joinTime <= 0) return size;
    joinTime -= joinGroupMaxHistoryMsgInterval * 1000;
    /// 大于加入家族多久前的消息不再拉取
    for(Msg msg in state.msgList?.reversed ?? []) {
      if (msg.sentTime < joinTime) {
        return 0;
      }
    }

    return size;
  }

  @override
  void initBloc() {
    super.initBloc();
    checkGroupChatConfig();
    add(LoadMsgListEvent());
    final time = Duration(milliseconds: 500);
    listenRxStream<List<Msg>>(rxUtil.observerStream<Msg>(MsgEvent.newMsgReceived).bufferTime(time), _onNewMsgReceived);
    listenRxEvent<Msg>(im.ChatEvent.msgListHandleNewMsg, _handleMsgSent);

    listenRxEvent<RCIMIWBlockedMessageInfo?>(MsgEvent.msgBlocked, _onMessageBlocked);

    if (!isGroupChat) {
      listenRxEvent<String>(im.SendGiftUnlockChatEvent.show, (userId) {
        if (userId == targetId) {
          needSendGiftUnlock = true;
        }
      });
    }
    chatBloc.chatPageController.lastStarLightCallback = getLastOtherMsgStarLightVal;
  }

  void checkGroupChatConfig() async {
    if (isGroupChat == false) return;
    final section =
        await getService<AbsRemoteConfigService>()?.getSection(sectionAppUi);
    final value = section?.getValue('family_chat_config');
    needLimit = value?.getBool("limit") ?? false;
    joinGroupMaxHistoryMsgInterval = value?.getInt("interval") ?? _defaultInterval;
  }

  /// 加载一次消息列表后监听事件
  void _observerEvent() {
    _isInitList = false;
    final time = Duration(milliseconds: 500);
    listenRxStream<List<Msg>>(rxUtil.observer<Msg>(MsgEvent.sentStatusChanged).bufferTime(time), _onSentStatusChanged);
    listenRxEvent<String>(im.ChatEvent.historyCleared, _onHistoryCleared);
    listenRxEvent<String>(im.MsgEvent.readReceiptReceived, _onReadReceiptReceived);
    listenRxEvent<List<int>>(im.MsgEvent.msgDeleted, _deleteMsg);
    listenRxEvent<MsgSendByEventModel>(im.MsgEvent.send, _sendByEvent);

    /// 私聊需要监听的事件
    if (!isGroupChat) {
      listenRxEvent<String>(UserRelationEvent.beFollowed, _onBeFollowed);
      final rxMerge = Rx.merge([
        rxUtil.observer<String>(UserRelationEvent.follow),
        rxUtil.observer<String>(UserRelationEvent.unFollow),
        rxUtil.observer<String>(UserRelationEvent.beUnFollow),
      ]);
      listenRxStream(rxMerge.throttleTime(time, trailing: true, leading: false), _onUserInfoUpdated);
    }

    if (isBottleChat || !isGroupChat) {
      /// 截屏事件
      listenRxEvent(ScreenEvent.captured, (_) => _onScreenCaptured(true));
      listenRxEvent(ChatPageEvent.screenCaptured, (_) => _onScreenCaptured(false));
      listenRxEvent<Map<String, UserInfo>>(UserStatusEvent.updateDetailMap, (Map<String, UserInfo> map) {
        if (map.keys.contains(targetId)) {
          _onUserInfoUpdated(targetId);
        }
      });

      listenRxEvent<List<String>>(UserStatusEvent.updateUsers, (List<String> uids) {
        if (uids.contains(targetId)) {
          _onUserInfoUpdated(targetId);
        }
      });
    }
  }

  Future<void> _loadUserInfo(String targetId) async {
    final uid = isBottleChat ? imChatService.getBottleTargetUserIdByTargetId(targetId) : targetId;

    final targetUser = await userService.getUserInfo(uid);
    final currentUser = await userService.getCurrentUserInfo();
    add(RefreshPageEvent(state.clone(
      targetUser: targetUser,
      currentUser: currentUser,
    )));
  }

  Future<void> _onLoadMsgList(LoadMsgListEvent event, Emitter<ChatMsgListState> emit) async {
    List<Msg> msgList;
    final chat = await imChatService.getChat(targetId, chatType);
    final chatTargetId = isBottleChat ? getBottleChatTargetUid(targetId) : targetId;
    UserInfo? targetUser;
    GroupInfoModel? groupInfo;
    if (isGroupChat && !isBottleChat) {
      groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    } else {
      targetUser = await userService.getUserInfo(chatTargetId, fromServer: true);
    }
    final currentUser = await userService.getCurrentUserInfo();
    final initialUnreadCount = chat?.unreadMsgCount ?? 0;
    _maxFollowGuideCount = await socialService.maxFollowGuideCount();

    bool showGreeting = false;
    final key = spChatShowGreetingTargetId(targetId);

    /// 融云消息初始化查询已开始
    _rcMsgInitLoaded = true;
    if (highlightMsg != null) {
      msgList = await imMsgService.getMsgListByTime(
        chatType: chatType,
        targetId: targetId,
        sentTime: highlightMsg!.sentTime,
        beforeCount: max(initialUnreadCount, size),
        afterCount: 150,
      );
    } else {
      msgList = await imMsgService.getMsgList(
        chatType: chatType,
        targetId: targetId,
        count: isGroupChat ? pageCount(groupInfo) : max(initialUnreadCount, size),
      );
    }
    if (!isGroupChat) {
      final lastMsgs = await imMsgService.getMsgListByTime(
        targetId: targetId,
        chatType: chatType,
        sentTime: 1,
        beforeCount: 1,
        afterCount: 1,
      );
      if (msgList.isEmpty && lastMsgs.isEmpty) {
        showGreeting = !(_pref.getBool(key) ?? false);
        ChatDetailReporter.reportGreetingImp(from, matchType, targetId);
      }

      if (!lastMsgs.any((e) => e.content is IcebreakerMsgContent)) {
        msgList = _insertIcebreaker(msgList);
      } else {
        Msg? icebreakerMsg = msgList.firstWhereOrNull((e) => e.content is IcebreakerMsgContent);
        if (icebreakerMsg != null) {
          IcebreakerMsgContent icebreakerContent = icebreakerMsg.content as IcebreakerMsgContent;
          if (icebreakerContent.version != IcebreakerMsgContent.currentVersion) {
            msgList.remove(icebreakerMsg);
            await imMsgService.deleteLocalMsg([icebreakerMsg]);
            msgList = _insertIcebreaker(msgList);
          }
        }
      }
    }

    _checkExistReceivedMsg(msgList);
    
    final newState = state.clone(
      msgList: _filterMsgList(msgList),
      targetUser: targetUser,
      groupInfo: groupInfo,
      showUnreadList: false,
      hasMore: false,
      currentUser: currentUser,
      showGreeting: showGreeting,
    );

    // 隐藏猜拳入口
    // punchGameController.onInitPunchGameState(isNewChat: !newState.hasUserMsg);
    // Get.find<ChatQuickActionController>(tag: getxTag)
    //     .onWinkActionInit(isNewChat: !newState.hasUserMsg && !showGreeting);

    emit(newState);

    if (showGreeting) {
      _pref.setBool(key, true);
    }

    chatBloc.emit(chatBloc.state.clone(showGreeting: showGreeting));

    /// 非机器人，有未读消息
    if (targetUser?.isRobot != true && initialUnreadCount > 0 && !isAssistance) {
      _checkReplyIntervalTime(msgList);
    }

    /// 显示state.msgList后，检查是否有未处理的消息
    WidgetUtils.post((_) {
      if (_missedMsgList.isNotEmpty && state.msgList != null) {
        _onNewMsgReceived(_missedMsgList.toList());
        _missedMsgList.clear();
      }
    });
    if (_isInitList) _observerEvent();

    /// 普通群聊有未读消息
    if (initialUnreadCount > 0 &&
        isGroupChat &&
        !isBottleChat &&
        (newState.msgList?.length ?? 0) < initialUnreadCount) {
      firstUnreadMsg = await imMsgService.getFirstUnreadMessage(targetId: targetId, chatType: ChatType.group);
    }

    handleHasValidMsgChange(newState.hasValidMsg);
    _positionToHighlight();
    _checkIfShowUnreadNavChip(initialUnreadCount, emit);

    if (msgList.isNotEmpty) {
      emojiRainWidgetController.onReceived(msgList);
      onReportLoadMsgList(msgList);
      _handleUnreadStatus(targetId, msgList);
    }

    if (isBottleChat) {
      await _insertBottleCard(emit);
    }
  }

  ///检查是否存在对方回复的真实消息
  void _checkExistReceivedMsg(List<Msg> msgList) {
    for (var element in msgList) {
      if (!element.fromSelf && element.content?.isRealMsg == true) {
        rxUtil.send(im.ChatEvent.showOnlineStatus, true);
      }
    }
  }

  void handleHasValidMsgChange(bool hasValidMsg) {
    onHasValidMsgChange?.call(hasValidMsg);
  }

  void _onRefreshChatMsgList(RefreshChatMsgListEvent event, Emitter<ChatMsgListState> emit) {
    var newState = state.clone(msgList: event.msgList);
    emit.call(newState);
    handleHasValidMsgChange(newState.hasValidMsg);
  }

  Future<void> onMsgListLoadMore(bool isLeading) async {
    // 如果最后一项是破冰消息，拉下一页数据拿倒二的消息，避免破冰消息插错页的情况下拉不到前面的消息
    // 这样可能导致下一页还有这一条破冰消息，这个会在filter方法中过滤掉
    Msg? anchor;

    /// 由于使用reverse,所以 isLeading false 向上加载旧消息，true 向下加载新消息
    if (isLeading) {
      anchor = state.msgList?.firstWhereOrNull((e) => e?.content?.isRealMsg == true);
    } else {
      anchor = state.msgList?.lastWhereOrNull((e) => e?.content?.isRealMsg == true);
    }
    if (targetId.isEmpty || anchor == null) return null;
    final count = pageCount(null);
    final list = await imMsgService.getMsgListByTime(
      chatType: chatType,
      targetId: targetId,
      sentTime: anchor.sentTime + 1,
      beforeCount: isLeading ? 0 : count,
      afterCount: isLeading ? count : 0,
    );

    if (list.isEmpty) {
      return;
    }

    /// 去重
    for (int i = list.length - 1; i >= 0; i--) {
      Msg msg = list[i];
      final index = state.msgList?.indexOf(msg) ?? -1;
      if (index >= 0) {
        state.msgList?[index] = msg;
        list.removeAt(i);
      }
    }

    if (isLeading) {
      state.msgList?.insertAll(0, list);
    } else {
      state.msgList?.addAll(list);
    }
    add(RefreshChatMsgListEvent(_filterMsgList(state.msgList)));
  }

  Future<void> onMsgListLoadMoreCustom(bool isLeading) async {
    // 如果最后一项是破冰消息，拉下一页数据拿倒二的消息，避免破冰消息插错页的情况下拉不到前面的消息
    // 这样可能导致下一页还有这一条破冰消息，这个会在filter方法中过滤掉
    Msg? anchor;

    /// isLeading true 向上加载旧消息，false 向下加载新消息
    if (isLeading) {
      anchor = state.topMsgList?.lastWhereOrNull((e) => e?.content?.isRealMsg == true);
      anchor = anchor ?? state.bottomMsgList?.firstWhereOrNull((e) => e?.content?.isRealMsg == true);
    } else {
      anchor = state.bottomMsgList?.lastWhereOrNull((e) => e?.content?.isRealMsg == true);
    }
    if (targetId.isEmpty || anchor == null) return null;
    final count = pageCount(null);
    final list = await imMsgService.getMsgListByTime(
      chatType: chatType,
      targetId: targetId,
      sentTime: isLeading ? anchor.sentTime - 1 : anchor.sentTime + 1,
      beforeCount: isLeading ? count : 0,
      afterCount: isLeading ? 0 : count,
    );
    if (list.isEmpty) {
      if (!isLeading) {
        add(NoMoreMsgEvent());
      }
      return;
    }

    /// 去重
    for (int i = list.length - 1; i >= 0; i--) {
      Msg msg = list[i];
      int index = state.topMsgList?.indexOf(msg) ?? -1;
      if (index >= 0) {
        state.topMsgList?[index] = msg;
        list.removeAt(i);
      } else {
        index = state.bottomMsgList?.indexOf(msg) ?? -1;
        if (index >= 0) {
          state.bottomMsgList?[index] = msg;
          list.removeAt(i);
        }
      }
    }
    if (isLeading) {
      state.topMsgList?.addAll(_filterMsgList(list) ?? []);
    } else {
      state.bottomMsgList?.addAll(_filterMsgList(list.reversed.toList()) ?? []);
    }

    // _checkShowSendGiftGuide(state.topMsgList ?? []);
    add(RefreshPageEvent(state.clone()));
  }

  List<Msg>? _filterMsgList(
    List<Msg>? msgList, {
    bool iceBreaker = true,
    bool bottle = true,
    bool freeTask = true,
  }) {
    if (msgList == null) return msgList;
    final validList = <Msg>[];
    Msg? icebreakerMsg;
    Msg? bottleMsg;
    Msg? freeTaskMsg;
    bool hasIcebreaker = false;
    bool hasBottle = false;

    /// 倒序显示的
    for (int i = 0; i < msgList.length; i++) {
      final msg = msgList[i];
      // LogExt.largeD(_tag, () => '_filterMsgList i=$i, ${msg.content.runtimeType} , ${msg.content?.starlightInfo?.toJson()}');
      // 无法判断消息列表加载是否异常，破冰消息有可能重复插入，这里过滤掉多的破冰消息
      if (iceBreaker && msg.content is IcebreakerMsgContent) {
        if (!hasIcebreaker) {
          hasIcebreaker = true;
          icebreakerMsg = msg;
        }
      } else if (bottle && msg.content is BottleMsgContent) {
        if (!hasBottle) {
          hasBottle = true;
          bottleMsg = msg;
        }
      } else if (freeTask && msg.content is FreeMoneyTaskGuideMsgContent) {
        if (i == 0) {
          freeTaskMsg = msg;
        }
      } else {
        validList.add(msg);
      }
    }
    if (freeTaskMsg != null) validList.insert(0, freeTaskMsg);
    // 保证破冰消息一定在最上面
    if (icebreakerMsg != null) validList.add(icebreakerMsg);
    if (bottleMsg != null) validList.add(bottleMsg);

    _checkShowSendGiftGuide(validList);

    return validList;
  }

  void _onResendMsg(ChatEventResend event, Emitter<ChatMsgListState> emit) {
    final context = getContext();
    if (context == null) return;

    if (event.msg.extra?.isNotEmpty == true) {
      Map? extraMap;
      try {
        extraMap = json.decode(event.msg.extra!);
      } on Exception catch (e) {
        Log.e('chat', 'onResendMsg msg.extra jsonDecode err->$e');
      }
      // 被拦截的消息
      if (extraMap != null && extraMap.containsKey('block_msg') == true) {
        bool? click = extraMap['can_click'];
        if (click == true) {
          final s = LocaleStrings.instance;
          String? resendContent = extraMap['resend_content'] ?? s.resendMsg;

          showAlertDialog(
            context: context,
            content: resendContent,
            confirmText: s.resend,
            confirmTextColor: R.color.primaryLightColor,
            onConfirm: () async {
              if (state.msgList?.isEmpty ?? true) return;
              state.msgList!.remove(event.msg);
              add(RefreshPageEvent(state.clone()));
              ChatDetailReporter.reportResendMsg(matchType, targetId, event.msg, chatFromType);

              event.msg.content?.forceSend = true;
              final newMsg = await imMsgService.resendMsg(event.msg, chatType: chatType);
              if (newMsg != null) {
                _supplyMissedStatus(newMsg);
                state.msgList!.insert(0, newMsg);
                add(RefreshPageEvent(state.clone()));
              } else {
                state.msgList!.insert(0, event.msg);
                add(RefreshPageEvent(state.clone()));
              }
            },
          );
        }
        return;
      }
    }

    final s = LocaleStrings.instance;
    showAlertDialog(
      context: context,
      content: s.resendMsg,
      confirmText: s.resend,
      confirmTextColor: R.color.primaryLightColor,
      onConfirm: () async {
        if (state.msgList?.isEmpty ?? true) return;
        state.msgList!.remove(event.msg);
        add(RefreshPageEvent(state.clone()));
        ChatDetailReporter.reportResendMsg(matchType, targetId, event.msg, chatFromType);
        final newMsg = await imMsgService.resendMsg(event.msg, chatType: chatType);
        if (newMsg != null) {
          _supplyMissedStatus(newMsg);
          state.msgList!.insert(0, newMsg);
          add(RefreshPageEvent(state.clone()));
        } else {
          state.msgList!.insert(0, event.msg);
          add(RefreshPageEvent(state.clone()));
        }
      },
    );
  }

  void _onListScrollEnd(ChatListScrollEndEvent event, Emitter<ChatMsgListState> emit) {
    if (state.initialUnreadCount > 0) {
      _checkIfShowUnreadNavChip(state.initialUnreadCount, emit);
    }
  }

  void _positionToHighlight() {
    WidgetUtils.post((_) {
      final msgList = state.msgList;
      if (!isPageValid || !scrollController.hasClients) return;
      if (highlightMsg == null || msgList?.isNotEmpty != true) return;
      final index = msgList!.indexOf(highlightMsg!);
      if (index < 0) return;
      scrollController.scrollToIndex(
        index,
        duration: _scrollTime,
        preferPosition: AutoScrollPosition.middle,
      );
    });
  }

  void _checkIfShowUnreadNavChip(int initialUnreadCount, Emitter<ChatMsgListState> emit) {
    if (initialUnreadCount <= 0) return;
    WidgetUtils.post((_) {
      if (state.msgList == null) return;
      if (isGroupChat && !isBottleChat && state.msgList!.length < initialUnreadCount) {
        add(RefreshPageEvent(state.clone(initialUnreadCount: initialUnreadCount)));
      } else {
        if (state.msgList!.length < initialUnreadCount) return;
        final msg = state.msgList![initialUnreadCount - 1];
        final rect = rectGetters[msg.id]?.getRect();
        final show = (rect?.top ?? 0) < 60.pt;
        add(RefreshPageEvent(state.clone(initialUnreadCount: show ? initialUnreadCount : 0)));
      }
    });
  }

  /// 处理 发送消息的方法返回 比 状态更新回调 晚导致的状态丢失问题
  void _supplyMissedStatus(Msg msg) {
    final missedStatus = _missedStatusMsgList.firstWhereOrNull((e) => e == msg);
    if (missedStatus != null) {
      _missedStatusMsgList.remove(missedStatus);
      msg.status = missedStatus.status;
    }
  }

  ///发送状态更新
  void _onSentStatusChanged(List<Msg> msgs) {
    if (msgs.isEmpty) return;

    bool needUpdate = false;
    List<Msg> updateMsg = [];
    for (Msg msg in msgs) {
      if (msg.targetId != targetId || state.msgList == null) return;

      if (_msgBlockedSet.contains(msg.id)) {
        continue;
      }

      final index = state.msgList!.indexOf(msg);
      if (index >= 0) {
        state.msgList![index] = msg;
        needUpdate = true;
        updateMsg.add(msg);
      } else if (isPageShow) {
        // 如果当前还在这个界面，在发送时插入消息列表
        state.msgList!.insert(0, msg);
        needUpdate = true;
        updateMsg.add(msg);
      } else {
        // 通常是sendMsg、resendMsg方法返回比这个回调晚的情况会命中这个分支
        final i = _missedStatusMsgList.indexOf(msg);
        if (i < 0) {
          _missedStatusMsgList.add(msg);
        } else {
          _missedStatusMsgList[i] = msg;
        }
      }
    }

    if (needUpdate) {
      chatBloc.chatPageController.onSendMsgStatusChange(updateMsg);
      add(RefreshPageEvent(state.clone()));
    }
  }

  void _onRefreshPage(RefreshPageEvent event, Emitter<ChatMsgListState> emit) {
    emit.call(event.state);
  }

  void _onNavToFirstUnreadMsg(ChatEventUnreadMsgNavTap event, Emitter<ChatMsgListState> emit) async {
    if (state.msgList?.isNotEmpty != true) return;
    final initialUnreadCount = state.initialUnreadCount;

    /// 非漂流瓶群聊,由于群聊未读数量可能几千条，分页读取
    if (isGroupChat && !isBottleChat && firstUnreadMsg != null) {
      final msgList = await imMsgService.getMsgListByTime(
          chatType: chatType, targetId: targetId, sentTime: firstUnreadMsg!.sentTime, afterCount: size);
      final newState = state.clone(
          msgList: null,
          topMsgList: [],
          bottomMsgList: _filterMsgList(msgList.reversed.toList()),
          hasMore: true,
          showUnreadList: true,
          initialUnreadCount: 0);
      emit.call(newState);
    } else {
      emit.call(state.clone(initialUnreadCount: 0));

      ///私聊直接跳到未读消息处
      int index;
      if (state.msgList!.length <= initialUnreadCount) {
        index = state.msgList!.length - 1;
      } else {
        index = initialUnreadCount - 1;
      }
      scrollController.scrollToIndex(
        index,
        duration: _scrollTime,
        preferPosition: AutoScrollPosition.end,
      );
    }
  }

  void _onChatLongPressMsg(ChatLongPressMsgEvent event, Emitter<ChatMsgListState> emit) {
    ChatEventLongPressMsg chatEventLongPressMsg = ChatEventLongPressMsg(event.msg, event.key);
    rxUtil.send(im.ChatEvent.longPressMsg, chatEventLongPressMsg);
  }

  void _onNoMoreMsg(NoMoreMsgEvent event, Emitter<ChatMsgListState> emit) {
    add(LoadMsgListEvent());
  }

  List<Msg> _insertIcebreaker(List<Msg> msgList) {
    if (isAssistance || _insertingIcebreaker) return msgList;
    _insertingIcebreaker = true;
    Msg? icebreakerMsg = msgList.firstWhereOrNull((e) => e.content is IcebreakerMsgContent);
    if (icebreakerMsg != null) {
      IcebreakerMsgContent icebreakerContent = icebreakerMsg.content as IcebreakerMsgContent;
      if (icebreakerContent.version == IcebreakerMsgContent.currentVersion) {
        return msgList;
      }
    }
    ;

    final content = IcebreakerMsgContent.obtain();
    Msg msg = Msg(
        targetId: targetId,
        status: MsgStatus.received,
        msgUid: '',
        id: IcebreakerMsgContent.localMsgId,
        direction: MsgDirection.receive,
        content: content,
        chatType: chatType,
        receiveStatus: MsgReceiveStatus.read,
        senderUid: '',
        sentTime: 1);
    msgList.add(msg);
    _insertIcebreakerToLocal();
    return msgList;
  }

  /// 插入
  Future _insertIcebreakerToLocal() async {
    final icebreaker = await socialService.getIcebreaker(targetId);
    if (icebreaker == null) return;

    /// 如果是女性用户，插入聊天模式提示
    PbImChatSafeMode? pbImChatSafeMode;
    final currentUser = await userService.getCurrentUserInfo();
    if (currentUser?.sex == Sex.female) {
      bool isFreeChat = await chatSafeModeService.isFreeChatMode(targetId: targetId);
      pbImChatSafeMode = PbImChatSafeMode.create()
        ..uid = accountService.getAccountInfo()?.uid ?? ""
        ..free = isFreeChat
        ..safe = !isFreeChat;
    }
    final content = IcebreakerMsgContent.obtain(
      matching: icebreaker.matching,
      info: icebreaker.msgs,
      pbImChatSafeMode: pbImChatSafeMode,
      winkStatus: icebreaker.winkStatus,
    );

    await imMsgService.insertOutgoingMessage(
      content: content,
      targetId: targetId,
      chatType: chatType,
      sendTime: 1,
      sendMsgReceivedNotify: false,
    );
  }

  bool _hasInsertBottleMsg = false;

  /// 如果是漂流瓶聊天，需要像插入破冰卡片一样，头部插入一条漂流瓶信息
  Future _insertBottleCard(Emitter<ChatMsgListState> emit) async {
    if (isAssistance || _hasInsertBottleMsg) return;
    _hasInsertBottleMsg = true;
    final msgList = state.msgList;
    Msg? bottleMsg = msgList?.firstWhereOrNull((e) => e?.content is BottleMsgContent);
    if (bottleMsg != null) return;
    String bottleId = getBottleIdByGroupId(targetId);
    var bottle = await bottleService.getBottleInfo(bottleId);

    if (bottle != null) {
      final content = BottleMsgContent.obtain(bottle);
      final msg = await imMsgService.insertOutgoingMessage(content: content, targetId: targetId, chatType: chatType);
      if (msg != null) {
        state.msgList?.add(msg);
        emit.call(state.clone());
      }
    }
  }

  void _onNewMsgReceived(List<Msg> msgs) {
    if (msgs.isEmpty) return;
    final showUnreadList = state.showUnreadList;
    var msgList = showUnreadList ? state.bottomMsgList ?? [] : state.msgList;

    /// 消息列表未初始化完成
    if (msgList == null) {
      for (Msg msg in msgs) {
        if ((targetId == msg.senderUid || targetId == msg.targetId) && msg.chatType == chatType) {
          _missedMsgList.add(msg);
        }
      }
      return;
    }
    chatBloc.chatPageController.onNewMsgReceived(msgs);

    int? lastTime;
    bool hadUnRead = false;
    for (Msg msg in msgs) {
      if (msg.content is IcebreakerMsgContent) continue;

      // 服务端发送的通话记录消息，也会在这里收到
      if ((targetId == msg.senderUid || targetId == msg.targetId) && msg.chatType == chatType) {
        if (!msg.fromSelf) {
          lastTime = msg.sentTime + 1;

          if (msg.chatType == ChatType.private && msg.content?.isRealMsg == true) {
            rxUtil.send(im.ChatEvent.showOnlineStatus, true);
          }

          /// 有未读
          if (msg.receiveStatus != MsgReceiveStatus.read) {
            hadUnRead = true;
          }
        }

        /// 展示未读消息列表时，有新消息未加载，先不插入当前列表
        if (state.hasMore) return;
        final index = msgList.indexOf(msg);
        if (index < 0) {
          if (state.showUnreadList) {
            msgList.add(msg);
          } else {
            msgList.insert(0, msg);
            if (scrollController.hasClients && scrollController.offset < 500 && msgList.length > maxMsgSize) {
              msgList.removeRange(maxMsgSize - 100, msgList.length);
            }
          }
        } else {
          msgList[index] = msg;
        }
      }
      receiveReportHandler(msg, targetId, matchType, chatFromType);
    }

    msgList = _filterMsgList(msgList, iceBreaker: false, bottle: false, freeTask: true);

    onReportReceivedMsgs(msgs);
    reportChatCreate(targetId, isBottleChat ? StatisticPageFrom.bottle : matchType,
        msgs: msgList, roomId: roomId, roomOwnerId: roomOwnerId);

    final newState = showUnreadList ? state.clone(bottomMsgList: msgList) : state.clone(msgList: msgList);

    /// 发送礼物解锁聊天
    if (msgList?.isNotEmpty == true && msgList?.length == 2 && msgList?.first.direction == MsgDirection.send) {
      _checkInsertCompleteBioOrMomentMsg(msgList, lastTime: lastTime);
    }
    add(RefreshPageEvent(newState.clone()));

    handleHasValidMsgChange(newState.hasValidMsg);

    /// 设为已读
    if (hadUnRead && null != lastTime && (chatType == ChatType.private || isBottleChat)) {
      imMsgService.sendReadReceipt(targetId: targetId, timestamp: lastTime, chatType: chatType);
      imChatService.clearUnreadStatus(targetId, lastTime, chatType);
    }
  }

  void _checkInsertCompleteBioOrMomentMsg(List<Msg>? msgs, {int? lastTime}) async {
    if (isBottleChat || (msgs?.isEmpty ?? true) || hasCheckInsertCompleteBio) return;
    var newMsgs = msgs?.toList();
    hasCheckInsertCompleteBio = true;

    /// 去掉系统提示和漂流瓶揭开身份提示
    newMsgs?.removeWhere((msg) =>
        (msg.content is SystemMsgContent &&
            (msg.content as SystemMsgContent).type == PbSystemMsgType.PbSystemMsgType_MSG_ALERT) ||
        msg.content is UncoverIdentityMsgContent);
    if (newMsgs?.length == 2) {
      // double process = await UserInfoImproveManager.instance()
      //     .checkCompleteProgress( pageFrom: "chat", onlyStep: true);
      //
      // if (process < 4) {
      //   _insertLocalMsgInFirst(
      //     CompleteBioTipMsgContent(),
      //     sentTime: lastTime ?? DateTime.now().millisecondsSinceEpoch,
      //   );
      // } else if ((accountService.getAccountInfo()?.postCount ?? 0) <= 0) {
      //   // _insertLocalMsgInFirst(
      //   //   PostMomentMsgContent(),
      //   //   sentTime: lastTime ?? DateTime.now().millisecondsSinceEpoch,
      //   // );
      // }
    }
  }

  void _handleUnreadStatus(String targetId, List<Msg> msgList) {
    final lastMsg = msgList.first;
    imChatService.clearUnreadStatus(targetId, lastMsg.sentTime, chatType);
    if (lastMsg.status != MsgStatus.read && (chatType == ChatType.private || isBottleChat)) {
      imMsgService.sendReadReceipt(targetId: targetId, timestamp: lastMsg.sentTime, chatType: chatType);
    }
  }

  void _onReadReceiptReceived(String _targetId) async {
    if (_targetId != targetId) return;
    final list = state.msgList;
    if (list?.isNotEmpty != true) return;
    List<Msg>? newList = await imMsgService.getMsgList(
      chatType: chatType,
      targetId: targetId,
      count: list!.length,
    );
    newList = _filterMsgList(newList);

    Log.i(_tag, 'lastStatus = ${describeEnum(newList!.first.status)}');
    // 直接使用newList会导致页面图片闪烁
    Map<int, Msg> listMap = {};
    for (var message in list) {
      listMap[message.id] = message;
    }

    for (var message in newList) {
      Msg? msg = listMap[message.id];
      if (msg != null) {
        msg.status = message.status;
      }
    }
    add(RefreshPageEvent(state.clone()));
  }

  void _onUserInfoUpdated(String _targetId) {
    if (_targetId != targetId) return;
    if (isGroupChat && !isBottleChat) return;
    _loadUserInfo(targetId);
  }

  void _onBeFollowed(String _targetId) async {
    if (_targetId != targetId) return;
    _onUserInfoUpdated(targetId);
    if (state.targetUser?.isFollowing != false) return;
    final dao = (await DbUtil.getAppDb())?.beFollowedGuideDao;
    if (dao == null) return;

    final chat = await dao.getBeFollowedGuideChat(targetId);
    if (chat != null) return;

    final followMsg = await _insertLocalMsgInFirst(BeFollowedMsgContent.obtain());
    if (followMsg != null) {
      dao.insertChat(BeFollowedGuideChat(targetId));
      reportFollowGuideMsgListImp();
    }
  }

  Future<Msg?> _insertLocalMsgInFirst(MsgContent content, {int? sentTime}) async {
    final msg = await imMsgService.insertOutgoingMessage(
      content: content,
      targetId: targetId,
      chatType: chatType,
      sendTime: sentTime ?? DateTime.now().millisecondsSinceEpoch,
    );
    if (msg != null) {
      state.msgList?.insert(0, msg);
      add(RefreshPageEvent(state.clone()));
    }
    return msg;
  }

  void _onHistoryCleared(String targetId) {
    if (targetId != targetId || isBottleChat) return;
    if (state.initialUnreadCount > 0) {
      add(RefreshPageEvent(state.clone(initialUnreadCount: 0)));
    }
    add(LoadMsgListEvent());
  }

  void _sendByEvent(MsgSendByEventModel model) async {
    if (targetId.isEmpty) return;
    if (model.targetId != targetId) return;

    final startTime = DateTime.now();
    final msg = model.msg;
    String key = "${msg?.id}-${msg?.targetId}";
    startTimingWithTime(key: key, time: startTime);
  }

  void _checkReplyIntervalTime(List<Msg> msgs) async {
    /// 今天已经符合条件，不再检测该逻辑
    if ((shareGuidHandle.getShareGuidSp().userReply ?? 0) == 0) {
      int? intervalTime = _getReplyIntervalTime(msgs);
      if (intervalTime == null) return;
      shareGuidHandle.addShareSp(ShowType.userReply, intervalTime: intervalTime);
    }

    // if (!(await evaluateHandle.needShowEvaluate())) return;

    if (((await evaluateService.getEvaluate()).replyChat ?? 0) == 0) {
      int? intervalTime = _getReplyIntervalTime(msgs);
      if (intervalTime == null) return;
      evaluateService.checkReplyInterval(intervalTime);
    }
  }

  int? _getReplyIntervalTime(List<Msg> msgs) {
    Msg? selfLastMsg, otherMsg;
    for (var msg in msgs) {
      /// 排除自动发送的消息、非真实消息、漂流瓶卡片消息、系统消息
      if (msg.fromSelf &&
          msg.content?.isAutoSendMsg == false &&
          msg.content?.isRealMsg == true &&
          !(msg.content is BottleMsgContent) &&
          !(msg.content is SystemMsgContent)) {
        selfLastMsg = msg;
        break;
      }
    }
    if (selfLastMsg != null) {
      int index = msgs.indexOf(selfLastMsg);
      List lists = msgs.sublist(0, index);

      /// 说明对方有回复
      if (lists.isNotEmpty) {
        for (var msg in lists.reversed) {
          /// 排除自动发送的消息、非真实消息、漂流瓶卡片消息、系统消息
          if (!msg.fromSelf &&
              msg.content?.isAutoSendMsg == false &&
              msg.content?.isRealMsg == true &&
              !(msg.content is BottleMsgContent) &&
              !(msg.content is SystemMsgContent)) {
            otherMsg = msg;
            break;
          }
        }
      }

      if (otherMsg != null) {
        int intervalTime = otherMsg.sentTime - selfLastMsg.sentTime;
        return intervalTime;
      }
    }
    return null;
  }

  double? getLastOtherMsgStarLightVal() {
    if (state.msgList?.isNotEmpty != true) return null;
    for (var msg in state.msgList!) {
      /// 如果之前有自己发的消息，则不显示
      if (msg.fromSelf &&
          msg.content?.isAutoSendMsg == false &&
          msg.content?.isRealMsg == true &&
          !(msg.content is BottleMsgContent) &&
          !(msg.content is SystemMsgContent)) {
        return null;
      }
      if (!msg.fromSelf &&
          msg.content?.isAutoSendMsg == false &&
          msg.content?.isRealMsg == true &&
          msg.content?.isUserMsg == true &&
          !(msg.content is BottleMsgContent) &&
          !(msg.content is SystemMsgContent)) {
        var starlightInfo = msg.content?.starlightInfo;
        if (starlightInfo?.receiverNextReplyAddVal.isValid() == true) {
          return toDouble(starlightInfo?.receiverNextReplyAddVal);
        }
        break;
      }
    }
    return null;
  }

  /// 处理消息发送后，消息列表的视图变化
  void _handleMsgSent(Msg? msg) async {
    if (msg == null) return;
    if (msg.targetId != targetId) return;
    if (state.showUnreadList) {
      add(LoadMsgListEvent());
      return;
    }

    reportChatCreate(targetId, isBottleChat ? StatisticPageFrom.bottle : matchType);
    final isFirstMsg = !state.hasValidMsg;
    bool needCheckBio = false;
    if (!isFirstMsg &&
        state.msgList?.isNotEmpty == true &&
        state.msgList?.length == 2 &&
        state.msgList!.first.content is UncoverIdentityMsgContent) {
      needCheckBio = true;
    }

    _supplyMissedStatus(msg);
    if (state.msgList != null && state.msgList!.indexOf(msg) < 0) {
      state.msgList?.insert(0, msg);
    } else {
      _missedMsgList.add(msg);
    }
    final newState;
    /// 送礼不取消打招呼，如 首次聊天解锁送礼
    if (msg.content is ImGiftMessageContent) {
      newState = state;
    } else {
      newState = state.clone(showGreeting: false);
      chatBloc.emit(chatBloc.state.clone(showGreeting: false));
      add(RefreshPageEvent(newState));
    }
    handleHasValidMsgChange(newState.hasValidMsg);
    if (needCheckBio) {
      _checkInsertCompleteBioOrMomentMsg(state.msgList, lastTime: msg.sentTime + 1);
    }
    if (isFirstMsg && !isBottleChat) {
      // 通知服务端产生会话
      socialService.markChatStatus(targetId);
      _checkInsertCompleteBioOrMomentMsg(newState.msgList, lastTime: msg.sentTime + 1);
    } else {
      int count = await imMsgService.getSentCount(targetId);

      /// 漂流瓶发送20句聊天
      if (isBottleChat && count >= 20) {
        bool hasSent = bottleService.hasUncoverIdentityGuide(targetId);
        bottleService.sentUncoverIdentityGuide(targetId);

        /// 之前没发送过
        if (!hasSent) {
          final guideMsg = await _insertLocalMsgInFirst(GuideIdentityMsgContent.obtain(targetId));
          if (guideMsg != null) {
            bottleService.sentUncoverIdentityGuide(targetId);
          }
        }
      } else if (!isBottleChat &&
          state.targetUser?.isFollowing == false &&
          _maxFollowGuideCount > 0 &&
          count >= _maxFollowGuideCount) {
        /// 未关注 && 发送消息超过 _maxFollowGuideCount 条
        final dao = (await DbUtil.getAppDb())?.followGuideDao;
        final chat = await dao?.getFollowGuideChat(targetId);

        /// 之前没发送过
        if (dao != null && chat == null) {
          final followMsg = await _insertLocalMsgInFirst(FollowMsgContent.obtain());
          if (followMsg != null) {
            dao.insertChat(FollowGuideChat(targetId));
            reportFollowGuideMsgListImp();
          }
        }
      }
    }

    _scrollToBottom();
  }

  void _deleteMsg(List<int> ids) {
    state.msgList?.removeWhere((msg) {
      return ids.contains(msg.id);
    });
    add(RefreshPageEvent(state.clone()));
  }

  void _scrollToBottom() {
    if (scrollController.hasClients && isPageValid) {
      scrollController.animateTo(
        scrollController.position.minScrollExtent,
        duration: _scrollTime,
        curve: Curves.easeOut,
      );
    }
  }

  void _onScreenCaptured(bool fromSelf) {
    Log.i(_tag, 'fromSelf = $fromSelf, isPageShow = $isPageShow');
    // 官方小助手不提示
    if (isAssistance) return;

    // 自己截图，但不在当前页面，不提示
    if (fromSelf && (!isPageShow || !isForeground)) return;

    // 没聊过天不提示
    if (!state.hasValidMsg) return;

    ChatDetailReporter.reportListScreenShot(matchType, targetId, chatFromType);
    if (!(globalSp.getBool(spKeyHasShownScreenshotAlert) ?? false)) {
      final context = getContext();
      if (context == null) return;
      showScreenshotAlertDialog(context);
      globalSp.setBool(spKeyHasShownScreenshotAlert, true);
      ChatDetailReporter.reportListScreenShotImp(matchType, targetId, chatFromType);
    }

    _insertLocalMsgInFirst(ScreenCaptureMsgContent());
    final uid = isBottleChat ? targetId : accountService.getAccountInfo()?.uid;
    if (targetId.isEmpty || uid == null) {
      Log.e(_tag, 'uid = $uid, targetId = $targetId');
      return;
    }
    socialService.sendSignal(
      isBottleChat ? getBottleChatTargetUid(targetId) : targetId,
      PbBizPush(
        pushEvent: PbPushEvent.PbPushEvent_SCREENSHOT,
        data: PbScreenshotNotice(uid: uid).writeToBuffer(),
      ),
    );
  }

  /// 处理自动发送的消息，由于自动发送的消息可能会在消息列表查询出来后、消息监听事件注册前
  /// or emit的state.msgList还是null
  /// 才发送出去，时序性无法确定，需要特殊处理
  void _onAutoSendMsg(Msg? msg) {
    if (targetId != msg?.targetId) return;

    /// 还未开始查询已发送中，则走正常初始化逻辑
    if (!_rcMsgInitLoaded) return;

    /// 已开始查询消息，监听接收事件还未正常工作
    if ((_rcMsgInitLoaded && state.msgList == null)) {
      /// state刷新后尝试处理该自动发送消息
      WidgetUtils.post((_) {
        if (state.msgList != null) {
          _onNewMsgReceived([msg!]);
        } else {
          _onAutoSendMsg(msg);
        }
      });
    }
  }

  ///消息被拦截
  void _onMessageBlocked(RCIMIWBlockedMessageInfo? info) {
    if (info == null) return;
    final _targetId = info.targetId;
    final conversationType = info.conversationType;
    ChatType convertChatType = getChatType(conversationType);
    if (_targetId != targetId && convertChatType != chatType) return;

    final messageId = info.blockedMsgUId;
    int? reasonCode;
    _msgBlockedSet.add(messageId);
    final extra = info.extra ?? '';
    try {
      Map<String, dynamic> jsonObj = jsonDecode(extra);
      reasonCode = jsonObj['reason_code'];
    } on Exception catch (e) {
      print(e);
    }
    if (state.msgList != null) {
      final msgList = state.msgList;
      bool isExist = false;
      for (Msg msg in msgList!) {
        if (msg.msgUid == messageId) {
          msg.status = MsgStatus.failed;
          msg.extra = extra;
          isExist = true;
          break;
        }
      }

      if (isExist) {
        add(RefreshPageEvent(state.clone(msgList: msgList)));
        chatBloc.chatPageController.onMsgBlocked(reasonCode);
      }
    }
  }

  void _checkShowSendGiftGuide(List<Msg> list) {
    if (hasShowGiftGuide) return;

    if (list.isEmpty) return;
    if (list.length < 4) return;

    /// 只有私聊显示
    if (!chatBloc.isPrivateChat) return;

    /// 正在播放不展示
    if (chatBloc.state.showGiftGuideAnimation) return;

    /// 男用户与女用户聊天才显示
    if (!(chatBloc.state.currentUser?.sex == Sex.male && chatBloc.state.targetUser?.sex == Sex.female)) {
      return;
    }

    final key = spChatGiftGuideTargetId(targetId);

    final show = _pref.getBool(key) ?? false;
    if (show) {
      hasShowGiftGuide = show;
      return;
    }

    int realCount = 0;
    for (final msg in list) {
      /// 跳过非聊天消息
      if (!(msg.content?.isRealMsg ?? false)) continue;

      /// 跳过自己发的聊天消息
      if (msg.direction == MsgDirection.send) continue;

      realCount += 1;

      if (realCount >= 4) break;
    }

    if (realCount < 4) return;

    /// 对方回复5句开启
    Future.delayed(Duration(milliseconds: 300), () {
      chatBloc.add(ShowGiftGuideEvent(true));
    });

    _pref.setBool(key, true);

    return;
  }
}
