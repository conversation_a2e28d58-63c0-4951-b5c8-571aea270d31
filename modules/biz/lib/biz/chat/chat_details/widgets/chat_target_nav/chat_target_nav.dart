import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/chat_target_nav/bloc/chat_target_nav_bloc.dart';
import 'package:biz/biz/report/report_success_dialog.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/common/widgets/icon_button.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/bottle/bottle_utils.dart';
import 'package:service/modules/im/const/const.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';

class ChatTargetNavBar extends StatefulWidget implements PreferredSizeWidget {
  ChatTargetNavBar(
    this.targetId,
    this.matchType,
    this.isBottleChat,
    this.isGroupChat, {
    Key? key,
    this.onBackTap,
    this.reportFollow,
    this.hasUncoverIdentity = false,
    this.hasQuitGroup = false,
    this.color,
    this.inRoom,
  }) : super(key: key);

  final String? targetId;
  final String? matchType;
  final Function()? onBackTap;
  final Function? reportFollow;
  final bool isBottleChat;
  final bool isGroupChat;
  final bool hasUncoverIdentity;
  final bool hasQuitGroup;

  final Color? color;

  final bool? inRoom;

  @override
  Size get preferredSize {
    return Size.fromHeight(55.pt);
  }

  @override
  State<StatefulWidget> createState() => _ChatTargetNavBarState();
}

class _ChatTargetNavBarState extends State<ChatTargetNavBar> {
  late final ChatTargetNavBloc _bloc;

  @override
  void initState() {
    _bloc = ChatTargetNavBloc(
      widget.targetId,
      widget.matchType,
      widget.isBottleChat,
      widget.isGroupChat,
      reportFollow: widget.reportFollow,
      inRoom: widget.inRoom
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<ChatTargetNavBloc, ChatTargetNavState>(
      bloc: _bloc,
      builder: (context, state) => CommonAppBar(
        backgroundColor: widget.isBottleChat ? Colors.transparent : widget.color,
        leading: _backBtn(),
        leadingWidth: _bloc.state.totalUnreadCount! >= 99 ? 101.pt : 91.pt,
        titleWidget: GestureDetector(
          child: _newTitle(state),
          onTap: () => _bloc.add(ChatTitleClickEvent()),
        ),
        actions: widget.targetId?.length != assistanceLength ? getActionWidgets(context) : null,
      ),
    );
  }

  Widget _backBtn() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.pt),
        child: Row(
          children: [
            FLImage.asset(
              Res.commonBack0,
              width: 11.pt,
              fit: BoxFit.cover,
            ),
            6.wSpace,
            Visibility(
                visible: _unreadCountSpan().isNotEmpty,
                child: Container(
                  alignment: Alignment.center,
                  height: 16.pt,
                  padding: EdgeInsets.symmetric(horizontal: 8.pt),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.pt), color: Colors.white),
                  child: Text.rich(TextSpan(children: _unreadCountSpan())),
                ))
          ],
        ),
      ),
      onTap: widget.onBackTap,
    );
  }

  List<InlineSpan> _unreadCountSpan() {
    final texts = <InlineSpan>[];

    if (_bloc.state.totalUnreadCount! > 0) {
      texts.add(TextSpan(
        text: '${_bloc.state.totalUnreadCount! <= 99 ? _bloc.state.totalUnreadCount : 99}',
        style: TextStyle(fontSize: 12.sp, fontWeight: FontWeightExt.medium, color: R.color.color20),
      ));

      if (_bloc.state.totalUnreadCount! > 99) {
        texts.add(TextSpan(
          text: '+',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeightExt.heavy,
            color: R.color.color20,
          ),
        ));
      }
    }
    return texts;
  }

  Widget _newTitle(ChatTargetNavState state) {
    String displayedName = _bloc.isBottleChat
        ? state.targetUser?.bottleInfo?.name ?? ''
        : _bloc.isGroupChat
            ? state.groupInfo?.groupName ?? ''
            : state.targetUser?.displayedName ?? '';

    Color onlineTimeColor = const Color(0xFF00CB38);
    if (_bloc.state.isOnline == false) {
      onlineTimeColor = R.color.colorB2;
    }

    String uid = _bloc.state.targetUser?.uid ?? '';
    final onlineTitle = _bloc.state.onlineStatusString ?? '';

    Widget child = _bloc.state.showLiveRoom == true
        ? GestureDetector(
            onTap: () {
              routerUtil.push(R_LIVE_ROME_JOIN, params: {P_ID: _bloc.roomId, P_STATISTIC_FROM: StatisticPageFrom.chat});
            },
            child: Container(
              height: 15.pt,
              margin: EdgeInsets.only(top: 4.pt),
              padding: EdgeInsets.symmetric(horizontal: 5.pt),
              decoration: BoxDecoration(
                color: const Color(0xFFB500FF),
                borderRadius: BorderRadius.circular(7.5.pt),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Lottie.asset(Assets.liveUserInRoom, width: 11.pt, height: 10.pt, fit: BoxFit.cover, repeat: true),
                  3.wSpace,
                  Text(
                    LocaleStrings.instance.chattingNow,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 9.sp,
                      fontWeight: FontWeightExt.medium
                    ),
                  ),
                ],
              ),
            ),
          )
        : Visibility(
            visible: onlineTitle.isNotEmpty,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 4.pt,
                  height: 4.pt,
                  decoration: BoxDecoration(
                    color: onlineTimeColor,
                    borderRadius: BorderRadius.circular(2.pt),
                  ),
                ),
                2.wSpace,
                Text(
                  onlineTitle,
                  style: TextStyle(fontSize: 11.pt, fontWeight: FontWeightExt.medium, color: onlineTimeColor),
                )
              ],
            ),
          );

    Widget nameWidget = Text(
      displayedName,
      maxLines: 1,
      style: TextStyle(
        color: _bloc.isBottleChat || _bloc.state.targetUser?.isOfficial == true ? Colors.white : R.color.color20,
        fontSize: widget.inRoom == true ? 18.sp : 20.sp,
        fontWeight: FontWeightExt.black,
      ),
    );
    Widget decoratedNameWidget;
    if (_bloc.state.targetUser?.isOfficial == true) {
      decoratedNameWidget = ShaderMask(
        shaderCallback: (bounds) {
          return LinearGradient(colors: [
            Color(0xFF378BFF),
            Color(0xFF7D4EF9),
            Color(0xFFCE42C3),
            Color(0xFFFF847E)
          ])
              .createShader(Offset.zero & bounds.size, textDirection: Directionality.of(context));
        },
        child: nameWidget,
      );
    } else {
      decoratedNameWidget = nameWidget;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // if (_bloc.state.showOnlineStatus || _bloc.state.showLiveRoom == true) 10.hSpace,
        Visibility(visible: _bloc.state.showOnlineStatus || _bloc.state.showLiveRoom == true, child: 10.hSpace),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: decoratedNameWidget,
            ),
            GlobalWidgets.spacingHorizontal(6.pt),
            Visibility(
                visible: _bloc.state.targetUser?.isFollowing == false && uid.length > assistanceLength,
                child: GestureDetector(
                  child: Container(
                    width: 22.pt,
                    height: 22.pt,
                    key: _bloc.followGuideKey,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(11.pt),
                        gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: state.targetUser?.sexStr == 'male'
                                ? [
                                    const Color(0xFF61DAF4),
                                    const Color(0xFF458BFF),
                                  ]
                                : [
                                    const Color(0xFFF37DA5),
                                    const Color(0xFFCD46E0),
                                  ])),
                    child: FLImage.asset(Res.chatIconFollowAdd, width: 12.pt, height: 12.pt),
                  ),
                  onTap: () {
                    _bloc.add(ChatEventFollow());
                  },
                ))
          ],
        ),
        Visibility(visible: _bloc.state.showOnlineStatus || _bloc.state.showLiveRoom == true, child: child),
        5.hSpace,
      ],
    );
  }

  List<Widget> getActionWidgets(BuildContext context) {
    List<Widget> list = [];
    if (widget.isBottleChat) {
      /// 瓶子被删除或者：无举报按钮
      if (!widget.hasQuitGroup) {
        list.add(FIconButton(
            icon: Res.commonReport3,
            iconSize: Size(24.pt, 24.pt),

            /// 举报
            onTap: () {
              var groupId = widget.targetId ?? "";
              if (groupId.isEmpty) return;
              var targetUserUid = getBottleChatTargetUid(groupId);
              var bottleId = getBottleChatBottleId(groupId);
              routerUtil.push(R_SETTINGS_REPORT, params: {
                P_DATA: BottleUserReport(
                    bottleId: bottleId,
                    targetUid: targetUserUid,
                    groupId: groupId,
                    resultCallback: (bool result, ReportTemplate template) {
                      if (!result) return;
                      showReportSuccessDialog(
                        template.targetUid,
                        page: R_CHAT,
                        type: template.statisticType,
                        toContent: template.commentId ?? template.itemId ?? "",
                        confirmTxt: LocaleStrings.instance.deleteUser,
                        confirmCallback: () => _bloc.add(DeleteBottleChatEvent(groupId)),
                      );
                    }),
                P_STATISTIC_FROM: R_CHAT,
              });
            }));
      }

      list.add(Padding(
        padding: EdgeInsets.only(left: 6.pt, right: 8.pt),
        child: FIconButton(
            icon: Res.commonDelete5,
            iconSize: Size(22.pt, 22.pt),
            onTap: () {
              /// 漂流瓶删除
              var groupId = widget.targetId ?? "";
              if (groupId.isEmpty) return;
              _bloc.add(DeleteBottleChatEvent(groupId));
            }),
      ));
    } else if (_bloc.isFamilyChat) {
      if (_bloc.state.groupInfo?.rankRouter?.isNotEmpty == true) {
        list.add(
          IconBtn(
            icon: Res.commonRoomRankCup,
            iconSize: Size(13.5.pt, 15.pt),
            widgetSize: Size(28.pt, 28.pt),
            margin: EdgeInsets.only(right: 11.pt),
            bgDecoration: BoxDecoration(
              color: Colors.white38,
              shape: BoxShape.circle
            ),
            onTap: () {
              DeepLink.jump(_bloc.state.groupInfo?.rankRouter ?? "", from: R_CHAT);
            },
          ),
        );
      }

      list.add(
        IconBtn(
          icon: Res.chatIconSetting,
          iconSize: Size(16.5.pt, 17.pt),
          widgetSize: Size(28.pt, 28.pt),
          margin: EdgeInsets.only(right: 15.pt),
          bgDecoration: BoxDecoration(
              color: Colors.white38,
              shape: BoxShape.circle
          ),
          onTap: () {
            routerUtil.push(R_FAMILY_SETTING, params: {
              P_ID: imChatService.getFamilyIdByTargetId(widget.targetId ?? ""),
              P_FAMILY_ROLE: _bloc.state.currentUser?.familyInfo?.role,
              P_TYPE: "familyChat",
            });
          },
        ),
      );

    } else if (!widget.isGroupChat) {
      list.add(Padding(
        padding: EdgeInsets.only(right: 9.pt),
        child: FIconButton(
          icon: Res.commonMore3,
          iconSize: Size(13.pt, 5.pt),
          onTap: () => _bloc.add(ChatEventMore()),
        ),
      ));
    }
    return list;
  }
}
