import 'dart:async';

import 'package:biz/biz/live_room/widgets/message/room_message_dialog.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/common/widgets/pop_menu/bubble_popup.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:biz/global/date_format_utils.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/bottle_statistics.g.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/chat_detail_statistic_handler.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/bottle/bottle_utils.dart';
import 'package:service/modules/im/const/enums.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/group_info.dart';
import 'package:service/modules/im/model/input_status.dart';
import 'package:service/modules/im/model/source_tag_style.dart';
import 'package:service/modules/live/api/live_api.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/service.dart';

part 'chat_target_nav_event.dart';

part 'chat_target_nav_state.dart';

class ChatTargetNavBloc extends BaseBloc<ChatTargetNavEvent, ChatTargetNavState> {
  ChatTargetNavBloc(
    this._targetId,
    this._matchType,
    this.isBottleChat,
    this.isGroupChat, {
    this.reportFollow,
    this.inRoom,
  }) : super(ChatTargetNavState()) {
    on<ChatEventFollow>(_onChatEventFollow);
    on<ChatEventMore>(_more);
    on<LoadDataEvent>(_onLoadData);
    on<ShowInputStatusEvent>(_onShowInputStatus);
    on<HideInputStatusEvent>(_onHideInputStatus);
    on<ChatTitleClickEvent>(_onChatTitleClick);
  }

  final String? _targetId;
  final String? _matchType;
  final Function? reportFollow;

  /// 是否是漂流瓶聊天
  final bool isBottleChat;

  /// 是否群聊
  final bool isGroupChat;

  final bool? inRoom;

  /// 是否是家族群聊
  bool get isFamilyChat => isGroupChat && (_targetId?.contains(imChatService.familyChatPrefix) ?? false);

  ChatType get chatType => isGroupChat ? ChatType.group : ChatType.private;

  Timer? _inputTypeTimer;
  StreamSubscription? _inputStatusSub;

  String? roomId;

  BubblePopup? _popup;

  final followGuideKey = GlobalKey();

  @override
  void initBloc() {
    super.initBloc();

    add(LoadDataEvent());
    final time = Duration(milliseconds: 500);

    /// 私聊
    if (!isGroupChat) {
      final rxMerge = Rx.merge([
        rxUtil.observer<String>(UserRelationEvent.follow),
        rxUtil.observer<String>(UserRelationEvent.unFollow),
        rxUtil.observer<String>(UserRelationEvent.beUnFollow),
      ]);

      listenRxStream(rxMerge.throttleTime(time, trailing: true, leading: false), (_) {
        add(LoadDataEvent());
      });
    }

    /// 私聊和漂流瓶
    if (!isGroupChat || isBottleChat) {
      listenRxStream<List<String>>(rxUtil.observer<List<String>>(UserStatusEvent.updateUsers), (uids) {
        if (_targetId != null && uids.contains(_targetId)) {
          add(LoadDataEvent());
        }
      });

      _inputStatusSub =
          rxUtil.observer<InputInfo>(ChatEvent.inputStatusChanged).throttleTime(time).listen(_onInputStatusChanged);
    }

    listenRxStream(rxUtil.observer(ChatEvent.unReadCountUpdate), (value) {
      if (value != null) {
        emit(state.clone(totalUnreadCount: value as int));
      }
    });

    listenRxStream(rxUtil.observer(ChatEvent.showOnlineStatus), (value) {
      if (value != null && state.showOnlineStatus != value && _targetId != null) {
        accountService.updateUserStatus(targetUid: _targetId, onlineStatus: value as bool);
        emit(state.clone(showOnlineStatus: value as bool));
      }
    });

    listenRxStream<PbDeepTalkUnlockMsg>(rxUtil.observer(ChatEvent.showFollowGuide), (value) {
      if (_targetId != value.fuid) return;

      _showFollowGuide(value.msg);
    });

    listenRxStream(rxUtil.observer(ChatEvent.getLatestGroupInfo), (groupInfo) {
      if (groupInfo != null) {
        emit(state.clone(groupInfo: groupInfo));
      }
    });

    listenRxEvent<GroupInfoModel?>(ChatEvent.getLatestGroupInfo, (groupInfo) {

    });

    _getUserOnlineStatus();
    _getUnreadCount();
    _getUserLiveStatus();
  }

  void _getUserOnlineStatus() async {
    if (_targetId == null) return;
    final status = await accountService.checkUserStatus(_targetId);
    if (status != null) {
      if (status.isOnline == true) {
        emit(state.clone(isOnline: true, onlineStatusString: LocaleStrings.instance.active));
      } else {
        if (status.lastActiveTime != null) {
          var timeString = DateFormatUtils.dataFormatTime(status.lastActiveTime!);
          emit(state.clone(isOnline: false, onlineStatusString: timeString));
        }
      }
    }
  }

  void _getUnreadCount() async {
    // await imChatService.clearUnreadStatus(_targetId!, 0, chatType);
    int totalUnreadCount = await imCacheService.getAllUnreadCount(refresh: true);
    if (totalUnreadCount > 0) {
      emit(state.clone(totalUnreadCount: totalUnreadCount));
    }
  }

  void _onLoadData(LoadDataEvent event, Emitter<ChatTargetNavState> emit) async {
    if (_targetId == null) return;
    UserInfo? targetUser;
    GroupInfoModel? groupInfo;
    final currentUser = await userService.getCurrentUserInfo();
    if (isBottleChat) {
      var values = _targetId!.split("_");
      if (values.length == 4) {
        var userId = values[2];
        var realTargetId = userId == currentUser?.uid ? values[3] : userId;

        targetUser = await userService.getUserInfo(realTargetId);
      }
    } else if (isGroupChat) {
      groupInfo = await imCacheService.getGroupInfo(targetId: _targetId ?? "");
    } else {
      targetUser = await userService.getUserInfo(_targetId!);
    }

    var tagStyle;
    var tagUiId = targetUser?.sourceTag?.tagUiId;
    if (tagUiId != null) {
      var appUi = await getService<AbsRemoteConfigService>()?.getSection('app_ui');
      var sourceTagStyleMap = appUi?.getValue('user_tag_style');
      var styleConfig = sourceTagStyleMap?.getValue(tagUiId)?.getAll();
      if (styleConfig != null) {
        tagStyle = SourceTagStyle.fromJson(styleConfig);
      }
    }

    emit.call(state.clone(
      targetUser: targetUser,
      groupInfo: groupInfo,
      currentUser: currentUser,
      sourceTagStyle: tagStyle,
    ));
  }

  void _onChatEventFollow(event, emit) async {
    if (_targetId == null) return;

    ///是否被封禁
    var context = getContext();
    if (context != null && (await hasBeRestricted(context))) {
      return;
    }
    var resp = await socialService.oprFollow(targetUid: _targetId, isFollow: true, page: R_CHAT);
    reportFollow?.call();
    if (!resp.isSuccess) {
      toast(resp.msg ?? "");
    }
  }

  void _more(event, emit) {
    ChatDetailReporter.reportListNaviMore(_matchType, _targetId);
    routerUtil.push(R_CHAT_SETTINGS,
        params: {P_TARGET_ID: _targetId, P_STATISTIC_FROM: StatisticPageFrom.chat, P_STATISTIC_MATCH_TYPE: _matchType});
  }

  void _onInputStatusChanged(InputInfo info) {
    if (info.targetId != _targetId) return;
    add(ShowInputStatusEvent(info));
    _inputTypeTimer?.cancel();
    _inputTypeTimer = Timer(const Duration(seconds: 8), () {
      add(HideInputStatusEvent());
    });
  }

  void _onShowInputStatus(ShowInputStatusEvent event, Emitter<ChatTargetNavState> emit) {
    emit(state.clone(inputType: event.info.inputType));
  }

  void _onHideInputStatus(HideInputStatusEvent event, Emitter<ChatTargetNavState> emit) {
    emit(state.removeInputType());
  }

  void _onChatTitleClick(ChatTitleClickEvent event, Emitter<ChatTargetNavState> emit) {
    if (isFamilyChat) {
      routerUtil.push(R_FAMILY_DETAIL,
          params: {P_ID: imChatService.getFamilyIdByTargetId(_targetId ?? ""), P_STATISTIC_FROM: 'chat_title'});
    }
  }

  void _reportBottleMsgDelete(String groupId) async {
    String uid = getBottleChatTargetUid(groupId);
    final user = await userService.getUserInfo(uid);
    BottleStatistics.reportBottleMsgDelete(toUid: uid, toGender: user?.sexStr);
  }

  _deleteAndPop(String groupId) async {
    final result = await imChatService.deleteChat(groupId, chatType: ChatType.group);
    if (result) routerUtil.pop();
  }

  @override
  void dispose() {
    _inputTypeTimer?.cancel();
    _inputStatusSub?.cancel();
    _popup?.dismiss();

    super.dispose();
  }

  // 获取用户在房状态
  void _getUserLiveStatus() async {
    if (_targetId == null) {
      return;
    }
    roomId = "";
    var resp = await liveApi.getUserInRoom(targetUid: _targetId!);
    if (resp.isSuccess) {
      if (resp.data?.roomId?.isNotEmpty == true && resp.data?.roomHasPassword == false) {
        roomId = resp.data?.roomId!;
        emit(state.clone(showLiveRoom: true));
        return;
      }
    }
    emit(state.clone(showLiveRoom: false));
  }

  void _showFollowGuide(String title) {
    if (!isPageValid) return;

    final ctx = getContext();
    if (ctx == null) return;

    _popup = BubblePopup(
      context: ctx,
      content: Container(
        width: 220.pt,
        padding: EdgeInsets.only(left: 13.pt, right: 8.pt, top: 8.pt, bottom: 8.pt),
        child: Text(
          title,
          style: TextStyle(
            height: 1.2,
            color: Colors.white,
            fontSize: 13.sp,
            fontWeight: FontWeightExt.medium,
          ),
        ),
      ),
      backgroundColor: const Color(0xFFB582FD),
      gradient: LinearGradient(colors: [
        const Color(0xFF8875FF),
        const Color(0xFFB582FD),
      ]),
      width: 220.pt,
      offsetX: -70.pt,
      offsetY: 2.pt,
      borderRadius: 8.pt,
      localToGlobalOffset: Offset(0, inRoom == true ? -roomMessageOffsetY : 0),
      onTap: () {
        add(ChatEventFollow());
      },
      bounceHeight: 8.pt,
      showBounceAnimation: true,
    );

    _popup?.show(followGuideKey);
  }
}
