import 'package:biz/biz/chat/chat_details/widgets/magic_box/dialog/magic_box_info_dialog.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/entrance/bloc/magic_box_entrance_bloc.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/service.dart';

class MagicBoxEntranceWidget extends StatelessWidget {
  final String targetId;
  final bool isGroup;
  late final MagicBoxEntranceBloc _bloc;

  MagicBoxEntranceWidget({required this.targetId, required this.isGroup}) {
    _bloc = MagicBoxEntranceBloc(targetId: targetId, isGroup: isGroup);
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<MagicBoxEntranceBloc, MagicBoxEntranceState>(
        bloc: _bloc,
        builder: (_, __) {
          return Visibility(
              visible: _bloc.state.visible,
              child: ScaleTapWidget(
                  onTap: () {
                    _bloc.reportClick();
                    showMagicBoxInfoDialog(targetId: targetId);
                  },
                  child: Container(
                      margin: EdgeInsets.only(bottom: 16.pt),
                      width: 40.pt,
                      height: 40.pt,
                      child: FLImage.asset(
                        Res.chatIconMagicBoxEntrance,
                        width: 40.pt,
                        fit: BoxFit.cover,
                      ))));
        });
  }
}
