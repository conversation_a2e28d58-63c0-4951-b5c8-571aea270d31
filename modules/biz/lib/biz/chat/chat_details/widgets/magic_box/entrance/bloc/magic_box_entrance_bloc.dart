import 'package:biz/biz.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:meta/meta.dart';
import 'package:service/common/statistics/group_statistics.g.dart';

part 'magic_box_entrance_event.dart';
part 'magic_box_entrance_state.dart';

class MagicBoxEntranceBloc
    extends BaseBloc<MagicBoxEntranceEvent, MagicBoxEntranceState> {
  MagicBoxEntranceBloc({required this.targetId, required this.isGroup})
      : super(MagicBoxEntranceState()) {
    on<InitDataEvent>(_initData);
  }

  final String targetId;
  final bool isGroup;

  @override
  void initBloc() {
    super.initBloc();
    _reportImpl();
    add(InitDataEvent());
  }

  void _initData(InitDataEvent event, Emitter emit) async {
    if (isGroup) {
      final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
      bool show = groupInfo?.functionSwitch?.boxStatus == 1;
      emit.call(state.clone(visible: show));
    }
  }

  void reportClick() async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxEntranceClick(
        groupId: targetId, familyId: groupInfo?.familyId);
  }

  void _reportImpl() async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxEntranceImp(
        groupId: targetId, familyId: groupInfo?.familyId);
  }
}
