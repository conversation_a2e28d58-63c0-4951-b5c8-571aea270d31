import 'dart:ui' as ui;

import 'package:biz/biz.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/widget/image.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/im/model/magic_box_bonus.dart';
import 'package:service/service.dart' hide FLImage;
import 'package:service/utils/value_parsers.dart';

void showMagicBoxBonusDialog(
    {required MagicBoxBonus bonus, required String icon}) {
  showDialog((_) {
    return MagicBoxBonusDialog(bonus: bonus, icon: icon);
  });
}

class MagicBoxBonusDialog extends StatefulWidget {
  final MagicBoxBonus bonus;
  final String icon;

  MagicBoxBonusDialog({required this.bonus, required this.icon});

  @override
  State<StatefulWidget> createState() => _MagicBoxBonusDialogState();
}

class _MagicBoxBonusDialogState extends State<MagicBoxBonusDialog> {
  final _svgaController = SvgaController();
  bool _showSvga = true;

  @override
  void initState() {
    super.initState();
    _svgaController.setEndCallback((info) {
      if (mounted) {
        setState(() {
          _showSvga = false;
        });
      }
    });
    WidgetsBinding.instance?.addPostFrameCallback((timeStamp) async {

    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: _body(),
          ),
        ),
        onWillPop: () => Future.value(!_showSvga));
  }

  Widget _body() {
    if (_showSvga) {
      return SizedBox(
        width: 1.w,
        child: SvgaWidget(
          controller: _svgaController,
        ),
      );
    } else {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            left: 0.pt,
            right: 0.pt,
            top: -55.pt,
            child: FLImage.asset(
              Res.chatBgMagicBoxBonus,
              width: 1.w,
              fit: BoxFit.cover,
            ),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 25.pt),
            decoration: BoxDecoration(
                border: Border.all(color: Color(0xFFCC7F38), width: 1.pt),
                borderRadius: BorderRadius.circular(12.pt),
                gradient: LinearGradient(
                  colors: [Color(0xFF7B2C27), Color(0xFF3A190D)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )),
            child: Stack(
              alignment: Alignment.center,
              children: [_topRightBg(), _bottomLeftBg(), _detail()],
            ),
          ),
          Positioned(
            left: 21.pt,
            right: 21.pt,
            top: -47.pt,
            child: FLImage.asset(
              Res.chatBgGiftGuide,
              height: 82.pt,
            ),
          )
        ],
      );
    }
  }

  Widget _detail() {
    final rewards = _reward();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 30.pt),
        Text(
          LocaleStrings.instance.congratulations,
          style: TextStyle(
              fontStyle: FontStyle.italic,
              fontWeight: FontWeight.bold,
              fontSize: 20.sp,
              color: Color(0xFFFFE882)),
        ),
        SizedBox(height: 24.pt),
        Text(
          LocaleStrings.instance.youGot,
          style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18.sp,
              color: Colors.white),
        ),
        SizedBox(height: 27.pt),
        SizedBox(
          height: 150.pt,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            padding: EdgeInsets.symmetric(horizontal: 18.pt),
            itemCount: rewards.length,
            itemBuilder: (_, index) {
              return rewards[index];
            },
            separatorBuilder: (BuildContext context, int index) {
              return SizedBox(width: 10.pt);
            },
          ),
        ),
        SizedBox(height: 28.pt),
        ScaleTapWidget(
          onTap: () {
            routerUtil.pop();
          },
          child: Container(
            width: 252.pt,
            height: 50.pt,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25.pt),
                gradient: LinearGradient(
                  colors: [Color(0xFFFFE7BD), Color(0xFFFFB650)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )),
            child: Text(
              LocaleStrings.instance.iKnow,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: Color(0xFFAF5215),
              ),
            ),
          ),
        ),
        SizedBox(height: 30.pt)
      ],
    );
  }

  List<Widget> _reward() {
    final list = <Widget>[];
    if ((widget.bonus.diamond ?? 0) > 0) {
      list.add(_item(
          icon: Res.financeDiamond,
          name: LocaleStrings.instance.diamonds,
          count: widget.bonus.diamond ?? 0,
          isUrl: false));
    }
    if ((widget.bonus.coin ?? 0) > 0) {
      list.add(_item(
          icon: Res.financeGoldCoin,
          name: LocaleStrings.instance.coins,
          count: widget.bonus.coin ?? 0,
          isUrl: false));
    }
    if ((widget.bonus.gem ?? 0) > 0) {
      list.add(_item(
          icon: Res.financeGem,
          name: LocaleStrings.instance.gems,
          count: widget.bonus.gem ?? 0,
          isUrl: false));
    }
    if (widget.bonus.giftCount?.isNotEmpty == true &&
        widget.bonus.giftInfo?.isNotEmpty == true) {
      final countList = widget.bonus.giftCount?.split(',') ?? [];
      if (countList.length == widget.bonus.giftInfo?.length) {
        int index = 0;
        widget.bonus.giftInfo?.forEach((gift) {
          list.add(_item(
              icon: gift.icon ?? '',
              name: gift.name ?? '',
              count: toInt(countList[index]),
              days: gift.endTimeStr,
              isUrl: true));
          index++;
        });
      }
    }

    if (widget.bonus.goodsCount?.isNotEmpty == true &&
        widget.bonus.goodsInfo?.isNotEmpty == true) {
      final countList = widget.bonus.goodsCount?.split(',') ?? [];
      if (countList.length == widget.bonus.goodsInfo?.length) {
        int index = 0;
        widget.bonus.goodsInfo?.forEach((goods) {
          list.add(_item(
              icon: goods.thumb ?? '',
              name: goods.name ?? '',
              count: toInt(countList[index]),
              days: goods.endTimeStr,
              isUrl: true));
          index++;
        });
      }
    }

    return list;
  }

  Widget _item({required String icon,
    required String name,
    required int count,
    required bool isUrl,
    String? days}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 90.pt,
              height: 90.pt,
            ),
            if (isUrl)
              CachedNetworkImage(
                imageUrl: icon,
                width: 90.pt,
                height: 90.pt,
                fit: BoxFit.cover,
                errorWidget: (_, __, ___) {
                  return GlobalWidgets.imageError();
                },
                progressIndicatorBuilder: (context, url, progress) {
                  return GlobalWidgets.imagePlaceholder();
                },
              ),
            if (!isUrl)
              FLImage.asset(
                icon,
                width: 60.pt,
                height: 60.pt,
                fit: BoxFit.cover,
              ),
            if (days?.isNotEmpty == true)
              Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    height: 16.pt,
                    padding: EdgeInsets.symmetric(horizontal: 4.pt),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5.pt),
                        gradient: LinearGradient(
                            colors: [Color(0xFFFF9162), Color(0xFFFFAE33)])),
                    alignment: Alignment.center,
                    child: Text(
                      days ?? '',
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white),
                    ),
                  ))
          ],
        ),
        SizedBox(height: 11.pt),
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 90.pt),
          child: Text(
            name,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(height: 6.pt),
        Text(
          'x $count',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20.sp,
            color: Color(0xFFF8E16D),
          ),
        )
      ],
    );
  }

  Widget _topRightBg() {
    return Positioned(
        right: 0,
        top: 0,
        child: FLImage.asset(
          Res.chatBgMagicBoxTaskTop,
          width: 116.pt,
        ));
  }

  Widget _bottomLeftBg() {
    return Positioned(
        left: 0,
        bottom: 0,
        child: FLImage.asset(
          Res.chatBgMagicBoxTaskBottom,
          width: 116.pt,
        ));
  }
}
