import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/task/bloc/magic_task_bloc.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/task/model/task_model.dart';

class MagicTaskWidget extends StatelessWidget {
  late final MagicTaskBloc _bloc;

  final String targetId;
  final List<TaskModel>? tasks;

  MagicTaskWidget({required this.targetId, this.tasks}) {
    _bloc = MagicTaskBloc(targetId: targetId, tasks: tasks);
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<MagicTaskBloc, MagicTaskState>(
        bloc: _bloc,
        builder: (_, __) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 17.pt),
                child: Text(
                  LocaleStrings.instance.magicBoxTask,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFF6F6F6),
                  ),
                ),
              ),
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                padding:
                    EdgeInsets.symmetric(horizontal: 12.pt, vertical: 12.pt),
                itemCount: _bloc.state.tasks?.length ?? 0,
                itemBuilder: (_, index) {
                  final item = _bloc.state.tasks![index];
                  return _item(index, item);
                },
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(height: 12.pt);
                },
              )
            ],
          );
        });
  }

  Widget _item(int index, TaskModel model) {
    return Container(
      height: 84.pt,
      width: double.infinity,
      decoration: BoxDecoration(
          border: Border.all(color: Color(0xFFCC7F38), width: 1.pt),
          borderRadius: BorderRadius.circular(12.pt),
          gradient: LinearGradient(
            colors: [Color(0xFF463108), Color(0xFF1D1402)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          )),
      child: Stack(
        alignment: Alignment.center,
        children: [
          _topRightBg(),
          _bottomLeftBg(),
          _content(index, model),
        ],
      ),
    );
  }

  Widget _content(int index, TaskModel model) {
    return Row(
      children: [
        SizedBox(width: 12.pt),
        CachedNetworkImage(
          imageUrl: model.taskInfo?.icon ?? "",
          width: 40.pt,
          height: 40.pt,
        ),
        SizedBox(width: 12.pt),
        Expanded(
            child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              TextSpan(children: [
                TextSpan(
                    text: model.desc ?? "",
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14.sp,
                        color: Colors.white)),
                TextSpan(
                  text: " (${model.userCount}/${model.taskInfo?.count ?? 0})",
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14.sp,
                      color: Color(0xFFAC8B5A)),
                )
              ]),
            ),
            SizedBox(height: 4.pt),
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                FLImage.asset(Res.chatIconMagicBoxKey,
                    width: 18.pt, fit: BoxFit.cover),
                SizedBox(width: 6.pt),
                Text("x${model.bonus?.rewardKeys ?? 0}",
                    style: TextStyle(fontSize: 14.sp, color: Colors.white))
              ],
            )
          ],
        )),
        SizedBox(width: 10.pt),
        ScaleTapWidget(
            onTap: () => _bloc.add(ClickEvent(index, model)),
            child: Container(
              width: 74.pt,
              height: 38.pt,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: _itemGradientColors(model),
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter),
                  borderRadius: BorderRadius.circular(25.pt)),
              child: Text(
                _itemText(model),
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.sp,
                    color: _itemTextColor(model)),
              ),
            )),
        SizedBox(width: 12.pt),
      ],
    );
  }

  Widget _topRightBg() {
    return Positioned(
        right: 0,
        top: 0,
        child: FLImage.asset(
          Res.chatBgMagicBoxTaskTop,
          width: 116.pt,
        ));
  }

  Widget _bottomLeftBg() {
    return Positioned(
        left: 0,
        bottom: 0,
        child: FLImage.asset(
          Res.chatBgMagicBoxTaskBottom,
          width: 116.pt,
        ));
  }

  String _itemText(TaskModel model) {
    switch (model.status) {
      case -1:
        return LocaleStrings.instance.go;
      case 0:
        return LocaleStrings.instance.get;
      default:
        return LocaleStrings.instance.done;
    }
  }

  Color _itemTextColor(TaskModel model) {
    switch (model.status) {
      case -1:
        return Colors.white;
      case 0:
        return Color(0xFF725403);
      default:
        return Color(0x8CFFFFFF);
    }
  }

  List<Color> _itemGradientColors(TaskModel model) {
    switch (model.status) {
      case -1:
        return [Color(0xFF3897FF), Color(0xFF6CB2FF)];
      case 0:
        return [Color(0xFFFFE835), Color(0xFFFFC931)];
      default:
        return [Color(0x33FFFFFF), Color(0x33FFFFFF)];
    }
  }
}
