import 'package:biz/biz/chat/chat_details/widgets/magic_box/dialog/magic_box_user_list_dialog.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/group_statistics.g.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/service.dart';

class SendKeysWidget extends StatefulWidget {
  SendKeysWidget({required this.targetId, this.count = 0});

  final int count;
  final String targetId;

  @override
  State<StatefulWidget> createState() => _SendKeysWidgetState();
}

class _SendKeysWidgetState extends State<SendKeysWidget>
    with AutomaticKeepAliveClientMixin {
  final CompositeSubscription _subscription = CompositeSubscription();

  int _count = 0;

  @override
  void initState() {
    super.initState();
    _count = widget.count;
    _subscription.add(
        rxUtil.observer<int>(ImFunctionEvent.receiveMagicKeys).listen((value) {
          _count += value;
          if (mounted) {
            setState(() {});
          }
        }));
    _subscription.add(
        rxUtil.observer<String>(ImFunctionEvent.sendMagicKey).listen((value) {
      _count -= 1;
      if (_count < 0) {
        _count = 0;
      }
      if (mounted) {
        setState(() {});
      }
    }));
  }

  @override
  void didUpdateWidget(covariant SendKeysWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.count != widget.count) {
      _count = widget.count;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _subscription.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    bool canSend = widget.count > 0;
    return Column(
      children: [
        SizedBox(height: 10.pt),
        FLImage.asset(
          Res.chatIconMagicBoxKey,
          width: 146.pt,
          fit: BoxFit.cover,
        ),
        SizedBox(height: 54.pt),
        Text(
          LocaleStrings.instance.keysCanBeGifted(_count),
          style: TextStyle(
            fontSize: 16.sp,
            color: Color(0xFFFFC46A),
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 30.pt),
        ScaleTapWidget(
          enabled: canSend,
          onTap: _sendKey,
          child: Container(
            height: 45.pt,
            width: 253.pt,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(canSend
                        ? Res.chatBgMagicBoxOpen
                        : Res.chatBgMagicBoxOpened),
                    fit: BoxFit.cover)),
            alignment: Alignment.center,
            child: Text(
              LocaleStrings.instance.sendAKey,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: canSend ? Color(0xFF903801) : Colors.white,
                  fontSize: 18.sp,
                  shadows: canSend
                      ? [Shadow(color: Colors.white, offset: Offset(0, 1.2))]
                      : null),
            ),
          ),
        ),
      ],
    );
  }

  void _sendKey() async {
    final groupInfo =
        await imCacheService.getGroupInfo(targetId: widget.targetId);
    if (groupInfo == null) return;
    _reportClickSend();
    showMagicBoxUserListDialog(groupInfo: groupInfo, isSend: true);
  }

  void _reportClickSend() async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: widget.targetId);
    GroupStatistics.reportMagicboxSendClick(
        groupId: widget.targetId, familyId: groupInfo?.familyId);
  }
}
