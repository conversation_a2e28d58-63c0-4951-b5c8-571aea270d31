import 'package:biz/biz.dart';
import 'package:biz/biz/chat/search/widgets/search_bar.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/sex_age_chip.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/common/statistics/group_statistics.g.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/group_info.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/utils/loading.dart';

void showMagicBoxUserListDialog(
    {required GroupInfoModel groupInfo, bool isSend = false}) {
  showBottomSheet(
      (_) => MagicBoxUserList(groupInfo: groupInfo, isSend: isSend));
}

class MagicBoxUserList extends StatefulWidget {
  MagicBoxUserList({required this.groupInfo, this.isSend = false});

  final GroupInfoModel groupInfo;
  final bool isSend;

  @override
  State<StatefulWidget> createState() => _MagicBoxUserListState();
}

class _MagicBoxUserListState extends State<MagicBoxUserList> {
  List<UserInfo> _allUserList = [];
  var _searchKey = '';

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadAllUser();
    _reportImpl();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllUser() async {
    List<String> uids = (await imCacheService.getGroupInfo(
                targetId: widget.groupInfo.groupId ?? ''))
            ?.userList
            ?.split(",") ??
        [];
    uids.remove(accountService.currentUid() ?? '');
    _allUserList = await userService.getUserInfoList(uids);
    _allUserList
        .sort((a, b) => uids.indexOf(a.uid).compareTo(uids.indexOf(b.uid)));
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        height: 1.h * 0.651,
        width: 1.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30.pt)),
            color: Color(0xFFCC701C)),
        alignment: Alignment.bottomCenter,
        child: Container(
          height: 1.h * 0.651 - 1.pt,
          width: 1.w,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(30.pt)),
              gradient: LinearGradient(
                  colors: [Color(0xFF7B2C27), Color(0xFF3A190D)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter)),
          child: _body(),
        ),
      ),
    );
  }

  Widget _body() {
    return Column(
      children: [
        SizedBox(height: 20.pt),
        Text(
          widget.isSend
              ? LocaleStrings.instance.sendAKey
              : LocaleStrings.instance.claimAKey,
          style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
              fontWeight: FontWeight.bold),
        ),
        _search(),
        Expanded(child: _list())
      ],
    );
  }

  Widget _list() {
    var list =
        List.generate(_allUserList.length, (index) => _allUserList[index]);
    if (_searchKey.isNotEmpty) {
      list.retainWhere((element) =>
          element.nickname?.toLowerCase().contains(_searchKey.toLowerCase()) ==
          true);
    }
    final bool showFamily = !widget.isSend && _searchKey.isEmpty;
    return ListView.separated(
      controller: _scrollController,
      padding: EdgeInsets.only(top: 12.pt),
      shrinkWrap: true,
      itemCount: showFamily ? list.length + 1 : list.length,
      itemBuilder: (_, index) {
        if (index == 0 && showFamily) {
          return _itemFamily();
        } else {
          var item = list[showFamily ? index - 1 : index];
          return _item(item);
        }
      },
      separatorBuilder: (_, __) {
        return GlobalWidgets.listDivider(
            startMargin: 76.pt, color: Color(0x14FFFFFF));
      },
    );
  }

  Widget _itemFamily() {
    final child = Container(
      height: 64.pt,
      padding: EdgeInsets.symmetric(horizontal: 16.pt),
      alignment: Alignment.center,
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10.pt),
            child: CachedNetworkImage(
              imageUrl: widget.groupInfo.groupCover ?? '',
              width: 44.pt,
              height: 44.pt,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(width: 16.pt),
          Container(
            constraints: BoxConstraints(maxWidth: 244.pt),
            child: Text(
              widget.groupInfo.groupName ?? '',
              style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white),
            ),
          ),
        ],
      ),
    );
    return GestureDetector(
      onTap: () {
        _reportClick();
        if (!widget.isSend) {
          _requiredKey(isGroup: true);
        }
      },
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }

  Widget _item(UserInfo item) {
    final child = Container(
      height: 64.pt,
      padding: EdgeInsets.symmetric(horizontal: 16.pt),
      alignment: Alignment.center,
      child: Row(
        children: [
          UserAvatar(
            url: item.avatar,
            userId: item.uid,
            size: 44.pt,
            hasFrame: false,
          ),
          SizedBox(width: 16.pt),
          Container(
            constraints: BoxConstraints(maxWidth: 244.pt),
            child: Text(
              item.nickname ?? '',
              style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white),
            ),
          ),
          SizedBox(width: 5.pt),
          SexAgeChip(sex: item.sex, age: item.age),
        ],
      ),
    );
    return GestureDetector(
      onTap: () {
        _reportClick(user: item);
        if (widget.isSend) {
          _sendKey(item.uid);
        } else {
          _requiredKey(isGroup: false, uid: item.uid);
        }
      },
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }

  Widget _search() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 18.pt),
      child: MySearchBar(
          showCancel: false,
          inputColor: Colors.white,
          backgroundColor: Color(0x0FFFFFFF),
          autofocus: false,
          closeColor: Colors.white,
          prefixColor: Colors.white,
          suffixBgColor: Color(0x26FFFFFF),
          cursorColor: Colors.white,
          placeholderTextColor: Color(0x69FFFFFF),
          placeholder: LocaleStrings.instance.searchTheFamilyUser,
          controller: _searchController,
          border: Border.all(color: Color(0x70FFFFFF), width: 0.5.pt),
          onChanged: (_) {
            final key = _searchController.text;
            if (key != _searchKey) {
              if (mounted) {
                setState(() {
                  _searchKey = key;
                });
              }
            }
          },
          onCompleted: () {}),
    );
  }

  void _requiredKey({bool isGroup = false, String? uid}) async {
    int startTs = DateTime.now().millisecondsSinceEpoch;
    showLoading();
    final resp = await imFunctionService.requiredMagicKey(
        targetId: (isGroup ? widget.groupInfo.groupId : uid) ?? '',
        type: isGroup ? '1' : '2',
        familyId: widget.groupInfo.familyId ?? '');
    hideLoading();
    if (resp.isSuccess) {
      toast(LocaleStrings.instance.requestSuccessful);
      routerUtil.pop();
    } else {
      toast(resp.msg ?? LocaleStrings.instance.pleaseTryAgain);
    }
    int dt = DateTime.now().millisecondsSinceEpoch - startTs;
    _reportRequest(resp.code, dt);
  }

  void _sendKey(String? uid) async {
    int startTs = DateTime.now().millisecondsSinceEpoch;
    showLoading();
    final resp = await imFunctionService.sendMagicKey(
        targetUid: uid ?? '',
        familyId: widget.groupInfo.familyId ?? '',
        from: 'gift');
    hideLoading();
    if (resp.isSuccess) {
      toast(LocaleStrings.instance.sendSuccessfully);
      rxUtil.send(ImFunctionEvent.sendMagicKey, uid);
      routerUtil.pop();
    } else {
      toast(resp.msg ?? LocaleStrings.instance.pleaseTryAgain);
    }
    int dt = DateTime.now().millisecondsSinceEpoch - startTs;
    _reportRequest(resp.code, dt);
  }

  void _reportImpl() async {
    GroupStatistics.reportMagicboxUserlistImp(
        groupId: widget.groupInfo.groupId,
        familyId: widget.groupInfo.familyId,
        type: widget.isSend ? 'send' : 'request');
  }

  void _reportClick({UserInfo? user}) async {
    GroupStatistics.reportMagicboxUserlistClick(
        groupId: widget.groupInfo.groupId,
        familyId: widget.groupInfo.familyId,
        toUid: user?.uid ?? 'group',
        toGender: user?.sexStr,
        type: widget.isSend ? 'send' : 'request');
  }

  void _reportRequest(int code, int duration) async {
    GroupStatistics.reportMagicboxRequestEnd(
      groupId: widget.groupInfo.groupId,
      familyId: widget.groupInfo.familyId,
      type: widget.isSend ? 'send' : 'request',
      result: code == 1 ? 'succ' : 'fail',
      reason: code.toString(),
      duration: duration.toString(),
    );
  }
}
