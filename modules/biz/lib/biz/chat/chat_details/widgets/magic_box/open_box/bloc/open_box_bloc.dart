import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/dialog/magic_box_bonus_dialog.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/dialog/magic_box_user_list_dialog.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:biz/global/widgets/no_enough_money_dialog.dart';
import 'package:meta/meta.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/group_statistics.g.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/magic_box_model.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/utils/loading.dart';

part 'open_box_event.dart';
part 'open_box_state.dart';

class OpenBoxBloc extends BaseBloc<OpenBoxEvent, OpenBoxState> {
  OpenBoxBloc({required this.targetId, this.boxList, this.boxKeys})
      : super(OpenBoxState()) {
    on<RefreshDataEvent>(_refreshData);
    on<RequiredKeyEvent>(_requiredKey);
    on<ClickOpenEvent>(_openBox);
  }

  final String targetId;
  final List<MagicBoxDetail>? boxList;
  final MagicBoxKeys? boxKeys;

  @override
  void initBloc() {
    super.initBloc();
    listenRxEvent<MagicBoxDetail>(ImFunctionEvent.openMagicBox, _refreshBox);
    if (boxList?.isNotEmpty == true) {
      add(RefreshDataEvent(
          currentBox: boxList!.first, boxList: boxList, boxKeys: boxKeys));
    }
  }

  void _refreshData(RefreshDataEvent event, Emitter emit) {
    emit.call(state.clone(
        currentBox: event.currentBox,
        boxList: event.boxList,
        boxKeys: event.boxKeys));
  }

  void _requiredKey(RequiredKeyEvent event, Emitter emit) async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    if (groupInfo == null) return;
    _reportClickRequest();
    showMagicBoxUserListDialog(groupInfo: groupInfo, isSend: false);
  }

  void _openBox(ClickOpenEvent event, Emitter emit) async {
    _reportClickOpen(state.currentBox?.level?.toString() ?? '');
    int startTs = DateTime.now().millisecondsSinceEpoch;
    showLoading();
    final resp = await imFunctionService.openMagicBox(
        targetId: targetId, level: state.currentBox?.level?.toString() ?? '');
    hideLoading();
    int dt = DateTime.now().millisecondsSinceEpoch - startTs;
    _reportOpenRequest(resp.code, dt, state.currentBox?.level?.toString() ?? '');
    if (resp.isSuccess && resp.data != null) {
      rxUtil.send(ImFunctionEvent.openMagicBox, state.currentBox);
      showMagicBoxBonusDialog(
          bonus: resp.data!, icon: state.currentBox?.closeIcon ?? '');
    } else {
      if (resp.code == 2002) {
        showNoEnoughMoneyDialog(
            currencyType: CurrencyType.diamond,
            from: NoMoneyTypeStatistic.magicBox,
            onConfirm: () {
              routerUtil.pop();
            });
        return;
      }
      toast(resp.msg ?? LocaleStrings.instance.pleaseTryAgain);
    }
  }

  void _refreshBox(MagicBoxDetail box) {
    final list = state.boxList;
    list?.forEach((item) {
      if (item.level == box.level) {
        item.status = 1;
      }
    });
    int keys = state.boxKeys?.receiveKeys ?? 0;
    keys -= (box.boxRequires?.keys ?? 0);
    if (keys < 0) keys = 0;
    add(RefreshDataEvent(
        currentBox: state.currentBox?..status = 1,
        boxList: list,
        boxKeys: state.boxKeys?..receiveKeys = keys));
  }

  void _reportClickRequest() async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxRequestClick(
        groupId: targetId, familyId: groupInfo?.familyId);
  }

  void _reportClickOpen(String id) async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxOpenClick(
      groupId: targetId,
      familyId: groupInfo?.familyId,
      content: id,
    );
  }

  void _reportOpenRequest(int code, int duration, String id) async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxRequestEnd(
      groupId: targetId,
      familyId: groupInfo?.familyId,
      type: 'open',
      result: code == 1 ? 'succ' : 'fail',
      reason: code.toString(),
      duration: duration.toString(),
      content: id,
    );
  }
}
