import 'package:biz/biz/chat/chat_details/widgets/magic_box/magic_box_widget.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/router/router_url.dart';

void showMagicBoxInfoDialog({required String targetId}) {
  showBottomSheet((_) => MagicBoxWidget(targetId: targetId),
        enableDrag: false,
  routeSettings: RouteSettings(name: R_CHAT_MAGIC_BOX_DIALOG));
}
