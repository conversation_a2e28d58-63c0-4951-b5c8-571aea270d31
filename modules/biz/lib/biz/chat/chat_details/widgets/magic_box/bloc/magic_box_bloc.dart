import 'package:biz/global/bloc/base_bloc.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/group_statistics.g.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/magic_box_model.dart';
import 'package:service/service.dart';

part 'magic_box_event.dart';
part 'magic_box_state.dart';

class MagicBoxBloc extends BaseBloc<MagicBoxEvent, MagicBoxState> {
  MagicBoxBloc({required this.targetId}) : super(MagicBoxState()) {
    on<InitDataEvent>(_initData);
  }

  final String targetId;

  late final TabController tabController;

  @override
  void initBloc() {
    super.initBloc();
    add(InitDataEvent());
    listenRxEvent<int>(ImFunctionEvent.goToSendKeys, tabController.animateTo);
    tabController.addListener(() {
      if (tabController.indexIsChanging == true) return;
      _reportImpl(tabController.index == 0 ? 'open' : 'send');
    });
    _reportImpl('open');
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void _initData(InitDataEvent event, Emitter emit) async {
    if (state.isError) {
      emit.call(state.clone(isError: false));
    }
    final info = await imFunctionService.getMagicBoxInfo(targetId: targetId);
    if (info == null) {
      emit.call(state.clone(isError: true));
    } else {
      emit.call(state.clone(model: info, isError: false));
    }
  }

  void _reportImpl(String page) async {
    final groupInfo = await imCacheService.getGroupInfo(targetId: targetId);
    GroupStatistics.reportMagicboxImp(
        page: page, groupId: targetId, familyId: groupInfo?.familyId);
  }
}
