import 'package:biz/biz/chat/chat_details/widgets/magic_box/bloc/magic_box_bloc.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/open_box/open_box_widget.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/send_keys/send_keys_widget.dart';
import 'package:biz/biz/chat/chat_details/widgets/magic_box/task/magic_task_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:service/global/const/urls.dart';
import 'package:service/service.dart';

class MagicBoxWidget extends StatefulWidget {
  final String targetId;

  MagicBoxWidget({required this.targetId});

  @override
  State<StatefulWidget> createState() => _MagicBoxWidgetState();
}

class _MagicBoxWidgetState extends State<MagicBoxWidget>
    with SingleTickerProviderStateMixin {
  late final MagicBoxBloc _bloc = MagicBoxBloc(targetId: widget.targetId);

  @override
  void initState() {
    super.initState();
    _bloc.tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<MagicBoxBloc, MagicBoxState>(
        bloc: _bloc,
        builder: (_, __) {
          return Material(
            color: Colors.transparent,
            child: Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    top: 30.pt,
                    bottom: 0,
                    child: _bgColor()),
                _bg(),
                _rule(),
                Positioned.fill(child: _detail())
              ],
            ),
          );
        });
  }

  Widget _rule() {
    return Positioned(
      right: 11.pt,
      top: 35.pt,
      child: GestureDetector(
        onTap: () {
          routerUtil.push(R_WEB, params: {P_URL: urlMagicBoxFaq});
        },
        child: FLImage.asset(
          Res.chatIconRule,
          width: 20.pt,
          height: 20.pt,
        ),
      ),
    );
  }

  Widget _list() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 1.w,
            height: 360.pt,
            child: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: _bloc.tabController,
              children: [
                OpenBoxWidget(
                  targetId: widget.targetId,
                  boxList: _bloc.state.model?.boxList,
                  boxKeys: _bloc.state.model?.boxKeys,
                ),
                SendKeysWidget(
                  targetId: widget.targetId,
                  count: _bloc.state.model?.boxKeys?.earningKeys ?? 0,
                ),
              ],
            ),
          ),
          MagicTaskWidget(
              targetId: widget.targetId, tasks: _bloc.state.model?.tasks),
        ],
      ),
    );
  }

  Widget _detail() {
    return Column(
      children: [
        SizedBox(height: 19.pt),
        Text(
          LocaleStrings.instance.magicBox,
          style: TextStyle(
              fontStyle: FontStyle.italic,
              fontWeight: FontWeight.bold,
              fontSize: 18.sp,
              color: Color(0xFFFFD039)),
        ),
        SizedBox(height: 14.pt),
        if (_bloc.state.model != null) ..._content(),
        if (_bloc.state.isError) _netFailed(),
        if (!_bloc.state.isError && _bloc.state.model == null)
          Expanded(child: GlobalWidgets.pageLoading()),
      ],
    );
  }

  List<Widget> _content() {
    return [
      Text(
        LocaleStrings.instance.exchangeKeysToUnlockBox,
        style: TextStyle(
            fontSize: 13.sp, fontWeight: FontWeight.w600, color: Colors.white),
      ),
      SizedBox(height: 4.pt),
      Text(
        LocaleStrings.instance.roomTaskRefreshTime,
        style: TextStyle(
            fontSize: 10.sp,
            color: Color(0x87FFFFFF)),
      ),
      SizedBox(height: 12.pt),
      Container(
        width: 240.pt,
        height: 32.pt,
        decoration: BoxDecoration(
            border: Border.all(color: Color(0xFF90672F), width: 1.pt),
            color: Color(0xFF1D0207),
            borderRadius: BorderRadius.circular(25.pt)),
        child: _tabBar(),
      ),
      Expanded(child: _list())
    ];
  }

  Widget _tabBar() {
    return TabBar(
        controller: _bloc.tabController,
        isScrollable: false,
        unselectedLabelStyle:
            TextStyle(fontSize: 14.sp, color: Color(0xC7FFFFFF)),
        labelStyle: TextStyle(
            fontWeight: FontWeight.bold, fontSize: 16.sp, color: Colors.white),
        unselectedLabelColor: Colors.white,
        labelPadding: EdgeInsets.zero,
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
          gradient:
              LinearGradient(colors: [Color(0xFFF37B3A), Color(0xFFFFD486)]),
          borderRadius: BorderRadius.all(Radius.circular(25.pt)),
        ),
        tabs: [
          Tab(
              child: Container(
            height: 32.pt,
            width: 120.pt,
            alignment: Alignment.center,
            child: Text(LocaleStrings.instance.openBox),
          )),
          Tab(
              child: Container(
            height: 32.pt,
            width: 120.pt,
            alignment: Alignment.center,
            child: Text(LocaleStrings.instance.sendKeys),
          )),
        ]);
  }

  Widget _bg() {
    return Container(
      width: 1.w,
      height: 1.h * 0.72,
      alignment: Alignment.topCenter,
      child:
          FLImage.asset(Res.chatBgMagicBox, width: 1.w, fit: BoxFit.fitWidth),
    );
  }

  Widget _bgColor() {
    return Container(
      height: double.infinity,
      decoration: BoxDecoration(
          color: Color(0xFF110702),
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.pt))),
    );
  }

  /// 网络异常
  Widget _netFailed() {
    return EmptyWidget(
      isDark: true,
      logoAsset: Res.emptyEmptyNetwork,
      title: LocaleStrings.instance.hintNetError,
      detail: LocaleStrings.instance.checkNetWork,
      btnSting: LocaleStrings.instance.tryAgain,
      btnAction: () => _bloc.add(InitDataEvent()),
    );
  }
}
