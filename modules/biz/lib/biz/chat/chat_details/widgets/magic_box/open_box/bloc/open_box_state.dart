part of 'open_box_bloc.dart';

@immutable
class OpenBoxState {

  final MagicBoxDetail? currentBox;
  final List<MagicBoxDetail>? boxList;
  final MagicBoxKeys? boxKeys;

  OpenBoxState({this.currentBox, this.boxList, this.boxKeys});

  OpenBoxState clone(
      {MagicBoxDetail? currentBox,
      List<MagicBoxDetail>? boxList,
      MagicBoxKeys? boxKeys}) {
    return OpenBoxState(
      currentBox: currentBox ?? this.currentBox,
      boxList: boxList ?? this.boxList,
      boxKeys: boxKeys ?? this.boxKeys,
    );
  }
}
