import 'package:biz/biz/task/util/task_action_link.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:meta/meta.dart';
import 'package:service/common/statistics/group_statistics.g.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/sticker/const/task_action_code.dart';
import 'package:service/modules/task/model/task_model.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';
import 'package:service/utils/value_parsers.dart';

part 'magic_task_event.dart';
part 'magic_task_state.dart';

class MagicTaskBloc extends BaseBloc<MagicTaskEvent, MagicTaskState> {
  MagicTaskBloc({required this.targetId, this.tasks})
      : super(MagicTaskState()) {
    on<RefreshDataEvent>(_refreshData);
    on<ClickEvent>(_clickItem);
  }

  final String targetId;

  final List<TaskModel>? tasks;

  @override
  void initBloc() {
    super.initBloc();
    add(RefreshDataEvent(tasks));
  }

  void _clickItem(ClickEvent event, Emitter emit) async {
    final task = event.task;
    GroupStatistics.reportMagicboxTaskClick(
        groupId: targetId,
        status: task.status == 0 ? "1" : "0",
        content: task.taskInfo?.code);
    if (task.status == 0) {
      showLoading();
      final resp = await imFunctionService.getMagicTask(
          targetId: targetId, taskName: task.taskName ?? '');
      hideLoading();
      if (resp.isSuccess) {
        rxUtil.send(
            ImFunctionEvent.receiveMagicKeys, toInt(task.bonus?.rewardKeys));
        final list = state.tasks;
        list?[event.index]?..status = 1;
        emit.call(state.clone(tasks: list));
      } else {
        toast(resp.msg ?? LocaleStrings.instance.tryAgain);
      }
    } else if (task.status == -1) {
      if (task.taskInfo?.code != TaskActionCodes.chatMagicBoxSendKeys) {
        routerUtil.removeAll(R_CHAT_MAGIC_BOX_DIALOG);
      }
      if (task.taskInfo?.router?.isEmpty ?? true) {
        TaskActionLink.check(task.taskInfo?.code,
            action: task.taskInfo?.action,
            params: {
              'target_id': targetId,
              'from': 'magic_box',
              'is_chat_group': '1'
            });
      } else {
        TaskActionLink.jump(
            routeUrl: task.taskInfo?.router, actionCode: task.taskInfo?.action);
      }
    }
  }

  void _refreshData(RefreshDataEvent event, Emitter emit) async {
    emit.call(state.clone(tasks: event.tasks));
  }
}
