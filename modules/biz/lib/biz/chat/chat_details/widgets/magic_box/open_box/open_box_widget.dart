import 'package:biz/biz/chat/chat_details/widgets/magic_box/open_box/bloc/open_box_bloc.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/cache_image_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/modules/im/model/magic_box_model.dart';
import 'package:service/service.dart';

class OpenBoxWidget extends StatefulWidget {
  OpenBoxWidget({required this.targetId, this.boxList, this.boxKeys});

  final MagicBoxKeys? boxKeys;
  final List<MagicBoxDetail>? boxList;
  final String targetId;

  @override
  State<StatefulWidget> createState() => _OpenBoxWidgetState();
}

class _OpenBoxWidgetState extends State<OpenBoxWidget>
    with AutomaticKeepAliveClientMixin {
  late final OpenBoxBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = OpenBoxBloc(targetId: widget.targetId, boxList: widget.boxList,
        boxKeys: widget.boxKeys);
  }

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<OpenBoxBloc, OpenBoxState>(
      bloc: _bloc,
      builder: (_, __) {
        return _content();
      },
    );
  }

  Widget _content() {
    if (_bloc.state.boxList?.isEmpty ?? true) {
      return SizedBox();
    }
    final boxRequires = _bloc.state.currentBox?.boxRequires;
    final opened = _bloc.state.currentBox?.status == 1;
    return Column(
      children: [
        SizedBox(height: 10.pt),
        Stack(children: [
          SizedBox(
            width: 1.w,
            height: 146.pt,
          ),
          CarouselSlider(
            items: _bloc.state.boxList!
                .map((e) => Container(
                      child: Container(
                        child: CachedNetworkImage(
                          imageUrl:
                              (e.status == 1 ? e.openIcon : e.closeIcon) ?? '',
                          fit: BoxFit.cover,
                          height: 146.pt,
                          errorWidget: (_, __, ___) {
                            return GlobalWidgets.imageError();
                          },
                          progressIndicatorBuilder: (context, url, progress) {
                            return GlobalWidgets.imagePlaceholder();
                          },
                        ),
                      ),
                    ))
                .toList(),
            options: CarouselOptions(
              onPageChanged: (index, _) {
                _bloc.add(
                    RefreshDataEvent(currentBox: _bloc.state.boxList![index]));
              },
              height: 146.pt,
              enlargeCenterPage: true,
              enableInfiniteScroll: true,
              enlargeFactor: 0.6,
              viewportFraction: 0.5,
              enlargeStrategy: CenterPageEnlargeStrategy.height,
            ),
          ),
        ]),
        SizedBox(height: 8.pt),
        Text(
          boxRequires?.diamond != null && boxRequires?.diamond != 0
              ? LocaleStrings.instance.keysAndDiamondsOpenThisBox(
                  boxRequires?.keys ?? 0, boxRequires?.diamond ?? 0)
              : LocaleStrings.instance.keysOpenThisBox(boxRequires?.keys ?? 0),
          style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white),
        ),
        SizedBox(height: 30.pt),
        Text(
          LocaleStrings.instance
              .keysGiftedByOthers(_bloc.state.boxKeys?.receiveKeys ?? 0),
          style: TextStyle(
            fontSize: 16.sp,
            color: Color(0xFFFFC46A),
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 15.pt),
        ScaleTapWidget(
          enabled: !opened,
          onTap: () => _bloc.add(ClickOpenEvent()),
          child: Container(
            height: 45.pt,
            width: 253.pt,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(opened
                        ? Res.chatBgMagicBoxOpened
                        : Res.chatBgMagicBoxOpen),
                    fit: BoxFit.cover)),
            alignment: Alignment.center,
            child: Text(
              opened
                  ? LocaleStrings.instance.opened
                  : LocaleStrings.instance.open,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: opened ? Colors.white : Color(0xFF903801),
                  fontSize: 18.sp,
                  shadows: opened ? null: [
                    Shadow(color: Color(0xFFFFE9A4), offset: Offset(0, 1.2))
                  ]),
            ),
          ),
        ),
        SizedBox(height: 15.pt),
        ScaleTapWidget(
          onTap: () => _bloc.add(RequiredKeyEvent()),
          child: Container(
            height: 45.pt,
            width: 253.pt,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(Res.chatBgMagicRequestKey),
                    fit: BoxFit.cover)),
            alignment: Alignment.center,
            child: Text(
              LocaleStrings.instance.requestKey,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B619D),
                  fontSize: 18.sp,
                  shadows: [
                    Shadow(color: Color(0xFFC6E5FF), offset: Offset(0, 1.2))
                  ]),
            ),
          ),
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
