import 'package:biz/biz/chat/chat_details/widgets/msg_content/i_msg_content.dart';
import 'package:biz/biz/chat/chat_details/widgets/msg_content/msg_content_share/widget/common_share.dart';

import 'package:biz/biz/chat/chat_details/widgets/msg_content/msg_content_share/widget/event_tag_share.dart';

import 'package:biz/biz/chat/chat_details/widgets/msg_content/msg_content_share/widget/moment_share.dart';
import 'package:biz/biz/chat/chat_details/widgets/msg_content/msg_content_share/widget/room_share.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:service/modules/im/model/pb_msg_content.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/pb/net/pb_share.pbenum.dart';

class MsgContentShare extends StatelessWidget with IMsgContent {
  const MsgContentShare(
      {Key? key,
      required this.content,
      required this.fromSelf,
      this.targetUser})
      : super(key: key);
  final bool fromSelf;
  final PbMsgContent content;
  final UserInfo? targetUser;

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    var detail = content.getData<PbImShare>();
    if (detail == null) return Container();
    switch (detail.type) {
      case PbShareType.PbShareType_LIVE_ROOM:
        return RoomShare(detail: detail, fromSelf: fromSelf);
      case PbShareType.PbShareType_MOMENT:
        return MsgContentMomentShare(detail: detail, fromSelf: fromSelf);

      case PbShareType.PbShareType_WEB:
        return MsgContentCommonShare(detail: detail, fromSelf: fromSelf);

      case PbShareType.PbShareType_LIVE_EVENT_TAG:
        return MsgContentEventTagShare(
          detail: detail,
          fromSelf: fromSelf,
        );
    }
    return Container();
  }

  @override
  bool get isBubble => false;
}
