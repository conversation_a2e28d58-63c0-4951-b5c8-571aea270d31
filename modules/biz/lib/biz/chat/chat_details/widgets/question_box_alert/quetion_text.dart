import 'package:biz/utils/arabic_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/service.dart';

///问题widget
class QuestionAniTextWidget extends StatefulWidget {
  QuestionAniTextWidget(this.question);

  final String question;

  @override
  QuestionAniTextWidgetState createState() => QuestionAniTextWidgetState();
}

class QuestionAniTextWidgetState extends State<QuestionAniTextWidget>
    with TickerProviderStateMixin {
  AnimationController? _controller;
  Animation? _animation;

  String? _question;

  @override
  void initState() {
    super.initState();
    _question = widget.question;
    _controller =
        AnimationController(vsync: this, duration: Duration(milliseconds: 500));

    if (_controller != null) {
      _animation = Tween(begin: 1.0, end: 0.0).animate(CurvedAnimation(
        parent: _controller!,
        curve: Curves.fastOutSlowIn,
      ));
    }
  }

  @override
  void didUpdateWidget(QuestionAniTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    var questionNew = widget.question;

    if ((_question?.isNotEmpty ?? false) && questionNew != _question) {
      _controller?.reset();
      _controller?.forward();
    } else {
      _controller?.value = 1;
    }

    if (mounted) {
      setState(() {
        _question = questionNew;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return _controller != null
        ? AnimatedBuilder(
            animation: _controller!,
            builder: (BuildContext context, Widget? child) {
              return ClipRRect(
                  child: Transform(
                transform: Matrix4.translationValues(
                    (_animation?.value ?? 0.0) * width, 0.0, 0.0),
                child: _questionWidget(_question ?? ""),
              ));
            })
        : SizedBox();
  }

  Widget _questionWidget(String question) {
    bool hadAr = hasArabicGlyphs(question);
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.pt),
      child: Text(
        question,
        textDirection: hadAr ? TextDirection.rtl : TextDirection.ltr,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeightExt.heavy,
          fontSize: 26.sp,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
