import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'dart:math';

import 'package:audio_player/audioplayers.dart';
import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/const/const.dart';
import 'package:biz/biz/chat/chat_details/model/chat_quit_wreck.dart';
import 'package:biz/biz/chat/chat_details/widgets/community_rules_dialog.dart';
import 'package:biz/biz/chat/chat_details/widgets/disturb_alert_dialog.dart';
import 'package:biz/biz/chat/chat_details/widgets/emoji_rain_widget.dart';
import 'package:biz/biz/chat/chat_details/widgets/leaving_confirm_dialog.dart';
import 'package:biz/biz/chat/chat_details/widgets/msg_asset_picker.dart';
import 'package:biz/biz/chat/chat_details/widgets/msg_item_action_menu.dart';
import 'package:biz/biz/chat/chat_details/widgets/record/record_btn.dart';
import 'package:biz/biz/chat/chat_details/widgets/refer_msg/refer_out_content_factory.dart';
import 'package:biz/biz/chat/chat_details/widgets/sensitive_alert_dialog.dart';
import 'package:biz/biz/chat/dialog/at_user_dialog.dart';
import 'package:biz/biz/chat/dialog/mak_call_dialog.dart';
import 'package:biz/biz/evaluate/handler/evaluate_handle.dart';
import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge/recharge_dialog.dart';
import 'package:biz/biz/gift/dialog/gift_dialog.dart';
import 'package:biz/biz/live_room/component/gift_panel/gift_panel_widget.dart';
import 'package:biz/biz/user/chat_pre/chat_pre_router.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/statistic/bloc/page_duration_statistic_mixin.dart';
import 'package:biz/global/widgets/bottom_drawer.dart';
import 'package:biz/global/widgets/input_bar/input_bar.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:service/common/async_token.dart';
import 'package:service/common/statistics/chat_block_statistics.g.dart';
import 'package:service/common/statistics/chat_statistics.g.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/chat_detail_statistic_handler.dart';
import 'package:service/common/statistics/qbox_statistics.g.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/extension/xfile.dart';
import 'package:service/global/const/recommend_type.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/sp.dart';
import 'package:service/global/widget/soft_keyboard_wrapper.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/bottle/bottle_utils.dart';
import 'package:service/modules/bottle/const/events.dart';
import 'package:service/modules/call/call_util.dart';
import 'package:service/modules/family/const/events.dart';
import 'package:service/modules/family/model/family_info_model.dart';
import 'package:service/modules/family/model/family_setting_model.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/home/<USER>/home_wink_source_handler.dart';
import 'package:service/modules/im/const/const.dart';
import 'package:service/modules/im/const/enums.dart';
import 'package:service/modules/im/const/events.dart' as im;
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/handler/chat_handler.dart';
import 'package:service/modules/im/model/audio_msg_content.dart';
import 'package:service/modules/im/model/db_msg.dart';
import 'package:service/modules/im/model/group_info.dart';
import 'package:service/modules/im/model/im_gift_message_content.dart';
import 'package:service/modules/im/model/image_msg_content.dart';
import 'package:service/modules/im/model/input_status.dart';
import 'package:service/modules/im/model/local_msg_content.dart';
import 'package:service/modules/im/model/match_chat_send_msg.dart';
import 'package:service/modules/im/model/msg.dart';
import 'package:service/modules/im/model/msg_content.dart';
import 'package:service/modules/im/model/pb_msg_content.dart';
import 'package:service/modules/im/model/reference_msg_content.dart';
import 'package:service/modules/im/model/text_msg_content.dart';
import 'package:service/modules/im/model/video_msg_content.dart';
import 'package:service/modules/im/rong_cloud/convert/const_converter.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/modules/live/room_gift/model/gift_display_msg.dart';
import 'package:service/modules/live/room_gift/model/send_gift_resp.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/modules/questions/const/events.dart';
import 'package:service/modules/questions/model/begin_question.dart';
import 'package:service/modules/social/model/icebreaker.dart';
import 'package:service/modules/social/model/pre_say_hi_data.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/sticker/const/enums.dart';
import 'package:service/modules/sticker/model/sticker.dart';
import 'package:service/modules/user/model/social_detail.dart';
import 'package:service/modules/user/model/user_count_info.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_common.pb.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/pb/net/pb_push.pbserver.dart';
import 'package:service/utils/asset_compressor.dart';
import 'package:service/utils/global_audio_player.dart';
import 'package:service/utils/loading.dart';

import '../../../../global/statistic/chat_send_statistic.dart';
import '../../chat_mode/mode_utils.dart';
import '../chat_page_controller.dart';
import '../widgets/punch_game/punch_game_controller.dart';
import '../widgets/quick_action/chat_quick_action_controller.dart';
import 'chat_report_mixin.dart';

part 'bottom_panel_control_mixin.dart';
part 'chat_event.dart';
part 'chat_state.dart';

mixin AbsChatModuleController {
  ChatBloc get chatBloc;
}

class ChatBloc extends PageVisibilityBloc<ChatEvent, ChatState>
    with WidgetsBindingObserver, BottomPanelControlMixin, PageTimingMixin, PageDurationStatisticMixin, ChatReportMixin {
  static const String _tag = 'ChatBloc';
  final AutoScrollController scrollController = AutoScrollController();
  final FakeRecordBtnController recordBtnController = FakeRecordBtnController();

  String get getxTag => 'chat_$hashCode';

  PunchGameController get punchGameController => Get.put<PunchGameController>(PunchGameController(), tag: getxTag);

  ChatQuickActionController get quickActionController =>
      Get.put<ChatQuickActionController>(ChatQuickActionController(this), tag: getxTag);

  ChatPageController get chatPageController => Get.put<ChatPageController>(ChatPageController(this), tag: getxTag);

  /// 揭露身份 svg Controller
  SvgaController? uncoverController;

  bool? _targetOnline;
  bool _hasShownLeavingDialog = false;

  String? get chatTargetId => getArgument<String>(P_TARGET_ID);

  String? get pageFrom => getArgument<String>(P_STATISTIC_FROM);

  String? matchType;

  String? get pKeyword => getArgument<String?>(P_KEYWORD);

  String? get source => getArgument<String>(P_SOURCE);

  String? get roomId => getArgument<String>(P_ROOM_ID);

  String? get roomOwnerId => getArgument<String>(P_ROOM_OWNER_ID);

  bool get isBottleChat =>
      getArgument<bool?>(P_IS_BOTTLE_CHAT) == true || getArgument<String?>(P_IS_BOTTLE_CHAT) == "true";

  /// 家族群聊
  bool get isFamilyChat => (chatTargetId?.contains(imChatService.familyChatPrefix) ?? false);

  /// 普通群聊，非漂流瓶群聊
  bool get isBaseGroupChat => conversationType == RCIMIWConversationType.group && !isBottleChat;

  /// 是否私聊
  bool get isPrivateChat => conversationType == RCIMIWConversationType.private;

  /// 是否群聊
  bool get isGroupChat => conversationType == RCIMIWConversationType.group;

  Msg? get highlightMsg => getArgument<Msg?>(P_DATA);

  MsgContent? get referData => getArgument<MsgContent?>(P_CHAT_REFER_DATA);

  RCIMIWConversationType get conversationType {
    final type = getArgument(P_CONVERSATION_TYPE);
    if (type is RCIMIWConversationType) return type;
    final typeInt = int.tryParse(type);
    if (typeInt != null) return getRCConversationTypeByRaw(typeInt);
    return (isBottleChat ? RCIMIWConversationType.group : RCIMIWConversationType.private);
  }

  bool get isFromMatching => getArgument<String>(P_IS_MATCH) == '1';

  bool get isOfficial => (getArgument<String>(P_TARGET_ID)?.length ?? 0) == assistanceLength;

  bool get isActivityAssistance => getArgument<String>(P_TARGET_ID) != activityAssistantUid;

  bool get isFamilyAssistantUid => getArgument<String>(P_TARGET_ID) != familyAssistantUid;

  bool get needAutoSend => getArgument<String?>(P_NEED_AUTO_SEND_MSG) == "true";

  bool get showGiftPanel => getArgument<bool>(P_SHOW_GIFT_PANEL) ?? false;

  // bool get needSendGiftUnlock => getArgument<String?>(P_UNLOCK_GIFT) == '1';
  // bool get preChatCheck => (getArgument<bool>(P_CHECK_PRE_CHAT) ?? true);
  bool get preChatCheck => false;

  /// 打点参数
  String get chatFromType => isBottleChat ? "bottle" : "im";

  /// 初始化默认聊过天
  bool hasValidMsg = true;

  Timer? _timerBubbleGuide;

  ///是否点击过界面
  bool hasPagePanDown = false;

  bool? _canChat;

  final emojiRainWidgetController = EmojiRainWidgetController();

  /// 本次会话是否是新会话
  bool beforeIsNewChat = false;

  /// 是否异性
  bool oppositeSex = false;

  /// @的列表，key=id,value=name, 所有人id为0,
  final Map<String, String> _atUserMap = {};
  final _atAllStr = '@{0} ';

  /// 顶部在房状态
  Widget? inRoomBar;

  /// 家族群聊在房用户列表
  Widget? familyInRoomUsers;

  bool _firstLoad = false;

  bool get _isBell => getArgument('P_SOURCE') == ChatSourceType.fateBell;

  bool get inRoom => getArgument(P_IS_ROOM) ?? false;

  FamilySettingModel? familySettingModel;
  FamilyInfoModel? familyInfoModel;

  bool get isBanned => (isFamilyChat && familySettingModel?.speakSetting == 2
      && familyInfoModel?.isPatriarchOrVice == false);

  bool isUserBanned = false;

  @override
  String get pageDurationKey => "chat_page_key";

  ChatBloc() : super(ChatState.init()) {
    on<InitChatEvent>(_initData);
    on<ChatEventBack>(_onChatTapBack);
    on<ChatEventTapBlank>(_onChatTapBlank);
    on<ChatEventRecordFinished>(_onChatRecordFinished);
    on<ChatEventPagePanDown>(_onChatPagePanDown);
    on<ChatEventSendSticker>(_sendStick);
    on<ChatEventRecord>((event, emit) {
      switchBottomPanel(bottomDrawerType != BottomDrawerType.recordType || !bottomDrawerController.isOpen,
          type: BottomDrawerType.recordType);
    });
    on<ChatEventLive>((event, emit) => _gotoLive());
    on<ChatEventAlbum>((event, emit) => _pickAndSendImageVideo());
    on<ChatEventQASwitch>(_onTapQaSwitch);
    on<ChatEventCall>((event, emit) => _call());
    on<ChatEventInputting>((event, emit) => _sendInputStatus(event));
    on<ChatEventSendText>((event, emit) => _sendText());
    on<HasValidMsgChangeEvent>((event, emit) => emit.call(state.clone()));
    on<ChatEventInputPanelSwitched>((event, emit) {
      if (event.show) switchBottomPanel(false);
    });
    on<ChatEventCancelRefer>((event, emit) => emit.call(state.remove(referringData: true)));
    on<ShowQuestionBoxBubbleEvent>((event, emit) => emit.call(state.clone(showQuestionBoxBubble: true)));
    on<ChatEventKeyboardSwitched>((event, emit) => rxUtil.send(im.KeyboardEvent.keyboardChange, event.visible));
    on<SearchStickersPanelChangeEvent>((event, emit) => emit.call(state.clone(searchStickersPanelVisible: event.show)));
    on<ChangeViewInsetsBottomEvent>((event, emit) => emit.call(state.clone(viewInsetsBottom: event.viewInsetsBottom)));
    on<ReferChatMsgEvent>((event, emit) => emit.call(state.clone(referringMsg: event.msg)));
    on<ChatEventUserGuide>((_, __) {
      return routerUtil.push(R_FAQ);
    });
    on<ChatEventGift>((_, __) => _showGiftPanel());
    on<ChatEventOtherInputting>((event, emit) {
      if (event.type == null) {
        emit.call(state.remove(inputType: true));
      } else {
        emit.call(state.clone(inputType: event.type));
      }
    });
    on<ChatEventUncoverIdentity>(_uncoverIdentity);
    on<ChatEventBottleHadDelete>(_bottleHadDelete);
    on<ChangeModeEvent>(_onChangeMode);
    on<ClickChangeModeEvent>((_, __) => _onClickChangeMode());

    on<ShowChatModeBubbleEvent>((event, emit) =>
        emit.call(state.clone(showSafeModeBubble: event.show, isFreeRequest: event.lastRequestTypeFree)));

    on<ChatModeBubbleCloseEvent>((event, emit) => _closeChatModeBubble());

    on<ShowInRoomBarEvent>((event, emitter) => emitter.call(state.clone(showInRoomBar: event.show)));
    on<InputAtEvent>(_inputAt);
    // on<ChatEventExpandFuncBar>(_onClickExpandFuncBar);
    on<ChatEventSendGift>(_onClickSendGift);
    on<ChatEventTapGreetMsg>(_onChatTapGreetMsg);
    on<ChatEventCloseGreetMsg>(_onChatCloseGreetMsg);
    on<ShowGiftGuideEvent>(_showGiftGuide);
    on<ShowUnlockGiftGuideEvent>(_showUnlockGiftGuide);
    on<SendUnlockGiftEvent>(_sendUnlockGift);
    on<ShowUnlockGiftWidgetEvent>(_showUnlockGiftView);
    on<ChatGroupBanEvent>((event, emit) => emit.call(state.clone()));
    on<ChatGroupMemberRoleChangedEvent>((event, emit){
      familyInfoModel?.role = event.role;
      emit.call(state.clone());
    });

    quickActionController.getxTag = getxTag;
  }

  @override
  void setArguments(Object? args) {
    super.setArguments(args);
    emojiRainWidgetController.uid = chatTargetId;
    quickActionController.targetId = chatTargetId;
    quickActionController.source = source;
    punchGameController.source = source;
  }

  @override
  void initBloc() {
    super.initBloc();
    checkMatchType();
    add(InitChatEvent());
    _initRx();
  }

  /// 释放各个模块Controller
  void _releaseModuleController() {
    Get.delete<PunchGameController>(tag: getxTag);
    Get.delete<ChatQuickActionController>(tag: getxTag);
    Get.delete<ChatPageController>(tag: getxTag);
  }

  void checkMatchType() {
    String type = 'other';
    String? tempMatchType = getArgument<String>(P_STATISTIC_MATCH_TYPE);
    if (tempMatchType?.isNotEmpty == true) {
      type = tempMatchType!;
    } else {
      Route<dynamic>? previousRoute = FLRouter.routeObserver.getPreviousRoute;
      if (previousRoute != null && previousRoute.settings.name != null) {
        type = previousRoute.settings.name!;
      }
    }
    matchType = type;
  }

  void _initRx() async {
    listenRxEvent<bool>(im.SearchPanelEvent.searchPanelChange, _searchPanelChange);
    listenRxEvent<ChatEventLongPressMsg>(im.ChatEvent.longPressMsg, _handleMsgLongPress);
    listenRxEvent<SendGiftResp>(ChatGiftEvent.sendSuccess, _giftSendSuccess, isSingle: true);
    listenRxEvent<SendGiftResp>(ChatGiftEvent.endCombo, _endCombo);

    if (isBottleChat) {
      listenRxEvent<String>(BottleEvent.uncoverIdentitySuc, _handleUncoverIdentity);
      listenRxEvent<String>(BottleEvent.uncoverIdentityApply, _handleUncoverIdentityApply);
      listenRxEvent<String>(BottleEvent.destroyGroup, _destroyGroup);
    } else if (isGroupChat) {
      listenRxEvent<UserInfo>(im.ChatEvent.atUser, _atUser);
      listenRxEvent<UserInfo>(RoomUserOptEvent.atUser, _atUser);
      listenRxEvent<String>(BottleEvent.destroyGroup, _destroyGroup);
      listenRxEvent<int>(im.ChatEvent.groupBan, _groupBan);
      listenRxEvent<int>(im.ChatEvent.groupMemberRoleChanged, (value){
        add(ChatGroupMemberRoleChangedEvent(value));
      });
      listenRxEvent<PbGroupMemberNotice>(FamilyEvent.changedRoleByOthers, (notice) {
        if (chatTargetId?.contains(notice.groupId) == true) {
          add(ChatGroupMemberRoleChangedEvent(notice.familyRole));
        }
      });
    } else {
      listenRxEvent<BeginQuestion>(QuestionBoxStateEvent.beginQuestion, _beginQuestionBox);
      listenRxEvent<int>(ChatSafeModeEvent.closeBubble, (value) => _closeChatModeBubble());
      listenRxEvent<ChatSafeMode>(ChatSafeModeEvent.modeChange, _changeMode);
      listenRxEvent<bool>(ChatSafeModeEvent.modeRequest, _isShowChatModeBubble);
      listenRxEvent<bool>(im.ChatEvent.unlockChat, (_) {
        final gift = state.unlockGift;
        if (gift != null) {
          add(ShowUnlockGiftGuideEvent(null));
        }
        chatPageController.updateEmojiStatus(false);
        sendLocalMSg(LocaleStrings.instance.chatUnlockedSuccess);
      });
      listenRxEvent<bool>(im.ChatEvent.unlockFeature, (_) {
        chatPageController.loadPrivateChatInfo(refresh: true);
      });
    }

    punchGameController.showPunchGameEntry.listen((value) {
      quickActionController.onPunchGameEntryChange(value);
    });

    punchGameController.punchGameEnable.listen((value) {
      quickActionController.onPunchGameEnableChange(value);
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      /// 移除其他聊天详情页
      routerUtil.removeAllExceptLast(R_CHAT);
    });
  }

  /// 监听页面生命周期
  @override
  void onChangeDependencies() {
    super.onChangeDependencies();
    final context = getContext();
    if (context != null) addPageVisibilityObserver(ModalRoute.of(context));
  }

  // Future<void> _checkPermission() async {
  //   if (Platform.isAndroid) {
  //     if (globalSp.getBool(spKeyHasRequestStorageInChat) != true) {
  //       var context = getContext();
  //       if (context != null) {
  //         String content = ReportAuthType.storage;
  //         String page = ReportAuthPage.chat;
  //         requestPermission(context,
  //             type: PermissionType.storage,
  //             authContent: content,
  //             authPage: page);
  //       }
  //
  //       globalSp.setBool(spKeyHasRequestStorageInChat, true);
  //     }
  //   }
  // }

  void _checkAndShowDialog() {
    final context = getContext();
    if (context == null) return;

    // 社区规范
    final hasShownRules = globalSp.getBool(spKeyHasShownCommunityRules) ?? false;
    if (!hasShownRules) {
      showCommunityRulesDialog(context);
      globalSp.setBool(spKeyHasShownCommunityRules, true);
    }
  }

  /// 只初始化用户相关信息和页面布局信息
  Future<void> _initData(InitChatEvent event, Emitter<ChatState> emit) async {
    final targetId = chatTargetId ?? '';
    if (targetId.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        toast(LocaleStrings.instance.videoTryAgain);
        if (getContext() != null) routerUtil.pop(context: getContext());
      });
      Log.e(_tag, 'targetId = $targetId');
      return;
    }

    await _loadBaseInfo(targetId, emit);
    _loadTargetOnlineStatus();
    // _loadBubbleGuide();
    // _randomSendMsg(targetId);
    _insertUnRespTip();
    if (isBottleChat) {
      _loadBottle();
    }

    if (!_firstLoad && showGiftPanel == true) {
      _firstLoad = true;
      add(ChatEventSendGift());
    }
  }

  void _beginChat() {
    WidgetUtils.post((_) {
      if (isPageValid) _checkAndShowDialog();
    });
    // _checkPermission().then((value) {
    //   WidgetUtils.post((_) {
    //     if (isPageValid) _checkAndShowDialog();
    //   });
    // });
  }

  void _gotoLive() async {
    // bool can =
    //     await RoomHelper.checkCanCreateRoom(from: StatisticPageFrom.chat);
    // reportClickLive();
    // if (can) {
    //
    //   switchRecordPanel(true, type: BottomDrawerType.liveType);
    // }
  }

  Future<void> _loadBaseInfo(String targetId, Emitter<ChatState> emit) async {
    UserInfo? targetUser;
    GroupInfoModel? groupInfo;
    SocialDetail? relation;
    final currentUser = await userService.getUserInfo(accountService.currentUid() ?? "", fromServer: false, needUpdate: true);
    UserCountInfo? userCountInfo;
    if (isBottleChat) {
      var values = targetId.split("_");
      if (values.length == 4) {
        var userId = values[2];
        var realTargetId = userId == currentUser?.uid ? values[3] : userId;
        targetUser = await userService.getUserInfo(realTargetId);
      }
    } else if (isBaseGroupChat) {
      chatPageController.updateExpandListByGroupChat();
      groupInfo = await imCacheService.getGroupInfo(targetId: targetId, fromServer: true);
      rxUtil.send(im.ChatEvent.getLatestGroupInfo, groupInfo);
      isUserBanned = groupInfo?.isMute ?? false;
      familySettingModel = (await familyService.getSettingsInfo(familyId: groupInfo?.familyId ?? "")).data;
      familyInfoModel = (await familyService.getMyFamilyInfo()).data;
      userCountInfo = await userService.getUserCount();

      if (userCountInfo.luckyBagPermission == true) {
        RoomStatistics.reportLuckybagEntranceImp(groupId: chatTargetId, from: "group");
      }
    } else {
      targetUser = await userService.getUserInfo(targetId);
      relation = await userService.getRelation(targetUid: targetId);
    }
    punchGameController.initData(targetId: targetId, currentUser: currentUser, targetUser: targetUser);

    oppositeSex = targetUser?.sex != currentUser?.sex;
    Log.d(_tag, 'relation = ${relation.toString()}');
    emit.call(state.clone(
      targetUser: targetUser,
      currentUser: currentUser,
      groupInfo: groupInfo,
      userCountInfo: userCountInfo,
      canCall: relation?.isFriend ?? false,
      referringMsgContent: referData,
    ));
    if (isPrivateChat) {
      chatPageController.loadPrivateChatInfo();
      chatPageController.loadGameInfo();
      _checkAndInitGiftUnlockChat();
      // await _loadChatMode(emit);
      _intBeforeIsNewChat();
    } else {
      _canChat = true;
      _beginChat();
      ChatDetailReporter.reportMsgListImp(pageFrom, matchType, chatTargetId, hasValidMsg, false,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
    }
  }

  Future<void> _loadChatMode(Emitter<ChatState> emit) async {
    if (chatTargetId?.isNotEmpty == true) {
      bool isFreeMode = await chatSafeModeService.isFreeChatMode(targetId: chatTargetId!);
      emit.call(state.clone(isFreeMode: isFreeMode));
      ChatDetailReporter.reportMsgListImp(pageFrom, matchType, chatTargetId, hasValidMsg, isFreeMode,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
    }
  }

  void _loadTargetOnlineStatus() async {
    final targetId = chatTargetId;
    if (targetId == null || isGroupChat) return;
    final status = await accountService.checkUserStatus(targetId);
    if (status != null) {
      _targetOnline = status.isOnline;
      emojiRainWidgetController.isOnLine = status.isOnline;
    }
  }

  ///提示气泡
  void _loadBubbleGuide() async {
    if (isGroupChat) return;

    /// 问题盒子
    /// 当天是否展示过
    var hasShowToday = _checkQuestionBoxGuide();
    if (!hasShowToday) {
      _timerBubbleGuide = Timer.periodic(Duration(seconds: 4), (timer) {
        _timerBubbleGuide?.cancel();
        if (hasPagePanDown) return;
        add(ShowQuestionBoxBubbleEvent());
        globalSp.setInt(spKeyHasActionInChat, DateTime.now().millisecondsSinceEpoch);

        ///打点: 问题盒子引导出现
        QboxStatistics.reportQboxGuide(gender: state.currentUser?.sexStr);
      });
    }
  }

  void _atUser(UserInfo info) {
    _atUserMap[info.uid] = info.displayedName;
    inputBarController.addAtUserList([info], removeFirst: false);
  }

  /// 安全模式气泡引导
  bool _isShowChatModeBubble(bool isSentFreeRequest) {
    final targetId = chatTargetId ?? "";

    if (targetId.isEmpty) return false;

    /// 安全模式
    bool showChatModeBubble = chatSafeModeService.showBubbleGuide(targetId);

    if (showChatModeBubble) {
      chatSafeModeService.updateBubbleGuide(targetId);
      add(ShowChatModeBubbleEvent(true, isSentFreeRequest));
      _timerBubbleGuide = Timer.periodic(Duration(seconds: 4), (timer) {
        _timerBubbleGuide?.cancel();
        _closeChatModeBubble();
      });
    }
    return showChatModeBubble;
  }

  /// 关闭聊天模式气泡引导
  void _closeChatModeBubble() {
    add(ShowChatModeBubbleEvent(false, state.isFreeRequest));
  }

  bool _checkQuestionBoxGuide() {
    final lastTime = globalSp.getInt(spKeyHasActionInChat);
    if (lastTime == null) return false;
    return DateTimeUtils.isSameDay(DateTime.now(), DateTime.fromMillisecondsSinceEpoch(lastTime));
  }

  void _onChatTapBack(_, __) {
    if (_back()) routerUtil.pop(context: getContext());
  }

  void _onChatTapBlank(_, __) {
    hideAllPanel();
  }

  void _onChatPagePanDown(ChatEventPagePanDown event, Emitter<ChatState> emit) {
    if (state.showSafeModeBubble) {
      add(ChatModeBubbleCloseEvent());
      return;
    }
    if (hasPagePanDown) return;
    hasPagePanDown = true;
    if (isBottleChat) return;
    if (!state.showQuestionBoxBubble) return;
    emit.call(state.clone(showQuestionBoxBubble: false));
  }

  Future<bool> onWillPop() {
    return Future.value(_back());
  }

  /// return true 退出
  bool _back() {
    // 因为WillPopScope会拦截返回手势，所以iOS在页面退出后处理
    if (!Platform.isAndroid) return true;

    // 不是来自匹配
    if (!isFromMatching) return true;

    // 已经聊过天了
    if (hasValidMsg) return true;

    // 已经提示过了
    if (_hasShownLeavingDialog) return true;
    _hasShownLeavingDialog = true;

    final context = getContext();
    if (context == null) return true;
    return !showLeavingConfirmDialog(
      context: context,
      targetOnline: _targetOnline,
      onLeave: () {
        if (isPageValid) routerUtil.pop();
      },
    );
  }

  void _handleMsgLongPress(ChatEventLongPressMsg event) async {
    if (isOfficial || state.hasUncoverIdentity || state.hasQuitGroup) return;
    final box = event.key.currentContext?.findRenderObject() as RenderBox?;
    final context = getContext();
    if (box != null && box.attached && context != null) {
      final token = AsyncToken();
      bool needResume = false;
      final bottom = softKeyboardHeight(context);
      if (bottom > 0 && !state.searchStickersPanelVisible) {
        /// 键盘弹出的情况下，界面底部展示与键盘相同高度的空间，保证
        Future.delayed(const Duration(milliseconds: 200)).then((value) {
          /// 延时结束后如果已经取消了就不处理
          if (!token.isCancelled) {
            needResume = true;
            add(ChangeViewInsetsBottomEvent(bottom));
          }
        });
      }
      ChatDetailReporter.reportLongPressMore(matchType, chatTargetId, event.msg.msgUid);
      await showDialog(
        (_) => MsgItemActionMenu(
          msg: event.msg,
          targetUser: state.targetUser,
          currentUser: state.currentUser,
          size: box.size,
          offset: box.localToGlobal(Offset.zero),
          onReplyTap: _reply,
          onCopyTap: _copy,
          onReportTap: _report,
          onDeleteTap: _delete,
          onAddStickerTap: _addStickerToFavourites,
          isBottleChat: isBottleChat,
          isGroupChat: isGroupChat,
        ),
        context: context,
      );
      token.cancel();
      if (needResume) {
        Future.delayed(const Duration(milliseconds: 200)).then((value) {
          if (state.viewInsetsBottom != 0) {
            add(ChangeViewInsetsBottomEvent(0));
          }
        });
      }
    } else {
      Log.e(_tag, 'msgId = ${event.msg.id}');
    }
  }

  void _reply(Msg msg) {
    add(ReferChatMsgEvent(msg));
    ChatDetailReporter.reportLongPressMoreAct(matchType, chatTargetId, msg.msgUid, "reply");
  }

  void _copy(Msg msg) {
    Clipboard.setData(ClipboardData(text: msg.content?.contentDesc ?? ''));
    toast(LocaleStrings.instance.copySuccess);
    ChatDetailReporter.reportLongPressMoreAct(matchType, chatTargetId, msg.msgUid, "copy");
  }

  void _report(Msg msg) {
    final context = getContext();
    if (context == null || msg.content == null) return;
    final template = MsgReport(
        type: isBottleChat ? "bottle_im" : null,
        targetId: msg.senderUid,
        msg: msg,
        msgContent: msg.content!,
        groupId: isGroupChat ? chatTargetId : null);
    routerUtil.push(R_SETTINGS_REPORT, params: {P_DATA: template, P_STATISTIC_FROM: R_CHAT});
    ChatDetailReporter.reportLongPressMoreAct(matchType, chatTargetId, msg.msgUid, "report");
  }

  void _delete(Msg msg) async {
    await imMsgService.deleteLocalMsg([msg]);
    ChatDetailReporter.reportLongPressMoreAct(matchType, chatTargetId, msg.msgUid, "delete");
  }

  void _pickAndSendImageVideo() async {
    ChatDetailReporter.reportListMediaClick(matchType, chatTargetId, state.isFreeMode,
        chatType: isGroupChat ? ChatType.group : ChatType.private);
    final targetId = chatTargetId;
    final context = getContext();
    if (targetId == null || context == null) return;
    final result = await MsgAssetPicker().pickMsgAssets(context,
        targetId: targetId,
        targetUser: state.targetUser,
        currentUser: state.currentUser,
        matchType: matchType ?? "other",
        authPage: ReportAuthPage.chatImage,
        isFreeMode: state.isFreeMode,
        chatType: isGroupChat ? ChatType.group : ChatType.private);
    if (result == null) return;
    showLoading();
    for (final asset in result.assets) {
      MsgContent content;
      if (asset.isImage) {
        final file = await AssetCompressor.compressImage(asset);
        if (file == null) continue;
        final assetSize = await commonService.getXFileSize(asset);
        content = ImageMsgContent.obtain(
          file.path,
          assetSize.width,
          assetSize.height,
        );
      } else {
        content = VideoMsgContent.obtain(
          localPath: asset.path,
          duration: await commonService.getVideoDuration(asset.path),
          // 部分机型拿到的宽高是反的，暂时不取宽高（会导致消息列表的视频item无法根据视频宽高显示）
          // width: asset.orientatedWidth,
          // height: asset.orientatedHeight,
        );
      }
      content.destructAfterView = result.destructAfterView;

      ///检查骚扰消息条数限制(安全模式下)
      if (!state.isFreeMode && await checkDisturbMsg(chatTargetId, state.targetUser)) {
        hideLoading();
        return;
      }
      sendMsg(content);
    }
    hideLoading();
  }

  void _onTapQaSwitch(ChatEventQASwitch event, Emitter<ChatState> emit) {
    punchGameController.updatePunchGameSwitch(!event.isOpen ? 1 : 2);
  }

  void _call() async {
    final context = getContext();
    ChatDetailReporter.reportMsgListCall(pageFrom, matchType, chatTargetId);
    if (context == null) return;
    if (state.canCall) {
      ///是否被封禁
      if ((await hasBeRestricted(context))) {
        return;
      }
      final targetId = chatTargetId;
      if (targetId == null) return;
      startCall(context, uid: targetId, callback: (callId) {
        ChatDetailReporter.reportMsgListCallSend(matchType, callId, chatTargetId);
      });
    } else {
      reportFollowGuideCallImp();
      showMakeCallDialog(
        context: context,
        currentAvatar: state.currentUser?.avatar,
        targetAvatar: state.targetUser?.avatar,
      );
    }
  }

  /// 上一次发送typing时间，必须间隔大于5秒
  int _lastSendTypingTime = 0;

  void _sendInputStatus(ChatEventInputting event) async {
    final targetId = chatTargetId;
    rxUtil.send(im.TextChangeEvent.textChanged, event.text);

    final current = DateTime.now().millisecondsSinceEpoch;

    /// 5秒内不发typing
    if (current - _lastSendTypingTime < 5000) {
      return;
    }

    if (isGroupChat) return;

    _lastSendTypingTime = current;
    if (targetId != null) {
      /// 对方在线
      if ((await accountService.checkUserStatus(targetId))?.isOnline == true) {
        imMsgService.sendInputtingStatus(
          targetId: targetId,
          inputType: InputType.text,
        );
      }
    }
  }

  @override
  void reportListAudioClick() {
    ChatDetailReporter.reportListAudioClick(matchType, chatTargetId, chatFromType, state.isFreeMode,
        chatType: isGroupChat ? ChatType.group : ChatType.private);
  }

  /// 录音完成，发送录音信息
  void _onChatRecordFinished(ChatEventRecordFinished event, Emitter<ChatState> emit) async {
    final second = event.seconds.toString();
    if (event.cancel) {
      ChatDetailReporter.reportListAudioCancel(matchType, chatTargetId, second, chatFromType, state.isFreeMode,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
      return;
    }
    if (event.path == null || event.seconds < 1) {
      toast(LocaleStrings.instance.msgTooShort);
      ChatDetailReporter.reportListAudioTooShort(chatFromType, matchType, chatTargetId, state.isFreeMode,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
    } else {
      final content = AudioMsgContent.obtain(event.path!, event.seconds);
      await sendMsg(content);
      ChatDetailReporter.reportListAudioSend(matchType, chatTargetId, second, chatFromType, state.isFreeMode,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
      // 超时上报
      if (event.seconds == maxRecordSeconds) {
        ChatDetailReporter.reportListAudioTooLong(chatFromType, matchType, chatTargetId, state.isFreeMode,
            chatType: isGroupChat ? ChatType.group : ChatType.private);
      }
    }
  }

  void _sendText() async {
    final text = inputBarController.text;
    final showText = inputBarController.showText;
    if (text.isNotEmpty) {
      if (chatTargetId == null) return;
      String targetId = isBottleChat ? getBottleChatTargetUid(chatTargetId!) : chatTargetId!;
      inputBarController.text = '';

      /// 非自由模式，需要本地检查敏感内容
      if (!state.isFreeMode) {
        final category = await imMsgService.checkSensitiveAlert(targetId: targetId, text: text);
        final alertContent = category?.localeContent;
        if (category != null && alertContent != null && getContext() != null) {
          // 触发警告
          showSensitiveAlertDialog(
            getContext()!,
            alertContent,
            targetId,
            category.type,
            () => inputBarController.text = '',
          );
          imMsgService.updateSensitiveAlertTime(category);
          return;
        }
      }

      ChatDetailReporter.reportTextSend(matchType, targetId, text, chatFromType, state.isFreeMode,
          chatType: isGroupChat ? ChatType.group : ChatType.private);
      final mentionedInfo = _createMentionedInfo(text, showText);
      MsgContent? msgContent;

      if (state.referringMsg != null) {
        msgContent = ReferenceMsgContent.obtain(text, state.referringMsg!, mentionedInfo: mentionedInfo);
      } else {
        msgContent = referOutContentFactory.getOutMsgContent(text, referringMsgContent: state.referringMsgContent);
      }
      if (msgContent == null) {
        msgContent = TextMsgContent.obtain(text, mentionedInfo: mentionedInfo);
      }

      Log.d(
          _tag,
          'mentionedInfo type: ${mentionedInfo?.type} '
          'uids: ${mentionedInfo?.userIdList}');
      Log.d(_tag, 'atUserMap ${_createAtUserMap(text)}');

      String? pushContent;
      if (mentionedInfo != null) {
        pushContent = mentionedInfo.mentionedContent;
      }

      final result = await sendMsg(msgContent, atUserMap: _createAtUserMap(text), pushContent: pushContent);

      if (result) {
        inputBarController.clearEditController();
        _atUserMap.clear();
        if (isGroupChat && mentionedInfo != null && mentionedInfo.userIdList?.isNotEmpty == true) {
          reportSendAtSucc(targetId, mentionedInfo.userIdList!.join(','));
        }
      } else {
        inputBarController.text = text;
      }
    } else {
      Log.i(_tag, 'inputtingText = $text');
    }
  }

  Future<bool> sendMsg(
    MsgContent content, {
    String? pushContent,
    bool? autoSend,
    Map<String, String>? atUserMap,
    bool addIntimacy = true,
    num? intimacyVal,
    num? intimacyVal1, // 新版亲密值
    bool isGift = false,
  }) async {
    ///是否被封禁
    var context = getContext();
    if (context != null && (await hasBeRestricted(context))) {
      return false;
    }

    ///检查骚扰消息条数限制
    if (!state.isFreeMode && await checkDisturbMsg(chatTargetId, state.targetUser)) {
      return false;
    }

    final targetId = chatTargetId;
    if (targetId?.isNotEmpty == true) {
      final isFirstMsg = !hasValidMsg;

      ///是否新会话,非群聊聊天时上报
      if (isFirstMsg && !isGroupChat) {
        /// 通知服务端，已开启新回话
        disturbService.startNewChat(targetId!, pageFrom ?? StatisticPageFrom.chatList);

        await socialService.reportChatSource(targetId: targetId, source: mapChatApiSource(source));
        await accountService.checkUserStatus(targetId, isForce: true);
      }

      num? increaseIntimacy;
      num? giftIncreaseIntimacy;
      if (addIntimacy) {
        giftIncreaseIntimacy = intimacyVal1;
        if (!chatPageController.intimacyReachMax) {
          increaseIntimacy = intimacyVal ?? chatPageController.sendMsgIncreaseIntimacyVal(isGift: isGift);
        }
      }

      double? starLight = chatPageController.lastStarLightCallback?.call();
      bool canIncludeExpansion = false;
      Map<String, String>? expansion;
      if (giftIncreaseIntimacy?.isValid() == true ||
          increaseIntimacy?.isValid() == true ||
          starLight?.isValid() == true) {
        canIncludeExpansion = true;
        expansion = {};
        if (increaseIntimacy?.isValid() == true) {
          expansion[expansionPerMsgIncreaseIntimacy] = "$increaseIntimacy";
        }
        if (giftIncreaseIntimacy?.isValid() == true) {
          expansion[expansionPerMsgIncreaseIntimacyKey] = "$giftIncreaseIntimacy";
        }
        if (starLight?.isValid() == true) {
          expansion[expansionReplyMsgIncreaseStarLight] = "${starLight?.noZeroDecimal()}";
        }
      }
      LogExt.largeD(_tag, () => 'sendMsg content=${content.runtimeType}, expansion=$expansion');

      final startTime = DateTime.now();
      final msg = await imMsgService.sendMsg(
          content: content,
          targetId: targetId!,
          pushContent: pushContent,
          chatType: isGroupChat ? ChatType.group : ChatType.private,
          autoSend: autoSend ?? false,
          atUserMap: atUserMap,
          canIncludeExpansion: canIncludeExpansion,
          expansion: expansion,
          pushTitle: state.currentUser?.nickname,
          imageUrlFCM: state.currentUser?.avatar);
      String key = "${msg?.id}-${msg?.targetId}";
      ChatSendStatistic? chatSendStatistic =
          getContext()?.findAncestorStateOfType<ApplicationState>()?.chatSendStatistic;
      chatSendStatistic?.statisticParams?[key] = {
        "matchType": matchType,
      };
      _handleMsgSent(msg);
      startTimingWithTime(key: key, time: startTime);
      if (autoSend == true) {
        ChatDetailReporter.reportAutoSendMsg(msg: msg, targetUser: state.targetUser, matchType: matchType);
      }
      if (!state.isFreeMode && !isBaseGroupChat) {
        ///非自由模式下，骚扰限制增加记录
        disturbService.sendMsg(targetId, state.targetUser);
      }

      /// 匹配场景开启会话时插入送礼提示
      if (isFirstMsg && (pageFrom?.isNotEmpty ?? false) && StatisticPageFrom.mathList.contains(pageFrom)) {
        // sendGiftGuideSysMsg(targetId: targetId);
      }

      return true;
    } else {
      Log.e(_tag, 'targetId = $targetId');
      return false;
    }
  }

  void _handleMsgSent(Msg? msg) {
    if (msg == null) return;
    /// 送礼消息不取消引用，如 首次聊天解锁送礼
    if (state.hasRefer() && msg.content is! ImGiftMessageContent) {
      add(ChatEventCancelRefer());
    }
    rxUtil.send(im.ChatEvent.msgListHandleNewMsg, msg);
  }

  RCIMIWMentionedInfo? _createMentionedInfo(String content, String showText) {
    if (_atUserMap.isEmpty) {
      return null;
    } else {
      final mentionedContent = '${state.currentUser?.displayedName}: $showText';
      var uids = <String>[];
      _atUserMap.forEach((key, value) {
        if (value.isNotEmpty) {
          if (content.contains('@{$key} ')) uids.add(key);
        }
      });
      if (uids.isEmpty) return null;
      if (content.contains(_atAllStr) && _atUserMap.containsKey('0')) {
        return RCIMIWMentionedInfo.create(
            type: RCIMIWMentionedType.all, userIdList: uids, mentionedContent: mentionedContent);
      } else {
        return RCIMIWMentionedInfo.create(
            type: RCIMIWMentionedType.part, userIdList: uids, mentionedContent: mentionedContent);
      }
    }
  }

  Map<String, String>? _createAtUserMap(String content) {
    if (_atUserMap.isEmpty) {
      return null;
    } else {
      _atUserMap.removeWhere((key, value) => !content.contains('@{$key} '));
      return _atUserMap;
    }
  }

  /// 发送表情
  void _sendStick(ChatEventSendSticker sticker, Emitter<ChatState> emit) {
    inputBarController.clearEditController();
    _atUserMap.clear();
    var msgContent = stickerService.getStickerMsgContent(sticker: sticker.sticker);
    sendMsg(msgContent);
    ChatDetailReporter.reportSendSticker(sticker.from, sticker.sticker.id, chatTargetId);
    if (sticker.isRecommend == true) {
      inputBarController.text = '';
    }
  }

  /// 揭露身份
  void _uncoverIdentity(ChatEventUncoverIdentity uncoverIdentity, Emitter<ChatState> emit) {
    reportBottleMsgUncoverSucc();
    emit.call(state.clone(hasUncoverIdentity: true));

    WidgetUtils.post((duration) {
      if (uncoverController == null) {
        uncoverController = SvgaController();
      }
      uncoverController?.startSvga(SvgaInfo(assetUrl: Res.svgaUncoverSuc, repeat: true));
    });
  }

  /// 删除漂流瓶
  void _bottleHadDelete(ChatEventBottleHadDelete deleteBottle, Emitter<ChatState> emit) {
    emit.call(state.clone(hasQuitGroup: true));
  }

  /// 改变聊天模式
  void _onChangeMode(ChangeModeEvent event, Emitter<ChatState> emit) {
    emit.call(state.clone(isFreeMode: !state.isFreeMode));
  }

  /// @人弹窗
  void _inputAt(InputAtEvent event, Emitter<ChatState> emit) {
    final context = getContext();
    if (context == null) return;
    if (isGroupChat && !isBottleChat && (state.groupInfo?.userList?.isNotEmpty == true)) {
      showAtUserDialog(
          context: context,
          targetId: chatTargetId ?? '',
          onSelected: (list) {
            for (var element in list) {
              _atUserMap[element.uid] = element.displayedName;
            }
            inputBarController.addAtUserList(list);
          },
          onSelectedAll: () {
            _atUserMap['0'] = 'All';
            inputBarController.addAtUserList([UserInfo(uid: '0', nickname: 'All')]);
          });
    }
  }

  /// 收藏表情
  void _addStickerToFavourites(Msg msg) async {
    showLoading();
    if (msg.content is PbMsgContent) {
      var content = msg.content as PbMsgContent;
      if (content.im?.type == PbCustomImType.PbCustomImType_STICKER) {
        PbImSticker? detail = content.getData<PbImSticker>();
        if (detail != null) {
          Sticker sticker = Sticker.fromPb(detail);
          ChatDetailReporter.reportAddSticker(sticker.id);
          var result =
              await stickerService.oprFavorite(action: StickerAction.add, stickerId: sticker.id, sticker: sticker);
          if (result?.isSuccess ?? false) {
            toast(LocaleStrings.instance.addedSuccess);
          } else if (result != null && result.code == 1018) {
            toast(LocaleStrings.instance.stickerLimit);
          } else {
            toast((result?.msg?.isNotEmpty ?? false) ? (result?.msg ?? "") : LocaleStrings.instance.checkNetWork);
          }
        }
      }
    }
    hideLoading();
  }

  /// 点击礼物面板
  Future<void> _showGiftPanel() async {
    if (chatTargetId?.isEmpty ?? true) return;
    // showChatGiftDialog(
    //     targetId: isPrivateChat ? chatTargetId! : "",
    //     groupId: isPrivateChat ? null : chatTargetId,
    //     from: pageFrom,
    //     isUnlock: false,
    //     isNewChat: !hasValidMsg);
    reportMsgListGift(chatTargetId, pageFrom);
  }

  void _changeMode(ChatSafeMode chatSafeMode) {
    if (chatSafeMode.targetId == chatTargetId && state.isFreeMode != chatSafeMode.isFree) {
      add(ChangeModeEvent());
    }
  }

  @override
  void dispose() {
    _stopPlaying();
    _sendQuitEvent();
    ChatDetailReporter.reportMsgListExit(pageFrom, matchType, chatTargetId, hasValidMsg, state.isFreeMode,
        chatType: isGroupChat ? ChatType.group : ChatType.private);
    _timerBubbleGuide?.cancel();

    _releaseModuleController();
    super.dispose();

    ///触发评分卡场景：退出聊天会话时出现
    evaluateHandle.check(
        checkPostGuide: !isBottleChat,
        checkShareGuide: true,
        targetId: chatTargetId,
        from: StatisticPageFrom.chat);
  }

  void _stopPlaying() async {
    final player = GlobalAudioPlayer.instance();
    if (player.isPlay) {
      await player.stop();
      await player.changePlayingRoute(PlayingRoute.SPEAKERS);
    }
  }

  ChatQuitWreck? _sendQuitEvent() {
    final args = getArguments();
    if (args == null) return null;
    ChatQuitWreck chatQuitWreck = ChatQuitWreck(
      args: args,
      hasSentMsg: hasValidMsg,
      targetOnline: _targetOnline,
    );
    rxUtil.send(
      ChatPageEvent.quit,
      chatQuitWreck,
    );
    return chatQuitWreck;
  }

  @override
  void endTimingAndReport() async {
    stopTiming(key: pageDurationKey);
    ChatDetailReporter.reportMsgListDuration(pageFrom, getOnTime(key: pageDurationKey), matchType, chatTargetId,
        chatType: isGroupChat ? ChatType.group : ChatType.private);
    removeTimer(key: pageDurationKey);
  }

  void _searchPanelChange(bool open) {
    if (isPageShow) {
      Future.delayed(Duration(milliseconds: open ? 0 : 500), () {
        add(SearchStickersPanelChangeEvent(open));
      });
    }
  }

  void _groupBan(int value) {
    familySettingModel?.speakSetting = value;
    add(ChatGroupBanEvent());
  }

  /// 发起问题盒子成功
  void _beginQuestionBox(BeginQuestion beginQuestion) {
    routerUtil.push(R_QUESTION_BOX_ANSWER,
        params: {P_DATA: beginQuestion.question, P_ID: beginQuestion.msgUid, P_TARGET_ID: beginQuestion.targetId});
  }

  void _onClickChangeMode() {
    if (chatTargetId == null) return;
    switchChatSafeMode(targetId: chatTargetId!, isFreeMode: state.isFreeMode, from: "button");
  }

  /// 非匹配场景下进入聊天需送礼
  void _checkAndInitGiftUnlockChat() {
    /// 非匹配场景下点击聊天需送礼
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (chatTargetId?.isEmpty ?? true) return;
      if (state.targetUser?.isOfficial ?? false) return;

      if (preChatCheck == true) {
        _canChat = await _checkPreChat();
      } else {
        _canChat = true;
      }
      if (_canChat == true) {
        _beginChat();
      } else {
        if (isPageValid) routerUtil.pop();
      }

      // final chat = await imChatService.getChat(chatTargetId!);
      //
      // bool isNewChat = !(chat?.hasRealMsg ?? false);

      // if (needSendGiftUnlock) {
      //   showChatGiftDialog(
      //       targetId: isPrivateChat ? chatTargetId! : "",
      //       groupId: isPrivateChat ? null : chatTargetId,
      //       from: pageFrom,
      //       isUnlock: true,
      //       isNewChat: isNewChat,
      //       onlyShopHighGift: true);
      //   return;
      // }

      // Quiz每日推荐的用户
      // 匹配场景
      // var list = [
      //   StatisticPageFrom.constellation,
      //   StatisticPageFrom.chatList,
      //   StatisticPageFrom.push,
      // ]..addAll(StatisticPageFrom.mathList);
      //
      // if ((pageFrom?.isNotEmpty ?? false) && list.contains(pageFrom)) return;
      //
      // /// 互关状态
      // final relation =
      //     await userService.getRelation(targetUid: chatTargetId ?? "");
      // if (relation.isFriend) return;
      //
      // /// 有聊过天
      // if (!isNewChat) return;

      // rxUtil.send(im.SendGiftUnlockChatEvent.show, chatTargetId);
      //
      // showChatGiftDialog(
      //     targetId: isPrivateChat ? chatTargetId! : "",
      //     groupId: isPrivateChat ? null : chatTargetId,
      //     from: pageFrom,
      //     isUnlock: true,
      //     isNewChat: isNewChat);
    });
  }

  Future<bool> _checkPreChat() async {
    bool canChat = false;
    showLoading();
    FlatHttpResponse<PreSayHiData>? resp;
    try {
      resp = await socialService.preSayHi(fuid: chatTargetId!, source: mapChatApiSource(source));
    } on Exception catch (e) {
      print(e);
    }
    hideLoading();
    if (resp?.isSuccess == true) {
      if (resp!.data?.canDirectChat == true) {
        canChat = true;
      } else {
        var resultMap =
            await showBottomChatPreCheckDialog(userInfo: resp.data!.userInfo, sayHiData: resp.data!, source: source);
        if (resultMap != null) {
          var chatPreResult = ChatPreDialogResult.fromMap(resultMap);
          if (chatPreResult.canChat == true) {
            canChat = true;
          }
        }

        if (canChat != true) {
          ChatBlockStatistics.reportChatBlockPopClose(
            isWink: resp.data!.winkInfo?.hasWink == true ? '1' : '0',
            autherId: resp.data!.userInfo.uid,
            autherGender: resp.data!.userInfo.sexStr,
          );
        }
      }
    } else {
      toast(resp?.msg ?? LocaleStrings.instance.defaultError);
    }
    return canChat;
  }

  void onHasValidMsgChange(bool _hasValidMsg) {
    if (hasValidMsg != _hasValidMsg) {
      hasValidMsg = _hasValidMsg;
      add(HasValidMsgChangeEvent());
    }
  }

  /// 成功揭露身份事件处理
  void _handleUncoverIdentity(String groupId) {
    ///  非当前会话 || 非漂流瓶会话
    if (chatTargetId != groupId || !isBottleChat) return;
    switchBottomPanel(false);

    add(ChatEventUncoverIdentity());
  }

  /// 获取揭露身份状态
  _loadBottle() async {
    var groupId = chatTargetId ?? "";
    if (groupId.isEmpty) return;
    bool hasUncoverIdentity = bottleService.getUncoverIdentityStatus(groupId);
    if (hasUncoverIdentity) {
      add(ChatEventUncoverIdentity());
      return;
    }

    var hasQuit = await bottleService.hadQuitBottleChat(groupId: groupId);
    if (hasQuit) {
      add(ChatEventBottleHadDelete());
    }
  }

  /// 揭露身份申请
  _handleUncoverIdentityApply(String groupId) async {
    ///  非当前会话 || 非漂流瓶会话
    if (chatTargetId != groupId || !isBottleChat) return;

    bool hasApplyToday = await bottleService.hasApplyUncoverToday(groupId);

    if (hasApplyToday) {
      toast(LocaleStrings.instance.uncoverLimit);
      return;
    }

    // showBottleUncoverConfirmDialog(callback: () async {
    //   bool result = await bottleService.sendUncoverIdentity(groupId);
    //   if (!result) toast(LocaleStrings.instance.uncoverLimit);
    // });
  }

  /// 解散群组
  _destroyGroup(String groupId) async {
    ///  非当前会话 || 非漂流瓶会话
    if (chatTargetId != groupId || !isBottleChat) return;

    add(ChatEventBottleHadDelete());
  }

  /// 被匹弹框，点击去聊天，自动发送一条打招呼信息
  void _randomSendMsg(String uid) async {
    if (!needAutoSend) return;
    bool needSend = await _getNeedSend();
    if (needSend) {
      /// 百分之50概率发破冰消息或者动态表情
      if (Random().nextDouble() > 0.5) {
        /// 破冰问题列表
        List<IcebreakerQuestion> allQuestion = await socialService.getIcebreakerQuestions(uid) ?? [];
        if (allQuestion.isEmpty) return;

        int index = Random().nextInt(allQuestion.length);
        final question = allQuestion[index];
        final context = getContext();
        if (context == null) return;

        String questionTitle = question.name ?? "";
        if (questionTitle.isEmpty) return;
        sendMsg(TextMsgContent.obtain(questionTitle), autoSend: true);
      } else {
        /// 动态表情
        List<Sticker>? list = await stickerService.getStickers(type: StickerMenuType.category, content: "11");

        if (list?.isEmpty ?? true) return;
        int index = Random().nextInt(list!.length);
        var msgContent = stickerService.getStickerMsgContent(sticker: list[index]);
        sendMsg(msgContent, autoSend: true);
      }
    }
  }

  /// 获取云控配置,是否自动发送打招呼消息
  Future<bool> _getNeedSend() async {
    bool needSend = true;
    final userInfo = await userService.getCurrentUserInfo();
    //获取云控配置
    final section = await getService<AbsRemoteConfigService>()?.getSection('app_ui');
    var key;
    if (getArgument(P_RECOMMEND_TYPE) == recommendTypeAnchor) {
      key = 'match_chat_random_send_msg_anchor';
    } else {
      key = 'match_chat_random_send_msg';
    }
    var value = section?.getValue(key)?.getAll();
    if (value != null) {
      MatchChatSendMsgModel model = MatchChatSendMsgModel.fromJson(value);
      needSend = model.needSend(userInfo?.sex);
    }
    return needSend;
  }

  void _intBeforeIsNewChat() {
    if (chatTargetId?.isEmpty ?? true) return;
    beforeIsNewChat = disturbService.isNewChat(chatTargetId ?? "");
  }

  /// 未回复
  void _insertUnRespTip() async {
    if ((chatTargetId?.isEmpty ?? true) || isGroupChat || !oppositeSex) return;

    bool needInsert = await disturbService.needInsertUnRespTip(chatTargetId!);
    if (needInsert) {
      disturbService.setHasRespOrHasTipUnResp(chatTargetId!, insertMsg: true);
    }
  }

  // FutureOr<void> _onClickExpandFuncBar(
  //     ChatEventExpandFuncBar event, Emitter<ChatState> emit) {
  //   switchBottomPanel(!state.chatBarFuncExpanded,
  //       type: BottomDrawerType.expandType);
  // }

  FutureOr<void> _onClickSendGift(ChatEventSendGift event, Emitter<ChatState> emit) {
    add(ShowGiftGuideEvent(false));

    // showChatGiftDialog(
    //     targetId: isPrivateChat ? chatTargetId! : "",
    //     groupId: isPrivateChat ? null : chatTargetId,
    //     from: pageFrom,
    //     isUnlock: false,
    //     isNewChat: !hasValidMsg);

    showGiftPanelDialog(
      source: isFamilyChat ? GiftPanelSource.familyChat : GiftPanelSource.chat,
      targetUid: state.targetUser?.uid,
      isUnlockChat: state.unlockGift != null,
      groupId: chatTargetId,
    );
  }

  HashMap<String, int> comboCountHash = HashMap();

  void _giftSendSuccess(SendGiftResp value) {
    // 非combo礼物 直接发送
    if (value.isComboGift != true) {
      var buildChatGiftMsg = ChatHandler.buildChatGiftMsg(giftMessage: value);
      if (isFamilyChat == false) {
        sendMsg(
            buildChatGiftMsg, intimacyVal: value.chatIntimacyVal, intimacyVal1: value.userIntimacyVal, isGift: true);
        rxUtil.send(ChatGiftEvent.giftDisplay,
            GiftDisplayMsg(imGiftMsg: ImGiftMessageContent.fromPb(PbImGiftMsg.fromBuffer(buildChatGiftMsg.im!.data))));
      }
    } else {
      String receivedComboId = value.comboId ?? "";
      if (comboCountHash.containsKey(receivedComboId)) {
        comboCountHash[receivedComboId] = comboCountHash[receivedComboId]! + 1;
      } else {
        comboCountHash[receivedComboId] = 1;
      }
    }
  }

  void _endCombo(SendGiftResp value) {
    print("ChatBloc._endCombo: ");
    int giftCount = comboCountHash[value.comboId ?? ""] ?? 1;
    var buildChatGiftMsg = ChatHandler.buildChatGiftMsg(giftMessage: value, giftCount: giftCount);
    sendMsg(buildChatGiftMsg, intimacyVal: giftCount * (value.chatIntimacyVal ?? 0));
    // rxUtil.send(ChatGiftEvent.giftDisplay, GiftDisplayMsg(imGiftMsg: content));
    rxUtil.send(ChatGiftEvent.giftDisplay,
        GiftDisplayMsg(imGiftMsg: ImGiftMessageContent.fromPb(PbImGiftMsg.fromBuffer(buildChatGiftMsg.im!.data))));
  }

  void _onChatCloseGreetMsg(ChatEventCloseGreetMsg event, Emitter<ChatState> emit) {
    emit.call(state.clone(showGreeting: false));
  }

  void _onChatTapGreetMsg(ChatEventTapGreetMsg event, Emitter<ChatState> emit) {
    String msg = event.msg;
    if (chatTargetId == null || chatTargetId?.isEmpty == true) return;
    sendMsg(TextMsgContent.obtain(msg));

    final from = _isBell ? 'Fate bell' : 'Message';
    final content = msg;
    ChatStatistics.reportSendMsgQuicklyInitiate(toUid: chatTargetId, from: from, content: content);

    imMsgService.addGreetMsgReportItem(targetId: chatTargetId!, greetId: event.id, from: from);
  }

  void _showGiftGuide(ShowGiftGuideEvent event, Emitter<ChatState> emit) {
    emit.call(state.clone(showGiftGuideAnimation: event.show));
  }

  void _showUnlockGiftGuide(ShowUnlockGiftGuideEvent event, Emitter<ChatState> emit) {
    if (event.gift == null) {
      emit.call(state.remove(isUnlockGift: true));
    } else {
      emit.call(state.clone(unlockGift: event.gift));
    }
  }

  void _showUnlockGiftView(ShowUnlockGiftWidgetEvent event, Emitter<ChatState> emit) {
    emit.call(state.clone(showUnlockGift: event.show));
  }

  /// 赠送解锁聊天礼物
  void _sendUnlockGift(SendUnlockGiftEvent event, Emitter<ChatState> emit) async {
    final model = state.unlockGift;
    if (model == null) return;

    final rsp = await imChatService.sendChatGift(
      giftId: model.id ?? '',
      fUid: chatTargetId ?? '',
      isUnlockChat: true,
    );
    if (rsp.isSuccess) {
      final gift = rsp.data;
      if (gift != null) _giftSendSuccess(gift..giftInfo = model);
    } else if (rsp.code == 1019) {
      showRechargeDialog(index: model.currencyType == CurrencyType.goldCoin ? 1 : 0);
    } else {
      toast(rsp.msg ?? LocaleStrings.instance.defaultError);
    }
  }

  /// 发送一条本地消息
  void sendLocalMSg(String title) {
    imMsgService.insertOutgoingMessage(
      content: LocalMsgContent(text: title),
      targetId: chatTargetId ?? '',
      sendTime: DateTime.now().millisecondsSinceEpoch,
    );
  }
}
