import 'dart:async';

import 'package:biz/biz/chat/chat_list/bloc/user_info_mixin.dart';
import 'package:biz/biz/chat/chat_list/widgets/fate_bell/fate_bell_list_controller.dart';
import 'package:biz/biz/main/event.dart';
import 'package:biz/biz/notify/notify_permission_guide_dialog.dart';
import 'package:biz/biz/notify/notify_permission_manager.dart';
import 'package:biz/biz/report/report_success_dialog.dart';
import 'package:biz/global/bloc/main_tab_bloc_mixin.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/statistic/bloc/tab_duration_report.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:meta/meta.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:service/common/statistics/bottle_statistics.g.dart';
import 'package:service/common/statistics/chat_list_statistics.g.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/chat_list_statistic_handler.dart';
import 'package:service/common/statistics/message_statistics.g.dart';
import 'package:service/common/statistics/relationship_statistics.g.dart';
import 'package:service/common/statistics/room_statistics.g.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/modules/account/const/events.dart';
import 'package:service/modules/account/model/online_status.dart';
import 'package:service/modules/bottle/bottle_utils.dart';
import 'package:service/modules/family/const/events.dart';
import 'package:service/modules/im/const/const.dart';
import 'package:service/modules/im/const/enums.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/chat.dart';
import 'package:service/modules/im/model/msg.dart';
import 'package:service/modules/im/model/pb_msg_content.dart';
import 'package:service/modules/im/rong_cloud/convert/const_converter.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/social/model/contact_group.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/task/const/events.dart';
import 'package:service/modules/user/model/high_value_user.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_common.pbenum.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/loading.dart';

part 'chat_list_event.dart';

part 'chat_list_state.dart';

class ChatListBloc extends PageVisibilityBloc<ChatListEvent, ChatListState>
    with MainTabBlocMixin, UserInfoMixin, PageTimingMixin, TabDurationReport {
  static const String _tag = 'ChatListBloc';

  ItemScrollController? _scrollController;

  ItemScrollController get scrollController => _scrollController ??= ItemScrollController();

  FateBellListController fateBellController = FateBellListController();

  double startPosition = 0;
  int downTime = 0;
  int upTime = 0;
  List<ChatType> chatTypes = [];
  UserInfo? currentUserInfo;

  final List<String> _friendUids = [];
  bool _needFreshFriendUids = true;

  /// 是否有列表操作交互
  final bool hasOperation;

  /// 仅展示私聊列表
  final bool onlyPrivate;

  /// 漂流瓶消息列表
  final bool isBottleChat;

  /// 客户列表
  final bool isCustomerList;

  /// 进入后台会话列表不刷新
  bool needUpdateList = false;

  int bottleUnreadCount = 0;

  bool _initStatus = true;

  /// 如果是客户经理角色 插入一条自定义聊天
  final _amChat = Chat.amChat();

  List<HighValueUser> _highValueUsers = [];

  /// 是否在房间打开的聊天列表
  final bool inRoom;

  bool isFirstLoad = true;

  ChatListBloc({
    this.onlyPrivate = false,
    this.hasOperation = true,
    this.isBottleChat = false,
    this.isCustomerList = false,
    this.inRoom = false,
  }) : super(ChatListState()) {
    on<ChatListEventReload>(_onReloadEvent);
    on<ChatListEventDebounceReload>(_onRealReloadEvent);
    on<ChangeFriendTypeEvent>(_changeFriendType);
    on<BottleUnreadCountChangeEvent>((BottleUnreadCountChangeEvent event, Emitter<ChatListState> emitter) {
      bottleUnreadCount = event.count;
      if (_initStatus) {
        _initStatus = false;
        emitter.call(state.clone(bottleMsgCount: bottleUnreadCount));
      }
    });
  }

  @override
  void initBloc() {
    super.initBloc();
    _reportChatListImp();
    _initRxEvent();
    if (isBottleChat) {
      chatTypes = [ChatType.group];
    } else if (onlyPrivate) {
      chatTypes = [ChatType.private];
    } else {
      chatTypes = [ChatType.group, ChatType.private];
    }
    add(ChatListEventReload());
    _reportLiveRoom();
    chatListService.loadSex();

    final from = inRoom ? 'room' : 'chat';
    MessageStatistics.reportMessageImp(from: from);
  }

  Timer? _reloadDebounceTimer;

  bool isSystemAccount(String uid) {
    return uid.length == assistanceLength && uid.substring(0, 2) == "10";
  }

  void _onReloadEvent(ChatListEventReload event, Emitter<ChatListState> emitter) async {
    if (isCustomerList) {
      _fetchCustomers();
    }

    _reloadDebounceTimer?.cancel();
    _reloadDebounceTimer = Timer(Duration(milliseconds: 250), () => add(ChatListEventDebounceReload()));

    final user = await userService.getCurrentUserInfo();
    final isAccountManager = user?.isAccountManager == 1;
    emitter.call(state.clone(isAccountManager: isAccountManager, myAmId: user?.managerUid));
  }

  /// 真正的刷新
  void _onRealReloadEvent(ChatListEventDebounceReload event, Emitter<ChatListState> emitter) async {
    final refreshState = await _refreshChatList();
    emitter.call(refreshState);
  }

  @override
  void onPause() {
    super.onPause();
  }

  @override
  void onResume() {
    super.onResume();
    fateBellController.refreshFateBellList();
    _notifyChatListChanged();
  }

  void _initRxEvent() {
    listenRxEvent<List<ContactGroup>>(ContactEvent.changed, (value) {
      if (state.selectType == FriendType.friend) {
        add(ChatListEventReload());
      }
    });

    if (isBottleChat) {
      listenRxStream<List<Msg>>(
          rxUtil.observerStream<Msg>(MsgEvent.newMsgReceived).bufferTime(Duration(milliseconds: 1000)), (value) {
        if (value.isEmpty) return;
        if (!isPageResume) {
          needUpdateList = true;
          return;
        }
        add(ChatListEventReload());
      });
      listenRxEvent(ChatEvent.bottleConversationUpdate, (_) async {
        if (!isPageResume) {
          needUpdateList = true;
          return;
        }
        add(ChatListEventReload());
      });
    } else {
      listenRxEvent<List<Chat>>(ChatEvent.conversationUpdate, (value) async {
        if (!isPageResume) {
          needUpdateList = true;
          return;
        }
        add(ChatListEventReload());
      });
      listenRxEvent(AccountEvent.completeInfo, (value) {
        chatListService.loadSex();
      });
      listenRxEvent<int>(TaskEvent.starlightHasRewards, (value) {
        _refreshMaleTaskDiamond();
      });
      listenRxEvent<int>(TaskEvent.complete, (value) {
        _refreshMaleTaskDiamond();
      });
      listenRxEvent<int>(AccountEvent.amUpdate, (value) {
        add(ChatListEventReload());
      });
      listenRxEvent<String>(FamilyEvent.quitFamily, (value) async {
        final targetId = value.contains(imChatService.familyChatPrefix) ? value : "${imChatService.familyChatPrefix}_$value";
        await imChatService.deleteChat(targetId, chatType: ChatType.group);
        await imChatService.clearHistory(targetId, ChatType.group);
        add(ChatListEventReload());
      });

      listenRxEvent<MainPageName>(MainTabEvent.tabSwitched, (value) {
        if (value == MainPageName.msg) {
          _refreshChatList(needCheckGroup: true);
        }
      });
    }
  }

  Future<ChatListState> _refreshChatList({bool needCheckGroup = false}) async {
    print("_refreshChatList");
    _needFreshFriendUids = true;

    List<Chat>? chats;

    if (isBottleChat) {
      /// 漂流瓶根据tag来查会话列表
      chats = await imChatService.getConversationsFromTagByPage(imChatService.bottleTagId,
          count: state.chats?.length ?? 20);
    } else {
      try {
        if (state.selectType == FriendType.all) {
          _amChat.unreadMsgCount = 0;
        }

        chats = await _formatChatList(await imCacheService.getChatList());

        if (state.isAccountManager && !isCustomerList && state.selectType == FriendType.all) {
          if (chats.firstOrNull != _amChat) {
            chats.insert(0, _amChat);
          }
        }
      } on Exception catch (e) {
        Log.i(_tag, "catch Exception $e");
        buglyLog(msg: '${e.toString()}');
        chats = [];
      }
    }

    final user = await userService.getCurrentUserInfo();

    if (needCheckGroup || isFirstLoad) {
      isFirstLoad = false;
      final deleteList = [];
      for (final chat in chats) {
        if (chat.chatType == ChatType.group) {
          final groupInfo = await imCacheService.getGroupInfo(targetId: chat.targetId, fromServer: true);
          if (user != null && groupInfo?.userList?.contains(user!.uid) != true) {
            deleteList.add(chat);
            await imChatService.deleteChat(chat.targetId, chatType: ChatType.group);
            await imChatService.clearHistory(chat.targetId, ChatType.group);
          }
        }
      }
      for (var item in deleteList) {
        chats.remove(item);
      }
    }

    if (chats.isEmpty) {
      final userCount = await userService.getUserCount();
      return state.clone(chats: chats, matchCount: userCount.quickMatchingCount, bottleMsgCount: bottleUnreadCount);
    } else {
      return state.clone(chats: chats, bottleMsgCount: bottleUnreadCount);
    }
  }

  Future<List<Chat>> _formatChatList(List<Chat> chats) async {
    List<Chat> result = List.from(chats);

    if (onlyPrivate) {
      result.retainWhere((e) =>
          e.hasRealMsg &&
          e.targetId.length != assistanceLength &&
          e.targetId.length != superAssistanceLength &&
          e.chatType == ChatType.private);
    }
    if (state.selectType == FriendType.all) {
      final managerUid = (await userService.getCurrentUserInfo())?.managerUid;
      if (state.isAccountManager) {
        List<Chat> amResult = [];
        _amChat.msgContent = null;
        for (final chat in result) {
          final isCustomers = chat.biz?.isCustomer ?? false;

          /// 当前页面是只筛选出客户的列表
          if (isCustomerList) {
            if (isCustomers) {
              amResult.add(chat);
            }
          } else {
            /// 整合所有客户未读数 到 _amChat
            if (isCustomers) {
              _amChat.unreadMsgCount = _amChat.unreadMsgCount + chat.unreadMsgCount;
            } else {
              amResult.add(chat);
            }
          }
        }
        final amChat = result.firstWhereOrNull((e) => e.biz?.isCustomer == true);
        if (amChat != null) {
          _amChat.cloneAm(amChat);
        }
        if (isCustomerList) {
          amResult = _handleCustomersList(amResult);
        } else {
          final hasAm = amResult.firstWhereOrNull((e) => e.targetId == Chat.amGroupId);
          /// 当前会话列表没有客户列表 则插入客户集合会话
          if (hasAm == null) {
            amResult.insert(0, _amChat);
          }
        }
        result = amResult;
      }

      /// 当前会话列表没有 am 则插入会话
      if (managerUid != null && managerUid.isNotEmpty) {
        if (result.firstWhereOrNull((e) => e.targetId == managerUid) == null) {
          result.add(Chat(chatType: ChatType.private, targetId: managerUid, unreadMsgCount: 0, isPinned: false));
        }
      }

      /// 排序
      _sortChatList(result, managerUid: managerUid);
      return result;
    } else {
      if (state.isAccountManager && !isCustomerList) {
        chats.remove(_amChat);
      }

      /// 如果选择好友列表，需要过滤好友uids
      await _refreshFriendUids();
      result.retainWhere((e) {
        return _friendUids.contains(e.targetId);
      });
      return result;
    }
  }

  Future<void> _refreshFriendUids() async {
    if (_needFreshFriendUids) {
      _friendUids.clear();
      final contact = await socialService.getContactGroups() ?? [];
      contact.retainWhere((e) => e.name == groupFriends);
      for (var friend in contact) {
        _friendUids.addAll(friend.uids);
      }
      _needFreshFriendUids = false;
    }
  }

  void _notifyChatListChanged() {
    if (!needUpdateList) return;
    add(ChatListEventReload());
  }

  Future<void> onLoadMore() async {
    Log.d(_tag, 'state.chats count -> ${state.chats?.length}');
    if (state.chats?.isNotEmpty == true) {
      List<Chat>? chats;
      if (isBottleChat) {
        chats = state.chats ?? [];

        /// 漂流瓶根据tag来查会话列表
        final newChats = await imChatService.getConversationsFromTagByPage(imChatService.bottleTagId,
            startTime: state.chats?.last.sentTime ?? 0);
        chats.addAll(newChats);
      } else {
        chats = await _formatChatList(await imCacheService.onLoadMore());
        if (chats.isEmpty) chats = [];
      }
      emit(state.clone(chats: chats));
    }
  }

  @override
  Stream<ChatListState> mapEventToState(ChatListEvent event) async* {
    switch (event.runtimeType) {
      case ChatListEventSearch:
        routerUtil.push(R_CHAT_SEARCH);
        ChatListStatistics.reportChatListSearchClick();
        break;
      case ChatListEventTapItem:
        _handleTapItem((event as ChatListEventTapItem).chat);
        break;
      case ChatListEventDelete:
        _deleteChat((event as ChatListEventDelete).chat);
        break;
      case ChatListEventMore:
        _showMoreDialog(event as ChatListEventMore);
        break;
      case ChatListEventMatch:
        // routerUtil.push(R_MATCH);
        rxUtil.send(AppMainEvent.changeTab, MainPageName.home.name);
        break;
      case ChatListEventScrollStart:
        startPosition = (event as ChatListEventScrollStart).position;
        break;
      case ChatListEventScrollEnd:
        (event as ChatListEventScrollEnd).isUp ? downTime += 1 : upTime += 1;
        break;
      case WishBottleTapEvent:
        _gotoWishBottle();

        break;
    }
  }

  void _handleTapItem(Chat chat) async {
    bool isDeepLinkChat = imChatService.isDeepLinkChat(chat);
    if (isDeepLinkChat) {
      if (!(chat.msgContent is PbMsgContent)) return;
      final msg = chat.msgContent as PbMsgContent;
      if (msg.im?.type == PbCustomImType.PbCustomImType_DEEPLINK_MSG) {
        final pbMsg = msg.getData<PbImChatDeepLinkMsg>();
        DeepLink.jump(pbMsg?.deeplink ?? "");
        imChatService.clearUnreadStatus(chat.targetId, chat.sentTime ?? 0, chat.chatType);
      }
    } else {
      if (chat.isAmGroup) {
        routerUtil.push(R_CHAT_LIST, params: {
          P_PRIVATE: true,
          P_HAS_OPR: false,
          P_HIDE_TOP: true,
          P_CHAT_IS_CUSTOMER_LIST: true,
        });
        return;
      }
      routerUtil.push(
        R_CHAT,
        params: {
          P_TARGET_ID: chat.targetId,
          P_IS_BOTTLE_CHAT: isBottleChat,
          P_CHECK_PRE_CHAT: false,
          P_CONVERSATION_TYPE: getRCConversationType(chat.chatType),
          P_STATISTIC_FROM: isBottleChat ? StatisticPageFrom.bottle : StatisticPageFrom.chatList,
          P_STATISTIC_MATCH_TYPE: StatisticPageFrom.chatList,
          P_IS_ROOM: inRoom
        },
        context: getContext(),
      );

      final targetUser = await userService.getUserInfo(imChatService.getBottleTargetUserId(chat));

      OnlineStatus? onlineStatus = await accountService.checkUserStatus(targetUser?.uid ?? '');
      final isOnline = onlineStatus?.isOnline.toReportString() ?? '';

      ChatListStatistics.reportChatListItemClick(
          isNotified: chat.unreadMsgCount == 0 ? ReportTabNotifyStatus.none : ReportTabNotifyStatus.notify,
          isOnline: isOnline,
          toUid: chat.targetId,
          toGender: targetUser?.sexStr);
    }
  }

  void _pin(Chat chat) {
    imChatService.pinChat(chat.targetId, !chat.isPinned, chat.chatType);
    if (chat.isPinned) {
      ChatListReporter.reportChatListItemUnPin(chat.targetId);
    } else {
      ChatListReporter.reportChatListItemPin(chat.targetId);
    }
  }

  void _showMoreDialog(ChatListEventMore event) {
    final context = getContext();
    if (context == null) return;
    final chat = event.chat;
    final s = LocaleStrings.instance;
    showBottomSheetDialog(
      context: context,
      actions: [
        if (!isBottleChat)
          SheetAction(
            title: chat.isPinned ? s.unpin : s.pin,
            onAction: () => _pin(chat),
          ),
        SheetAction(
          title: chat.isMuted ? s.unmute : s.mute,
          onAction: () => _mute(chat),
        ),
        // if (!isBottleChat)
        //   SheetAction(
        //     title: s.delete,
        //     onAction: () => _deleteChat(chat),
        //   ),
        if (isBottleChat)
          SheetAction(
            title: s.report,
            onAction: () => _report(chat),
          ),
      ],
    );
  }

  void _mute(Chat chat) async {
    final mute = !chat.isMuted;
    final result = await imChatService.setChatNotificationStatus(chat.targetId, mute, chat.chatType);
    if (!result) return;

    /// 此处打点 漂流瓶类型跳过
    if (isBottleChat) return;

    final user = await userService.getUserInfo(chat.targetId);
    if (mute) {
      RelationshipStatistics.reportMuteUserSucc(
        toUid: chat.targetId,
        toGender: user?.sexStr,
        page: R_CHAT_LIST,
        isFollowed: user?.followed.toString(),
        isFollowing: user?.following.toString(),
      );
    } else {
      RelationshipStatistics.reportUnmuteUserSucc(
        toUid: chat.targetId,
        toGender: user?.sexStr,
        page: R_CHAT_LIST,
        isFollowed: user?.followed.toString(),
        isFollowing: user?.following.toString(),
      );
    }
  }

  Future<void> _deleteChat(Chat chat) async {
    final s = LocaleStrings.instance;
    final context = getContext();
    if (context == null) return;
    if (isBottleChat) {
      var hasQuit = await bottleService.hadQuitBottleChat(groupId: chat.targetId);
      if (hasQuit) {
        _delete(chat);
        return;
      }
      // showDeleteBottleDialog(context, chat.targetId, callback: () async {
      //   showLoading();
      //   var resp = await bottleService.deleteBottle(groupId: chat.targetId);
      //   hideLoading();
      //   if (!resp.isSuccess) {
      //     toast(resp.msg ?? "");
      //     return;
      //   }
      //   _delete(chat);
      // });
      return;
    } else {
      showAlertDialog(
        context: context,
        content: s.sureDelete,
        confirmText: s.delete,
        onConfirm: () async {
          _delete(chat);
        },
      );
    }
    ChatListReporter.reportChatListItemDelete(chat.targetId);
  }

  void _delete(Chat chat) async {
    showLoading();
    final _ = await imChatService.deleteChat(chat.targetId, chatType: chat.chatType);
    hideLoading();
  }

  void _report(Chat chat) {
    var groupId = chat.targetId;
    if (groupId.isEmpty) return;
    var targetUserUid = getBottleChatTargetUid(groupId);
    var bottleId = getBottleChatBottleId(groupId);

    routerUtil.push(R_SETTINGS_REPORT, params: {
      P_DATA: BottleUserReport(
          bottleId: bottleId,
          targetUid: targetUserUid,
          groupId: groupId,
          resultCallback: (bool result, ReportTemplate template) {
            if (!result) return;
            showReportSuccessDialog(template.targetUid,
                page: R_CHAT_LIST,
                type: template.statisticType,
                toContent: template.commentId ?? template.itemId ?? "",
                confirmTxt: LocaleStrings.instance.deleteUser,
                confirmCallback: () => _deleteChat(chat));
          }),
      P_STATISTIC_FROM: R_CHAT_LIST,
    });
  }

  @override
  void onTabDoubleTap() {
    if (scrollController.isAttached && state.chats != null) {
      final index = state.chats!.indexWhere((e) => e.unreadMsgCount > 0);
      if (getContext() == null || index < 0) return;
      scrollController.scrollTo(
        index: index,
        duration: const Duration(milliseconds: 200),
      );
    } else {
      Log.e(_tag, 'isAttached = ${scrollController.isAttached}, state = ${state.runtimeType}');
    }
  }

  @override
  void onTabResume() {
    super.onTabResume();
    fateBellController.getFateBellList();
    beginTiming();
    WidgetUtils.post((_) => _showNotifyGuide());
  }

  @override
  void onTabPause() {
    super.onTabPause();
    endTimingAndReport();
  }

  void _showNotifyGuide() async {
    final manager = NotifyPermissionManager.instance();
    if (manager.isNotifyNeverRemind()) {
      return;
    }
    final isGranted = await Permission.notification.isGranted;
    if (isGranted) return;
    if (manager.hasCheckedToday()) return;
    final context = getContext();
    if (context == null) return;
    manager.updateLastCheckTime();
    final authPage = ReportAuthPage.chatList;
    final authContent = ReportAuthType.notify;
    showNotifyPermissionGuideDialog(authPage: authPage, authContent: authContent);
  }

  @override
  MainPageName get pageName => MainPageName.msg;

  @override
  void onLocaleChange(String localeName) {
    super.onLocaleChange(localeName);
    emit(state.clone());
  }

  /// 聊天记录列表打点
  void _reportChatListImp() async {
    int count = await imCacheService.getAllUnreadCount();
    ChatListStatistics.reportTabChatImp(
        isNotified: count == 0 ? ReportTabNotifyStatus.none : ReportTabNotifyStatus.notify);
  }

  /// 前往漂流瓶
  void _gotoWishBottle() {
    BottleStatistics.reportBottleEntranceClick(from: 'msglist');
    routerUtil.push(R_BOTTLE_PROGRESS, params: {P_STATISTIC_FROM: "Chat_vmatch_click"});
  }

  void _changeFriendType(ChangeFriendTypeEvent event, Emitter<ChatListState> emitter) {
    if (state.selectType == event.type) return;
    emitter.call(state.clone(selectType: event.type));
    add(ChatListEventReload());
  }

  @override
  void dispose() {
    if (isBottleChat) {
      imCacheService.cleanGroupChat();
    }
    _reloadDebounceTimer?.cancel();
    fateBellController.onClose();
    super.dispose();
  }

  @override
  String get pageDurationKey => "chat_list_tab";

  @override
  void endTimingAndReport() {
    if (!isReport) return;
    isReport = false;
    stopTiming(key: pageDurationKey);
    ChatListStatistics.reportTabChatDuration(
        duration: getOnTime(key: pageDurationKey), scrollDown: downTime.toString(), scrollUp: upTime.toString());
    downTime = 0;
    upTime = 0;
    removeTimer(key: pageDurationKey);
  }

  void _reportLiveRoom() async {
    if (isBottleChat || !hasOperation) return;
    RoomStatistics.reportRoomEntranceImp(from: "chat");
  }

  Future<void> _refreshMaleTaskDiamond() async {
    // 男端重新刷新钻石数
    chatListService.maleUpdateChatList();
  }

  void _sortChatList(List<Chat> chats, {String? managerUid}) {
    chats.sort((a, b) {
      final temp1 = getSort(a.targetId, managerUid, a.isPinned, a.sentTime, b.sentTime);
      final temp2 = getSort(b.targetId, managerUid, b.isPinned, b.sentTime, b.sentTime);
      return temp1.compareTo(temp2);
    });
  }

  /// 聊天消息排序，按以下优先级，数值越低，优先级越高：
  /// 1.am 客户经理 || am 分组
  /// 2.手动置顶
  /// 3.系统账号
  int getSort(String targetId, String? amId, bool isPinned, int? currentSentTime, int? anotherSendTime) {
    if ((amId?.isNotEmpty == true && targetId == amId) || targetId == Chat.amGroupId) {
      return 1;
    }
    if (isPinned) {
      return 2;
    }
    if (isSystemAccount(targetId)) {
      return 3;
    }
    return (currentSentTime ?? 0) > (anotherSendTime ?? 0) ? 4 : 5;
  }

  /// 固定展示客户列表 如没有查出聊天记录 则插入一条空白会话
  List<Chat> _handleCustomersList(List<Chat> chats) {
    if (_highValueUsers.isEmpty) return chats;

    List<Chat> result = chats;

    final uIds = chats.mapNotNull((e) => e.targetId);
    for (final user in _highValueUsers) {
      final uid = user.uid ?? '';
      if (uid.isNotEmpty && uIds.contains(uid) == false) {
        final chat = Chat(chatType: ChatType.private, targetId: uid, unreadMsgCount: 0, isPinned: false);
        result.add(chat);
      }
    }
    return result;
  }

  /// 获取客户列表
  void _fetchCustomers() async {
    _highValueUsers = await userService.getManagerUsers();
    _highValueUsers.length;
  }
}
