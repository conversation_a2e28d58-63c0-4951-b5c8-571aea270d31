import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/global/widgets/user_nick_widget.dart';
import 'package:biz/common/widgets/highlight_text.dart';
import 'package:biz/global/widgets/frame/frame_list_item.dart';
import 'package:biz/global/widgets/sex_age_chip.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:service/common/statistics/chat_list_statistics.g.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/contact_list_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';
import 'package:biz/biz/user/friends/user_top_view.dart';
class FrameContactListTile extends StatelessWidget {
  final Widget child;
  final int? index;

  FrameContactListTile(this.child, {this.index});

  @override
  Widget build(BuildContext context) {
    return FrameListItem(
      index: index,
      placeHolder: FrameListItem.contactPlaceHolder,
      itemBuilder: (BuildContext context) {
        return child;
      },
    );
  }
}

class ContactListTile extends StatefulWidget {
  ContactListTile(
      {Key? key,
      required this.avatarSize,
      this.userInfo,
      required this.uid,
      this.showDivider = true,
      this.keyword,
      this.onTap,
      this.needHighlightText = false,
      this.groupName,
      this.from})
      : super(key: key);
  final double avatarSize;
  final UserInfo? userInfo;
  final String uid;
  final bool showDivider;
  final String? keyword;
  final String? groupName;
  final String? from;

  /// 需要高亮关键字
  final bool needHighlightText;
  final Function? onTap;

  @override
  _ContactListTileState createState() => _ContactListTileState();
}

class _ContactListTileState extends State<ContactListTile> {
  StreamSubscription? _userSub;
  UserInfo? _userInfo;

  @override
  void initState() {
    super.initState();
    _userInfo = widget.userInfo;
    _initData();
  }

  void _initData() async {
    _userInfo ??= await userService.getUserInfo(widget.uid);
    if (_userInfo == null || !mounted) return;
    setState(() {});

    _userSub = rxUtil
        .observer<List<String>>(UserStatusEvent.updateUsers)
        .stream
        .listen(_onUserInfoChanged);
  }

  @override
  void didUpdateWidget(covariant ContactListTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.uid != widget.uid) {
      _reloadData();
    }
  }

  void _reloadData() async {
    _userInfo = await userService.getUserInfo(widget.uid);
    if (_userInfo == null || !mounted) return;
    setState(() {});
  }

  @override
  void dispose() {
    _userSub?.cancel();
    super.dispose();
  }

  void _onUserInfoChanged(List<String> uids) async {
    if (uids.contains(widget.uid)) {
      _userInfo = await userService.getUserInfo(widget.uid);
      if (_userInfo == null || !mounted) return;
      setState(() {});
    }
  }

  void _onItemTap() {
    routerUtil.push(R_USER_INFO,
        params: {P_UID: widget.uid, P_STATISTIC_FROM: widget.from});
    String _reportType = widget.onTap?.call();
    if (_reportType == "contact_list_page") {
      _reportContactItemClick(widget.uid);
    } else if (_reportType == "chat_search_page" ||
        _reportType == "contact_search_detail_page") {
      ChatListStatistics.reportChatListSearchResultItemClick(
        type: SearchResultType.contact,
      );
    }
  }

  void _reportContactItemClick(String uid) async {
    final status = await accountService.checkUserStatus(uid);
    final user = await userService.getUserInfo(uid);
    ContactListStatistics.reportContactItemClick(
      content: widget.groupName ?? "",
      isOnline: status?.isOnline.toReportString(),
      toUid: uid,
      toGender: user?.sexStr,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 18.pt, vertical: 6.pt),
        padding: EdgeInsets.only(left: 4.pt, right: 13.pt, top: 9.pt, bottom: 9.pt),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(11.pt),
        ),
        child: UserTopView(
            targetId: _userInfo?.uid,
            avatar: _userInfo?.avatar,
            avatarCode: _userInfo?.avatarCode,
            nickname: _userInfo?.nickname,
            sex: _userInfo?.sexStr,
            age: _userInfo?.age,
            isVoiceAuth: _userInfo?.isVoiceAuth,
            following: _userInfo?.isFollowing,
            from: widget.from,
            familyRole: _userInfo?.familyInfo?.role
        ),
      ),
      onTap: _onItemTap,
    );
  }
}
