import 'package:biz/biz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/file.dart';
import 'package:flutter_vap/flutter_vap.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/getx/get_event_mixin.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/im/model/im_gift_message_content.dart';
import 'package:service/modules/live/room_gift/model/gift_display_msg.dart';
import 'package:service/modules/live/room_gift/model/room_gift_display_manager.dart';

class ChatGiftEffectDisplayWidgetLogic extends GetxController with GetBindingMixin, GetEventMixin {
  RoomGiftDisplayManager? giftQueueManager;

  // final VapController vapController = VapController();
  final SvgaController giftController = SvgaController();
  GiftDisplayMsg? giftDisplayMsg;
  bool _showVap = false;

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
  }

  @override
  void stateDispose() {
    super.stateDispose();

  }

  void _initRx() {
    listenRxEvent<GiftDisplayMsg>(ChatGiftEvent.giftDisplay, _giftMsgDisplay);
  }

  void init() {
    giftController.setEndCallback((info) {
      _svgaEndCallback();
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      VapController.stop();
    });
  }

  void _svgaEndCallback() {
    _next();
  }

  void _next() {
    giftDisplayMsg = null;
    _showVap = false;
    update();
    giftQueueManager?.complete();
  }

  void _giftMsgDisplay(GiftDisplayMsg giftPbMsg) async {
    if (giftPbMsg.type != GiftDisplayType.chatGift){
      return;
    }
    if (giftQueueManager == null) {
      giftQueueManager = RoomGiftDisplayManager(giftController, showHandle: _show);
    }
    giftQueueManager?.put(giftPbMsg, fromSelf: giftPbMsg.fromSelf ?? false);
  }

  /// 处理展示
  void _show(GiftDisplayMsg msg, File? file, bool fromSelf) async {
    _display(msg, file);
  }

  void _display(GiftDisplayMsg msg, File? file) async {
    print("ChatGiftEffectDisplayWidgetLogic._display: ");
    if (file?.existsSync() == true) {
      /// 礼物特效
      String? videoUrl;
      if (msg.imGiftMsg?.giftType == IMGiftType.vap){
        videoUrl = msg.imGiftMsg?.giftDisplayUrl;
      }

      String? svgaUrl;
      if (msg.imGiftMsg?.giftType == IMGiftType.svga){
        svgaUrl = msg.imGiftMsg?.giftDisplayUrl;
      }

      if (videoUrl?.isNotEmpty == true) {
        bool vapIsShow = _showVap;

        giftDisplayMsg = msg;
        _showVap = true;
        update();

        WidgetUtils.post((duration) async {
          /// 部分手机出现VapView未初始化导致播放失败，如果一开始没有挂载上去，延迟400ms后播放
          if (!vapIsShow) {
            await Future.delayed(Duration(milliseconds: 400));
          }

          if (getContext() != null) {
            _playWithVap(file?.path ?? "");
          }
        });
      } else if (svgaUrl?.isNotEmpty == true) {
        /// svga
        giftDisplayMsg = msg;
        _showVap = false;
        update();

        WidgetUtils.post((duration) async {
          if (getContext() != null) {
            giftController.startSvga(SvgaInfo(
              file: file,
              repeat: false,
            ));
          }
        });
      }else {
        giftQueueManager?.complete();
      }
      return;
    } else {
      giftQueueManager?.complete();
    }
  }

  /// 视频动画
  void _playWithVap(String filePath, {int? type}) {
    VapController.playPath(filePath, type: type, resultCallback: _vapCallback);
  }

  /// vap 回调
  void _vapCallback(Map<dynamic, dynamic>? result) {
    VapController.stop();

    /// 如果是进场座驾还要等待进场横幅动效结束才能继续执行
    // if (giftDisplayMsg?.isGiftMsg == false) {
    //   return;
    // }

    giftDisplayMsg = null;
    _showVap = false;
    update();
    giftQueueManager?.complete();
  }
}
