import 'package:biz/biz.dart';
import 'package:biz/biz/chat/bloc/chat_tab_bloc.dart';
import 'package:biz/biz/chat/chat_list/chat_list.dart';
import 'package:biz/biz/chat/contact/contact_group_page.dart';
import 'package:biz/biz/home/<USER>/home_style_tab_indicator.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/app_bar/icon_btn.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/keep_wrapper.dart';

import '../ad/float_btn/float_btn.dart';
import '../ad/float_btn/no_scaling_animation.dart';

class ChatTabPage extends StatelessWidget {
  final _tabs = [
    KeepWrapper(child: ContactGroupPage()),
    KeepWrapper(child: ChatList(showRoomUserRecommend: true)),
  ];

  @override
  Widget build(BuildContext context) {
    final bloc = ChatTabBloc();
    return LifecycleBlocBuilder<ChatTabBloc, ChatTabState>(
      bloc: bloc,
      builder: (BuildContext context, state) {
        return Stack(
          children: [
            FLImage.asset(Res.chatBgPage, width: double.infinity, fit: BoxFit.fitHeight),
            _page(bloc, state, context),
          ],
        );
      },
    );
  }

  Widget _page(ChatTabBloc bloc, ChatTabState state, BuildContext context) {
    if (state.hadRelease) {
      return Scaffold(
        backgroundColor: Colors.transparent,
        body: EmptyWidget(),
      );
    }
    final tab = DefaultTabController(
      length: _tabs.length,
      initialIndex: _tabs.length - 1,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Column(
          children: [
            SizedBox(
              height: MediaQuery.of(context).padding.top,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _tabBar(),
                Spacer(),
                _addFriendsBtn(state),
                SizedBox(width: 10.pt),
              ],
            ),
            Expanded(child: TabBarView(children: _tabs)),
          ],
        ),
        floatingActionButton: FloatBtn(
          size: Size(56.pt, 56.pt),
          type: FloatAdType.chat,
        ),
        floatingActionButtonLocation: FloatBtnLocation(context),
        floatingActionButtonAnimator: NoScalingAnimation(),
      ),
    );
    return bloc.state.showGuideStep == ChtGuideStep.none ? tab : Stack(children: [tab, _guideWidget(bloc)]);
  }

  Widget _tabBar() {
    TextStyle labelStyle({FontWeight? fontWeight, double? fontSize}) {
      return TextStyle(fontSize: fontSize ?? 15.sp, fontWeight: fontWeight ?? FontWeight.normal);
    }

    Widget chatTab = Text(LocaleStrings.instance.chats);

    return TabBar(
      isScrollable: true,
      unselectedLabelStyle: labelStyle(fontWeight: FontWeightExt.heavy, fontSize: 15.sp),
      unselectedLabelColor: R.color.colorB2,
      labelColor: R.color.color1F,
      labelStyle: labelStyle(fontWeight: FontWeightExt.heavy, fontSize: 18.sp),
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.symmetric(horizontal: 14.pt),
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      indicator: HomeStyledTabIndicator(
        margin: 0.pt,
        width: 20.pt,
        height: 4.pt,
        fillColor: R.color.primaryColor,
      ),
      tabs: [
        Tab(child: Text(LocaleStrings.instance.contacts)),
        Tab(child: chatTab),
      ],
    );
  }

  Widget _addFriendsBtn(ChatTabState state) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        IconBtn(
          icon: Res.commonAdd4,
          iconSize: Size(16.pt, 16.pt),
          iconColor: R.color.color20,
          widgetSize: Size(27.pt, 27.pt),
          margin: EdgeInsets.only(right: 10.pt),
          bgDecoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(13.5.pt)),
          ),
          onTap: () {
            // routerUtil.push(R_CHAT_SEARCH, params: {P_IS_SEARCH_BY_ID: "true"});
            routerUtil.push(R_LIVE_ROOM_SEARCH);
          },
        ),
        Positioned(top: 4.pt, left: 30.pt, child: _numberBadgeWidget(state.friendsRequestCount))
      ],
    );
  }

  Widget _numberBadgeWidget(int badge) {
    if (badge <= 0) {
      return SizedBox();
    }

    final badgeWidth = 15.pt;
    return Container(
      height: badgeWidth,
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(badgeWidth / 2),
      ),
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 4.pt),
      child: FittedBox(
        child: Text(badge <= 99 ? '$badge' : '···',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10.sp,
            )),
      ),
    );
  }

  Widget _guideWidget(ChatTabBloc bloc) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withOpacity(0.75),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                final step = bloc.state.showGuideStep;
                if (step == ChtGuideStep.first) {
                  bloc.add(ChatShowGuideEvent(ChtGuideStep.second));
                } else {
                  bloc.add(ChatShowGuideEvent(ChtGuideStep.none));
                }
              },
              child: const SizedBox.shrink(),
            ),
          ),
          Visibility(
            visible: bloc.state.showGuideStep == ChtGuideStep.first,
            child: Positioned(
              top: 130.pt,
              child: ChatGuideWidget(
                img: Res.chatImgGuideFate,
                title: LocaleStrings.instance.chatGuideTipsFate,
                onNext: () => bloc.add(ChatShowGuideEvent(ChtGuideStep.second)),
              ),
            ),
          ),
          Visibility(
            visible: bloc.state.showGuideStep == ChtGuideStep.second,
            child: Positioned(
              top: 45.pt,
              child: ChatGuideWidget(
                img: Res.chatImgGuideMsg,
                title: LocaleStrings.instance.chatGuideTipsMsg,
                direction: VerticalDirection.up,
                onNext: () => bloc.add(ChatShowGuideEvent(ChtGuideStep.none)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ChatGuideWidget extends StatelessWidget {
  final String img;
  final String title;
  final VerticalDirection direction;
  final VoidCallback? onNext;

  const ChatGuideWidget({
    super.key,
    required this.img,
    required this.title,
    this.direction = VerticalDirection.down,
    this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      verticalDirection: direction,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        FLImage.asset(
          img,
          width: 1.w - 14.pt,
          fit: BoxFit.cover,
        ),
        Row(
          children: [
            Column(
              verticalDirection: direction,
              children: [
                FLImage.asset(
                  Res.chatImgGuideLine,
                  width: 12.pt,
                  fit: BoxFit.cover,
                ),
                Transform.scale(
                  scaleY: direction == VerticalDirection.up ? -1 : 1,
                  child: FLImage.asset(
                    Res.chatIconGuideArrow,
                    width: 20.pt,
                    fit: BoxFit.cover,
                  ),
                )
              ],
            ),
            40.wSpace,
          ],
        ),
        Container(
          padding: EdgeInsets.fromLTRB(27.pt, 21.pt, 30.pt, 12.pt),
          width: 1.w - 14.pt,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14.pt),
            gradient: LinearGradient(
              colors: [const Color(0xFF8875FF), const Color(0xFFB582FD)],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13.sp,
                  fontWeight: FontWeightExt.medium,
                ),
              ),
              4.hSpace,
              GeneralBtn(
                width: 72.pt,
                height: 24.pt,
                title: LocaleStrings.instance.next,
                childColor: R.color.primaryColor,
                backgroundColor: Colors.white,
                borderColor: R.color.primaryColor,
                borderWidth: 1.pt,
                fontSize: 13.sp,
                onTap: onNext,
              ),
            ],
          ),
        )
      ],
    );
  }
}
