import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/game/gameview/new_game/widgets/game_error_widget.dart';
import 'package:biz/biz/game/gameview/new_game/widgets/game_loading_widget.dart';
import 'package:biz/biz/game/gameview/new_game/widgets/game_start_coin_ani.dart';
import 'package:biz/biz/game/gameview/new_game/widgets/game_start_star_coins_icon_ani.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/game/abs_game_service.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/modules/live/room/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';

import '../../../../common/widgets/gradient/gradient_widget.dart';
import '../../../live_room/component/settings/animated_settings_button.dart';
import '../../game_rule/game_rule_dialog.dart';
import 'bloc/new_game_bloc.dart';
import 'game_player_container.dart';

final _waitingSvgaKey = GlobalKey();

/// 保存选手位key
Map<int, GlobalKey?> playerSeatKeyMap = {};

/// 游戏区域高度
final gameViewBodyHeight = 1.w * 968.0 / 750.0;

class NewGameWidget extends StatefulWidget {
  final String roomId;
  final GameType type;

  /// 玩家最大数量
  final int playerNum;

  /// 是否最小化
  final bool isMini;

  /// 是否是快速模式
  final bool isQuickMode;

  NewGameWidget(
    this.roomId,
    this.type, {
    this.playerNum = 4,
    this.isMini = false,
    this.isQuickMode = false,
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => NewGameWidgetState();
}

class NewGameWidgetState extends State<NewGameWidget> with TickerProviderStateMixin {
  late NewGameBloc _bloc;
  final String viewType = 'plugins.room/webgame';

  Timer? _timer;

  /// 游戏英文logo
  late String gameLogo;

  /// 游戏阿语logo
  late String gameLogoAr;

  /// 是否房主
  Future<bool> get isRoomOwner async => (await roomService.getCurrentUserInfo())?.isOwner ?? false;

  /// 游戏界面最小化变化值
  double _aniValue = 1;

  final _aniDuration = const Duration(milliseconds: 500);

  StreamSubscription? _subAni;

  late AnimationController _aniController;

  late Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _aniController = AnimationController(vsync: this, duration: const Duration(milliseconds: 500));
    _animation = Tween(begin: Offset(0, 0), end: Offset(0.46, 0.40))
        .animate(CurvedAnimation(parent: _aniController, curve: Curves.easeIn));
    _aniController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        roomService.updateRoomModeGameMiniStatus(isMini: true);
      }
    });
    switch (widget.type) {
      case GameType.game_ludo:
        gameLogo = Res.liveGameLudoLogo;
        gameLogoAr = Res.liveGameLudoLogo;
        break;
      default:
        gameLogo = Res.liveGameLudoLogo;
        gameLogoAr = Res.liveGameLudoLogo;
        break;
    }

    _bloc = NewGameBloc(widget.roomId, widget.type, widget.playerNum, isQuickMode: widget.isQuickMode);
    _bloc.progressController = AnimationController(vsync: this, lowerBound: 0, upperBound: 1, value: 0);

    _subAni = rxUtil.observer<bool>(RoomOperationEvent.startGameMini).listen((value) {
      if (!mounted) return;
      if (_aniValue == 0) return;

      setState(() {
        _aniValue = 0;
      });
      WidgetUtils.post((duration) {
        _aniController.forward(from: 0);
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _bloc.close();
    _subAni?.cancel();
    _aniController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isMini) {
      return LifecycleBlocBuilder<NewGameBloc, NewGameState>(
          bloc: _bloc,
          builder: (context, state) {
            return SizedBox.shrink();
          });
    }

    final EdgeInsets padding = MediaQuery.of(context).padding;
    var height = min(gameViewBodyHeight, 1.h - (234.pt + padding.bottom + padding.top));

    var child = LifecycleBlocBuilder<NewGameBloc, NewGameState>(
        bloc: _bloc,
        builder: (context, state) {
          final isWaitingVisible = state.status == GameStatus.wait || state.status == GameStatus.ready;
          return Container(
            width: 1.w,
            height: height,
            constraints: BoxConstraints(maxHeight: height),
            child: Stack(
              children: [
                /// 游戏
                Positioned.fill(
                  child: Opacity(
                      opacity: (GameStatus.wait != state.status && state.status != GameStatus.error) ? 1 : 0,
                      child: _body()),
                ),

                /// 准备阶段
                Positioned.fill(child: Visibility(visible: isWaitingVisible, child: _gameWaitWidget())),

                /// 游戏开始动画
                Positioned.fill(
                    child: IgnorePointer(
                  child: GameStartCoinAniWidget(displayController: _bloc.coinAniController),
                )),

                Positioned.fill(child: Visibility(visible: state.status == GameStatus.error, child: _failureWidget())),
                Positioned.fill(
                    child: Visibility(
                        visible: state.status == GameStatus.none ||
                            state.status == GameStatus.loading,
                        child: _loadingWidget())),

                Positioned(child: _setting(state), right: 5.pt, top: 80.pt),
              ],
            ),
          );
        });

    return SlideTransition(
      position: _animation,
      child: AnimatedScale(
        child: child,
        duration: _aniDuration,
        scale: _aniValue,
        curve: Curves.easeIn,
      ),
    );
  }

  Widget _body() {
    return Stack(
      children: [
        /// LUDO模式显示当前模式提示
        if (widget.type == GameType.game_ludo && playerService.isGamePlaying())
          Positioned(
              top: 35.pt,
              left: 64.pt,
              child: Text(
                roomService.getCurrentRoom()?.isQuickMode == true
                    ? LocaleStrings.instance.quicklyLudoTips
                    : LocaleStrings.instance.normalLudoTips,
                style: TextStyle(color: Colors.white, fontSize: 12.pt),
              )),
        Platform.isIOS ? _iosBody() : _androidBody()
      ],
    );
  }

  Widget _androidBody() {
    return PlatformViewLink(
      viewType: viewType,
      surfaceFactory: (BuildContext context, PlatformViewController controller) {
        return AndroidViewSurface(
          controller: controller as AndroidViewController,
          gestureRecognizers: const <Factory<OneSequenceGestureRecognizer>>{},
          hitTestBehavior: PlatformViewHitTestBehavior.opaque,
        );
      },
      onCreatePlatformView: (PlatformViewCreationParams params) {
        return PlatformViewsService.initSurfaceAndroidView(
          id: params.id,
          viewType: viewType,
          layoutDirection: TextDirection.ltr,
          creationParamsCodec: const StandardMessageCodec(),
          onFocus: () {
            params.onFocusChanged(true);
          },
        )
          ..addOnPlatformViewCreatedListener(params.onPlatformViewCreated)
          ..create();
      },
    );
  }

  Widget _iosBody() {
    final creationParams = <String, String>{
      "height": widget.type.gameHeight,
    };
    return UiKitView(
      viewType: viewType,
      layoutDirection: TextDirection.ltr,
      creationParams: creationParams,
      creationParamsCodec: const StandardMessageCodec(),
    );
  }

  Widget _loadingWidget() {
    return GameLoadingWidget(
      widget.type,
      gameStatus: _bloc.state.status,
      progressController: _bloc.progressController,
      key: ValueKey("game_loading_widget"),
    );
  }

  Widget _failureWidget() {
    return GameErrorWidget(widget.type, onRetry: () {
      _bloc.add(ReloadGameEvent());
    });
  }

  Widget _gameWaitWidget() {
    final children = <Widget>[];
    children.addAll([
      _logoSvga(widget.type),
      if (widget.type == GameType.game_ludo)
        OuterBorderGradientText(
          text: widget.isQuickMode ? LocaleStrings.instance.quickly : LocaleStrings.instance.normal,
          textStyle: TextStyle(fontSize: 15.sp, fontWeight: fontWeightBold),
          borderWidth: 1,
          borderColor: Color(0xFF027DB6),
        ),
      _coinsWidget(),
      _gamePlayerWidget()
    ]);
    return Container(
      width: double.infinity,
      color: Colors.transparent,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _logoSvga(GameType type) {
    return SizedBox(
      height: 155.pt,
      key: _waitingSvgaKey,
      child: SvgaWidget(
        svgaInfo: SvgaInfo(
          repeat: true,
          assetUrl: type.svgaLogo,
        ),
        decodeHeight: 155.pt,
        scale: 1.5,
        fit: BoxFit.contain,
        checkUrlOnly: true,
      ),
    );
  }

  Widget _gamePlayerWidget() {
    return GamePlayerContainer(
      controller: _bloc.playerController,
      onInvite: (index) => _bloc.add(InviteEvent(seatIndex: index)),
      onInviteRoomUser: (index) => _bloc.add(InviteEvent(seatIndex: index, isOnlineUSer: true)),
      onJoin: (index) => _bloc.add(JoinGameEvent(seatIndex: index)),
      onLock: (index) => _bloc.add(LockPosEvent(seatIndex: index)),
      onQuit: () => _bloc.add(QuitGameEvent()),
      onStart: () => _bloc.add(PlayGameEvent()),
      onAutoStartCheck: (checked) => _bloc.add(SetAutoStartEvent(checked: checked)),
    );
  }

  Widget _coinsWidget() {
    return SizedBox(
      width: 250.pt,
      height: 85.pt,
      child: Stack(alignment: Alignment.center, children: [
        Positioned(
          child: Container(
            height: 50.pt,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [Colors.black.withOpacity(0.5), Colors.black.withOpacity(0.2), Colors.transparent, Colors.transparent])),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "${LocaleStrings.instance.rank}1:",
                      maxLines: 1,
                      style: TextStyle(color: Color(0xFFFFE25E), fontSize: 13.sp),
                    ),
                    SizedBox(width: 7.5.pt),
                    FLImage.asset(Res.financeIconExchangeCoin, width: 17.pt, height: 17.pt),
                    SizedBox(width: 6.pt),
                    SizedBox(
                        child: Text(
                          '${roomService.getCurrentRoom()?.gameConfig?.total ?? ''}',
                          maxLines: 1,
                          style: TextStyle(color: Colors.white, fontSize: 15.sp, fontWeight: fontWeightBold),
                        ),
                        width: 90.pt),
                  ],
                ),
                // SizedBox(height: 5.pt),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "${LocaleStrings.instance.ticket}:",
                      maxLines: 1,
                      style: TextStyle(color: Color(0xFFFFE25E), fontSize: 13.sp),
                    ),
                    SizedBox(width: 7.5.pt),
                    FLImage.asset(Res.financeIconExchangeCoin, width: 17.pt, height: 17.pt),
                    SizedBox(width: 6.pt),
                    SizedBox(
                        child: Text(
                          '${roomService.getCurrentRoom()?.gameConfig?.cost ?? ''}',
                          maxLines: 1,
                          style: TextStyle(color: Colors.white, fontSize: 15.sp, fontWeight: fontWeightBold),
                        ),
                        width: 90.pt),
                  ],
                ),
              ],
            ),
          ),
          left: 50.pt,
          width: 250.pt,
        ),
        Positioned(
          top: 0,
          left: 0,
          child: GameStarCoinsIconAniWidget(
              key: _bloc.keyStarCoinsIcon, displayController: _bloc.coinAniController.betIconAniController),
        ),
      ]),
    );
  }

  Widget _setting(NewGameState state) {
    return Visibility(
      visible: roomService.getCurrentRoom()?.isGameMode == true,
      child: AnimatedSettingsButton(size: 28.pt,
          onMusicTap: (enable) {
        newGameService.setMusicEnable(enable);
        /// 通知相关状态更新
        rxUtil.send(RoomGameEvent.musicEnable, enable);
      }, onHelpTap: () {
          final gMode = roomService.getCurrentRoom()?.mode;
          switch (gMode) {
            case RoomMode.LUDO_GAME:
              showGameFaqDialog(gameType: GameType.game_ludo);
              return;
            case RoomMode.DOMINO_GAME:
              showGameFaqDialog(gameType: GameType.game_domino);
              return;
            case RoomMode.CANDY_GAME:
              showGameFaqDialog(gameType: GameType.game_candy);
              return;
            case RoomMode.UNO_GAME:
              showGameFaqDialog(gameType: GameType.game_uno);
              return;
          }
        },),
    );
  }
}
