import 'dart:async';

import 'package:biz/biz.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/svga.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/modules/live/room/event/event.dart';
import 'package:service/pb/net/pb_push.pb.dart';
import 'package:service/utils/string_extension.dart';

import '../../../../common/widgets/gradient/gradient_widget.dart';
import '../../game_result_share/game_result_share_dialog.dart';
import 'game_rank_item.dart';

/// 游戏结果
class CommonGameResultWidget extends StatefulWidget {
  final PbGameResultNotice result;
  final GameType gameType;
  final VoidCallback? close;
  final VoidCallback? again;
  final VoidCallback? onWillPop;
  final bool showPlay;
  final int sec;
  final void Function(String uid)? onAddFriend;
  final void Function(String uid)? onSendGift;

  CommonGameResultWidget(
    this.result,
    this.gameType, {
    this.close,
    this.again,
    this.onWillPop,
    this.showPlay = false,
    this.sec = 0,
    this.onAddFriend,
    this.onSendGift,
  });

  @override
  State<StatefulWidget> createState() {
    return _CommonGameResultWidgetState();
  }
}

class _CommonGameResultWidgetState extends State<CommonGameResultWidget>
    with SingleTickerProviderStateMixin {
  Timer? _countDownTimer;
  int totalSec = 10;
  StreamSubscription? _subscription;

  bool _hadShowSvga = false;

  final SvgaController _svgaController = SvgaController();

  @override
  void initState() {
    super.initState();
    if (widget.sec > 0) {
      totalSec = widget.sec;
      _initCountDownTimer();
    }

    _svgaController.setEndCallback((e) {
      if (mounted && _hadShowSvga == false) {
        setState(() {
          _hadShowSvga = true;
        });
      }
    });

    _subscription = rxUtil.observer(RoomGameEvent.closeResult).listen((value) {
      if (mounted) {
        _popDialog();
      }
    });
  }

  void _initCountDownTimer() {
    _countDownTimer?.cancel();
    _countDownTimer = Timer.periodic(Duration(seconds: 1), (value) {
      if (mounted) {
        if (totalSec > 0) {
          totalSec -= 1;
        } else {
          _countDownTimer?.cancel();
          _popDialog();
          widget.onWillPop?.call();
        }
      }
    });
  }

  @override
  void dispose() {
    _countDownTimer?.cancel();
    _countDownTimer = null;
    _subscription?.cancel();
    _subscription = null;
    super.dispose();
  }

  Widget _winnerSvga() {
    final info = SvgaInfo(
      assetUrl: Res.liveSvgaBombcatMvp,
      repeat: false,
    );

    if (widget.result.items.isNotEmpty) {
      info.textMap['text1'] = SvgaText(
          widget.result.items.first.user.user.nickname,
          textSize: 36,
          color: "#ffffff");
      String headimgurl = widget.result.items.first.user.user.headimgurl;
      if (headimgurl.contains("?")) {
        headimgurl = headimgurl.substring(0, headimgurl.indexOf("?"));
      }
      info.imageMap['photo1'] = headimgurl
          .resized(width: 100)
          .formatted(format: "png")
          .circled(radius: 100);
    }
    return Material(
      color: Colors.transparent,
      child: SizedBox(
        width: 309.pt,
        height: 205.pt,
        child: SvgaWidget(
          controller: _svgaController,
          svgaInfo: info,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_hadShowSvga) {
      return _winnerSvga();
    }

    return WillPopScope(
        onWillPop: () {
          widget.onWillPop?.call();
          return Future.value(true);
        },
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              height: 520.pt,
              width: 350.pt,
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  Positioned(
                    child: _body(),
                    top: 70.pt,
                    bottom: 0,
                    left: 0,
                    right: 0,
                  ),
                  Positioned(child: _top(), top: 0),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _body() {
    return Container(
      child: Column(
        children: [
          Expanded(child: _ranking()),
          SizedBox(height: 7.pt),
          if (widget.again != null) _oneMoreGameBtn(),
          SizedBox(height: 23.pt),
        ],
      ),
    );
  }

  Widget _ranking() {
    final children = <Widget>[];

    for (int i = 0; i < widget.result.items.length; i++) {
      children.add(
        GameRankItemWidget(
          rank: i,
          resultItem: widget.result.items[i],
          onAddFriend: (uid) {
            widget.onAddFriend?.call(uid);
          },
          onSendGift: (uid) {
            _popDialog();
            widget.onSendGift?.call(uid);
          },
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: children,
        mainAxisSize: MainAxisSize.max,
      ),
    );
  }

  Widget _top() {
    return SizedBox(
      width: 253.pt,
      height: 76.pt,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned(
            child: SizedBox(
              width: 253.pt,
              height: 76.pt,
              child: FLImage.asset(Res.liveGameResultTop),
            ),
          ),
          Positioned(
            top: 46.pt,
            child: GradientShadowText(
              LocaleStrings.instance.gameOver,
              colors: [Colors.white,Colors.white],
              fontSize: 16.sp,
              shadowColor: Color(0xFFBA3D1B),
              shadowOffset: Offset(2.pt, 2.pt),
            ),
          ),
        ],
      ),
    );
  }

  Widget _oneMoreGameBtn() {
    final uid = accountService.currentUid();
    final rank =
    widget.result.items.indexWhere((e) => e.user.user.uid == uid);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (rank != -1) ...[
          ScaleTapWidget(
            child: Container(
              width: 114.pt,
              height: 37.pt,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Res.liveGameBtnYellow),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FLImage.asset(
                    Res.gameIconResultShare,
                    width: 17.pt,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(width: 7.5.pt),
                  Text(
                    LocaleStrings.instance.share,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: const Color(0xFFA44400),
                      fontSize: 14.sp,
                      fontWeight: fontWeightBold,
                    ),
                  )
                ],
              ),
            ),
            onTap: () {
              routerUtil.pop(context: context);
              if (rank < widget.result.items.length) {
                final result = widget.result.items[rank];
                showGameResultShareDialog(
                  context: context,
                  result: result,
                  gameType: widget.gameType,
                  isMvp:  rank == 0,
                  rank: rank + 1,
                );
              }
            },
          ),
          SizedBox(width: 26.pt),
        ],
        ScaleTapWidget(
          child: Container(
            width: 114.pt,
            height: 37.pt,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Res.liveGameBtnGreen),
              ),
            ),
            child: Text(
              LocaleStrings.instance.playAgain,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: const Color(0xFF2C6704),
                fontSize: 14.sp,
                fontWeight: fontWeightBold,
              ),
            ),
          ),
          onTap: () {
            widget.again?.call();
            _popDialog();
            widget.close?.call();
          },
        )
      ],
    );
  }

  void _popDialog() {
    var listRoutes =
        FLRouter.routeObserver.getRoutes().map((e) => e.settings.name);
    if (listRoutes.contains(R_LIVE_LIVE_ROME)) {
      routerUtil.popUntil(R_LIVE_LIVE_ROME);
    }
  }
}
