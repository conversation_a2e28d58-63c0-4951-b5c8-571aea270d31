import 'package:biz/biz.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/game_statistics.g.dart';
import 'package:service/global/const/remote_config.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/pb/net/pb_game.pb.dart';
import 'package:package_info/package_info.dart';

import '../../../global/widgets/boundary_to_image_share_widget.dart';

void showGameResultShareDialog({
  required BuildContext context,
  required PbGameResultItem result,
  required GameType gameType,
  required bool isMvp,
  required int rank,
}) async {
  final PackageInfo packageInfo = await PackageInfo.fromPlatform();

  final section =
      await getService<AbsRemoteConfigService>()?.getSection(sectionAppUi);
  // final showWhatsApp =
  //     section?.getValue(functionShareType)?.getBool('whatsapp') ?? false;

  showBoundaryToImageWidgetShareDialog(
    context: context,
    boundaryToImageWidget: GameResultShareWidget(
      result: result,
      appName: packageInfo.appName,
      gameType: gameType,
      isMvp: isMvp,
      rank: rank,
    ),
    showWhatsApp: true,
    from: "game_result",
    shareTypeClickCallback: (type) {
      final shareType = type == ShareApps.extra ? 'save' : type.name;
      GameStatistics.reportGameOverShareClick(
        roomId: roomService.getCurrentRoomId(),
        gameId: gameType.name,
        gameRank: '$rank',
        shareType: shareType,
      );
    },
  );
  GameStatistics.reportGameOverShareImp(
    roomId: roomService.getCurrentRoomId(),
    gameId: gameType.name,
  );
}

class GameResultShareWidget extends StatelessWidget {
  const GameResultShareWidget({
    super.key,
    required this.result,
    required this.appName,
    required this.gameType,
    required this.isMvp,
    required this.rank,
  });

  final PbGameResultItem result;
  final GameType gameType;
  final bool isMvp;
  final int rank;
  final String appName;

  String get _bgPath => gameType.shareBgUrl;

  String get _framePath {
    if (isMvp) return Res.gameImgGameShareMvpFrame;
    switch (rank) {
      case 1:
        return Res.gameImgGameShare1Frame;
      case 2:
        return Res.gameImgGameShare2Frame;
      case 3:
        return Res.gameImgGameShare3Frame;
      default:
        return Res.gameImgGameShare4Frame;
    }
  }

  String get _nameBgPath {
    if (isMvp) return Res.gameImgGameShareName1;
    switch (rank) {
      case 1:
        return Res.gameImgGameShareName1;
      case 2:
        return Res.gameImgGameShareName2;
      case 3:
        return Res.gameImgGameShareName3;
      default:
        return Res.gameImgGameShareName4;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 310.pt,
      height: 449.pt,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.pt),
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.topCenter,
            children: [
              CachedNetworkImage(
                imageUrl: _bgPath,
                width: 310.pt,
                fit: BoxFit.fitWidth,
                errorWidget: (_, __, ___) => SizedBox(height: 361.pt),
              ),
              Positioned(
                top: 21.pt,
                child: _avatarWidget(),
              ),
              Positioned(
                top: 140.pt,
                child: _nameWidget(),
              ),
            ],
          ),
          Expanded(
            child: _appInfo(),
          ),
        ],
      ),
    );
  }

  Widget _avatarWidget() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        FLImage.asset(
          _framePath,
          height: 109.pt,
          fit: BoxFit.cover,
        ),
        Positioned(
          bottom: 18.pt,
          child: UserAvatar(
            url: result.user.user.headimgurl,
            size: 61.pt,
          ),
        )
      ],
    );
  }

  Widget _nameWidget() {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        FLImage.asset(
          _nameBgPath,
          height: 54.pt,
          fit: BoxFit.cover,
        ),
        Positioned(
          top: 7.pt,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 200.pt),
            child: Text(
              result.user.user.nickname,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.white, fontSize: 18.sp),
            ),
          ),
        ),
        Visibility(
          visible: isMvp,
          child: Positioned(
            top: -21.pt,
            left: 4.pt,
            child: FLImage.asset(
              Res.gameIconGameShareMvp,
              height: 54.pt,
              fit: BoxFit.cover,
            ),
          ),
        )
      ],
    );
  }

  Widget _appInfo() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(height: 7.5.pt, color: const Color(0xFFF5F7F9)),
          Padding(
            padding: EdgeInsets.all(10.pt),
            child: Row(
              children: [
                FLImage.asset(Res.commonLogo, width: 56.pt),
                SizedBox(width: 15.5.pt),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FLImage.asset(
                      Res.commonColorfulTextLogo,
                      height: 16.5.pt
                    ),
                    SizedBox(height: 10.pt),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 200.pt),
                      child: Text(
                        'www.winker.chat',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFFB2B2B2),
                        ),
                      ),
                    ),
                  ],
                ),
                Spacer(),
                FLImage.asset(
                  Res.gameShareQrCode,
                  width: 53.pt,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
