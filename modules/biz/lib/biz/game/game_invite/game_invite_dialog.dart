import 'dart:convert';

import 'package:biz/biz.dart';
import 'package:biz/biz/share/share_widget.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:flutter/material.dart' hide showBottomSheet;
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/modules/common/const/enums.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/friend/model/friend.dart';
import 'package:service/modules/friend/model/friend_list.dart';
import 'package:service/modules/live/game/const/enums.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/loading.dart';
import 'package:service/common/statistics/game_statistics.g.dart';
import 'package:service/common/statistics/invite_game_statistics.g.dart';

import '../../user/user_info/helper/add_friend_helper.dart';
import 'game_invite_item.dart';

/// 游戏邀请
void showGameInviteDialog(
  String roomId, {
  int? seatIndex,
  VoidCallback? close,
  VoidCallback? again,
  VoidCallback? onWillPop,
  GameType gameType = GameType.game_ludo,
}) {
  DialogScheduler.instance().schedule(
    () => showBottomSheet(
        (_) => _GameInviteDialog(
              roomId,
              close: close,
              again: again,
              onWillPop: onWillPop,
              seatIndex: seatIndex,
              gameType: gameType,
            ),
        barrierDismissible: true),
  );

  GameStatistics.reportGameInviteRoomUserImp(
    roomId: roomService.getCurrentRoomId(),
    gameId: gameType.name,
  );
}

/// 游戏结果
class _GameInviteDialog extends StatefulWidget {
  final VoidCallback? close;
  final VoidCallback? again;
  final VoidCallback? onWillPop;
  final int? seatIndex;
  final GameType gameType;
  final String roomId;

  _GameInviteDialog(this.roomId,
      {this.seatIndex, this.close, this.again, this.onWillPop, this.gameType = GameType.game_ludo});

  @override
  State<StatefulWidget> createState() {
    return _GameInviteDialogState();
  }
}

class _GameInviteDialogState extends State<_GameInviteDialog> with SingleTickerProviderStateMixin {
  FriendList? _friends;

  LiveRoomShare? _shareTmp;

  @override
  void initState() {
    super.initState();

    _loadShareTmp();
    _loadFriend();
  }

  void _loadFriend() async {
    final resp = await friendService.getFriendList(recommend: true, opt: '');
    if (resp.isSuccess && mounted) {
      setState(() {
        _friends = resp.data;
      });
    }

    InviteGameStatistics.reportInviteGamePopupImp();
  }

  void _loadShareTmp() async {
    final roomInfo = roomService.getCurrentRoom();
    final userInfo = await roomService.getCurrentUserInfo();

    /// 拼接房间deeplink
    var deepLink = "${DeepLink.deepLinkProto}${R_LIVE_ROME_JOIN.replaceFirst("/", "")}"
        "${"?$P_ID=${roomInfo?.roomId}"}${"&$P_STATISTIC_FROM=share"}";
    final content;

    /// 如果房间有密码，并且自己是房主，分享的房间链接自动带有密码
    if (roomInfo?.hasPw == 1 && (userInfo?.isOwner ?? false)) {
      deepLink = "$deepLink${"&$P_ROOM_PWD=${roomInfo?.password}"}";
      content = LocaleStrings.instance
          .liveRoomShareContentAndPwd(roomInfo?.name ?? "", roomInfo?.roomId ?? "", roomInfo?.password ?? "");
    } else {
      content = LocaleStrings.instance.liveRoomShareContent(roomInfo?.name ?? "", roomInfo?.roomId ?? "");
    }
    _shareTmp = LiveRoomShare(
      content: content,
      imContent: json.encode(roomInfo?.toJson() ?? ""),
      icon: roomInfo?.cover,
      deepLink: deepLink,
      link: deepLink,
      // tagBackgroundColor: roomService.getCurrentRoom()?.tagBackgroundColor,
      // tagTextColor: roomService.getCurrentRoom()?.tagTextColor,
    );
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(18.pt),
            topLeft: Radius.circular(18.pt),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _top(),
            if (_shareTmp != null) ...[
              ShareWidget(
                showFriends: false,
                showApps: true,
                showBlock: false,
                shareTemplate: _shareTmp!,
                shareStatisticCallback: (moreType, type, user) {
                  GameStatistics.reportGameInviteRoomShareClick(
                      roomId: roomService.getCurrentRoomId(),
                      gameId: widget.gameType.name,
                      shareType: type?.name);
                },
                shareWidgetType: ShareWidgetType.normal,
              ),
              SizedBox(height: 20.pt),
            ],
            _body(),
          ],
        ),
      ),
    );
  }

  Widget _body() {
    final children = <Widget>[];

    if (_friends != null && _friends?.friendList?.isNotEmpty == true) {
      children.add(Expanded(child: _friendWidget()));
    } else if (_friends != null && _friends?.recommendList?.isNotEmpty == true) {
      children.addAll([
        _noFriendStatusWidget(),
        Expanded(child: _recommendWidget()),
      ]);
    } else {
      children.add(Expanded(child: EmptyWidget()));
    }

    children.add(SizedBox(height: 20.pt));

    return SizedBox(
      height: 340.pt,
      child: Column(
        children: children,
      ),
    );
  }

  Widget _noFriendStatusWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 59.5.pt,
          alignment: Alignment.center,
          child: Text(
            LocaleStrings.instance.noFriendsOnline,
            style: TextStyle(
              color: Color(0xFF8C867B),
              fontSize: 18.sp,
              fontWeight: fontWeightRegular,
            ),
          ),
        ),
        Container(
          color: Color(0xFFE9E2CE),
          width: 310.pt,
          height: 1.pt,
        ),
        Container(
          height: 24.pt,
          alignment: Alignment.bottomRight,
          padding: EdgeInsets.symmetric(horizontal: 16.pt),
          child: Text(
            LocaleStrings.instance.friendsYouMayKnow,
            textAlign: TextAlign.end,
            style: TextStyle(
              color: Color(0xFF33B81D),
              fontSize: 15.sp,
              fontWeight: fontWeightRegular,
            ),
          ),
        ),
      ],
    );
  }

  Widget _friendWidget() {
    return ListView.builder(
      itemBuilder: (BuildContext context, int index) {
        final item = _friends!.friendList![index];
        return GameInviteItemWidget(
          friend: item,
          isFriend: true,
          onInvite: () => _onInviteFriend(item),
        );
      },
      itemCount: _friends?.friendList?.length ?? 0,
    );
  }

  Widget _recommendWidget() {
    return ListView.builder(
      itemBuilder: (BuildContext context, int index) {
        final item = _friends!.recommendList![index];
        return GameInviteItemWidget(
          friend: item,
          isFriend: false,
          onAddFriend: () => _onAddFriend(item),
        );
      },
      itemCount: _friends?.recommendList?.length ?? 0,
    );
  }

  void _onAddFriend(Friend friend) async {
    final uid = friend.uid ?? '';
    if (uid.isEmpty) return;
    AddFriendHelper.sendFriendRequest(uid);
  }

  void _onInviteFriend(Friend friend) async {
    final uid = friend.uid ?? '';

    GameStatistics.reportGameInviteRoomUserClick(
      roomId: roomService.getCurrentRoomId(),
      gameId: widget.gameType.name,
      toUid: uid,
      toGender: friend.gender == 'male'
          ? 'm'
          : friend.gender == 'female'
              ? 'f'
              : '',
    );

    if (uid.isEmpty) return;
    showLoading();
    final resp = await newGameService.gameInvite(fUid: uid, roomId: widget.roomId, pos: widget.seatIndex);
    hideLoading();
    if (resp.isSuccess) {
      roomService.logInviteUser(uid);
      friend.hadInvite = true;
      friend.inviteSec = 60;
      if (mounted) {
        setState(() {});
      }
    } else {
      toast(resp.msg ?? LocaleStrings.instance.defaultError);
    }
  }

  Widget _top() {
    return Container(
      height: 45.pt,
      child: Row(
        children: [
          /// 对称
          SizedBox(width: 50.5.pt),
          Expanded(
            child: Text(
              LocaleStrings.instance.inviteFriends,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 18.sp,
                fontWeight: fontWeightBold,
              ),
            ),
          ),
          ScaleTapWidget(
              child: Container(
                width: 44.5.pt,
                height: 44.5.pt,
                padding: EdgeInsets.all(14.pt),
                child: FLImage.asset(Res.commonCloseGrey),
              ),
              onTap: () {
                routerUtil.pop(context: context);
              }),

          SizedBox(width: 6.pt),
        ],
      ),
    );
  }
}
