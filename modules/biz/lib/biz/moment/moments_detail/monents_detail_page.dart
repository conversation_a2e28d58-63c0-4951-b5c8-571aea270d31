import 'dart:convert';

import 'package:biz/biz/moment/moments_detail/bloc/moment_detail_bloc.dart';
import 'package:biz/biz/moment/widgets/moment_helper.dart';
import 'package:biz/biz/moment/widgets/moment_item.dart';
import 'package:biz/biz/sticker/sticker_search/sticker_search_panel.dart';
import 'package:biz/biz/sticker/stickers/widget/sticker.dart';
import 'package:biz/biz/sticker/stickers/widget/sticker_load_widget.dart';
import 'package:biz/biz/user/utils/user_info_helper.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/empty_widget.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/input_bar/input_bar.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/utils/time_util.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/moment_detail_statistic_handler.dart';
import 'package:service/common/statistics/handler/moment_more_statistic_handler.dart';
import 'package:service/common/statistics/moment_detail_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/sex.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/model/comment.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/moments/model/simple_user_info.dart';
import 'package:service/modules/sticker/model/sticker.dart';
import 'package:service/service.dart';
import 'package:biz/global/widgets/cache_image_widget.dart';
import 'package:biz/global/date_format_utils.dart';

@FRoute(url: R_MOMENTS_DETAIL, desc: "动态详情")
class MomentsDetailPage extends StatelessWidget {
  final _bloc = MomentsDetailBloc();
  double screenWidth = 0;
  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    _bloc.context = context;
    return LifecycleBlocBuilder<MomentsDetailBloc, MomentsDetailState>(
        bloc: _bloc,
        builder: (context, state) {
          return _page();
        });
  }

  Widget _page() {
    return Scaffold(
      appBar: _appBar(),
      body: _body(),
      resizeToAvoidBottomInset:
          false, //!_bloc.state.searchStickersPanelVisible,
    );
  }

  CommonAppBar _appBar() {
    return CommonAppBar(
      centerTitle: true,
      title: LocaleStrings.instance.moment,
    );
  }

  Widget _newTitle() {
    String? displayedName = _bloc.state.momentDetail?.userInfo?.displayedName;

    String followImage = Res.chatChatDetailFollowFamale;
    if (_bloc.state.momentDetail?.userInfo?.sexStr == 'male') {
      followImage = Res.chatChatDetailFollowMale;
    }

    bool showFollow = (accountService.getAccountInfo()?.uid !=
        _bloc.state.momentDetail?.userInfo?.uid) && _bloc.state.momentDetail?.momentActions?.follow != 1;

    return Visibility(
      visible: _bloc.state.appBarTitleVisible,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  displayedName ?? '',
                  maxLines: 1,
                  style: TextStyle(
                    color: R.color.textColor1,
                    fontSize: 21.sp,
                    fontWeight: FontWeightExt.heavy,
                  ),
                ),
              ),
              GlobalWidgets.spacingHorizontal(6.pt),
              Visibility(
                  visible: showFollow,
                  child: GestureDetector(
                    child: Container(
                      width: 27.pt,
                      height: 21.pt,
                      child: Image.asset(followImage),
                    ),
                    onTap: () {
                      MomentDetailStatistics.reportMomentDetailFollow(
                          type: "title",
                          from: _bloc.getArgument(P_STATISTIC_FROM),
                          postId: _bloc.state.momentDetail?.itemId,
                          autherId: _bloc.state.momentDetail?.userInfo?.uid,
                          autherGender: _bloc.state.momentDetail?.userInfo?.sexStr,
                          style: getMomentType(_bloc.state.momentDetail));
                      _bloc.add(CommentDetailFollowEvent());
                    } ,
                  )
              )
            ],
          )
        ],
      ),
    );
  }

  Widget _body() {
    return Column(
      children: [
        Expanded(
            child: Stack(
          children: [
            if (_bloc.state.momentDetail == null)
              Center(child: GlobalWidgets.listLoadingView())
            else
              GestureDetector(
                onPanDown: (value) {
                  _bloc.add(CommentCancelKeyboard());
                },
                child: SmartRefresher(
                    controller: _bloc.refreshController,
                    onLoading: () => _bloc.add(CommentLoadData()),
                    enablePullDown: false,
                    enablePullUp: _bloc.state.hasMore,
                    physics: ScrollPhysics(),
                    header: WaterDropHeader(),
                    child: _list()),
              ),
            Positioned(bottom: 0.pt, left: 20.pt, child: _stickerWidget()),
          ],
        )),
        Divider(color: Color(0xFFF0F0F0), height: 2.pt),
        if (!_bloc.state.hasBeDelete) _bottomBar()
      ],
    );
  }

  Widget _list() {
    if (_bloc.state.hasBeDelete) {
      return Center(
        child: EmptyWidget(
            logoAsset: Res.emptyEmptyFeed,
            detail: LocaleStrings.instance.momentNoExist),
      );
    }
    var rest = <Widget>[];
    rest.add(_detail());
    rest.add(Container(
      height: 1.pt,
      color: Color(0xFFF5F7F9),
      margin: EdgeInsets.symmetric(horizontal: 10.pt),
    ));
    rest.add(_label());
    rest.add(_commentsLst());

    return ListView(
      controller: _bloc.scrollController,
      shrinkWrap: true,
      children: rest,
    );
  }

  Widget _detail() {
    return _bloc.state.momentDetail == null
        ? Container()
        : ItemMoments(
            showFollow: true,
            showDetail: true,
            commentTab: _handleCommentTab,
            moment: _bloc.state.momentDetail!,
            momentItemStatisticCallback: _handleStatistics,
            itemMoreStatisticCallback: _handleMoreStatistics,
            itemType: StatisticPageFrom.momentDetail,
            fromWhere: _bloc.getArgument(P_STATISTIC_FROM),
            showRoomStatus: false,
          );
  }


  Widget _label() {
    return Padding(
      padding:
          EdgeInsets.only(left: 16.pt, right: 16.pt, top: 20.pt, bottom: 15.pt),
      child: Text(LocaleStrings.instance.comments,
          style: TextStyle(
              fontSize: 14.sp,
              color: R.color.textColor1)),
    );
  }

  Widget _bottomBar() {
    return Stack(
      children: [
        InputBar(
          fillColor: Color(0xFFF1F3F7),
          backgroundColor: Colors.white,
          showBottomBar: true,
          controller: _bloc.inputBarController,
          hintText: _bloc.state.refName?.replaceFirst(_bloc.prefix, "") ?? LocaleStrings.instance.pleaseKindlyComment,
          onSendTap: () => _bloc.add(CommentSend()),
          keyboardShowCallback: () {
            if (!_bloc.inputBarController.isActive) {
              _bloc.commentPlace = "comment_bar";
              _handleStatistics(
                  MomentStatisticsType.comment, _bloc.state.momentDetail);
            }
          },
          stickerSend: (sticker, from) =>
              _bloc.add(CommentStickerClick(sticker, from: from)),
        ),
        StickerSearchPanel(
            panelController: _bloc.searchPanelController,
            stickerSend: (sticker) {
              _bloc.add(CommentStickerClick(sticker));
            }),
      ],
    );
  }

  Widget _commentsLst() {
    if (_bloc.state.listComment == null) {
      return Padding(
        padding: EdgeInsets.only(top: 50.pt),
        child: SizedBox(
          width: 100.pt,
          height: 100.pt,
          child: GlobalWidgets.pageLoading(),
        ),
      );
    }

    if (_bloc.state.listComment?.isNotEmpty ?? false) {
      return Container(
        color: colorBackground,
        child: Column(
          children: _bloc.state.listComment!.map(_item).toList(),
        ),
      );
    } else {
      return EmptyWidget(
          logoAsset: Res.emptyEmptyUserPost,
          detail: LocaleStrings.instance.emptyComments);
    }
  }

  Widget _item(Comment comment) {
    return HfButton(
      width: double.infinity,
      alignment: Alignment.topLeft,
      onPressed: () {
        _bloc.add(CommentClick(comment));

        /// 动态详情-评论部分-点击评论item上报
        reportMomentDetailCommentItem(_bloc.state.momentDetail?.itemId,
            _bloc.getArgument(P_STATISTIC_FROM), comment);
      },
      onLongPressed: () => _bloc.add(CommentLongClick(comment)),
      padding: EdgeInsets.only(
        left: 3.pt,
      ),
      child: _comment(comment),
    );
  }

  Widget _comment(Comment comment) {
    final isMe = accountService.currentUid() == comment.userInfo?.uid;
    if (comment.isFold) {
      return GestureDetector(
        onTap: () {
          _bloc.add(CommentExpandEvent(comment: comment, isFold: false));
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.pt, vertical: 5.pt),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
             children: [
               Container(
                 width: 37.pt,
                 height: 1.pt,
                 color: R.color.textBlueColor3.withOpacity(0.3),
               ),
               SizedBox(width: 9.pt),
               Text(
                 LocaleStrings.instance.seeMoreReplies,
                 style: TextStyle(
                   color: R.color.textBlueColor3,
                   fontSize: 13.sp,
                   fontWeight: FontWeightExt.heavy
                 ),
               ),
               FLImage.asset(Res.commonArrowDown, color: R.color.textBlueColor3, width: 10.pt, height: 10.pt)
             ],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      child: Stack(
        children: [
          _avatar(comment.userInfo?.avatar ?? "", comment.userInfo?.uid ?? "",
              comment.commentId, comment),
          Padding(
            padding: EdgeInsets.only(left: 60.pt, right: 58.pt),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 10.pt),
                _nickName(comment.userInfo),
                SizedBox(height: 4.pt),
                _content(comment.content?.trim(), isMe),
                _extContent(comment.extContent),
                SizedBox(height: 6.pt),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _publishTime(comment.time),
                    SizedBox(width: 18.pt),
                    _reply(comment),
                    // _report(comment),
                    SizedBox(width: 15.pt),
                    _delete(comment)
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _avatar(String url, String uid, String? commentId, Comment comment) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        showUserInfo(uid: uid, from: StatisticPageFrom.momentDetail, matchType: StatisticPageFrom.momentDetail);
        reportMomentDetailCommentProfile(_bloc.state.momentDetail?.itemId,
            _bloc.getArgument(P_STATISTIC_FROM), comment);
      },
      child: UserAvatar(
        url: url,
        size: 40.pt,
        hasFrame: true,
        userId: uid,
        showRoomStatus: false,
      ),
    );
  }

  Widget _nickName(SimpleUserInfo? userInfo) {
    final row = Row(
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: 200.pt,
          ),
          child: Text(
            userInfo?.displayedName ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: R.color.textColor1,
              fontWeight: FontWeight.bold,
              fontSize: 16.sp,
            ),
          ),
        ),
        Visibility(
          child: SizedBox(
            width: 20.pt,
            height: 20.pt,
            child: FLImage.asset(Res.commonVoiceVerifyMark),
          ),
          visible: userInfo?.isVoiceAuth == true,
        ),
      ],
    );

    return row;
  }

  Widget _content(String? content, bool isMe) {
    var rest = <TextSpan>[];

    if (!isMe) {
      final txt = content?.maskPhoneNumbers;
      if (txt != null) content = txt;
    }
    ///发送动态表情会拼接[sticker]，需要切掉
    if (content?.isNotEmpty == true && content!.endsWith("[sticker]")) {
      content = content.substring(0, content.length - 9);
    }

    if ((content?.startsWith("@") ?? false) &&
        (content?.contains(_bloc.prefix) ?? false)) {
      rest.add(TextSpan(
        text: content!
            .substring(0, content.indexOf(_bloc.prefix))
            .replaceFirst(_bloc.prefix, ""),
        style: TextStyle(color: R.color.primaryColor, fontSize: 16.sp),
      ));
      content = " ${content.split(_bloc.prefix)[1]}";
    }
    if (content?.isNotEmpty ?? false) {
      rest.add(TextSpan(
        text: content,
        style: TextStyle(color: R.color.textColor3, fontSize: 16.sp),
      ));
    }
    if (rest.isEmpty) return SizedBox.shrink();

    return Text.rich(
      TextSpan(
        children: rest,
      ),
    );
  }

  Widget _extContent(String? extContent) {
    if (extContent == null) return SizedBox.shrink();
    var extJson = jsonDecode(extContent);
    var richBody = extJson["rich_body"];
    Sticker sticker = Sticker.fromJson(richBody);
    if (sticker.oriUrl == null) return SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.only(top: 12.pt),
      child: StickerLoadWidget(
        sticker,
        maxHeight: 130.pt,
        maxWidth: 130.pt,
        onTap: (sticker) => _bloc.add(StickerDetailClick(sticker)),
      ),
    );
  }

  Widget _stickerWidget() {
    var sticker = _bloc.state.sticker;
    if (sticker == null) return SizedBox.shrink();
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 9.pt, right: 9.pt),
          child: _stickerBox(sticker),
        ),
        Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              child: Container(
                width: 18.pt,
                height: 18.pt,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    color: Color(0x60121332)),
                child: FLImage.asset(
                  Res.commonDelete4,
                ),
              ),
              onTap: () => _bloc.add(
                DeleteCommentSticker(),
              ),
            )),
      ],
    );
  }

  Widget _stickerBox(Sticker sticker) {
    return Column(
      children: [
        Container(
          width: 80.pt,
          height: 80.pt,
          padding: EdgeInsets.all(5.pt),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.pt),
              boxShadow: [
                BoxShadow(
                  color: Color(0x1A893FFF),
                  blurRadius: 14.pt,
                  offset: Offset(0, 3.pt),
                ),
              ]),
          child: StickerImage(
            sticker,
            key: Key(sticker.id),
            width: 70.pt,
            height: 70.pt,
            showOri: true,
            cacheSticker: true,
            onTap: (_sticker) {
              _bloc.add(CommentStickerClick(sticker));
            },
          ),
        ),
        Container(
          width: 18.pt,
          height: 0,
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                  color: Colors.white, width: 10.pt, style: BorderStyle.solid),
              right: BorderSide(
                  color: Colors.transparent,
                  width: 9.pt,
                  style: BorderStyle.solid),
              left: BorderSide(
                  color: Colors.transparent,
                  width: 9.pt,
                  style: BorderStyle.solid),
            ),
          ),
        ),
      ],
    );
  }

  Widget _publishTime(int? time) {
    return Text(
      '${DateFormatUtils.dataFormatTime(time ?? 0)}',
      style: TextStyle(
        color: R.color.textBlueColor3,
        fontSize: 13.sp,
      ),
    );
  }

  Widget _reply(Comment comment) {
    return GestureDetector(
      child: Text(
        LocaleStrings.instance.reply,
        style: TextStyle(fontSize: 13.sp, color: R.color.primaryColor),
      ),
      onTap: () {
        _bloc.add(CommentClick(comment));

        /// 动态详情-评论部分-点击评论reply上报
        reportMomentDetailCommentReply(_bloc.state.momentDetail?.itemId,
            _bloc.getArgument(P_STATISTIC_FROM), comment);
      },
    );
  }

  Widget _delete(Comment comment) {
    if (_bloc.state.momentDetail?.userInfo?.uid !=
        accountService.getAccountInfo()?.uid) {
      return SizedBox();
    }
    return GestureDetector(
      child: Text(
        LocaleStrings.instance.delete,
        style: TextStyle(fontSize: 13.sp, color: Color(0xFF909BAD)),
      ),
      onTap: () {
        _bloc.add(DeleteComment(comment));
      },
    );
  }

  Widget _report(Comment comment) {
    bool isSelf = comment.userInfo?.uid ==
        accountService.getAccountInfo()?.uid;
    return GestureDetector(
      child: Visibility(
        visible: !isSelf,
        child: Padding(
          padding: EdgeInsets.only(left: 15.pt),
          child: Text(
            LocaleStrings.instance.report,
            style: TextStyle(fontSize: 13.sp, color: Color(0xFF909BAD)),
          ),
        ),
      ),
      onTap: () {
        _bloc.add(CommentReportClick(comment));
      },
    );
  }

  /// 详情相关事件点击上报
  void _handleStatistics(MomentStatisticsType type, Moment? moment,
      {String? act}) {
    if (moment != null) {
      handleDetailMomentStatistic(type, moment,
          _bloc.getArgument(P_STATISTIC_FROM), _bloc.commentPlace, act);
    }
  }

  /// 详情点击更多相关事件报告
  void _handleMoreStatistics(
      MoreStatisticsType type, Moment moment, ShareApps? shareType) {
    handleMomentMoreStatistic(
        type, moment, MomentStatisticsPage.detail, shareType);
  }

  /// TODO ALLEN isActive状态有问题
  void _handleCommentTab() {
    if (!_bloc.inputBarController.isActive) {
      _bloc.inputBarController.showKeyboard();
      _handleStatistics(MomentStatisticsType.comment, _bloc.state.momentDetail);
    }
  }
}
