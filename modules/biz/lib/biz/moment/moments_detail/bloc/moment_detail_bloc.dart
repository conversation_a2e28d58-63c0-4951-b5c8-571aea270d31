import 'dart:async';
import 'dart:developer';

import 'package:biz/biz/evaluate/handler/evaluate_handle.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/statistic/bloc/page_duration_statistic_mixin.dart';
import 'package:biz/global/widgets/bottom_drawer.dart';
import 'package:biz/global/widgets/input_bar/input_bar.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:service/router/page_visibility_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/moment_detail_statistic_handler.dart';
import 'package:service/common/statistics/moment_detail_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/const/prefix.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/global/statistics/request_statistic_handler.dart';
import 'package:service/modules/evaluate/const/enums.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/model/comment.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/moments/model/moment_center_guide.dart';
import 'package:service/modules/moments/model/moment_list_result.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/sticker/model/sticker.dart';
import 'package:service/service.dart';
import 'package:service/utils/connectivity_util.dart';
import 'package:service/utils/loading.dart';

part 'moment_detail_event.dart';

part 'moment_detail_state.dart';

class MomentsDetailBloc
    extends PageVisibilityBloc<MomentsDetailEvent, MomentsDetailState>
    with PageTimingMixin, PageDurationStatisticMixin {
  MomentsDetailBloc() : super(MomentsDetailState());

  BuildContext? context;

  String get getMomentId => getArgument<String>(P_MOMENT_ID) ?? "";

  final InputBarController inputBarController = InputBarController();

  ///搜索表情面板
  final BottomDrawerController searchPanelController = BottomDrawerController();

  final ScrollController scrollController = ScrollController();

  String commentPlace = "button";

  final prefix = AtPrefix;

  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initBloc() {
    super.initBloc();
    _initData();
    listenRxEvent<bool>(SearchPanelEvent.searchPanelChange, _searchPanelChange);
    listenRxEvent<String>(UserRelationEvent.block, _handlerBlockUser);
    listenRxEvent<ReportTemplate>(UserRelationEvent.report, _handlerReportUser);

    listenRxEvent<String>(MomentsStatusEvent.moveTheme, (value){
      _initData();
    });

    listenRxEvent<String>(MomentsStatusEvent.deleteMoment, (value){
        routerUtil.remove((route) => route == ModalRoute.of(context!));
    });

    listenRxEvent<String>(UserRelationEvent.follow, (value){
      if (state.momentDetail?.userInfo?.uid == value) {
        state.momentDetail?.momentActions?.follow = 1;
        emit(state.clone());
      }
    });

    addListenr();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();

    ///触发评分卡场景：退出动态详情时出现引导
    evaluateHandle.check(checkPostGuide: true, checkShareGuide: true);
  }

  void addListenr() {
    scrollController.addListener(() {
      bool visible = false;
      if (scrollController.offset > 56.pt) {
        visible = true;
      } else {
        visible = false;
      }

      if (state.appBarTitleVisible != visible) {
        emit(state.clone()..appBarTitleVisible = visible);
      }
    });
  }

  void _initData() async {
    MomentListResp? momentResp =
        await momentsService.getMomentDetailById(momentId: getMomentId);

    _initMoment(momentResp?.list?[0]);

    _loadComments();

    if (state.momentDetail != null) {
      reportMomentDetailImp(state.momentDetail!, getArgument(P_STATISTIC_FROM));
    }

    bool? commentFocus = getArgument<String>("commentFocus") == "true";

    if (commentFocus) {
      inputBarController.showKeyboard();
    }

    emit(state.clone()..centerGuide = momentResp?.centerGuide);

    WidgetsBinding.instance?.addPostFrameCallback((timeStamp) {
      /// 移除其他聊天详情页
      RouterUtil.removeExactMiddle(R_MOMENTS_DETAIL);
    });
  }

  Future<void> _initMoment(Moment? moment) async {
    if (moment == null) {
      toast(LocaleStrings.instance.checkNetWork);
      Moment? moment =
          await momentsService.getMomentFromCacheById(momentId: getMomentId);
      if (moment != null) {
        emit(state.clone()..momentDetail = moment);
      }
      return;
    }

    if (moment.itemId.isEmpty || moment.userInfo == null) {
      rxUtil.send(MomentsStatusEvent.deleteMoment, getMomentId);
      emit(state.clone()..hasBeDelete = true);
      return;
    }
    emit(state.clone()..momentDetail = moment);

    ///添加评分卡片条件记录：查看动态详情
    evaluateService.addRecord(EvaluateType.momentDetail);
    await postGuideService.addRecord(PostGuideType.momentDetail);
  }

  Future<List<Comment>> _getComments({String? startId, int? size}) async {
    return await momentsOperationService.getCommentList(
        momentId: getMomentId, startId: startId, size: size ?? state.size);
  }

  @override
  Stream<MomentsDetailState> mapEventToState(
    MomentsDetailEvent event,
  ) async* {
    switch (event.runtimeType) {
      case CommentLoadData:
        _loadComments();
        break;

      case CommentSend:
        yield await _send();
        break;

      case CommentClick:
        yield await _commentClick(event as CommentClick);
        break;

      case CommentLongClick:
        yield await _commentLongClick(event as CommentLongClick);
        break;
      case CommentCancelKeyboard:
        _cancelKeyboard();
        break;
      case CommentStickerClick:
        yield state.clone()..sticker = (event as CommentStickerClick).sticker;
        inputBarController.changeSendBtnStatus(true);
        if (searchPanelController.isOpen) {
          rxUtil.send(SearchPanelEvent.searchPanelChange, false);
        }
        break;
      case DeleteCommentSticker:
        yield state.clone()..sticker = null;
        inputBarController.changeSendBtnStatus(false);
        break;

      case StickerDetailClick:
        routerUtil.push(R_STICKER_DETAIL, params: {
          P_ID: (event as StickerDetailClick).sticker.id,
          P_STATISTIC_FROM: "momentpage"
        });
        break;
      case DeleteComment:
        showAlertDialog(
            content: LocaleStrings.instance.deleteCommentSure,
            cancelText: LocaleStrings.instance.no,
            cancelTextColor: R.color.primaryLightColor,
            confirmText: LocaleStrings.instance.yes,
            context: getContext(),
            onConfirm: () {
              _deleteComment((event as DeleteComment).comment);
            });
        break;
      case CommentDetailFollowEvent:
        _handleFollow(event as CommentDetailFollowEvent);
        break;
      case CommentReportClick:
        CommentReportClick mEvent = event as CommentReportClick;
        _reportComment(mEvent.comment);
        break;
      case CommentExpandEvent:
        CommentExpandEvent mEvent = event as CommentExpandEvent;
        _expandFoldComment(mEvent);
        break;
    }
  }

  Future<void> _loadComments({bool scrollToBottom = false}) async {
    String? commentId;
    if (state.listComment?.isNotEmpty ?? false) {
      commentId = state.listComment![state.listComment!.length - 1].commentId;
    }
    List<Comment> respListComment = await _getComments(startId: commentId);
    List<Comment> listComment = _filterCommentList(respListComment);

    MomentsDetailState newState = state.clone();
    refreshController.loadComplete();
    if (listComment.length < state.size) {
      refreshController.loadNoData();
      newState.hasMore = false;
    } else {
      refreshController.resetNoData();
      newState.hasMore = true;
    }
    if (newState.listComment == null) {
      newState.listComment = [];
    }

    emit(newState..listComment?.addAll(listComment));
    if (scrollToBottom) {
      Future.delayed(Duration(milliseconds: 100), () {
        if (scrollController.hasClients) {
          scrollController.animateTo(
            scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  Future<MomentsDetailState> _send() async {
    if (inputBarController.text.trim().isEmpty && state.sticker == null) {
      return state;
    }

    if (connectivityUtil.isNoNet()) {
      toast(LocaleStrings.instance.checkNetWork);
      return state;
    }

    ///是否被封禁
    final context = getContext();
    if (context != null && (await hasBeRestricted(context))) {
      return state;
    }

    showLoading();
    String content = inputBarController.text.trim();
    if (state.refName?.isNotEmpty ?? false) {
      content = state.refName! + content;
    }

    final report = MomentCommentRequestReport(
        momentDetail: state.momentDetail,
        from: getArgument(P_STATISTIC_FROM),
        reCommentId: state.reCommentId,
        refUid: state.refUid,
        commentType: state.sticker != null ? "sticker" : "text");

    final resp = await momentsOperationService.comment(
        momentId: getMomentId,
        content: content,
        replayUid: state.refUid,
        sticker: state.sticker);
    report.reqEndReport(resp);

    hideLoading();
    if (resp.isSuccess) {
      inputBarController.text = "";
      _loadComments(scrollToBottom: true);
      _cancelKeyboard();
      Moment? momentDetail = state.momentDetail
        ?..commentNum = (state.momentDetail?.commentNum ?? 1) + 1;
      emit(state.clone()
        ..momentDetail = momentDetail
        ..sticker = null);
    } else {
      toast(resp.msg ?? LocaleStrings.instance.failedToSend);
    }

    return state.clone();
  }

  Future<MomentsDetailState> _commentClick(CommentClick event) async {
    String text =
        "@${event.comment.userInfo?.nickname ?? event.comment.userInfo?.showUid ?? ""}$prefix";
    inputBarController.text = "";

    inputBarController.showKeyboard();

    return state.clone()
      ..reCommentId = event.comment.commentId
      ..refUid = event.comment.userInfo?.uid
      ..refName = text;
  }

  Future<MomentsDetailState> _commentLongClick(CommentLongClick event) async {
    FocusManager.instance.primaryFocus?.unfocus();
    showBottomSheetDialog(context: getContext()!, actions: [
      if (event.comment.userInfo?.uid != accountService.getAccountInfo()?.uid)
        SheetAction(
            title: LocaleStrings.instance.reply,
            onAction: () {
              add(CommentClick(event.comment));
              reportDetailCommentMoreReply(getMomentId, event.comment);
            }),
      SheetAction(
          title: LocaleStrings.instance.copy,
          onAction: () {
            _copy(event.comment.content);
            reportDetailCommentMoreCopy(getMomentId, event.comment);
          }),
      if (event.comment.userInfo?.uid != accountService.getAccountInfo()?.uid)
        SheetAction(
            title: LocaleStrings.instance.report,
            onAction: () {
              _reportComment(event.comment);
            }),
      if (event.comment.userInfo?.uid == accountService.getAccountInfo()?.uid)
        SheetAction(
            title: LocaleStrings.instance.delete,
            onAction: () async {
              _deleteComment(event.comment);
            }),
    ]);

    return state;
  }

  void _reportComment(Comment comment) async {
    routerUtil.push(R_SETTINGS_REPORT, params: {
      P_DATA: CommentReport(
          itemId: comment.momentId ?? "",
          targetUid: comment.userInfo?.uid ?? "",
          commentId: comment.commentId ?? ""),
      P_STATISTIC_FROM: R_MOMENTS_DETAIL
    });
    reportDetailCommentMoreReport(getMomentId, comment);
  }

  void _deleteComment(Comment comment) async {
    bool result = await momentsOperationService.deleteComment(
        momentId: getMomentId, commentId: comment.commentId!);
    toast(result ? "Success" : "Fail");
    if (result) {
      List<Comment>? list = state.listComment;
      if (list == null) return;
      list.remove(comment);
      Moment? momentDetail = state.momentDetail
        ?..commentNum = (state.momentDetail?.commentNum ?? 1) - 1;
      emit(state.clone()
        ..listComment = list
        ..momentDetail = momentDetail);
    }
  }

  void _copy(String? content) {
    if (content?.isEmpty ?? true) {
      log("moment comment copy fail:$content");
    }
    Clipboard.setData(
        ClipboardData(text: content?.replaceFirst(prefix, " ") ?? ""));
    toast("${LocaleStrings.instance.copySuccess}");
  }

  void _cancelKeyboard() {
    if (state.refUid?.isNotEmpty ?? false) {
      if (inputBarController.text.trim().isEmpty) {
        emit(state.clone()
          ..refUid = null
          ..refName = null
          ..reCommentId = null);
      }
    }
    inputBarController.hideBottomPanel();
    if (searchPanelController.isOpen) {
      rxUtil.send(SearchPanelEvent.searchPanelChange, false);
    }
  }

  _searchPanelChange(open) {
    if (isPageShow) {
      Future.delayed(Duration(milliseconds: open ? 0 : 500), () {
        emit(state.clone()..searchStickersPanelVisible = open);
      });
    }
  }

  @override
  String get pageDurationKey => "moment_detail_page";

  /// 动态详情时长上报
  @override
  void endTimingAndReport() async {
    stopTiming(key: pageDurationKey);
    MomentDetailStatistics.reportMomentDetailDuration(
      postId: state.momentDetail?.itemId,
      autherId: state.momentDetail?.userInfo?.uid,
      autherGender: state.momentDetail?.userInfo?.sexStr,
      style: getMomentType(state.momentDetail),
      from: getArgument(P_STATISTIC_FROM),
      themeId: state.momentDetail?.topicInfo?.id,
      duration: getOnTime(key: pageDurationKey),
    );
    removeTimer(key: pageDurationKey);
  }

  void _handlerBlockUser(String targetId) {
    if (state.momentDetail?.uid == targetId) {
      _removePage();
    } else {
      if (state.listComment?.isNotEmpty == true) {
        List<Comment> listComment = _filterCommentList(state.listComment!);
        emit(state.clone()..listComment = listComment);
      }
    }
  }

  void _handlerReportUser(ReportTemplate template) {
    if (state.momentDetail?.uid == template.targetUid &&
        (state.momentDetail?.itemId == template.itemId ||
            template is UserReport)) {
      _removePage();
    } else {
      if (state.listComment?.isNotEmpty == true) {
        List<Comment> listComment = _filterCommentList(state.listComment!);
        emit(state.clone()..listComment = listComment);
      }
    }
  }

  void _removePage() {
    final context = getContext();
    if (context == null) return;
    routerUtil.remove((route) => route == ModalRoute.of(context));
  }

  void _handleFollow(CommentDetailFollowEvent event) async {
    ///是否被封禁
    if ((await hasBeRestricted(context!))) {
      return;
    }

    String? uid = state.momentDetail?.userInfo?.uid;
    if (uid != null) {

      var resp = await socialService.oprFollow(
          targetUid: uid,
          isFollow: true,
          page: StatisticPageFrom.momentDetail);
      if (resp.isSuccess) {

        toast(LocaleStrings.instance.followSuccess);
        rxUtil.send(UserRelationEvent.follow, uid);

        state.momentDetail?.userInfo?.following = 1;
        emit(state.clone());
        return;
      }
      toast(resp.msg ?? "");
    }
  }

  List<Comment> _filterCommentList(List<Comment> list) {
    List<Comment> filterList = [];
    for (var element in list) {
      if (element.uid != null ) {
        if (socialService.containsBlackList(element.uid!)) {
          continue;
        }

        if (socialService.containsReportList(element.uid!)) {
          element.isFold = true;
        } else {
          element.isFold = false;
        }

        filterList.add(element);
      }
    }
    return filterList;
  }

  void _expandFoldComment(CommentExpandEvent event) {
    if (state.listComment?.isNotEmpty == true) {
      List<Comment> listComment = state.listComment!;
      for (var element in listComment) {
        if (element.commentId == event.comment.commentId) {
          element.isFold = event.isFold;
          break;
        }
      }
      emit(state.clone());
    }
  }
}
