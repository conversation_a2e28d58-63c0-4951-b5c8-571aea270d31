import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

import 'package:biz/biz/moment/moments_publish/bloc/moment_publish_bloc.dart';
import 'package:biz/biz/moment/widgets/moment_voice_play_widget.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/common/widgets/emoji/emoji_picker.dart';
import 'package:biz/common/widgets/operate_permission/operate_permission_dialog.dart';
import 'package:biz/global/date_format_utils.dart';
import 'package:biz/global/widgets/app_bar/app_bar.dart';
import 'package:biz/global/widgets/bottom_drawer.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/guide_bubble.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:extended_wrap/extended_wrap.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/post_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/widget/soft_keyboard_wrapper.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/service.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:biz/biz/moment/widgets/moment_create_theme.dart';
import 'package:get/get.dart';
import 'package:biz/biz/moment/widgets/moment_create_title.dart';
import 'package:service/modules/moments/model/theme_item.dart';
import 'package:service/modules/moments/model/publish_title_item.dart';
import 'package:biz/global/widgets/cache_image_widget.dart';
import 'package:biz/biz/moment/controller/moment_title_controller.dart';
import 'package:expandable_text/expandable_text.dart';
import 'package:image_picker/image_picker.dart';
import 'package:service/extension/xfile.dart';

import '../../../global/widgets/bar_body_divider.dart';
import '../widgets/moment_item_hashtag.dart';

@FRoute(url: R_MOMENTS_PUBLISH, desc: "编辑/发布动态")
class MomentsPublishPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _MomentsPublishPageState();
}

class _MomentsPublishPageState extends State<MomentsPublishPage> {
  final _bloc = MomentsPublishBloc();
  final BottomDrawerController _emojiController = BottomDrawerController();
  final ScrollController scrollController = ScrollController();
  double keyBoardHeight = 190.pt;

  var cacheMap = {};

  @override
  void dispose() {
    cacheMap.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    _bloc.setArguments(args);
    return LifecycleBlocBuilder<MomentsPublishBloc, MomentsPublishState>(
        bloc: _bloc,
        builder: (context, state) {
          return _page(context);
        });
  }

  Widget _page(BuildContext context) {
    return WillPopScope(
      child: Scaffold(
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
            // _emojiController.close();
          },
          child: Container(
            decoration: BoxDecoration(
                color: Colors.white
            ),
            child: Column(
              children: [
                _top(),
                Container(
                  width: 1.w,
                  height: 4.pt,
                  color: Color(0xFFF5F7F9),
                ),
                Expanded(child: _contentCard(context)),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () {
        return _checkEmptyEdit(context);
      },
    );
  }

  /// 顶部栏
  Widget _top() {
    bool postState = _bloc.checkCanPost();

    String postImgName = postState ? Res.feedPublishFeedPostEnable : Res.feedPublishFeedPostDisable;

    return Container(
      height: 96.pt,
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.only(bottom: 14.pt),
      child: Row(
        children: [
          GlobalWidgets.spacingHorizontal(10.pt),
          GestureDetector(
            child: Container(
              width: 25.pt,
              height: 25.pt,
              child: Image.asset(Res.commonBack),
            ),
            onTap: () {
              routerUtil.pop();
            },
          ),
          Spacer(),
          GestureDetector(
            onTap: () {
              if(!_bloc.checkCanPost()) return;
              _bloc.add(MomentsPublish());
            },
            child: Container(
              height: 30.pt,
              padding: EdgeInsets.symmetric(horizontal: 12.pt),
              decoration: _postButtonDecoration(postState),
              child: Row(
                children: [
                  Container(
                    width: 15.pt,
                    height: 15.pt,
                    child: Image.asset(postImgName),
                  ),
                  GlobalWidgets.spacingHorizontal(6.pt),
                  Text(
                    LocaleStrings.instance.post,
                    style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeightExt.medium,
                        color: postState ? Colors.white : Color(0xFF909090),
                        decoration: TextDecoration.none
                    ),
                  )
                ],
              ),
            ),
          ),
          GlobalWidgets.spacingHorizontal(20.pt)
        ],
      ),
    );
  }

  BoxDecoration _postButtonDecoration(bool state) {
    if (state) {
      return  BoxDecoration(
          borderRadius: BorderRadius.circular(15.pt),
          gradient: R.color.btnPrimaryLinearGradient
      );
    }
    return BoxDecoration(
      borderRadius: BorderRadius.circular(15.pt),
      color: _bloc.checkCanPost() ? Colors.white : Color(0xFFF5F7F9),
    );
  }

  /// 内容卡片
  Widget _contentCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24)
        )
      ),
      child: Column(
        children: [
          _bodyUserInfo(),
          _media(context),
          SizedBox(height: 18.pt),
          /// 标签
          Align(
            alignment: Alignment.centerLeft,
            child: Visibility(
                visible: _bloc.state.hashtags.isNotEmpty,
                child: _hashTagList(context)),
          ),
          Container(
            height: 1.pt,
            margin: EdgeInsets.symmetric(horizontal: 10.5.pt),
            color: Color(0xFFF5F7F9),
          ),
          SizedBox(height: 13.pt),
          _bottomBar()
        ],
      ),
    );
  }

  /// 标签
  Widget _hashTagList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 12.pt, right: 12.pt, bottom: 10.pt),
      child: MomentHashtags(
        hashtags: _bloc.state.hashtags,
        wrap: true,
        from: StatisticPageFrom.momentPublish,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.5.pt),
            border: Border.all(color: Color(0xFFF4F4F6))),
        showIcon: false,
        fontSize: 13.sp,
        maxLine: 12,
        padding: EdgeInsets.symmetric(horizontal: 12.pt),
        itemTap: (data) => _bloc.add(MomentsPublishDeleteHashtag(data)),
        extra: FLImage.asset(
          Res.commonCloseSearch,
          width: 15.pt,
        ),
      ),
    );
  }


  ///用户信息
  Widget _bodyUserInfo() {
     return Container(
       padding: EdgeInsets.only(top: 20.pt),
       child: Row(
         mainAxisSize: MainAxisSize.min,
         crossAxisAlignment: CrossAxisAlignment.start,
         children: [
           GlobalWidgets.spacingHorizontal(20.pt),
           UserAvatar(url: _bloc.state.user?.avatar ?? "", size: 50.pt),
           Expanded(child: _inputWidget())
         ],
       ),
     );
  }

  /// 选择主题按钮
  Widget _selectTheme() {
    if (_bloc.state.themeItem != null) {
      return GestureDetector(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 9.pt),
          height: 26.pt,
          constraints: BoxConstraints(
            maxWidth: 130.pt
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.pt),
            color: R.color.primaryColor.withOpacity(0.1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  _bloc.state.themeItem?.name ?? '',
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeightExt.medium,
                      color: R.color.primaryColor,
                      decoration: TextDecoration.none
                  ),
                ),
              ),
              GlobalWidgets.spacingHorizontal(5.pt),
              FLImage.asset(Res.feedPublishThemeSelectedIndicator, width: 18.pt, height: 18.pt)
            ],
          ),
        ),
        onTap: () {
          _clickSelectTheme('select_theme');
        }
      );
    }
     return _emptyTheme();
  }

  Widget _emptyTheme() {
    return GestureDetector(
      child: Container(
        height: 30.pt,
        padding: EdgeInsets.symmetric(horizontal: 9.pt),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.pt),
          color: Color(0xFFF6F7F9),
        ),
        child: Row(
          children: [
            Container(
              width: 21.pt,
              height: 21.pt,
              child: Image.asset(Res.feedPublishFeedAdd),
            ),
            GlobalWidgets.spacingHorizontal(6.pt),
            Text(
              LocaleStrings.instance.selectTheme,
              style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeightExt.medium,
                  color: R.color.textColor1,
                  decoration: TextDecoration.none
              ),
            )
          ],
        ),
      ),
      onTap: () {
        _clickSelectTheme('select_theme');
      },
    );
  }

  void _clickSelectTheme(String? from) async {
    MediaType type = MediaType.text;
    if (_bloc.state.imgList![0].isImage) {
      type = MediaType.image;
    }  else if (_bloc.state.imgList![0].isVideo) {
      type = MediaType.video;
    }
     showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return MomentCreateTheme(
            from: from,
              mediaType: type,
            customizedTheme: _bloc.customizedTheme,
              selectedTheme: _bloc.state.themeItem,
            enterBlock: (item){
              _bloc.add(MomentsAddThemeEvent(item: item));
            },
          );
        }
    );
  }

  Widget _inputWidget() {
    return Container(
      margin: EdgeInsets.only(left: 20.pt, right: 20.pt),
      height: 168.pt,
      child: _inputTxtFiled(),
    );
  }

  Widget _inputTxtFiled() {
    return SingleChildScrollView(
      controller: scrollController,
      child: Container(
        padding: EdgeInsets.only(top: 0),
        child: TextField(
          textAlignVertical: TextAlignVertical.top,
          textAlign: TextAlign.start,
          inputFormatters: [
            LengthLimitingTextInputFormatter(_bloc.state.maxLength),
          ],
          maxLines: null,
          focusNode: _bloc.focusNode,
          keyboardType: TextInputType.multiline,
          autofocus: true,
          cursorColor: R.color.primaryColor,
          controller: _bloc.inputController,
          style: TextStyle(
            color: R.color.textColor1,
            // backgroundColor: colorBackground,
            fontWeight: FontWeightExt.medium,
            fontSize: 15.sp,
          ),
          decoration: InputDecoration(
            border: InputBorder.none,
            counterText: "",
            contentPadding: EdgeInsets.symmetric(horizontal: 0.pt),
            hintStyle: TextStyle(color: Color(0xffB2B2B2), fontSize: 15.sp),
            hintText: _bloc.getPostHint(),
          ),
          onTap: _emojiController.close,
        ),
      ),
    );
  }
  
  Widget _bottomBar() {
    return Row(
      children: [
        SizedBox(width: 17.pt),
        GestureDetector(
          onTap: () {
            _bloc.add(MomentsPublishPickHashtag());
          },
          child: Container(
            width: 26.pt,
            height: 26.pt,
            decoration: BoxDecoration(
                color: Color(0xFFF5F7F9),
                borderRadius: BorderRadius.all(Radius.circular(50.pt))
            ),
            padding: EdgeInsets.all(7.pt),
            child: FLImage.asset(Res.commonMomentTag),
          ),
        ),
        Expanded(child: SizedBox.shrink()),
        _visitScope(),
        SizedBox(width: 11.5.pt)
      ],
    );
  }

  Widget _visitScope() {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 5.pt, horizontal: 9.pt),
        decoration: BoxDecoration(
          color: Color(0xFFF5F7F9),
          borderRadius: BorderRadius.all(Radius.circular(50.pt))
        ),
        child: Row(
          children: [
            Container(
              width: 16.pt,
              height: 16.pt,
              child: Image.asset(getPermissionIcon(_bloc.state.permission)),
            ),
            GlobalWidgets.spacingHorizontal(3.pt),
            Text(getPermissionText(_bloc.state.permission),
                style: TextStyle(
                    fontSize: 15.sp,
                    color: Color(0xFF909090),
                    fontWeight: FontWeightExt.medium,
                    decoration: TextDecoration.none
                )
            ),
            GlobalWidgets.spacingHorizontal(6.pt),

            FLImage.asset(
              Res.feedPublishPermissionArrow,
              width: 6.pt,
              height: 9.pt,
            )
          ],
        ),
      ),
      onTap: () {
        _bloc.add(PermissionChangeClick());
      },
    );
  }

   Widget _bottomBarItem(String imgName, String title, int index) {

    return GestureDetector(
      child: FittedBox(
        child: Container(
          alignment: Alignment.center,
          height: 32.pt,
          constraints: BoxConstraints(
            minWidth: 100.pt,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.pt),
            color: Color(0xFFF6F7F9)
          ),
          child: Row(
             children: [
               FLImage.asset(imgName, width: 18.pt, height: 18.pt),
               Text(
                 title,
                 style: TextStyle(
                   fontSize: 15.sp,
                   fontWeight: FontWeightExt.medium,
                   color: R.color.textColor1
                 ),
               )
             ],
          ),
        ),
      ),
      onTap: () async {
        if (index == 1) {
          final item = await showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) {
                return MomentCreateTitle();
              }
          );
          if (item != null) {
            _bloc.add(MomentsAddThemeTitleEvent(item: item as PublishTitleItem));
          }
        }
      },
    );
   }

   Widget _bottomTitleList() {
    return ListView.builder(
        padding: EdgeInsets.only(top: 18.pt),
        itemCount: _bloc.state.themeTitles?.length,
        itemBuilder: (context, index){
          PublishTitleItem item = _bloc.state.themeTitles![index];
          return  _bottomTitleItem(item);
        }
    );
  }

   /// 底部标签title
   Widget _bottomTitleItem(PublishTitleItem item) {
      return Container(
        height: 65.pt,
        margin: EdgeInsets.only(left: 20.pt, right: 20.pt, top: 5.pt, bottom: 5.pt),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7.pt),
          color: Color(0xFFF6F7F9)
        ),
        child: Row(
          children: [
            GlobalWidgets.spacingHorizontal(8.pt),
            CachedNetworkImage(
              imageUrl: item.icon ?? "",
              width: 39.pt,
              height: 39.pt,
            ),
            GlobalWidgets.spacingHorizontal(5.pt),
            Expanded(
                child: Text(
                  _themeTitleFormat(item),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeightExt.heavy,
                    decoration: TextDecoration.none,
                    color: R.color.textColor1,
                  ),
                )
            ),
            GlobalWidgets.spacingHorizontal(5.pt),
            GestureDetector(
              onTap: () {
                 _bloc.add(MomentsAddThemeTitleEvent(item:item, isAdd: false));
              },
              child: FLImage.asset(Res.feedThemeTitleDelete, width: 30.pt, height: 30.pt),
            ),
            GlobalWidgets.spacingHorizontal(12.pt),
          ],
        ),
      );
   }

   String _themeTitleFormat(PublishTitleItem? item) {
    if (item != null) {
      if (item.type == MomentTitleController.calendar) {
        return DateFormatUtils.formatDateFromSecYMDW(int.parse(item.desc!), DateSeparator.line);
      } else {
        return item.desc!;
      }
    }
    return '';
   }

  Widget _addWidget(BuildContext context) {
    return GestureDetector(
        onTap: () => _bloc.add(MomentsPublishPick("add_button")),
        child: Container(
            width: 74.pt,
            height: 74.pt,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFE3E4E5), width: 1.pt),
              borderRadius: BorderRadius.all(Radius.circular(8.pt)),
            ),
            child: FLImage.asset(
              Res.feedPublishFeedAdd,
              width: 24.pt,
              height: 24.pt,
              fit: BoxFit.fill,
            )));
  }

  Widget _itemImg(XFile asset) {
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        GestureDetector(
          onTap: () => _bloc.add(PublishPreview(asset)),
          child: _itemMedia(asset),
        ),
        _delete(asset)
      ],
    );
  }

  Widget _itemVoice(XFile asset) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16.pt,
        ),
        MomentVoicePlayWidget(
          voicePath: asset.path,
          seconds: asset.audioDuration,
        ),
        SizedBox(
          width: 6.pt,
        ),
        GestureDetector(
          child: Container(
            width: 18.pt,
            height: 18.pt,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100),
                color: Color(0x60121332)),
            child: FLImage.asset(
              Res.commonDelete4,
            ),
          ),
          onTap: () => _bloc.add(
            DeleteMomentVoice(),
          ),
        )
      ],
    );
  }

  Widget _itemMedia(XFile asset) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        color: colorBackground,
        child: Stack(
          alignment: Alignment.center,
          children: [
            cacheMap[asset.name] == null ?
            FutureBuilder<Uint8List?>(
              // Flutter 2.10.5
              // future: asset.thumbData,
              // Flutter 3.5.0
              initialData: cacheMap[asset.name],
              future: asset.isVideo
                  ? commonService.getVideoThumbnailData(asset.path)
                  : asset.readAsBytes(),
              builder: (_, snapshot) {
                if (snapshot.data != null) {
                  cacheMap.putIfAbsent(asset.name, () => Image.memory(
                    snapshot.data!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ));
                  return Image.memory(
                    snapshot.data!,
                    key: ValueKey(asset.name),
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                }
                return SizedBox();
              },
            ) : cacheMap[asset.name],
            if (asset.isVideo)
              FLImage.asset(
                Res.commonPlay2,
                width: 30.pt,
                height: 30.pt,
              )
          ],
        ),
      ),
    );
  }

  /// 图片/视频/录音
  Widget _media(BuildContext context) {
    var medias;
    List<Widget> restImages = [];

    if (_bloc.state.onlyRecord) {
      return _itemVoice(_bloc.state.imgList![0]);
    }

    ///图片列表
    if (_bloc.state.imgList?.isNotEmpty ?? false) {
      restImages.addAll(_bloc.state.imgList!.map(_itemImg).toList());
    }

    if ((_bloc.state.imgList?.isEmpty ?? true) ||
        (_bloc.state.imgList?.length ?? 0) < _bloc.state.maxImgNum &&
            ((_bloc.state.imgList?.isNotEmpty ?? false) &&
                _bloc.state.imgList?[0].isImage == true)) {
      ///添加按钮
      restImages.add(_addWidget(context));
    }
    medias = GridView(
      padding: EdgeInsets.symmetric(horizontal: 20.pt, vertical: 0.pt),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 15.pt,
          crossAxisSpacing: 15.pt,
          childAspectRatio: 1),
      shrinkWrap: true,
      children: restImages,
    );

    ///视频

    return Container(
      width: MediaQuery.of(context).size.width,
      // height: 78.pt,
      margin: EdgeInsets.only(left: 0.pt, right: 0.pt),
      child: medias,
    );
  }

  /// 删除
  Widget _delete(XFile asset) {
    return Positioned(
      top: -6.pt,
      right: -6.pt,
      child: HfButton(
        onPressed: () {
          _bloc.add(MomentsPublishDelete(asset));
          cacheMap.remove(asset.name);
        },
        padding: EdgeInsets.only(left: 10.pt, bottom: 10.pt),
        child: ClipOval(
          child: Container(
            padding: EdgeInsets.all(1.pt),
            color: R.color.textColor1.withOpacity(0.6),
            child: FLImage.asset(Res.commonClose,
                width: 21.pt, height: 21.pt, color: Colors.white),
          ),
        ),
      ),
    );
  }

  /// 表情栏
  Widget _emojiDrawer() {
    return BottomDrawer(
      controller: _emojiController,
      widgetBuilder: (_) =>  SizedBox(
        height: keyBoardHeight,
        child: EmojiPicker(
          padding: EdgeInsets.symmetric(horizontal: 1.pt),
          verticalSpacing: 18.pt,
          horizontalSpacing: 15.pt,
          onEmojiSelected: _onEmojiSelected,
        ),
      ),
    );
  }

  Widget _itemPick(String imgRes, bool enable, VoidCallback callback) {
    return HfButton(
      onPressed: callback,
      width: 62.pt,
      height: double.infinity,
      child: Opacity(
        opacity: enable ? 1 : 0.3,
        child: FLImage.asset(imgRes, width: 32.pt, height: 32.pt),
      ),
    );
  }

  Future<bool> _checkEmptyEdit(BuildContext context) {
    if (!_bloc.checkCanPost() && _bloc.state.themeItem == null && _bloc.state.themeTitles == null) {
      _bloc.exitStatus = "exit";
      return Future.value(true);
    }

    showAlertDialog(
        context: context,
        content: LocaleStrings.instance.discardPublishEdit,
        styleContent: TextStyle(
          color: Color(0xff202020),
          fontSize: 14.sp,
          fontWeight: FontWeight.w600
        ),
        styleCancel: TextStyle(
            color: Color(0xFF825CFF),
            fontSize: 16.sp,
            fontWeight: FontWeightExt.heavy
        ),
        styleConfirm: TextStyle(
            color: Color(0xFFB2B2B2),
            fontSize: 16.sp,
            fontWeight: FontWeightExt.heavy
        ),
        cancelText: LocaleStrings.instance.keepEditing,
        confirmText: LocaleStrings.instance.discardPost,
        btnDirection: TextDirection.rtl,
        onConfirm: () async {
          _bloc.exitStatus = "exit";
          FocusScope.of(context).requestFocus(FocusNode());
          Navigator.of(context).pop();
          _reportDiscardAct(false);
        },
        onCancel: () async {
          _reportDiscardAct(true);
        });

    return Future.value(false);
  }

  void _onEmojiSelected(String emoji) {
    final text = '${_bloc.inputController.text}$emoji';
    _bloc.inputController.text = text;

    scrollController.animateTo(
      scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 10),
      curve: Curves.easeOut,
    );
  }

  void _reportDiscardAct(bool isCancel) {
    PostStatistics.reportPostDetailDiscardPopoutAct(
        from: _bloc.getArgument(P_STATISTIC_FROM),
        act: isCancel ? "keep" : "discard");
  }
}
