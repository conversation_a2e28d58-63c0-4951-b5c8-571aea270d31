import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:biz/biz/moment/controller/moment_theme_controller.dart';
import 'package:biz/biz/moment/controller/moment_title_controller.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/common/widgets/operate_permission/operate_permission_dialog.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/date_format_utils.dart';
import 'package:biz/global/statistic/bloc/page_duration_statistic_mixin.dart';
import 'package:biz/global/widgets/record_voice_dialog.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:biz/utils/res_picker/photo_video_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_picker/image_picker.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/handler/chat_detail_statistic_handler.dart';
import 'package:service/common/statistics/post_statistics.g.dart';
import 'package:service/common/statistics/voicepost_statistics.g.dart';
import 'package:service/extension/xfile.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/global/permission/permission_util.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/common/model/audio_x_file.dart';
import 'package:service/modules/hashtag/handler/pick_hashtag_manager.dart';
import 'package:service/modules/hashtag/model/hashtag.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/handler/publish_moment_manager.dart';
import 'package:service/modules/moments/model/publish_moment.dart';
import 'package:service/modules/moments/model/publish_title_item.dart';
import 'package:service/modules/moments/model/theme_item.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/modules/voice_verify/const/enums.dart';
import 'package:service/service.dart';
import 'package:service/utils/connectivity_util.dart';
import 'package:service/utils/loading.dart';
import 'package:service/utils/size_convertor.dart';

part 'moment_publish_event.dart';

part 'moment_publish_state.dart';

class MomentsPublishBloc extends PageVisibilityBloc<MomentsPublishEvent, MomentsPublishState>
    with PageTimingMixin, PageDurationStatisticMixin {
  MomentsPublishBloc() : super(MomentsPublishState());

  String? momentId = "";

  TextEditingController inputController = TextEditingController();

  String? exitStatus = "edit";

  final String momentPublishGuideKey = "momentPublishGuideKey";

  bool isPreview = false;

  bool _canSend = false;

  String? get getHashtagId => getArgument<String>(P_HASHTAG_ID);

  String? get from => getArgument<String>(P_STATISTIC_FROM);

  FocusNode focusNode = FocusNode();

  ThemeItem? customizedTheme;

  String? get themeId => getArgument<String>(P_THEME_ID);

  String? get themeName => getArgument<String>(P_THEME_NAME);

  String? get hashtagId => getArgument<String>(P_HASHTAG_ID);

  @override
  void initBloc() {
    super.initBloc();

    _chekNumberOfLines();
    _initData();
    _initGuid();
    PostStatistics.reportPostDetailImp(from: getArgument(P_STATISTIC_FROM));

    postGuideService.show(type: PostGuideWidgetType.bubble);

    if (themeId != null && themeName != null) {
      ThemeItem item = ThemeItem(id: themeId, name: themeName, section: MomentThemeController.MyTheme);
      emit(state.clone(themeItem: item));
    }
  }

  void _initData() async {
    _getPublishId();

    userService.getCurrentUserInfo().then((value) {
      if (value != null) {
        emit(state.clone(user: value));
      }
    });

    final hashtagId = this.hashtagId ?? "";
    if (hashtagId.isNotEmpty) {
      var hashtag = await hashtagService.getHashtagById(id: hashtagId);
      if (hashtag != null) {
        HashtagPickManager.instance().put(hashtag);
        emit(state.clone(hashtags: [hashtag]));
      }
    }

    ShareImageToMomentModel? imageFromRouter = getArgument<ShareImageToMomentModel>(P_IMAGE_FILE);
    if (imageFromRouter != null) {
      final context = getContext();
      if (context == null) {
        return;
      }

      bool hasPermission = true;
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      // android10 以下才判断权限
      if (androidInfo.version.sdkInt < 29) {
        hasPermission = await checkPermission(type: PermissionType.photo);
      }
      if (hasPermission) {
        final dirPath = await getTemporaryDirectory();
        final fileName = '${DateTime.now().millisecondsSinceEpoch}.png';
        final file = File(join(dirPath.path, 'screenshot', fileName));
        if (!file.existsSync()) {
          await file.create(recursive: true);
        }
        final photoFile = await file.writeAsBytes(imageFromRouter.imageData, flush: true);

        final assetEntity = XFile.fromData(
          imageFromRouter.imageData,
          mimeType: "image/png",
          path: file.path,
        );
        emit(state.clone(imgList: [assetEntity]));

        final result = await ImageGallerySaver.saveFile(
          photoFile.path,
          isReturnPathOfIOS: true,
          name: fileName,
        );
        if (result is Map && result["isSuccess"] == true) {
        } else {
          toast(LocaleStrings.instance.saveFailed);
        }
      } else {
        showGoSettingDialog(context, type: PermissionType.storage);
      }
    }
  }

  void _initGuid() {
    bool hadShowGuid = globalSp.getBool(momentPublishGuideKey) ?? false;
    if (!hadShowGuid && from == "guide") {
      globalSp.setBool(momentPublishGuideKey, true);
      emit(state.clone(showPictureGuid: true));
    }
  }

  void _nextGuid() {
    if (state.showPictureGuid) {
      emit(state.clone(showPictureGuid: false));
    }
  }

  Future<bool> _getPublishId() async {
    var result = await momentsService.getPublishId();
    momentId = result?.itemId;
    return result != null;
  }

  @override
  Stream<MomentsPublishState> mapEventToState(
    MomentsPublishEvent event,
  ) async* {
    switch (event.runtimeType) {
      case MomentsPublish:
        yield await _publish();
        break;

      case MomentsPublishPick:
        _pick(event as MomentsPublishPick);
        _nextGuid();
        break;

      case MomentsPublishDelete:
        yield await _delete(event as MomentsPublishDelete);
        break;

      case PublishPreview:
        _preview(event as PublishPreview);
        break;

      case RecordVoiceClick:
        _showRecordVoice();
        break;

      case DeleteMomentVoice:
        _showSureDeleteDialog();
        break;

      case MomentPublishPageClick:
        _nextGuid();
        break;
      case PermissionChangeClick:
        _handlePermissionChange();
        break;
      case MomentsAddThemeEvent:
        _updateTheme(event as MomentsAddThemeEvent);
        break;
      case MomentsAddThemeTitleEvent:
        _updateTitle(event as MomentsAddThemeTitleEvent);
        break;
      case MomentsPublishPickHashtag:
        yield _pickHashtag();
        break;
      case MomentsPublishDeleteHashtag:
        yield _deleteHashtag(event as MomentsPublishDeleteHashtag);
        break;
    }
  }

  /// 选择hashtag
  MomentsPublishState _pickHashtag() {
    HashtagPickManager.instance().setPoshHashtag(state.hashtags);
    routerUtil.push(R_HASHTAG_SEARCH).then((value) {
      var list = <Hashtag>[];
      list.addAll(HashtagPickManager.instance().getPoshHashtag() ?? []);
      emit(state.clone(hashtags: list));
      var context = getContext();
      if (context == null) return;
      FocusScope.of(context).requestFocus(focusNode);
    });
    return state;
  }

  MomentsPublishState _deleteHashtag(MomentsPublishDeleteHashtag event) {
    return state.clone(hashtags: state.hashtags.where((value) => value.id != event.tag.id).toList());
  }

  Future<MomentsPublishState> _publish() async {
    if (!checkCanPost()) {
      return state;
    }

    ///是否被封禁
    if ((await hasBeRestricted(getContext()!))) {
      return state;
    }
    if (PublishMomentManager.instance().publishingMoment == true ||
        PublishMomentManager.instance().lastPublishFailure == true) {
      _showPublishAlert(getContext()!);
      return state;
    }

    if (momentId?.isEmpty ?? true) {
      if (connectivityUtil.isNoNet()) {
        toast(LocaleStrings.instance.checkNetWork);
        return state;
      }
      showLoading();
      bool result = await _getPublishId();

      hideLoading();
      if (!result) {
        return state;
      }
    }

    String? themeId;

    String? themeName;
    String? themeDesc;
    bool? isRecommendTheme;
    if (state.themeItem != null) {
      if (state.themeItem?.section == MomentThemeController.NewTheme || state.themeItem?.isCustomizedTheme == true) {
        themeName = state.themeItem!.name!;
        themeDesc = state.themeItem!.desc;
        isRecommendTheme = state.themeItem?.isRecommend;
      } else {
        themeId = state.themeItem?.id;
      }
    }
    String? titleIds;
    if (state.themeTitles?.isNotEmpty == true) {
      List<String> ids = [];
      for (var element in state.themeTitles!) {
        ids.add(element.id!);
      }
      titleIds = ids.join(',');
    }

    String? labelType;
    String? labelValue;
    if (state.themeTitles?.isNotEmpty == true) {
      if (state.themeTitles![0].type == MomentTitleController.calendar) {
        labelType = '4';
      } else {
        labelType = '1';
      }
      labelValue = state.themeTitles![0].desc;
    }

    PublishMomentManager.instance().publishMomentHandle(
      PublishMoment(
        files: state.imgList ?? [],
        momentId: momentId!,
        text: inputController.text.trim(),
        visible: state.permission,
        from: getArgument(P_STATISTIC_FROM) ?? "",
        topicId: themeId,
        topicName: themeName,
        topicDesc: themeDesc,
        labelType: labelType,
        labelIconId: titleIds,
        labelValue: labelValue,
        isRecommend: isRecommendTheme ?? false,
        dateLabelValue:
            labelType == '4' ? DateFormatUtils.formatDateFromSecYMDW(int.parse(labelValue!), DateSeparator.line) : null,
        hashtags: state.hashtags.map((e) => e.id).toList().join(","),
      ),
    );

    exitStatus = "succ";

    final context = getContext();
    if (context != null) {
      Navigator.of(context).pop();
    }

    return state;
  }

  void _showPublishAlert(BuildContext context) {
    showAlertDialog(
      context: context,
      content: LocaleStrings.instance.onlyPublishOneMoment,
      confirmText: LocaleStrings.instance.yes,
      cancelText: LocaleStrings.instance.cancel,
      onConfirm: () async {
        PublishMomentManager.instance().cancelPublish();
        _publish();
      },
    );
  }

  MomentsPublishState _delete(MomentsPublishDelete event) {
    PostStatistics.reportPostDetailMediaRemove(
        from: getArgument(P_STATISTIC_FROM), content: event.asset.isVideo ? "video" : "image");
    var list = state.imgList ?? [];
    list.remove(event.asset);
    return state.clone(imgList: list);
  }

  void _preview(PublishPreview event) async {
    _onPreviewLoaded();
    final index = state.imgList?.indexOf(event.asset) ?? 0;
    routerUtil.push(R_XFILE_PREVIEW_PAGE, params: {P_DATA: state.imgList, P_INDEX: index});
  }

  void _pick(MomentsPublishPick event) async {
    /// 视频只能选择一个
    if (state.containVideo()) {
      return;
    }

    /// 应对关闭Dialog时Flutter焦点系统自动寻找下一个组件获取焦点的特性
    emit(state.clone(canRequestFocus: false));

    var context = getContext();
    showBottomSheetDialog(
        context: context,
        actions: [
          SheetAction(
            title: LocaleStrings.instance.camera,
            titleStyle: TextStyle(fontSize: 17.sp, fontWeight: FontWeightExt.medium, color: Color(0xFF202020)),
            onAction: () => _userCamera(event),
          ),
          SheetAction(
            title: LocaleStrings.instance.album,
            titleStyle: TextStyle(fontSize: 17.sp, fontWeight: FontWeightExt.medium, color: Color(0xFF202020)),
            onAction: () =>
                state.imgList?.isNotEmpty == true ? _pickPhoto(event, MediaType.image) : _pickPicOrVideo(event),
          ),
        ],
        onCancel: () {
          emit(state.clone(canRequestFocus: true));
        });
  }

  void _pickPicOrVideo(event) {
    showBottomSheetDialog(
        context: getContext(),
        actions: [
          SheetAction(
            title: LocaleStrings.instance.chatInputFucPhoto,
            titleStyle: TextStyle(fontSize: 17.sp, fontWeight: FontWeightExt.medium, color: Color(0xFF202020)),
            onAction: () => _pickPhoto(event, MediaType.image),
          ),
          SheetAction(
            title: LocaleStrings.instance.video,
            titleStyle: TextStyle(fontSize: 17.sp, fontWeight: FontWeightExt.medium, color: Color(0xFF202020)),
            onAction: () => _pickPhoto(event, MediaType.video),
          ),
        ],
        onCancel: () {
          emit(state.clone(canRequestFocus: true));
        });
  }

  void _userCamera(MomentsPublishPick event) async {
    reportMediaAdd(event.type);

    var context = getContext();
    if (context != null) {
      isPreview = false;
      var asset = await pickPhotoVideoFromCamera(context);
      if (asset == null) return;

      final imgList = state.imgList ?? [];
      imgList.add(asset);
      _pickResult(imgList);
    }
  }

  void _pickPhoto(MomentsPublishPick event, MediaType type) async {
    reportMediaAdd(event.type);

    var context = getContext();
    if (context != null) {
      isPreview = false;
      final count = state.maxImgNum - (state.imgList?.length ?? 0);
      final list = <XFile>[];
      if (event.type != MediaType.video) {
        list.addAll(state.imgList ?? []);
      }
      showLoading();
      final List<XFile>? assets = await pickPhotoVideo(context,
          type: type, maxSize: count, authPage: ReportAuthPage.momentPost, isGifSupport: false);
      hideLoading();
      list.addAll(assets ?? []);
      if (list == null) return;

      _pickResult(list);

      emit(state.clone(canRequestFocus: true));
    }
  }

  void _pickResult(List<XFile> list) {
    emit(state.clone(imgList: list));
    _reportMediaEnd(list);
    var context = getContext();
    if (context == null) return;
    FocusScope.of(context).requestFocus(focusNode);
  }

  void _chekNumberOfLines() {
    inputController.addListener(() {
      if (inputController.text.length > state.maxLength) {
        String text = inputController.text.characters.skipLast(1).string;
        inputController.text = text;
        inputController.selection = TextSelection.fromPosition(TextPosition(offset: inputController.text.length));
      }
      if (_canSend != checkCanPost()) {
        emit(state.clone());
      }
    });
  }

  /// 是否可发布
  bool checkCanPost() {
    _canSend = (inputController.text.trim().isNotEmpty) || (state.imgList?.isNotEmpty ?? false);
    return _canSend;
  }

  void _showRecordVoice() {
    VoicepostStatistics.reportPostDetailVoice();
    var context = getContext();
    if (context == null) return;
    showRecordVoiceDialog(context, deleteClick: (int voiceSeconds) {
      VoicepostStatistics.reportPostDetailVoiceRecordDelete(duration: voiceSeconds.toString());
      // emit(state.clone(imgList: []));
    }, sureClick: (String path, int seconds) {
      List<XFile> voices = [];
      // voices.add(XFile(id: path, typeInt: 3, width: 0, height: 0, duration: seconds, relativePath: path));
      voices.add(AudioXFile(path, duration: seconds));
      emit(state.clone(imgList: voices));
      FocusScope.of(context).requestFocus(focusNode);
      VoicepostStatistics.reportPostDetailVoiceRecordConfirm(duration: seconds.toString());
    }, recordClick: () {
      VoicepostStatistics.reportPostDetailVoiceRecord();
    }, onReportRecord: (seconds, type, status) {
      VoicepostStatistics.reportPostDetailVoiceRecordEnd(duration: seconds, type: type, status: status);
    }, onStartPlay: () {
      VoicepostStatistics.reportPostDetailVoiceRecordPlay();
    });
  }

  void _handlePermissionChange() {
    final context = getContext();
    if (context == null) return;
    showOperatePermissionDialog(context, state.permission, from: "moment", changePermission: (permission) {
      emit(state.clone(permission: permission));
    });
  }

  @override
  String get pageDurationKey => "moment_publish_page";

  @override
  void endTimingAndReport() {
    super.endTimingAndReport();
    stopTiming(key: pageDurationKey);
    PostStatistics.reportPostDetailDuration(
        from: getArgument(P_STATISTIC_FROM), duration: getOnTime(key: pageDurationKey), status: exitStatus);
    removeTimer(key: pageDurationKey);
  }

  @override
  void onBackground() {
    exitStatus = "edit";
    super.onBackground();
  }

  @override
  void dispose() {
    PostStatistics.reportPostDetailExit(
        from: getArgument(P_STATISTIC_FROM),
        content: (inputController.text.trim().isEmpty && (state.imgList?.isEmpty ?? true)) ? "0" : "1");
    super.dispose();
    HashtagPickManager.instance().initPublishPost();
  }

  void reportMediaAdd(String type) {
    PostStatistics.reportPostDetailMediaAdd(from: getArgument(P_STATISTIC_FROM));
  }

  /// 相册媒体选择页面展示上报
  void _onPickerLoaded() {
    PostStatistics.reportPostMediaSelectImp();
  }

  /// 用户相册媒体文件选择预览页面展示上报
  void _onPreviewLoaded() {
    PostStatistics.reportPostMediaSelectPreviewImp();
  }

  /// 用户相册媒体预览选择发送
  void _onPreviewSend(bool preview) {
    isPreview = preview;
  }

  void _reportMediaEnd(List<XFile> result) async {
    if (result.isEmpty) return;
    int totalLength = await ChatDetailReporter.getMediaSize(result);
    final size = sizeConvertor.byteToMB(totalLength).toStringAsFixed(2);
    final type = (result[0].isVideo) ? "video" : "image";
    final numImage = type == "video" ? "" : result.length.toString();
    if (isPreview) {
      PostStatistics.reportPostMediaSelectPreviewDone(type: type, size: size, numImage: numImage);
    } else {
      PostStatistics.reportPostMediaSelectDone(type: type, size: size, numImage: numImage);
    }
  }

  void _showSureDeleteDialog() {
    VoicepostStatistics.reportPostDetailVoiceDelete(duration: state.imgList![0].audioDuration.toString());
    var context = getContext();
    showAlertDialog(
      context: context,
      content: LocaleStrings.instance.deleteSureContent,
      confirmText: LocaleStrings.instance.yes,
      onConfirm: () {
        emit(state.clone(imgList: []));
        var context = getContext();
        if (context == null) return;
        FocusScope.of(context).requestFocus(focusNode);
      },
    );
  }

  String getPostHint() {
    return from == "guide" ? LocaleStrings.instance.guidePublishInput : LocaleStrings.instance.tipPublishInput;
  }

  void _updateTitle(MomentsAddThemeTitleEvent event) {
    List<PublishTitleItem> items = [];

    if (event.isAdd) {
      items.add(event.item);
    } else {
      if (state.themeTitles?.isNotEmpty == true) {
        items.addAll(state.themeTitles!);
      }

      for (var element in items) {
        if (element.id == event.item.id) {
          items.remove(element);
          break;
        }
      }
    }

    emit(state.clone(themeTitles: items));
  }

  void _updateTheme(MomentsAddThemeEvent event) {
    if (event.item?.isCustomizedTheme == true) {
      customizedTheme = event.item;
    }

    emit(state.clone(themeItem: event.item));
  }
}
