import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/biz/moment/handler/moment_praise_handler.dart';
import 'package:biz/biz/moment/widgets/moment_helper.dart';
import 'package:biz/biz/moment/widgets/moment_item_header.dart';
import 'package:biz/biz/moment/widgets/moment_item_medias.dart';
import 'package:biz/biz/moment/widgets/moment_item_text.dart';
import 'package:biz/biz/user/utils/user_info_helper.dart';
import 'package:biz/common/widgets/operate_permission/operate_permission_dialog.dart';
import 'package:biz/global/date_format_utils.dart';
import 'package:biz/global/num_format_utils.dart';
import 'package:biz/global/widgets/frame/frame_list_item.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/utils/time_util.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/home_statistics.g.dart';
import 'package:service/common/statistics/moment_detail_statistics.g.dart';
import 'package:service/common/statistics/moment_preview_statistics.g.dart';
import 'package:service/common/statistics/moment_statistics.g.dart';
import 'package:service/common/statistics/voicepost_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/home/<USER>/home_wink_source_handler.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/voice_verify/const/enums.dart';
import 'package:service/utils/loading.dart';

import 'moment_item_hashtag.dart';
import 'moment_item_mixin.dart';

class FrameMomentItemWidget extends StatelessWidget {
  final ItemMoments child;
  final int? index;

  FrameMomentItemWidget(this.child, {this.index});

  @override
  Widget build(BuildContext context) {
    return FrameListItem(
      index: index,
      placeHolder: FrameListItem.momentPlaceHolder,
      itemBuilder: (BuildContext context) => child,
    );
  }
}

///广场动态条目
class ItemMoments extends StatefulWidget {
  final bool? showFollow;
  final bool showDetail;
  final bool? showPermission;
  final Moment moment;
  final bool inMediasDetail;
  final bool? selfAction;
  final VoidCallback? commentTab;
  final Function(String)? commentSuccessCallback;
  final Function(bool)? expandCallback;
  final Function()? textClickCallback;
  final int? index;

  /// 是否展示头像框
  final bool showFrame;

  /// 处理动态item的按钮打点回调
  final MomentItemStatisticCallback? momentItemStatisticCallback;

  /// 处理动态item more页面的按钮打点回调
  final Function(MoreStatisticsType, Moment, ShareApps?)?
      itemMoreStatisticCallback;

  /// 区分是哪里的item
  /// reco_list动态推荐列表/follow_list动态关注列表/inbox动态信箱/userpage用户主页/push
  /// 推送/share_chat端内聊天分享/share_sns端外动态链接分享/topic_detail hashtag列表
  final String? itemType;

  /// 哪个页面的item
  final String? fromWhere;

  final Function(RenderBox, int)? layoutCallBack;
  final Function(double)? deleteCallback;

  final Function? onStartPlay;
  final bool reportFollowOrChat;

  /// 展示在房状态
  final bool showRoomStatus;

  final bool expandTxt;

  ItemMoments(
      {Key? key,
      this.showFollow,
      this.showDetail = false,
      this.showPermission = false,
      this.inMediasDetail = false,
      this.commentSuccessCallback,
      this.selfAction = false,
      this.commentTab,
      required this.moment,
      this.index,
      this.expandCallback,
      this.momentItemStatisticCallback,
      this.itemMoreStatisticCallback,
      this.itemType,
      this.fromWhere,
      this.textClickCallback,
      this.layoutCallBack,
      this.deleteCallback,
      this.onStartPlay,
      this.reportFollowOrChat = false,
      this.showRoomStatus = false,
      this.expandTxt = false,
      this.showFrame = true})
      : super(key: key);

  @override
  _ItemMomentsState createState() => _ItemMomentsState();
}

class _ItemMomentsState extends State<ItemMoments> with MomentItemMixin {
  int _commentNum = 0;
  bool _hasFollow = true;
  int _permission = VerifyPermission.PUBLIC;
  String _momentId = "";
  late Moment moment;

  StreamSubscription<Moment>? _momentSub;
  StreamSubscription<String>? _commentSub;
  StreamSubscription<String>? _deleteCommentSub;
  StreamSubscription<String>? _deleteMomentSub;
  StreamSubscription<Moment>? _updateMomentVisibleSub;

  double _itemHeight = 0;
  final GlobalKey _key = GlobalKey();

  @override
  void dispose() {
    _momentSub?.cancel();
    _commentSub?.cancel();
    _deleteCommentSub?.cancel();
    _deleteMomentSub?.cancel();
    _updateMomentVisibleSub?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    moment = widget.moment;
    _commentNum = moment.commentNum ?? 0;
    _permission = moment.visible ?? VerifyPermission.PUBLIC;
    _momentId = moment.itemId;
    if (widget.showFollow ?? false) {
      _hasFollow = (accountService.getAccountInfo()?.uid !=
              widget.moment.userInfo?.uid) &&
          widget.moment.momentActions?.follow == 1;
    }
    _initListener();
    // if (widget.moment.roomEventInfo?.id?.isNotEmpty == true) {
    //   EventStatistics.reportEventImp(
    //       eventId: widget.moment.roomEventInfo?.id, page: "moment");
    // }
    super.initState();
  }

  @override
  void didUpdateWidget(ItemMoments oldWidget) {
    if (!widget.inMediasDetail &&
        !oldWidget.inMediasDetail &&
        widget.moment != oldWidget.moment) {
      _updateMomentItem(widget.moment, false);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    ///头像/昵称/在线描述/关注
    var header = _itemHeader(context);

    ///文本
    var contentStr = _itemText();

    ///媒体列表
    var contentMedia = _itemMedias(context);

    /// 来自分享相关
    /// 活动分享
    // var contentEvent = MomentRoomEvent(eventInfo: widget.moment.roomEventInfo);

    /// 家族分享
    // var contentFamily = FamilyShareContentWidget(
    //     familyInfo: widget.moment.familyInfo, from: "moment");

    /// 点赞/评论/更多
    var contentOpr = _itemOpr(context);

    // 监听widget渲染完成
    WidgetsBinding.instance?.addPostFrameCallback((duration) {
      var box = _key.currentContext?.findRenderObject();
      if (box is RenderBox) {
        widget.layoutCallBack?.call(box, (widget.index ?? 0));
        if (box.hasSize) {
          _itemHeight = box.size.height;
        }
      }
    });

    EdgeInsets containerPadding =  EdgeInsets.only(top: 0.pt, left: 6.pt, right: 6.pt);

    if (widget.showDetail) {
      containerPadding = EdgeInsets.only(top: 0.pt, left: 0.pt, right: 0.pt);
    }

    return GestureDetector(
      onTap: _itemTap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        padding: containerPadding,
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.zero,
              key: _key,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        left: widget.showDetail ? 6.pt : (widget.showFrame ? 0 : 12.pt),
                        right: widget.showDetail ? 6.pt : 0,
                        top: 4.pt),
                    child: header,
                  ),
                  _commmentDetailContent(contentStr, contentMedia, contentOpr)
                ],
              ),
            ),
            Positioned(
              top: 25.pt,
              right: 10.pt,
              child: Row(
                children: [
                  _chatToWidget(),
                  _joinWidget(),
                ],
              ),
            )
                  
          ],
        ),
      ),
    );
  }

  Widget _commmentDetailContent(Widget contentStr, Widget contentMedia, Widget contentOpr) {
    if (widget.showDetail) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GlobalWidgets.spacingVertical(8.pt),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 18.pt),
            child: contentStr,
          ),
          contentMedia,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 18.pt),
            child: _labelInfoWidget(),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 18.pt),
            child: contentOpr,
          ),
        ],
      );
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.pt),
      child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            widget.showFrame ? GlobalWidgets.spacingVertical(0.pt) : GlobalWidgets.spacingVertical(10.pt),
            contentStr,
            contentMedia,
            _labelInfoWidget(),
            contentOpr,
          ]
      ),
    );
  }

  Widget _itemHeader(BuildContext context) {
    if (widget.moment.uid?.isEmpty ?? true) return SizedBox();
    return MomentHeader(
      itemType: widget.itemType,
      userInfo: widget.moment.userInfo,
      moment: widget.moment,
      showDetail: widget.showDetail,
      fromWhere: widget.fromWhere,
      showFrame: widget.showFrame,
      time: widget.moment.publishTime,
      isFollowing: _hasFollow,
      showPermission: widget.showPermission,
      permission: _permission,
      showFollow: widget.showFollow ?? false,
      textColor: widget.inMediasDetail ? Colors.white : R.color.textColor1,
      showRoomStatus: widget.showRoomStatus,
      timeColor: widget.inMediasDetail
          ? colorSubtitle.withOpacity(0.6)
          : colorSubtitle,
      callback: () {
        if (widget.showDetail) return;
        showMomentDetail(
            momentId: widget.moment.itemId, itemType: widget.fromWhere);
      },
      avatarTab: () {
        if (widget.selfAction ?? false) return;

        /// 点击头像打点
        widget.momentItemStatisticCallback
            ?.call(MomentStatisticsType.profile, widget.moment);
        showUserInfo(
            uid: widget.moment.userInfo?.uid ?? "",
            from: widget.itemType,
          source: _setupItemSource()
        );
      },
      onFollowStatusChanged: (value) {
        if (mounted) {
          _detailFollowHandler();
          setState(() {
            _hasFollow = value;
          });
        }
      },
      followOrChatTapCallback: (value) {
        if (widget.reportFollowOrChat) {
        }
      },
    );
  }

  Widget _itemText() {
    if (widget.moment.text?.isEmpty ?? true) return Container();
    return GestureDetector(
        onTap: () {
          if (widget.inMediasDetail) {
            widget.textClickCallback?.call();
          }
          if (widget.showDetail || widget.inMediasDetail) return;
          widget.momentItemStatisticCallback?.call(
              MomentStatisticsType.contentClick, widget.moment,
              act: "text");
          showMomentDetail(
              momentId: widget.moment.itemId, itemType: widget.fromWhere);
        },
        child: Container(
          child: MomentsStr(widget.moment.text ?? "",
              showAll: widget.showDetail || widget.expandTxt,
              textColor:
                  widget.inMediasDetail ? Colors.white : R.color.textColor1,
              expandText: LocaleStrings.instance.more,
              expandTextColor: R.color.primaryDeepColor,
              collapseText: LocaleStrings.instance.less,
              maxLines: widget.showDetail && !widget.inMediasDetail ? 100 : 5,
              expandCallback: widget.expandCallback,
              linkTapStaticCallback: _reportLinkTap,
              canLongPressCopy: true),
        ));
  }

  Widget _itemMedias(BuildContext context) {
    return (widget.moment.medias?.isNotEmpty ?? false) && !widget.inMediasDetail
        ? Padding(
            padding: widget.showDetail ? EdgeInsets.only(top: 8.pt) : EdgeInsets.only(top: 12.pt),
            child: MomentMedias(
              moment: widget.moment,
              mediasDetail: widget.showDetail,
              showDetail: widget.showDetail,
              callback: (index) async {
                ///通话中不允许查看moment video
                if (widget.moment.medias?[index].type == "video" &&
                    (await callService.checkIsInCall())) {
                  toast(LocaleStrings.instance.inCallNotice);
                  return;
                }

                widget.momentItemStatisticCallback?.call(
                    MomentStatisticsType.contentClick, widget.moment,
                    act: widget.moment.medias?[index].type == "video"
                        ? "video"
                        : "image");
                showMomentMedias(context,
                    momentId: widget.moment.itemId,
                    index: index,
                    from: widget.fromWhere ?? "",
                    moment: widget.moment);
              },
              onStartPlay: widget.onStartPlay ?? _reportStartPlay,
            ),
          )
        : Container();
  }

  /// 标签
  Widget _labelInfoWidget() {
    if (widget.moment.hashtags?.isNotEmpty ?? false) {
      return Padding(
        padding:
        EdgeInsets.only(top: 16.pt, bottom: 8.pt),
        child: MomentHashtags(
            wrap: true,
            hashtags: widget.moment.hashtags!,
            from: widget.fromWhere,
            showIcon: false,
            maxLine: 12,
            borderColor: Color(0xFFF5F7F9),
            padding:
            EdgeInsets.symmetric(horizontal: 9.pt)),
      );
    } else {
      return Container();
    }
  }

  Widget _itemOpr(BuildContext context) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 16.pt),
        child: Row(
          children: [
            _btnPraise(context),
            _btnComment(context),
            Spacer(),
            widget.inMediasDetail
                ? Expanded(child: buildCommentInput())
                : _btnMore(context),
          ],
        ));
  }

  Widget _btnPraise(BuildContext context) {
    return _oprBtn(
        context: context,
        inMediasDetail: widget.inMediasDetail,
        icon: hasPraise
            ? _icon(Res.feedIconHasPraise)
            : _icon(Res.feedIconUnPraise,
                color: widget.inMediasDetail ? Colors.white : null),
        text: NumFormatUtils.numFormat(praiseNum),
        callback: () async {
          MomentPraiseHandler().handlePraise(
              context: context,
              hasPraise: hasPraise,
              moment: moment,
              statisticCallback: widget.momentItemStatisticCallback);
        });
  }

  Widget _btnComment(BuildContext context) {
    return _oprBtn(
      context: context,
      inMediasDetail: widget.inMediasDetail,
      icon: _icon(Res.feedIconComment,
          color: widget.inMediasDetail ? Colors.white : null),
      text: NumFormatUtils.numFormat(_commentNum),
      callback: () {
        widget.commentTab?.call();
        if (widget.inMediasDetail) {
          FocusScope.of(context).requestFocus(focusNode);
        }

        if (widget.showDetail || widget.inMediasDetail) {
          return;
        } else {
          widget.momentItemStatisticCallback
              ?.call(MomentStatisticsType.comment, widget.moment);
        }
        showMomentDetail(
            momentId: widget.moment.itemId,
            itemType: widget.fromWhere,
            commentFocus: true);
      },
    );
  }

  Widget _btnMore(BuildContext context) {


    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        widget.momentItemStatisticCallback
            ?.call(MomentStatisticsType.more, widget.moment);
        showMomentMoreDialog(context,
            showPermission: widget.showPermission,
            moment: widget.moment,
            selfAction: widget.selfAction ?? false,
            itemMoreStatisticCallback: widget.itemMoreStatisticCallback,
            clickPermission: () {
              _showOperatePermissionDialog(context);
            }
        );
      },
      child: Container(
        width: 26.pt,
        height: 26.pt,
        decoration: BoxDecoration(
          color: Color(0xFFF5F7F9),
          borderRadius: BorderRadius.circular(13.pt),
        ),
        child: FLImage.asset(Res.feedMore, width: 26.pt, height: 26.pt),
      ),
    );
  }

  /// 操作按钮
  Widget _oprBtn({
    required BuildContext context,
    required Widget icon,
    required String text,
    required VoidCallback callback,
    required bool inMediasDetail,
  }) {
    bool ltr = Directionality.of(context) == TextDirection.ltr;

    return GestureDetector(
      child: Container(
        padding: EdgeInsets.only(right: 16.pt),
        child: Row(
          children: [
            _oprImgWithBackground(icon: icon),
            GlobalWidgets.spacingHorizontal(5.pt),
            Text(
                text,
                style: TextStyle(
                    fontSize: 14.sp,
                    color: inMediasDetail ? Colors.white : R.color.textBlueColor3
                )
            )
          ],
        ),
      ),
      onTap: callback,
    );
  }

  Widget _oprImgWithBackground({required Widget icon}) {
    return ClipOval(
      child: Container(
        width: 26.pt,
        height: 26.pt,
        color: Color(0xFFF5F7F9),
        child: icon,
      ),
    );
  }

  Widget _time() {
    return Text(
      '${TimeUtil.getTimeFromNow((widget.moment.publishTime ?? 0) * 1000)}',
      style: TextStyle(
        color: R.color.textBlueColor3,
        fontSize: 13.sp,
      ),
    );
  }

  Widget _icon(String icon, {Color? color}) {
    return color != null
        ? FLImage.asset(icon, width: 21.pt, height: 21.pt, color: color)
        : FLImage.asset(icon, width: 21.pt, height: 21.pt);
  }

  /// 聊天按钮
  Widget _chatToWidget() {
    if (StatisticPageFrom.userPage == widget.itemType || ((moment.roomId?.isNotEmpty ?? false) || moment.userInfo?.uid == accountService.currentUid())) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      key: ValueKey(moment.itemId),
      child: Container(
        height: 24.pt,
        padding: EdgeInsets.only(left: 6.pt, right: 11.pt),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(13.pt),
          border: Border.all(color: R.color.primaryColor, width: 1.pt),
        ),
        child: Row(
          children: [ 
            FLImage.asset(Res.profileIconChat, width: 16.pt, fit: BoxFit.cover, color: R.color.primaryColor),
            SizedBox(width: 5.pt),
            Text(
                LocaleStrings.instance.chat,
                style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeightExt.heavy,
                    color: R.color.primaryColor
                )
            )
          ],
        ),
      ),
      onTap: () {

        MomentStatistics.reportTabMomentItemTalk(
          postId: moment?.itemId,
          autherId: moment?.userInfo?.uid,
          autherGender: moment?.userInfo?.sexStr,
        );

        HomeStatistics.reportHomeUserStateClick(
            autherAge: '${moment.userInfo?.age ?? 0}',
            autherGender: moment.userInfo?.sex.name,
            autherId: moment.userInfo?.uid,
            reasonState: moment.userInfo?.reasonState,
            lastActiveTime: '${moment.publishTime ?? 0}',
            clickType: ClickType.chat,
            from: StatisticPageFrom.forYou
        );

        jumpChatRefMoment(
            targetUid: moment.userInfo?.uid ?? '',
            moment: moment!,
          from: widget.itemType,
          statisticMatchType: widget.itemType,
        );

      },
    );
  }

  Widget _joinWidget() {
  if (StatisticPageFrom.userPage == widget.itemType || (moment.roomId?.isEmpty ?? true)) {
    return const SizedBox.shrink();
  }

  return GestureDetector(
    key: ValueKey('${moment.itemId}_join'),
    child: Container(
      height: 26.pt,
      padding: EdgeInsets.symmetric(horizontal: 8.pt),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(13.pt),
        color: const Color(0xFF825CFF),
      ),
      child: Row(
        children: [
          FLImage.asset(Res.homeIconRoomJoin, width: 15.pt, height: 15.pt),
          SizedBox(width: 5.pt),
          Text(
            LocaleStrings.instance.join,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeightExt.heavy,
              color: Colors.white,
            ),
          ),
        ],
      ),
    ),
    onTap: () {
      HomeStatistics.reportHomeUserStateClick(
          autherAge: '${moment.userInfo?.age ?? 0}',
          autherGender: moment.userInfo?.sex.name,
          autherId: moment.userInfo?.uid,
          reasonState: moment.userInfo?.reasonState,
          lastActiveTime: '${moment.publishTime ?? 0}',
          clickType: ClickType.join,
          from: StatisticPageFrom.forYou
      );
      routerUtil.push(
        R_LIVE_ROME_JOIN,
        params: {
          P_ID: widget.moment.roomId,
          P_STATISTIC_FROM: StatisticPageFrom.homeForYou,
        },
      );
    },
  );
}

  void _itemTap() {
    if (widget.inMediasDetail) {
      widget.textClickCallback?.call();
    }
    if (widget.showDetail || widget.inMediasDetail) return;
    widget.momentItemStatisticCallback
        ?.call(MomentStatisticsType.contentClick, widget.moment, act: "text");
    showMomentDetail(
        momentId: widget.moment.itemId, itemType: widget.fromWhere);
  }

  /// 显示权限操作弹窗
  void _showOperatePermissionDialog(BuildContext context) {
    showOperatePermissionDialog(context, _permission, from: "bio",
        changePermission: (permission) {
      _changePermission(permission);
    });
  }

  void _changePermission(int permission) async {
    if (_permission != permission && _momentId.isNotEmpty) {
      showLoading();
      final response = await momentsService.modify(
          momentId: _momentId, visible: permission.toString());
      hideLoading();
      if (response.isSuccess) {
        widget.moment.visible = permission;
        rxUtil.send(MomentsStatusEvent.updateVisible, widget.moment);
      } else {
        toast(response.msg ?? LocaleStrings.instance.checkNetWork);
      }
    }
  }

  void _initListener() {
    _momentSub = rxUtil
        .observer<Moment>(MomentsStatusEvent.updateMoment)
        .listen((value) {
      if (widget.moment.itemId == value.itemId) {
        _updateMomentItem(value, true);
      }
    });
    rxUtil.send(MomentsStatusEvent.updateMoment, moment);

    _commentSub =
        rxUtil.observer<String>(MomentsStatusEvent.addComment).listen((value) {
      if (widget.moment.itemId == value) {
        _updateCommentNum(true);
      }
    });

    _deleteCommentSub = rxUtil
        .observer<String>(MomentsStatusEvent.deleteComment)
        .listen((value) {
      if (widget.moment.itemId == value) {
        _updateCommentNum(false);
      }
    });

    _deleteMomentSub = rxUtil
        .observer<String>(MomentsStatusEvent.deleteMoment)
        .listen((value) {
      if (widget.moment.itemId == value) {
        widget.deleteCallback?.call(_itemHeight);
      }
    });

    _updateMomentVisibleSub = rxUtil
        .observer<Moment>(MomentsStatusEvent.updateVisible)
        .listen((value) {
      if (widget.moment.itemId == value.itemId) {
        _updateVisible(value.visible);
      }
    });
  }

  void _updateVisible(int? visible) {
    if (mounted && visible != null) {
      setState(() {
        _permission = visible;
      });
    }
  }

  void _updateCommentNum(bool addComment) {
    if (mounted) {
      setState(() {
        _commentNum += (addComment ? 1 : (_commentNum > 0 ? -1 : 0));
      });
    }
  }

  /// 详情页/预览页 关注上报
  void _detailFollowHandler() {
    if (widget.inMediasDetail) {
      MomentPreviewStatistics.reportMomentPreviewFollow(
          from: widget.fromWhere,
          postId: widget.moment.itemId,
          autherId: widget.moment.userInfo?.uid,
          autherGender: widget.moment.userInfo?.sexStr,
          style: getMomentType(widget.moment));
    } else {
      MomentDetailStatistics.reportMomentDetailFollow(
          from: widget.fromWhere,
          postId: widget.moment.itemId,
          autherId: widget.moment.userInfo?.uid,
          autherGender: widget.moment.userInfo?.sexStr,
          style: getMomentType(widget.moment));
    }
  }

  void _updateMomentItem(Moment newMoment, bool _pageUpdate) {
    moment = newMoment;
    praiseNum = moment.praiseNum ?? 0;
    hasPraise = moment.momentActions?.hasPraise == 1;
    _commentNum = moment.commentNum ?? 0;
    _permission = moment.visible ?? VerifyPermission.PUBLIC;
    _momentId = moment.itemId;
    if (_pageUpdate && mounted) setState(() {});
  }

  void _reportStartPlay() {
    VoicepostStatistics.reportMomentItemVoicePlay(
        postId: widget.moment.itemId,
        autherId: widget.moment.uid,
        autherGender: widget.moment.userInfo?.sexStr,
        from: widget.fromWhere);
  }

  void _reportLinkTap(String url) {
    MomentStatistics.reportMomentLinkClick(
        postId: widget.moment.itemId,
        url: url,
        toUid: widget.moment.userInfo?.uid,
        toGender: widget.moment.userInfo?.sexStr);
  }

  @override
  OnSendCommentCallback? get onSendCommentCallback =>
      widget.commentSuccessCallback;

  @override
  VoidCallback? get onTapCommentInput => widget.commentTab;

  String? _setupItemSource() {
    switch (widget.itemType) {
      case MomentListTypeStatistic.recommend:
      case MomentListTypeStatistic.following:
      case StatisticPageFrom.momentDetail:
        return ChatSourceType.moment;
    }
    return null;
  }
}
