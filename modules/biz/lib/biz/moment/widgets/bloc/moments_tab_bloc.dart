import 'dart:async';

import 'package:biz/biz.dart';
import 'package:biz/global/bloc/page_visibility_bloc.dart';
import 'package:biz/global/statistic/moment_imp_report_mixin.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/statistics/handler/hashtag_statistic_handler.dart';
import 'package:service/common/statistics/moment_statistics.g.dart';
import 'package:service/common/statistics/operation_statistics.g.dart';
import 'package:service/modules/account/const/events.dart';
import 'package:service/modules/ad/const/const.dart';
import 'package:service/modules/ad/model/banner_resp.dart';
import 'package:service/modules/family/const/events.dart';
import 'package:service/modules/hashtag/model/hashtag.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/social/const/events.dart';
import 'package:service/modules/social/model/report_template.dart';
import 'package:service/modules/user/model/user_state_model.dart';
import 'package:service/service.dart';
import 'package:service/utils/connectivity_util.dart';
import 'package:biz/biz/moment/controller/follow_user_state_list_controller.dart';

part 'moments_tab_event.dart';

part 'moments_tab_state.dart';

class MomentsTabBloc extends PageVisibilityBloc<MomentsTabEvent, MomentsTabState> with ItemImpReport {
  MomentsTabBloc(this.type, this.scrollController, {this.hashtagId}) : super(MomentsTabState());

  MomentsType type;

  /// 话题
  final String? hashtagId;
  late RefreshController refreshController;

  final ScrollController scrollController;

  FollowUserStateListController followListController = FollowUserStateListController();

  String maleLastIdx = '0';
  String femaleLastIdx = '0';

  @override
  void initBloc() {
    super.initBloc();

    rxUtil.observer<int>(UserRegQAEvent.onComplete).listen((_) => _reloadData(null));
    if (accountService.getAccountInfo() == null) {
      rxUtil.observer<String>(AccountEvent.loginSuccess).listen(_reloadData);
      return;
    }
    _reloadData(null);
  }

  void _reloadData(String? uid) {
    _initData();
    loadBannerData();

    /// 动态推荐列表展示
    MomentStatistics.reportTabMomentImp(type: statisticMomentListType(type));
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
    Log.i("MomentsTabBloc", 'dispose');
  }

  @override
  void onResume() {
    super.onResume();
  }

  void _initData() async {
    var info = await userService.getCurrentUserInfo();
    if (info?.uid.isEmpty ?? true) {
      return;
    }

    if (hashtagId?.isEmpty ?? true) {
      Log.i("MomentsTabBloc _initData", "getMomentsFromCache");

      if (type != MomentsType.family && type != MomentsType.newest) {
        try {
          ///先读取缓存
          List<Moment> list = await momentsService.getMomentsFromCache(type: type);

          refreshController.loadNoData();

          Log.i("MomentsTabBloc _initData", "getMomentsFromCache list ${list.length}");

          if (list.isNotEmpty) {
            emit(state.clone(
                hasFamily: info?.hasFamily,
                listMoment: list,
                hasMore: type == MomentsType.recommend));
          }
        } on Exception catch (e) {
          add(MomentsEventRefresh());
        }
      }
    }

    Log.i("MomentsTabBloc _initData", "MomentsEventRefresh");
    add(MomentsEventRefresh());

    if (type == MomentsType.recommend ||
        type == MomentsType.following ||
        type == MomentsType.newest ||
        type == MomentsType.latest ||
        type == MomentsType.family) {
      listenRxEvent<Moment?>(MomentPublish.success, _publishSuccess);
    }

    listenRxEvent<String>(MomentsStatusEvent.deleteMoment, _handlerDeleteMoment);

    listenRxEvent<MomentsType?>(MomentsStatusEvent.refresh, (value) {
      if (value == type) {
        add(MomentsEventRefresh());
      }
    });
    listenRxEvent<String>(UserRelationEvent.block, (String targetId) {
      add(BlockUserEvent(targetId));
    });

    listenRxEvent<ReportTemplate>(UserRelationEvent.report, (ReportTemplate reportTemplate) {
      if (reportTemplate is UserReport) {
        add(BlockUserEvent(reportTemplate.targetUid));
      }
    });

    listenRxEvent<UserStateModel>(FollowUserListEvent.updateCurrentUserState, (UserStateModel stateModel) {
      followListController.updateCurrentUserState(stateModel);
    });

    /// 家族相关信息刷新
    listenRxEvent<String>(FamilyEvent.joinFamily, (value) {
      if (type == MomentsType.family) {
        add(MomentsEventRefresh());
      }
    });
    listenRxEvent<String>(FamilyEvent.applyJoinSuccessfully, (value) {
      if (type == MomentsType.family) {
        add(MomentsEventRefresh());
      }
    });
    listenRxEvent<String>(FamilyEvent.quitFamily, (value) {
      if (type == MomentsType.family) {
        add(MomentsEventRefresh());
      }
    });
  }

  Future<void> loadBannerData() async {
    final list = await adService.getBannerList(type: BannerType.moment);
    emit(state.clone(bannerList: list));

    state.bannerList?.forEach(_reportBannerImp);
  }

  void _reportBannerImp(BannerItem? item) {
    OperationStatistics.reportBannerMomentImp(
      content: '${item?.id}',
      from: "for you",
      activityName: item?.extraInfo.activityId,
    );
  }

  @override
  Stream<MomentsTabState> mapEventToState(
    MomentsTabEvent event,
  ) async* {
    switch (event.runtimeType) {
      case BlockUserEvent:
        _handlerBlockUser(event as BlockUserEvent);
        break;
      case MomentsEventRefresh:
        followListController.followUserStates();
        _initHashtags();
        _initFamilyInfo();
        yield await _loadData(event);
        rxUtil.send(MainTabEvent.tabRefresh, true);
        break;
      case MomentsEventLoadMore:
        yield await _loadData(event);
        break;
      case MomentsEventTryAgain:
        emit(state.clone(hasError: false, lastTime: null, lastItemId: null));
        yield await _loadData(MomentsEventRefresh());
        break;
      case ExpandTxtEvent:
        yield _expandTxt(event as ExpandTxtEvent);
        break;
    }
  }

  void _initFamilyInfo() async {
    if (type == MomentsType.family) {
      var info = await userService.getCurrentUserInfo();
      emit(state.clone(hasFamily: info?.hasFamily));
    }
  }

  void _initHashtags() async {
    /// hashtag 推荐
    if (type == MomentsType.recommend) {
      var recommendHashtags = await hashtagService.getRecommends() ?? [];
      emit(state.clone(recommendTags: recommendHashtags));

      if (recommendHashtags.isEmpty) return;

      /// 上报展示打点
      HashtagReporter.instance().recommendsShow(recommendHashtags);
    }
  }

  ///从服务端刷新
  Future<MomentsTabState> _loadData(MomentsTabEvent event) async {
    int page = (state.page ?? 1) + 1;

    if (connectivityUtil.isNoNet() && (refreshController.isRefresh)) {
      refreshController.refreshFailed();
      toast(LocaleStrings.instance.checkNetWork);
      return state.clone(hasError: true);
    }

    int? lastTime = state.lastTime;
    String? lastItemId = state.lastItemId;

    switch (event.runtimeType) {
      case MomentsEventRefresh:
        page = 1;
        lastTime = null;
        lastItemId = null;
        maleLastIdx = '0';
        femaleLastIdx = '0';
        clearImpItem();

        break;
    }
    var data = await momentsService.getMomentList(
        type: type,
        hashtagId: hashtagId,
        page: page,
        size: state.size,
        lastTime: lastTime,
        lastItemId: lastItemId,
        maleLastIdx: maleLastIdx,
        femaleLastIdx: femaleLastIdx);
    if (!data.isSuccess && page == 1) {
      toast(data.msg ?? LocaleStrings.instance.checkNetWork);
    }

    var resp = data.data;

    maleLastIdx = resp?.maleLastIdx ?? '0';
    femaleLastIdx = resp?.femaleLastIdx ?? '0';

    MomentsTabState newState = state.clone();

    if (page == 1) {
      if (resp != null) {
        newState = newState.clone(listMoment: []);
      }
      refreshController.refreshCompleted();
      newState = newState.clone(hasError: resp == null && (newState.listMoment?.isEmpty ?? true));

      ///防止推荐列表刷空的情况
      if (type == MomentsType.recommend && resp != null && (resp.list?.isEmpty ?? true)) {
        if (page == 1) {
          List<Moment>? list = await momentsService.getMomentsFromCache(type: type);
          return state.clone(listMoment: list, hasMore: true, expandTxtList: []);
        }
        return state;
      }
    } else {
      refreshController.loadComplete();
    }
    if (resp?.list?.isNotEmpty ?? false) {
      newState = newState.clone(hasMore: true);
      refreshController.resetNoData();
    } else {
      type == MomentsType.recommend ? refreshController.resetNoData() : refreshController.loadNoData();
      newState = newState.clone(hasMore: type == MomentsType.recommend);
    }
    final list = resp?.list;
    if (list?.isNotEmpty == true && type == MomentsType.recommend) {
      for (var item in list!) {
        item.itemPageId = "${item.itemId}-$page";
      }
    }

    newState.listMoment?.addAll(list ?? []);
    newState.listMoment?.toSet().toList();
    needCollect = true;

    String? newStateLastId = resp?.lastItemId;
    if (type == MomentsType.following) {
      newStateLastId = resp?.lastId;
    }

    return newState.clone(
        page: page,
        lastTime: (list?.isEmpty ?? true) ? lastTime : resp?.lastTime,
        lastItemId: (list?.isEmpty ?? true) ? lastItemId : newStateLastId,
        expandTxtList: page == 1 ? [] : null);
  }

  void _handlerDeleteMoment(String momentId) async {
    if (state.listMoment?.isEmpty ?? true) return;
    Future.delayed(Duration(milliseconds: 500), () {
      for (var moment in state.listMoment!) {
        if (moment.itemId == momentId) {
          List<Moment> list = state.listMoment!;
          list.remove(moment);
          emit(state.clone(listMoment: list));
          break;
        }
      }
    });
  }

  void _publishSuccess(Moment? moment) {
    if (moment == null) return;

    var list = state.listMoment ?? [];
    list.insert(0, moment);
    emit(state.clone(listMoment: list));

    Future.delayed(Duration(milliseconds: 100), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.minScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void reportItemShow(RenderBox box, int index, String type) async {
    if (!needCollect) return;
    if (currentBox == null) return;
    if (!box.hasSize) return;
    if (state.listMoment?.isEmpty ?? true) return;
    if (state.listMoment![index].hasReport == true) return;

    state.listMoment![index].hasReport = true;
    final currentEndY = currentStartY + currentBox!.size.height;
    if (itemHeight >= currentStartY && itemHeight <= currentEndY) {
      reportTabItemImp(state.listMoment![index], type);
    } else {
      setItem(key: index, item: ImpReportMoment(state.listMoment![index], itemHeight));
    }
    itemHeight += box.size.height;
  }

  void _handlerBlockUser(BlockUserEvent event) {
    if (state.listMoment?.isNotEmpty == true) {
      List<Moment> list = [];
      for (var moment in state.listMoment!) {
        if (moment.uid != event.targetId) {
          list.add(moment);
        }
      }
      emit(state.clone(listMoment: list));
    }
  }

  /// 处理文本收起展示全部
  MomentsTabState _expandTxt(ExpandTxtEvent event) {
    List<String> list = []..addAll(state.expandTxtList ?? []);
    if (event.isExpand) {
      list.add(event.momentId);
    } else {
      list.remove(event.momentId);
    }
    return state.clone(expandTxtList: list);
  }

  void reportExposure(List<int> indexs, MomentsType pageType) async {
    int offset = 0;
    if (pageType == MomentsType.recommend) {
      offset = 2;
    }

    if (state.listMoment?.isNotEmpty == true) {
      List<Moment> listMoment = state.listMoment!;
      int listCount = listMoment.length;
      List<String> keyIds = [];
      for (var index in indexs) {
        int targetIndex = index - offset;
        if (targetIndex >= 0 && targetIndex <= listCount - 1) {
          var element = listMoment[targetIndex];
          if (!element.hasExposure && element.keyId?.isNotEmpty == true) {
            element.hasExposure = true;
            keyIds.add(element.keyId!);
          }
        }
      }

      if (keyIds.isNotEmpty) {
        String keyIdsString = keyIds.join(',');
        await momentsService.reportExposure(keyIds: keyIdsString);
      }
    }
  }
}
