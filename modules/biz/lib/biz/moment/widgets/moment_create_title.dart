import 'package:biz/biz.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:biz/biz/moment/controller/moment_title_controller.dart';
import 'package:get/get.dart';
import 'package:service/modules/moments/model/publish_title_item.dart';
import 'package:biz/biz/moment/widgets/moment_publish_pop_widget.dart';
import 'package:service/modules/moments/model/theme_item.dart';

class MomentCreateTitle extends StatefulWidget {
  const MomentCreateTitle({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _MomentCreateTitleState();
  }
}

class _MomentCreateTitleState extends State {

  final MomentTitleController _controller = MomentTitleController();
  late double itemWidth;

  @override
  void initState() {
    super.initState();
    _controller.getTitleList();
  }

  @override
  Widget build(BuildContext context) {
    itemWidth = ((MediaQuery.of(context).size.width - 36.pt) / 5).floorToDouble();

    return MomentPublishPopWidget(
        listenerContrller: _controller,
        title: LocaleStrings.instance.title,
        onBack: () => Navigator.of(context).pop(),
        onEnter: () {
          PublishTitleItem item = _controller.currentItem();
          Navigator.of(context).pop(item);
        },
        child: Container(
            child: Column(
              children: [
             _inputTextField(),
             Expanded(child: _grid())
           ],
         )
       )
    );
  }

   Widget _inputTextField() {
      return Container(
          margin: EdgeInsets.only(left: 18.pt, top: 24.pt, right: 18.pt),
          height: 45.pt,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(11.pt),
            color: Colors.white
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GlobalWidgets.spacingHorizontal(11.pt),
              Obx(() => _controller.iconImage.value.isNotEmpty ? CachedNetworkImage(
                imageUrl: _controller.iconImage.value,
                width: 26.pt,
                height: 26.pt,
              ) : SizedBox(width: 26.pt, height: 26.pt)),
              GlobalWidgets.spacingHorizontal(8.pt),
              Expanded(
                  child: Card(
                      elevation: 0,
                      margin: EdgeInsets.all(0),
                      color: Colors.transparent,
                      child: Obx(() => TextField(
                        maxLines: 1,
                        controller: _controller.inputController,
                        enabled: _controller.enableEdit.value,
                        inputFormatters: [LengthLimitingTextInputFormatter(35)],
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.all(0),
                          border: InputBorder.none,
                          hintText: LocaleStrings.instance.publishTitleHintText,
                          hintStyle: TextStyle(
                            color: Color(0x80A3AFC2)
                          )
                        ),
                        style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeightExt.medium
                        )
                    ),
                    )
                  )
              ),
              GlobalWidgets.spacingHorizontal(11.pt)
            ],
          ),
      );
   }

   Widget _grid() {
    return Padding(
      padding: EdgeInsets.only(left: 18.pt, top: 18.pt, right: 18.pt),
      child: Obx(() =>
          CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: _gridTop(),
              ),
              SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  crossAxisSpacing: 0,
                  mainAxisSpacing: 0,
                ),
                delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
                     PublishTitleItem item = _controller.list[index + 4];
                     return _gridItem(item, index + 4);
                  },
                  childCount: _controller.list.length - 4,
                ),
              )
            ],
          )
      )
    );
   }

   Widget _gridTop() {
    if (_controller.list.length <= 0) {
      return SizedBox();
    }
     PublishTitleItem? item0 = _controller.list[0];
     PublishTitleItem? item1 = _controller.list[1];
     PublishTitleItem? item2 = _controller.list[2];
     PublishTitleItem? item3 = _controller.list[3];

     return Container(
       alignment: Alignment.topLeft,
       child: Row(
         mainAxisAlignment: MainAxisAlignment.start,
           mainAxisSize: MainAxisSize.min,
         children: [
           if (item0 != null) _dateGridItem(item0),
           if (item1 != null) _gridItem(item1, 1),
           if (item2 != null) _gridItem(item2, 2),
           if (item3 != null) _gridItem(item3, 3),
         ],
       ),
     );
   }

   /// 日期的item
   Widget _dateGridItem(PublishTitleItem item) {
      return Container(
        width:  itemWidth * 2,
        height: itemWidth,
        child: Row(
          children: [
            _dateBorder(item, (itemWidth * 2 - 8.pt).floorToDouble(), itemWidth),
            GlobalWidgets.spacingHorizontal(6.pt),
            _dateGridSeperatorLine()
          ],
        ),
      );
   }


   /// 日期的item border
   Widget _dateBorder(PublishTitleItem item, double width, double height) {
     return GestureDetector(
       child: Container(
         width: width,
         height: height,
         child: Stack(
           alignment: Alignment.center,
           children: [
             _dateContentItem(item),
             Obx(() =>
                 Visibility(
                   visible: _controller.selectIndex == 0,
                   child: Container(
                     width: width,
                     height: height,
                     decoration: BoxDecoration(
                         borderRadius: BorderRadius.circular(11.pt),
                         border: Border.all(color: R.color.primaryColor, width: 2.pt)
                     ),
                     child: SizedBox(),
                   ),
                 )
             )
           ],
         ),
       ),
       onTap: () {
         _controller.selectedIndex(0);
       },
     );
   }

   Widget _dateContentItem(PublishTitleItem item) {
    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CachedNetworkImage(
            imageUrl: item.icon ?? "",
            width: 26.pt,
            height: 26.pt,
          ),
          GlobalWidgets.spacingHorizontal(11.pt),
          Text(
            _controller.currentDate,
            maxLines: 1,
            style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeightExt.black,
                fontStyle: FontStyle.italic,
                color: Color(0xFF858585)
            ),
          ),
        ],
      ),
    );
   }

   Widget _dateGridSeperatorLine() {
     return Container(
       width: 2.pt,
       height: 21.pt,
       color: Color(0x80A3AFC2),
       child: SizedBox(),
     );
   }

   Widget _gridItem(PublishTitleItem item, int index) {
    return GestureDetector(
      child: Container(
        width: itemWidth,
        height: itemWidth,
        padding: EdgeInsets.all(5.pt),
        child: Stack(
          alignment: Alignment.center,
          children: [
            CachedNetworkImage(
              imageUrl: item.icon ?? "",
              width: itemWidth - 36.pt,
              height: itemWidth - 36.pt,
            ),
            Obx(() => Visibility(
              visible: _controller.selectIndex.value == index,
              child: Container(
                width: itemWidth,
                height: itemWidth,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(11.pt),
                    border: Border.all(color: R.color.primaryColor, width: 2.pt),
                ),
                child: SizedBox(),
              ),
            ))
          ],
        ),
      ),
      onTap: () {
        _controller.selectedIndex(index);
      },
    );
   }
}