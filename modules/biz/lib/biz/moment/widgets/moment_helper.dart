import 'dart:convert';

import 'package:biz/biz/moment/controller/moment_theme_controller.dart';
import 'package:biz/biz/moment/medias_detail/medias_detail_page.dart';
import 'package:biz/biz/share/share_widget.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/biz/web/js/model/share_model.dart';
import 'package:biz/utils/anim_util.dart';
import 'package:flutter/material.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/dialog/bottom_sheet.dart';
import 'package:service/modules/common/model/share_template.dart';
import 'package:service/modules/im/model/refer_multimedia_msg_content.dart';
import 'package:service/modules/moments/const/enums.dart';
import 'package:service/modules/moments/const/events.dart';
import 'package:service/modules/moments/handler/publish_moment_manager.dart';
import 'package:service/modules/moments/model/deep_link.dart';
import 'package:service/modules/moments/model/file_info.dart';
import 'package:service/modules/moments/model/moment.dart';
import 'package:service/modules/moments/model/publish_moment.dart';
import 'package:service/modules/moments/model/theme_item.dart';
import 'package:service/modules/voice_verify/const/enums.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';

import 'moment_create_theme.dart';
import 'moment_item_medias.dart';

void showMomentMoreDialog(BuildContext context,
    {required Moment moment,
    bool selfAction = false,
    bool? showPermission,
    Function? clickPermission,
    Function(MoreStatisticsType, Moment, ShareApps?)?
        itemMoreStatisticCallback,
    }) {
  if (moment.userInfo?.uid == accountService.getAccountInfo()?.uid &&
      selfAction) {
    List<SheetAction> actions = [];
    if (showPermission ?? false) {
      actions.add(SheetAction(
          title: LocaleStrings.instance.permission,
          onAction: () {
            clickPermission?.call();
          }));
    }
    actions.addAll([
      /* SheetAction(
          title: moment.isPin == true
              ? LocaleStrings.instance.cancelPin
              : LocaleStrings.instance.pin,
          onAction: () {
            momentsService.pin(
                momentId: moment.isPin == true ? "" : moment.itemId);
            itemMoreStatisticCallback?.call(
                MoreStatisticsType.pin, moment, null);
          }),*/
      // SheetAction(
      //     title: LocaleStrings.instance.moveToAnotherTheme,
      //     onAction: () {
      //        showThemeListPop(context, moment);
      //       itemMoreStatisticCallback?.call(
      //           MoreStatisticsType.moveTheme, moment, null);
      //     }),
      SheetAction(
          title: LocaleStrings.instance.delete,
          onAction: () {
            deleteMomentConfirm(context, moment: moment);
            itemMoreStatisticCallback?.call(
                MoreStatisticsType.delete, moment, null);
          }),
    ]);
    showBottomSheetDialog(context: context, actions: actions);

    /// 用于界面更多界面展示 上报
    itemMoreStatisticCallback?.call(MoreStatisticsType.pop, moment, null);
    return;
  }

  showShareDialog(
      context: context,
      template: MomentShare(moment: moment),
      showBlock: true,
      itemMoreStatisticCallback: itemMoreStatisticCallback,
    clickItemCallback: (String title) {
      if (title == LocaleStrings.instance.moveTheme) {
        Navigator.of(context).pop();
        showThemeListPop(context, moment);
        itemMoreStatisticCallback?.call(
            MoreStatisticsType.moveTheme, moment, null);
      } else if (title == LocaleStrings.instance.delete) {
        Navigator.of(context).pop();
        deleteMomentConfirm(context, moment: moment);
        itemMoreStatisticCallback?.call(
            MoreStatisticsType.delete, moment, null);
      }
    }
  );
}

void deleteMomentConfirm(BuildContext context, {required Moment moment}) {
  showAlertDialog(
    context: context,
    content: LocaleStrings.instance.sureDelete,
    confirmText: LocaleStrings.instance.confirm,
    onConfirm: () {
      momentsService.delete(momentId: moment.itemId);
    },
  );
}

/// [momentId] 动态id
void showMomentDetail(
    {required String momentId,
    Moment? moment,
    bool commentFocus = false,
    String? itemType}) {
  if (momentId.isEmpty) {
    return;
  }
  routerUtil.push(R_MOMENTS_DETAIL, params: {
    P_MOMENT_ID: momentId,
    "commentFocus": commentFocus.toString(),
    P_STATISTIC_FROM: itemType
  });
}

/// [momentId] 动态id
/// [index] 查看媒体数组下标
void showMomentMedias(BuildContext context,
    {required String momentId,
    required int index,
    required String from,
    Moment? moment}) {
  final route = PageRouteBuilder(
    barrierColor: Colors.transparent,
    opaque: false,
    pageBuilder: (_, __, ___) {
      return MediasDetailPage(momentId, index, from, moment: moment);
    },
    transitionsBuilder: easeFadeTransitionsBuilder,

    ///push动画时长
    transitionDuration: const Duration(milliseconds: 500),

    ///pop动画时长
    reverseTransitionDuration: const Duration(milliseconds: 100),
    settings: RouteSettings(name: R_MOMENTS_MEDIAS),
  );
  Navigator.of(context, rootNavigator: true).push(route);
}

Future<bool> shareMomentFromShareModel(ShareModel share) async {
  showLoading();
  var momentIdResult = await momentsService.getPublishId();
  var momentId = momentIdResult?.itemId ?? "";
  if (momentId.isEmpty) {
    hideLoading();
    toast(LocaleStrings.instance.checkNetWork);
    return false;
  }
  DeepLinkInfo? deeplink;
  if (share.deepLink?.isNotEmpty == true) {
    deeplink = DeepLinkInfo(
        share.deepLink ?? "", share.title, share.deepLinkIcon ?? "");
  }

  List<FileInfo> listImg = [];

  var shareImg = share.picture ?? "";
  if (shareImg.isNotEmpty) {
    var fileModel = await commonService.getFileModelFromUrl(shareImg);

    if (fileModel != null) {
      fileModel.name = "opt_pic_0";
      fileModel.publishId = momentId;

      /// 上传文件
      listImg =
          await commonService.upload([fileModel], moduleId: momentId) ?? [];
    }
  }

  var resp = await PublishMomentManager.instance().publish(
    filesBase: listImg.isNotEmpty
        ? base64.encode(utf8.encode(jsonEncode(listImg)))
        : null,
    checkPublishingMoment: false,
    detail: PublishMoment(
        momentId: momentId,
        text: share.text ?? '',
        visible: VerifyPermission.PUBLIC,
        files: [],
        from: share.from ?? "",
        hashtags: share.hashtags,
        deeplink: deeplink != null ? json.encode(deeplink) : null,
        contentType: share.contentType,
        skipAudit: share.skipAudit),
  );
  hideLoading();
  if (resp?.isSuccess == true) {
  } else {
    toast(resp?.msg ?? LocaleStrings.instance.checkNetWork);
  }

  return resp?.isSuccess == true;
}

Future<void> jumpPostMoment(BuildContext context, String from,
    {String? themeID, String? themeName}) async {
  ///是否被封禁
  if ((await hasBeRestricted(context))) {
    return;
  }
  routerUtil.pushWithAnimation(
      R_MOMENTS_PUBLISH,
      params: {
        P_STATISTIC_FROM: from,
        P_THEME_ID: themeID,
        P_THEME_NAME: themeName
      },
      duration: Duration(milliseconds: 240),
      animationBuilder: (BuildContext context, Animation<double> animation,
          Animation secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.linear;
        final tween = Tween(begin: begin, end: end);

        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: curve,
        );

        return SlideTransition(
          position: tween.animate(curvedAnimation),
          child: child,
        );
      }
  );
}

/// 跳转动态详情
void jumpMomentDetail(String momentId, String from) {
  showMomentDetail(momentId: momentId, itemType: from);
}

/// 跳转话题聚合页
void jumpThemeList(String themeId, String from) {
  routerUtil.push(R_THEME_LIST,
      params: {P_THEME_ID: themeId, P_STATISTIC_FROM: from});
}

/// 跳转到私聊页面，并且携带上动态的引用
void jumpChatRefMoment({required String targetUid, required Moment moment, String? from, String? statisticMatchType}) {
  var data = RefMultiMediaMsgContent.fromMoment(moment);

  routerUtil.push(R_CHAT, params: {
    P_TARGET_ID: targetUid,
    P_CONVERSATION_TYPE: RCIMIWConversationType.private,
    P_STATISTIC_FROM: from,
    P_STATISTIC_MATCH_TYPE: statisticMatchType,
    P_CHAT_REFER_DATA: data,
  });
}

String formatTime(Duration? duration) {
  if (duration == null || duration.inMilliseconds < 0) return "00:00:00";
  String minutes =
  MomentMedias.format.format(duration.inMinutes.remainder(Duration.minutesPerHour));
  String seconds =
  MomentMedias.format.format(duration.inSeconds.remainder(Duration.secondsPerMinute));
  return "$minutes:$seconds";
}

void showThemeListPop(BuildContext context, Moment moment) {

  MediaType type = MediaType.text;
  if (moment.medias?.isNotEmpty == true) {
    if (moment.medias![0].type == 'video') {
      type = MediaType.video;
    } else {
      type = MediaType.image;
    }
  }

  ThemeItem? targetThemeItem = moment.topicInfo?.clone();
  targetThemeItem?.section = MomentThemeController.MyTheme;

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return MomentCreateTheme(
          mediaType: type,
          customizedTheme: null,
          selectedTheme: targetThemeItem,
          enterBlock: (item) async {
             final resp = await momentsService.moveTheme(momentId: moment.itemId, oriThemeId: moment.topicInfo?.id, nowThemeId: item.id, themeName: item.name, themeDesc: item.desc);
             if (resp.isSuccess) {
               rxUtil.send(MomentsStatusEvent.moveTheme, resp.data?.topicInfo?.id ?? '');
               toast(LocaleStrings.instance.moveThemeSucc);
             } else {
               toast(resp.msg ?? '');
             }
          },
        );
      }
  );
}