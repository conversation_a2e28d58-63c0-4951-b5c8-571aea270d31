import 'dart:async';
import 'dart:developer';

import 'package:biz/common/widgets/text_selected_dialog.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/service.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/modules/common/const/event.dart';

class MomentsStr extends StatefulWidget {
  MomentsStr(this.text,
      {required this.expandText,
      required this.collapseText,
      this.expandTextColor,
      this.showAll,
      this.textScaleFactor,
      this.maxLines = 2,
      this.semanticsLabel,
      this.textColor,
      this.scrollAble,
      this.expandCallback,
      this.textDirection,
      this.fontSize,
      this.linkTapStaticCallback,
      this.canLongPressCopy = false});

  final String text;
  final String expandText;
  final String collapseText;
  final bool? showAll;
  final Color? textColor;
  final Color? expandTextColor;
  final double? textScaleFactor;
  final int maxLines;
  final String? semanticsLabel;
  final Function(bool)? expandCallback;

  final TextDirection? textDirection;
  final bool? scrollAble;

  final double? fontSize;

  final bool canLongPressCopy;

  final Function(String)? linkTapStaticCallback;

  @override
  _MomentsState createState() => _MomentsState();
}

class _MomentsState extends State<MomentsStr> {
  bool _expanded = false;
  late TapGestureRecognizer _tapGestureRecognizer;
  late LongPressGestureRecognizer _longPressGestureRecognizer;

  final _textKey = GlobalKey();

  StreamSubscription? _sub;

  /// 被长按选中
  bool isSelected = false;

  @override
  void initState() {
    super.initState();
    _expanded = widget.showAll ?? false;
    _tapGestureRecognizer = TapGestureRecognizer()..onTap = _toggleExpanded;
  }

  @override
  void dispose() {
    _tapGestureRecognizer.dispose();
    _sub?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(MomentsStr oldWidget) {
    super.didUpdateWidget(oldWidget);

    // if(oldWidget.id != widget.id || ){
    //
    // }
  }

  /// 长按后监听
  void _observerRx() {
    _sub?.cancel();
    _sub = rxUtil.observer(TextSelectedDialogEvent.hide).listen((value) {
      if (!mounted) return;
      setState(() {
        isSelected = false;
      });
    });
  }

  void _toggleExpanded() {
    setState(() {
      _expanded = !_expanded;
      widget.expandCallback?.call(_expanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    // super.build(context);
    final TextStyle effectiveTextStyle =
        TextStyle(color: widget.textColor, fontSize: widget.fontSize ?? 14.sp);

    final TextStyle linkTextStyle = effectiveTextStyle.copyWith(
      color: widget.expandTextColor ?? primaryColorHeavyPurple,
        fontWeight: FontWeightExt.heavy
    );
    final textScaleFactor =
        widget.textScaleFactor ?? MediaQuery.textScaleFactorOf(context);

    final locale = Localizations.localeOf(context);

    final linkText =
        _expanded ? ' ${widget.collapseText}' : '${widget.expandText}';

    final link = TextSpan(
      text: _expanded ? '' : '\u2026 ',
      style: effectiveTextStyle,
      children: <TextSpan>[
        TextSpan(
          text: linkText,
          style: linkTextStyle,
          recognizer: _tapGestureRecognizer,
        )
      ],
    );

    final text = TextSpan(text: widget.text, style: effectiveTextStyle);

    Widget result = LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        assert(constraints.hasBoundedWidth);
        final double maxWidth = constraints.maxWidth;

        TextPainter textPainter = TextPainter(
          text: link,
          textDirection: Directionality.of(context),
          textScaleFactor: textScaleFactor,
          maxLines: widget.maxLines,
          locale: locale,
        );
        textPainter.layout(minWidth: constraints.minWidth, maxWidth: maxWidth);

        final linkWidth = textPainter.width;

        textPainter.text = text;
        textPainter.layout(minWidth: constraints.minWidth, maxWidth: maxWidth);
        final textSize = textPainter.size;
        final position = textPainter.getPositionForOffset(Offset(
          textSize.width - linkWidth * 2,
          textSize.height,
        ));
        final endOffset = textPainter.getOffsetBefore(position.offset);

        var newText = (!_expanded && textPainter.didExceedMaxLines)
            ? '${widget.text.characters.skipLast(widget.text.length - (endOffset ?? 0))}'
            : widget.text;

        List<TextSpan> spans = _formatText(newText,
            textStyle: effectiveTextStyle, linkStyle: linkTextStyle);

        TextSpan textSpan;
        if (textPainter.didExceedMaxLines) {
          spans.add(link);
          textSpan = TextSpan(style: effectiveTextStyle, children: spans);
        } else {
          textSpan = TextSpan(style: effectiveTextStyle, children: spans);
        }
        return GestureDetector(
          onLongPress: _onLongPress,
          child: Container(
            decoration:
                BoxDecoration(color: isSelected ? Color(0xFFE5E5E5) : null),
            child: Text.rich(
              textSpan,
              key: _textKey,
              softWrap: true,
              textDirection: widget.textDirection ?? Directionality.of(context),
              textScaleFactor: textScaleFactor,
              overflow: TextOverflow.clip,
            ),
          ),
        );
      },
    );

    if (widget.semanticsLabel != null) {
      result = Semantics(
        textDirection: widget.textDirection ?? Directionality.of(context),
        label: widget.semanticsLabel,
        child: ExcludeSemantics(
          child: result,
        ),
      );
    }

    return result;
  }

  List<TextSpan> _formatText(
    String text, {
    required TextStyle textStyle,
    required TextStyle linkStyle,
  }) {
    var textList = text.split('\n');

    List<TextSpan> spans = [];

    /// 循环回车分割
    for (int i = 0; i < textList.length; i++) {
      String s = textList[i];
      var spaceList = s.split(' ');

      /// 循环空格分割
      for (int j = 0; j < spaceList.length; j++) {
        String ss = spaceList[j];
        if (ss.isNotEmpty && checkUrl(ss)) {
          spans.add(
            TextSpan(
                text: ss,
                style: linkStyle,
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    DeepLink.jump(ss);
                    widget.linkTapStaticCallback?.call(ss);
                  }),
          );
        } else {
          spans.add(TextSpan(text: ss, style: textStyle));
        }

        if (j != spaceList.length - 1) {
          spans.add(TextSpan(text: " ", style: textStyle));
        }
      }

      if (i != textList.length - 1) {
        spans.add(TextSpan(text: "\n", style: textStyle));
      }
    }
    return spans;
  }

  void _onLongPress() {
    if (!mounted) return;
    final box = _textKey.currentContext?.findRenderObject() as RenderBox?;
    if (box == null) return;
    _observerRx();
    showTextSelectedDialog(
        text: widget.text,
        offset: box!.localToGlobal(Offset.zero),
        size: box!.size);
    setState(() {
      isSelected = true;
    });
  }
}
