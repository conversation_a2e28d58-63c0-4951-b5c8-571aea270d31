import 'dart:ui';

import 'package:biz/biz.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:service/common/statistics/post_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/moments/model/theme_item.dart';
import 'package:biz/biz/moment/controller/moment_theme_controller.dart';
import 'package:biz/biz/moment/widgets/moment_create_custom_theme.dart';
import 'package:service/modules/moments/const/enums.dart';


class MomentCreateTheme extends StatefulWidget {

  MomentCreateTheme({Key? key, required this.mediaType, this.from, this.customizedTheme, this.selectedTheme, this.enterBlock});

  MomentThemeEnterCallback? enterBlock;

  MediaType mediaType = MediaType.text;

  ///自定义主题
  ThemeItem? customizedTheme;

  ///上次选择的主题
  ThemeItem? selectedTheme;

  String? from;

  @override
  State<StatefulWidget> createState() {
    return _MomentCreateThemeState();
  }
}

class _MomentCreateThemeState extends State<MomentCreateTheme> {

  final MomentThemeController _controller = MomentThemeController();

  @override
  void initState() {
    super.initState();

    PostStatistics.reportPostDetailThemePageImp(from: widget.from);

    if (widget.selectedTheme != null) {
      _controller.selectedTheme = widget.selectedTheme!.clone();
    }

    if (widget.customizedTheme != null) {
      _controller.customizedTheme = widget.customizedTheme!.clone();
    }
    _controller.getThemeList(widget.mediaType);
  }

   @override
   Widget build(BuildContext context) {

     return Container(
         height: MediaQuery.of(context).size.height - 67.pt,
         width: double.infinity,
         decoration: BoxDecoration(
           color: Color(0xFFEDEFF2),
           borderRadius: BorderRadius.only(topLeft: Radius.circular(24.pt), topRight: Radius.circular(24.pt))
         ),
         child: Column(
           crossAxisAlignment: CrossAxisAlignment.center,
           children: [
             GlobalWidgets.spacingVertical(21.pt),
             _top(),
             Expanded(child: _list())
           ],
         )
     );
   }

   Widget _top() {
     return Container(
       height: 30.pt,
       child: Stack(
         children: [
           Center(
             child: Text(
               LocaleStrings.instance.postWhichTheme,
               style: TextStyle(
                   fontSize: 18.sp,
                   color: R.color.textColor1,
                   fontWeight: FontWeightExt.heavy,
                   decoration: TextDecoration.none
               ),
             ),
           ),
           Positioned(
             right: 20.pt,
               child: _rigthAction()
           )
         ],
       ),
     );
   }

   Widget _rigthAction() {
     return  GestureDetector(
       onTap: () {
         if (_controller.selectedTheme != null) {
           ThemeItem? item = _controller.selectedTheme?.clone();
           widget.enterBlock?.call(item!);
           Navigator.of(context).pop();
         }
       },
       child: Container(
           height: 30.pt,
           width: 54.pt,
           alignment: Alignment.center,
           decoration: _postButtonDecoration(_controller.selectedTheme != null),
           child: Text(
               LocaleStrings.instance.done,
               style: TextStyle(
                   color: _controller.selectedTheme != null ? Colors.white : Color(0xFFBCC7DA),
                   fontWeight: FontWeightExt.heavy,
                   fontSize: 13.sp,
                   decoration: TextDecoration.none
               )
           )
       ),
     );
   }

  BoxDecoration _postButtonDecoration(bool state) {
    if (state) {
      return  BoxDecoration(
          borderRadius: BorderRadius.circular(15.pt),
          gradient: R.color.btnPrimaryLinearGradient
      );
    }
    return BoxDecoration(
      borderRadius: BorderRadius.circular(15.pt),
      color: Color(0x33B8C4CE) ,
    );
  }

   Widget _list() {
     return Padding(
       padding: EdgeInsets.only(left: 13.pt, right: 13.pt, top: 10.pt),
       child: GetBuilder<MomentThemeController>(
         init: _controller,
           builder: (controller){
             return ListView.builder(
               padding: EdgeInsets.zero,
               itemBuilder: (BuildContext context, int index) {

                 if (index == _controller.list.length) {
                   return SizedBox(
                     height: 88.pt,
                   );
                 }

                 return GestureDetector(
                   onTap: () {
                     ThemeItem model = _controller.list[index];
                     if (_controller.selectedTheme == model || model.isSectionHeader || model.isThemeAdd) {
                       return;
                     }
                     _controller.selectedTheme?.isSelected = false;
                     model.isSelected = true;
                     _controller.selectedTheme = model;
                     setState(() {});

                   },
                   child: _item(index),
                 );
               },
               itemCount: _controller.list.length + 1,
             );
           }
       )
     );
   }

   Widget _item(int index) {
     ThemeItem model = _controller.list[index];

     if (model.isSectionHeader) {

       double height = 52.pt;

      String title;
      if (model.section == MomentThemeController.MyTheme) {
        height = 42.pt;
        title = LocaleStrings.instance.myTheme;
      } else {
        title = LocaleStrings.instance.newTheme;
      }

      return Container(
         alignment: Alignment.bottomLeft,
         height: height,
         padding: EdgeInsets.only(bottom: 11.pt),
         child: Text(
           title,
           style: TextStyle(
               fontSize: 13.pt,
               fontWeight: FontWeightExt.heavy,
               color: R.color.textBlueColor2,
               decoration: TextDecoration.none
           ),
         ),
       );
     }

     BorderRadius radius = BorderRadius.circular(0);
     if (model.isSectionFirst && model.isSectionLast) {
       radius =  BorderRadius.all(Radius.circular(12.pt));
     } else if (model.isSectionFirst) {
       radius = BorderRadius.only(topRight: Radius.circular(12.pt), topLeft: Radius.circular(12.pt));
     } else if (model.isSectionLast) {
       radius = BorderRadius.only(bottomLeft: Radius.circular(12.pt), bottomRight: Radius.circular(12.pt));
     }

     double height = 76.pt;
     if (model.section == MomentThemeController.NewTheme) {
        height = 57.pt;
     }

     if (model.isThemeAdd) {
       return _myThemeAdd(height, radius);
     }

     return Container(
       height: height,
         decoration: BoxDecoration(
           borderRadius: radius,
           color: Colors.white,
         ),
       child: Column(
         children: [
           Expanded(
             child: Container(
               child: Row(
                 children: [
                   GlobalWidgets.spacingHorizontal(18.pt),
                   _itemThemeTitle(model),
                   GlobalWidgets.spacingHorizontal(10.pt),
                   _itemThemeIcon(model),
                   GlobalWidgets.spacingHorizontal(18.pt.pt),
                 ],
               ),
             ),
           ),
           Divider(
             height: 1.pt,
             indent: 5.pt,
             endIndent: 5.pt,
             color: Color(0x1A1F1F1F),
           )
         ],
       )
     );
   }

   Widget _itemThemeTitle(ThemeItem model) {
     return Expanded(
       child: Column(
         mainAxisAlignment: MainAxisAlignment.center,
         crossAxisAlignment: CrossAxisAlignment.start,
         children: [
           Text(
             model.name ?? "",
             maxLines: 1,
             overflow: TextOverflow.ellipsis,
             style: TextStyle(
                 fontSize: 16.sp,
                 fontWeight: FontWeightExt.medium,
                 decoration: TextDecoration.none,
                 color: R.color.textColor1
             ),
           ),
           Text(
             model.section == MomentThemeController.MyTheme ? LocaleStrings.instance.myThemeSubtitle(model.momentNum ?? 0) : (model.desc ?? ''),
             maxLines: 1,
             overflow: TextOverflow.ellipsis,
             style: TextStyle(
                 fontSize: 13.sp,
                 fontWeight: FontWeightExt.medium,
                 decoration: TextDecoration.none,
                 color: R.color.textBlueColor2
             ),
           ),
         ],
       ),
     );
   }

   Widget _itemThemeIcon(ThemeItem model) {
     return Stack(
       alignment: Alignment.center,
       children: [
         Visibility(
            visible: model.momentPhoto?.isNotEmpty == true,
             child: ClipRRect(
                 borderRadius: BorderRadius.circular(9.pt),
                 child: CachedNetworkImage(imageUrl: model.momentPhoto ?? '', width: 48.pt, height: 48.pt, fit: BoxFit.cover))
         ),
         Visibility(
           visible: model.isSelected,
             child: FLImage.asset(Res.commonIcSelectRight, width: 23.pt, height: 23.pt)
         )
       ],
     );
   }

   Widget _myThemeAdd(double height, BorderRadius radius) {
    return GestureDetector(
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: radius
        ),
        child: Column(
          children: [
            Expanded(
                child: Row(
                  children: [
                    GlobalWidgets.spacingHorizontal(18.pt),
                    FLImage.asset(Res.feedPublishFeedAdd, width: 21.pt, height: 21.pt),
                    GlobalWidgets.spacingHorizontal(8.pt),
                    Text(
                      LocaleStrings.instance.customizedThemes,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeightExt.medium,
                        decoration: TextDecoration.none,
                        color: R.color.textColor1,
                      ),
                    ),
                  ],
                ),
            ),

            Visibility(
                visible: _controller.newThemeListCount() > 1,
                child: Divider(
                  height: 1.pt,
                  indent: 5.pt,
                  endIndent: 5.pt,
                  color: Color(0x1A1F1F1F),
                )
            )
          ],
        ),
      ),
      onTap: () {
        Navigator.of(context).push(
            PageRouteBuilder(
                opaque: false,
                pageBuilder: (con, animation, secondaryAnimation){
                  return MomentCreateCustomTheme(enterBlock: _controller.addCustomTheme, themeList: _controller.list);
                }
            )
        ).then((value) => {
          _controller.addCustomTheme(value)
        });
      },
    );
   }

}