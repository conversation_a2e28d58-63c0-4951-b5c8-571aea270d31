
import 'package:biz/biz.dart';
import 'package:biz/biz/intimacy/widgets/intimacy_animation_controller.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:flutter_vap/flutter_vap.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:get/get.dart';

void showIntimacyAnimationDialog({
  EdgeInsets insets = EdgeInsets.zero,
  int intimacyLevel = 0,
  bool showLevelUp = false,
  bool autoDismiss = true,
  UserInfo? targetUser,
  UserInfo? intimacyUser,
  VoidCallback? onComplete
}) {
  dialog.showDialog((_) =>
      IntimacyAnimationWidget(
          insets: insets,
          intimacyLevel: intimacyLevel,
          showLevelUp: showLevelUp,
          targetUser: targetUser,
          intimacyUser: intimacyUser,
          onComplete: onComplete,
          autoDismiss: autoDismiss,
      ),
      routeSettings: RouteSettings(name: R_INTIMATE_ANIMATION),
      barrierDismissible: false,
      barrierColor: Colors.transparent
  );
}

class IntimacyAnimationWidget extends StatefulWidget {

  final EdgeInsets insets;
  final int intimacyLevel;
  final bool showLevelUp;
  /// 为true会自动pop当前路由
  final bool autoDismiss;
  final UserInfo? targetUser;
  final UserInfo? intimacyUser;
  final VoidCallback? onComplete;

  const IntimacyAnimationWidget({
    this.insets = EdgeInsets.zero,
    this.intimacyLevel = 0,
    this.showLevelUp = false,
    this.autoDismiss = false,
    this.targetUser,
    this.intimacyUser,
    this.onComplete
  });

  @override
  State<StatefulWidget> createState() => _IntimacyAnimationWidgetState();
}

class _IntimacyAnimationWidgetState extends State<IntimacyAnimationWidget> with SingleTickerProviderStateMixin {

  double _topSpacing = 0;
  double _vapWidgetHeight = 0;
  Size _screenSize = Size.zero;
  final double _levelImageWidth = 220.pt;
  final double _levelImageHeight = 240.pt;
  final IntimacyAnimationController _controller = IntimacyAnimationController();

  late AnimationController _animationController;

  late Animation<Offset> _leftSlideAnimation;
  late Animation<double> _leftFadeAnimation;

  late Animation<Offset> _rightSlideAnimation;
  late Animation<double> _rightFadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller.autoDismiss = widget.autoDismiss;
    _controller.intimacyLevel = widget.intimacyLevel;
    _controller.onComplete = widget.onComplete;
    _animationController = AnimationController(vsync: this, duration: Duration(milliseconds: 1000));

    Interval curve = Interval(0.4, 0.9);

    _leftSlideAnimation = Tween<Offset>(
        begin: Offset(0, 0),
        end: Offset(0.3, 1)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: curve,
    ));

    _leftFadeAnimation = Tween<double>(
        begin: 0,
        end: 1
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: curve,
    ));

    _rightSlideAnimation = Tween<Offset>(
        begin: Offset(0, 0),
        end: Offset(-0.3, 1)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: curve,
    ));

    _rightFadeAnimation = Tween<double>(
        begin: 0,
        end: 1
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: curve,
    ));

    WidgetUtils.post((duration) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(IntimacyAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _controller.autoDismiss = widget.autoDismiss;
    _controller.intimacyLevel = widget.intimacyLevel;
    _controller.onComplete = widget.onComplete;

    if (oldWidget.intimacyLevel != widget.intimacyLevel ||
        oldWidget.showLevelUp != widget.showLevelUp ||
        oldWidget.intimacyUser?.uid != widget.intimacyUser?.uid ||
        oldWidget.targetUser?.uid != widget.targetUser?.uid) {
      WidgetUtils.post((duration) {
        _animationController.reset();
        _animationController.forward();
        _controller.startPlay();
      });
    }
  }

  double _culRealTopMargin() {
    double innerPadding = (_vapWidgetHeight - _levelImageHeight) / 2;
    if (widget.insets.top > 0) {
      return innerPadding >= widget.insets.top ? 0 : ( widget.insets.top - innerPadding);
    }

    if (widget.insets.bottom > 0) {
      return innerPadding >= widget.insets.bottom ?
      (_screenSize.height - _vapWidgetHeight) :
      (_screenSize.height - _vapWidgetHeight - (widget.insets.bottom - innerPadding));
    }

    return 0;
  }

  @override
  Widget build(BuildContext context) {
    _screenSize = MediaQuery.of(context).size;
    _vapWidgetHeight = _screenSize.width * 680 / 640;
    _topSpacing = _culRealTopMargin();
    return IgnorePointer(
      child: GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
            init: _controller,
            global: false,
            autoRemove: false,
            builder: (controller) {
              return Material(
                color: Colors.transparent,
                child: Container(
                  padding: EdgeInsets.only(
                      top: _topSpacing
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          _intimacyVapLevel(),
                          _contentWidget()
                          // _pag
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  Widget _contentWidget() {
    return Container(
      width: _levelImageWidth,
      height: _levelImageHeight,
      alignment: Alignment.center,
      child: Stack(
        alignment: Alignment.topCenter,
        clipBehavior: Clip.none,
        children: [
          Positioned(
              top: -28.pt,
              child: _intimacyLevelUp()
          ),
          _intimacyImageLevel(),
          Positioned(
            top: 80.pt,
            left: 0,
            child: SlideTransition(
              position: _leftSlideAnimation,
              child: FadeTransition(
                opacity: _leftFadeAnimation,
                child: _userAvatar(
                    widget.targetUser?.avatar ?? '',
                    widget.targetUser?.avatarCode ?? '',
                    widget.targetUser?.uid ?? ''
                ),
              ),
            ),
          ),
          Positioned(
            top: 80.pt,
            right: 0,
            child: SlideTransition(
              position: _rightSlideAnimation,
              child: FadeTransition(
                opacity: _rightFadeAnimation,
                child: _userAvatar(
                    widget.intimacyUser?.avatar ?? '',
                    widget.intimacyUser?.avatarCode ?? '',
                    widget.intimacyUser?.uid ?? ''
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _intimacyImageLevel() {
    if (_controller.intimacyItem?.spaceConfig.imageUrl.isNotEmpty == true &&
        _controller.intimacyItem?.spaceConfig.animationUrl.isEmpty == true) {
      return CachedNetworkImage(
        width: _levelImageWidth,
        height: _levelImageHeight,
        imageUrl: _controller.intimacyItem?.spaceConfig.imageUrl ?? '',
      );
    }
    return SizedBox(
      width: _levelImageWidth,
      height: _levelImageHeight,
    );
  }

  Widget _intimacyVapLevel() {
    return _controller.isPageShow ? SizedBox(
      width: 1.w,
      height: _vapWidgetHeight,
      child: GlobalWidgets.vapWidget(),
    ) : const SizedBox.shrink();
  }

  Widget _userAvatar(String avatar, String avatarCode, String uid) {
    if (avatar.trim().isEmpty == true && avatarCode.trim().isEmpty == true) {
      return FLImage.asset(Res.intimacyAniAvatarEmpty,
          width: 64.pt, height: 64.pt);
    }
    return UserAvatar(
      url: avatar,
      avatarCode: avatarCode,
      size: 64.pt,
      hasFrame: true,
      userId: uid,
      borderWidth: 1.pt,
      borderColor: Colors.white,
    );
  }

  Widget _intimacyLevelUp() {
    if (!widget.showLevelUp) return SizedBox.shrink();
    return Container(
      height: 28.pt,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14.pt),
        color: R.color.primaryColor.withOpacity(0.4)
      ),
      padding: EdgeInsets.symmetric(horizontal: 13.pt),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            LocaleStrings.instance.lvNum(widget.intimacyLevel ?? 0),
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeightExt.heavy,
              color: R.color.primaryLightColor,
              fontStyle: FontStyle.italic
            ),
          ),
          SizedBox(width: 3.pt),
          FLImage.asset(Res.intimacyIconLevelUp, width: 11.pt, height: 15.pt),
          SizedBox(width: 5.pt),
          Text(
            "${_controller.intimacyItem?.spaceConfig.name ?? ''}",
            style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeightExt.heavy,
                color: Colors.white,
                fontStyle: FontStyle.italic
            ),
          ),
        ],
      ),
    );
  }

}