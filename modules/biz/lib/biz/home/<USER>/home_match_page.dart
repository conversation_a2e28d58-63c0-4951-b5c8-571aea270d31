import 'package:biz/biz.dart';
import 'package:biz/biz/home/<USER>/home_match_logic.dart';
import 'package:biz/common/widgets/user_gender_icon_widget.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:flutter/material.dart' hide showDialog;
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/global/dialog/dialog_util.dart';
import 'package:service/global/widget/svga.dart';

const String _homeMachDialogRouteName = "dialog/home_match";

void showHomeMatchDialog({
  BuildContext? context,
}) {
  DialogScheduler.instance().schedule(
    () {
      return showDialog(
        (_) => HomeMatchPage(),
        barrierDismissible: true,
        context: context,
        routeSettings: dialogRouteSettings(_homeMachDialogRouteName),
      );
    },
    forceShow: true,
  );
}

class HomeMatchPage extends StatefulWidget {
  const HomeMatchPage({super.key});

  @override
  State<HomeMatchPage> createState() => _HomeMatchPageState();
}

class _HomeMatchPageState extends State<HomeMatchPage>
    with GetStateBuilderMixin<HomeMatchPage, HomeMatchLogic>, SingleTickerProviderStateMixin {
  late AnimationController animationController;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    animationController.addStatusListener((AnimationStatus status) {
      if (status == AnimationStatus.completed) {}
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  HomeMatchLogic initCtl() {
    return HomeMatchLogic();
  }

  @override
  Widget build(BuildContext context) {
    return buildGetWidget(context);
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    final top = MediaQuery.of(context).padding.top;
    return Material(
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(child: FLImage.asset(Res.homeBgMatch, width: double.infinity, fit: BoxFit.fitWidth)),
          Positioned(
            top: top + 70.pt,
            child: SizedBox(
              width: 145.pt,
              child: SvgaWidget(
                svgaInfo: SvgaInfo(assetUrl: Res.svgaMatchTitle, repeat: true),
              ),
            ),
          ),
          Obx(() {
            final user = getCtl.matchUser.value;
            if (user != null) return const SizedBox.shrink();
            return SizedBox(
              width: 1.w,
              height: 1.w * 2,
              child: SvgaWidget(
                svgaInfo: SvgaInfo(assetUrl: Res.svgaMatchBg, repeat: true),
              ),
            );
          }),
          Obx(
            () {
              final avatar = getCtl.avatar.value;
              final matchAvatar = getCtl.matchUser.value?.avatar ?? '';
              if (avatar.isNotEmpty && matchAvatar.isNotEmpty) {
                return _buildAnimatedWidget();
              }
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  UserAvatar(
                    url: getCtl.avatar.value,
                    size: 112.pt,
                    borderColor: const Color(0xFF962FB4),
                    borderWidth: 4.pt,
                  ),
                  40.hSpace,
                ],
              );
            },
          ),
          Obx(() {
            final user = getCtl.matchUser.value;
            if (user == null) return const SizedBox.shrink();
            return Positioned(
              top: 430.pt,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    user.nickname,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20.sp,
                      fontWeight: FontWeightExt.heavy,
                    ),
                  ),
                  10.wSpace,
                  USerGenderIconWidget(genderStr: user.sex)
                ],
              ),
            );
          })
        ],
      ),
    );
  }

  Widget _buildAnimatedWidget() {
    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;
    // 定义左侧头像动画，从屏幕左侧 (-screenWidth/2) 到中间的 (-50)
    final leftAvatarAnimation = Tween<double>(begin: -screenWidth / 2, end: -100).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
    );

    // 定义右侧头像动画，从屏幕右侧 (screenWidth/2) 到中间的 (50)
    final rightAvatarAnimation = Tween<double>(begin: screenWidth / 2, end: 100).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
    );

    // 开始动画
    animationController.forward();

    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // 左侧头像
            Positioned(
              top: 270.pt,
              left: MediaQuery.of(context).size.width / 2 + leftAvatarAnimation.value,
              child: UserAvatar(
                url: getCtl.avatar.value,
                size: 112.pt,
                borderColor: const Color(0xFF962FB4),
                borderWidth: 4.pt,
              ),
            ),
            // 右侧头像
            Positioned(
              top: 270.pt,
              right: MediaQuery.of(context).size.width / 2 - rightAvatarAnimation.value,
              child: UserAvatar(
                url: getCtl.matchUser.value?.avatar ?? '',
                size: 112.pt,
                borderColor: const Color(0xFF962FB4),
                borderWidth: 4.pt,
              ),
            ),
          ],
        );
      },
    );
  }
}
