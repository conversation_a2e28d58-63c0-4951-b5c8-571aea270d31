import 'package:biz/biz.dart';
import 'package:biz/biz/home/<USER>/home_for_you_list_widget.dart';
import 'package:biz/biz/live_room/page/room_party/controller/check_daily_rewards_task.dart';
import 'package:biz/biz/main/event.dart';
import 'package:biz/biz/task/newbie/newbie_dialog.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/account/const/events.dart';
import 'package:service/modules/home/<USER>/home_gift_package_module.dart';
import 'package:service/modules/user/const/events.dart';
import 'package:service/modules/user/model/user_count_info.dart';
import 'package:service/service.dart';

import '../moment/widgets/monent_tab_page.dart';
import 'nearby/nearby_page_widget.dart';

class HomePageController extends AbsGetController {
  static const String _tag = 'HomePage';

  static const String kHomeUpdateIntervalKey = 'kHomeUpdateIntervalKey';
  static const int kHomeReloadInterval = 5 * 60 * 1000;

  final HomeForYouPageControllerExternal forYouCtl = HomeForYouPageControllerExternal();
  final NearByPageControllerExternal nearbyCtl = NearByPageControllerExternal();

  static Preferences pref = Preferences.newInstance(_tag);

  TabController? tabController;

  Rxn<HomeGiftPackageModule> homeGiftPackageConfig = Rxn<HomeGiftPackageModule>();

  String? currentUid;

  final _pref = Preferences.newInstance(spTask);

  String get _uid => accountService.currentUid() ?? '';

  bool _requestNewBie = false;

  final refreshController = RefreshController();

  MomentPageController recController = MomentPageController();

  RxInt noticeNum = 0.obs;

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
    _initData();
  }

  void _initRx() {
    listenRxEvent<MainPageName>(MainTabEvent.tabSwitched, _tabChange);
    listenRxEvent<String>(AccountEvent.logoutSuccess, _accountLogoutSuccess);
    listenRxEvent<MainPageName>(MainTabEvent.doubleTap, _onMainTabDoubleTap);
    listenRxEvent<int>(UserRegQAEvent.onComplete, (_) async => _checkSign());
    listenRxEvent<String>(AccountEvent.loginSuccess, (_) async => _checkSign());
    listenRxEvent<UserCountInfo>(UserCountEvent.update, _unReadUpdate);
  }

  void _initData() {
    tabController?.addListener(_changeTab);
  }

  void _initHomeConfig() async {
    var resp = await homeService.homePackageConfig();
    if (resp.isSuccess) {
      homeGiftPackageConfig.value = resp.data;
    }
  }

  void _initNotice() {
    ///信箱未读
    userService.getUserCount().then((value) {
      noticeNum.value = value.mailMsgCount ?? 0;
    });
  }

  void _unReadUpdate(UserCountInfo userCountInfo) {
    noticeNum.value = userCountInfo.mailMsgCount ?? 0;
  }

  @override
  void onPageShow() {
    super.onPageShow();
    _refreshList(index: tabController?.index ?? 0);
  }

  @override
  void onReady() {
    super.onReady();
    // _checkSign();
  }

  void _tabChange(MainPageName pageName) {
    if (pageName == MainPageName.home) {
      _refreshList(index: tabController?.index ?? 0);
    }
  }

  void _changeTab() {
    int index = tabController?.index ?? 0;
    _refreshList(index: index);
  }

  void _accountLogoutSuccess(String uid) {
    String forYouKey = '${kHomeUpdateIntervalKey}_0';
    pref.setInt(forYouKey, 0);

    String nearbyKey = '${kHomeUpdateIntervalKey}_1';
    pref.setInt(nearbyKey, 0);
  }

  void _checkSign() async {
    final isRegisterUser = accountService.getAccountInfo()?.registered ?? false;
    if (!isRegisterUser) return;

    final isNeedRegQuestion = accountService.getAccountInfo()?.isNeedRegQuestion ?? false;
    if (isNeedRegQuestion) return;

    final showNewbie = _pref.getBool(spKeyNewbieDialog(_uid)) ?? false;

    if (CheckDailyRewardsTask.isRegisterUser && !showNewbie) {
      final show = await _checkNewbie();
      if (!show) _checkSignBoard();
    } else {
      _checkSignBoard();
    }
  }

  void _refreshList({required int index, bool force = false, bool? scrollToTop}) {
    String indexKey = '${kHomeUpdateIntervalKey}_$index';

    int? interval = pref.getInt(indexKey);
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    if (!force && interval != null && currentTime - interval < kHomeReloadInterval) {
      return;
    }
    pref.setInt(indexKey, currentTime);

    switch (index) {
      case 0:
        forYouCtl.loadData(scrollToTop: scrollToTop);
        _initHomeConfig();

        _checkSign();
        break;
      case 1:
        nearbyCtl.loadData(scrollToTop: scrollToTop);
        break;
      default:
        break;
    }
  }

  void _onMainTabDoubleTap(MainPageName value) {
    int index = tabController?.index ?? 0;
    _refreshList(index: index, force: true, scrollToTop: true);
  }

  /// 新手礼包
  /// 新注册的用户在第一次打开app游戏主页时，自动弹出新手礼包弹框。弹框优先级，新手礼包＞7天签到弹框
  Future<bool> _checkNewbie() async {
    if (_requestNewBie) return true;

    _requestNewBie = true;
    final sex = (await userService.getCurrentUserInfo())?.sexStr ?? '';
    final list = await taskService.taskNewBie(sex: sex);
    _requestNewBie = false;

    if (list.isEmpty) return false;

    final reward = list.firstOrNull;
    if (reward == null) return false;

    showNewbieDialog(
      model: reward,
      context: getContext(),
      onClosed: () {
        CheckDailyRewardsTask.isRegisterUser = false;
        _checkSignBoard();
      },
    );

    Log.i(_tag, 'showNewbieDialog');

    if (_uid.isNotEmpty) {
      _pref.setBool(spKeyNewbieDialog(_uid), true);
    }

    return true;
  }

  void _checkSignBoard() async {
    final canShow = await CheckDailyRewardsTask.instance.checkCanShow();
    if (canShow) {
      await CheckDailyRewardsTask.instance.showSignBoard();
    }
  }
}
