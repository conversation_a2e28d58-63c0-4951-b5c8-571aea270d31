import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/home/<USER>/home_for_you_list_controller.dart';
import 'package:biz/biz/home/<USER>/home_for_you_list_widget.dart';
import 'package:biz/biz/home/<USER>';
import 'package:biz/biz/home/<USER>/home_style_tab_indicator.dart';
import 'package:biz/biz/home/<USER>/home_top_widget.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/common/getx/get_binding_builder.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/common/statistics/moment_statistics.g.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/moments/const/enums.dart';

import '../../global/widgets/app_bar/icon_btn.dart';
import '../ad/float_btn/float_btn.dart';
import '../ad/float_btn/no_scaling_animation.dart';
import '../moment/widgets/moment_publish_progress.dart';
import '../moment/widgets/moment_top_widget.dart';
import '../moment/widgets/monent_tab_page.dart';
import '../violation/violation_dialog.dart';
import 'nearby/nearby_page_widget.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HomePageState();
  }
}

class _HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  final HomePageController _controller = HomePageController();

  TabController? _tabController;

  final ScrollController scrollController = ScrollController();
  final MomentTopWidgetController _topWidgetController = MomentTopWidgetController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this, initialIndex: 0);

    _controller.tabController = _tabController;
    scrollController.addListener(() {
      Log.d("tag", "scrollController.offset = ${scrollController.offset}");

      // if (scrollController.offset < 50) {
      //   _topWidgetController.onMoveToTop();
      //   return;
      // }
      // double height = max(0, scrollController.offset);
      // _topWidgetController.onMove(height);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark,
      child: GetBindingBuilder(
        controller: _controller,
        child: GetBuilder(
          init: _controller,
          global: false,
          autoRemove: false,
          builder: (controller) {
            return Material(
              color: Colors.transparent,
              child: Stack(
                children: [
                  FLImage.asset(Res.homeBgPage, width: 1.w, fit: BoxFit.cover),
                  Scaffold(
                    backgroundColor: Colors.transparent,
                    body: SafeArea(
                      child: Stack(
                        children: [
                          Positioned(
                              top: 0,
                              left: 0,
                              right: 0,
                              child: Row(
                                children: [
                                  SizedBox(width: 10.pt),
                                  FLImage.asset(Res.homeIconLogo, width: 98.pt, fit: BoxFit.cover),
                                  Expanded(child: SizedBox.shrink()),
                                  _momentsNews(),
                                  ..._navBarActions()
                                ],
                              )),
                          Positioned(
                              top: 50.pt,
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: _bodyContent()),
                          Positioned(
                              bottom: 114.pt,
                              right: 18.pt,
                              child: MomentTopWidget(
                                  controller: _topWidgetController,
                                  callback: onMoveToTop))
                        ],
                      ),
                    ),
                    floatingActionButton: FloatBtn(
                      size: Size(56.pt, 56.pt),
                      type: FloatAdType.home,
                    ),
                    floatingActionButtonLocation: FloatBtnLocation(context),
                    floatingActionButtonAnimator: NoScalingAnimation(),
                  ),

                ],
              ),
            );
          },
        ),
      ),
    );
  }


  void onMoveToTop() {
    if (mounted && scrollController.hasClients == true) {
      scrollController.animateTo(
        scrollController.position.minScrollExtent ?? 0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
    }
  }

  ///新消息
  Widget _momentsNews() {
    return Obx(() => GestureDetector(
      child: Container(
          width: 44.pt,
          height: 44.pt,
          // color: Colors.orange,
          child: Stack(
            children: [
              Positioned(top: 9.pt, left: 15.pt, child: _noticeIcon()),
              Positioned(top: 4.pt, right: 0, child: _badgeWidget(_controller.noticeNum.value))
            ],
          )),
      onTap: () {
        /// 信箱点击打点上报
        MomentStatistics.reportTabMomentInboxClick(
            isNotified:
            (_controller.noticeNum.value == 0 ? ReportTabNotifyStatus.none : ReportTabNotifyStatus.notify));
        routerUtil.push(R_MOMENTS_NOTICES);
      },
    ));
  }

  /// 发布
  List<Widget> _navBarActions() {
    return [
      IconBtn(
        icon: Res.feedPublishEnter,
        onTap: () async {
          ///是否被封禁
          if ((await hasBeRestricted(context))) {
            return;
          }
          MomentStatistics.reportTabMomentSendClick(type: 'reco_list');
          routerUtil.pushWithAnimation(R_MOMENTS_PUBLISH,
              params: {P_STATISTIC_FROM: StatisticPageFrom.momentPost},
              duration: Duration(milliseconds: 240), animationBuilder:
                  (BuildContext context, Animation<double> animation, Animation secondaryAnimation, child) {
                const begin = Offset(0.0, 1.0);
                const end = Offset.zero;
                const curve = Curves.linear;
                final tween = Tween(begin: begin, end: end);

                final curvedAnimation = CurvedAnimation(
                  parent: animation,
                  curve: curve,
                );

                return SlideTransition(
                  position: tween.animate(curvedAnimation),
                  child: child,
                );
              });
        },
      )
    ];
  }

  Widget _badgeWidget(int badge) {
    if (badge <= 0) {
      return SizedBox();
    }

    final badgeWidth = 16.pt;

    return FittedBox(
      child: Container(
        width: badge >= 10 ? null : badgeWidth,
        height: badgeWidth,
        decoration: BoxDecoration(
          color: R.color.badgeBgColor,
          borderRadius: BorderRadius.circular(badgeWidth / 2),
        ),
        alignment: Alignment.center,
        padding: badge >= 10 ? EdgeInsets.symmetric(horizontal: 4.pt) : EdgeInsets.zero,
        child: Text(badge <= 99 ? '$badge' : '99+',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeightExt.heavy,
              fontSize: 11.sp,
            )),
      ),
    );
  }

  Widget _noticeIcon() {
    return Container(
      width: 26.pt,
      height: 26.pt,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(13.pt),
        color: Color(0x1AA3AFC2),
      ),
      child: FLImage.asset(Res.feedIconMsg, width: 23.pt, height: 23.pt),
    );
  }

  Widget _bodyContent() {
    return ExtendedNestedScrollView(
          controller: scrollController,
          onlyOneScrollInBody: true,
          headerSliverBuilder: (context, boxIsScrolled) {
            return [
              SliverOverlapAbsorber(
                handle: ExtendedNestedScrollView.sliverOverlapAbsorberHandleFor(context),
                sliver: SliverToBoxAdapter(
                  child: HomeTopWidget(),
                ),
              )
            ];
          },
          body: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollUpdateNotification && notification.metrics.axis == Axis.vertical) {
                final pixels = notification.metrics.pixels;
                Log.d("tag", "notification.metrics.pixels: ${notification.metrics.pixels}");
                if (pixels < 50) {
                  _topWidgetController.onMoveToTop();
                  return false;
                }
                double height = max(0, pixels);
                _topWidgetController.onMove(height);
              }
              return false;
            },
            child: _listContentView(),
        ));
  }

  Widget _listContentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_tabBar(), MomentPublishProgress(), Expanded(child: _tabBarView())],
    );
  }

  Widget _tabBar() {
    return _TabBarWidget(tabController: _tabController!);
  }

  Widget _tabBarView() {
    return TabBarView(controller: _tabController, children: [
      MomentTabPage(MomentsType.recommend, controller: _controller.recController, pScroll: scrollController),
      HomeForYouListWidget(
        controllerExternal: _controller.forYouCtl,
        type: HomeForYouType.forYou,
      ),
      HomeForYouListWidget(
        controllerExternal: _controller.forYouCtl,
        type: HomeForYouType.following,
      ),
      NearByPageWidget(
        controllerExternal: _controller.nearbyCtl,
      ),
      MomentTabPage(MomentsType.family, controller: _controller.recController, pScroll: scrollController),
    ]);
  }
}

class _TabBarWidget extends StatefulWidget {
  final TabController tabController;

  const _TabBarWidget({required this.tabController});

  @override
  State<_TabBarWidget> createState() => _TabBarWidgetState();
}

class _TabBarWidgetState extends State<_TabBarWidget> {
  @override
  Widget build(BuildContext context) {
    return TabBar(
        controller: widget.tabController,
        isScrollable: true,
        labelColor: R.color.color20,
        unselectedLabelColor: R.color.colorB2,
        unselectedLabelStyle: TextStyle(fontSize: 15.sp, fontWeight: FontWeightExt.bold),
        labelStyle: TextStyle(fontSize: 18.sp, fontWeight: FontWeightExt.black),
        indicatorPadding: EdgeInsets.only(bottom: 0.pt),
        indicatorSize: TabBarIndicatorSize.tab,
        labelPadding: EdgeInsets.symmetric(horizontal: 13.pt, vertical: 0),
        indicator: HomeStyledTabIndicator(
            margin: 0.pt,
            width: 20.pt,
            height: 4.pt,
            fillColor: R.color.primaryColor,
            onIndicatorChanged: () {
              WidgetUtils.post((duration) {
                if (mounted) {
                  setState(() {});
                }
              });
            }),
        tabs: [
          Tab(
            text: LocaleStrings.instance.forYou,
          ),
          Tab(
            text: LocaleStrings.instance.online,
          ),
          Tab(
            text: LocaleStrings.instance.following,
          ),
          Tab(
            text: LocaleStrings.instance.nearby,
          ),
          Tab(
            text: LocaleStrings.instance.family.capitalizeFirst,
          ),
        ]);
  }
}
