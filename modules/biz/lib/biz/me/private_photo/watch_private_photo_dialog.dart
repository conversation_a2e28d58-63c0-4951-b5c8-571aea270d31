


import 'package:biz/biz.dart';
import 'package:biz/global/widgets/buttons.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_util.dart' as dialog;

void showWatchPrivatePhotoDialog({
  VoidCallback? onSendGifts,
  VoidCallback? onToChat,
}) {
  dialog.showDialog( (_) => WatchPrivatePhotoDialog(
    onSendGifts: onSendGifts,
    onToChat: onToChat,
  ),);
}

class WatchPrivatePhotoDialog extends StatefulWidget {

  final VoidCallback? onSendGifts;
  final VoidCallback? onToChat;

  const WatchPrivatePhotoDialog({
    super.key,
    this.onSendGifts,
    this.onToChat,
  });

  @override
  State<WatchPrivatePhotoDialog> createState() => _WatchPrivatePhotoDialogState();
}

class _WatchPrivatePhotoDialogState extends State<WatchPrivatePhotoDialog> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(27.pt),
          color: Colors.white,
        ),
        margin: EdgeInsets.symmetric(horizontal: 30.pt),
        padding: EdgeInsets.symmetric(horizontal: 20.pt),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 18.pt),
            _title(),
            SizedBox(height: 20.pt),
            _icon(),
            SizedBox(height: 9.pt),
            _content(),
            SizedBox(height: 20.pt),
            Row(
              children: [
                Expanded(child: _sendGifts()),
                SizedBox(width: 15.pt),
                Expanded(child: _toChat()),
              ],
            ),
            SizedBox(height: 27.pt),
          ],
        ),
      ),
    );
  }

  _title() {
    return Text(
      LocaleStrings.instance.kindTips,
      style: TextStyle(
        fontSize: 21.pt,
        fontWeight: FontWeightExt.heavy,
        fontStyle: FontStyle.italic,
        color: R.color.textColor1,
      ),
    );
  }
  
  _icon() {
    return FLImage.asset(Res.profilePrivatePhotoAlertIcon, width: 145.pt, height: 115.pt);
  }

  _content() {
    return Text(
      LocaleStrings.instance.privatePhotoTipHelp,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 15.pt,
        fontWeight: FontWeightExt.medium,
        color: R.color.textBlueColor1,
      ),
    );
  }

  _sendGifts() {
    return GeneralBtn(
      height: 48.pt,
      title: LocaleStrings.instance.sendGifts,
      childColor: Colors.white,
      fontSize: 18.pt,
      fontWeight: FontWeightExt.heavy,
      gradient: LinearGradient(
        colors: [Color(0xFFFF4F97), Color(0xFFFF9DFC)],
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
      ),
      onTap: () {
        Navigator.of(context).pop();
        widget.onSendGifts?.call();
      },
    );
  }

  _toChat() {
    return GeneralBtn(
      height: 48.pt,
      title: LocaleStrings.instance.chat,
      childColor: R.color.primaryColor,
      fontSize: 18.pt,
      fontWeight: FontWeightExt.heavy,
      backgroundColor: R.color.primaryLightColor.withOpacity(0.12),
      onTap: () {
        Navigator.of(context).pop();
        widget.onToChat?.call();
      },
    );
  }
}
