
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/common/getx/get_abs_controller.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/profile_statistics.g.dart';
import 'package:service/modules/im/const/events.dart';
import 'package:service/modules/live/room_gift/model/send_gift_resp.dart';
import 'package:service/modules/user/model/user_private_photo_list.dart';
import 'package:service/service.dart';

import 'event.dart';
import 'watch_private_photo_dialog.dart';
class PrivatePhotoListController extends AbsGetController with GetStatusCtlMix {

  String targetUid = "";
  List<UserPrivatePhotoItem> privatePhotoList = [];
  final photoCount = 0.obs;
  String? matchType;
  String? from;
  bool canView = false;

  bool get isSelf => targetUid == accountService.getAccountInfo()?.uid;

  @override
  void stateInit() {
    super.stateInit();
    _initRx();
    _initData();
  }

  void _initRx() {
    listenRxEvent<int>(PrivatePhotoEvent.refreshList, _refreshData);
    // listenRxEvent<List<UserPrivatePhotoItem>>(PrivatePhotoEvent.updateList, _detailUpdateData);
    listenRxEvent<SendGiftResp>(ChatGiftEvent.sendSuccess, _giftSendSuccess);
  }

  void _initData() async {
    final resp = await userService.getPrivatePhotoList(fuid: targetUid);
    if (resp.isSuccess) {
      privatePhotoList.clear();
      canView = resp.data?.canView ?? false;
      if (resp.data?.list?.isNotEmpty == true) {
        privatePhotoList.addAll(resp.data!.list!);
      }
      photoCount.value = privatePhotoList.length;
    }
    update();
  }

  void _refreshData(int value) {
    _initData();
  }

  void _detailUpdateData(List<UserPrivatePhotoItem> list) {
    privatePhotoList.clear();
    privatePhotoList.addAll(list);
    photoCount.value = privatePhotoList.length;
    update();
  }

  void _giftSendSuccess(SendGiftResp value) {
    _initData();
  }

  void jumpDetail() {
    ProfileStatistics.reportPrivatePhotosView(
      toUid: targetUid,
      isUnlock: canView ? "1" : "0",
    );

    if (!isSelf && !canView) {
      showWatchPrivatePhotoDialog(
        onSendGifts: () {

          ProfileStatistics.reportPrivatePhotosLockPopClick(
            toUid: targetUid,
            type: "gift",
          );

          routerUtil.push(R_CHAT, params: {
            P_TARGET_ID: targetUid,
            P_CONVERSATION_TYPE: RCIMIWConversationType.private,
            P_STATISTIC_FROM: from,
            P_STATISTIC_MATCH_TYPE: matchType,
            P_CHECK_PRE_CHAT: true,
            P_SHOW_GIFT_PANEL: true,
          });
        },
        onToChat: () {

          ProfileStatistics.reportPrivatePhotosLockPopClick(
            toUid: targetUid,
            type: "chat",
          );

          routerUtil.push(R_CHAT, params: {
            P_TARGET_ID: targetUid,
            P_CONVERSATION_TYPE: RCIMIWConversationType.private,
            P_STATISTIC_FROM: from,
            P_STATISTIC_MATCH_TYPE: matchType,
            P_CHECK_PRE_CHAT: true,
          });
        }
      );
      return;
    }

    routerUtil.push(R_USER_PRIVATE_PHOTO, params: {
      P_TARGET_ID: targetUid,
    });
  }

}