import 'package:biz/biz/live_room/page/room_rank/room_rank_helper.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:biz/global/widgets/user_color_name_widget.dart';
import 'package:flutter/material.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/live/room_rank/model/room_rank_list_model.dart';
import 'package:service/service.dart';

/// 全栈排行榜顶部123的UI
class GameRankTop123Widget extends StatelessWidget {
  final List<RoomRankListItem> top3;

  GameRankTop123Widget({super.key, required this.top3});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double bgWidth = screenWidth - 16.pt - 18.pt;
    double bgHeight = bgWidth * 322 / 780;

    return Container(
      height: bgHeight,
      child: St<PERSON>(
        alignment: Alignment.topCenter,
        children: [
          FLImage.asset(
            Res.gameCenterTop123Bg,
            width: bgWidth,
            height: bgHeight
          ),
          ..._buildTop1(top3.getSafeElement(0), context),
          ..._buildTop2(top3.getSafeElement(1), context),
          ..._buildTop3(top3.getSafeElement(2), context),
        ],
      ),
    );
  }

  List<Widget> _buildTop1(RoomRankListItem? item, BuildContext context) {
    if (item == null) return [];
    return  [
      PositionedDirectional(
        top: 10.pt,
        child: UserAvatar(
          url: item.headimgurl,
          size: 50.pt,
          userId: item.uid,
          // false,不去加载，使用当前接口下发的rid（实时性）
          loadRoomStatus: false,
          showRoomStatus: item.roomId?.isNotEmpty == true,
          hasFrame: true,
          inRoom: true,
          roomFontSize: 8.pt,
          onTap: ({rid}) {
            _onTapAvatar(item.uid, item.roomId, context);
          },
        ),
      ),
      PositionedDirectional(
        top: 82.pt,
        child: Column(
          children: [
            SizedBox(height: 5.pt),
            Container(
              constraints: BoxConstraints(maxWidth: 110.pt),
              child: UserColorNameWidget(
                name: item.nickname.fixAutoLines(),
                uid: item.uid,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                  height: 1.4,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            SizedBox(height: 2.pt),
            Text(
              '${item.contribution}',
              style: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 11.pt,
                fontWeight: FontWeightExt.medium,
                height: 1.3,
              ),
            )
          ],
        ),
      ),
    ];
  }

  List<Widget> _buildTop2(RoomRankListItem? item, BuildContext context) {
    if (item == null) return [];
    return [
      PositionedDirectional(
        start: 0.pt,
        top: 38.pt,
        width: 116.pt,
        child: UserAvatar(
          url: item.headimgurl,
          size: 35.pt,
          userId: item.uid,
          loadRoomStatus: false,
          showRoomStatus: item.roomId?.isNotEmpty == true,
          hasFrame: true,
          inRoom: true,
          roomFontSize: 8.pt,
          onTap: ({rid}) {
            _onTapAvatar(item.uid, item.roomId, context);
          },
        ),
      ),
      PositionedDirectional(
        top: 90.pt,
        start: 0,
        width: 116.pt,
        child: _build23(item, context),
      ),
    ];
  }

  List<Widget> _buildTop3(RoomRankListItem? item, BuildContext context) {
    if (item == null) return [];
    return [
      PositionedDirectional(
        end: 0.pt,
        top: 38.pt,
        width: 116.pt,
        child: UserAvatar(
          url: item.headimgurl,
          size: 35.pt,
          userId: item.uid,
          // false,不去加载，使用当前接口下发的rid（实时性）
          loadRoomStatus: false,
          showRoomStatus: item.roomId?.isNotEmpty == true,
          hasFrame: true,
          inRoom: true,
          roomFontSize: 8.pt,
          onTap: ({rid}) {
            _onTapAvatar(item.uid, item.roomId, context);
          },
        ),
      ),
      PositionedDirectional(
        top: 90.pt,
        end: 0,
        width: 116.pt,
        child: _build23(item, context),
      ),
    ];
  }

  Widget _build23(RoomRankListItem item, BuildContext context) {
    return Column(
      children: [
        // SizedBox(height: 3.pt),
        Container(
          constraints: BoxConstraints(maxWidth: 90.pt),
          child: UserColorNameWidget(
            name: item.nickname.fixAutoLines(),
            uid: item.uid,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
              height: 1.4,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        SizedBox(height: 2.pt),
        Text(
          '${item.contribution}',
          style: TextStyle(
            color: Colors.white.withOpacity(0.5),
            fontSize: 11.pt,
            fontWeight: FontWeightExt.medium,
            height: 1.3,
          ),
        )
      ],
    );
  }

  void _onTapAvatar(String uid, String? rid, BuildContext context) {
    routerUtil.pop(context: context);
    jumpRankAvatar(uid: uid, rid: rid);
  }
}
