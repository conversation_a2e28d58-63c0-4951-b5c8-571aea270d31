import 'package:biz/biz/game_center/tabs/jack_pot/jackpot_tab_page.dart';
import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/widget/keep_wrapper.dart';
import 'package:service/service.dart';

import 'dialog/game_rule_dialog.dart';
import 'game_center_controller.dart';
import 'tabs/rank/game_rank_widget.dart';
import 'widget/game_list_item_widget.dart';

@FRoute(desc: '游戏中心', url: R_GAME_CENTER)
class GameCenterPage extends StatefulWidget {
  const GameCenterPage({super.key});

  @override
  State<GameCenterPage> createState() => _GameCenterPageState();
}

class _GameCenterPageState extends State<GameCenterPage>
    with GetStateBuilderMixin<GameCenterPage, GameCenterController>, TickerProviderStateMixin {
  VoidCallback? get onTapError => getCtl.loadData;

  VoidCallback? get onTapEmpty => getCtl.loadData;

  bool get isDart => true;

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(body: _buildBody(context)),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Stack(
      children: [
        FLImage.asset(
          Res.gameCenterBgMain,
          width: 1.w,
          height: 1.h,
          fit: BoxFit.cover,
        ),
        SafeArea(
          bottom: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              10.hSpace,
              _buildTitleBar(),
              Expanded(child: buildGetWidget(context)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTitleBar() {
    return Row(
      children: [
        18.wSpace,
        GestureDetector(
          child: FLImage.svg(Res.commonBackIconCircle, width: 33.pt, height: 33.pt),
          onTap: () {
            Navigator.pop(context);
          },
        ),
        Spacer(),
        Text(LocaleStrings.instance.gameCenter,
            style: TextStyle(color: Colors.white, fontSize: 21.pt, fontWeight: FontWeightExt.heavy)),
        Spacer(),
        _buildHelp(),
        18.wSpace,
      ],
    );
  }

  Widget _buildHelp() {
    return GestureDetector(
      onTap: GameRuleDialog.showGameRuleDialog,
      child: FLImage.asset(
        Res.commonHelp3,
        width: 33.pt,
        height: 33.pt,
      ),
    );
  }

  @override
  Widget buildStatusContent(BuildContext context) {
    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return [
          SliverPadding(
            padding: EdgeInsets.only(top: 30.pt),
            sliver: SliverFixedExtentList(
              itemExtent: 84.pt,
              delegate: SliverChildBuilderDelegate(
                (_, index) {
                  var item = getCtl.gameList.elementAt(index);
                  return GameListItemWidget(
                    item: item,
                    onTap: () => getCtl.onTapGameListItem(index, item),
                  );
                },
                childCount: getCtl.gameList.length,
              ),
            ),
          )
        ];
      },
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTabs(),
          Flexible(child: _tabBarView(context)),
        ],
      ),
    );
  }

  @override
  GameCenterController initCtl() {
    return GameCenterController(this);
  }

  Widget _buildTabs() {
    return Container(
      height: 41.pt,
      margin: EdgeInsets.only(top: 18.pt),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [Color(0xFF822DD9), Color(0xFF1A1A33)]),
        borderRadius: BorderRadius.circular(21.pt),
        border: Border.all(color: Color(0xFFA465FF), width: 1.pt),
      ),
      child: TabBar(
        controller: getCtl.tabController,
        isScrollable: true,
        labelStyle: TextStyle(
          fontSize: 16.pt,
          fontWeight: FontWeightExt.black,
          fontFamily: R.primaryFontFamily,
          fontStyle: FontStyle.italic,
        ),
        labelColor: Colors.white,
        unselectedLabelStyle: TextStyle(
          fontSize: 16.pt,
          fontWeight: FontWeightExt.heavy,
          fontFamily: R.primaryFontFamily,
          fontStyle: FontStyle.italic,
        ),
        unselectedLabelColor: Color(0x80FFFFFF),
        tabs: [
          Tab(text: LocaleStrings.instance.jackPot),
          Tab(text: LocaleStrings.instance.rank),
        ],
        onTap: (index) {
          getCtl.onTabChange(index);
        },
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF9C6CFF),
              Color(0xFF633AFE),
            ],
          ),
          borderRadius: BorderRadius.circular(19.pt),
        ),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
      ),
    );
  }

  Widget _tabBarView(BuildContext context) {
    if (getCtl.tabController == null) {
      return SizedBox.shrink();
    }

    double screenWidth = MediaQuery.of(context).size.width;
    double gameWidth = screenWidth - 18.pt;
    double height = 35.pt + gameWidth + 20.pt + 80.pt + 9.pt;

    return SizedBox(
      height: height,
      child: ExtendedTabBarView(
        controller: getCtl.tabController,
        children: [
          JackpotTabPage(),
          GameRankWidget(),
        ].map((e) => KeepWrapper(child: e)).toList(growable: false),
      ),
    );
  }
}
