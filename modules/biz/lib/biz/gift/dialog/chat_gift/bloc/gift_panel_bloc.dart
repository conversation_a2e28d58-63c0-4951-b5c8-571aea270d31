import 'dart:async';
import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/biz/chat/chat_details/widgets/disturb_alert_dialog.dart';
// import 'package:biz/biz/finance/recharge_reward/recharge_reward_dialog.dart';
import 'package:biz/biz/gift/dialog/chat_gift/select_user/select_gift_user_widget.dart';
import 'package:biz/biz/gift/util/gift_statistics_mixin.dart';
import 'package:biz/biz/gift/util/num_pop_menu.dart';
import 'package:biz/biz/violation/violation_dialog.dart';
import 'package:biz/common/widgets/pop_menu/show_more_text_popup.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:biz/global/widgets/function_guid/function_guid.dart';
import 'package:biz/global/widgets/function_guid/step_widget_builder.dart';
import 'package:biz/global/widgets/no_enough_money_dialog.dart';
import 'package:biz/route/page_time_mixin.dart';
import 'package:flutter/material.dart';
import 'package:service/common/statistics/const/const.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/alert_dialog.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/finance/const/events.dart';
import 'package:service/modules/finance/model/wallet.dart';
import 'package:service/modules/gift/const/enums.dart';
import 'package:service/modules/gift/const/events.dart';
import 'package:service/modules/gift/handler/gift_cache_manager.dart';
import 'package:service/modules/gift/model/gift.dart';
import 'package:service/modules/gift/model/gift_pb_msg.dart';
import 'package:service/modules/gift/model/gift_tab_config.dart';
import 'package:service/modules/mall/const/enums.dart';
import 'package:service/modules/mall/model/goods_detail.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/pb/net/pb_custom_im.pb.dart';
import 'package:service/service.dart';
import 'package:service/utils/loading.dart';

part 'gift_panel_event.dart';

part 'gift_panel_state.dart';

class GiftPanelBloc extends BaseBloc<GiftPanelEvent, GiftPanelState>
    with PageTimingMixin, GiftStatisticsMixin {
  GiftPanelBloc(
      {required this.targetUid,
      required this.tickerProvider,
      this.isNewChat = false,
      this.isUnlock = false,
      this.isCpGift = false,
      this.pageFrom,
      this.onlyShopHighGift = false, this.groupId})
      : super(GiftPanelState()) {
    on<GiftPanelEventSelectNum>(_selectNum);
    on<GiftPanelEventSend>((_, __) => _send());
    on<GiftPanelEventSelect>(_selectGoods);
    on<GiftPanelEventData>((event, emitter) {
      emitter.call(state.clone(
          targetUser: event.targetUser,
          wallet: event.wallet,
          showGuide: event.showGuide,
          showUnlockGuide: event.showUnlockGuide,
          canSale: event.canSale,
          tabIndex: event.tabIndex,
          targetUidList: event.targetUidList));
    });
    on<GiftPanelEventExtraTab>(
        (event, emitter) => emitter.call(state.clone(extraTabs: event.tabs)));
    on<ShowTagDescEvent>(_showTag);
    on<GiftPanelEventSelectUser>((event, __) => showSelectUser());
  }

  final String targetUid;

  final String? groupId;

  final bool isCpGift;
  final bool? isNewChat;
  final bool? isUnlock;
  final bool onlyShopHighGift;

  final TickerProvider tickerProvider;

  /// im类型
  bool get isPrivateChat=> groupId?.isEmpty ?? true;

  TabController? tabController;

  String get pageKey => "gift_panel";

  Guid? guid;

  String? pageFrom;

  int get tabNum => giftPanelTabs.length;

  bool get inAvatarTab => giftPanelTabs.isNotEmpty
      ? (giftPanelTabs[tabController?.index ?? 0].type == GiftTabType.avatar)
      : false;

  List<GiftTabModel> giftPanelTabs = [];

  /// 是否点击过发送按钮
  bool hasTapSend = false;

  GlobalKey selectNumKey = GlobalKey();
  ShowMorePopup? _popupSelectUser;
  GlobalKey selectUserKey = GlobalKey();

  @override
  void initBloc() {
    super.initBloc();

    from = _from();
    currentSelectUid = targetUid;

    startTiming(key: pageKey);

    _initTab();

    listenRxEvent(FinanceEvent.walletChange, _walletChange);

    listenRxEvent(GiftPanelEvents.cancel, (value) {
      var context = getContext();
      if (context != null) {
        Navigator.of(context).pop();
      }
    });

    _initData();

    reportPanelShow(GiftTypeStatistic.shopGifts);
  }

  @override
  void dispose() {
    _popupSelectUser?.dismiss();
    GiftNumPopMenu.instance().dispose();
    _reportGiftBoardDuration();
    if (!hasTapSend && !isCpGift) {
      // showGiftBagDialog(from: "giftclose");
    }
    super.dispose();
  }

  /// 初始化标签栏
  void _initTab() async {
    giftPanelTabs.addAll(await giftService.getGiftPanelTabs());

    /// 高价值解锁面板：过滤背包
    if (onlyShopHighGift) {
      giftPanelTabs
          .removeWhere((element) => element.type == GiftTabType.backpack);
    }

    /// 是否cp送礼面板：过滤头像框
    if (isCpGift) {
      giftPanelTabs
          .removeWhere((element) => element.type == GiftTabType.avatar);
    }

    var tabInfo = giftService.getLastSelectGiftTab();

    int initialIndex = getInitIndex(tabInfo);

    tabController = TabController(
        length: giftPanelTabs.length,
        vsync: tickerProvider,
        initialIndex: initialIndex);
    add(GiftPanelEventData(tabIndex: initialIndex));
    tabController?.addListener(() {
      add(ShowTagDescEvent(gift: null));
      if (tabController?.indexIsChanging == true) return;

      var index = tabController?.index ?? initialIndex;
      add(GiftPanelEventData(tabIndex: index));

      reportPanelShow(inAvatarTab
          ? GiftTypeStatistic.avatarGifts
          : index > (onlyShopHighGift ? 0 : 1)
              ? GiftTypeStatistic.shopGifts
              : GiftTypeStatistic.backpackGifts);

      setSelectTab(index);
    });

    add(GiftPanelEventExtraTab(giftPanelTabs));
  }

  /// 初始化tab index
  int getInitIndex(GiftTabInfo tabInfo) {
    if (giftPanelTabs.isEmpty) return 0;

    int initialIndex = max(
        giftPanelTabs.indexWhere((element) => element.type == GiftTabType.gift),
        0);

    if (tabInfo.giftsType == GiftTabType.avatar && !isCpGift) {
      initialIndex = max(
          giftPanelTabs
              .indexWhere((element) => element.type == GiftTabType.avatar),
          0);
    } else if (tabInfo.giftsType.isNotEmpty &&
        tabInfo.tag.isNotEmpty &&
        giftPanelTabs.isNotEmpty) {
      initialIndex = giftPanelTabs.indexWhere((element) =>
          element.type == tabInfo.giftsType && element.tag == tabInfo.tag);
    }

    if (initialIndex < 0) {
      initialIndex = max(
          giftPanelTabs
              .indexWhere((element) => element.type == GiftTabType.gift),
          0);
    }
    return initialIndex;
  }

  /// 记录选中的tab
  void setSelectTab(int index) {
    var giftTab = giftPanelTabs[index];

    Map<int, Good> map = {}..addAll(state.selected);
    bool canSale = index == 0 ? true : map[index]?.canSale ?? true;
    add(GiftPanelEventData(canSale: canSale));
    giftService.selectGiftTab(
        giftTabInfo:
            GiftTabInfo(giftTab.type ?? GiftTabType.gift, giftTab.tag ?? ""));
  }

  void _initData() async {
    /// 我的钱包
    var wallet = await financeService.getMyWallet();
    bool showGuide = false;
    bool showUnlockGuide = false;

    /// 是否展示新手引导
    if (isUnlock == false) {
      showGuide = GiftCacheManager.instance().isFirstOpenPanel();
      if (showGuide) {
        guid = Guid(
            noAnimation: false,
            stepCount: 1,
            maskClosable: true,
            padding: EdgeInsets.all(8.pt),
            borderRadius: BorderRadius.all(Radius.circular(9.pt)),
            onNextStepTap: (_) async {},
            widgetBuilder: StepWidgetBuilder.useDefaultTheme(texts: [
              LocaleStrings.instance.giftSendGuid,
            ], showBtn: false));

        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          Future.delayed(Duration(milliseconds: 500), () {
            var context = getContext();
            if (context != null) {
              guid?.start(context);
            }
          });
        });
      }
    } else {
      showUnlockGuide = GiftCacheManager.instance().isFirstOpenUnlockPanel();
    }

    UserInfo? userInfo;
    List<String>? uidList;

    if (!isPrivateChat) {
      uidList = (await imCacheService.getGroupInfo(targetId: groupId ?? ""))
              ?.userList
              ?.split(",") ??
          [];

      var lastSelectUid = globalSp.getString('$spKeyGroupChatGiftLastUid$groupId') ?? "";

      if(lastSelectUid.isNotEmpty && uidList.contains(lastSelectUid)){
        currentSelectUid = lastSelectUid;
      }

      if (uidList.isNotEmpty && currentSelectUid.isEmpty) {
        currentSelectUid = uidList[0];
      }
    }

    if (currentSelectUid.isNotEmpty) {
      userInfo = await userService.getUserInfo(currentSelectUid);
    }

    add(GiftPanelEventData(
        targetUser: userInfo,
        targetUidList: uidList,
        wallet: wallet,
        showGuide: showGuide,
        showUnlockGuide: showUnlockGuide));

    if (showGuide) {
      GiftCacheManager.instance().setFirstOpenPanel();
    }
    if (showUnlockGuide) {
      GiftCacheManager.instance().setFirstOpenUnlockPanel();
    }
  }

  /// 当前选择的礼物id
  String? _currentSelectId() {
    var good = state.selected[tabController?.index ?? 0];

    if (good == null) return null;

    switch (good.runtimeType) {
      case GiftInfo:
        return (good as GiftInfo).id;
      case GoodsDetail:
        return (good as GoodsDetail).id;
      case BackpackGift:
        return (good as BackpackGift).info?.id;
    }

    return null;
  }

  /// 来源
  String? _from() {
    if(groupId?.isNotEmpty ?? false) return GiftTypeStatistic.group;

    if (onlyShopHighGift) return GiftTypeStatistic.unlockHighIm;
    if (isCpGift) return GiftTypeStatistic.loveZone;
    return (isUnlock ?? false)
        ? GiftTypeStatistic.unlockIm
        : GiftTypeStatistic.im;
  }

  /// 钱包变动
  void _walletChange(Wallet wallet) {
    add(GiftPanelEventData(wallet: wallet));
  }

  /// 所有商城礼物面板选中商品
  void _selectGoods(GiftPanelEventSelect event, Emitter emitter) {
    var good = event.good;
    if (good == null) return;
    bool canSale = good is BackpackGift ? true : good.canSale;

    Map<int, Good> map = {}..addAll(state.selected);
    map[event.tabIndex] = good;
    emitter.call(state.clone(selected: map, canSale: canSale));
    switch (good.runtimeType) {
      case GiftInfo:
        good as GiftInfo;
        if ((good.tagDesc?.isEmpty ?? true) || (good.tag?.isEmpty ?? true)) {
          emitter.call(
            state.cloneTag(selectedTag: null),
          );
        }
        reportSelectGift(GiftTypeStatistic.shopGifts,
            giftInfo: good, selectNum: state.selectNum);
        break;
      case GoodsDetail:
        reportSelectGift(GiftTypeStatistic.avatarGifts,
            goods: good as GoodsDetail, selectNum: 1, batchType: 1);
        break;
      case BackpackGift:
        good as BackpackGift;
        if (good.info != null) {
          reportSelectGift(GiftTypeStatistic.backpackGifts,
              giftInfo: good.info!, selectNum: state.selectNum);
        }
        break;
    }
  }

  /// 选择数量
  void _selectNum(GiftPanelEventSelectNum event, Emitter emitter) {
    emitter.call(state.clone(selectNum: event.num));
    reportGiftBoardNum(_currentSelectId(), event.num,
        style: "${tabController?.index}");
  }

  /// 发送礼物
  Future<GiftPanelState> _send() async {
    hasTapSend = true;

    ///是否被封禁
    if ((await hasBeRestricted(FLRouter.routeObserver.getLastContext()))) {
      return state;
    }

    ///检查骚扰消息条数限制
    if (!(isCpGift) && isPrivateChat &&
        await checkDisturbMsg(currentSelectUid, await getTargetUser())) {
      return state;
    }

    /// 拉黑
    else if ((await getTargetUser())?.isBlocked == true) {
      toast(LocaleStrings.instance.failedToSend);
      return state;
    }

    Good? good = state.selected[tabController?.index ?? 0];

    switch (good.runtimeType) {
      case BackpackGift:
        return await _sendBackpackGift(good as BackpackGift);
      case GiftInfo:
      case GoodsDetail:
        return await _sendShopGiftWithCheck(good!);
    }

    return state;
  }

  /// 发送商店礼物-检查余额
  Future<GiftPanelState> _sendShopGiftWithCheck(Good goods) async {
    GiftInfo? selectedGift;
    GoodsDetail? selectedGoods;

    switch (goods.runtimeType) {
      case GiftInfo:
        selectedGift = goods as GiftInfo;
        break;
      case GoodsDetail:
        selectedGoods = goods as GoodsDetail;
        break;
    }

    if (selectedGoods == null && selectedGift == null) return state;

    /// 礼物数量
    int selectNum = inAvatarTab ? 1 : state.selectNum ?? 1;

    int price = selectedGoods?.price ?? selectedGift?.price ?? 1;

    int total = selectNum * (price);

    reportSendClick(
      selectedGoods != null
          ? GiftTypeStatistic.avatarGifts
          : GiftTypeStatistic.shopGifts,
      selectNum,
      giftInfo: selectedGift,
      goodsDetail: selectedGoods,
    );

    if (state.wallet != null &&
        !(await checkMyWallet(
            price: total,
            currencyType: selectedGoods?.currencyType ??
                selectedGift?.currencyType ??
                CurrencyType.diamond))) {
      _showNoEnoughMoney(total, goods.currencyType,
          selectedGift: selectedGift, selectedGoods: selectedGoods);
      return state;
    }

    var checkCount = GiftCacheManager.instance().getConfirmBalance();

    if (checkCount > 0 && total > checkCount) {
      reportSendGiftConfirmShow(total);
      showAlertDialog(
          context: FLRouter.routeObserver.getLastContext(),
          content:
              LocaleStrings.instance.spendCoinsToSendGift(selectNum * (price)),
          confirmText: LocaleStrings.instance.yes,
          confirmTextColor: R.color.primaryDeepColor,
          cancelText: LocaleStrings.instance.no,
          cancelTextColor: R.color.primaryColor,
          barrierDismissible: false,
          onConfirm: () {
            if (selectedGoods != null) {
              _sendGoodsGift(total, selectNum, selectedGoods);
            } else if (selectedGift != null) {
              _sendShopGift(total, selectNum, selectedGift);
              reportSendGiftConfirm(selectedGift, total);
            }
          },
          priority: 1);
      return state;
    }

    if (selectedGoods != null) {
      await _sendGoodsGift(total, selectNum, selectedGoods);
    } else if (selectedGift != null) {
      await _sendShopGift(total, selectNum, selectedGift);
    }

    return state;
  }

  /// 发送商店类型礼物
  Future<void> _sendGoodsGift(
      int total, int selectNum, GoodsDetail goods) async {
    showLoading();
    var resp = await mallService.sendGoodsToFriend(
        goods.type ?? "", goods.id ?? "", currentSelectUid,
        goods: goods, inGiftPanel: true, groupId: groupId,isDiscount: goods.isDiscount);
    hideLoading();
    if (resp.isSuccess) {
    } else if (resp.code == 1040) {
      toast(LocaleStrings.instance.userHasItem);
    } else {
      toast(resp.msg ?? LocaleStrings.instance.pleaseTryAgain);
    }

    if (resp.isSuccess) {
      status = 1;
      routerUtil.removeAll(R_CHAT_GIFT);
      return;
    }
    // sendGiftFail(resp.code, msg: resp.msg, goods: goods, roomId: roomId, from: from);
  }

  /// 发送礼物
  Future<void> _sendShopGift(
      int total, int selectNum, GiftInfo selectedGift) async {
    reportSendGiftStart("buy_send", selectNum, giftInfo: selectedGift);

    DateTime startTime = DateTime.now();
    showLoading();
    FlatHttpResponse? resp = await giftService.sendGift(
          groupId: groupId,
          count: selectNum,
          targetUid: currentSelectUid,
          giftId: selectedGift.id ?? "",
          type: GiftTabType.avatar);

    hideLoading();
    String endTime =
        DateTime.now().difference(startTime).inMilliseconds.toString();

    reportSendGiftEnd("buy_send", selectNum, resp, endTime,
        giftInfo: selectedGift);
    if (resp?.isSuccess ?? false) {
      _sendCpGiftBus(selectedGift, selectNum);
      _addDisturb();
      status = 1;
      var context = getContext();
      if (context != null) {
        Navigator.of(context).pop();
      }
    }

    /// 金币不足
    else if (resp?.code == 1019) {
      _showNoEnoughMoney(total, selectedGift.currencyType,
          selectedGift: selectedGift);
    }

    /// 单日送礼数上限
    else if (resp?.code == 1021) {
      toast(LocaleStrings.instance.giftUpperLimit);
    }

    /// 被拉黑
    else if (resp?.code == 1004) {
      toast(LocaleStrings.instance.failedToSend);
    } else {
      toast(resp?.msg ?? LocaleStrings.instance.failedToSend);
    }
  }

  /// 金币不足弹窗
  void _showNoEnoughMoney(int total, CurrencyType currencyType,
      {GiftInfo? selectedGift, GoodsDetail? selectedGoods}) {
    reportGiftNoMoney(total,
        giftInfo: selectedGift,
        goods: selectedGoods,
        style:
            "${onlyShopHighGift ? (tabController?.index ?? 0) + 1 : tabController?.index}");

    showNoEnoughMoneyDialog(
      onConfirm: () {
        if (isUnlock ?? false) {
          var context = getContext();
          if (context != null) {
            Navigator.of(context).pop();
          }
          routerUtil.removeAll(R_CHAT);
        }
      },
      from: NoMoneyTypeStatistic.gift,
      currencyType: currencyType,
    );
  }

  /// 发送背包礼物
  Future<GiftPanelState> _sendBackpackGift(
      BackpackGift selectedBackpack) async {
    /// 背包礼物
    GiftInfo? giftInfo = selectedBackpack.info;

    /// 发送数量
    int selectNum = state.selectNum ?? 1;

    if (selectedBackpack.bagId?.isEmpty ?? true) return state;
    if (giftInfo == null) return state;

    /// 上报点击发送打点
    reportSendClick(GiftTypeStatistic.backpackGifts, selectNum,
        giftInfo: giftInfo);

    /// 背包礼物不足
    if ((selectedBackpack.count ?? 0) < selectNum) {
      toast(LocaleStrings.instance.quantityShortage);
      return state;
    }

    reportSendGiftStart("send", selectNum, giftInfo: giftInfo);
    DateTime startTime = DateTime.now();
    showLoading();
    FlatHttpResponse? resp = await giftService.sendGift(
        targetUid: currentSelectUid,
        groupId:  groupId,
        count: selectNum,
        bagId: selectedBackpack.bagId ?? "",
        giftId: giftInfo.id ?? "",
        type: GiftTabType.backpack);
    hideLoading();
    String endTime =
        DateTime.now().difference(startTime).inMilliseconds.toString();

    reportSendGiftEnd("send", selectNum, resp, endTime, giftInfo: giftInfo);
    if (resp?.isSuccess ?? false) {
      _sendCpGiftBus(giftInfo, selectNum);
      _addDisturb();
      status = 1;
      var context = getContext();
      if (context != null) {
        Navigator.of(context).pop();
      }
    } else {
      toast(resp?.msg ?? LocaleStrings.instance.failedToSend);
    }
    return state;
  }

  /// 如果是cp礼物，自己发送礼物数据
  void _sendCpGiftBus(GiftInfo? giftInfo, int selectNum) {
    if (isCpGift && giftInfo != null) {
      rxUtil.send(
          GiftEvents.cpGiftMsg,
          GiftPbMsg(
              targetId: currentSelectUid,
              pbImGift: PbImGift()
                ..iconUrl = giftInfo.icon ?? ''
                ..count = selectNum
                ..id = giftInfo.id ?? ''
                ..name = giftInfo.name ?? ''
                ..level = giftInfo.level ?? 0
                ..fileUrl = giftInfo.filePath ?? ''
                ..videoUrl = giftInfo.videoAnimation ?? ''));
    }
  }

  /// 显示礼物标签说明
  void _showTag(ShowTagDescEvent event, Emitter emitter) async {
    emitter.call(state.cloneTag(
        selectedTagSize: event.size,
        selectedTagOffset: event.offset,
        selectedTag: event.gift));
  }

  ///骚扰限制增加记录
  void _addDisturb() async {
    ///是否新会话
    if (isNewChat ?? false) {
      await disturbService.startNewChat(
          currentSelectUid, pageFrom ?? StatisticPageFrom.chatList);
    }
    disturbService.sendMsg(currentSelectUid, null);
  }

  /// 打点：赠送成功或关闭礼物面板，礼物面板停留时长
  void _reportGiftBoardDuration() async {
    stopTiming(key: pageKey);
    String onTime = getOnTime(key: pageKey);
    removeTimer(key: pageKey);
    reportGiftBoardDuration(onTime);
  }

  void showSelectUser() {
    var context = getContext();
    if (context == null) return;
    if (state.targetUidList?.isEmpty ?? true) return;

    _popupSelectUser = ShowMorePopup(context,
        child: SelectGiftUserWidget(
            uidList: []..addAll(state.targetUidList ?? []),
            selectedUid: state.targetUser?.uid,
            selectCallBack: (user) {
              currentSelectUid = user.uid;
              globalSp.setString('$spKeyGroupChatGiftLastUid$groupId', user.uid);
              add(GiftPanelEventData(targetUser: user));
              _popupSelectUser?.dismiss();
            }),
        width: 160.pt,
        bottom: 55.pt,
        padding: EdgeInsets.zero,
        backgroundColor: Color(0xFFF2F2F2),
        border: Border.all(color: Color(0xFFC2A1E4), width: 1.pt),
        showShadow: false,
        isDownArrow: false,
        borderRadius: BorderRadius.circular(10.pt));
    _popupSelectUser?.show(widgetKey: selectUserKey);
  }

  void showNumPop(BuildContext context) {
    final good = state.selected.isNotEmpty == true
        ? state.selected[tabController?.index ?? 0]
        : null;
    GiftNumPopMenu.instance().show(context,
        widgetKey: selectNumKey,
        backgroundColor: Color(0xFFF2F2F2),
        textColor: R.color.primaryColor,
        showBorder: false,
        showEdit: true,
        colorEdit: R.color.primaryDeepColor,
        // editTap: () => showRoomGiftEditNumDialog(callback: (num) {
        //       if (num > 0) {
        //         add(GiftPanelEventSelectNum(num));
        //       }
        //     }),
        // listNum: good is GiftInfo && good.giftType == GiftType.blindbox
        //     ? [17, 7, 1]
        //     : null,
        // selectCallback: (value) => add(GiftPanelEventSelectNum(value))
      );
  }
}
