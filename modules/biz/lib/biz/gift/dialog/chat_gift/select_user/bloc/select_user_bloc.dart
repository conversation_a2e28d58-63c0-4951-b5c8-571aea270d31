import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/global/bloc/base_bloc.dart';
import 'package:meta/meta.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/modules/user/model/user_info.dart';

part 'select_user_event.dart';

part 'select_user_state.dart';

class SelectUserBloc extends BaseBloc<SelectUserEvent, SelectUserState> {
  SelectUserBloc({this.uidList}) : super(SelectUserState()) {
    on<SelectUserEventData>((event, emitter) =>
        emitter.call(state.clone(list: event.list, uidList: event.uidList)));
    on<LoadMore>((_, __) => _loadMore());
  }

  final List<String>? uidList;

  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  final pageSize = 20;

  @override
  void initBloc() {
    super.initBloc();

    _init();
  }

  void _init() async {
    if (uidList?.isEmpty ?? true) return;
    List<String> list = []
      ..addAll((uidList ?? []).getRange(0, min(uidList?.length ?? 0, pageSize)));
    List<UserInfo> users = await userService.getUserInfoList(list);
    users.sort((a, b) => list.indexOf(a.uid).compareTo(list.indexOf(b.uid)));
    add(SelectUserEventData(
        list: users,
        uidList: uidList
          ?..removeRange(0, min(uidList?.length ?? 0, pageSize))));
  }

  /// 加载更多
  void _loadMore() async {
    if (state.uidList?.isEmpty ?? true) {
      refreshController.loadNoData();
      refreshController.loadComplete();
      return;
    }

    List<String> uidList = []..addAll((state.uidList ?? [])
        .getRange(0, min(state.uidList?.length ?? 0, pageSize)));

    List<UserInfo> users = await userService.getUserInfoList(uidList);
    users.addAll(state.list ?? []);
    users.sort((a, b) => uidList.indexOf(a.uid).compareTo(uidList.indexOf(b.uid)));

    add(SelectUserEventData(
        list: users,
        uidList: state.uidList
          ?..removeRange(0, min(state.uidList?.length ?? 0, pageSize))));

    refreshController.loadComplete();
  }
}
