import 'dart:math';

import 'package:biz/biz.dart';
import 'package:biz/common/bloc/lifecycle_bloc_builder.dart';
import 'package:biz/global/widgets/global_widgets.dart';
import 'package:biz/global/widgets/user_avatar/user_avatar.dart';
import 'package:feature_widgets_nullsafety/feature_widget.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/modules/user/model/user_info.dart';
import 'package:service/service.dart';

import 'bloc/select_user_bloc.dart';

class SelectGiftUserWidget extends StatelessWidget {
  final List<String>? uidList;
  final String? selectedUid;
  final Function(UserInfo)? selectCallBack;

  SelectGiftUserWidget({this.uidList, this.selectCallBack, this.selectedUid})
      : _bloc = SelectUserBloc(uidList: uidList);

  final SelectUserBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return LifecycleBlocBuilder<SelectUserBloc, SelectUserState>(
      bloc: _bloc,
      builder: (_, state) => _page(),
    );
  }

  Widget _page() {
    var list = _bloc.state.list ?? [];

    var rest = <Widget>[];
    for (int i = 0; i < list.length; i++) {
      rest.add(_item(list[i], 160.pt, showBorder: i > 0, callback: () {
        selectCallBack?.call(list[i]);
      }));
    }

    double height = rest.length * 40.pt;
    return Container(
      clipBehavior: Clip.antiAlias,
      height: min(height, 335.pt),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.pt)),
      constraints: BoxConstraints(maxHeight: 335.pt),
      child: SmartRefresher(
        controller: _bloc.refreshController,
        primary: false,
        onLoading: () => _bloc.add(LoadMore()),
        enablePullDown: false,
        enablePullUp: _bloc.state.uidList?.isNotEmpty ?? false,
        footer: GlobalWidgets.refreshFoot(),
        child: ListView(
          children: rest,
        ),
      ),
    );
  }

  Widget _item(UserInfo model, double width,
      {bool showBorder = true, VoidCallback? callback}) {
    return Column(
      children: [
        if (showBorder)
          Container(
            width: width,
            height: 0.5.pt,
            color: Colors.black.withOpacity(.05),
          ),
        HfButton(
            height: 40.pt,
            width: width,
            color: model.uid == selectedUid ? Colors.white : Colors.transparent,
            onPressed: () {
              callback?.call();
            },
            child: Row(
              children: [
                SizedBox(width: 12.pt),
                Visibility(
                  visible: (model.avatar?.isNotEmpty ?? false) ||
                      (model.avatarCode?.isNotEmpty ?? false),
                  child: UserAvatar(
                      url: model.avatar,
                      avatarCode: model.avatarCode,
                      size: 23.pt),
                ),
                Visibility(
                  visible: (model.avatar?.isNotEmpty ?? false) ||
                      (model.avatarCode?.isNotEmpty ?? false),
                  child: SizedBox(width: 4.pt),
                ),
                Expanded(
                    child: Text(
                  '${model.displayedName}',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontWeight: FontWeightExt.medium,
                      fontSize: 15.sp,
                      color: model.uid == selectedUid
                          ? R.color.primaryDeepColor
                          : Color(0xFF4C556B)),
                ))
              ],
            ))
      ],
    );
  }
}
