import 'dart:async';

import 'package:biz/app_init.dart';
import 'package:biz/biz/splash/splash_page.dart';
import 'package:biz/route/page_statistic.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lifecycle/lifecycle.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/dialog/dialog_scheduler.dart';
import 'package:service/router/page_visibility_observer.dart';
import 'package:service/service.dart';
import 'package:golden_eye/kit/image_cache_info/core/custom_navigator_observer.dart';

import 'biz/main/event.dart';
import 'common/life_navigator/life_navigator_observer.dart';
import 'global/statistic/chat_send_statistic.dart';

export 'package:feature_widgets_nullsafety/feature_widget.dart';
export "package:service/service.dart" hide Rx;

export 'global/widgets/cache_image_widget.dart' hide timeDilation;
import 'package:biz/garbage/references.dart';

ThemeData _theme = ThemeData(fontFamily: 'Avenir', useMaterial3: false);

ThemeData _appTheme() => _theme.copyWith(
    brightness: Brightness.light,
    appBarTheme: _theme.appBarTheme.copyWith(
      color: colorAppBar,
      titleTextStyle: TextStyle(
        color: R.color.textColor1,
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
      ),
    ),
    tabBarTheme: const TabBarTheme(
      dividerColor: Colors.transparent,
    ),
    scaffoldBackgroundColor: colorBackground);

Widget bizMaterialAppBuilder() {
  return Application();
}

class Application extends StatefulWidget {
  const Application({Key? key}) : super(key: key);

  @override
  ApplicationState createState() => ApplicationState();

  static void rebuild(BuildContext context) {
    context.findAncestorStateOfType<ApplicationState>()?.rebuild();
  }
}

bool get isAppBackground => _isAppBackground;
bool _isAppBackground = false;
GlobalKey appWidgetKey = GlobalKey();

class ApplicationState extends State<Application> with WidgetsBindingObserver {
  StreamSubscription? _subLocalChange;
  StreamSubscription? _subThemeChange;
  Locale? _locale;
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  final _pageStatistic = PageStatistic();
  final chatSendStatistic = ChatSendStatistic();

  bool get _leakDetectEnable => false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance?.addObserver(this);
    // _initLeak();
    _subLocalChange = rxUtil.observer<String>(LocalEvent.change).listen((value) async {
      await IntlLocalizations.delegate.load(Locale(value));
      setState(() {
        _locale = Locale(value);
      });
    });

    _subThemeChange = rxUtil.observer<int>(ThemeChangeEvent.change).listen((value) {
      if (mounted) {
        rebuild();
      }
    });

    _pageStatistic.init();
    chatSendStatistic.init();
  }

  // void _initLeak() {
  //   if (_leakDetectEnable) {
  //     LeakDetector().init(maxRetainingPath: 300);
  //     LeakDetector().onLeakedStream.listen((LeakedInfo info) {
  //       info.retainingPath.forEach(print);
  //       showLeakedInfoPage(navigatorKey.currentContext!, info);
  //     });
  //   }
  // }

  @override
  void dispose() {
    print('app exit');
    Log.i('biz', 'app exit');
    AppInit.exit();
    super.dispose();
    WidgetsBinding.instance?.removeObserver(this);
    _subLocalChange?.cancel();
    _subThemeChange?.cancel();
  }

  void rebuild() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<NavigatorObserver> navigatorObservers = [
      FLRouter.routeObserver,
      PageVisibilityObserver.routeObserver,
      defaultLifecycleObserver,
      FlutterSmartDialog.observer,
      DialogQueueRouteObserver(),
      CustomNavigatorObserver.instance,
      LifeNavigatorObserver(),
    ];

    // if (_leakDetectEnable) {
    //   navigatorObservers.add(LeakNavigatorObserver(
    //     shouldCheck: (route) {
    //       return route.settings.name != null && route.settings.name != '/';
    //     },
    //   ));
    // }

    return RepaintBoundary(
        key: appWidgetKey,
        child: GetMaterialApp(
          navigatorKey: navigatorKey,
          theme: _appTheme(),
          onGenerateRoute: FLRouter.instance.onGenerateRoute,
          home: SplashPage(),
          navigatorObservers: navigatorObservers,
          locale: _locale,
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            IntlLocalizations.delegate,
          ],
          logWriterCallback: _getXLocalLogWriter,
          supportedLocales: IntlLocalizations.delegate.supportedLocales,
          builder: FlutterSmartDialog.init(builder: (context, widget) {
            final mediaQuery =  MediaQuery.of(context);
            if (mediaQuery.orientation == Orientation.portrait) {
              resolutionUtil.reSetup(context, uiWidth: 375, uiHeight: 811);
            }

            double scale = MediaQuery.of(context).textScaleFactor;
            return MediaQuery(
              /// 限制字体缩放不大于1.3
              data: MediaQuery.of(context).copyWith(textScaleFactor: scale > 1.3 ? 1.3 : scale),
              child: widget ?? Container(),
            );
          }),
        ));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        _isAppBackground = true;
        Log.e('biz', 'inactive');
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        rxUtil.send(PageVisibilityEvent.onForeground, 1);
        notificationService.setBackground(false);
        accountService.startHeartBreak();
        _isAppBackground = false;
        Log.i('biz', 'resumed');
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        rxUtil.send(PageVisibilityEvent.onBackground, 0);
        notificationService.setBackground(true);
        _isAppBackground = true;
        Log.i('biz', 'paused');

        /// 收起软键盘
        FocusScope.of(context).requestFocus(FocusNode());

        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        Log.i('biz', 'detached');
        break;
      default:
        break;
    }
  }

  ///低内存回调
  @override
  void didHaveMemoryPressure() {
    super.didHaveMemoryPressure();
    buglyLog(msg: 'didHaveMemoryPressure');
    Log.e('biz', 'didHaveMemoryPressure -> $isAppBackground');
    if (!isAppBackground) {
      /// 内存警告处理
      rxUtil.send(MainPageShowEvent.release, 1);
    } else {
      /// 进入后台情况下清除liveImages
      PaintingBinding.instance?.imageCache?.clearLiveImages();
    }

    /// 进入后台也要处理图片内存
    DefaultCacheManager().store.emptyMemoryCache();
  }

  void _getXLocalLogWriter(String text, {bool isError = false}) {
    if (isError) {
      Log.e('GetX', '$text');
    } else {
      Log.d('GetX', '$text');
    }
  }
}
