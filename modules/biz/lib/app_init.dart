import 'dart:io';

import 'package:biz/biz.dart';
import 'package:biz/biz/main/overlay/im_msg_overlay_handler.dart';
import 'package:biz/global/screenshot_observer.dart';
import 'package:biz/route/biz_router.r.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'package:service/common/getx/get_abs_state.dart';
import 'package:service/common/statistics/general_statistics.g.dart';
import 'package:service/common/statistics/login_statistics.g.dart';
import 'package:service/global/config/app_config.dart';
import 'package:service/global/config/ui_config.dart';
import 'package:service/global/sp.dart';
import 'package:service/modules/alarm/abs_alarm_service.dart';
import 'package:service/modules/bottle/abs_bottle_service.dart';
import 'package:service/modules/call/abs_call_service.dart';
import 'package:service/modules/channel_msg/abs_channel_msg_service.dart';
import 'package:service/modules/disturb/abs_disturb_service.dart';
import 'package:service/modules/evaluate/abs_evaluate_service.dart';
import 'package:service/modules/finance/abs_finance_service.dart';
import 'package:service/modules/finance/handler/starlight_change_handler.dart';
import 'package:service/modules/friend/abs_friend_service.dart';
import 'package:service/modules/gift/abs_gift_service.dart';
import 'package:service/modules/im/abs_im_function_service.dart';
import 'package:service/modules/im/abs_im_room_service.dart';
import 'package:service/modules/im/abs_safe_mode_service.dart';
import 'package:service/modules/intimacy/asb_intimacy_service.dart';
import 'package:service/modules/live/room_gift/handler/gift_float_screen_handler.dart';
import 'package:service/modules/live/room_gift/handler/gift_float_screen_lottery_handler.dart';
import 'package:service/modules/live/room_gift/handler/rank_global_change_handler.dart';
import 'package:service/modules/location/abs_location_service.dart';
import 'package:service/modules/moments/abs_moments_service.dart';
import 'package:service/modules/moments/abs_post_guide_service.dart';
import 'package:service/modules/notification/abs_notification_service.dart';
import 'package:service/modules/overlay/abs_overlay_service.dart';
import 'package:service/modules/questions/handler/system_tip.dart';
import 'package:service/modules/social/abs_social_service.dart';
import 'package:service/modules/sticker/services/abs_sticker_mic_emoji_service.dart';
import 'package:service/modules/sticker/services/abs_sticker_service.dart';
import 'package:service/modules/sticker/services/abs_version_service.dart';
import 'package:service/modules/task/abs_task_service.dart';
import 'package:service/modules/task/handler/task_progress_handler.dart';
import 'package:service/modules/upgrade/gp_update_observer.dart';
import 'package:service/modules/user/service/abs_avatar_service.dart';
import 'package:service/modules/voice_verify/abs_voice_verify_service.dart';
import 'package:service/private/o2_service.service.dart';
import 'package:service/service.dart';
import 'package:service/utils/connectivity_util.dart';
import 'package:service/utils/deep_link.dart';
import 'package:service/utils/logger.dart';
import 'package:golden_eye/custom_impl/test_tools_widgets_binding.dart';

import 'biz/finance/wallet/handler/recharge_suc_handler.dart';
import 'biz/finance/wallet/observer/pay_observer.dart';
import 'biz/launch/launch_args_manager.dart';
import 'biz/live_room/component/mini_room_float_button/mini_room_float_button.dart';
import 'biz/live_room/overlay/network_disconnect_overlay_handler.dart';
import 'biz/live_room/overlay/room_invite_overlay_handler.dart';
import 'biz/live_room/overlay/upgrade/room_upgrade_overlay.dart';
import 'biz/match/match_manager.dart';
import 'biz/task/task_center/task_complete_overlay.dart';
import 'biz/test/test_overlay.dart';
import 'common/deeplink/app_deeplink_service_handler.dart';
import 'global/widgets/empty_widget.dart';
import 'global/widgets/global_widgets.dart';
import 'net/http_interceptor.dart';

class AppInit {
  AppInit._();

  static var _hadInitHighPriority = false;

  static void init() {
    FLRouter.instance.addCreator(BizRouterCreator());
    serviceCenter.addCreator(O2ServiceCreator());
    FeatureFlatBase.init(isolateEnable: false);
    // resolutionUtil.reSetup(uiWidth: 375, uiHeight: 811);
    if (!kReleaseMode) {
      TestToolsWidgetsBinding.ensureInitialized();
    } else {
      WidgetsFlutterBinding.ensureInitialized();
    }
    DeepLink.deepLinkServiceHandler = AppDeepLinkServiceHandler();
    R.initialize();
    buglyLog(msg: 'flutter AppInit success');
  }

  /// 高优先级启动
  /// 不能放出初始化channel插件
  static Future<void> initHighPriority() async {
    if (_hadInitHighPriority) {
      return;
    }
    _hadInitHighPriority = true;

    // resolutionUtil.reSetup(uiWidth: 375, uiHeight: 811);
    await initEnvConfig();

    Log.init(isolateName: 'ui', debugMode: debugEnv, needConsole: false);
    Log.addOutput(Logger());

    /// overlay handler
    getService<AbsOverlayService>()?.addHandler(ImMsgOverlayHandler());
    getService<AbsOverlayService>()?.addHandler(TaskCompleteOverlay());
    getService<AbsOverlayService>()?.addHandler(MiniRoomOverlayHandler());
    getService<AbsOverlayService>()?.addHandler(NetWorkDisconnectOverlayHandler());
    getService<AbsOverlayService>()?.addHandler(TestOverlayHandler());

    getService<AbsOverlayService>()?.addHandler(RoomUpgradeOverlay());

    rxUtil.send(ResolutionEvent.update, 0);

    if (debugEnv) {
      getService<AbsOverlayService>()?.show(GlobalOverlayType.debug);
    }

    Log.i("App", "App launch");
    getService<AbsIsolateService>()?.setErrorCallback((msg) {
      Log.e('IsolateError', 'msg-> $msg');
    });
    buglyLog(msg: 'initHighPriority success');
  }

  static var _hadInitLowPriority = false;

  /// 低优先级，进入splash页面后再执行
  static Future<void> initLowPriority() async {
    if (_hadInitLowPriority) {
      return;
    }
    _hadInitLowPriority = true;

    rxUtil.send(ResolutionEvent.update, 0);

    if (Platform.isAndroid) {
      getService<AbsNotificationService>()?.cancelNotification(cancelAll: true);
    }

    getService<AbsHttpService>()?.addInterceptor(BizHttpInterceptor());
    getService<AbsChannelService>()?.addHandler(_ActivityBackHandler());
    // getService<AbsChannelService>()?.addHandler(ScreenshotObserver());
    getService<AbsChannelService>()?.addHandler(GpDownloadObserver());

    getService<AbsNovaRemoteConfigService>()
      ?..init()
      ..fetchUpdate();

    getService<AbsChannelMsgService>();
    getService<AbsChannelMsgService>()?.addHandler(PayObserver());
    getService<AbsCallService>();
    getService<AbsSocialService>();
    getService<AbsMomentsService>();
    getService<AbsMomentsOperationService>();
    getService<AbsEvaluateService>();
    getService<AbsDisturbService>();
    getService<AbsNotificationService>()?.addHandler(ScreenshotPushHandler());
    getService<AbsNotificationService>()?.addHandler(BoxStartTipHandler());
    getService<AbsNotificationService>()?.addHandler(StarLightChangeHandler());
    getService<AbsNotificationService>()?.addHandler(TaskProgressHandler());
    getService<AbsStickerService>();
    getService<AbsStickerVersionService>();
    getService<AbsStickerMicEmojiService>();
    getService<AbsFinanceService>();
    getService<AbsFriendService>();
    getService<AbsGiftService>();
    getService<AbsTaskService>();
    getService<AbsBottleService>();
    getService<AbsPostGuideService>();
    getService<AbsChatSafeModeService>();
    getService<AbsIMFunctionService>();
    getService<AbsVoiceVerifyService>();
    getService<AbsAvatarService>();
    getService<AbsLocationService>();
    getService<AbsImRoomService>();

    getService<AbsNotificationService>()?.addHandler(GiftFloatScreenHandler());
    getService<AbsNotificationService>()?.addHandler(GiftFloatScreenLotteryHandler());
    getService<AbsNotificationService>()?.addHandler(RankGlobalChangeHandler());
    getService<AbsNotificationService>()?.addHandler(RechargeSucNotificationHandler());
    getService<AbsNotificationService>()?.addHandler(RoomInviteOverlayHandler());

    getService<AbsIntimacyService>()?.pullIntimacyConfig();

    imBaseService.init(
      AppConfig.instance.rongCloudAppId,
      pushConfig: RCIMIWPushOptions.create(enableFCM: true, enableHWPush: false),
      naviServer: AppConfig.instance.rongServerUrl,
      fileServer: '',
    );
    LaunchArgsManager.instance().init();
    MatchManager.inst;
    await IntlLocalizations.startup();

    /// 网络状态
    connectivityUtil.status;

    getAbsConfig.init(
      defaultLoadingView: GlobalWidgets.pageLoading,
      defaultErrorView: ({errMsg, onTapError, isDart}) => EmptyWidget(
        logoAsset: Res.emptyEmptyList,
        detail: errMsg,
        btnSting: LocaleStrings.instance.tryAgain,
        btnAction: onTapError,
        isDark: isDart ?? false,
      ),
      defaultEmptyView: ({onTapEmpty, isDart}) => EmptyWidget(
        isDark: isDart ?? false,
        logoAsset: Res.emptyRoomMemberListEmpty,
        detail: LocaleStrings.instance.noResult,
        btnAction: onTapEmpty,
      ),
    );

    ServiceManager.inst
      ..init()
      ..initTag()
      ..registerController();

    /// 应用启动上报
    GeneralStatistics.reportAppLaunch();
    _checkLaunch2d();

    /// 上报fcmToken
    accountService.updateFCMToken();

    buglyLog(msg: 'initLowPriority success');
  }

  /// 检查超过24小时的上报
  static _checkLaunch2d() {
    final now = DateTime.now();
    final hadReport = globalSp.getBool(spKeyHadReportLaunch2D) ?? false;
    if (!hadReport) {
      final firstTime = globalSp.getInt(spKeyStartAppFirstTime);
      if (firstTime != null && firstTime > 0) {
        final lastDate = DateTime.fromMillisecondsSinceEpoch(firstTime);
        final duration = now.difference(lastDate);

        /// 首次打开后 次天打开上报
        if (duration.inHours >= 24 && duration.inHours <= 48) {
          reportFbEvent(name: 'ndau_app_open_2d');
          LoginStatistics.reportNdauAppOpen2d();
          globalSp.setBool(spKeyHadReportLaunch2D, true);
        }
      }
    }
  }

  /// 进入首页后需要进行的事
  static Future<void> doAfterHome() async {
    getService<AbsAlarmService>()?.startCheckLocalPush();
  }

  static void exit() {}
}

class _ActivityBackHandler implements AbsMethodHandler {
  @override
  String methodName() {
    return 'activity_back_pressed';
  }

  @override
  Future onHandler(dynamic arguments) async {
    routerUtil.pop();
    return null;
  }
}
