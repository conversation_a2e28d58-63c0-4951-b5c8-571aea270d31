// @dart=2.12
import 'package:biz/biz.dart';
import 'package:biz/biz/finance/wallet/wallet_page/widget/recharge/recharge_dialog.dart';
import 'package:biz/biz/live_room/handler/live_room_handler.dart';
import 'package:biz/biz/live_room/plugins/turntable_game/turntable_game.dart';
import 'package:biz/biz/share/share_guid_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:service/utils/value_parsers.dart';
import 'package:biz/common/widgets/half_link_dialog.dart';

class RouterInterceptor implements AbsRouterInterceptor {
  final _tag = 'RouterInterceptor';
  late Map<String, void Function(Map<String, dynamic>? params)> _pushInterceptorMapping;

  RouterInterceptor() {
    _pushInterceptorMapping = <String, void Function(Map<String, dynamic>? params)>{
      R_LIVE_ROME_JOIN: _onRoomJoin,
      // R_LIVE_ROOM_MATCH: _onRoomMatch,
      // R_LIVE_LUDO_MATCH: (_) => _onGameMatch(_, 'ludo'),
      // R_LIVE_KNIFE_MATCH: (_) => _onGameMatch(_, 'knife'),
      // R_LIVE_DOMINO_MATCH: (_) => _onGameMatch(_, 'domino'),
      // R_LIVE_TRUTH_DARE: (_) => _onGameMatch(_, 'truth_dare'),
      // R_LIVE_DRAW_GUESS: (_) => _onGameMatch(_, 'draw_guess'),
      // R_LIVE_UNDERCOVER: (_) => _onGameMatch(_, 'undercover'),
      R_RECHARGE_SHEET: _onRechargeSheet,
      R_SHARE_GUIDE_DIALOG: _onShareGuideDialog,
      R_LIVE_ROOM_GIFT: _onRoomGift,
      R_LIVE_ROOM_INFO_DIALOG: _roomInfoDialog,
      R_PREMIUM_INFO_DIALOG: _premiumInfoDialog,
      R_WEB_DIALOG: _onInterceptWebPage,
      R_RECHARGE_DIALOG: _onRechargeSheet,
      R_COMMON_WEB_PAGE: _onInterceptWebPage,
      R_BS_GAME_TURNTABLE: _onInterceptBsGameTurntable
    };
  }

  void _onInterceptWebPage(Map<String, dynamic>? params) {
    if (params == null) return;

    showHalfLinkDialog(
        linkModel: HalfLinkModel(
            url: dynamicToString(params['url']),
            isHalf: false,
            backgroundColor: Colors.transparent),
        heightRatio: toDoubleOrNull(params[P_HEIGHT_RATIO]),
        minHeight: toDoubleOrNull(params[P_MIN_HEIGHT]));
  }

  void _onInterceptBsGameTurntable(Map<String, dynamic>? params) {
    showTurntableGame(FLRouter.routeObserver.getLastContext());
  }

  void _onRechargeSheet(Map<String, dynamic>? params) {
    showRechargeDialog(from: LiveRoomHandler.from);
  }

  void _onRoomJoin(Map<String, dynamic>? params) {
    final roomId = params?[P_ID];
    final roomPwd = params?[P_ROOM_PWD] ?? '';
    final from = params?[P_STATISTIC_FROM];
    final start = params?[P_ROOM_START];
    final checkSwitchRoom = params?[P_IS_CHECK] ?? true;

    LiveRoomHandler.joinRoom(
      roomId is String ? roomId : "",
      pwd: roomPwd,
      from: from,
      start: start,
      checkSwitchRoom: checkSwitchRoom,
      params: params,
    );
  }

  void _onRoomMatch(Map<String, dynamic>? params) async {
    if (await callService.checkIsInCall()) {
      toast(LocaleStrings.instance.inCallNotice);
      return;
    }
    final from = params?[P_STATISTIC_FROM];
    final type = params?[P_TYPE];
    final dialog = params?[P_ROUTE_DIALOG];
    if (dialog == '1') {
      _onGameMatch(params, type);
    } else {
      LiveRoomHandler.roomMatch(type ?? '', from: from);
    }
  }

  void _onGameMatch(Map<String, dynamic>? params, String gameType) async {
    if (await callService.checkIsInCall()) {
      toast(LocaleStrings.instance.inCallNotice);
      return;
    }

    final from = params?[P_STATISTIC_FROM];
    LiveRoomHandler.gameMatch(gameType: gameType, from: from);
  }

  void _onShareGuideDialog(Map<String, dynamic>? params) async {
    final from = params?[P_STATISTIC_FROM];
    // showShareGuidDialog(
    //   FLRouter.routeObserver.getLastContext(),
    //   await userService.getCurrentUserInfo(),
    //   from: from,
    // );
  }

  void _onRoomGift(Map<String, dynamic>? params) async {
    final targetId = params?[P_TARGET_ID];
    final giftId = params?[P_GIFT_ID];
    // showRoomGiftDialog(targetId: targetId, giftId: giftId);
  }

  void _roomInfoDialog(Map<String, dynamic>? params) async {
    final from = params?[P_STATISTIC_FROM];
    final index = toInt(params?[P_INDEX]);
    // showRoomInfoDialog(from: from, index: index);
  }

  void _premiumInfoDialog(Map<String, dynamic>? params) async {
    final from = params?[P_STATISTIC_FROM];
    // showPremiumInfoDialog(from: from);
  }

  @override
  Future<bool> shouldInterceptPop({BuildContext? context, Map<String, dynamic>? params}) async {
    var routes = FLRouter.routeObserver.getRoutes();
    var count = routes.length;

    if (count <= 1 && null != context) {
      buglyLog(msg: 'app exit');
    }

    if (null != context) {
      Navigator.of(context).pop(params);
      return true;
    }

    if (routes.isEmpty) return false;
    final navigator = routes.last?.navigator;
    if ((navigator?.mounted ?? false) && (await navigator?.maybePop(params) ?? false)) {
      return true;
    }

    return false;
  }

  @override
  Future<bool> shouldInterceptPush(String url, {BuildContext? context, Map<String, dynamic>? params}) async {
    /// 切换首页
    if (accountService.hadLogin() && url.contains(R_MAIN)) {
      _handlerPushMain(url, params: params);
      return true;
    }

    if (url == R_SHARE_DIALOG) {
      final userInfo = await userService.getCurrentUserInfo();
      if (userInfo != null) {
        showShareGuidDialog(
          context,
          userInfo,
        );
        return true;
      }
    }

    if (null != context) {
      Navigator.of(context).pushNamed(url, arguments: params);
      return true;
    }

    /// 是否跳转直播
    if (_pushInterceptorMapping[url] != null) {
      _pushInterceptorMapping[url]?.call(params);
      return true;
    }

    /// 跳转原生
    if (!FLRouter.instance.hadPager(url)) {
      getService<AbsChannelService>()?.invokeMethod('jump_native', arguments: {
        'url': url,
        'params': params,
      });

      return true;
    }
    return false;
  }

  void _handlerPushMain(String url, {Map<String, dynamic>? params}) {
    /// 首页不是主页
    if (FLRouter.routeObserver.getRoutes().length > 1) {
      routerUtil.popUntil(R_MAIN);
    }
    if (params?[P_TAB_NAME] is String) {
      String? name;
      try {
        name = params?[P_TAB_NAME] ?? '';
      } on Exception catch (e) {
        Log.e(_tag, 'handlerPushMain parseError -> $e');
      }
      rxUtil.send(AppMainEvent.changeTab, name);
    }
  }

  @override
  Future<bool> shouldInterceptPopAndPush(String url, {BuildContext? context, Map<String, dynamic>? params}) async {
    return false;
  }

  @override
  Future<bool> shouldInterceptPushAndRemoveAll(String url,
      {BuildContext? context, Map<String, dynamic>? params}) async {
    /// 去掉切换首页动画
    // if (url == R_MAIN) {
    //   if (context == null) {
    //     context = FLRouter.routeObserver.getLastContext();
    //   }
    //
    //   Navigator.of(context).pushAndRemoveUntil(
    //       PageRouteBuilder(
    //         settings: RouteSettings(name: url, arguments: params),
    //         transitionDuration: Duration(milliseconds: 100), //动画时间为500毫秒
    //         pageBuilder: (BuildContext context, Animation<double> animation,
    //             Animation<double> secondaryAnimation) {
    //           return FadeTransition(
    //             //使用渐隐渐入过渡,
    //             opacity: animation,
    //             child:
    //                 FLRouter.instance.builderWidget(url, params: params), //路由B
    //           );
    //         },
    //       ),
    //           (route) => false);
    //
    //   // Navigator.of(context)
    //   //     .pushAndRemoveUntil(path, (route) => false, arguments: args)
    //   return true;
    // }

    return false;
  }

  @override
  Future<bool> shouldInterceptRemoveAllExceptLast(String path, {BuildContext? context}) {
    return Future.value(false);
  }
}
